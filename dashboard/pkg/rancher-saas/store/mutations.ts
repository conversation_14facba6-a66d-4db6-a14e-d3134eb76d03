export default {
  ADD_MESSAGE(state: any, message: { role: string; content: string; timestamp: string }) {
    state.chat.messages.push(message);
  },
  RESET_CHAT(state: any) {
    state.chat.messages = [];
    state.chat.chatId = '';
  },
  SET_CHAT_ID(state: any, id: string) {
    state.chat.chatId = id;
  },
  SET_BACKEND_URL(state: any, backendUrl: string) {
    state.config.backendUrl = backendUrl;
  },
  SET_LOADING(state: any, isLoading: boolean) {
    state.chat.isLoading = isLoading;
  },
  SET_CONFIG(state: any, config: any) {
    state.config = config;
  },
  SET_EXPIRATION(state: any, expiration: Date) {
    state.expiration = expiration;
  },
  SET_PLG_MODE(state: any, isPLGMode: boolean) {
    state.config.isPLGMode = isPLGMode;
  },
  SET_LAST_WIZARD_STEP(state: any, stepName: string | null) {
    state.config.lastWizardStep = stepName;
  }
};
