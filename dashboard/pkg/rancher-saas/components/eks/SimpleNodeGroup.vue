<script lang="ts">
import { defineComponent, PropType } from 'vue';
import LabeledInput from '@components/Form/LabeledInput/LabeledInput.vue';
import LabeledSelect from '@shell/components/form/LabeledSelect.vue';
import UnitInput from '@shell/components/form/UnitInput.vue';
import Banner from '@components/Banner/Banner.vue';
import { EKSNodeGroup, AWS } from '../../types';

export default defineComponent({
  name: 'SimpleNodeGroup',

  components: {
    LabeledInput,
    LabeledSelect,
    UnitInput,
    Banner
  },

  props: {
    modelValue: {
      type:     Object as PropType<EKSNodeGroup>,
      required: true
    },
    instanceTypeOptions: {
      type:    Array,
      default: () => []
    },
    loadingInstanceTypes: {
      type:    Boolean,
      default: false
    },
    mode: {
      type:    String,
      default: 'create'
    },
    rules: {
      type:    Object,
      default: () => ({})
    }
  },

  emits: ['update:modelValue'],

  computed: {
    nodeGroup: {
      get(): EKSNodeGroup {
        return this.modelValue;
      },
      set(value: EKSNodeGroup) {
        this.$emit('update:modelValue', value);
      }
    },

    recommendedInstanceTypes(): AWS.InstanceTypeOption[] {
      // Filter to show only recommended instance types for PLG users
      return (this.instanceTypeOptions as AWS.InstanceTypeOption[]).filter((opt: AWS.InstanceTypeOption) => {
        if (opt.kind === 'group') {
          const label = opt.label || '';
          return label.includes('General Purpose') || label.includes('Compute Optimized');
        }
        const value = opt.value || '';
        // Recommend t3/t4g series for cost-effectiveness
        return value.startsWith('t3.') || value.startsWith('t4g.') ||
               value.startsWith('m5.') || value.startsWith('m6i.');
      });
    },

    minMaxDesiredErrors(): string | null {
      const errs = (this.rules?.minMaxDesired || []).reduce((errs: string[], rule: Function) => {
        const err = rule({
          minSize: this.nodeGroup.minSize,
          maxSize: this.nodeGroup.maxSize,
          desiredSize: this.nodeGroup.desiredSize
        });

        if (err) {
          errs.push(err);
        }

        return errs;
      }, []);

      return errs.length ? errs[0] : null;
    },

    estimatedMonthlyCost(): string {
      // Simple cost estimation based on instance type and count
      const costMap: Record<string, number> = {
        't3.small':   15,
        't3.medium':  30,
        't3.large':   60,
        't3.xlarge':  120,
        't4g.small':  12,
        't4g.medium': 24,
        't4g.large':  48,
        'm5.large':   70,
        'm5.xlarge':  140,
        'm6i.large':  70,
        'm6i.xlarge': 140,
      };

      const instanceCost = this.nodeGroup.instanceType ? costMap[this.nodeGroup.instanceType] || 50 : 50;
      const totalCost = instanceCost * (this.nodeGroup.desiredSize || 2);
      
      return `~$${totalCost}/month`;
    }
  },

  methods: {
    updateInstanceType(value: string) {
      this.nodeGroup.instanceType = value;
      // Emit the updated nodeGroup to ensure parent component gets the changes
      this.$emit('update:modelValue', this.nodeGroup);
    },

    updateNodeCount(field: 'minSize' | 'maxSize' | 'desiredSize', value: string) {
      const numValue = parseInt(value, 10) || 0;
      this.nodeGroup[field] = numValue;

      // Ensure logical constraints - maintain the same logic as the advanced mode
      if (field === 'minSize' && numValue > (this.nodeGroup.maxSize || 0)) {
        this.nodeGroup.maxSize = numValue;
      }
      if (field === 'maxSize' && numValue < (this.nodeGroup.minSize || 0)) {
        this.nodeGroup.minSize = numValue;
      }
      if (field === 'desiredSize') {
        if (numValue < (this.nodeGroup.minSize || 0)) {
          this.nodeGroup.minSize = numValue;
        }
        if (numValue > (this.nodeGroup.maxSize || 0)) {
          this.nodeGroup.maxSize = numValue;
        }
      }

      // Emit the updated nodeGroup to ensure parent component gets the changes
      this.$emit('update:modelValue', this.nodeGroup);
    }
  }
});
</script>

<template>
  <div class="simple-node-group">
    <div class="node-config-section">
      <h4>Instance Type</h4>
      <p class="text-muted mb-10">
        Select the computing power for your worker nodes
      </p>
      
      <LabeledSelect
        :value="nodeGroup.instanceType"
        :mode="mode"
        label="Instance Type"
        :options="instanceTypeOptions"
        :loading="loadingInstanceTypes"
        :rules="rules.instanceType"
        data-testid="eks-instance-type-dropdown"
        @update:value="updateInstanceType"
      />

      <div
        v-if="nodeGroup.instanceType"
        class="cost-estimate mt-10"
      >
        <i class="icon icon-dollar" />
        <span>Estimated cost: <strong>{{ estimatedMonthlyCost }}</strong></span>
      </div>
    </div>

    <div class="node-config-section mt-20">
      <h4>Cluster Size</h4>
      <p class="text-muted mb-10">
        Configure auto-scaling for your cluster
      </p>

      <div class="row">
        <div class="col span-4">
          <LabeledInput
            v-model:value="nodeGroup.minSize"
            label="Minimum Nodes"
            type="number"
            :mode="mode"
            :rules="rules.minSize"
            @update:value="updateNodeCount('minSize', $event)"
          />
        </div>
        <div class="col span-4">
          <LabeledInput
            v-model:value="nodeGroup.desiredSize"
            label="Desired Nodes"
            type="number"
            :mode="mode"
            :rules="rules.desiredSize"
            @update:value="updateNodeCount('desiredSize', $event)"
          />
        </div>
        <div class="col span-4">
          <LabeledInput
            v-model:value="nodeGroup.maxSize"
            label="Maximum Nodes"
            type="number"
            :mode="mode"
            :rules="rules.maxSize"
            @update:value="updateNodeCount('maxSize', $event)"
          />
        </div>
      </div>

      <Banner
        v-if="!!minMaxDesiredErrors"
        color="error"
        :label="minMaxDesiredErrors"
      />

      <Banner
        v-else
        color="info"
        class="mt-10"
      >
        <p>
          Your cluster will automatically scale between
          <strong>{{ nodeGroup.minSize }}</strong> and
          <strong>{{ nodeGroup.maxSize }}</strong> nodes based on workload.
        </p>
      </Banner>
    </div>

    <div class="node-config-section mt-20">
      <h4>Storage</h4>
      <p class="text-muted mb-10">
        Disk space for each node
      </p>
      
      <div class="row">
        <div class="col span-6">
          <UnitInput
            v-model:value="nodeGroup.diskSize"
            label="Disk Size"
            suffix="GB"
            :mode="mode"
            :rules="rules.diskSize"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.simple-node-group {
  .node-config-section {
    padding: 15px;
    background: var(--body-bg);
    border: 1px solid var(--border);
    border-radius: var(--border-radius);

    h4 {
      margin: 0 0 5px 0;
      font-size: 16px;
      font-weight: 600;
    }

    .text-muted {
      color: var(--text-muted);
      font-size: 14px;
    }
  }

  .instance-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .badge-recommended {
      background: var(--success);
      color: var(--success-text);
      padding: 2px 8px;
      border-radius: 3px;
      font-size: 11px;
      font-weight: 600;
      text-transform: uppercase;
    }
  }

  .cost-estimate {
    padding: 10px;
    background: var(--info-banner-bg);
    border: 1px solid var(--info);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--text-default);

    i {
      color: var(--info);
    }

    strong {
      color: var(--text-default);
      font-weight: 600;
    }
  }
}
</style>

