<template>
  <div class="plg-mode-toggle">
    <div class="toggle-wrapper">
      <ToggleSwitch
        :value="isPLGMode"
        :on-value="false"
        :off-value="true"
        on-label=" Advanced"
        off-label=""
        @update:value="updatePLGMode"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import ToggleSwitch from '@components/Form/ToggleSwitch/ToggleSwitch.vue';

export default defineComponent({
  name: 'PLGModeToggle',

  components: {
    ToggleSwitch,
  },

  computed: {
    // Get PLG mode state directly from store
    isPLGMode(): boolean {
      return this.$store.getters['saasAdmin/isPLGMode'] ?? true;
    }
  },

  methods: {
    updatePLGMode(isPlgMode: boolean) {
      // Update store - this will trigger watchers in other components
      this.$store.dispatch('saasAdmin/setPLGMode', isPlgMode);
    },
  },
});
</script>

<style lang="scss" scoped>
.plg-mode-toggle {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;

  .toggle-wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    //background: var(--box-bg);
    // border: 1px solid var(--border);
    // border-radius: 4px;

    .toggle-label {
      font-size: 14px;
      color: var(--body-text);
      font-weight: 500;
    }
  }
}
</style>
