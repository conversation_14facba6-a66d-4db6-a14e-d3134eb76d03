{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Card/Card.vue?vue&type=template&id=35b78590&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Card/Card.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Card/Card.vue?vue&type=template&id=35b78590&ts=true", "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Card/Card.vue"], "names": ["showHighlightBorder", "sticky", "title", "content", "showActions", "buttonText"], "mappings": "AAAA,OAAO,EAAE,UAAU,IAAI,WAAW,EAAE,eAAe,IAAI,gBAAgB,EAAE,eAAe,IAAI,gBAAgB,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,SAAS,IAAI,UAAU,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,cAAc,IAAI,eAAe,EAAE,MAAM,KAAK,CAAA;AAEtT,MAAM,UAAU,GAAG,ECgFV,KAAK,EAAC,WAAW,EAAA,CAAA;AD/E1B,MAAM,UAAU,GAAG;ICiFX,KAAK,EAAC,YAAY;IAClB,aAAW,EAAC,iBAAiB;CD/EpC,CAAA;AACD,MAAM,UAAU,GAAG;ICsFX,KAAK,EAAC,WAAW;IACjB,aAAW,EAAC,gBAAgB;CDpFnC,CAAA;AACD,MAAM,UAAU,GAAG;IACjB,GAAG,EAAE,CAAC;IC0FA,KAAK,EAAC,cAAc;IACpB,aAAW,EAAC,mBAAmB;CDxFtC,CAAA;AAED,MAAM,UAAU,MAAM,CAAC,IAAS,EAAC,MAAW,EAAC,MAAW,EAAC,MAAW,EAAC,KAAU,EAAC,QAAa;IAC3F,OAAO,CAAC,UAAU,EAAE,EC0DpB,mBAAA,CAuCM,KAAA,EAAA;QAtCJ,EAAE,EAAC,mCAAmC;QACtC,KAAK,EAAA,eAAA,CAAA,CAAC,gBAAgB,EAAA,EAAA,kBAAA,EACOA,IAAAA,CAAAA,mBAAmB,EAAA,aAAA,EAAiBC,IAAAA,CAAAA,MAAM,EAAA,CAAA,CAAA;QACvE,aAAW,EAAC,MAAM;KD1DnB,EAAE;QC4DD,mBAAA,CAgCM,KAAA,EAhCN,UAgCM,EAAA;YA/BJ,mBAAA,CAOM,KAAA,EAPN,UAOM,EAAA;gBAHJ,WAAA,CAEO,IAAA,CAAA,MAAA,EAAA,OAAA,EAAA,EAAA,EAFP,GAEO,EAAA,CAAA;oBD/DL,gBAAgB,CAAC,gBAAgB,CC8D9BC,IAAAA,CAAAA,KAAK,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;iBD7DT,CAAC;aACH,CAAC;YACF,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;YAC7C,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GC6DvB,mBAAA,CAAI,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA;YD5DJ,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;YC6D7C,mBAAA,CAOM,KAAA,EAPN,UAOM,EAAA;gBAHJ,WAAA,CAEO,IAAA,CAAA,MAAA,EAAA,MAAA,EAAA,EAAA,EAFP,GAEO,EAAA,CAAA;oBDhEL,gBAAgB,CAAC,gBAAgB,CC+D9BC,IAAAA,CAAAA,OAAO,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;iBD9DX,CAAC;aACH,CAAC;YACF,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;YAC7C,CC+DQC,IAAAA,CAAAA,WAAW,CAAA;gBD9DjB,CAAC,CAAC,CAAC,UAAU,EAAE,EC6DjB,mBAAA,CAaM,KAAA,EAbN,UAaM,EAAA;oBARJ,WAAA,CAOO,IAAA,CAAA,MAAA,EAAA,SAAA,EAAA,EAAA,EAPP,GAOO,EAAA,CAAA;wBANL,mBAAA,CAKS,QAAA,EAAA;4BAJP,KAAK,EAAC,kBAAkB;4BACvB,OAAK,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA;gCDhElB,YAAY;gCACZ,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC,CC+DO,IAAA,CAAA,YAAA,IAAA,IAAA,CAAA,YAAA,CAAA,GAAA,IAAA,CAAY,CAAA,CAAA;yBD9DjB,EAAE,gBAAgB,CCgElBC,IAAAA,CAAAA,UAAU,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;qBD/DZ,CAAC;iBACH,CAAC,CAAC;gBACL,CAAC,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;SACtC,CAAC;KACH,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAA;AACpB,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Card/Card.vue.tsx", "sourceRoot": "", "sourcesContent": ["import { renderSlot as _renderSlot, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass } from \"vue\"\n\nconst _hoisted_1 = { class: \"card-wrap\" }\nconst _hoisted_2 = {\n  class: \"card-title\",\n  \"data-testid\": \"card-title-slot\"\n}\nconst _hoisted_3 = {\n  class: \"card-body\",\n  \"data-testid\": \"card-body-slot\"\n}\nconst _hoisted_4 = {\n  key: 0,\n  class: \"card-actions\",\n  \"data-testid\": \"card-actions-slot\"\n}\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  return (_openBlock(), _createElementBlock(\"div\", {\n    id: \"focus-trap-card-container-element\",\n    class: _normalizeClass([\"card-container\", {'highlight-border': _ctx.showHighlightBorder, 'card-sticky': _ctx.sticky}]),\n    \"data-testid\": \"card\"\n  }, [\n    _createElementVNode(\"div\", _hoisted_1, [\n      _createElementVNode(\"div\", _hoisted_2, [\n        _renderSlot(_ctx.$slots, \"title\", {}, () => [\n          _createTextVNode(_toDisplayString(_ctx.title), 1 /* TEXT */)\n        ])\n      ]),\n      _cache[1] || (_cache[1] = _createTextVNode()),\n      _cache[2] || (_cache[2] = _createElementVNode(\"hr\", null, null, -1 /* CACHED */)),\n      _cache[3] || (_cache[3] = _createTextVNode()),\n      _createElementVNode(\"div\", _hoisted_3, [\n        _renderSlot(_ctx.$slots, \"body\", {}, () => [\n          _createTextVNode(_toDisplayString(_ctx.content), 1 /* TEXT */)\n        ])\n      ]),\n      _cache[4] || (_cache[4] = _createTextVNode()),\n      (_ctx.showActions)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [\n            _renderSlot(_ctx.$slots, \"actions\", {}, () => [\n              _createElementVNode(\"button\", {\n                class: \"btn role-primary\",\n                onClick: _cache[0] || (_cache[0] = \n//@ts-ignore\n(...args) => (_ctx.buttonAction && _ctx.buttonAction(...args)))\n              }, _toDisplayString(_ctx.buttonText), 1 /* TEXT */)\n            ])\n          ]))\n        : _createCommentVNode(\"v-if\", true)\n    ])\n  ], 2 /* CLASS */))\n}", "<script lang=\"ts\">\nimport { defineComponent, PropType } from 'vue';\nimport { useBasicSetupFocusTrap } from '@shell/composables/focusTrap';\n\nexport default defineComponent({\n\n  name:  'Card',\n  props: {\n    /**\n     * The card's title.\n     */\n    title: {\n      type:    String,\n      default: ''\n    },\n    /**\n     * The text content for the card's body.\n     */\n    content: {\n      type:    String,\n      default: ''\n    },\n    /**\n     * The function to invoke when the default action button is clicked.\n     */\n    buttonAction: {\n      type:    Function as PropType<(event: MouseEvent) => void>,\n      default: (): void => { }\n    },\n    /**\n     * The text for the default action button.\n     */\n    buttonText: {\n      type:    String,\n      default: 'go'\n    },\n    /**\n     * Toggles the card's highlight-border class.\n     */\n    showHighlightBorder: {\n      type:    Boolean,\n      default: true\n    },\n    /**\n     * Toggles the card's Actions section.\n     */\n    showActions: {\n      type:    Boolean,\n      default: true\n    },\n    sticky: {\n      type:    Boolean,\n      default: false,\n    },\n    triggerFocusTrap: {\n      type:    Boolean,\n      default: false,\n    },\n  },\n  setup(props) {\n    if (props.triggerFocusTrap) {\n      useBasicSetupFocusTrap('#focus-trap-card-container-element', {\n        // needs to be false because of import YAML modal from header\n        // where the YAML editor itself is a focus trap\n        // and we can't have it superseed the \"escape key\" to blur that UI element\n        // In this case the focus trap moves the focus out of the modal\n        // correctly once it closes because of the \"onBeforeUnmount\" trigger\n        escapeDeactivates: false,\n        allowOutsideClick: true,\n      });\n    }\n  }\n});\n</script>\n\n<template>\n  <div\n    id=\"focus-trap-card-container-element\"\n    class=\"card-container\"\n    :class=\"{'highlight-border': showHighlightBorder, 'card-sticky': sticky}\"\n    data-testid=\"card\"\n  >\n    <div class=\"card-wrap\">\n      <div\n        class=\"card-title\"\n        data-testid=\"card-title-slot\"\n      >\n        <slot name=\"title\">\n          {{ title }}\n        </slot>\n      </div>\n      <hr>\n      <div\n        class=\"card-body\"\n        data-testid=\"card-body-slot\"\n      >\n        <slot name=\"body\">\n          {{ content }}\n        </slot>\n      </div>\n      <div\n        v-if=\"showActions\"\n        class=\"card-actions\"\n        data-testid=\"card-actions-slot\"\n      >\n        <slot name=\"actions\">\n          <button\n            class=\"btn role-primary\"\n            @click=\"buttonAction\"\n          >\n            {{ buttonText }}\n          </button>\n        </slot>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang='scss'>\n .card-container {\n  &.highlight-border {\n    border-left: 5px solid var(--primary);\n  }\n  border-radius: var(--border-radius);\n  display: flex;\n  flex-basis: 40%;\n  margin: 10px;\n  min-height: 100px;\n  padding: 10px;\n  box-shadow: 0 0 20px var(--shadow);\n  &:not(.top) {\n    align-items: top;\n    flex-direction: row;\n    justify-content: start;\n  }\n  .card-wrap {\n    width: 100%;\n  }\n   & .card-body {\n    color: var(--input-label);\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n   }\n   & .card-actions {\n     align-self: end;\n     display: flex;\n     padding-top: 20px;\n   }\n   & .card-title {\n    align-items: center;\n    display: flex;\n    width: 100%;\n     h5 {\n       margin: 0;\n     }\n    .flex-right {\n      margin-left: auto;\n    }\n   }\n\n  // Sticky mode will stick header and footer to top and bottom with content in the middle scrolling\n   &.card-sticky {\n      // display: flex;\n      // flex-direction: column;\n      overflow: hidden;\n\n    .card-wrap {\n      display: flex;\n      flex-direction: column;\n\n      .card-body {\n        justify-content: flex-start;\n        overflow: auto;\n      }\n\n      > * {\n        flex: 0;\n      }\n\n      .card-body {\n        flex: 1;\n      }\n    }\n   }\n }\n</style>\n"]}]}