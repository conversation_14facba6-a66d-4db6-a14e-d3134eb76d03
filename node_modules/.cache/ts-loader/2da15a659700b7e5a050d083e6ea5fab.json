{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/NodeGroup.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/NodeGroup.vue", "mtime": 1754992537319}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/NodeGroup.vue"], "names": [], "mappings": "AACA,OAAO,EAAE,eAAe,EAAW,MAAO,KAAK,CAAA;AAC/C,OAAO,EAAE,UAAU,EAAQ,MAAO,MAAM,CAAA;AACxC,OAAO,QAAO,MAAO,iBAAiB,CAAA;AAEtC,OAAO,EAAE,KAAK,EAAE,KAAI,EAAE,MAAO,4BAA4B,CAAA;AACzD,OAAO,EAAE,SAAQ,EAAE,MAAO,qBAAqB,CAAA;AAC/C,OAAO,EAAE,OAAM,EAAE,MAAO,qBAAqB,CAAA;AAE7C,OAAO,aAAY,MAAO,0CAA0C,CAAA;AACpE,OAAO,YAAW,MAAO,gDAAgD,CAAA;AACzE,OAAO,QAAO,MAAO,wCAAwC,CAAA;AAC7D,OAAO,QAAO,MAAO,qCAAqC,CAAA;AAC1D,OAAO,MAAK,MAAO,+BAA+B,CAAA;AAClD,OAAO,SAAQ,MAAO,sCAAsC,CAAA;AAC5D,OAAO,YAAW,MAAO,yCAAyC,CAAA;AAElE,OAAO,EAAE,uBAAuB,EAAE,SAAQ,EAAE,MAAO,gBAAgB,CAAA;AAEnE,OAAO,EAAE,yBAAwB,EAAE,MAAO,cAAc,CAAA;AAExD,sEAAqE;AACrE,MAAM,0BAA0B,GAA4B;IAC1D,OAAO,EAAO,SAAS;IACvB,QAAQ,EAAM,UAAU;IACxB,YAAY,EAAE,cAAc;IAC5B,SAAS,EAAK,EAAE;IAChB,YAAY,EAAE,mBAAmB;IACjC,QAAQ,EAAM,qBAAoB;CACnC,CAAA;AAED,MAAM,iBAAgB,GACtB;;;;;;;;;qBASqB,CAAA;AAErB,eAAe,eAAe,CAAC;IAC7B,IAAI,EAAE,aAAa;IAEnB,KAAK,EAAE,CAAC,qBAAqB,EAAE,0BAA0B,EAAE,kBAAkB,EAAE,uBAAuB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,wBAAwB,EAAE,OAAO,EAAE,qBAAqB,EAAE,iBAAiB,EAAE,sBAAsB,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,eAAe,EAAE,aAAa,EAAE,gBAAgB,EAAE,YAAY,EAAE,6BAA6B,EAAE,iBAAiB,EAAE,kBAAkB,CAAC;IAEpc,UAAU,EAAE;QACV,YAAY;QACZ,aAAa;QACb,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,SAAS;QACT,YAAW;KACZ;IAED,KAAK,EAAE;QACL,QAAQ,EAAE;YACR,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,EAAC;SACX;QACD,YAAY,EAAE;YACZ,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,GAAG,EAAC;gBACX,OAAO,EAAE,CAAA;YACX,CAAA;SACD;QACD,oBAAoB,EAAE;YACpB,IAAI,EAAK,OAAO;YAChB,OAAO,EAAE,KAAI;SACd;QACD,iBAAiB,EAAE;YACjB,IAAI,EAAK,KAAK;YACd,OAAO,EAAE,GAAG,EAAC,CAAE,EAAC;SACjB;QACD,MAAM,EAAE;YACN,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,GAAG,EAAC;gBACX,OAAO,EAAE,CAAA;YACX,CAAA;SACD;QACD,IAAI,EAAE;YACJ,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,GAAG,EAAC;gBACX,OAAO,EAAE,CAAA;YACX,CAAA;SACD;QACD,GAAG,EAAE;YACH,IAAI,EAAK,OAAO;YAChB,OAAO,EAAE,KAAI;SACd;QACD,QAAQ,EAAE;YACR,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,EAAC;SACX;QACD,YAAY,EAAE;YACZ,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,EAAC;SACX;QACD,OAAO,EAAE;YACP,IAAI,EAAK,CAAC,MAAM,EAAE,IAAI,CAAC;YACvB,OAAO,EAAE,EAAC;SACX;QACD,WAAW,EAAE;YACX,IAAI,EAAK,CAAC,MAAM,EAAE,MAAM,CAAC;YACzB,OAAO,EAAE,IAAG;SACb;QACD,OAAO,EAAE;YACP,IAAI,EAAK,CAAC,MAAM,EAAE,MAAM,CAAC;YACzB,OAAO,EAAE,IAAG;SACb;QACD,OAAO,EAAE;YACP,IAAI,EAAK,CAAC,MAAM,EAAE,MAAM,CAAC;YACzB,OAAO,EAAE,IAAG;SACb;QACD,QAAQ,EAAE;YACR,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,IAAG;SACb;QACD,SAAS,EAAE;YACT,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,EAAC;SACX;QACD,aAAa,EAAE;YACb,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,EAAC;SACX;QACD,MAAM,EAAE;YACN,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,EAAC;SACX;QACD,sBAAsB,EAAE;YACtB,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,EAAC;SACX;QAED,cAAc,EAAE;YACd,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,GAAG,EAAC,GAAG,CAAA;SACjB;QAED,OAAO,EAAE;YACP,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,EAAC;SACX;QAED,cAAc,EAAE;YACd,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,EAAC;SACX;QAED,sBAAsB,EAAE;YACtB,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,EAAC;SACX;QAED,IAAI,EAAE;YACJ,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,KAAI;SACd;QAED,QAAQ,EAAE;YACR,IAAI,EAAK,KAAgC;YACzC,OAAO,EAAE,GAAG,EAAC,CAAE,EAAC;SACjB;QAED,oBAAoB,EAAE;YACpB,IAAI,EAAK,OAAO;YAChB,OAAO,EAAE,IAAG;SACb;QAED,SAAS,EAAE;YACT,IAAI,EAAK,OAAO;YAChB,OAAO,EAAE,KAAI;SACd;QAED,mBAAmB,EAAE;YACnB,IAAI,EAAK,KAAK;YACd,OAAO,EAAE,GAAG,EAAC,CAAE,EAAC;SACjB;QAED,uBAAuB,EAAE;YACvB,IAAI,EAAK,KAAK;YACd,OAAO,EAAE,GAAG,EAAC,CAAE,EAAC;SACjB;QAED,eAAe,EAAE;YACf,IAAI,EAAK,KAAuC;YAChD,OAAO,EAAE,GAAG,EAAC,CAAE,EAAC;SACjB;QAED,WAAW,EAAE;YACX,IAAI,EAAK,KAA2B;YACpC,OAAO,EAAE,GAAG,EAAC,CAAE,EAAC;SACjB;QAED,aAAa,EAAE;YACb,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,IAAG;SACb;QAED,eAAe,EAAE;YACf,IAAI,EAAK,OAAO;YAChB,OAAO,EAAE,KAAI;SACd;QAED,oBAAoB,EAAE;YACpB,IAAI,EAAK,OAAO;YAChB,OAAO,EAAE,KAAI;SACd;QAED,YAAY,EAAE;YACZ,IAAI,EAAK,OAAO;YAChB,OAAO,EAAE,KAAI;SACd;QAED,sBAAsB,EAAE;YACtB,IAAI,EAAK,OAAO;YAChB,OAAO,EAAE,KAAI;SACd;QAED,kBAAkB,EAAE;YAClB,IAAI,EAAK,OAAO;YAChB,OAAO,EAAE,KAAI;SACd;QAED,KAAK,EAAE;YACL,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,GAAG,EAAC;gBACX,OAAO,EAAE,CAAA;YACX,CAAA;SACF;KACD;IAED,OAAO;QACL,IAAI,CAAC,8BAA6B,GAAI,QAAQ,CAAC,IAAI,CAAC,qBAAqB,EAAE,GAAG,CAAC,CAAA;IACjF,CAAC;IAED,IAAI;QACF,MAAM,KAAI,GAAI,IAAI,CAAC,MAAoB,CAAA;QACvC,MAAM,CAAA,GAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;QAEjC,OAAO;YACL,mBAAmB,EAAI,IAAI,CAAC,OAAO;YACnC,qBAAqB,EAAE,EAAE,kBAAkB,EAAE,CAAC,CAAC,sBAAsB,CAAA,EAAyB;YAE9F,qBAAqB,EAAW,EAAE,QAAQ,EAAE,CAAC,CAAC,sBAAsB,CAAA,EAAG;YACvE,sBAAsB,EAAU,KAAK;YACrC,mHAAkH;YAClH,0BAA0B,EAAM,EAA8B;YAC9D,8BAA8B,EAAE,IAAuB;YACvD,mHAAkH;YAClH,wBAAuB;YACvB,cAAc,EAAkB,SAAS,EAAC;SAC3C,CAAA;IACH,CAAC;IAED,KAAK,EAAE;QACL,sBAAsB,EAAE;YACtB,OAAO,CAAC,GAAG;gBACT,IAAI,GAAE,IAAK,GAAG,CAAC,gBAAe,IAAK,IAAI,CAAC,sBAAsB,EAAE,CAAA;oBAC9D,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAA;gBAClE,CAAA;YACF,CAAC;YACD,SAAS,EAAE,IAAG;SACf;QAED,sBAAsB,EAAE;YACtB,OAAO;gBACL,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAA;YAClE,CAAC;YACD,SAAS,EAAE,IAAG;SACf;QAED,qBAAqB,CAAC,GAAE,GAAI,EAAE,EAAE,GAAE,GAAI,EAAE;YACtC,IAAI,CAAC,sBAAqB,GAAI,IAAI,CAAA;YAClC,IAAI,IAAI,CAAC,8BAA8B,EAAE,CAAA;gBACvC,IAAI,CAAC,8BAA8B,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;YAC/C,CAAA;QACF,CAAC;QAED,sBAAsB,CAAC,GAAG;YACxB,IAAI,GAAE,IAAK,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,EAAE,CAAA;gBAC9C,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAA;YACzC,CAAA;iBAAO,CAAA;gBACL,IAAI,CAAC,KAAK,CAAC,0BAA0B,EAAE,IAAI,CAAC,CAAA;YAC9C,CAAA;QACF,CAAC;QAED,WAAW,EAAE;YACX,OAAO,CAAC,GAAG;gBACT,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAA;oBACjC,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAA;gBACpC,CAAA;YACF,CAAC;YACD,IAAI,EAAE,IAAG;SACX;KACD;IAED,QAAQ,EAAE;QACR,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE,QAAO,EAAG,CAAC;QAE9B,eAAe;;YACb,MAAM,SAAQ,GAAI,CAAA,MAAA,IAAI,CAAC,aAAa,0CAAE,SAAQ,KAAK,EAAE,CAAA;YAErD,OAAO,SAAS,CAAC,uBAAuB,CAAA;QAC1C,CAAC;QAED,wBAAwB;;YACtB,MAAM,SAAQ,GAAI,CAAA,MAAA,IAAI,CAAC,aAAa,0CAAE,SAAQ,KAAK,EAAE,CAAA;YACrD,MAAM,aAAY,GAAI,IAAI,CAAC,aAAa,CAAA;YACxC,MAAM,wBAAuB,GAAI,CAAC,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,6BAA4B,KAAK,EAAE,CAAC,CAAC,aAAa,CAAC,CAAA;YAEhG,OAAO,OAAO,CAAC,IAAI,CAAC,cAAc,CAAA,IAAK,CAAC,OAAO,CAAC,SAAS,CAAC,uBAAuB,CAAA,IAAK,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAA;QAC1H,CAAC;QAED,qBAAqB;YACnB,MAAM,EAAE,cAAa,GAAI,EAAC,EAAE,GAAI,IAAI,CAAA;YAEpC,OAAO,CAAC,CAAC,CAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,EAAC,CAAA,IAAK,CAAC,CAAC,CAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,OAAO,CAAA,CAAA;QAC1D,CAAC;QAED,mBAAmB;YACjB,OAAO,CAAC,IAAI,CAAC,wBAAuB,IAAK,CAAC,IAAI,CAAC,qBAAqB,CAAA;QACtE,CAAC;QAED,qBAAqB;YACnB,OAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAC,CAAE,CAAC,CAAC,CAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,kBAAiB,KAAK,EAAE,CAAC,CAAC,UAAU,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAA;QAC9J,CAAC;QAED,sBAAsB,EAAE;YACtB,GAAG;;gBACD,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAA;oBACjC,OAAO,EAAE,gBAAgB,EAAE,IAAI,CAAC,eAAe,EAAE,kBAAkB,EAAE,IAAI,CAAC,CAAC,CAAC,8CAA8C,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,eAAc,EAAG,CAAA,EAAG,CAAA;gBAC/J,CAAA;gBACA,MAAM,EAAC,GAAI,MAAA,IAAI,CAAC,cAAc,0CAAE,EAAE,CAAA;gBAElC,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAAsB,EAAE,EAAC,CAAE,EAAE,CAAC,gBAAe,IAAK,EAAE,CAAC,gBAAe,KAAM,EAAE,CAAA,IAAK,IAAI,CAAC,qBAAqB,CAAA;YACrJ,CAAC;YACD,GAAG,CAAC,GAAuB;gBACzB,IAAI,GAAG,CAAC,kBAAiB,KAAM,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,EAAE,CAAA;oBAC5E,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAA;oBAEvC,OAAM;gBACR,CAAA;gBACA,MAAM,IAAG,GAAI,GAAG,CAAC,kBAAkB,CAAA;gBACnC,MAAM,EAAC,GAAI,GAAG,CAAC,gBAAgB,CAAA;gBAC/B,MAAM,OAAM,GAAI,GAAG,CAAC,oBAAoB,CAAA;gBAExC,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE;oBAClC,IAAI,EAAE,EAAE,EAAE,OAAM;iBACjB,CAAC,CAAA;YACJ,CAAA;SACD;QAED,4BAA4B;;YAC1B,IAAI,IAAI,CAAC,0BAAyB,KAAK,MAAA,IAAI,CAAC,0BAA0B,0CAAE,sBAAsB,CAAA,EAAE,CAAA;gBAC9F,OAAO,IAAI,CAAC,0BAA0B,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAC,CAAE,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,EAAE,CAAA;YAC9G,CAAA;YAEA,OAAO,EAAE,CAAA;QACX,CAAC;QAED,mBAAmB;;YACjB,OAAO,CAAC,CAAA,MAAA,IAAI,CAAC,0BAA0B,0CAAE,sBAAqB,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,EAAC,WAAE,OAAA,CAAC,CAAC,aAAY,MAAM,MAAA,IAAI,CAAC,cAAc,0CAAE,OAAO,CAAA,CAAA,EAAA,CAAA,IAAK,IAAI,CAAA;QACnJ,CAAC;QAED,mBAAmB;;YACjB,OAAO,MAAA,IAAI,CAAC,mBAAmB,0CAAE,kBAAkB,CAAA;QACrD,CAAC;QAED,eAAe,EAAE;YACf,GAAG;gBACD,MAAM,GAAE,GAAI,IAAI,CAAC,QAAQ,CAAA;gBAEzB,IAAI,CAAC,GAAG,EAAE,CAAA;oBACR,OAAO,IAAI,CAAC,qBAAqB,CAAA;gBACnC,CAAA;gBAEA,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAiB,EAAE,EAAC,CAAE,IAAI,CAAC,GAAE,KAAM,GAAG,CAAA,CAAE;YACrE,CAAC;YACD,GAAG,CAAC,GAAgB;gBAClB,IAAI,GAAG,CAAC,GAAG,EAAE,CAAA;oBACX,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,GAAG,CAAC,GAAG,CAAC,CAAA;gBACxC,CAAA;qBAAO,CAAA;oBACL,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAA;gBACnC,CAAA;YACF,CAAA;SACD;QAED,mBAAmB;YACjB,OAAO,iBAAiB,CAAA;QAC1B,CAAC;QAED,mBAAmB;YACjB,OAAO,IAAI,CAAC,oBAAmB,IAAK,IAAI,CAAC,SAAS,CAAA;QACpD,CAAC;QAED,kBAAkB;YAChB,OAAO,IAAI,CAAC,cAAa,KAAM,IAAI,CAAC,sBAAsB,CAAA;QAC5D,CAAC;QAED,cAAc;YACZ,OAAO,CAAC,IAAI,CAAC,kBAAiB,IAAK,IAAI,CAAC,mBAAkB,KAAM,IAAI,CAAC,cAAa,IAAK,CAAC,IAAI,CAAC,SAAS,CAAA;QACxG,CAAC;QAED,WAAW,EAAE;YACX,GAAG;gBACD,OAAO,IAAI,CAAC,cAAa,IAAK,IAAI,CAAC,OAAM,KAAM,IAAI,CAAC,cAAc,CAAA;YACpE,CAAC;YACD,GAAG,CAAC,GAAY;gBACd,IAAI,GAAG,EAAE,CAAA;oBACP,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAA;oBACjD,IAAI,CAAC,KAAK,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAA;gBAC5C,CAAA;qBAAO,CAAA;oBACL,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAA;oBACtD,IAAI,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAA;gBAC7C,CAAA;YACF,CAAA;SACD;QAED,mBAAmB;;YACjB,MAAM,IAAG,GAAI,CAAC,CAAA,MAAA,IAAI,CAAC,KAAK,0CAAE,aAAY,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,IAAc,EAAE,IAAc,EAAE,EAAC;gBACtF,MAAM,GAAE,GAAI,IAAI,CAAC;oBACf,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,WAAW,EAAE,IAAI,CAAC,WAAU;iBAC3E,CAAC,CAAA;gBAEF,IAAI,GAAG,EAAE,CAAA;oBACP,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;gBAChB,CAAA;gBAEA,OAAO,IAAI,CAAA;YACb,CAAC,EAAE,EAAc,CAAC,CAAA;YAElB,OAAO,IAAI,CAAC,MAAK,CAAE,CAAA,CAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAA,CAAE,CAAA,CAAE,IAAI,CAAA;QAC5C,CAAC;QAED,MAAM;YACJ,OAAO,IAAI,CAAC,IAAG,KAAM,KAAK,CAAA;QAC5B,CAAA;KACD;IAED,OAAO,EAAE;QACP,KAAI,CAAE,8BAA8B,CAAC,cAAkC;YACrE,MAAM,EAAE,MAAM,EAAE,sBAAqB,EAAE,GAAI,IAAI,CAAA;YAE/C,IAAI,CAAC,MAAK,IAAK,CAAC,sBAAqB,IAAK,IAAI,CAAC,MAAM,EAAE,CAAA;gBACrD,OAAM;YACR,CAAA;YACA,MAAM,KAAI,GAAI,IAAI,CAAC,MAAoB,CAAA;YACvC,MAAM,SAAQ,GAAI,MAAM,KAAK,CAAC,QAAQ,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,iBAAiB,EAAE,sBAAqB,EAAG,CAAC,CAAA;YAExG,IAAI,CAAA;gBACF,IAAI,cAAc,CAAC,kBAAiB,KAAM,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,EAAE,CAAA;oBACvF,IAAI,cAAc,CAAC,gBAAgB,EAAE,CAAA;wBACnC,IAAI,CAAC,0BAAyB,GAAI,MAAM,SAAS,CAAC,8BAA8B,CAAC,EAAE,gBAAgB,EAAE,cAAc,CAAC,gBAAe,EAAG,CAAC,CAAA;oBACzI,CAAA;yBAAO,CAAA;wBACL,IAAI,CAAC,0BAAyB,GAAI,MAAM,SAAS,CAAC,8BAA8B,CAAC,EAAE,kBAAkB,EAAE,cAAc,CAAC,kBAAiB,EAAG,CAAC,CAAA;oBAC7I,CAAA;gBACF,CAAA;YACF,CAAA;YAAE,OAAO,GAAG,EAAE,CAAA;gBACZ,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAA;YAC1B,CAAA;QACF,CAAC;QAED,qBAAqB,CAAC,MAAM,EAAmC,EAAE,MAAM,EAAmC;YACxG,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,OAAO,CAAC,CAAC,UAAkB,EAAE,EAAC;;gBACpE,MAAM,MAAK,GAAI,0BAA0B,CAAC,UAAU,CAAC,CAAA;gBAErD,IAAI,MAAK,KAAM,mBAAmB,EAAE,CAAA;oBAClC,MAAM,EAAE,iBAAgB,EAAE,GAAI,GAAG,CAAA;oBAEjC,IAAI,iBAAiB,EAAE,CAAA;wBACrB,MAAM,IAAG,GAAI,EAA4B,CAAA;wBAEzC,iBAAiB,CAAC,OAAO,CAAC,CAAC,GAAmE,EAAE,EAAC;4BAC/F,IAAI,GAAG,CAAC,YAAW,KAAM,UAAS,IAAK,GAAG,CAAC,IAAG,IAAK,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAA;gCAClE,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAA;4BAC1C,CAAA;wBACF,CAAC,CAAC,CAAA;wBACF,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAA;oBACzC,CAAA;yBAAO,CAAA;wBACL,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,EAAE,GAAG,yBAAyB,CAAC,YAAW,EAAG,CAAC,CAAA;oBAClF,CAAA;gBACF,CAAA;qBAAO,IAAI,MAAK,KAAM,qBAAqB,EAAE,CAAA;oBAC3C,MAAM,EAAE,mBAAkB,EAAE,GAAI,GAAG,CAAA;oBAEnC,IAAI,mBAAkB,IAAK,mBAAmB,CAAC,MAAM,EAAE,CAAA;wBACrD,MAAM,IAAG,GAAI,MAAA,MAAA,mBAAmB,CAAC,CAAC,CAAC,0CAAE,GAAG,0CAAE,UAAU,CAAA;wBAEpD,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAA;oBACrC,CAAA;yBAAO,CAAA;wBACL,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,yBAAyB,CAAC,QAAQ,CAAC,CAAA;oBACnE,CAAA;gBACF,CAAA;qBAAO,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE,CAAA;oBACzC,IAAI,CAAC,KAAK,CAAC,UAAW,UAAW,EAAE,EAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAA;gBACtE,CAAA;qBAAO,CAAA;oBACL,IAAI,CAAC,KAAK,CAAC,UAAW,UAAW,EAAE,EAAE,yBAAyB,CAAC,UAAoD,CAAC,CAAC,CAAA;gBACvH,CAAA;YACF,CAAC,CAAC,CAAA;YAEF,IAAI,CAAC,SAAS,CAAC,GAAG,EAAC;gBACjB,IAAI,CAAC,cAAa,GAAI,SAAS,EAAE,CAAA;gBACjC,IAAI,CAAC,sBAAqB,GAAI,KAAK,CAAA;YACrC,CAAC,CAAC,CAAA;QACJ,CAAC;QAED,aAAa,CAAC,KAAa;;YACzB,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAA;gBAC5B,OAAO,IAAI,CAAA;YACb,CAAA;YAEA,MAAM,iBAAgB,GAAI,0BAA0B,CAAC,KAAK,CAAwC,CAAA;YAElG,IAAI,CAAC,iBAAiB,EAAE,CAAA;gBACtB,OAAO,IAAI,CAAA;YACb,CAAA;YACA,MAAM,iBAAgB,GAAI,MAAA,IAAI,CAAC,mBAAmB,0CAAG,iBAAiB,CAAC,CAAA;YAEvE,IAAI,iBAAgB,KAAM,SAAQ,IAAK,CAAC,CAAC,CAAC,OAAO,iBAAgB,KAAM,QAAQ,CAAA,IAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAA;gBAChH,IAAI,KAAI,KAAM,UAAU,EAAE,CAAA;oBACxB,MAAM,YAAW,GAAI,iBAAiB,CAAC,CAAC,CAA2B,CAAA;oBAEnE,OAAO,CAAA,MAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,GAAG,0CAAE,UAAS,KAAK,IAAI,CAAA;gBAC9C,CAAA;gBACA,IAAI,KAAI,KAAM,cAAc,EAAE,CAAA;oBAC5B,MAAM,IAAG,GAAI,CAAC,iBAAgB,IAAK,EAAE,CAA2B,CAAA;oBAEhE,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,GAAyB,EAAE,EAAC,CAAE,GAAG,CAAC,YAAW,KAAM,UAAU,CAAC,CAAC,CAAC,CAAC,CAAA;gBACvF,CAAA;gBAEA,OAAO,iBAAiB,CAAA;YAC1B,CAAA;YAEA,OAAO,IAAI,CAAA;QACb,CAAC;KACF;CACF,CAAC,CAAA", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/NodeGroup.vue.tsx", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent, PropType } from 'vue';\nimport { mapGetters, Store } from 'vuex';\nimport debounce from 'lodash/debounce';\n\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\nimport { randomStr } from '@shell/utils/string';\nimport { isEmpty } from '@shell/utils/object';\n\nimport LabeledSelect from '@shell/components/form/LabeledSelect.vue';\nimport LabeledInput from '@components/Form/LabeledInput/LabeledInput.vue';\nimport Checkbox from '@components/Form/Checkbox/Checkbox.vue';\nimport KeyValue from '@shell/components/form/KeyValue.vue';\nimport Banner from '@components/Banner/Banner.vue';\nimport UnitInput from '@shell/components/form/UnitInput.vue';\nimport FileSelector from '@shell/components/form/FileSelector.vue';\n\nimport { MANAGED_TEMPLATE_PREFIX, parseTags } from '../../util/aws';\nimport { AWS } from '../../types';\nimport { DEFAULT_NODE_GROUP_CONFIG } from './CruEKS.vue';\n\n// map between fields in rancher eksConfig and amazon launch templates\nconst launchTemplateFieldMapping: {[key: string]: string} = {\n  imageId:      'ImageId',\n  userData:     'UserData',\n  instanceType: 'InstanceType',\n  ec2SshKey:    '',\n  resourceTags: 'TagSpecifications',\n  diskSize:     'BlockDeviceMappings'\n};\n\nconst DEFAULT_USER_DATA =\n`MIME-Version: 1.0\nContent-Type: multipart/mixed; boundary=\"==MYBOUNDARY==\"\n\n--==MYBOUNDARY==\nContent-Type: text/x-shellscript; charset=\"us-ascii\"\n\n#!/bin/bash\necho \"Running custom user data script\"\n\n--==MYBOUNDARY==--\\\\`;\n\nexport default defineComponent({\n  name: 'EKSNodePool',\n\n  emits: ['update:instanceType', 'update:spotInstanceTypes', 'update:ec2SshKey', 'update:launchTemplate', 'update:nodeRole', 'update:nodeRole', 'update:version', 'update:poolIsUpgrading', 'error', 'update:resourceTags', 'update:diskSize', 'update:nodegroupName', 'update:desiredSize', 'update:minSize', 'update:maxSize', 'update:labels', 'update:tags', 'update:imageId', 'update:gpu', 'update:requestSpotInstances', 'update:userData', 'update:ec2SshKey'],\n\n  components: {\n    LabeledInput,\n    LabeledSelect,\n    KeyValue,\n    Banner,\n    Checkbox,\n    UnitInput,\n    FileSelector\n  },\n\n  props: {\n    nodeRole: {\n      type:    String,\n      default: ''\n    },\n    resourceTags: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    requestSpotInstances: {\n      type:    Boolean,\n      default: false\n    },\n    spotInstanceTypes: {\n      type:    Array,\n      default: () => []\n    },\n    labels: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    tags: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    gpu: {\n      type:    Boolean,\n      default: false\n    },\n    userData: {\n      type:    String,\n      default: ''\n    },\n    instanceType: {\n      type:    String,\n      default: ''\n    },\n    imageId: {\n      type:    [String, null],\n      default: ''\n    },\n    desiredSize: {\n      type:    [Number, String],\n      default: null\n    },\n    minSize: {\n      type:    [Number, String],\n      default: null\n    },\n    maxSize: {\n      type:    [Number, String],\n      default: null\n    },\n    diskSize: {\n      type:    Number,\n      default: null\n    },\n    ec2SshKey: {\n      type:    String,\n      default: ''\n    },\n    nodegroupName: {\n      type:    String,\n      default: ''\n    },\n    region: {\n      type:    String,\n      default: ''\n    },\n    amazonCredentialSecret: {\n      type:    String,\n      default: ''\n    },\n\n    launchTemplate: {\n      type:    Object,\n      default: () => {}\n    },\n\n    version: {\n      type:    String,\n      default: ''\n    },\n\n    clusterVersion: {\n      type:    String,\n      default: ''\n    },\n\n    originalClusterVersion: {\n      type:    String,\n      default: ''\n    },\n\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n\n    ec2Roles: {\n      type:    Array as PropType<AWS.IamRole[]>,\n      default: () => []\n    },\n\n    isNewOrUnprovisioned: {\n      type:    Boolean,\n      default: true\n    },\n\n    poolIsNew: {\n      type:    Boolean,\n      default: false\n    },\n\n    instanceTypeOptions: {\n      type:    Array,\n      default: () => []\n    },\n\n    spotInstanceTypeOptions: {\n      type:    Array,\n      default: () => []\n    },\n\n    launchTemplates: {\n      type:    Array as PropType<AWS.LaunchTemplate[]>,\n      default: () => []\n    },\n\n    sshKeyPairs: {\n      type:    Array as PropType<string[]>,\n      default: () => []\n    },\n\n    normanCluster: {\n      type:    Object,\n      default: null\n    },\n\n    poolIsUpgrading: {\n      type:    Boolean,\n      default: false\n    },\n\n    loadingInstanceTypes: {\n      type:    Boolean,\n      default: false\n    },\n\n    loadingRoles: {\n      type:    Boolean,\n      default: false\n    },\n\n    loadingLaunchTemplates: {\n      type:    Boolean,\n      default: false\n    },\n\n    loadingSshKeyPairs: {\n      type:    Boolean,\n      default: false\n    },\n\n    rules: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  created() {\n    this.debouncedSetValuesFromTemplate = debounce(this.setValuesFromTemplate, 500);\n  },\n\n  data() {\n    const store = this.$store as Store<any>;\n    const t = store.getters['i18n/t'];\n\n    return {\n      originalNodeVersion:   this.version,\n      defaultTemplateOption: { LaunchTemplateName: t('eks.defaultCreateOne') } as AWS.LaunchTemplate,\n\n      defaultNodeRoleOption:          { RoleName: t('eks.defaultCreateOne') },\n      loadingSelectedVersion:         false,\n      // once a specific lt has been selected, an additional query is made to get full information on every version of it\n      selectedLaunchTemplateInfo:     {} as AWS.LaunchTemplateDetail,\n      debouncedSetValuesFromTemplate: null as Function | null,\n      // the keyvalue component needs to be re-rendered if the value prop is updated by parent component when as-map=true\n      // TODO nb file an issue\n      resourceTagKey:                 randomStr()\n    };\n  },\n\n  watch: {\n    selectedLaunchTemplate: {\n      handler(neu) {\n        if (neu && neu.LaunchTemplateId && this.amazonCredentialSecret) {\n          this.fetchLaunchTemplateVersionInfo(this.selectedLaunchTemplate);\n        }\n      },\n      immediate: true\n    },\n\n    amazonCredentialSecret: {\n      handler() {\n        this.fetchLaunchTemplateVersionInfo(this.selectedLaunchTemplate);\n      },\n      immediate: true\n    },\n\n    'selectedVersionData'(neu = {}, old = {}) {\n      this.loadingSelectedVersion = true;\n      if (this.debouncedSetValuesFromTemplate) {\n        this.debouncedSetValuesFromTemplate(neu, old);\n      }\n    },\n\n    'requestSpotInstances'(neu) {\n      if (neu && !this.templateValue('instanceType')) {\n        this.$emit('update:instanceType', null);\n      } else {\n        this.$emit('update:spotInstanceTypes', null);\n      }\n    },\n\n    sshKeyPairs: {\n      handler(neu) {\n        if (!neu.includes(this.ec2SshKey)) {\n          this.$emit('update:ec2SshKey', '');\n        }\n      },\n      deep: true\n    }\n  },\n\n  computed: {\n    ...mapGetters({ t: 'i18n/t' }),\n\n    rancherTemplate() {\n      const eksStatus = this.normanCluster?.eksStatus || {};\n\n      return eksStatus.managedLaunchTemplateID;\n    },\n\n    hasRancherLaunchTemplate() {\n      const eksStatus = this.normanCluster?.eksStatus || {};\n      const nodegroupName = this.nodegroupName;\n      const nodeGroupTemplateVersion = (eksStatus?.managedLaunchTemplateVersions || {})[nodegroupName];\n\n      return isEmpty(this.launchTemplate) && !isEmpty(eksStatus.managedLaunchTemplateID) && !isEmpty(nodeGroupTemplateVersion);\n    },\n\n    hasUserLaunchTemplate() {\n      const { launchTemplate = {} } = this;\n\n      return !!launchTemplate?.id && !!launchTemplate?.version;\n    },\n\n    hasNoLaunchTemplate() {\n      return !this.hasRancherLaunchTemplate && !this.hasUserLaunchTemplate;\n    },\n\n    launchTemplateOptions(): AWS.LaunchTemplate[] {\n      return [this.defaultTemplateOption, ...this.launchTemplates.filter((template) => !(template?.LaunchTemplateName || '').startsWith(MANAGED_TEMPLATE_PREFIX))];\n    },\n\n    selectedLaunchTemplate: {\n      get(): AWS.LaunchTemplate {\n        if (this.hasRancherLaunchTemplate) {\n          return { LaunchTemplateId: this.rancherTemplate, LaunchTemplateName: this.t('eks.nodeGroups.launchTemplate.rancherManaged', { name: this.rancherTemplate }) };\n        }\n        const id = this.launchTemplate?.id;\n\n        return this.launchTemplateOptions.find((lt: AWS.LaunchTemplate) => lt.LaunchTemplateId && lt.LaunchTemplateId === id) || this.defaultTemplateOption;\n      },\n      set(neu: AWS.LaunchTemplate) {\n        if (neu.LaunchTemplateName === this.defaultTemplateOption.LaunchTemplateName) {\n          this.$emit('update:launchTemplate', {});\n\n          return;\n        }\n        const name = neu.LaunchTemplateName;\n        const id = neu.LaunchTemplateId;\n        const version = neu.DefaultVersionNumber;\n\n        this.$emit('update:launchTemplate', {\n          name, id, version\n        });\n      }\n    },\n\n    launchTemplateVersionOptions(): number[] {\n      if (this.selectedLaunchTemplateInfo && this.selectedLaunchTemplateInfo?.LaunchTemplateVersions) {\n        return this.selectedLaunchTemplateInfo.LaunchTemplateVersions.map((version) => version.VersionNumber).sort();\n      }\n\n      return [];\n    },\n\n    selectedVersionInfo(): AWS.LaunchTemplateVersion | null {\n      return (this.selectedLaunchTemplateInfo?.LaunchTemplateVersions || []).find((v: any) => v.VersionNumber === this.launchTemplate?.version) || null;\n    },\n\n    selectedVersionData(): AWS.LaunchTemplateVersionData | undefined {\n      return this.selectedVersionInfo?.LaunchTemplateData;\n    },\n\n    displayNodeRole: {\n      get() {\n        const arn = this.nodeRole;\n\n        if (!arn) {\n          return this.defaultNodeRoleOption;\n        }\n\n        return this.ec2Roles.find((role: AWS.IamRole) => role.Arn === arn) ;\n      },\n      set(neu: AWS.IamRole) {\n        if (neu.Arn) {\n          this.$emit('update:nodeRole', neu.Arn);\n        } else {\n          this.$emit('update:nodeRole', '');\n        }\n      }\n    },\n\n    userDataPlaceholder() {\n      return DEFAULT_USER_DATA;\n    },\n\n    poolIsUnprovisioned() {\n      return this.isNewOrUnprovisioned || this.poolIsNew;\n    },\n\n    clusterWillUpgrade() {\n      return this.clusterVersion !== this.originalClusterVersion;\n    },\n\n    nodeCanUpgrade() {\n      return !this.clusterWillUpgrade && this.originalNodeVersion !== this.clusterVersion && !this.poolIsNew;\n    },\n\n    willUpgrade: {\n      get() {\n        return this.nodeCanUpgrade && this.version === this.clusterVersion;\n      },\n      set(neu: boolean) {\n        if (neu) {\n          this.$emit('update:version', this.clusterVersion);\n          this.$emit('update:poolIsUpgrading', true);\n        } else {\n          this.$emit('update:version', this.originalNodeVersion);\n          this.$emit('update:poolIsUpgrading', false);\n        }\n      }\n    },\n\n    minMaxDesiredErrors() {\n      const errs = (this.rules?.minMaxDesired || []).reduce((errs: string[], rule: Function) => {\n        const err = rule({\n          minSize: this.minSize, maxSize: this.maxSize, desiredSize: this.desiredSize\n        });\n\n        if (err) {\n          errs.push(err);\n        }\n\n        return errs;\n      }, [] as string[]);\n\n      return errs.length ? errs.join(' ') : null;\n    },\n\n    isView() {\n      return this.mode === _VIEW;\n    }\n  },\n\n  methods: {\n    async fetchLaunchTemplateVersionInfo(launchTemplate: AWS.LaunchTemplate) {\n      const { region, amazonCredentialSecret } = this;\n\n      if (!region || !amazonCredentialSecret || this.isView) {\n        return;\n      }\n      const store = this.$store as Store<any>;\n      const ec2Client = await store.dispatch('aws/ec2', { region, cloudCredentialId: amazonCredentialSecret });\n\n      try {\n        if (launchTemplate.LaunchTemplateName !== this.defaultTemplateOption.LaunchTemplateName) {\n          if (launchTemplate.LaunchTemplateId) {\n            this.selectedLaunchTemplateInfo = await ec2Client.describeLaunchTemplateVersions({ LaunchTemplateId: launchTemplate.LaunchTemplateId });\n          } else {\n            this.selectedLaunchTemplateInfo = await ec2Client.describeLaunchTemplateVersions({ LaunchTemplateName: launchTemplate.LaunchTemplateName });\n          }\n        }\n      } catch (err) {\n        this.$emit('error', err);\n      }\n    },\n\n    setValuesFromTemplate(neu = {} as AWS.LaunchTemplateVersionData, old = {} as AWS.LaunchTemplateVersionData) {\n      Object.keys(launchTemplateFieldMapping).forEach((rancherKey: string) => {\n        const awsKey = launchTemplateFieldMapping[rancherKey];\n\n        if (awsKey === 'TagSpecifications') {\n          const { TagSpecifications } = neu;\n\n          if (TagSpecifications) {\n            const tags = {} as {[key:string]: string};\n\n            TagSpecifications.forEach((tag: {Tags?: {Key: string, Value: string}[], ResourceType?: string}) => {\n              if (tag.ResourceType === 'instance' && tag.Tags && tag.Tags.length) {\n                Object.assign(tags, parseTags(tag.Tags));\n              }\n            });\n            this.$emit('update:resourceTags', tags);\n          } else {\n            this.$emit('update:resourceTags', { ...DEFAULT_NODE_GROUP_CONFIG.resourceTags });\n          }\n        } else if (awsKey === 'BlockDeviceMappings') {\n          const { BlockDeviceMappings } = neu;\n\n          if (BlockDeviceMappings && BlockDeviceMappings.length) {\n            const size = BlockDeviceMappings[0]?.Ebs?.VolumeSize;\n\n            this.$emit('update:diskSize', size);\n          } else {\n            this.$emit('update:diskSize', DEFAULT_NODE_GROUP_CONFIG.diskSize);\n          }\n        } else if (this.templateValue(rancherKey)) {\n          this.$emit(`update:${ rancherKey }`, this.templateValue(rancherKey));\n        } else {\n          this.$emit(`update:${ rancherKey }`, DEFAULT_NODE_GROUP_CONFIG[rancherKey as keyof typeof DEFAULT_NODE_GROUP_CONFIG]);\n        }\n      });\n\n      this.$nextTick(() => {\n        this.resourceTagKey = randomStr();\n        this.loadingSelectedVersion = false;\n      });\n    },\n\n    templateValue(field: string): string | null | AWS.TagSpecification | AWS.TagSpecification[] | AWS.BlockDeviceMapping[] {\n      if (this.hasNoLaunchTemplate) {\n        return null;\n      }\n\n      const launchTemplateKey = launchTemplateFieldMapping[field] as keyof AWS.LaunchTemplateVersionData;\n\n      if (!launchTemplateKey) {\n        return null;\n      }\n      const launchTemplateVal = this.selectedVersionData?.[launchTemplateKey];\n\n      if (launchTemplateVal !== undefined && (!(typeof launchTemplateVal === 'object') || !isEmpty(launchTemplateVal))) {\n        if (field === 'diskSize') {\n          const blockMapping = launchTemplateVal[0] as AWS.BlockDeviceMapping;\n\n          return blockMapping?.Ebs?.VolumeSize || null;\n        }\n        if (field === 'resourceTags') {\n          const tags = (launchTemplateVal || []) as AWS.TagSpecification[];\n\n          return tags.filter((tag: AWS.TagSpecification) => tag.ResourceType === 'instance')[0];\n        }\n\n        return launchTemplateVal;\n      }\n\n      return null;\n    },\n  },\n});\n</script>\n\n<template>\n  <div>\n    <h3>{{ t('eks.nodeGroups.groupDetails') }}</h3>\n    <div class=\"row mb-10\">\n      <div class=\"col span-6\">\n        <LabeledInput\n          :value=\"nodegroupName\"\n          label-key=\"eks.nodeGroups.name.label\"\n          :mode=\"mode\"\n          :disabled=\"!poolIsUnprovisioned\"\n          :rules=\"rules.nodegroupName\"\n          data-testid=\"eks-nodegroup-name\"\n          required\n          @update:value=\"$emit('update:nodegroupName', $event)\"\n        />\n      </div>\n\n      <div class=\"col span-6\">\n        <LabeledSelect\n          v-model:value=\"displayNodeRole\"\n          :mode=\"mode\"\n          data-testid=\"eks-noderole\"\n          label-key=\"eks.nodeGroups.nodeRole.label\"\n          :options=\"[defaultNodeRoleOption, ...ec2Roles]\"\n          option-label=\"RoleName\"\n          option-key=\"Arn\"\n          :disabled=\"!poolIsUnprovisioned\"\n          :loading=\"loadingRoles\"\n        />\n      </div>\n    </div>\n    <div class=\"row mb-10\">\n      <div class=\"col span-4\">\n        <LabeledInput\n          type=\"number\"\n          :value=\"desiredSize\"\n          label-key=\"eks.nodeGroups.desiredSize.label\"\n          :mode=\"mode\"\n          :rules=\"rules.desiredSize\"\n          @update:value=\"$emit('update:desiredSize', parseInt($event))\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <LabeledInput\n          type=\"number\"\n          :value=\"minSize\"\n          label-key=\"eks.nodeGroups.minSize.label\"\n          :mode=\"mode\"\n          :rules=\"rules.minSize\"\n          @update:value=\"$emit('update:minSize', parseInt($event))\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <LabeledInput\n          type=\"number\"\n          :value=\"maxSize\"\n          label-key=\"eks.nodeGroups.maxSize.label\"\n          :mode=\"mode\"\n          :rules=\"rules.maxSize\"\n          @update:value=\"$emit('update:maxSize', parseInt($event))\"\n        />\n      </div>\n    </div>\n    <Banner\n      v-if=\"!!minMaxDesiredErrors\"\n      color=\"error\"\n      :label=\"minMaxDesiredErrors\"\n    />\n    <div class=\"row mb-10\">\n      <div class=\"col span-6 mt-20\">\n        <KeyValue\n          :mode=\"mode\"\n          :title=\"t('eks.nodeGroups.groupLabels.label')\"\n          :read-allowed=\"false\"\n          :value=\"labels\"\n          :as-map=\"true\"\n          @update:value=\"$emit('update:labels', $event)\"\n        >\n          <template #title>\n            <h4>\n              {{ t('eks.nodeGroups.groupLabels.label') }}\n            </h4>\n          </template>\n        </KeyValue>\n      </div>\n      <div class=\"col span-6 mt-20\">\n        <KeyValue\n          :mode=\"mode\"\n          :title=\"t('eks.nodeGroups.groupTags.label')\"\n          :read-allowed=\"false\"\n          :as-map=\"true\"\n          :value=\"tags\"\n          data-testid=\"eks-resource-tags-input\"\n          @update:value=\"$emit('update:tags', $event)\"\n        >\n          <template #title>\n            <h4>{{ t('eks.nodeGroups.groupTags.label') }}</h4>\n          </template>\n        </KeyValue>\n      </div>\n    </div>\n    <hr\n      class=\"mb-20\"\n      role=\"none\"\n    >\n    <h3>{{ t('eks.nodeGroups.templateDetails') }}</h3>\n    <Banner\n      v-if=\"clusterWillUpgrade && !poolIsUnprovisioned\"\n      color=\"info\"\n      label-key=\"eks.nodeGroups.kubernetesVersion.clusterWillUpgrade\"\n      data-testid=\"eks-version-upgrade-banner\"\n    />\n    <div class=\"row mb-10\">\n      <div class=\"col span-4 upgrade-version\">\n        <LabeledInput\n          v-if=\"!nodeCanUpgrade\"\n          label-key=\"eks.nodeGroups.kubernetesVersion.label\"\n          :disabled=\"true\"\n          :value=\"version\"\n          data-testid=\"eks-version-display\"\n        />\n        <Checkbox\n          v-else\n          v-model:value=\"willUpgrade\"\n          :label=\"t('eks.nodeGroups.kubernetesVersion.upgrade', {from: originalNodeVersion, to: clusterVersion})\"\n          data-testid=\"eks-version-upgrade-checkbox\"\n          :disabled=\"isView\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <LabeledSelect\n          v-model:value=\"selectedLaunchTemplate\"\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.launchTemplate.label\"\n          :options=\"launchTemplateOptions\"\n          option-label=\"LaunchTemplateName\"\n          option-key=\"LaunchTemplateId\"\n          :disabled=\"!poolIsUnprovisioned\"\n          :loading=\"loadingLaunchTemplates\"\n          data-testid=\"eks-launch-template-dropdown\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <LabeledSelect\n          v-if=\"hasUserLaunchTemplate\"\n          :value=\"launchTemplate.version\"\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.launchTemplate.version\"\n          :options=\"launchTemplateVersionOptions\"\n          data-testid=\"eks-launch-template-version-dropdown\"\n          @update:value=\"$emit('update:launchTemplate', {...launchTemplate, version: $event})\"\n        />\n      </div>\n    </div>\n    <Banner\n      color=\"info\"\n      label-key=\"eks.nodeGroups.imageId.tooltip\"\n    />\n    <div class=\"row mb-10\">\n      <div class=\"col span-4\">\n        <LabeledInput\n          label-key=\"eks.nodeGroups.imageId.label\"\n          :mode=\"mode\"\n          :value=\"imageId\"\n          :disabled=\"hasUserLaunchTemplate\"\n          data-testid=\"eks-image-id-input\"\n          @update:value=\"$emit('update:imageId', $event)\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <LabeledSelect\n          :required=\"!requestSpotInstances && !templateValue('instanceType')\"\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.instanceType.label\"\n          :options=\"instanceTypeOptions\"\n          :loading=\"loadingInstanceTypes\"\n          :value=\"instanceType\"\n          :disabled=\"!!templateValue('instanceType') || requestSpotInstances\"\n          :tooltip=\"(requestSpotInstances && !templateValue('instanceType')) ? t('eks.nodeGroups.instanceType.tooltip'): ''\"\n          :rules=\"!requestSpotInstances ? rules.instanceType : []\"\n          data-testid=\"eks-instance-type-dropdown\"\n          @update:value=\"$emit('update:instanceType', $event)\"\n        />\n      </div>\n\n      <div class=\"col span-4\">\n        <UnitInput\n          :required=\"!templateValue('diskSize')\"\n          label-key=\"eks.nodeGroups.diskSize.label\"\n          :mode=\"mode\"\n          :value=\"diskSize\"\n          suffix=\"GB\"\n          :loading=\"loadingSelectedVersion\"\n          :disabled=\"!!templateValue('diskSize') || loadingSelectedVersion\"\n          :rules=\"rules.diskSize\"\n          data-testid=\"eks-disksize-input\"\n          @update:value=\"$emit('update:diskSize', $event)\"\n        />\n      </div>\n    </div>\n    <Banner\n      v-if=\"requestSpotInstances && hasUserLaunchTemplate\"\n      color=\"warning\"\n      :label=\"t('eks.nodeGroups.requestSpotInstances.warning')\"\n      data-testid=\"eks-spot-instance-banner\"\n    />\n    <div class=\"row mb-10\">\n      <div class=\"col span-4\">\n        <Checkbox\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.gpu.label\"\n          :value=\"gpu\"\n          :disabled=\"!!templateValue('imageId') || hasRancherLaunchTemplate\"\n          :tooltip=\"templateValue('imageId') ? t('eks.nodeGroups.gpu.tooltip') : ''\"\n          data-testid=\"eks-gpu-input\"\n          @update:value=\"$emit('update:gpu', $event)\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <Checkbox\n          :value=\"requestSpotInstances\"\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.requestSpotInstances.label\"\n          :disabled=\"hasRancherLaunchTemplate\"\n          @update:value=\"$emit('update:requestSpotInstances', $event)\"\n        />\n      </div>\n    </div>\n    <div\n      v-if=\"requestSpotInstances && !templateValue('instanceType')\"\n      class=\"row mb-10\"\n    >\n      <div\n        class=\"col span-6\"\n      >\n        <LabeledSelect\n          :mode=\"mode\"\n          :value=\"spotInstanceTypes\"\n          label-key=\"eks.nodeGroups.spotInstanceTypes.label\"\n          :options=\"spotInstanceTypeOptions\"\n          :multiple=\"true\"\n          :loading=\"loadingSelectedVersion || loadingInstanceTypes\"\n          data-testid=\"eks-spot-instance-type-dropdown\"\n          @update:value=\"$emit('update:spotInstanceTypes', $event)\"\n        />\n      </div>\n    </div>\n    <div class=\"row mb-15\">\n      <div class=\"col span-6 user-data\">\n        <LabeledInput\n          label-key=\"eks.nodeGroups.userData.label\"\n          :mode=\"mode\"\n          type=\"multiline\"\n          :value=\"userData\"\n          :disabled=\"hasUserLaunchTemplate\"\n          :placeholder=\"userDataPlaceholder\"\n          :sub-label=\"t('eks.nodeGroups.userData.tooltip', {}, true)\"\n          @update:value=\"$emit('update:userData', $event)\"\n        />\n        <FileSelector\n          :mode=\"mode\"\n          :label=\"t('generic.readFromFile')\"\n          class=\"role-tertiary mt-20\"\n          @selected=\"$emit('update:userData', $event)\"\n        />\n      </div>\n      <div class=\"col span-6\">\n        <LabeledSelect\n          :loading=\"loadingSshKeyPairs\"\n          :value=\"ec2SshKey\"\n          :options=\"sshKeyPairs\"\n          label-key=\"eks.nodeGroups.ec2SshKey.label\"\n          :mode=\"mode\"\n          :disabled=\"hasUserLaunchTemplate\"\n          :taggable=\"true\"\n          :searchable=\"true\"\n          data-testid=\"eks-nodegroup-ec2-key-select\"\n          @update:value=\"$emit('update:ec2SshKey', $event)\"\n        />\n      </div>\n    </div>\n    <div row=\"mb-10\">\n      <div class=\"col span-12 mt-20\">\n        <KeyValue\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.resourceTags.label\"\n          :value=\"resourceTags\"\n          :disabled=\"hasUserLaunchTemplate\"\n          :read-allowed=\"false\"\n          :as-map=\"true\"\n          @update:value=\"$emit('update:resourceTags', $event)\"\n        >\n          <template #title>\n            <h4>\n              {{ t('eks.nodeGroups.resourceTags.label') }}\n            </h4>\n          </template>\n        </KeyValue>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.user-data{\n  &>button{\n    float: right;\n  }\n}\n\n.upgrade-version {\n  display: flex;\n  align-items: center;\n}\n</style>\n"]}]}