{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownTrigger.vue?vue&type=template&id=12215990&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownTrigger.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVuZGVyU2xvdCBhcyBfcmVuZGVyU2xvdCwgY3JlYXRlQ29tbWVudFZOb2RlIGFzIF9jcmVhdGVDb21tZW50Vk5vZGUsIHdpdGhLZXlzIGFzIF93aXRoS2V5cywgd2l0aEN0eCBhcyBfd2l0aEN0eCwgb3BlbkJsb2NrIGFzIF9vcGVuQmxvY2ssIGNyZWF0ZUJsb2NrIGFzIF9jcmVhdGVCbG9jayB9IGZyb20gInZ1ZSI7CmV4cG9ydCBmdW5jdGlvbiByZW5kZXIoX2N0eCwgX2NhY2hlLCAkcHJvcHMsICRzZXR1cCwgJGRhdGEsICRvcHRpb25zKSB7CiAgICByZXR1cm4gKF9vcGVuQmxvY2soKSwgX2NyZWF0ZUJsb2NrKCRzZXR1cFsiUmNCdXR0b24iXSwgewogICAgICAgIHJlZjogImRyb3Bkb3duVHJpZ2dlciIsCiAgICAgICAgcm9sZTogImJ1dHRvbiIsCiAgICAgICAgImFyaWEtaGFzcG9wdXAiOiAibWVudSIsCiAgICAgICAgImFyaWEtZXhwYW5kZWQiOiAkc2V0dXAuaXNNZW51T3BlbiwKICAgICAgICBvbktleWRvd246IF93aXRoS2V5cygkc2V0dXAuaGFuZGxlS2V5ZG93biwgWyJlbnRlciIsICJzcGFjZSJdKSwKICAgICAgICBvbkNsaWNrOiBfY2FjaGVbMF0gfHwgKF9jYWNoZVswXSA9ICgkZXZlbnQpID0+ICgkc2V0dXAuc2hvd01lbnUodHJ1ZSkpKQogICAgfSwgewogICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFsKICAgICAgICAgICAgX3JlbmRlclNsb3QoX2N0eC4kc2xvdHMsICJkZWZhdWx0Iiwge30sICgpID0+IFsKICAgICAgICAgICAgICAgIF9jcmVhdGVDb21tZW50Vk5vZGUoIkVtcHR5IHNsb3QgY29udGVudCIpCiAgICAgICAgICAgIF0pCiAgICAgICAgXSksCiAgICAgICAgXzogMyAvKiBGT1JXQVJERUQgKi8KICAgIH0sIDggLyogUFJPUFMgKi8sIFsiYXJpYS1leHBhbmRlZCIsICJvbktleWRvd24iXSkpOwp9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownTrigger.vue?vue&type=template&id=12215990&ts=true", "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownTrigger.vue"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,IAAI,WAAW,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,QAAQ,IAAI,SAAS,EAAE,OAAO,IAAI,QAAQ,EAAE,SAAS,IAAI,UAAU,EAAE,WAAW,IAAI,YAAY,EAAE,MAAM,KAAK,CAAA;AAE5L,MAAM,UAAU,MAAM,CAAC,IAAS,EAAC,MAAW,EAAC,MAAW,EAAC,MAAW,EAAC,KAAU,EAAC,QAAa;IAC3F,OAAO,CAAC,UAAU,EAAE,EC0BpB,YAAA,CAWW,MAAA,CAAA,UAAA,CAAA,EAAA;QAVT,GAAG,EAAC,iBAAiB;QACrB,IAAI,EAAC,QAAQ;QACb,eAAa,EAAC,MAAM;QACnB,eAAa,EAAE,MAAA,CAAA,UAAU;QACzB,SAAO,EAAA,SAAA,CAAc,MAAA,CAAA,aAAa,EAAA,CAAA,OAAA,EAAA,OAAA,CAAA,CAAA;QAClC,OAAK,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAE,MAAA,CAAA,QAAQ,CAAA,IAAA,CAAA,CAAA,CAAA;KDzBjB,EAAE;QACD,OAAO,EAAE,QAAQ,CC0BjB,GAEO,EAAA,CAAA;YAFP,WAAA,CAEO,IAAA,CAAA,MAAA,EAAA,SAAA,EAAA,EAAA,EAFP,GAEO,EAAA,CAAA;gBADL,mBAAA,CAAA,oBAAA,CAAyB;aDxBxB,CAAC;SACH,CAAC;QACF,CAAC,EAAE,CAAC,CAAC,eAAe;KACrB,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC,CAAC,CAAA;AACpD,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownTrigger.vue.tsx", "sourceRoot": "", "sourcesContent": ["import { renderSlot as _renderSlot, createCommentVNode as _createCommentVNode, with<PERSON><PERSON><PERSON> as _withKeys, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock } from \"vue\"\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  return (_openBlock(), _createBlock($setup[\"RcButton\"], {\n    ref: \"dropdownTrigger\",\n    role: \"button\",\n    \"aria-haspopup\": \"menu\",\n    \"aria-expanded\": $setup.isMenuOpen,\n    onKeydown: _withKeys($setup.handleKeydown, [\"enter\",\"space\"]),\n    onClick: _cache[0] || (_cache[0] = ($event: any) => ($setup.showMenu(true)))\n  }, {\n    default: _withCtx(() => [\n      _renderSlot(_ctx.$slots, \"default\", {}, () => [\n        _createCommentVNode(\"Empty slot content\")\n      ])\n    ]),\n    _: 3 /* FORWARDED */\n  }, 8 /* PROPS */, [\"aria-expanded\", \"onKeydown\"]))\n}", "<script setup lang=\"ts\">\n/**\n * A button that opens a menu. Used in conjunction with `RcDropdown.vue`.\n */\nimport { inject, onMounted, ref } from 'vue';\nimport { RcButton, RcButtonType } from '@components/RcButton';\nimport { DropdownContext, defaultContext } from './types';\n\nconst {\n  showMenu,\n  registerTrigger,\n  isMenuOpen,\n  handleKeydown,\n} = inject<DropdownContext>('dropdownContext') || defaultContext;\n\nconst dropdownTrigger = ref<RcButtonType | null>(null);\n\nonMounted(() => {\n  registerTrigger(dropdownTrigger.value);\n});\n\nconst focus = () => {\n  dropdownTrigger?.value?.focus();\n};\n\ndefineExpose({ focus });\n</script>\n\n<template>\n  <RcButton\n    ref=\"dropdownTrigger\"\n    role=\"button\"\n    aria-haspopup=\"menu\"\n    :aria-expanded=\"isMenuOpen\"\n    @keydown.enter.space=\"handleKeydown\"\n    @click=\"showMenu(true)\"\n  >\n    <slot name=\"default\">\n      <!--Empty slot content-->\n    </slot>\n  </RcButton>\n</template>\n"]}]}