{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/useDropdownCollection.ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/useDropdownCollection.ts", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/useDropdownCollection.ts", "sourceRoot": "", "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/useDropdownCollection.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAE1B;;;;;GAKG;AACH,MAAM,CAAC,MAAM,qBAAqB,GAAG,GAAG,EAAE;IACxC,MAAM,aAAa,GAAG,GAAG,CAAY,EAAE,CAAC,CAAC;IACzC,MAAM,iBAAiB,GAAG,GAAG,CAAqB,IAAI,CAAC,CAAC;IACxD,MAAM,iBAAiB,GAAG,GAAG,CAAqB,IAAI,CAAC,CAAC;IAExD;;;OAGG;IACH,MAAM,0BAA0B,GAAG,CAAC,MAA0B,EAAE,EAAE;;QAChE,iBAAiB,CAAC,KAAK,GAAG,MAAM,CAAC;QACjC,IAAI,CAAA,MAAA,iBAAiB,CAAC,KAAK,0CAAE,iBAAiB,aAAY,WAAW,EAAE,CAAC;YACtE,qBAAqB,EAAE,CAAC;YACxB,IAAI,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,WAAW,EAAE,CAAC;gBAClD,iBAAiB,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC;QACH,CAAC;IACH,CAAC,CAAC;IAEF;;OAEG;IACH,MAAM,qBAAqB,GAAG,GAAG,EAAE;;QACjC,aAAa,CAAC,KAAK,GAAG,EAAE,CAAC;QACzB,MAAM,gBAAgB,GAAG,MAAA,iBAAiB,CAAC,KAAK,0CAAE,gBAAgB,CAAC,sBAAsB,CAAC,CAAC;QAE3F,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACpC,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAO;QACL,aAAa;QACb,iBAAiB;QACjB,iBAAiB;QACjB,0BAA0B;KAC3B,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["import { ref } from 'vue';\n\n/**\n * Manages a collection of dropdown items. Includes methods for registering\n * dropdown items and providing the collection to descendant components.\n *\n * @returns Dropdown collection methods and state.\n */\nexport const useDropdownCollection = () => {\n  const dropdownItems = ref<Element[]>([]);\n  const dropdownContainer = ref<HTMLElement | null>(null);\n  const firstDropdownItem = ref<HTMLElement | null>(null);\n\n  /**\n   * Registers the dropdown container and initializes dropdown items.\n   * @param target - The dropdown container element.\n   */\n  const registerDropdownCollection = (target: HTMLElement | null) => {\n    dropdownContainer.value = target;\n    if (dropdownContainer.value?.firstElementChild instanceof HTMLElement) {\n      registerDropdownItems();\n      if (dropdownItems.value[0] instanceof HTMLElement) {\n        firstDropdownItem.value = dropdownItems.value[0];\n      }\n    }\n  };\n\n  /**\n   * Registers dropdown items by querying the dropdown container for elements.\n   */\n  const registerDropdownItems = () => {\n    dropdownItems.value = [];\n    const dropdownNodeList = dropdownContainer.value?.querySelectorAll('[dropdown-menu-item]');\n\n    dropdownNodeList?.forEach((element) => {\n      dropdownItems.value.push(element);\n    });\n  };\n\n  return {\n    dropdownItems,\n    firstDropdownItem,\n    dropdownContainer,\n    registerDropdownCollection,\n  };\n};\n"]}]}