{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Accordion/Accordion.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Accordion/Accordion.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgZGVmaW5lQ29tcG9uZW50IH0gZnJvbSAndnVlJzsKaW1wb3J0IHsgbWFwR2V0dGVycyB9IGZyb20gJ3Z1ZXgnOwpleHBvcnQgZGVmYXVsdCBkZWZpbmVDb21wb25lbnQoewogICAgcHJvcHM6IHsKICAgICAgICB0aXRsZTogewogICAgICAgICAgICB0eXBlOiBTdHJpbmcsCiAgICAgICAgICAgIGRlZmF1bHQ6ICcnCiAgICAgICAgfSwKICAgICAgICB0aXRsZUtleTogewogICAgICAgICAgICB0eXBlOiBTdHJpbmcsCiAgICAgICAgICAgIGRlZmF1bHQ6IG51bGwKICAgICAgICB9LAogICAgICAgIG9wZW5Jbml0aWFsbHk6IHsKICAgICAgICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgICAgICAgZGVmYXVsdDogZmFsc2UKICAgICAgICB9CiAgICB9LAogICAgZGF0YSgpIHsKICAgICAgICByZXR1cm4geyBpc09wZW46IHRoaXMub3BlbkluaXRpYWxseSB9OwogICAgfSwKICAgIGNvbXB1dGVkOiB7IC4uLm1hcEdldHRlcnMoeyB0OiAnaTE4bi90JyB9KSB9LAogICAgbWV0aG9kczogewogICAgICAgIHRvZ2dsZSgpIHsKICAgICAgICAgICAgdGhpcy5pc09wZW4gPSAhdGhpcy5pc09wZW47CiAgICAgICAgfQogICAgfQp9KTsK"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Accordion/Accordion.vue"], "names": [], "mappings": "AACA,OAAO,EAAE,eAAc,EAAE,MAAO,KAAK,CAAA;AACrC,OAAO,EAAE,UAAS,EAAE,MAAO,MAAM,CAAA;AAEjC,eAAe,eAAe,CAAC;IAC7B,KAAK,EAAE;QACL,KAAK,EAAE;YACL,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,EAAC;SACX;QAED,QAAQ,EAAE;YACR,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,IAAG;SACb;QAED,aAAa,EAAE;YACb,IAAI,EAAK,OAAO;YAChB,OAAO,EAAE,KAAI;SACf;KACD;IAED,IAAI;QACF,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,aAAY,EAAG,CAAA;IACvC,CAAC;IAED,QAAQ,EAAE,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE,QAAO,EAAG,CAAA,EAAG;IAE5C,OAAO,EAAE;QACP,MAAM;YACJ,IAAI,CAAC,MAAK,GAAI,CAAC,IAAI,CAAC,MAAM,CAAA;QAC5B,CAAA;KACF;CACD,CAAC,CAAA", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Accordion/Accordion.vue.tsx", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent } from 'vue';\nimport { mapGetters } from 'vuex';\n\nexport default defineComponent({\n  props: {\n    title: {\n      type:    String,\n      default: ''\n    },\n\n    titleKey: {\n      type:    String,\n      default: null\n    },\n\n    openInitially: {\n      type:    Boolean,\n      default: false\n    }\n  },\n\n  data() {\n    return { isOpen: this.openInitially };\n  },\n\n  computed: { ...mapGetters({ t: 'i18n/t' }) },\n\n  methods: {\n    toggle() {\n      this.isOpen = !this.isOpen;\n    }\n  }\n});\n</script>\n\n<template>\n  <div class=\"accordion-container\">\n    <div\n      class=\"accordion-header\"\n      data-testid=\"accordion-header\"\n      @click=\"toggle\"\n    >\n      <i\n        class=\"icon text-primary\"\n        :class=\"{'icon-chevron-down':isOpen, 'icon-chevron-up':!isOpen}\"\n        data-testid=\"accordion-chevron\"\n      />\n      <slot name=\"header\">\n        <h2\n          data-testid=\"accordion-title-slot-content\"\n          class=\"mb-0\"\n        >\n          {{ titleKey ? t(titleKey) : title }}\n        </h2>\n      </slot>\n    </div>\n    <div\n      v-show=\"isOpen\"\n      class=\"accordion-body\"\n      data-testid=\"accordion-body\"\n    >\n      <slot />\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.accordion-container {\n  border: 1px solid var(--border)\n}\n.accordion-header {\n  padding: 16px 16px 16px 11px;\n  display: flex;\n  align-items: center;\n  &>*{\n    padding: 5px 0px 5px 0px;\n  }\n  I {\n    margin: 0px 10px 0px 10px;\n  }\n}\n.accordion-body {\n  padding: 0px 16px 16px;\n}\n</style>\n"]}]}