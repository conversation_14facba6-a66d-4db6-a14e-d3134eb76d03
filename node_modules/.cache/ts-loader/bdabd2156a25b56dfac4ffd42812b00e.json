{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/HeaderPageActionMenu.vue?vue&type=template&id=071de600&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/HeaderPageActionMenu.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgb3BlbkJsb2NrIGFzIF9vcGVuQmxvY2ssIGNyZWF0ZUJsb2NrIGFzIF9jcmVhdGVCbG9jayB9IGZyb20gInZ1ZSI7CmV4cG9ydCBmdW5jdGlvbiByZW5kZXIoX2N0eCwgX2NhY2hlLCAkcHJvcHMsICRzZXR1cCwgJGRhdGEsICRvcHRpb25zKSB7CiAgICByZXR1cm4gKF9vcGVuQmxvY2soKSwgX2NyZWF0ZUJsb2NrKCRzZXR1cFsiUmNEcm9wZG93bk1lbnUiXSwgewogICAgICAgIG9wdGlvbnM6ICRzZXR1cC5wYWdlQWN0aW9ucywKICAgICAgICAiYnV0dG9uLWFyaWEtbGFiZWwiOiBfY3R4LnQoJ25hdi5hY3Rpb25NZW51LmxhYmVsJyksCiAgICAgICAgImRyb3Bkb3duLWFyaWEtbGFiZWwiOiBfY3R4LnQoJ25hdi5hY3Rpb25NZW51LmJ1dHRvbi5sYWJlbCcpLAogICAgICAgICJkYXRhLXRlc3RpZCI6ICJwYWdlLWFjdGlvbnMtbWVudS1hY3Rpb24tYnV0dG9uIiwKICAgICAgICAiYnV0dG9uLXJvbGUiOiAidGVydGlhcnkiLAogICAgICAgIG9uU2VsZWN0OiAkc2V0dXAucGFnZUFjdGlvbgogICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgWyJvcHRpb25zIiwgImJ1dHRvbi1hcmlhLWxhYmVsIiwgImRyb3Bkb3duLWFyaWEtbGFiZWwiXSkpOwp9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/HeaderPageActionMenu.vue?vue&type=template&id=071de600&ts=true", "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/HeaderPageActionMenu.vue"], "names": ["t"], "mappings": "AAAA,OAAO,EAAE,SAAS,IAAI,UAAU,EAAE,WAAW,IAAI,YAAY,EAAE,MAAM,KAAK,CAAA;AAE1E,MAAM,UAAU,MAAM,CAAC,IAAS,EAAC,MAAW,EAAC,MAAW,EAAC,MAAW,EAAC,KAAU,EAAC,QAAa;IAC3F,OAAO,CAAC,UAAU,EAAE,ECUpB,YAAA,CAOE,MAAA,CAAA,gBAAA,CAAA,EAAA;QANC,OAAO,EAAE,MAAA,CAAA,WAAW;QACpB,mBAAiB,EAAEA,IAAAA,CAAAA,CAAC,CAAA,sBAAA,CAAA;QACpB,qBAAmB,EAAEA,IAAAA,CAAAA,CAAC,CAAA,6BAAA,CAAA;QACvB,aAAW,EAAC,iCAAiC;QAC7C,aAAW,EAAC,UAAU;QACrB,QAAM,EAAE,MAAA,CAAA,UAAU;KDTpB,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,SAAS,EAAE,mBAAmB,EAAE,qBAAqB,CAAC,CAAC,CAAC,CAAA;AACnF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/HeaderPageActionMenu.vue.tsx", "sourceRoot": "", "sourcesContent": ["import { openBlock as _openBlock, createBlock as _createBlock } from \"vue\"\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  return (_openBlock(), _createBlock($setup[\"RcDropdownMenu\"], {\n    options: $setup.pageActions,\n    \"button-aria-label\": _ctx.t('nav.actionMenu.label'),\n    \"dropdown-aria-label\": _ctx.t('nav.actionMenu.button.label'),\n    \"data-testid\": \"page-actions-menu-action-button\",\n    \"button-role\": \"tertiary\",\n    onSelect: $setup.pageAction\n  }, null, 8 /* PROPS */, [\"options\", \"button-aria-label\", \"dropdown-aria-label\"]))\n}", "<script setup lang=\"ts\">\nimport { computed } from 'vue';\nimport { useStore } from 'vuex';\nimport { RcDropdownMenu } from '@components/RcDropdown';\n\nconst store = useStore();\nconst pageActions = computed(() => store.getters.pageActions);\nconst pageAction = (_event: Event, action: string) => {\n  store.dispatch('handlePageAction', action);\n};\n</script>\n\n<template>\n  <rc-dropdown-menu\n    :options=\"pageActions\"\n    :button-aria-label=\"t('nav.actionMenu.label')\"\n    :dropdown-aria-label=\"t('nav.actionMenu.button.label')\"\n    data-testid=\"page-actions-menu-action-button\"\n    button-role=\"tertiary\"\n    @select=\"pageAction\"\n  />\n</template>\n"]}]}