{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/HeaderPageActionMenu.vue?vue&type=script&setup=true&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/HeaderPageActionMenu.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgZGVmaW5lQ29tcG9uZW50IGFzIF9kZWZpbmVDb21wb25lbnQgfSBmcm9tICd2dWUnOwppbXBvcnQgeyBjb21wdXRlZCB9IGZyb20gJ3Z1ZSc7CmltcG9ydCB7IHVzZVN0b3JlIH0gZnJvbSAndnVleCc7CmltcG9ydCB7IFJjRHJvcGRvd25NZW51IH0gZnJvbSAnQGNvbXBvbmVudHMvUmNEcm9wZG93bic7CmV4cG9ydCBkZWZhdWx0IC8qQF9fUFVSRV9fKi8gX2RlZmluZUNvbXBvbmVudCh7CiAgICBfX25hbWU6ICdIZWFkZXJQYWdlQWN0aW9uTWVudScsCiAgICBzZXR1cChfX3Byb3BzLCB7IGV4cG9zZTogX19leHBvc2UgfSkgewogICAgICAgIF9fZXhwb3NlKCk7CiAgICAgICAgY29uc3Qgc3RvcmUgPSB1c2VTdG9yZSgpOwogICAgICAgIGNvbnN0IHBhZ2VBY3Rpb25zID0gY29tcHV0ZWQoKCkgPT4gc3RvcmUuZ2V0dGVycy5wYWdlQWN0aW9ucyk7CiAgICAgICAgY29uc3QgcGFnZUFjdGlvbiA9IChfZXZlbnQsIGFjdGlvbikgPT4gewogICAgICAgICAgICBzdG9yZS5kaXNwYXRjaCgnaGFuZGxlUGFnZUFjdGlvbicsIGFjdGlvbik7CiAgICAgICAgfTsKICAgICAgICBjb25zdCBfX3JldHVybmVkX18gPSB7IHN0b3JlLCBwYWdlQWN0aW9ucywgcGFnZUFjdGlvbiwgZ2V0IFJjRHJvcGRvd25NZW51KCkgeyByZXR1cm4gUmNEcm9wZG93bk1lbnU7IH0gfTsKICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkoX19yZXR1cm5lZF9fLCAnX19pc1NjcmlwdFNldHVwJywgeyBlbnVtZXJhYmxlOiBmYWxzZSwgdmFsdWU6IHRydWUgfSk7CiAgICAgICAgcmV0dXJuIF9fcmV0dXJuZWRfXzsKICAgIH0KfSk7Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/HeaderPageActionMenu.vue?vue&type=script&setup=true&lang=ts", "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/HeaderPageActionMenu.vue"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,IAAI,gBAAgB,EAAE,MAAM,KAAK,CAAA;ACCzD,OAAO,EAAE,QAAQ,EAAE,MAAM,KAAK,CAAA;AAC9B,OAAO,EAAE,QAAQ,EAAE,MAAM,MAAM,CAAA;AAC/B,OAAO,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAA;ADGvD,eAAe,aAAa,CAAA,gBAAgB,CAAC;IAC3C,MAAM,EAAE,sBAAsB;IAC9B,KAAK,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE;QACnC,QAAQ,EAAE,CAAC;QCJb,MAAM,KAAK,GAAG,QAAQ,EAAE,CAAA;QACxB,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAA;QAC7D,MAAM,UAAU,GAAG,CAAC,MAAa,EAAE,MAAc,EAAE,EAAE;YACnD,KAAK,CAAC,QAAQ,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAA;QAC5C,CAAC,CAAA;QDQD,MAAM,YAAY,GAAG,EAAE,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,IAAI,cAAc,KAAK,OAAO,cAAc,CAAA,CAAC,CAAC,EAAE,CAAA;QACvG,MAAM,CAAC,cAAc,CAAC,YAAY,EAAE,iBAAiB,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAA;QAC1F,OAAO,YAAY,CAAA;IACnB,CAAC;CAEA,CAAC,CAAA", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/HeaderPageActionMenu.vue.tsx", "sourceRoot": "", "sourcesContent": ["import { defineComponent as _defineComponent } from 'vue'\nimport { computed } from 'vue';\nimport { useStore } from 'vuex';\nimport { RcDropdownMenu } from '@components/RcDropdown';\n\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'HeaderPageActionMenu',\n  setup(__props, { expose: __expose }) {\n  __expose();\n\nconst store = useStore();\nconst pageActions = computed(() => store.getters.pageActions);\nconst pageAction = (_event: Event, action: string) => {\n  store.dispatch('handlePageAction', action);\n};\n\nconst __returned__ = { store, pageActions, pageAction, get RcDropdownMenu() { return RcDropdownMenu } }\nObject.defineProperty(__returned__, '__isScriptSetup', { enumerable: false, value: true })\nreturn __returned__\n}\n\n})", "<script setup lang=\"ts\">\nimport { computed } from 'vue';\nimport { useStore } from 'vuex';\nimport { RcDropdownMenu } from '@components/RcDropdown';\n\nconst store = useStore();\nconst pageActions = computed(() => store.getters.pageActions);\nconst pageAction = (_event: Event, action: string) => {\n  store.dispatch('handlePageAction', action);\n};\n</script>\n\n<template>\n  <rc-dropdown-menu\n    :options=\"pageActions\"\n    :button-aria-label=\"t('nav.actionMenu.label')\"\n    :dropdown-aria-label=\"t('nav.actionMenu.button.label')\"\n    data-testid=\"page-actions-menu-action-button\"\n    button-role=\"tertiary\"\n    @select=\"pageAction\"\n  />\n</template>\n"]}]}