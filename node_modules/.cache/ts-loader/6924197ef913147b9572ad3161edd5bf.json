{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/TextArea/TextAreaAutoGrow.vue?vue&type=template&id=3d1ee14d&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/TextArea/TextAreaAutoGrow.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgbWVyZ2VQcm9wcyBhcyBfbWVyZ2VQcm9wcywgb3BlbkJsb2NrIGFzIF9vcGVuQmxvY2ssIGNyZWF0ZUVsZW1lbnRCbG9jayBhcyBfY3JlYXRlRWxlbWVudEJsb2NrIH0gZnJvbSAidnVlIjsKY29uc3QgX2hvaXN0ZWRfMSA9IFsidmFsdWUiLCAiZGF0YS10ZXN0aWQiLCAiZGlzYWJsZWQiLCAicGxhY2Vob2xkZXIiLCAic3BlbGxjaGVjayJdOwpleHBvcnQgZnVuY3Rpb24gcmVuZGVyKF9jdHgsIF9jYWNoZSwgJHByb3BzLCAkc2V0dXAsICRkYXRhLCAkb3B0aW9ucykgewogICAgcmV0dXJuIChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soInRleHRhcmVhIiwgX21lcmdlUHJvcHMoewogICAgICAgIHJlZjogInRhIiwKICAgICAgICB2YWx1ZTogX2N0eC52YWx1ZSwKICAgICAgICAiZGF0YS10ZXN0aWQiOiBfY3R4LiRhdHRyc1snZGF0YS10ZXN0aWQnXSA/IF9jdHguJGF0dHJzWydkYXRhLXRlc3RpZCddIDogJ3RleHQtYXJlYS1hdXRvLWdyb3cnLAogICAgICAgIGRpc2FibGVkOiBfY3R4LmlzRGlzYWJsZWQsCiAgICAgICAgc3R5bGU6IF9jdHguc3R5bGUsCiAgICAgICAgcGxhY2Vob2xkZXI6IF9jdHgucGxhY2Vob2xkZXIsCiAgICAgICAgY2xhc3M6IFtfY3R4LmNsYXNzTmFtZSwgIm5vLXJlc2l6ZSBuby1lYXNlIl0KICAgIH0sIF9jdHguJGF0dHJzLCB7CiAgICAgICAgc3BlbGxjaGVjazogX2N0eC5zcGVsbGNoZWNrLAogICAgICAgIG9uUGFzdGU6IF9jYWNoZVswXSB8fCAoX2NhY2hlWzBdID0gKCRldmVudCkgPT4gKF9jdHguJGVtaXQoJ3Bhc3RlJywgJGV2ZW50KSkpLAogICAgICAgIG9uSW5wdXQ6IF9jYWNoZVsxXSB8fCAoX2NhY2hlWzFdID0gKCRldmVudCkgPT4gKF9jdHgub25JbnB1dCgkZXZlbnQpKSksCiAgICAgICAgb25Gb2N1czogX2NhY2hlWzJdIHx8IChfY2FjaGVbMl0gPSAoJGV2ZW50KSA9PiAoX2N0eC4kZW1pdCgnZm9jdXMnLCAkZXZlbnQpKSksCiAgICAgICAgb25CbHVyOiBfY2FjaGVbM10gfHwgKF9jYWNoZVszXSA9ICgkZXZlbnQpID0+IChfY3R4LiRlbWl0KCdibHVyJywgJGV2ZW50KSkpCiAgICB9KSwgbnVsbCwgMTYgLyogRlVMTF9QUk9QUyAqLywgX2hvaXN0ZWRfMSkpOwp9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/TextArea/TextAreaAutoGrow.vue?vue&type=template&id=3d1ee14d&ts=true", "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/TextArea/TextAreaAutoGrow.vue"], "names": ["value", "$attrs", "isDisabled", "style", "placeholder", "className", "spellcheck", "$emit", "onInput"], "mappings": "AAAA,OAAO,EAAE,UAAU,IAAI,WAAW,EAAE,SAAS,IAAI,UAAU,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,MAAM,KAAK,CAAA;AAEnH,MAAM,UAAU,GAAG,CAAC,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,aAAa,EAAE,YAAY,CAAC,CAAA;AAEpF,MAAM,UAAU,MAAM,CAAC,IAAS,EAAC,MAAW,EAAC,MAAW,EAAC,MAAW,EAAC,KAAU,EAAC,QAAa;IAC3F,OAAO,CAAC,UAAU,EAAE,ECiLpB,mBAAA,CAeE,UAAA,EAfF,WAAA,CAeE;QAdA,GAAG,EAAC,IAAI;QACP,KAAK,EAAEA,IAAAA,CAAAA,KAAK;QACZ,aAAW,EAAEC,IAAAA,CAAAA,MAAM,CAAA,aAAA,CAAA,CAAA,CAAA,CAAkBA,IAAAA,CAAAA,MAAM,CAAA,aAAA,CAAA,CAAA,CAAA,CAAA,qBAAA;QAC3C,QAAQ,EAAEC,IAAAA,CAAAA,UAAU;QACpB,KAAK,EAAEC,IAAAA,CAAAA,KAAK;QACZ,WAAW,EAAEC,IAAAA,CAAAA,WAAW;QACxB,KAAK,EAAA,CAAEC,IAAAA,CAAAA,SAAS,EACX,mBAAmB,CAAA;KDjL1B,ECkLSJ,IAAAA,CAAAA,MAAM,EAAA;QACb,UAAU,EAAEK,IAAAA,CAAAA,UAAU;QACtB,OAAK,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEC,IAAAA,CAAAA,KAAK,CAAA,OAAA,EAAU,MAAM,CAAA,CAAA,CAAA;QAC5B,OAAK,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEC,IAAAA,CAAAA,OAAO,CAAC,MAAM,CAAA,CAAA,CAAA;QACrB,OAAK,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAED,IAAAA,CAAAA,KAAK,CAAA,OAAA,EAAU,MAAM,CAAA,CAAA,CAAA;QAC5B,MAAI,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEA,IAAAA,CAAAA,KAAK,CAAA,MAAA,EAAS,MAAM,CAAA,CAAA,CAAA;KDjL5B,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC,CAAA;AAC7C,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/TextArea/TextAreaAutoGrow.vue.tsx", "sourceRoot": "", "sourcesContent": ["import { mergeProps as _mergeProps, openBlock as _openBlock, createElement<PERSON>lock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = [\"value\", \"data-testid\", \"disabled\", \"placeholder\", \"spellcheck\"]\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  return (_openBlock(), _createElementBlock(\"textarea\", _mergeProps({\n    ref: \"ta\",\n    value: _ctx.value,\n    \"data-testid\": _ctx.$attrs['data-testid'] ? _ctx.$attrs['data-testid'] : 'text-area-auto-grow',\n    disabled: _ctx.isDisabled,\n    style: _ctx.style,\n    placeholder: _ctx.placeholder,\n    class: [_ctx.className, \"no-resize no-ease\"]\n  }, _ctx.$attrs, {\n    spellcheck: _ctx.spellcheck,\n    onPaste: _cache[0] || (_cache[0] = ($event: any) => (_ctx.$emit('paste', $event))),\n    onInput: _cache[1] || (_cache[1] = ($event: any) => (_ctx.onInput($event))),\n    onFocus: _cache[2] || (_cache[2] = ($event: any) => (_ctx.$emit('focus', $event))),\n    onBlur: _cache[3] || (_cache[3] = ($event: any) => (_ctx.$emit('blur', $event)))\n  }), null, 16 /* FULL_PROPS */, _hoisted_1))\n}", "<script lang=\"ts\">\nimport { defineComponent, inject, PropType } from 'vue';\nimport { debounce } from 'lodash';\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\n\ninterface NonReactiveProps {\n  queueResize(): void;\n}\n\nconst provideProps: NonReactiveProps = {\n  queueResize() {\n    // noop\n  }\n};\n\nexport default defineComponent({\n  inheritAttrs: false,\n\n  props: {\n    value: {\n      type:     String,\n      required: true\n    },\n\n    class: {\n      type:    [String, Array, Object] as PropType<string | unknown[] | Record<string, boolean>>,\n      default: ''\n    },\n\n    /**\n     * Sets the edit mode for Text Area.\n     * @values _EDIT, _VIEW\n     */\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n\n    /**\n     * Sets the Minimum height for Text Area. Prevents the height from becoming\n     * smaller than the value specified in minHeight.\n     */\n    minHeight: {\n      type:    Number,\n      default: 25\n    },\n\n    /**\n     * Sets the maximum height for Text Area. Prevents the height from becoming\n     * larger than the value specified in maxHeight.\n     */\n    maxHeight: {\n      type:    Number,\n      default: 200\n    },\n\n    /**\n     * Text that appears in the Text Area when it has no value set.\n     */\n    placeholder: {\n      type:    String,\n      default: ''\n    },\n\n    /**\n     * Specifies whether Text Area is subject to spell checking by the\n     * underlying browser/OS.\n     */\n    spellcheck: {\n      type:    Boolean,\n      default: true\n    },\n\n    /**\n     * Disables the Text Area.\n     */\n    disabled: {\n      type:    Boolean,\n      default: false\n    }\n  },\n\n  emits: ['update:value', 'paste', 'focus', 'blur'],\n\n  setup() {\n    const queueResize = inject('queueResize', provideProps.queueResize);\n\n    return { queueResize };\n  },\n\n  data() {\n    return {\n      curHeight: this.minHeight,\n      overflow:  'hidden'\n    };\n  },\n\n  computed: {\n    /**\n     * Determines if the Text Area should be disabled.\n     */\n    isDisabled(): boolean {\n      return this.disabled || this.mode === _VIEW;\n    },\n\n    /**\n     * Sets the height to one-line for SSR pageload so that it's already right\n     * (unless the input is long)\n     */\n    style(): string {\n      return `height: ${ this.curHeight }px; overflow: ${ this.overflow };`;\n    },\n\n    className(): string | unknown[] | Record<string, boolean> {\n      return this.class;\n    }\n  },\n\n  watch: {\n    $attrs: {\n      deep: true,\n      handler() {\n        this.queueResize();\n      }\n    }\n  },\n\n  created() {\n    this.queueResize = debounce(this.autoSize, 100);\n  },\n\n  mounted() {\n    (this.$refs.ta as HTMLElement).style.height = `${ this.curHeight }px`;\n    this.$nextTick(() => {\n      this.autoSize();\n    });\n  },\n\n  methods: {\n    /**\n     * Emits the input event and resizes the Text Area.\n    */\n    onInput(event: Event): void {\n      const val = (event?.target as HTMLInputElement)?.value;\n\n      this.$emit('update:value', val);\n      this.queueResize();\n    },\n\n    /**\n     * Gives focus to the Text Area.\n     */\n    focus(): void {\n      (this.$refs?.ta as HTMLElement).focus();\n    },\n\n    /**\n     * Sets the overflowY and height of the Text Area based on the content\n     * entered (calculated via scroll height).\n     */\n    autoSize(): void {\n      const el = this.$refs.ta as HTMLElement;\n\n      if (!el) {\n        return;\n      }\n\n      el.style.height = '1px';\n\n      const border = parseInt(getComputedStyle(el).getPropertyValue('borderTopWidth'), 10) || 0 + parseInt(getComputedStyle(el).getPropertyValue('borderBottomWidth'), 10) || 0;\n      const neu = Math.max(this.minHeight, Math.min(el.scrollHeight + border, this.maxHeight));\n\n      el.style.overflowY = el.scrollHeight > neu ? 'auto' : 'hidden';\n      el.style.height = `${ neu }px`;\n\n      this.curHeight = neu;\n    }\n  }\n});\n</script>\n\n<template>\n  <textarea\n    ref=\"ta\"\n    :value=\"value\"\n    :data-testid=\"$attrs['data-testid'] ? $attrs['data-testid'] : 'text-area-auto-grow'\"\n    :disabled=\"isDisabled\"\n    :style=\"style\"\n    :placeholder=\"placeholder\"\n    :class=\"className\"\n    class=\"no-resize no-ease\"\n    v-bind=\"$attrs\"\n    :spellcheck=\"spellcheck\"\n    @paste=\"$emit('paste', $event)\"\n    @input=\"onInput($event)\"\n    @focus=\"$emit('focus', $event)\"\n    @blur=\"$emit('blur', $event)\"\n  />\n</template>\n"]}]}