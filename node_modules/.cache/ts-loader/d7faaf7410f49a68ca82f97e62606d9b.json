{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/useDropdownContext.ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/useDropdownContext.ts", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/useDropdownContext.ts", "sourceRoot": "", "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/useDropdownContext.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,KAAK,CAAC;AAC1D,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAGhE,MAAM,eAAe,GAAG,WAAW,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC;AAErD;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAG,CAAC,IAA4B,EAAE,EAAE;IACjE,MAAM,EACJ,aAAa,EACb,iBAAiB,EACjB,iBAAiB,EACjB,0BAA0B,GAC3B,GAAG,qBAAqB,EAAE,CAAC;IAE5B,MAAM,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;IAE9B;;;OAGG;IACH,MAAM,QAAQ,GAAG,CAAC,IAAa,EAAE,EAAE;QACjC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC;QAC3B,CAAC;QACD,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;IAC5B,CAAC,CAAC;IAEF;;;MAGE;IACF,MAAM,eAAe,GAAG,GAAG,CAAsB,IAAI,CAAC,CAAC;IAEvD;;;OAGG;IACH,MAAM,eAAe,GAAG,CAAC,UAAwB,EAAE,EAAE;QACnD,eAAe,CAAC,KAAK,GAAG,UAAU,CAAC;IACrC,CAAC,CAAC;IAEF;;OAEG;IACH,MAAM,WAAW,GAAG,GAAG,EAAE;;QACvB,QAAQ,CAAC,KAAK,CAAC,CAAC;QAChB,MAAA,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,KAAK,0CAAE,KAAK,EAAE,CAAC;IAClC,CAAC,CAAC;IAEF;;;SAGK;IACL,MAAM,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;IAE9B,MAAM,aAAa,GAAG,GAAG,EAAE;QACzB,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC;IAC1B,CAAC,CAAC;IAEF;;OAEG;IACH,MAAM,QAAQ,GAAG,GAAG,EAAE;QACpB,QAAQ,CAAC,GAAG,EAAE;;YACZ,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;gBACtB,MAAA,iBAAiB,CAAC,KAAK,0CAAE,KAAK,EAAE,CAAC;gBAEjC,OAAO;YACT,CAAC;YAED,MAAA,iBAAiB,CAAC,KAAK,0CAAE,KAAK,EAAE,CAAC;YACjC,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC;QAC3B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF;;;MAGE;IACF,MAAM,sBAAsB,GAAG,GAAG,EAAE;QAClC,OAAO,CAAC,iBAAiB,EAAE;YACzB,QAAQ;YACR,eAAe;YACf,UAAU;YACV,aAAa;YACb,KAAK,EAAc,GAAG,EAAE,CAAC,WAAW,EAAE;YACtC,iBAAiB,EAAE,GAAG,EAAE;gBACtB,QAAQ,EAAE,CAAC;YACb,CAAC;YACD,aAAa;SACd,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAO;QACL,UAAU;QACV,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,sBAAsB;QACtB,0BAA0B;QAC1B,aAAa;KACd,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["import { ref, provide, nextTick, defineEmits } from 'vue';\nimport { useDropdownCollection } from './useDropdownCollection';\nimport { RcButtonType } from '@components/RcButton';\n\nconst rcDropdownEmits = defineEmits(['update:open']);\n\n/**\n * Composable that provides the context for a dropdown menu. Includes methods\n * and state for managing the dropdown's visibility, focus, and keyboard\n * interactions.\n *\n * @param firstDropdownItem - First item in the dropdown menu.\n * @returns Dropdown context methods and state. Used for programmatic\n * interactions and setting focus.\n */\nexport const useDropdownContext = (emit: typeof rcDropdownEmits) => {\n  const {\n    dropdownItems,\n    firstDropdownItem,\n    dropdownContainer,\n    registerDropdownCollection,\n  } = useDropdownCollection();\n\n  const isMenuOpen = ref(false);\n\n  /**\n   * Controls the visibility of the dropdown menu.\n   * @param show - Whether to show or hide the dropdown menu.\n   */\n  const showMenu = (show: boolean) => {\n    if (!show) {\n      didKeydown.value = false;\n    }\n    isMenuOpen.value = show;\n    emit('update:open', show);\n  };\n\n  /**\n  * A ref for the dropdown trigger element. Used for programmatic\n  * interactions and setting focus.\n  */\n  const dropdownTrigger = ref<RcButtonType | null>(null);\n\n  /**\n   * Registers the dropdown trigger element.\n   * @param triggerRef - The dropdown trigger element.\n   */\n  const registerTrigger = (triggerRef: RcButtonType) => {\n    dropdownTrigger.value = triggerRef;\n  };\n\n  /**\n   * Returns focus to the dropdown trigger and closes the menu.\n   */\n  const returnFocus = () => {\n    showMenu(false);\n    dropdownTrigger?.value?.focus();\n  };\n\n  /**\n     * Tracks if a keydown event has occurred. Important for distinguishing keyboard\n     * events from mouse events.\n     */\n  const didKeydown = ref(false);\n\n  const handleKeydown = () => {\n    didKeydown.value = true;\n  };\n\n  /**\n   * Sets focus to the first dropdown item if a keydown event has occurred.\n   */\n  const setFocus = () => {\n    nextTick(() => {\n      if (!didKeydown.value) {\n        dropdownContainer.value?.focus();\n\n        return;\n      }\n\n      firstDropdownItem.value?.focus();\n      didKeydown.value = false;\n    });\n  };\n\n  /**\n  * Provides Dropdown Context data and methods to descendants of RcDropdown.\n  * Accessed in descendents with the `inject()` function.\n  */\n  const provideDropdownContext = () => {\n    provide('dropdownContext', {\n      showMenu,\n      registerTrigger,\n      isMenuOpen,\n      dropdownItems,\n      close:             () => returnFocus(),\n      focusFirstElement: () => {\n        setFocus();\n      },\n      handleKeydown,\n    });\n  };\n\n  return {\n    isMenuOpen,\n    showMenu,\n    returnFocus,\n    setFocus,\n    provideDropdownContext,\n    registerDropdownCollection,\n    handleKeydown,\n  };\n};\n"]}]}