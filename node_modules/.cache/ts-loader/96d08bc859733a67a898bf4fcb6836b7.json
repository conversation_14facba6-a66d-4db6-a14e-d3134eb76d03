{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/TextArea/TextAreaAutoGrow.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/TextArea/TextAreaAutoGrow.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgZGVmaW5lQ29tcG9uZW50LCBpbmplY3QgfSBmcm9tICd2dWUnOwppbXBvcnQgeyBkZWJvdW5jZSB9IGZyb20gJ2xvZGFzaCc7CmltcG9ydCB7IF9FRElULCBfVklFVyB9IGZyb20gJ0BzaGVsbC9jb25maWcvcXVlcnktcGFyYW1zJzsKY29uc3QgcHJvdmlkZVByb3BzID0gewogICAgcXVldWVSZXNpemUoKSB7CiAgICAgICAgLy8gbm9vcAogICAgfQp9OwpleHBvcnQgZGVmYXVsdCBkZWZpbmVDb21wb25lbnQoewogICAgaW5oZXJpdEF0dHJzOiBmYWxzZSwKICAgIHByb3BzOiB7CiAgICAgICAgdmFsdWU6IHsKICAgICAgICAgICAgdHlwZTogU3RyaW5nLAogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZQogICAgICAgIH0sCiAgICAgICAgY2xhc3M6IHsKICAgICAgICAgICAgdHlwZTogW1N0cmluZywgQXJyYXksIE9iamVjdF0sCiAgICAgICAgICAgIGRlZmF1bHQ6ICcnCiAgICAgICAgfSwKICAgICAgICAvKioKICAgICAgICAgKiBTZXRzIHRoZSBlZGl0IG1vZGUgZm9yIFRleHQgQXJlYS4KICAgICAgICAgKiBAdmFsdWVzIF9FRElULCBfVklFVwogICAgICAgICAqLwogICAgICAgIG1vZGU6IHsKICAgICAgICAgICAgdHlwZTogU3RyaW5nLAogICAgICAgICAgICBkZWZhdWx0OiBfRURJVAogICAgICAgIH0sCiAgICAgICAgLyoqCiAgICAgICAgICogU2V0cyB0aGUgTWluaW11bSBoZWlnaHQgZm9yIFRleHQgQXJlYS4gUHJldmVudHMgdGhlIGhlaWdodCBmcm9tIGJlY29taW5nCiAgICAgICAgICogc21hbGxlciB0aGFuIHRoZSB2YWx1ZSBzcGVjaWZpZWQgaW4gbWluSGVpZ2h0LgogICAgICAgICAqLwogICAgICAgIG1pbkhlaWdodDogewogICAgICAgICAgICB0eXBlOiBOdW1iZXIsCiAgICAgICAgICAgIGRlZmF1bHQ6IDI1CiAgICAgICAgfSwKICAgICAgICAvKioKICAgICAgICAgKiBTZXRzIHRoZSBtYXhpbXVtIGhlaWdodCBmb3IgVGV4dCBBcmVhLiBQcmV2ZW50cyB0aGUgaGVpZ2h0IGZyb20gYmVjb21pbmcKICAgICAgICAgKiBsYXJnZXIgdGhhbiB0aGUgdmFsdWUgc3BlY2lmaWVkIGluIG1heEhlaWdodC4KICAgICAgICAgKi8KICAgICAgICBtYXhIZWlnaHQ6IHsKICAgICAgICAgICAgdHlwZTogTnVtYmVyLAogICAgICAgICAgICBkZWZhdWx0OiAyMDAKICAgICAgICB9LAogICAgICAgIC8qKgogICAgICAgICAqIFRleHQgdGhhdCBhcHBlYXJzIGluIHRoZSBUZXh0IEFyZWEgd2hlbiBpdCBoYXMgbm8gdmFsdWUgc2V0LgogICAgICAgICAqLwogICAgICAgIHBsYWNlaG9sZGVyOiB7CiAgICAgICAgICAgIHR5cGU6IFN0cmluZywKICAgICAgICAgICAgZGVmYXVsdDogJycKICAgICAgICB9LAogICAgICAgIC8qKgogICAgICAgICAqIFNwZWNpZmllcyB3aGV0aGVyIFRleHQgQXJlYSBpcyBzdWJqZWN0IHRvIHNwZWxsIGNoZWNraW5nIGJ5IHRoZQogICAgICAgICAqIHVuZGVybHlpbmcgYnJvd3Nlci9PUy4KICAgICAgICAgKi8KICAgICAgICBzcGVsbGNoZWNrOiB7CiAgICAgICAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgICAgICAgIGRlZmF1bHQ6IHRydWUKICAgICAgICB9LAogICAgICAgIC8qKgogICAgICAgICAqIERpc2FibGVzIHRoZSBUZXh0IEFyZWEuCiAgICAgICAgICovCiAgICAgICAgZGlzYWJsZWQ6IHsKICAgICAgICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgICAgICAgZGVmYXVsdDogZmFsc2UKICAgICAgICB9CiAgICB9LAogICAgZW1pdHM6IFsndXBkYXRlOnZhbHVlJywgJ3Bhc3RlJywgJ2ZvY3VzJywgJ2JsdXInXSwKICAgIHNldHVwKCkgewogICAgICAgIGNvbnN0IHF1ZXVlUmVzaXplID0gaW5qZWN0KCdxdWV1ZVJlc2l6ZScsIHByb3ZpZGVQcm9wcy5xdWV1ZVJlc2l6ZSk7CiAgICAgICAgcmV0dXJuIHsgcXVldWVSZXNpemUgfTsKICAgIH0sCiAgICBkYXRhKCkgewogICAgICAgIHJldHVybiB7CiAgICAgICAgICAgIGN1ckhlaWdodDogdGhpcy5taW5IZWlnaHQsCiAgICAgICAgICAgIG92ZXJmbG93OiAnaGlkZGVuJwogICAgICAgIH07CiAgICB9LAogICAgY29tcHV0ZWQ6IHsKICAgICAgICAvKioKICAgICAgICAgKiBEZXRlcm1pbmVzIGlmIHRoZSBUZXh0IEFyZWEgc2hvdWxkIGJlIGRpc2FibGVkLgogICAgICAgICAqLwogICAgICAgIGlzRGlzYWJsZWQoKSB7CiAgICAgICAgICAgIHJldHVybiB0aGlzLmRpc2FibGVkIHx8IHRoaXMubW9kZSA9PT0gX1ZJRVc7CiAgICAgICAgfSwKICAgICAgICAvKioKICAgICAgICAgKiBTZXRzIHRoZSBoZWlnaHQgdG8gb25lLWxpbmUgZm9yIFNTUiBwYWdlbG9hZCBzbyB0aGF0IGl0J3MgYWxyZWFkeSByaWdodAogICAgICAgICAqICh1bmxlc3MgdGhlIGlucHV0IGlzIGxvbmcpCiAgICAgICAgICovCiAgICAgICAgc3R5bGUoKSB7CiAgICAgICAgICAgIHJldHVybiBgaGVpZ2h0OiAke3RoaXMuY3VySGVpZ2h0fXB4OyBvdmVyZmxvdzogJHt0aGlzLm92ZXJmbG93fTtgOwogICAgICAgIH0sCiAgICAgICAgY2xhc3NOYW1lKCkgewogICAgICAgICAgICByZXR1cm4gdGhpcy5jbGFzczsKICAgICAgICB9CiAgICB9LAogICAgd2F0Y2g6IHsKICAgICAgICAkYXR0cnM6IHsKICAgICAgICAgICAgZGVlcDogdHJ1ZSwKICAgICAgICAgICAgaGFuZGxlcigpIHsKICAgICAgICAgICAgICAgIHRoaXMucXVldWVSZXNpemUoKTsKICAgICAgICAgICAgfQogICAgICAgIH0KICAgIH0sCiAgICBjcmVhdGVkKCkgewogICAgICAgIHRoaXMucXVldWVSZXNpemUgPSBkZWJvdW5jZSh0aGlzLmF1dG9TaXplLCAxMDApOwogICAgfSwKICAgIG1vdW50ZWQoKSB7CiAgICAgICAgdGhpcy4kcmVmcy50YS5zdHlsZS5oZWlnaHQgPSBgJHt0aGlzLmN1ckhlaWdodH1weGA7CiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgICB0aGlzLmF1dG9TaXplKCk7CiAgICAgICAgfSk7CiAgICB9LAogICAgbWV0aG9kczogewogICAgICAgIC8qKgogICAgICAgICAqIEVtaXRzIHRoZSBpbnB1dCBldmVudCBhbmQgcmVzaXplcyB0aGUgVGV4dCBBcmVhLgogICAgICAgICovCiAgICAgICAgb25JbnB1dChldmVudCkgewogICAgICAgICAgICB2YXIgX2E7CiAgICAgICAgICAgIGNvbnN0IHZhbCA9IChfYSA9IGV2ZW50ID09PSBudWxsIHx8IGV2ZW50ID09PSB2b2lkIDAgPyB2b2lkIDAgOiBldmVudC50YXJnZXQpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS52YWx1ZTsKICAgICAgICAgICAgdGhpcy4kZW1pdCgndXBkYXRlOnZhbHVlJywgdmFsKTsKICAgICAgICAgICAgdGhpcy5xdWV1ZVJlc2l6ZSgpOwogICAgICAgIH0sCiAgICAgICAgLyoqCiAgICAgICAgICogR2l2ZXMgZm9jdXMgdG8gdGhlIFRleHQgQXJlYS4KICAgICAgICAgKi8KICAgICAgICBmb2N1cygpIHsKICAgICAgICAgICAgdmFyIF9hOwogICAgICAgICAgICAoKF9hID0gdGhpcy4kcmVmcykgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLnRhKS5mb2N1cygpOwogICAgICAgIH0sCiAgICAgICAgLyoqCiAgICAgICAgICogU2V0cyB0aGUgb3ZlcmZsb3dZIGFuZCBoZWlnaHQgb2YgdGhlIFRleHQgQXJlYSBiYXNlZCBvbiB0aGUgY29udGVudAogICAgICAgICAqIGVudGVyZWQgKGNhbGN1bGF0ZWQgdmlhIHNjcm9sbCBoZWlnaHQpLgogICAgICAgICAqLwogICAgICAgIGF1dG9TaXplKCkgewogICAgICAgICAgICBjb25zdCBlbCA9IHRoaXMuJHJlZnMudGE7CiAgICAgICAgICAgIGlmICghZWwpIHsKICAgICAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgICAgfQogICAgICAgICAgICBlbC5zdHlsZS5oZWlnaHQgPSAnMXB4JzsKICAgICAgICAgICAgY29uc3QgYm9yZGVyID0gcGFyc2VJbnQoZ2V0Q29tcHV0ZWRTdHlsZShlbCkuZ2V0UHJvcGVydHlWYWx1ZSgnYm9yZGVyVG9wV2lkdGgnKSwgMTApIHx8IDAgKyBwYXJzZUludChnZXRDb21wdXRlZFN0eWxlKGVsKS5nZXRQcm9wZXJ0eVZhbHVlKCdib3JkZXJCb3R0b21XaWR0aCcpLCAxMCkgfHwgMDsKICAgICAgICAgICAgY29uc3QgbmV1ID0gTWF0aC5tYXgodGhpcy5taW5IZWlnaHQsIE1hdGgubWluKGVsLnNjcm9sbEhlaWdodCArIGJvcmRlciwgdGhpcy5tYXhIZWlnaHQpKTsKICAgICAgICAgICAgZWwuc3R5bGUub3ZlcmZsb3dZID0gZWwuc2Nyb2xsSGVpZ2h0ID4gbmV1ID8gJ2F1dG8nIDogJ2hpZGRlbic7CiAgICAgICAgICAgIGVsLnN0eWxlLmhlaWdodCA9IGAke25ldX1weGA7CiAgICAgICAgICAgIHRoaXMuY3VySGVpZ2h0ID0gbmV1OwogICAgICAgIH0KICAgIH0KfSk7Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/TextArea/TextAreaAutoGrow.vue"], "names": [], "mappings": "AACA,OAAO,EAAE,eAAe,EAAE,MAAM,EAAW,MAAO,KAAK,CAAA;AACvD,OAAO,EAAE,QAAO,EAAE,MAAO,QAAQ,CAAA;AACjC,OAAO,EAAE,KAAK,EAAE,KAAI,EAAE,MAAO,4BAA4B,CAAA;AAMzD,MAAM,YAAY,GAAqB;IACrC,WAAW;QACT,OAAM;IACR,CAAA;CACD,CAAA;AAED,eAAe,eAAe,CAAC;IAC7B,YAAY,EAAE,KAAK;IAEnB,KAAK,EAAE;QACL,KAAK,EAAE;YACL,IAAI,EAAM,MAAM;YAChB,QAAQ,EAAE,IAAG;SACd;QAED,KAAK,EAAE;YACL,IAAI,EAAK,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAA2D;YAC1F,OAAO,EAAE,EAAC;SACX;QAED;;;WAGE;QACF,IAAI,EAAE;YACJ,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,KAAI;SACd;QAED;;;WAGE;QACF,SAAS,EAAE;YACT,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,EAAC;SACX;QAED;;;WAGE;QACF,SAAS,EAAE;YACT,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,GAAE;SACZ;QAED;;WAEE;QACF,WAAW,EAAE;YACX,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,EAAC;SACX;QAED;;;WAGE;QACF,UAAU,EAAE;YACV,IAAI,EAAK,OAAO;YAChB,OAAO,EAAE,IAAG;SACb;QAED;;WAEE;QACF,QAAQ,EAAE;YACR,IAAI,EAAK,OAAO;YAChB,OAAO,EAAE,KAAI;SACf;KACD;IAED,KAAK,EAAE,CAAC,cAAc,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC;IAEjD,KAAK;QACH,MAAM,WAAU,GAAI,MAAM,CAAC,aAAa,EAAE,YAAY,CAAC,WAAW,CAAC,CAAA;QAEnE,OAAO,EAAE,WAAU,EAAG,CAAA;IACxB,CAAC;IAED,IAAI;QACF,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAG,QAAO;SACnB,CAAA;IACH,CAAC;IAED,QAAQ,EAAE;QACR;;WAEE;QACF,UAAU;YACR,OAAO,IAAI,CAAC,QAAO,IAAK,IAAI,CAAC,IAAG,KAAM,KAAK,CAAA;QAC7C,CAAC;QAED;;;WAGE;QACF,KAAK;YACH,OAAO,WAAY,IAAI,CAAC,SAAU,iBAAkB,IAAI,CAAC,QAAS,GAAG,CAAA;QACvE,CAAC;QAED,SAAS;YACP,OAAO,IAAI,CAAC,KAAK,CAAA;QACnB,CAAA;KACD;IAED,KAAK,EAAE;QACL,MAAM,EAAE;YACN,IAAI,EAAE,IAAI;YACV,OAAO;gBACL,IAAI,CAAC,WAAW,EAAE,CAAA;YACpB,CAAA;SACF;KACD;IAED,OAAO;QACL,IAAI,CAAC,WAAU,GAAI,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAA;IACjD,CAAC;IAED,OAAO;QACJ,IAAI,CAAC,KAAK,CAAC,EAAkB,CAAC,KAAK,CAAC,MAAK,GAAI,GAAI,IAAI,CAAC,SAAU,IAAI,CAAA;QACrE,IAAI,CAAC,SAAS,CAAC,GAAG,EAAC;YACjB,IAAI,CAAC,QAAQ,EAAE,CAAA;QACjB,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,OAAO,EAAE;QACP;;UAEC;QACD,OAAO,CAAC,KAAY;;YAClB,MAAM,GAAE,GAAI,MAAC,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAA2B,0CAAE,KAAK,CAAA;YAEtD,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,GAAG,CAAC,CAAA;YAC/B,IAAI,CAAC,WAAW,EAAE,CAAA;QACpB,CAAC;QAED;;WAEE;QACF,KAAK;;YACH,CAAC,MAAA,IAAI,CAAC,KAAK,0CAAE,EAAkB,CAAA,CAAC,KAAK,EAAE,CAAA;QACzC,CAAC;QAED;;;WAGE;QACF,QAAQ;YACN,MAAM,EAAC,GAAI,IAAI,CAAC,KAAK,CAAC,EAAiB,CAAA;YAEvC,IAAI,CAAC,EAAE,EAAE,CAAA;gBACP,OAAM;YACR,CAAA;YAEA,EAAE,CAAC,KAAK,CAAC,MAAK,GAAI,KAAK,CAAA;YAEvB,MAAM,MAAK,GAAI,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,EAAE,EAAE,CAAA,IAAK,CAAA,GAAI,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,EAAE,EAAE,CAAA,IAAK,CAAC,CAAA;YACzK,MAAM,GAAE,GAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,YAAW,GAAI,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAA;YAExF,EAAE,CAAC,KAAK,CAAC,SAAQ,GAAI,EAAE,CAAC,YAAW,GAAI,GAAE,CAAE,CAAA,CAAE,MAAK,CAAE,CAAA,CAAE,QAAQ,CAAA;YAC9D,EAAE,CAAC,KAAK,CAAC,MAAK,GAAI,GAAI,GAAI,IAAI,CAAA;YAE9B,IAAI,CAAC,SAAQ,GAAI,GAAG,CAAA;QACtB,CAAA;KACF;CACD,CAAC,CAAA", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/TextArea/TextAreaAutoGrow.vue.tsx", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent, inject, PropType } from 'vue';\nimport { debounce } from 'lodash';\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\n\ninterface NonReactiveProps {\n  queueResize(): void;\n}\n\nconst provideProps: NonReactiveProps = {\n  queueResize() {\n    // noop\n  }\n};\n\nexport default defineComponent({\n  inheritAttrs: false,\n\n  props: {\n    value: {\n      type:     String,\n      required: true\n    },\n\n    class: {\n      type:    [String, Array, Object] as PropType<string | unknown[] | Record<string, boolean>>,\n      default: ''\n    },\n\n    /**\n     * Sets the edit mode for Text Area.\n     * @values _EDIT, _VIEW\n     */\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n\n    /**\n     * Sets the Minimum height for Text Area. Prevents the height from becoming\n     * smaller than the value specified in minHeight.\n     */\n    minHeight: {\n      type:    Number,\n      default: 25\n    },\n\n    /**\n     * Sets the maximum height for Text Area. Prevents the height from becoming\n     * larger than the value specified in maxHeight.\n     */\n    maxHeight: {\n      type:    Number,\n      default: 200\n    },\n\n    /**\n     * Text that appears in the Text Area when it has no value set.\n     */\n    placeholder: {\n      type:    String,\n      default: ''\n    },\n\n    /**\n     * Specifies whether Text Area is subject to spell checking by the\n     * underlying browser/OS.\n     */\n    spellcheck: {\n      type:    Boolean,\n      default: true\n    },\n\n    /**\n     * Disables the Text Area.\n     */\n    disabled: {\n      type:    Boolean,\n      default: false\n    }\n  },\n\n  emits: ['update:value', 'paste', 'focus', 'blur'],\n\n  setup() {\n    const queueResize = inject('queueResize', provideProps.queueResize);\n\n    return { queueResize };\n  },\n\n  data() {\n    return {\n      curHeight: this.minHeight,\n      overflow:  'hidden'\n    };\n  },\n\n  computed: {\n    /**\n     * Determines if the Text Area should be disabled.\n     */\n    isDisabled(): boolean {\n      return this.disabled || this.mode === _VIEW;\n    },\n\n    /**\n     * Sets the height to one-line for SSR pageload so that it's already right\n     * (unless the input is long)\n     */\n    style(): string {\n      return `height: ${ this.curHeight }px; overflow: ${ this.overflow };`;\n    },\n\n    className(): string | unknown[] | Record<string, boolean> {\n      return this.class;\n    }\n  },\n\n  watch: {\n    $attrs: {\n      deep: true,\n      handler() {\n        this.queueResize();\n      }\n    }\n  },\n\n  created() {\n    this.queueResize = debounce(this.autoSize, 100);\n  },\n\n  mounted() {\n    (this.$refs.ta as HTMLElement).style.height = `${ this.curHeight }px`;\n    this.$nextTick(() => {\n      this.autoSize();\n    });\n  },\n\n  methods: {\n    /**\n     * Emits the input event and resizes the Text Area.\n    */\n    onInput(event: Event): void {\n      const val = (event?.target as HTMLInputElement)?.value;\n\n      this.$emit('update:value', val);\n      this.queueResize();\n    },\n\n    /**\n     * Gives focus to the Text Area.\n     */\n    focus(): void {\n      (this.$refs?.ta as HTMLElement).focus();\n    },\n\n    /**\n     * Sets the overflowY and height of the Text Area based on the content\n     * entered (calculated via scroll height).\n     */\n    autoSize(): void {\n      const el = this.$refs.ta as HTMLElement;\n\n      if (!el) {\n        return;\n      }\n\n      el.style.height = '1px';\n\n      const border = parseInt(getComputedStyle(el).getPropertyValue('borderTopWidth'), 10) || 0 + parseInt(getComputedStyle(el).getPropertyValue('borderBottomWidth'), 10) || 0;\n      const neu = Math.max(this.minHeight, Math.min(el.scrollHeight + border, this.maxHeight));\n\n      el.style.overflowY = el.scrollHeight > neu ? 'auto' : 'hidden';\n      el.style.height = `${ neu }px`;\n\n      this.curHeight = neu;\n    }\n  }\n});\n</script>\n\n<template>\n  <textarea\n    ref=\"ta\"\n    :value=\"value\"\n    :data-testid=\"$attrs['data-testid'] ? $attrs['data-testid'] : 'text-area-auto-grow'\"\n    :disabled=\"isDisabled\"\n    :style=\"style\"\n    :placeholder=\"placeholder\"\n    :class=\"className\"\n    class=\"no-resize no-ease\"\n    v-bind=\"$attrs\"\n    :spellcheck=\"spellcheck\"\n    @paste=\"$emit('paste', $event)\"\n    @input=\"onInput($event)\"\n    @focus=\"$emit('focus', $event)\"\n    @blur=\"$emit('blur', $event)\"\n  />\n</template>\n"]}]}