{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Config.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Config.vue", "mtime": 1754992537319}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Config.vue"], "names": [], "mappings": "AACA,OAAO,EAAE,eAAe,EAAW,MAAO,KAAK,CAAA;AAE/C,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,OAAM,EAAE,MAAO,4BAA4B,CAAA;AAClE,OAAO,MAAK,MAAO,QAAQ,CAAA;AAC3B,OAAO,EAAS,UAAS,EAAE,MAAO,MAAM,CAAA;AACxC,OAAO,EAAE,QAAO,EAAE,MAAO,sBAAsB,CAAA;AAC/C,OAAO,EAAE,MAAK,EAAE,MAAO,mBAAmB,CAAA;AAE1C,OAAO,EAAE,UAAS,EAAE,MAAO,qBAAqB,CAAA;AAChD,OAAO,EAAE,OAAM,EAAE,MAAO,wBAAwB,CAAA;AAChD,OAAO,UAAS,MAAO,uCAAuC,CAAA;AAC9D,OAAO,MAAK,MAAO,+BAA+B,CAAA;AAElD,OAAO,aAAY,MAAO,0CAA0C,CAAA;AACpE,OAAO,QAAO,MAAO,qCAAqC,CAAA;AAC1D,OAAO,QAAO,MAAO,wCAAwC,CAAA;AAC7D,OAAO,YAAW,MAAO,gDAAgD,CAAA;AACzE,OAAO,WAAU,MAAO,gCAAgC,CAAA;AAExD,eAAe,eAAe,CAAC;IAC7B,IAAI,EAAE,WAAW;IAEjB,KAAK,EAAE,CAAC,eAAe,EAAE,oBAAoB,EAAE,0BAA0B,EAAE,4BAA4B,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,0BAA0B,EAAE,eAAe,EAAE,aAAa,CAAC;IAEjN,UAAU,EAAE;QACV,aAAa;QACb,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,YAAY;QACZ,MAAK;KACN;IAED,KAAK,EAAE;QACL,IAAI,EAAE;YACJ,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,KAAI;SACd;QAED,oBAAoB,EAAE;YACpB,IAAI,EAAK,OAAO;YAChB,OAAO,EAAE,IAAG;SACb;QAED,UAAU,EAAE;YACV,IAAI,EAAK,OAAO;YAChB,OAAO,EAAE,KAAI;SACd;QAED,QAAQ,EAAE;YACR,IAAI,EAAK,KAAK;YACd,OAAO,EAAE,GAAG,EAAC,CAAE,EAAC;SACjB;QAED,IAAI,EAAE;YACJ,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,GAAG,EAAC,GAAG,CAAA;SACjB;QAED,MAAM,EAAE;YACN,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,EAAC;SACX;QAED,iBAAiB,EAAE;YACjB,IAAI,EAAK,OAAO;YAChB,OAAO,EAAE,KAAI;SACd;QAED,WAAW,EAAE;YACX,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,EAAC;SACX;QAED,iBAAiB,EAAE;YACjB,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,EAAC;SACX;QAED,mBAAmB,EAAE;YACnB,IAAI,EAAK,OAAO;YAChB,OAAO,EAAE,KAAI;SACd;QAED,YAAY,EAAE;YACZ,IAAI,EAAK,OAAO;YAChB,OAAO,EAAE,KAAI;SACd;QAED,MAAM,EAAE;YACN,IAAI,EAAM,MAA6B;YACvC,QAAQ,EAAE,IAAG;SACd;QAED,eAAe,EAAE;YACf,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,EAAC;SACZ;KACD;IAED,IAAI;;QACF,MAAM,KAAI,GAAI,IAAI,CAAC,MAAoB,CAAA;QACvC,qHAAoH;QACpH,MAAM,qBAAoB,GAAI,MAAA,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,yBAAyB,CAAC,0CAAE,KAAK,CAAA;QAC5H,MAAM,CAAA,GAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;QAEjC,OAAO;YACL,OAAO,EAAgB,EAAkB;YACzC,UAAU,EAAa,KAAK;YAC5B,qBAAqB;YACrB,iBAAiB,EAAM,CAAC,CAAC,IAAI,CAAC,WAAU,IAAK,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM;YACtE,eAAe,EAAQ,KAAK;YAC5B,UAAU,EAAa,KAAK;YAC5B,qBAAqB,EAAE,WAAuB;YAC9C,kBAAkB,EAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,kCAAkC,CAAA,EAAG,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,gCAAgC,CAAA,EAAG,CAAC;SAErJ,CAAA;IACH,CAAC;IAED,KAAK,EAAE;QACL,eAAe,EAAE;YACf,OAAO;gBACL,IAAI,IAAI,CAAC,IAAG,KAAM,KAAK,EAAE,CAAA;oBACvB,IAAI,CAAC,uBAAuB,EAAE,CAAA;oBAC9B,IAAI,CAAC,YAAY,EAAE,CAAA;gBACrB,CAAA;YACF,CAAC;YACD,SAAS,EAAE,IAAG;SACf;QAED,+BAA+B,EAAE;YAC/B,OAAO;gBACL,IAAI,IAAI,CAAC,IAAG,KAAM,KAAK,EAAE,CAAA;oBACvB,IAAI,CAAC,uBAAuB,EAAE,CAAA;oBAC9B,IAAI,CAAC,YAAY,EAAE,CAAA;gBACrB,CAAA;YACF,CAAC;YACD,SAAS,EAAE,IAAG;SACf;QAED,mBAAmB,CAAC,GAAG;YACrB,IAAI,CAAC,GAAG,EAAE,CAAA;gBACR,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,EAAE,CAAC,CAAA;YACjC,CAAA;QACF,CAAC;QAED,mBAAmB,CAAC,GAAG;YACrB,IAAI,CAAC,GAAG,EAAE,CAAA;gBACR,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAA;YACtC,CAAA;QACF,CAAC;QAED,cAAc,EAAE;YACd,OAAO,CAAC,GAAG;gBACT,IAAI,GAAE,IAAK,GAAG,CAAC,MAAK,IAAK,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAA;oBAChD,IAAI,CAAC,KAAK,CAAC,0BAA0B,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;gBACtD,CAAA;YACF,CAAC;YACD,SAAS,EAAE,IAAG;SACf;KAEF;IAED,QAAQ,EAAE;QACR,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE,QAAO,EAAG,CAAC;QAE9B,6FAA4F;QAC5F,yGAAwG;QACxG,UAAU;;YACR,IAAI,IAAI,CAAC,IAAG,KAAM,OAAO,EAAE,CAAA;gBACzB,OAAO,IAAI,CAAA;YACb,CAAA;YACA,MAAM,UAAS,GAAI,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,UAAS,KAAK,EAAE,CAAA;YAEhD,MAAM,YAAW,GAAI,UAAU,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAC,CAAE,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA,IAAK,KAAK,CAAC,YAAY,CAAC,CAAA;YAErJ,OAAO,CAAC,YAAY,CAAC,MAAM,CAAA;QAC7B,CAAC;QAED,oBAAoB;YAClB,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAC,CAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,MAAK,GAAI,CAAC,CAAA;QACtE,CAAC;QAED,cAAc;YACZ,OAAO,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAS,EAAE,EAAC;gBAC9D,MAAM,OAAM,GAAI,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;gBAEhC,IAAI,IAAI,CAAC,qBAAoB,IAAK,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAA;oBACxF,OAAO,QAAQ,CAAA;gBACjB,CAAA;gBACA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAA;oBACzB,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAA,EAAG,CAAC,CAAA;gBACvC,CAAA;qBAAO,IAAI,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,OAAO,CAAC,EAAE,CAAA;oBACnE,MAAM,cAAa,GAAI,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,OAAO,CAAC,CAAA;oBAE/E,IAAI,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,cAAc,CAAC,EAAE,CAAA;wBACtC,QAAQ,CAAC,IAAI,CAAC;4BACZ,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,GAAI,CAAE,IAAK,IAAI,CAAC,CAAC,CAAC,4BAA4B,CAAE,EAAE,EAAE,QAAQ,EAAE,IAAG;yBACnF,CAAC,CAAA;oBACJ,CAAA;yBAAO,CAAA;wBACL,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAA,EAAG,CAAC,CAAA;oBACvC,CAAA;gBACF,CAAA;gBAEA,uCAAsC;gBACtC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAC;oBACpB,CAAC,CAAC,IAAG,GAAI,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;gBAC5B,CAAC,CAAC,CAAA;gBAEF,OAAO,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;YACvC,CAAC,EAAE,EAAwE,CAAC,CAAA;QAC9E,CAAC;QAED,UAAU;YACR,OAAO,CAAC,IAAI,CAAC,OAAM,IAAK,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAC,CAAE,CAAC,CAAC,MAAM,CAAC,CAAA;QAClD,CAAA;KACD;IAED,OAAO,EAAE;QACP,4CAA2C;QAC3C,uDAAsD;QACtD,4EAA2E;QAC3E,KAAI,CAAE,uBAAuB;YAC3B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAK,IAAK,CAAC,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAA;gBAC9D,OAAM;YACR,CAAA;YACA,IAAI,CAAC,eAAc,GAAI,IAAI,CAAA;YAC3B,IAAI,CAAA;gBACF,MAAM,SAAQ,GAAI,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,sBAAqB,EAAG,CAAC,CAAA;gBAC9I,MAAM,MAAK,GAAI,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,oBAAoB,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,uBAAsB,EAAG,CAAC,CAAA;gBAEpH,IAAI,CAAC,MAAM,EAAE,CAAA;oBACX,OAAM;gBACR,CAAA;gBACA,IAAI,CAAC,qBAAoB,GAAI,MAAM,CAAC,MAAM,CAAC,CAAC,QAAkB,EAAE,KAAmB,EAAE,EAAC;oBACpF,CAAC,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,aAAY,KAAK,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,EAAC;wBACnD,CAAC,CAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,eAAc,KAAK,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAC;4BACjD,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,EAAE,CAAA;gCACxC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,CAAA;4BACjC,CAAA;wBACF,CAAC,CAAC,CAAA;oBACJ,CAAC,CAAC,CAAA;oBAEF,OAAO,QAAQ,CAAA;gBACjB,CAAC,EAAE,EAAE,CAAC,CAAA;YACR,CAAA;YAAE,OAAO,GAAG,EAAE,CAAA;gBACZ,2HAA0H;YAC5H,CAAA;YAEA,IAAI,CAAC,eAAc,GAAI,KAAK,CAAA;QAC9B,CAAC;QAED,KAAI,CAAE,YAAY;YAChB,MAAM,EAAE,MAAM,EAAE,sBAAqB,EAAE,GAAI,IAAI,CAAC,MAAM,CAAA;YAEtD,IAAI,CAAC,MAAK,IAAK,CAAC,sBAAsB,EAAE,CAAA;gBACtC,OAAM;YACR,CAAA;YACA,IAAI,CAAC,UAAS,GAAI,IAAI,CAAA;YACtB,MAAM,KAAI,GAAI,IAAI,CAAC,MAAoB,CAAA;YACvC,MAAM,SAAQ,GAAI,MAAM,KAAK,CAAC,QAAQ,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,iBAAiB,EAAE,sBAAqB,EAAG,CAAC,CAAA;YAExG,IAAI,CAAA;gBACF,IAAI,CAAC,OAAM,GAAI,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,oBAAoB,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,UAAS,EAAG,CAAC,CAAA;gBAEvG,IAAI,CAAC,UAAS,GAAI,IAAI,CAAA;YACxB,CAAA;YAAE,OAAO,CAAC,EAAE,CAAA;gBACV,IAAI,CAAC,UAAS,GAAI,KAAK,CAAA;YACzB,CAAA;YACA,IAAI,CAAC,UAAS,GAAI,KAAK,CAAA;QACzB,CAAC;KAEH;CACD,CAAC,CAAA", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Config.vue.tsx", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent, PropType } from 'vue';\nimport { EKSConfig, AWS } from '@/types';\nimport { _EDIT, _VIEW, _CREATE } from '@shell/config/query-params';\nimport semver from 'semver';\nimport { Store, mapGetters } from 'vuex';\nimport { sortable } from '@shell/utils/version';\nimport { sortBy } from '@shell/utils/sort';\n\nimport { MANAGEMENT } from '@shell/config/types';\nimport { SETTING } from '@shell/config/settings';\nimport RadioGroup from '@components/Form/Radio/RadioGroup.vue';\nimport Banner from '@components/Banner/Banner.vue';\n\nimport LabeledSelect from '@shell/components/form/LabeledSelect.vue';\nimport KeyValue from '@shell/components/form/KeyValue.vue';\nimport Checkbox from '@components/Form/Checkbox/Checkbox.vue';\nimport LabeledInput from '@components/Form/LabeledInput/LabeledInput.vue';\nimport eksVersions from '../../assets/data/eks-versions';\n\nexport default defineComponent({\n  name: 'EKSConfig',\n\n  emits: ['update:kmsKey', 'update:serviceRole', 'update:kubernetesVersion', 'update:enableNetworkPolicy', 'update:ebsCSIDriver', 'update:serviceRole', 'update:secretsEncryption', 'update:kmsKey', 'update:tags'],\n\n  components: {\n    LabeledSelect,\n    RadioGroup,\n    KeyValue,\n    Checkbox,\n    LabeledInput,\n    Banner\n  },\n\n  props: {\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n\n    isNewOrUnprovisioned: {\n      type:    Boolean,\n      default: true\n    },\n\n    loadingIam: {\n      type:    Boolean,\n      default: false\n    },\n\n    eksRoles: {\n      type:    Array,\n      default: () => []\n    },\n\n    tags: {\n      type:    Object,\n      default: () => {}\n    },\n\n    kmsKey: {\n      type:    String,\n      default: ''\n    },\n\n    secretsEncryption: {\n      type:    Boolean,\n      default: false\n    },\n\n    serviceRole: {\n      type:    String,\n      default: ''\n    },\n\n    kubernetesVersion: {\n      type:    String,\n      default: ''\n    },\n\n    enableNetworkPolicy: {\n      type:    Boolean,\n      default: false\n    },\n\n    ebsCSIDriver: {\n      type:    Boolean,\n      default: false\n    },\n\n    config: {\n      type:     Object as PropType<EKSConfig>,\n      required: true\n    },\n\n    originalVersion: {\n      type:    String,\n      default: ''\n    }\n  },\n\n  data() {\n    const store = this.$store as Store<any>;\n    // This setting is used by RKE1 AKS GKE and EKS - rke2/k3s have a different mechanism for fetching supported versions\n    const supportedVersionRange = store.getters['management/byId'](MANAGEMENT.SETTING, SETTING.UI_SUPPORTED_K8S_VERSIONS)?.value;\n    const t = store.getters['i18n/t'];\n\n    return {\n      kmsKeys:               [] as AWS.KmsKey[],\n      canReadKms:            false,\n      supportedVersionRange,\n      customServiceRole:     !!this.serviceRole && !!this.serviceRole.length,\n      loadingVersions:       false,\n      loadingKms:            false,\n      allKubernetesVersions: eksVersions as string[],\n      serviceRoleOptions:    [{ value: false, label: t('eks.serviceRole.options.standard') }, { value: true, label: t('eks.serviceRole.options.custom') }],\n\n    };\n  },\n\n  watch: {\n    'config.region': {\n      handler() {\n        if (this.mode !== _VIEW) {\n          this.fetchKubernetesVersions();\n          this.fetchKMSKeys();\n        }\n      },\n      immediate: true\n    },\n\n    'config.amazonCredentialSecret': {\n      handler() {\n        if (this.mode !== _VIEW) {\n          this.fetchKubernetesVersions();\n          this.fetchKMSKeys();\n        }\n      },\n      immediate: true\n    },\n\n    'secretsEncryption'(neu) {\n      if (!neu) {\n        this.$emit('update:kmsKey', '');\n      }\n    },\n\n    'customServiceRole'(neu) {\n      if (!neu) {\n        this.$emit('update:serviceRole', '');\n      }\n    },\n\n    versionOptions: {\n      handler(neu) {\n        if (neu && neu.length && !this.kubernetesVersion) {\n          this.$emit('update:kubernetesVersion', neu[0].value);\n        }\n      },\n      immediate: true\n    },\n\n  },\n\n  computed: {\n    ...mapGetters({ t: 'i18n/t' }),\n\n    // the control plane k8s version can't be more than one minor version ahead of any node pools\n    // verify that all nodepools are on the same version as the control plane before showing upgrade optiopns\n    canUpgrade(): boolean {\n      if (this.mode === _CREATE) {\n        return true;\n      }\n      const nodeGroups = this.config?.nodeGroups || [];\n\n      const needsUpgrade = nodeGroups.filter((group) => semver.gt(semver.coerce(this.originalVersion), semver.coerce(group.version)) || group._isUpgrading);\n\n      return !needsUpgrade.length;\n    },\n\n    hasUpgradesAvailable() {\n      return this.versionOptions.filter((opt) => !opt.disabled).length > 1;\n    },\n\n    versionOptions(): {value: string, label: string, sort?: string, disabled?:boolean}[] {\n      return this.allKubernetesVersions.reduce((versions, v: string) => {\n        const coerced = semver.coerce(v);\n\n        if (this.supportedVersionRange && !semver.satisfies(coerced, this.supportedVersionRange)) {\n          return versions;\n        }\n        if (!this.originalVersion) {\n          versions.push({ value: v, label: v });\n        } else if (semver.lte(semver.coerce(this.originalVersion), coerced)) {\n          const withinOneMinor = semver.inc(semver.coerce(this.originalVersion), 'minor');\n\n          if (semver.gt(coerced, withinOneMinor)) {\n            versions.push({\n              value: v, label: `${ v } ${ this.t('eks.version.upgradeWarning') }`, disabled: true\n            });\n          } else {\n            versions.push({ value: v, label: v });\n          }\n        }\n\n        // Generate sort field for each version\n        versions.forEach((v) => {\n          v.sort = sortable(v.value);\n        });\n\n        return sortBy(versions, 'sort', true);\n      }, [] as {value: string, label: string, sort?: string, disabled?:boolean}[]);\n    },\n\n    kmsOptions(): string[] {\n      return (this.kmsKeys || []).map((k) => k.KeyArn);\n    }\n  },\n\n  methods: {\n    // there is no api for fetching eks versions\n    // fetch addons and look at which versions they support\n    // this assumes that all k8s versions are compatible with at least one addon\n    async fetchKubernetesVersions() {\n      if (!this.config.region || !this.config.amazonCredentialSecret) {\n        return;\n      }\n      this.loadingVersions = true;\n      try {\n        const eksClient = await this.$store.dispatch('aws/eks', { region: this.config.region, cloudCredentialId: this.config.amazonCredentialSecret });\n        const addons = await this.$store.dispatch('aws/depaginateList', { client: eksClient, cmd: 'describeAddonVersions' });\n\n        if (!addons) {\n          return;\n        }\n        this.allKubernetesVersions = addons.reduce((versions: string[], addon: AWS.EKSAddon) => {\n          (addon?.addonVersions || []).forEach((addonVersion) => {\n            (addonVersion?.compatibilities || []).forEach((c) => {\n              if (!versions.includes(c.clusterVersion)) {\n                versions.push(c.clusterVersion);\n              }\n            });\n          });\n\n          return versions;\n        }, []);\n      } catch (err) {\n        // if the user doesn't have permission to describe addon versions swallow the error and use a fallback list of eks versions\n      }\n\n      this.loadingVersions = false;\n    },\n\n    async fetchKMSKeys() {\n      const { region, amazonCredentialSecret } = this.config;\n\n      if (!region || !amazonCredentialSecret) {\n        return;\n      }\n      this.loadingKms = true;\n      const store = this.$store as Store<any>;\n      const kmsClient = await store.dispatch('aws/kms', { region, cloudCredentialId: amazonCredentialSecret });\n\n      try {\n        this.kmsKeys = await this.$store.dispatch('aws/depaginateList', { client: kmsClient, cmd: 'listKeys' });\n\n        this.canReadKms = true;\n      } catch (e) {\n        this.canReadKms = false;\n      }\n      this.loadingKms = false;\n    },\n\n  }\n});\n\n</script>\n\n<template>\n  <div>\n    <Banner\n      v-if=\"!canUpgrade && hasUpgradesAvailable\"\n      color=\"info\"\n      label-key=\"eks.version.upgradeDisallowed\"\n      data-testid=\"eks-version-upgrade-disallowed-banner\"\n    />\n    <div\n      :style=\"{'display':'flex',\n               'align-items':'center'}\"\n      class=\"row mb-10\"\n    >\n      <div class=\"col span-6\">\n        <LabeledSelect\n          :value=\"kubernetesVersion\"\n          :options=\"versionOptions\"\n          label-key=\"eks.version.label\"\n          :mode=\"mode\"\n          :loading=\"loadingVersions\"\n          :taggable=\"true\"\n          :searchable=\"true\"\n          data-testid=\"eks-version-dropdown\"\n          :disabled=\"!canUpgrade && hasUpgradesAvailable\"\n          @update:value=\"$emit('update:kubernetesVersion', $event)\"\n        />\n      </div>\n      <div class=\"col span-3\">\n        <Checkbox\n          :mode=\"mode\"\n          label-key=\"eks.enableNetworkPolicy.label\"\n          :value=\"enableNetworkPolicy\"\n          :disabled=\"!isNewOrUnprovisioned\"\n          @update:value=\"$emit('update:enableNetworkPolicy', $event)\"\n        />\n      </div>\n      <div class=\"col span-3\">\n        <Checkbox\n          :mode=\"mode\"\n          label-key=\"eks.ebsCSIDriver.label\"\n          :value=\"ebsCSIDriver\"\n          :disabled=\"!isNewOrUnprovisioned\"\n          @update:value=\"$emit('update:ebsCSIDriver', $event)\"\n        />\n      </div>\n    </div>\n    <div class=\"row mb-10\">\n      <div class=\"col span-6\">\n        <RadioGroup\n          v-model:value=\"customServiceRole\"\n          :mode=\"mode\"\n          :options=\"serviceRoleOptions\"\n          name=\"serviceRoleMode\"\n          data-testid=\"eks-service-role-radio\"\n          :disabled=\"mode!=='create'\"\n        />\n      </div>\n      <div class=\"col span-6\">\n        <LabeledSelect\n          v-if=\"customServiceRole\"\n          :value=\"serviceRole\"\n          :mode=\"mode\"\n          :disabled=\"mode!=='create'\"\n          :options=\"eksRoles\"\n          option-label=\"RoleName\"\n          option-key=\"RoleId\"\n          label-key=\"eks.serviceRole.label\"\n          :loading=\"loadingIam\"\n          data-testid=\"eks-service-role-dropdown\"\n          @update:value=\"$emit('update:serviceRole', $event.RoleName)\"\n        />\n      </div>\n    </div>\n\n    <div class=\"row mb-10\">\n      <div class=\"col span-6\">\n        <Checkbox\n          :value=\"secretsEncryption\"\n          :disabled=\"mode!=='create'\"\n          :mode=\"mode\"\n          label-key=\"eks.encryptSecrets.label\"\n          data-testid=\"eks-secrets-encryption-checkbox\"\n          @update:value=\"$emit('update:secretsEncryption', $event)\"\n        />\n      </div>\n    </div>\n    <div\n      v-if=\"secretsEncryption\"\n      class=\"row mb-10\"\n    >\n      <div\n        class=\"col span-6\"\n      >\n        <LabeledSelect\n          v-if=\"canReadKms\"\n          :value=\"kmsKey\"\n          :mode=\"mode\"\n          :options=\"kmsOptions\"\n          :loading=\"loadingKms\"\n          :label=\"t('cluster.machineConfig.amazonEc2.kmsKey.label')\"\n          data-testid=\"eks-kms-dropdown\"\n          :disabled=\"mode!=='create'\"\n          @update:value=\"$emit('update:kmsKey', $event)\"\n        />\n        <template v-else>\n          <LabeledInput\n            :value=\"kmsKey\"\n            :mode=\"mode\"\n            :label=\"t('cluster.machineConfig.amazonEc2.kmsKey.label')\"\n            :tooltip=\"t('cluster.machineConfig.amazonEc2.kmsKey.text')\"\n            data-testid=\"eks-kms-input\"\n            :disabled=\"mode!=='create'\"\n            @update:value=\"$emit('update:kmsKey', $event)\"\n          />\n        </template>\n      </div>\n    </div>\n\n    <div class=\"col span-6 mt-20\">\n      <KeyValue\n        :value=\"tags\"\n        :mode=\"mode\"\n        :as-map=\"true\"\n        :read-allowed=\"false\"\n        @update:value=\"$emit('update:tags', $event)\"\n      >\n        <template #title>\n          <h3 v-t=\"'eks.tags.label'\" />\n        </template>\n      </KeyValue>\n    </div>\n  </div>\n</template>\n"]}]}