{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/AccountAccess.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/AccountAccess.vue", "mtime": *************}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": *************}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": *************}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": *************}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": *************}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": *************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/AccountAccess.vue"], "names": [], "mappings": "AACA,OAAO,EAAE,eAAc,EAAE,MAAO,KAAK,CAAA;AACrC,OAAO,EAAE,OAAO,EAAE,KAAI,EAAE,MAAO,4BAA4B,CAAA;AAC3D,OAAO,aAAY,MAAO,0CAA0C,CAAA;AACpE,OAAO,gBAAe,MAAO,iEAAiE,CAAA;AAC9F,OAAO,EAAE,cAAa,EAAE,MAAO,cAAc,CAAA;AAC7C,OAAO,EAAE,UAAS,EAAE,MAAO,MAAM,CAAA;AAEjC,OAAO,EAAE,MAAK,EAAE,MAAO,qBAAqB,CAAA;AAG5C,eAAe,eAAe,CAAC;IAC7B,IAAI,EAAE,kBAAkB;IAExB,KAAK,EAAE,CAAC,eAAe,EAAE,OAAO,EAAE,mBAAmB,EAAE,mBAAmB,CAAC;IAE3E,UAAU,EAAE;QACV,aAAa;QACb,gBAAe;KAChB;IAED,KAAK,EAAE;QACL,IAAI,EAAE;YACJ,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,OAAM;SAChB;QAED,UAAU,EAAE;YACV,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,IAAG;SACb;QAED,MAAM,EAAE;YACN,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,EAAC;SACZ;KACD;IAED,KAAI,CAAE,KAAK;QACT,IAAI,IAAI,CAAC,IAAG,KAAM,KAAK,EAAE,CAAA;YACvB,IAAI,CAAC,cAAa,GAAI,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAA;YACtE,IAAI,IAAI,CAAC,cAAc,CAAC,MAAK,IAAK,CAAC,IAAI,CAAC,MAAM,EAAE,CAAA;gBAC9C,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,cAAc,CAAC,CAAA;YAC7C,CAAA;QACF,CAAA;IACF,CAAC;IAED,IAAI;QACF,OAAO,EAAE,OAAO,EAAE,EAAc,EAAE,cAAc,EAAE,EAAa,EAAG,CAAA;IACpE,CAAC;IAED,KAAK,EAAE;QACL,eAAe,EAAE;YACf,KAAI,CAAE,OAAO,CAAC,GAAG;gBACf,IAAI,GAAE,IAAK,IAAI,CAAC,IAAG,KAAM,KAAK,EAAE,CAAA;oBAC9B,sFAAqF;oBACrF,MAAM,uBAAsB,GAAI,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAA;oBACvE,IAAI,uBAAuB,EAAE,CAAA;wBAC3B,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,uBAAuB,CAAC,CAAA;oBACtD,CAAA;oBACA,MAAM,IAAI,CAAC,YAAY,EAAE,CAAA;gBAC3B,CAAA;qBAAO,CAAA;oBACL,IAAI,IAAI,CAAC,cAAc,CAAC,MAAK,IAAK,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAA;wBAC5E,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAA;4BAChD,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,cAAc,CAAC,CAAA;wBAC7C,CAAA;6BAAO,CAAA;4BACL,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAA;wBACrD,CAAA;oBACF,CAAA;gBACF,CAAA;YACF,CAAC;YACD,SAAS,EAAE,IAAG;SAChB;KACD;IAED,OAAO,EAAE;QACP,KAAI,CAAE,YAAY;YAChB,MAAM,EAAE,MAAM,EAAE,UAAS,EAAG,GAA0C,IAAI,CAAA;YAE1E,IAAI,CAAC,CAAC,MAAK,IAAK,CAAC,CAAC,UAAU,EAAE,CAAA;gBAC5B,IAAI,CAAA;oBACF,MAAM,SAAQ,GAAI,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,iBAAiB,EAAE,UAAS,EAAG,CAAC,CAAA;oBAElG,MAAM,GAAG,GAA+B,MAAM,SAAS,CAAC,eAAe,CAAC,EAAE,CAAC,CAAA;oBAE3E,IAAI,CAAC,OAAM,GAAI,CAAC,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,OAAM,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAC,CAAE,CAAC,CAAC,UAAU,CAAC,CAAA;gBAC9D,CAAA;gBAAE,OAAO,GAAG,EAAE,CAAA;oBACZ,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,4BAA4B,EAAE,EAAE,GAAE,EAAG,CAAC,CAAC,CAAA;gBACpE,CAAA;YACF,CAAA;QACF,CAAC;QAED,KAAI,CAAE,0BAA0B;YAC9B,IAAI,CAAC,IAAI,CAAC,UAAU;gBAAE,OAAO,IAAI,CAAA;YAC3B,yEAAwE;YAC9E,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,cAAc,EAAE;gBAC1C,IAAI,EAAE,MAAM,CAAC,gBAAgB;gBAC7B,EAAE,EAAE,IAAI,CAAC,UAAS;aACnB,CAAC,CAAC,IAAI,CAAC,kBAAiB,CAAE,EAAC;;gBAC1B,OAAO,CAAA,MAAA,kBAAkB,aAAlB,kBAAkB,uBAAlB,kBAAkB,CAAE,WAAW,0CAAE,aAAY,KAAK,IAAI,CAAA;YAC/D,CAAC,CAAC,CAAC,KAAK,CAAC,GAAE,CAAE,EAAC;gBACZ,OAAO,CAAC,IAAI,CAAC,4CAA4C,EAAE,GAAG,CAAC,CAAA;gBAC/D,OAAO,IAAI,CAAA;YACb,CAAC,CAAC,CAAA;QACJ,CAAC;KACF;IAED,QAAQ,EAAE;QACR,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE,QAAO,EAAG,CAAC;QAE9B,4EAA2E;QAC3E,eAAe;YACb,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,CAAA;QAC1B,CAAC;QAED,aAAa;YACX,OAAO,IAAI,CAAC,OAAO,CAAC,MAAK,CAAE,CAAA,CAAE,IAAI,CAAC,OAAM,CAAE,CAAA,CAAE,IAAI,CAAC,cAAc,CAAA;QACjE,CAAC;QAED,MAAM;YACJ,OAAO,OAAO,CAAA;QAChB,CAAC;QAED,IAAI;YACF,OAAO,KAAK,CAAA;QACd,CAAC;KAEF;CACF,CAAC,CAAA", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/AccountAccess.vue.tsx", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent } from 'vue';\nimport { _CREATE, _VIEW } from '@shell/config/query-params';\nimport LabeledSelect from '@shell/components/form/LabeledSelect.vue';\nimport SelectCredential from '@shell/edit/provisioning.cattle.io.cluster/SelectCredential.vue';\nimport { DEFAULT_REGION } from './CruEKS.vue';\nimport { mapGetters } from 'vuex';\nimport { AWS } from '../../types';\nimport { NORMAN } from '@shell/config/types';\n\n\nexport default defineComponent({\n  name: 'EKSAccountAccess',\n\n  emits: ['update-region', 'error', 'cancel-credential', 'update-credential'],\n\n  components: {\n    LabeledSelect,\n    SelectCredential\n  },\n\n  props: {\n    mode: {\n      type:    String,\n      default: _CREATE\n    },\n\n    credential: {\n      type:    String,\n      default: null\n    },\n\n    region: {\n      type:    String,\n      default: ''\n    }\n  },\n\n  async fetch() {\n    if (this.mode !== _VIEW) {\n      this.defaultRegions = await this.$store.dispatch('aws/defaultRegions');\n      if (this.defaultRegions.length && !this.region) {\n        this.$emit('update-region', DEFAULT_REGION);\n      }\n    }\n  },\n\n  data() {\n    return { regions: [] as string[], defaultRegions: [] as string[] };\n  },\n\n  watch: {\n    isAuthenticated: {\n      async handler(neu) {\n        if (neu && this.mode !== _VIEW) {\n          // Auto-default to credential's region only once on initialization if no region is set\n          const credentialDefaultRegion = await this.getCredentialDefaultRegion();\n          if (credentialDefaultRegion) {\n            this.$emit('update-region', credentialDefaultRegion);\n          }\n          await this.fetchRegions();\n        } else {\n          if (this.defaultRegions.length && !this.defaultRegions.includes(this.region)) {\n            if (this.defaultRegions.includes(DEFAULT_REGION)) {\n              this.$emit('update-region', DEFAULT_REGION);\n            } else {\n              this.$emit('update-region', this.defaultRegions[0]);\n            }\n          }\n        }\n      },\n      immediate: true\n    }\n  },\n\n  methods: {\n    async fetchRegions() {\n      const { region, credential }: { region: string, credential: string} = this;\n\n      if (!!region && !!credential) {\n        try {\n          const ec2Client = await this.$store.dispatch('aws/ec2', { region, cloudCredentialId: credential });\n\n          const res: {Regions: AWS.EC2Region[]} = await ec2Client.describeRegions({});\n\n          this.regions = (res?.Regions || []).map((r) => r.RegionName);\n        } catch (err) {\n          this.$emit('error', this.t('eks.errors.fetchingRegions', { err }));\n        }\n      }\n    },\n    \n    async getCredentialDefaultRegion(): Promise<string|null> {\n      if (!this.credential) return null;\n            // Use the correct store and schema type consistent with rest of codebase\n      return this.$store.dispatch('rancher/find', {\n        type: NORMAN.CLOUD_CREDENTIAL,\n        id: this.credential\n      }).then(credentialResource => {\n        return credentialResource?.decodedData?.defaultRegion || null;\n      }).catch(err => {\n        console.warn('Failed to fetch credential default region:', err);\n        return null;\n      });\n    },\n  },\n\n  computed: {\n    ...mapGetters({ t: 'i18n/t' }),\n\n    // once the credential is validated we can fetch a list of available regions\n    isAuthenticated(): boolean {\n      return !!this.credential;\n    },\n\n    regionOptions(): string[] {\n      return this.regions.length ? this.regions : this.defaultRegions;\n    },\n\n    CREATE(): string {\n      return _CREATE;\n    },\n\n    VIEW(): string {\n      return _VIEW;\n    },\n\n  },\n});\n</script>\n\n<template>\n  <div\n    :class=\"{'showing-form': !credential}\"\n    class=\"credential-region\"\n  >\n    <div class=\"region mb-10\">\n      <LabeledSelect\n        :disabled=\"mode!=='create'\"\n        :value=\"region\"\n        data-testid=\"eks_region\"\n        label-key=\"eks.region.label\"\n        :options=\"regionOptions\"\n        @update:value=\"$emit('update-region', $event)\"\n      />\n    </div>\n    <div\n      class=\"select-credential-container\"\n    >\n      <SelectCredential\n        :value=\"credential\"\n        data-testid=\"crueks-select-credential\"\n        :mode=\"mode === VIEW ? VIEW : CREATE\"\n        provider=\"aws\"\n        :default-on-cancel=\"true\"\n        :showing-form=\"!credential\"\n        class=\"select-credential\"\n        :cancel=\"()=>$emit('cancel-credential')\"\n        @update:value=\"$emit('update-credential', $event)\"\n      />\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\">\n  .credential-region {\n    display: flex;\n\n    .region {\n      flex-basis: 50%;\n      flex-grow: 0;\n      margin: 0 1.75% 0 0;\n    }\n\n    &.showing-form {\n      flex-direction: column;\n      flex-grow: 1;\n\n      &>.region {\n        margin: 0;\n      }\n\n      &>.select-credential-container{\n      display:flex;\n      flex-direction: column;\n      flex-grow: 1;\n      }\n    }\n\n    &>.select-credential-container{\n      flex-basis: 50%;\n\n      &>.select-credential{\n        flex-grow: 1;\n      }\n\n    }\n  }\n\n</style>\n"]}]}