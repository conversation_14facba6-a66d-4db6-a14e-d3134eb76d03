{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/labeled-select-utils/labeled-select.utils.ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/labeled-select-utils/labeled-select.utils.ts", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/labeled-select-utils/labeled-select.utils.ts", "sourceRoot": "", "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/labeled-select-utils/labeled-select.utils.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,cAAc,EAAmC,MAAM,qCAAqC,CAAC;AAmCtG;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,6BAA6B,CAAI,EACrD,IAAI,EACJ,IAAI,EACJ,GAAG,EACH,OAAO,GAAG,EAAE,EACZ,IAAI,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,oBAAoB,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC,EAC1F,KAAK,GAAG,SAAS,EACjB,gBAAgB,GAAG,IAAI,EACvB,QAAQ,GAAG,KAAK,GACwB;IACxC,MAAM,EACJ,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EACvC,GAAG,IAAI,CAAC;IAET,IAAI,CAAC;QACH,+BAA+B;QAE/B,MAAM,UAAU,GAAG,IAAI,cAAc,CAAC;YACpC,IAAI;YACJ,QAAQ;YACR,IAAI;YACJ,OAAO;SACR,CAAC,CAAC;QACH,MAAM,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,GAAI,KAAM,SAAS,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC;QACzE,kGAAkG;QAClG,MAAM,GAAG,GAAG,MAAM,GAAG,CAAC,QAAQ,CAAC,GAAI,KAAM,UAAU,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;QAC9D,IAAI,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;QAEpB,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,GAAG,MAAM,GAAG,CAAC,QAAQ,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAE5D,yCAAyC;QACzC,IAAI,OAAc,CAAC;QAEnB,IAAI,gBAAgB,EAAE,CAAC;YACrB,4BAA4B;YAC5B,MAAM,UAAU,GAAyB,EAAE,CAAC;YAE5C,OAAO,CAAC,OAAO,CAAC,CAAC,MAAW,EAAE,EAAE;gBAC9B,MAAM,EAAE,GAAG,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;gBAErC,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC,CAAC,iEAAiE;oBAC9F,OAAO;gBACT,CAAC;gBACD,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC;oBACpB,UAAU,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;gBACtB,CAAC;gBACD,UAAU,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC;YAEH,OAAO,GAAG,EAAE,CAAC;YAEb,gEAAgE;YAChE,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;gBAC5C,OAAO,CAAC,IAAI,CAAC;oBACX,IAAI,EAAM,OAAO;oBACjB,IAAI,EAAM,gBAAgB;oBAC1B,EAAE,EAAQ,EAAE;oBACZ,QAAQ,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;oBACtB,QAAQ,EAAE,IAAI;iBACf,CAAC,CAAC;gBACH,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,OAAO,CAAC;QACpB,CAAC;QAED,OAAO;YACL,IAAI,EAAG,OAAO;YACd,KAAK,EAAE,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,QAAQ,IAAI,MAAM,CAAC,gBAAgB,CAAC,CAAC;YAChF,KAAK,EAAE,GAAG,CAAC,KAAK;SACjB,CAAC;IACJ,CAAC;IAAC,OAAO,GAAG,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,iCAAiC;IACvD,CAAC;IAED,OAAO;QACL,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC;KAC7B,CAAC;AACJ,CAAC", "sourcesContent": ["import { LabelSelectPaginateFnOptions, LabelSelectPaginateFnResponse } from '@shell/types/components/labeledSelect';\nimport { PaginationArgs, PaginationParam, PaginationSort } from '@shell/types/store/pagination.types';\n\nexport interface LabelSelectPaginationFunctionOptions<T = any> {\n  opts: LabelSelectPaginateFnOptions<T>,\n  /**\n   * Resource type\n   */\n  type: string,\n  /**\n   * Store things\n   */\n  ctx: { getters: any, dispatch: any}\n  /**\n   * Filters to apply. This mostly covers the text a user has entered, but could be other things like namespace\n   */\n  filters?: PaginationParam[],\n  /**\n   * How to sort the response\n   */\n  sort?: PaginationSort[],\n  /**\n   * Vuex store name\n   */\n  store?: string,\n  /**\n   * True if the options returned should be grouped by namespace\n   */\n  groupByNamespace?: boolean,\n\n  /**\n   * Convert the results from JSON object to Rancher model class instance\n   */\n  classify?: boolean,\n}\n\n/**\n * This is a helper function to cover common functionality that could happen when a LabelSelect requests a new page\n */\nexport async function labelSelectPaginationFunction<T>({\n  opts,\n  type,\n  ctx,\n  filters = [],\n  sort = [{ asc: true, field: 'metadata.namespace' }, { asc: true, field: 'metadata.name' }],\n  store = 'cluster',\n  groupByNamespace = true,\n  classify = false,\n}: LabelSelectPaginationFunctionOptions<T>): Promise<LabelSelectPaginateFnResponse<T>> {\n  const {\n    pageContent, page, pageSize, resetPage\n  } = opts;\n\n  try {\n    // Construct params for request\n\n    const pagination = new PaginationArgs({\n      page,\n      pageSize,\n      sort,\n      filters\n    });\n    const url = ctx.getters[`${ store }/urlFor`](type, null, { pagination });\n    // Make request (note we're not bothering to persist anything to the store, response is transient)\n    const res = await ctx.dispatch(`${ store }/request`, { url });\n    let data = res.data;\n\n    if (classify) {\n      data = await ctx.dispatch('cluster/createMany', data);\n    }\n\n    const options = resetPage ? data : pageContent.concat(data);\n\n    // Create the new option collection by...\n    let resPage: any[];\n\n    if (groupByNamespace) {\n      // ... grouping by namespace\n      const namespaced: { [ns: string]: T[]} = {};\n\n      options.forEach((option: any) => {\n        const ns = option.metadata.namespace;\n\n        if (option.kind === 'group') { // this could contain a previous option set which contains groups\n          return;\n        }\n        if (!namespaced[ns]) {\n          namespaced[ns] = [];\n        }\n        namespaced[ns].push(option);\n      });\n\n      resPage = [];\n\n      // ... then sort groups by name and combined into a single array\n      Object.keys(namespaced).sort().forEach((ns) => {\n        resPage.push({\n          kind:     'group',\n          icon:     'icon-namespace',\n          id:       ns,\n          metadata: { name: ns },\n          disabled: true,\n        });\n        resPage = resPage.concat(namespaced[ns]);\n      });\n    } else {\n      resPage = options;\n    }\n\n    return {\n      page:  resPage,\n      pages: res.pages || Math.ceil(res.count / (pageSize || Number.MAX_SAFE_INTEGER)),\n      total: res.count\n    };\n  } catch (err) {\n    console.error(err); // eslint-disable-line no-console\n  }\n\n  return {\n    page: [], pages: 0, total: 0\n  };\n}\n"]}]}