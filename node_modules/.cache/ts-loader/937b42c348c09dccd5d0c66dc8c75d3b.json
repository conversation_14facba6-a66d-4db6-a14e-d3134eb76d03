{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/SimpleNodeGroup.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/SimpleNodeGroup.vue", "mtime": 1755002461158}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/SimpleNodeGroup.vue"], "names": [], "mappings": "AACA,OAAO,EAAE,eAAe,EAAW,MAAO,KAAK,CAAA;AAC/C,OAAO,YAAW,MAAO,gDAAgD,CAAA;AACzE,OAAO,aAAY,MAAO,0CAA0C,CAAA;AACpE,OAAO,SAAQ,MAAO,sCAAsC,CAAA;AAC5D,OAAO,MAAK,MAAO,+BAA+B,CAAA;AAGlD,eAAe,eAAe,CAAC;IAC7B,IAAI,EAAE,iBAAiB;IAEvB,UAAU,EAAE;QACV,YAAY;QACZ,aAAa;QACb,SAAS;QACT,MAAK;KACN;IAED,KAAK,EAAE;QACL,UAAU,EAAE;YACV,IAAI,EAAM,MAAgC;YAC1C,QAAQ,EAAE,IAAG;SACd;QACD,mBAAmB,EAAE;YACnB,IAAI,EAAK,KAA2C;YACpD,OAAO,EAAE,GAAG,EAAC,CAAE,EAAC;SACjB;QACD,KAAK,EAAE;YACL,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,GAAG,EAAC,CAAE,CAAC,EAAE,CAAA;SACpB;KACD;IAED,KAAK,EAAE,CAAC,mBAAmB,CAAC;IAE5B,QAAQ,EAAE;QACR,SAAS,EAAE;YACT,GAAG;gBACD,OAAO,IAAI,CAAC,UAAU,CAAA;YACxB,CAAC;YACD,GAAG,CAAC,KAAmB;gBACrB,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAA;YACxC,CAAA;SACD;QAED,wBAAwB;YACtB,+DAA8D;YAC9D,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,GAA2B,EAAE,EAAC;gBACpE,IAAI,GAAG,CAAC,IAAG,KAAM,OAAO;oBAAE,OAAO,KAAK,CAAA;gBACtC,MAAM,KAAI,GAAI,GAAG,CAAC,KAAI,IAAK,EAAE,CAAA;gBAC7B,iDAAgD;gBAChD,OAAO,KAAK,CAAC,UAAU,CAAC,KAAK,CAAA,IAAK,KAAK,CAAC,UAAU,CAAC,MAAM,CAAA;oBAClD,KAAK,CAAC,UAAU,CAAC,KAAK,CAAA,IAAK,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAA;YAC5D,CAAC,CAAC,CAAA;QACJ,CAAC;QAED,oBAAoB;YAClB,0DAAyD;YACzD,MAAM,OAAO,GAA2B;gBACtC,UAAU,EAAI,EAAE;gBAChB,WAAW,EAAG,EAAE;gBAChB,UAAU,EAAI,EAAE;gBAChB,WAAW,EAAG,GAAG;gBACjB,WAAW,EAAG,EAAE;gBAChB,YAAY,EAAE,EAAE;gBAChB,WAAW,EAAG,EAAE;gBAChB,UAAU,EAAI,EAAE;gBAChB,WAAW,EAAG,GAAG;gBACjB,WAAW,EAAG,EAAE;gBAChB,YAAY,EAAE,GAAG;aAClB,CAAA;YAED,MAAM,YAAW,GAAI,IAAI,CAAC,SAAS,CAAC,YAAW,CAAE,CAAA,CAAE,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAA,IAAK,EAAC,CAAE,CAAA,CAAE,EAAE,CAAA;YAClG,MAAM,SAAQ,GAAI,YAAW,GAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAU,IAAK,CAAC,CAAC,CAAA;YAElE,OAAO,KAAK,SAAS,QAAQ,CAAA;QAC/B,CAAA;KACD;IAED,OAAO,EAAE;QACP,kBAAkB,CAAC,KAAa;YAC9B,IAAI,CAAC,SAAS,CAAC,YAAW,GAAI,KAAK,CAAA;QACrC,CAAC;QAED,eAAe,CAAC,KAA4C,EAAE,KAAa;YACzE,MAAM,QAAO,GAAI,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAA,IAAK,CAAC,CAAA;YACzC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAA,GAAI,QAAQ,CAAA;YAEhC,6BAA4B;YAC5B,IAAI,KAAI,KAAM,SAAQ,IAAK,QAAO,GAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAM,IAAK,CAAC,CAAC,EAAE,CAAA;gBACnE,IAAI,CAAC,SAAS,CAAC,OAAM,GAAI,QAAQ,CAAA;YACnC,CAAA;YACA,IAAI,KAAI,KAAM,SAAQ,IAAK,QAAO,GAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAM,IAAK,CAAC,CAAC,EAAE,CAAA;gBACnE,IAAI,CAAC,SAAS,CAAC,OAAM,GAAI,QAAQ,CAAA;YACnC,CAAA;YACA,IAAI,KAAI,KAAM,aAAa,EAAE,CAAA;gBAC3B,IAAI,QAAO,GAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAM,IAAK,CAAC,CAAC,EAAE,CAAA;oBAC5C,IAAI,CAAC,SAAS,CAAC,OAAM,GAAI,QAAQ,CAAA;gBACnC,CAAA;gBACA,IAAI,QAAO,GAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAM,IAAK,CAAC,CAAC,EAAE,CAAA;oBAC5C,IAAI,CAAC,SAAS,CAAC,OAAM,GAAI,QAAQ,CAAA;gBACnC,CAAA;YACF,CAAA;QACF,CAAA;KACF;CACD,CAAC,CAAA", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/SimpleNodeGroup.vue.tsx", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent, PropType } from 'vue';\nimport LabeledInput from '@components/Form/LabeledInput/LabeledInput.vue';\nimport LabeledSelect from '@shell/components/form/LabeledSelect.vue';\nimport UnitInput from '@shell/components/form/UnitInput.vue';\nimport Banner from '@components/Banner/Banner.vue';\nimport { EKSNodeGroup, AWS } from '../../types';\n\nexport default defineComponent({\n  name: 'SimpleNodeGroup',\n\n  components: {\n    LabeledInput,\n    LabeledSelect,\n    UnitInput,\n    Banner\n  },\n\n  props: {\n    modelValue: {\n      type:     Object as PropType<EKSNodeGroup>,\n      required: true\n    },\n    instanceTypeOptions: {\n      type:    Array as PropType<AWS.InstanceTypeOption[]>,\n      default: () => []\n    },\n    rules: {\n      type:    Object,\n      default: () => ({})\n    }\n  },\n\n  emits: ['update:modelValue'],\n\n  computed: {\n    nodeGroup: {\n      get(): EKSNodeGroup {\n        return this.modelValue;\n      },\n      set(value: EKSNodeGroup) {\n        this.$emit('update:modelValue', value);\n      }\n    },\n\n    recommendedInstanceTypes(): AWS.InstanceTypeOption[] {\n      // Filter to show only recommended instance types for PLG users\n      return this.instanceTypeOptions.filter((opt: AWS.InstanceTypeOption) => {\n        if (opt.kind === 'group') return false;\n        const value = opt.value || '';\n        // Recommend t3/t4g series for cost-effectiveness\n        return value.startsWith('t3.') || value.startsWith('t4g.') || \n               value.startsWith('m5.') || value.startsWith('m6i.');\n      });\n    },\n\n    estimatedMonthlyCost(): string {\n      // Simple cost estimation based on instance type and count\n      const costMap: Record<string, number> = {\n        't3.small':   15,\n        't3.medium':  30,\n        't3.large':   60,\n        't3.xlarge':  120,\n        't4g.small':  12,\n        't4g.medium': 24,\n        't4g.large':  48,\n        'm5.large':   70,\n        'm5.xlarge':  140,\n        'm6i.large':  70,\n        'm6i.xlarge': 140,\n      };\n\n      const instanceCost = this.nodeGroup.instanceType ? costMap[this.nodeGroup.instanceType] || 50 : 50;\n      const totalCost = instanceCost * (this.nodeGroup.desiredSize || 2);\n      \n      return `~$${totalCost}/month`;\n    }\n  },\n\n  methods: {\n    updateInstanceType(value: string) {\n      this.nodeGroup.instanceType = value;\n    },\n\n    updateNodeCount(field: 'minSize' | 'maxSize' | 'desiredSize', value: string) {\n      const numValue = parseInt(value, 10) || 0;\n      this.nodeGroup[field] = numValue;\n      \n      // Ensure logical constraints\n      if (field === 'minSize' && numValue > (this.nodeGroup.maxSize || 0)) {\n        this.nodeGroup.maxSize = numValue;\n      }\n      if (field === 'maxSize' && numValue < (this.nodeGroup.minSize || 0)) {\n        this.nodeGroup.minSize = numValue;\n      }\n      if (field === 'desiredSize') {\n        if (numValue < (this.nodeGroup.minSize || 0)) {\n          this.nodeGroup.minSize = numValue;\n        }\n        if (numValue > (this.nodeGroup.maxSize || 0)) {\n          this.nodeGroup.maxSize = numValue;\n        }\n      }\n    }\n  }\n});\n</script>\n\n<template>\n  <div class=\"simple-node-group\">\n    <div class=\"node-config-section\">\n      <h4>Instance Type</h4>\n      <p class=\"text-muted mb-10\">\n        Select the computing power for your worker nodes\n      </p>\n      \n      <LabeledSelect\n        v-model:value=\"nodeGroup.instanceType\"\n        label=\"Instance Type\"\n        :options=\"recommendedInstanceTypes\"\n        :rules=\"rules.instanceType\"\n        :searchable=\"true\"\n        placeholder=\"Select an instance type\"\n      >\n        <template #option=\"{ option }\">\n          <div class=\"instance-option\">\n            <span>{{ option.label }}</span>\n            <span\n              v-if=\"option.value?.startsWith('t3.') || option.value?.startsWith('t4g.')\"\n              class=\"badge-recommended\"\n            >\n              Recommended\n            </span>\n          </div>\n        </template>\n      </LabeledSelect>\n\n      <div\n        v-if=\"nodeGroup.instanceType\"\n        class=\"cost-estimate mt-10\"\n      >\n        <i class=\"icon icon-dollar\" />\n        <span>Estimated cost: <strong>{{ estimatedMonthlyCost }}</strong></span>\n      </div>\n    </div>\n\n    <div class=\"node-config-section mt-20\">\n      <h4>Cluster Size</h4>\n      <p class=\"text-muted mb-10\">\n        Configure auto-scaling for your cluster\n      </p>\n\n      <div class=\"row\">\n        <div class=\"col span-4\">\n          <UnitInput\n            v-model:value=\"nodeGroup.minSize\"\n            label=\"Minimum Nodes\"\n            suffix=\"nodes\"\n            :rules=\"rules.minSize\"\n            @update:value=\"updateNodeCount('minSize', $event)\"\n          />\n        </div>\n        <div class=\"col span-4\">\n          <UnitInput\n            v-model:value=\"nodeGroup.desiredSize\"\n            label=\"Desired Nodes\"\n            suffix=\"nodes\"\n            :rules=\"rules.desiredSize\"\n            @update:value=\"updateNodeCount('desiredSize', $event)\"\n          />\n        </div>\n        <div class=\"col span-4\">\n          <UnitInput\n            v-model:value=\"nodeGroup.maxSize\"\n            label=\"Maximum Nodes\"\n            suffix=\"nodes\"\n            :rules=\"rules.maxSize\"\n            @update:value=\"updateNodeCount('maxSize', $event)\"\n          />\n        </div>\n      </div>\n\n      <Banner\n        color=\"info\"\n        class=\"mt-10\"\n      >\n        <p>\n          Your cluster will automatically scale between \n          <strong>{{ nodeGroup.minSize }}</strong> and \n          <strong>{{ nodeGroup.maxSize }}</strong> nodes based on workload.\n        </p>\n      </Banner>\n    </div>\n\n    <div class=\"node-config-section mt-20\">\n      <h4>Storage</h4>\n      <p class=\"text-muted mb-10\">\n        Disk space for each node\n      </p>\n      \n      <div class=\"row\">\n        <div class=\"col span-6\">\n          <UnitInput\n            v-model:value=\"nodeGroup.diskSize\"\n            label=\"Disk Size\"\n            suffix=\"GB\"\n            :rules=\"rules.diskSize\"\n          />\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.simple-node-group {\n  .node-config-section {\n    padding: 15px;\n    background: var(--body-bg);\n    border: 1px solid var(--border);\n    border-radius: var(--border-radius);\n\n    h4 {\n      margin: 0 0 5px 0;\n      font-size: 16px;\n      font-weight: 600;\n    }\n\n    .text-muted {\n      color: var(--text-muted);\n      font-size: 14px;\n    }\n  }\n\n  .instance-option {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    width: 100%;\n\n    .badge-recommended {\n      background: var(--success);\n      color: var(--success-text);\n      padding: 2px 8px;\n      border-radius: 3px;\n      font-size: 11px;\n      font-weight: 600;\n      text-transform: uppercase;\n    }\n  }\n\n  .cost-estimate {\n    padding: 10px;\n    background: var(--info-banner-bg);\n    border: 1px solid var(--info);\n    border-radius: var(--border-radius);\n    display: flex;\n    align-items: center;\n    gap: 10px;\n    color: var(--text-default);\n\n    i {\n      color: var(--info);\n    }\n\n    strong {\n      color: var(--text-default);\n      font-weight: 600;\n    }\n  }\n}\n</style>\n\n"]}]}