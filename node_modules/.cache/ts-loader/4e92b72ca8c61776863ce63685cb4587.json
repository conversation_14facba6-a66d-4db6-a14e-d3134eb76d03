{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Accordion/Accordion.vue?vue&type=template&id=42964d6c&scoped=true&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Accordion/Accordion.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Accordion/Accordion.vue?vue&type=template&id=42964d6c&scoped=true&ts=true", "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Accordion/Accordion.vue"], "names": ["isOpen", "<PERSON><PERSON><PERSON>", "t", "title"], "mappings": "AAAA,OAAO,EAAE,cAAc,IAAI,eAAe,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,UAAU,IAAI,WAAW,EAAE,eAAe,IAAI,gBAAgB,EAAE,eAAe,IAAI,gBAAgB,EAAE,KAAK,IAAI,MAAM,EAAE,cAAc,IAAI,eAAe,EAAE,SAAS,IAAI,UAAU,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,MAAM,KAAK,CAAA;AAE/T,MAAM,UAAU,GAAG,ECmCZ,KAAK,EAAC,qBAAqB,EAAA,CAAA;ADlClC,MAAM,UAAU,GAAG;IC+CT,aAAW,EAAC,8BAA8B;IAC1C,KAAK,EAAC,MAAM;CD7CrB,CAAA;AACD,MAAM,UAAU,GAAG;ICoDb,KAAK,EAAC,gBAAgB;IACtB,aAAW,EAAC,gBAAgB;CDlDjC,CAAA;AAED,MAAM,UAAU,MAAM,CAAC,IAAS,EAAC,MAAW,EAAC,MAAW,EAAC,MAAW,EAAC,KAAU,EAAC,QAAa;IAC3F,OAAO,CAAC,UAAU,EAAE,ECwBpB,mBAAA,CA2BM,KAAA,EA3BN,UA2BM,EAAA;QA1BJ,mBAAA,CAkBM,KAAA,EAAA;YAjBJ,KAAK,EAAC,kBAAkB;YACxB,aAAW,EAAC,kBAAkB;YAC7B,OAAK,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA;gBDvBZ,YAAY;gBACZ,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC,CCsBC,IAAA,CAAA,MAAA,IAAA,IAAA,CAAA,MAAA,CAAA,GAAA,IAAA,CAAM,CAAA,CAAA;SDrBf,EAAE;YCuBD,mBAAA,CAIE,GAAA,EAAA;gBAHA,KAAK,EAAA,eAAA,CAAA,CAAC,mBAAmB,EAAA,EAAA,mBAAA,EACIA,IAAAA,CAAAA,MAAM,EAAA,iBAAA,EAAA,CAAqBA,IAAAA,CAAAA,MAAM,EAAA,CAAA,CAAA;gBAC9D,aAAW,EAAC,mBAAmB;aDtBhC,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,CAAC;YACvB,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;YCuB7C,WAAA,CAOO,IAAA,CAAA,MAAA,EAAA,QAAA,EAAA,EAAA,EAPP,GAOO,EAAA,CAAA;gBANL,mBAAA,CAKK,IAAA,EALL,UAKK,EAAA,gBAAA,CADAC,IAAAA,CAAAA,QAAQ,CAAA,CAAA,CAAGC,IAAAA,CAAAA,CAAC,CAACD,IAAAA,CAAAA,QAAQ,CAAA,CAAA,CAAA,CAAIE,IAAAA,CAAAA,KAAK,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;aDzBpC,EAAE,IAAI,CAAC;SACT,CAAC;QACF,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;QAC7C,eAAe,CC0Bf,mBAAA,CAMM,KAAA,EANN,UAMM,EAAA;YADJ,WAAA,CAAQ,IAAA,CAAA,MAAA,EAAA,SAAA,EAAA,EAAA,EAAA,SAAA,EAAA,IAAA,CAAA;SD7BT,EAAE,GAAG,CAAC,gBAAgB,CAAC,EAAE;YACxB,CAAC,MAAM,ECwBCH,IAAAA,CAAAA,MAAM,CAAA;SDvBf,CAAC;KACH,CAAC,CAAC,CAAA;AACL,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Accordion/Accordion.vue.tsx", "sourceRoot": "", "sourcesContent": ["import { normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, renderSlot as _renderSlot, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, vShow as _vShow, withDirectives as _withDirectives, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"accordion-container\" }\nconst _hoisted_2 = {\n  \"data-testid\": \"accordion-title-slot-content\",\n  class: \"mb-0\"\n}\nconst _hoisted_3 = {\n  class: \"accordion-body\",\n  \"data-testid\": \"accordion-body\"\n}\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", {\n      class: \"accordion-header\",\n      \"data-testid\": \"accordion-header\",\n      onClick: _cache[0] || (_cache[0] = \n//@ts-ignore\n(...args) => (_ctx.toggle && _ctx.toggle(...args)))\n    }, [\n      _createElementVNode(\"i\", {\n        class: _normalizeClass([\"icon text-primary\", {'icon-chevron-down':_ctx.isOpen, 'icon-chevron-up':!_ctx.isOpen}]),\n        \"data-testid\": \"accordion-chevron\"\n      }, null, 2 /* CLASS */),\n      _cache[1] || (_cache[1] = _createTextVNode()),\n      _renderSlot(_ctx.$slots, \"header\", {}, () => [\n        _createElementVNode(\"h2\", _hoisted_2, _toDisplayString(_ctx.titleKey ? _ctx.t(_ctx.titleKey) : _ctx.title), 1 /* TEXT */)\n      ], true)\n    ]),\n    _cache[2] || (_cache[2] = _createTextVNode()),\n    _withDirectives(_createElementVNode(\"div\", _hoisted_3, [\n      _renderSlot(_ctx.$slots, \"default\", {}, undefined, true)\n    ], 512 /* NEED_PATCH */), [\n      [_vShow, _ctx.isOpen]\n    ])\n  ]))\n}", "<script lang=\"ts\">\nimport { defineComponent } from 'vue';\nimport { mapGetters } from 'vuex';\n\nexport default defineComponent({\n  props: {\n    title: {\n      type:    String,\n      default: ''\n    },\n\n    titleKey: {\n      type:    String,\n      default: null\n    },\n\n    openInitially: {\n      type:    Boolean,\n      default: false\n    }\n  },\n\n  data() {\n    return { isOpen: this.openInitially };\n  },\n\n  computed: { ...mapGetters({ t: 'i18n/t' }) },\n\n  methods: {\n    toggle() {\n      this.isOpen = !this.isOpen;\n    }\n  }\n});\n</script>\n\n<template>\n  <div class=\"accordion-container\">\n    <div\n      class=\"accordion-header\"\n      data-testid=\"accordion-header\"\n      @click=\"toggle\"\n    >\n      <i\n        class=\"icon text-primary\"\n        :class=\"{'icon-chevron-down':isOpen, 'icon-chevron-up':!isOpen}\"\n        data-testid=\"accordion-chevron\"\n      />\n      <slot name=\"header\">\n        <h2\n          data-testid=\"accordion-title-slot-content\"\n          class=\"mb-0\"\n        >\n          {{ titleKey ? t(titleKey) : title }}\n        </h2>\n      </slot>\n    </div>\n    <div\n      v-show=\"isOpen\"\n      class=\"accordion-body\"\n      data-testid=\"accordion-body\"\n    >\n      <slot />\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.accordion-container {\n  border: 1px solid var(--border)\n}\n.accordion-header {\n  padding: 16px 16px 16px 11px;\n  display: flex;\n  align-items: center;\n  &>*{\n    padding: 5px 0px 5px 0px;\n  }\n  I {\n    margin: 0px 10px 0px 10px;\n  }\n}\n.accordion-body {\n  padding: 0px 16px 16px;\n}\n</style>\n"]}]}