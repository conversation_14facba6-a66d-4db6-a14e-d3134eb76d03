{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/composables/useCompactInput.ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/composables/useCompactInput.ts", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgY29tcHV0ZWQgfSBmcm9tICd2dWUnOwpleHBvcnQgY29uc3QgdXNlQ29tcGFjdElucHV0ID0gKHByb3BzKSA9PiB7CiAgICBjb25zdCBpc0NvbXBhY3QgPSBjb21wdXRlZCgoKSA9PiB7CiAgICAgICAgLy8gQ29tcGFjdCBpZiBleHBsaWNpdGx5IHNldCAtIG90aGVyd2lzZSBjb21wYWN0IGlmIHRoZXJlIGlzIG5vIGxhYmVsCiAgICAgICAgcmV0dXJuIChwcm9wcy5jb21wYWN0ICE9PSBudWxsICYmIHByb3BzLmNvbXBhY3QgIT09IHVuZGVmaW5lZCkgPyAhIXByb3BzLmNvbXBhY3QgOiAhKHByb3BzLmxhYmVsIHx8IHByb3BzLmxhYmVsS2V5KTsKICAgIH0pOwogICAgcmV0dXJuIHsgaXNDb21wYWN0IH07Cn07Cg=="}, {"version": 3, "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/composables/useCompactInput.ts", "sourceRoot": "", "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/composables/useCompactInput.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAe,MAAM,KAAK,CAAC;AAY5C,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,KAAwB,EAAmB,EAAE;IAC3E,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,EAAE;QAC9B,qEAAqE;QACrE,OAAO,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI,IAAI,KAAK,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC;IACtH,CAAC,CAAC,CAAC;IAEH,OAAO,EAAE,SAAS,EAAE,CAAC;AACvB,CAAC,CAAC", "sourcesContent": ["import { computed, ComputedRef } from 'vue';\n\ninterface CompactInputProps {\n  compact?: boolean | null;\n  label?: string;\n  labelKey?: string;\n}\n\ninterface UseCompactInput {\n  isCompact: ComputedRef<boolean>;\n}\n\nexport const useCompactInput = (props: CompactInputProps): UseCompactInput => {\n  const isCompact = computed(() => {\n    // Compact if explicitly set - otherwise compact if there is no label\n    return (props.compact !== null && props.compact !== undefined) ? !!props.compact : !(props.label || props.labelKey);\n  });\n\n  return { isCompact };\n};\n"]}]}