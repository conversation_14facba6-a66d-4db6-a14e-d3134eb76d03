{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownTrigger.vue?vue&type=script&setup=true&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownTrigger.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgZGVmaW5lQ29tcG9uZW50IGFzIF9kZWZpbmVDb21wb25lbnQgfSBmcm9tICd2dWUnOwppbXBvcnQgeyBpbmplY3QsIG9uTW91bnRlZCwgcmVmIH0gZnJvbSAndnVlJzsKaW1wb3J0IHsgUmNCdXR0b24gfSBmcm9tICdAY29tcG9uZW50cy9SY0J1dHRvbic7CmltcG9ydCB7IGRlZmF1bHRDb250ZXh0IH0gZnJvbSAnLi90eXBlcyc7CmV4cG9ydCBkZWZhdWx0IC8qQF9fUFVSRV9fKi8gX2RlZmluZUNvbXBvbmVudCh7CiAgICBfX25hbWU6ICdSY0Ryb3Bkb3duVHJpZ2dlcicsCiAgICBzZXR1cChfX3Byb3BzLCB7IGV4cG9zZTogX19leHBvc2UgfSkgewogICAgICAgIC8qKgogICAgICAgICAqIEEgYnV0dG9uIHRoYXQgb3BlbnMgYSBtZW51LiBVc2VkIGluIGNvbmp1bmN0aW9uIHdpdGggYFJjRHJvcGRvd24udnVlYC4KICAgICAgICAgKi8KICAgICAgICBjb25zdCB7IHNob3dNZW51LCByZWdpc3RlclRyaWdnZXIsIGlzTWVudU9wZW4sIGhhbmRsZUtleWRvd24sIH0gPSBpbmplY3QoJ2Ryb3Bkb3duQ29udGV4dCcpIHx8IGRlZmF1bHRDb250ZXh0OwogICAgICAgIGNvbnN0IGRyb3Bkb3duVHJpZ2dlciA9IHJlZihudWxsKTsKICAgICAgICBvbk1vdW50ZWQoKCkgPT4gewogICAgICAgICAgICByZWdpc3RlclRyaWdnZXIoZHJvcGRvd25UcmlnZ2VyLnZhbHVlKTsKICAgICAgICB9KTsKICAgICAgICBjb25zdCBmb2N1cyA9ICgpID0+IHsKICAgICAgICAgICAgdmFyIF9hOwogICAgICAgICAgICAoX2EgPSBkcm9wZG93blRyaWdnZXIgPT09IG51bGwgfHwgZHJvcGRvd25UcmlnZ2VyID09PSB2b2lkIDAgPyB2b2lkIDAgOiBkcm9wZG93blRyaWdnZXIudmFsdWUpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5mb2N1cygpOwogICAgICAgIH07CiAgICAgICAgX19leHBvc2UoeyBmb2N1cyB9KTsKICAgICAgICBjb25zdCBfX3JldHVybmVkX18gPSB7IHNob3dNZW51LCByZWdpc3RlclRyaWdnZXIsIGlzTWVudU9wZW4sIGhhbmRsZUtleWRvd24sIGRyb3Bkb3duVHJpZ2dlciwgZm9jdXMsIGdldCBSY0J1dHRvbigpIHsgcmV0dXJuIFJjQnV0dG9uOyB9IH07CiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KF9fcmV0dXJuZWRfXywgJ19faXNTY3JpcHRTZXR1cCcsIHsgZW51bWVyYWJsZTogZmFsc2UsIHZhbHVlOiB0cnVlIH0pOwogICAgICAgIHJldHVybiBfX3JldHVybmVkX187CiAgICB9Cn0pOwo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownTrigger.vue?vue&type=script&setup=true&lang=ts", "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownTrigger.vue"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,IAAI,gBAAgB,EAAE,MAAM,KAAK,CAAA;ACIzD,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,KAAK,CAAA;AAC5C,OAAO,EAAE,QAAQ,EAAgB,MAAM,sBAAsB,CAAA;AAC7D,OAAO,EAAmB,cAAc,EAAE,MAAM,SAAS,CAAA;ADAzD,eAAe,aAAa,CAAA,gBAAgB,CAAC;IAC3C,MAAM,EAAE,mBAAmB;IAC3B,KAAK,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE;QCPrC;;WAEE;QAKF,MAAM,EACJ,QAAQ,EACR,eAAe,EACf,UAAU,EACV,aAAa,GACd,GAAG,MAAM,CAAkB,iBAAiB,CAAC,IAAI,cAAc,CAAA;QAEhE,MAAM,eAAe,GAAG,GAAG,CAAsB,IAAI,CAAC,CAAA;QAEtD,SAAS,CAAC,GAAG,EAAE;YACb,eAAe,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;QACxC,CAAC,CAAC,CAAA;QAEF,MAAM,KAAK,GAAG,GAAG,EAAE;;YACjB,MAAA,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,KAAK,0CAAE,KAAK,EAAE,CAAA;QACjC,CAAC,CAAA;QAED,QAAY,CAAC,EAAE,KAAK,EAAE,CAAC,CAAA;QDOvB,MAAM,YAAY,GAAG,EAAE,QAAQ,EAAE,eAAe,EAAE,UAAU,EAAE,aAAa,EAAE,eAAe,EAAE,KAAK,EAAE,IAAI,QAAQ,KAAK,OAAO,QAAQ,CAAA,CAAC,CAAC,EAAE,CAAA;QACzI,MAAM,CAAC,cAAc,CAAC,YAAY,EAAE,iBAAiB,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAA;QAC1F,OAAO,YAAY,CAAA;IACnB,CAAC;CAEA,CAAC,CAAA", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownTrigger.vue.tsx", "sourceRoot": "", "sourcesContent": ["import { defineComponent as _defineComponent } from 'vue'\nimport { inject, onMounted, ref } from 'vue';\nimport { RcButton, RcButtonType } from '@components/RcButton';\nimport { DropdownContext, defaultContext } from './types';\n\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'RcDropdownTrigger',\n  setup(__props, { expose: __expose }) {\n\n/**\n * A button that opens a menu. Used in conjunction with `RcDropdown.vue`.\n */\nconst {\n  showMenu,\n  registerTrigger,\n  isMenuOpen,\n  handleKeydown,\n} = inject<DropdownContext>('dropdownContext') || defaultContext;\n\nconst dropdownTrigger = ref<RcButtonType | null>(null);\n\nonMounted(() => {\n  registerTrigger(dropdownTrigger.value);\n});\n\nconst focus = () => {\n  dropdownTrigger?.value?.focus();\n};\n\n__expose({ focus });\n\nconst __returned__ = { showMenu, registerTrigger, isMenuOpen, handleKeydown, dropdownTrigger, focus, get R<PERSON><PERSON><PERSON>on() { return RcButton } }\nObject.defineProperty(__returned__, '__isScriptSetup', { enumerable: false, value: true })\nreturn __returned__\n}\n\n})", "<script setup lang=\"ts\">\n/**\n * A button that opens a menu. Used in conjunction with `RcDropdown.vue`.\n */\nimport { inject, onMounted, ref } from 'vue';\nimport { RcButton, RcButtonType } from '@components/RcButton';\nimport { DropdownContext, defaultContext } from './types';\n\nconst {\n  showMenu,\n  registerTrigger,\n  isMenuOpen,\n  handleKeydown,\n} = inject<DropdownContext>('dropdownContext') || defaultContext;\n\nconst dropdownTrigger = ref<RcButtonType | null>(null);\n\nonMounted(() => {\n  registerTrigger(dropdownTrigger.value);\n});\n\nconst focus = () => {\n  dropdownTrigger?.value?.focus();\n};\n\ndefineExpose({ focus });\n</script>\n\n<template>\n  <RcButton\n    ref=\"dropdownTrigger\"\n    role=\"button\"\n    aria-haspopup=\"menu\"\n    :aria-expanded=\"isMenuOpen\"\n    @keydown.enter.space=\"handleKeydown\"\n    @click=\"showMenu(true)\"\n  >\n    <slot name=\"default\">\n      <!--Empty slot content-->\n    </slot>\n  </RcButton>\n</template>\n"]}]}