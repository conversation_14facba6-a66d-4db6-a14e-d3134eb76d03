{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcButton/RcButton.vue?vue&type=template&id=b795a3d0&scoped=true&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcButton/RcButton.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVuZGVyU2xvdCBhcyBfcmVuZGVyU2xvdCwgY3JlYXRlQ29tbWVudFZOb2RlIGFzIF9jcmVhdGVDb21tZW50Vk5vZGUsIGNyZWF0ZVRleHRWTm9kZSBhcyBfY3JlYXRlVGV4dFZOb2RlLCBub3JtYWxpemVDbGFzcyBhcyBfbm9ybWFsaXplQ2xhc3MsIG9wZW5CbG9jayBhcyBfb3BlbkJsb2NrLCBjcmVhdGVFbGVtZW50QmxvY2sgYXMgX2NyZWF0ZUVsZW1lbnRCbG9jayB9IGZyb20gInZ1ZSI7CmV4cG9ydCBmdW5jdGlvbiByZW5kZXIoX2N0eCwgX2NhY2hlLCAkcHJvcHMsICRzZXR1cCwgJGRhdGEsICRvcHRpb25zKSB7CiAgICByZXR1cm4gKF9vcGVuQmxvY2soKSwgX2NyZWF0ZUVsZW1lbnRCbG9jaygiYnV0dG9uIiwgewogICAgICAgIHJlZjogIlJjRm9jdXNUYXJnZXQiLAogICAgICAgIHJvbGU6ICJidXR0b24iLAogICAgICAgIGNsYXNzOiBfbm9ybWFsaXplQ2xhc3MoeyAuLi4kc2V0dXAuYnV0dG9uQ2xhc3MsIC4uLihfY3R4LiRhdHRycy5jbGFzcyB8fCB7fSkgfSkKICAgIH0sIFsKICAgICAgICBfcmVuZGVyU2xvdChfY3R4LiRzbG90cywgImJlZm9yZSIsIHt9LCAoKSA9PiBbCiAgICAgICAgICAgIF9jcmVhdGVDb21tZW50Vk5vZGUoIiBFbXB0eSBDb250ZW50ICIpCiAgICAgICAgXSwgdHJ1ZSksCiAgICAgICAgX2NhY2hlWzBdIHx8IChfY2FjaGVbMF0gPSBfY3JlYXRlVGV4dFZOb2RlKCkpLAogICAgICAgIF9yZW5kZXJTbG90KF9jdHguJHNsb3RzLCAiZGVmYXVsdCIsIHt9LCAoKSA9PiBbCiAgICAgICAgICAgIF9jcmVhdGVDb21tZW50Vk5vZGUoIiBFbXB0eSBDb250ZW50ICIpCiAgICAgICAgXSwgdHJ1ZSksCiAgICAgICAgX2NhY2hlWzFdIHx8IChfY2FjaGVbMV0gPSBfY3JlYXRlVGV4dFZOb2RlKCkpLAogICAgICAgIF9yZW5kZXJTbG90KF9jdHguJHNsb3RzLCAiYWZ0ZXIiLCB7fSwgKCkgPT4gWwogICAgICAgICAgICBfY3JlYXRlQ29tbWVudFZOb2RlKCIgRW1wdHkgQ29udGVudCAiKQogICAgICAgIF0sIHRydWUpCiAgICBdLCAyIC8qIENMQVNTICovKSk7Cn0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcButton/RcButton.vue?vue&type=template&id=b795a3d0&scoped=true&ts=true", "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcButton/RcButton.vue"], "names": ["$attrs"], "mappings": "AAAA,OAAO,EAAE,UAAU,IAAI,WAAW,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,eAAe,IAAI,gBAAgB,EAAE,cAAc,IAAI,eAAe,EAAE,SAAS,IAAI,UAAU,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,MAAM,KAAK,CAAA;AAEtO,MAAM,UAAU,MAAM,CAAC,IAAS,EAAC,MAAW,EAAC,MAAW,EAAC,MAAW,EAAC,KAAU,EAAC,QAAa;IAC3F,OAAO,CAAC,UAAU,EAAE,EC8CpB,mBAAA,CAcS,QAAA,EAAA;QAbP,GAAG,EAAC,eAAe;QACnB,IAAI,EAAC,QAAQ;QACZ,KAAK,EAAA,eAAA,CAAA,EAAA,GAAO,MAAA,CAAA,WAAW,EAAA,GAAA,CAAMA,IAAAA,CAAAA,MAAM,CAAC,KAAK,IAAA,EAAA,CAAA,EAAA,CAAA;KD7C3C,EAAE;QC+CD,WAAA,CAEO,IAAA,CAAA,MAAA,EAAA,QAAA,EAAA,EAAA,EAFP,GAEO,EAAA,CAAA;YADL,mBAAA,CAAA,iBAAA,CAAsB;SD7CvB,EAAE,IAAI,CAAC;QACR,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;QC8C7C,WAAA,CAEO,IAAA,CAAA,MAAA,EAAA,SAAA,EAAA,EAAA,EAFP,GAEO,EAAA,CAAA;YADL,mBAAA,CAAA,iBAAA,CAAsB;SD5CvB,EAAE,IAAI,CAAC;QACR,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;QC6C7C,WAAA,CAEO,IAAA,CAAA,MAAA,EAAA,OAAA,EAAA,EAAA,EAFP,GAEO,EAAA,CAAA;YADL,mBAAA,CAAA,iBAAA,CAAsB;SD3CvB,EAAE,IAAI,CAAC;KACT,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAA;AACpB,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcButton/RcButton.vue.tsx", "sourceRoot": "", "sourcesContent": ["import { renderSlot as _renderSlot, createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, normalizeClass as _normalizeClass, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\"\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  return (_openBlock(), _createElementBlock(\"button\", {\n    ref: \"RcFocusTarget\",\n    role: \"button\",\n    class: _normalizeClass({ ...$setup.buttonClass, ...(_ctx.$attrs.class || { }) })\n  }, [\n    _renderSlot(_ctx.$slots, \"before\", {}, () => [\n      _createCommentVNode(\" Empty Content \")\n    ], true),\n    _cache[0] || (_cache[0] = _createTextVNode()),\n    _renderSlot(_ctx.$slots, \"default\", {}, () => [\n      _createCommentVNode(\" Empty Content \")\n    ], true),\n    _cache[1] || (_cache[1] = _createTextVNode()),\n    _renderSlot(_ctx.$slots, \"after\", {}, () => [\n      _createCommentVNode(\" Empty Content \")\n    ], true)\n  ], 2 /* CLASS */))\n}", "<script setup lang=\"ts\">\n/**\n * A button element used for performing actions, such as submitting forms or\n * opening dialogs.\n *\n * Example:\n *\n * <rc-button primary @click=\"doAction\">Perform an Action</rc-button>\n */\nimport { computed, ref, defineExpose } from 'vue';\nimport { ButtonRoleProps, ButtonSizeProps } from './types';\n\nconst buttonRoles: { role: keyof ButtonRoleProps, className: string }[] = [\n  { role: 'primary', className: 'role-primary' },\n  { role: 'secondary', className: 'role-secondary' },\n  { role: 'tertiary', className: 'role-tertiary' },\n  { role: 'link', className: 'role-link' },\n  { role: 'ghost', className: 'role-ghost' },\n];\n\nconst buttonSizes: { size: keyof ButtonSizeProps, className: string }[] = [\n  { size: 'small', className: 'btn-sm' },\n];\n\nconst props = defineProps<ButtonRoleProps & ButtonSizeProps>();\n\nconst buttonClass = computed(() => {\n  const activeRole = buttonRoles.find(({ role }) => props[role]);\n  const isButtonSmall = buttonSizes.some(({ size }) => props[size]);\n\n  return {\n    btn: true,\n\n    [activeRole?.className || 'role-primary']: true,\n\n    'btn-sm': isButtonSmall,\n  };\n});\n\nconst RcFocusTarget = ref<HTMLElement | null>(null);\n\nconst focus = () => {\n  RcFocusTarget?.value?.focus();\n};\n\ndefineExpose({ focus });\n</script>\n\n<template>\n  <button\n    ref=\"RcFocusTarget\"\n    role=\"button\"\n    :class=\"{ ...buttonClass, ...($attrs.class || { }) }\"\n  >\n    <slot name=\"before\">\n      <!-- Empty Content -->\n    </slot>\n    <slot>\n      <!-- Empty Content -->\n    </slot>\n    <slot name=\"after\">\n      <!-- Empty Content -->\n    </slot>\n  </button>\n</template>\n\n<style lang=\"scss\" scoped>\nbutton {\n  &.role-link {\n    &:focus, &.focused {\n      @include focus-outline;\n      outline-offset: -2px;\n    }\n\n    &:hover {\n      background-color: var(--accent-btn);\n      box-shadow: none;\n    }\n  }\n\n  &.role-ghost {\n    padding: 0;\n    background-color: transparent;\n\n    &:focus, &.focused {\n      @include focus-outline;\n      outline-offset: 0;\n    }\n\n    &:focus-visible {\n      @include focus-outline;\n      outline-offset: 0;\n    }\n  }\n}</style>\n"]}]}