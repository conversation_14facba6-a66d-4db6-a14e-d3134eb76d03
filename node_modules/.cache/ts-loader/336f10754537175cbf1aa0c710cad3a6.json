{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/types.ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/types.ts", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVmIH0gZnJvbSAndnVlJzsKZXhwb3J0IGNvbnN0IGRlZmF1bHRDb250ZXh0ID0gewogICAgaGFuZGxlS2V5ZG93bjogKCkgPT4gbnVsbCwKICAgIHNob3dNZW51OiAoX3Nob3cpID0+IG51bGwsCiAgICByZWdpc3RlclRyaWdnZXI6IChfdHJpZ2dlclJlZikgPT4gbnVsbCwKICAgIGRyb3Bkb3duSXRlbXM6IHJlZihbXSksCiAgICBmb2N1c0ZpcnN0RWxlbWVudDogKCkgPT4gbnVsbCwKICAgIGlzTWVudU9wZW46IHJlZihmYWxzZSksCiAgICBjbG9zZTogKCkgPT4gbnVsbCwKfTsK"}, {"version": 3, "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/types.ts", "sourceRoot": "", "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/types.ts"], "names": [], "mappings": "AAAA,OAAO,EAAO,GAAG,EAAE,MAAM,KAAK,CAAC;AAc/B,MAAM,CAAC,MAAM,cAAc,GAAoB;IAC7C,aAAa,EAAM,GAAG,EAAE,CAAC,IAAI;IAC7B,QAAQ,EAAW,CAAC,KAAqB,EAAE,EAAE,CAAC,IAAI;IAClD,eAAe,EAAI,CAAC,WAAgC,EAAE,EAAE,CAAC,IAAI;IAC7D,aAAa,EAAM,GAAG,CAAC,EAAE,CAAC;IAC1B,iBAAiB,EAAE,GAAG,EAAE,CAAC,IAAI;IAC7B,UAAU,EAAS,GAAG,CAAC,KAAK,CAAC;IAC7B,KAAK,EAAc,GAAG,EAAE,CAAC,IAAI;CAC9B,CAAC", "sourcesContent": ["import { Ref, ref } from 'vue';\nimport type { RcButtonType } from '@components/RcButton';\nimport { ButtonRoleProps, ButtonSizeProps } from '@components/RcButton/types';\n\nexport type DropdownContext = {\n  handleKeydown: () => void;\n  showMenu: (show: boolean) => void;\n  registerTrigger: (triggerRef: RcButtonType | null) => void;\n  dropdownItems: Ref<Element[]>;\n  focusFirstElement: () => void;\n  isMenuOpen: Ref<boolean>;\n  close: () => void;\n}\n\nexport const defaultContext: DropdownContext = {\n  handleKeydown:     () => null,\n  showMenu:          (_show: boolean | null) => null,\n  registerTrigger:   (_triggerRef: RcButtonType | null) => null,\n  dropdownItems:     ref([]),\n  focusFirstElement: () => null,\n  isMenuOpen:        ref(false),\n  close:             () => null,\n};\n\nexport type DropdownOption = {\n  action?: string;\n  divider?: boolean;\n  enabled: boolean;\n  icon?: string;\n  svg?: string;\n  label?: string;\n  total: number;\n  allEnabled: boolean;\n  anyEnabled: boolean;\n  available: number;\n  bulkable?: boolean;\n  bulkAction?: string;\n  altAction?: string;\n  weight?: number;\n}\n\nexport type RcDropdownMenuComponentProps = {\n  options: DropdownOption[];\n  buttonRole?: keyof ButtonRoleProps;\n  buttonSize?: keyof ButtonSizeProps;\n  buttonAriaLabel?: string;\n  dropdownAriaLabel?: string;\n  dataTestid?: string;\n}\n"]}]}