{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdown.vue?vue&type=template&id=cd7e8d90&scoped=true&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdown.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdown.vue?vue&type=template&id=cd7e8d90&scoped=true&ts=true", "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdown.vue"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,IAAI,WAAW,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,QAAQ,IAAI,SAAS,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,eAAe,IAAI,gBAAgB,EAAE,gBAAgB,IAAI,iBAAiB,EAAE,OAAO,IAAI,QAAQ,EAAE,WAAW,IAAI,YAAY,EAAE,QAAQ,IAAI,SAAS,EAAE,SAAS,IAAI,UAAU,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,MAAM,KAAK,CAAA;AAErX,MAAM,UAAU,GAAG,CAAC,YAAY,CAAC,CAAA;AAEjC,MAAM,UAAU,MAAM,CAAC,IAAS,EAAC,MAAW,EAAC,MAAW,EAAC,MAAW,EAAC,KAAU,EAAC,QAAa;IAC3F,MAAM,qBAAqB,GAAG,iBAAiB,CAAC,YAAY,CAAE,CAAA;IAE9D,OAAO,CAAC,UAAU,EAAE,EAAE,mBAAmB,CAAC,SAAS,EAAE,IAAI,EAAE;QCkD3D,YAAA,CA8Ba,qBAAA,EAAA;YA7BX,eAAa,EAAb,EAAa;YACZ,QAAQ,EAAE,EAAE;YACZ,KAAK,EAAE,MAAA,CAAA,UAAU;YACjB,WAAS,EAAE,KAAK;YAChB,SAAS,EAAE,MAAA,CAAA,eAAe;YAC1B,SAAS,EAAE,YAAY;YACvB,WAAU,EAAE,MAAA,CAAA,SAAS;SDhDrB,EAAE;YCsDQ,MAAM,EAAA,QAAA,CACf,GAcM,EAAA,CAAA;gBAdN,mBAAA,CAcM,KAAA,EAAA;oBAbJ,GAAG,EAAC,gBAAgB;oBACpB,KAAK,EAAC,gBAAgB;oBACtB,QAAQ,EAAC,IAAI;oBACb,IAAI,EAAC,MAAM;oBACX,kBAAgB,EAAC,UAAU;oBAC3B,0BAAwB,EAAxB,EAAwB;oBACvB,YAAU,EAAE,MAAA,CAAA,SAAS,IAAA,eAAA;oBACrB,SAAO,EAAA;wBDpDJ,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;4BACnC,YAAY;4BACZ,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC,CCkDK,MAAA,CAAA,aAAA,IAAA,MAAA,CAAA,aAAA,CAAA,GAAA,IAAA,CAAa,CAAA,CAAA;wBDjDnB,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,MAAW,EAAE,EAAE,CAAC,CCkD1C,MAAA,CAAA,QAAQ,EAAA,CAAA,EAAA,CAAA,MAAA,CAAA,CAAA,CAAA;qBDjDpB;iBACF,EAAE;oBCkDH,WAAA,CAEO,IAAA,CAAA,MAAA,EAAA,oBAAA,EAAA,EAAA,EAFP,GAEO,EAAA,CAAA;wBADL,mBAAA,CAAA,oBAAA,CAAyB;qBDhDxB,EAAE,IAAI,CAAC;iBACT,EAAE,EAAE,CAAC,2BAA2B,EAAE,UAAU,CAAC;aAC/C,CAAC;YACF,OAAO,EAAE,QAAQ,CC4BnB,GAEO,EAAA,CAAA;gBAFP,WAAA,CAEO,IAAA,CAAA,MAAA,EAAA,SAAA,EAAA,EAAA,EAFP,GAEO,EAAA,CAAA;oBADL,mBAAA,CAAA,4BAAA,CAAiC;iBD1B9B,EAAE,IAAI,CAAC;gBACR,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;aAC9C,CAAC;YACF,CAAC,EAAE,CAAC,CAAC,eAAe;YACpB,EAAE,EAAE,CAAC,CAAC,CAAC;SACR,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QACzC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;QCyC/C,mBAAA,CAOM,KAAA,EAAA;YANJ,GAAG,EAAC,iBAAiB;YACrB,KAAK,EAAC,iBAAiB;YACtB,SAAO,EAAA;gBDvCJ,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,MAAW,EAAE,EAAE,CAAC,CCuC3C,MAAA,CAAA,QAAQ,CAAA,KAAA,CAAA,CAAA,EAAA,CAAA,KAAA,CAAA,CAAA,CAAA;gBDtClB,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,SAAS;gBAC3C,YAAY;gBACZ,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC,CCqCQ,MAAA,CAAA,WAAA,IAAA,MAAA,CAAA,WAAA,CAAA,GAAA,IAAA,CAAW,CAAA,EAAA,CAAA,QAAA,CAAA,CAAA,CAAA;aDpCzB;SACF,EAAE;YCqCH,mBAAA,CAAA,6CAAA,CAAkD;SDnCjD,EAAE,GAAG,CAAC,gCAAgC,CAAC;KACzC,EAAE,EAAE,CAAC,qBAAqB,CAAC,CAAC,CAAA;AAC/B,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdown.vue.tsx", "sourceRoot": "", "sourcesContent": ["import { renderSlot as _renderSlot, createCommentVNode as _createCommentVNode, with<PERSON><PERSON><PERSON> as _with<PERSON>ey<PERSON>, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = [\"aria-label\"]\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_v_dropdown = _resolveComponent(\"v-dropdown\")!\n\n  return (_openBlock(), _createElementBlock(_Fragment, null, [\n    _createVNode(_component_v_dropdown, {\n      \"no-auto-focus\": \"\",\n      triggers: [],\n      shown: $setup.isMenuOpen,\n      \"auto-hide\": false,\n      container: $setup.popperContainer,\n      placement: 'bottom-end',\n      onApplyShow: $setup.applyShow\n    }, {\n      popper: _withCtx(() => [\n        _createElementVNode(\"div\", {\n          ref: \"dropdownTarget\",\n          class: \"dropdownTarget\",\n          tabindex: \"-1\",\n          role: \"menu\",\n          \"aria-orientation\": \"vertical\",\n          \"dropdown-menu-collection\": \"\",\n          \"aria-label\": $props.ariaLabel || 'Dropdown Menu',\n          onKeydown: [\n            _cache[0] || (_cache[0] = \n//@ts-ignore\n(...args) => ($setup.handleKeydown && $setup.handleKeydown(...args))),\n            _cache[1] || (_cache[1] = _withKeys(($event: any) => ($setup.setFocus()), [\"down\"]))\n          ]\n        }, [\n          _renderSlot(_ctx.$slots, \"dropdownCollection\", {}, () => [\n            _createCommentVNode(\"Empty slot content\")\n          ], true)\n        ], 40 /* PROPS, NEED_HYDRATION */, _hoisted_1)\n      ]),\n      default: _withCtx(() => [\n        _renderSlot(_ctx.$slots, \"default\", {}, () => [\n          _createCommentVNode(\"Empty slot content Trigger\")\n        ], true),\n        _cache[4] || (_cache[4] = _createTextVNode())\n      ]),\n      _: 3 /* FORWARDED */,\n      __: [4]\n    }, 8 /* PROPS */, [\"shown\", \"container\"]),\n    _cache[5] || (_cache[5] = _createTextVNode()),\n    _createElementVNode(\"div\", {\n      ref: \"popperContainer\",\n      class: \"popperContainer\",\n      onKeydown: [\n        _cache[2] || (_cache[2] = _withKeys(($event: any) => ($setup.showMenu(false)), [\"tab\"])),\n        _cache[3] || (_cache[3] = _withKeys(\n//@ts-ignore\n(...args) => ($setup.returnFocus && $setup.returnFocus(...args)), [\"escape\"]))\n      ]\n    }, [\n      _createCommentVNode(\"Empty container for mounting popper content\")\n    ], 544 /* NEED_HYDRATION, NEED_PATCH */)\n  ], 64 /* STABLE_FRAGMENT */))\n}", "<script setup lang=\"ts\">\n/**\n * Offers a list of choices to the user, such as a set of actions or functions.\n * Opened by activating RcDropdownTrigger.\n *\n * Example:\n *\n *  <rc-dropdown :aria-label=\"t('nav.actionMenu.label')\">\n *    <rc-dropdown-trigger tertiary>\n *      <i class=\"icon icon-actions\" />\n *    </rc-dropdown-trigger>\n *    <template #dropdownCollection>\n *      <rc-dropdown-item @click=\"performAction()\">\n *        Action 1\n *      </rc-dropdown-item>\n *      <rc-dropdown-separator />\n *      <rc-dropdown-item @click=\"performAction()\">\n *        Action 2\n *      </rc-dropdown-item>\n *    </template>\n *  </rc-dropdown>\n */\nimport { ref } from 'vue';\nimport { useClickOutside } from '@shell/composables/useClickOutside';\nimport { useDropdownContext } from '@components/RcDropdown/useDropdownContext';\n\ndefineProps<{\n  ariaLabel?: string\n}>();\n\nconst emit = defineEmits(['update:open']);\n\nconst {\n  isMenuOpen,\n  showMenu,\n  returnFocus,\n  setFocus,\n  provideDropdownContext,\n  registerDropdownCollection,\n  handleKeydown,\n} = useDropdownContext(emit);\n\nprovideDropdownContext();\n\nconst popperContainer = ref(null);\nconst dropdownTarget = ref(null);\n\nuseClickOutside(dropdownTarget, () => showMenu(false));\n\nconst applyShow = () => {\n  registerDropdownCollection(dropdownTarget.value);\n  setFocus();\n};\n\n</script>\n\n<template>\n  <v-dropdown\n    no-auto-focus\n    :triggers=\"[]\"\n    :shown=\"isMenuOpen\"\n    :auto-hide=\"false\"\n    :container=\"popperContainer\"\n    :placement=\"'bottom-end'\"\n    @apply-show=\"applyShow\"\n  >\n    <slot name=\"default\">\n      <!--Empty slot content Trigger-->\n    </slot>\n\n    <template #popper>\n      <div\n        ref=\"dropdownTarget\"\n        class=\"dropdownTarget\"\n        tabindex=\"-1\"\n        role=\"menu\"\n        aria-orientation=\"vertical\"\n        dropdown-menu-collection\n        :aria-label=\"ariaLabel || 'Dropdown Menu'\"\n        @keydown=\"handleKeydown\"\n        @keydown.down=\"setFocus()\"\n      >\n        <slot name=\"dropdownCollection\">\n          <!--Empty slot content-->\n        </slot>\n      </div>\n    </template>\n  </v-dropdown>\n  <div\n    ref=\"popperContainer\"\n    class=\"popperContainer\"\n    @keydown.tab=\"showMenu(false)\"\n    @keydown.escape=\"returnFocus\"\n  >\n    <!--Empty container for mounting popper content-->\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .popperContainer {\n    display: contents;\n    &:deep(.v-popper__popper) {\n\n      .v-popper__wrapper {\n        box-shadow: 0px 6px 18px 0px rgba(0, 0, 0, 0.25), 0px 4px 10px 0px rgba(0, 0, 0, 0.15);\n        border-radius: var(--border-radius-lg);\n\n        .v-popper__arrow-container {\n          display: none;\n        }\n\n        .v-popper__inner {\n          padding: 10px 0 10px 0;\n        }\n      }\n    }\n  }\n\n  .dropdownTarget {\n    &:focus-visible, &:focus {\n      outline: none;\n    }\n  }\n</style>\n"]}]}