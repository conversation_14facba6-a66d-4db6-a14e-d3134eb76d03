{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Config.vue?vue&type=template&id=40a6707b&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Config.vue", "mtime": 1754992537319}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Config.vue?vue&type=template&id=40a6707b&ts=true", "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Config.vue"], "names": ["canUpgrade", "hasUpgradesAvailable", "kubernetesVersion", "versionOptions", "mode", "loadingVersions", "$emit", "enableNetworkPolicy", "isNewOrUnprovisioned", "ebsCSIDriver", "customServiceRole", "serviceRoleOptions", "serviceRole", "eksRoles", "loadingIam", "secretsEncryption", "canReadKms", "kmsKey", "kmsOptions", "loadingKms", "t", "tags"], "mappings": "AAAA,OAAO,EAAE,gBAAgB,IAAI,iBAAiB,EAAE,SAAS,IAAI,UAAU,EAAE,WAAW,IAAI,YAAY,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,WAAW,IAAI,YAAY,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,eAAe,IAAI,gBAAgB,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,gBAAgB,IAAI,iBAAiB,EAAE,cAAc,IAAI,eAAe,EAAE,OAAO,IAAI,QAAQ,EAAE,MAAM,KAAK,CAAA;AAEnZ,MAAM,UAAU,GAAG;IC6RZ,KAAK,EAAE,EAAA,SAAA,EAAA,MAAA;QD3RC,aAAa,EAAC,QAAQ,EC4RC;IAChC,KAAK,EAAC,WAAW;CD3RtB,CAAA;AACD,MAAM,UAAU,GAAG,EC4RR,KAAK,EAAC,YAAY,EAAA,CAAA;AD3R7B,MAAM,UAAU,GAAG,ECySR,KAAK,EAAC,YAAY,EAAA,CAAA;ADxS7B,MAAM,UAAU,GAAG,ECiTR,KAAK,EAAC,YAAY,EAAA,CAAA;ADhT7B,MAAM,UAAU,GAAG,EC0TV,KAAK,EAAC,WAAW,EAAA,CAAA;ADzT1B,MAAM,UAAU,GAAG,EC0TR,KAAK,EAAC,YAAY,EAAA,CAAA;ADzT7B,MAAM,UAAU,GAAG,ECmUR,KAAK,EAAC,YAAY,EAAA,CAAA;ADlU7B,MAAM,UAAU,GAAG,ECmVV,KAAK,EAAC,WAAW,EAAA,CAAA;ADlV1B,MAAM,UAAU,GAAG,ECmVR,KAAK,EAAC,YAAY,EAAA,CAAA;ADlV7B,MAAM,WAAW,GAAG;IAClB,GAAG,EAAE,CAAC;IC8VF,KAAK,EAAC,WAAW;CD5VtB,CAAA;AACD,MAAM,WAAW,GAAG,EC8VZ,KAAK,EAAC,YAAY,EAAA,CAAA;AD7V1B,MAAM,WAAW,GAAG,ECwXX,KAAK,EAAC,kBAAkB,EAAA,CAAA;ADtXjC,MAAM,UAAU,MAAM,CAAC,IAAS,EAAC,MAAW,EAAC,MAAW,EAAC,MAAW,EAAC,KAAU,EAAC,QAAa;IAC3F,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,QAAQ,CAAE,CAAA;IACtD,MAAM,wBAAwB,GAAG,iBAAiB,CAAC,eAAe,CAAE,CAAA;IACpE,MAAM,mBAAmB,GAAG,iBAAiB,CAAC,UAAU,CAAE,CAAA;IAC1D,MAAM,qBAAqB,GAAG,iBAAiB,CAAC,YAAY,CAAE,CAAA;IAC9D,MAAM,uBAAuB,GAAG,iBAAiB,CAAC,cAAc,CAAE,CAAA;IAClE,MAAM,mBAAmB,GAAG,iBAAiB,CAAC,UAAU,CAAE,CAAA;IAC1D,MAAM,YAAY,GAAG,iBAAiB,CAAC,GAAG,CAAE,CAAA;IAE5C,OAAO,CAAC,UAAU,EAAE,ECwPpB,mBAAA,CAkIM,KAAA,EAAA,IAAA,EAAA;QDzXJ,CAAC,CCyPQA,IAAAA,CAAAA,UAAU,IAAIC,IAAAA,CAAAA,oBAAoB,CAAA;YDxPzC,CAAC,CAAC,CAAC,UAAU,EAAE,ECuPjB,YAAA,CAKE,iBAAA,EAAA;gBD3PI,GAAG,EAAE,CAAC;gBCwPV,KAAK,EAAC,MAAM;gBACZ,WAAS,EAAC,+BAA+B;gBACzC,aAAW,EAAC,uCAAuC;aDtPhD,CAAC,CAAC;YACL,CAAC,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;QACrC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;QCsP/C,mBAAA,CAqCM,KAAA,EArCN,UAqCM,EAAA;YAhCJ,mBAAA,CAaM,KAAA,EAbN,UAaM,EAAA;gBAZJ,YAAA,CAWE,wBAAA,EAAA;oBAVC,KAAK,EAAEC,IAAAA,CAAAA,iBAAiB;oBACxB,OAAO,EAAEC,IAAAA,CAAAA,cAAc;oBACxB,WAAS,EAAC,mBAAmB;oBAC5B,IAAI,EAAEC,IAAAA,CAAAA,IAAI;oBACV,OAAO,EAAEC,IAAAA,CAAAA,eAAe;oBACxB,QAAQ,EAAE,IAAI;oBACd,UAAU,EAAE,IAAI;oBACjB,aAAW,EAAC,sBAAsB;oBACjC,QAAQ,EAAA,CAAGL,IAAAA,CAAAA,UAAU,IAAIC,IAAAA,CAAAA,oBAAoB;oBAC7C,gBAAY,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEK,IAAAA,CAAAA,KAAK,CAAA,0BAAA,EAA6B,MAAM,CAAA,CAAA,CAAA;iBDxPxD,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;aAC7E,CAAC;YACF,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;YCyP7C,mBAAA,CAQM,KAAA,EARN,UAQM,EAAA;gBAPJ,YAAA,CAME,mBAAA,EAAA;oBALC,IAAI,EAAEF,IAAAA,CAAAA,IAAI;oBACX,WAAS,EAAC,+BAA+B;oBACxC,KAAK,EAAEG,IAAAA,CAAAA,mBAAmB;oBAC1B,QAAQ,EAAA,CAAGC,IAAAA,CAAAA,oBAAoB;oBAC/B,gBAAY,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEF,IAAAA,CAAAA,KAAK,CAAA,4BAAA,EAA+B,MAAM,CAAA,CAAA,CAAA;iBDvP1D,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;aACvD,CAAC;YACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;YCwP/C,mBAAA,CAQM,KAAA,EARN,UAQM,EAAA;gBAPJ,YAAA,CAME,mBAAA,EAAA;oBALC,IAAI,EAAEF,IAAAA,CAAAA,IAAI;oBACX,WAAS,EAAC,wBAAwB;oBACjC,KAAK,EAAEK,IAAAA,CAAAA,YAAY;oBACnB,QAAQ,EAAA,CAAGD,IAAAA,CAAAA,oBAAoB;oBAC/B,gBAAY,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEF,IAAAA,CAAAA,KAAK,CAAA,qBAAA,EAAwB,MAAM,CAAA,CAAA,CAAA;iBDtPnD,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;aACvD,CAAC;SACH,CAAC;QACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;QCuP/C,mBAAA,CA0BM,KAAA,EA1BN,UA0BM,EAAA;YAzBJ,mBAAA,CASM,KAAA,EATN,UASM,EAAA;gBARJ,YAAA,CAOE,qBAAA,EAAA;oBANQ,KAAK,EAAEI,IAAAA,CAAAA,iBAAiB;oBDrPhC,gBAAgB,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAW,EAAE,EAAE,CAAC,CAAC,CCqP/CA,IAAAA,CAAAA,iBAAiB,CAAA,GAAA,MAAA,CAAA,CAAA;oBAC/B,IAAI,EAAEN,IAAAA,CAAAA,IAAI;oBACV,OAAO,EAAEO,IAAAA,CAAAA,kBAAkB;oBAC5B,IAAI,EAAC,iBAAiB;oBACtB,aAAW,EAAC,wBAAwB;oBACnC,QAAQ,EAAEP,IAAAA,CAAAA,IAAI,KAAA,QAAA;iBDpPhB,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;aAClE,CAAC;YACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;YCqP/C,mBAAA,CAcM,KAAA,EAdN,UAcM,EAAA;gBDjQJ,CCqPQM,IAAAA,CAAAA,iBAAiB,CAAA;oBDpPvB,CAAC,CAAC,CAAC,UAAU,EAAE,ECmPjB,YAAA,CAYE,wBAAA,EAAA;wBD9PI,GAAG,EAAE,CAAC;wBCoPT,KAAK,EAAEE,IAAAA,CAAAA,WAAW;wBAClB,IAAI,EAAER,IAAAA,CAAAA,IAAI;wBACV,QAAQ,EAAEA,IAAAA,CAAAA,IAAI,KAAA,QAAA;wBACd,OAAO,EAAES,IAAAA,CAAAA,QAAQ;wBAClB,cAAY,EAAC,UAAU;wBACvB,YAAU,EAAC,QAAQ;wBACnB,WAAS,EAAC,uBAAuB;wBAChC,OAAO,EAAEC,IAAAA,CAAAA,UAAU;wBACpB,aAAW,EAAC,2BAA2B;wBACtC,gBAAY,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAER,IAAAA,CAAAA,KAAK,CAAA,oBAAA,EAAuB,MAAM,CAAC,QAAQ,CAAA,CAAA,CAAA;qBDlPvD,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;oBAC/E,CAAC,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;aACtC,CAAC;SACH,CAAC;QACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;QCmP/C,mBAAA,CAWM,KAAA,EAXN,UAWM,EAAA;YAVJ,mBAAA,CASM,KAAA,EATN,UASM,EAAA;gBARJ,YAAA,CAOE,mBAAA,EAAA;oBANC,KAAK,EAAES,IAAAA,CAAAA,iBAAiB;oBACxB,QAAQ,EAAEX,IAAAA,CAAAA,IAAI,KAAA,QAAA;oBACd,IAAI,EAAEA,IAAAA,CAAAA,IAAI;oBACX,WAAS,EAAC,0BAA0B;oBACpC,aAAW,EAAC,iCAAiC;oBAC5C,gBAAY,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEE,IAAAA,CAAAA,KAAK,CAAA,0BAAA,EAA6B,MAAM,CAAA,CAAA,CAAA;iBDjPxD,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;aACvD,CAAC;SACH,CAAC;QACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;QAC/C,CCkPQS,IAAAA,CAAAA,iBAAiB,CAAA;YDjPvB,CAAC,CAAC,CAAC,UAAU,EAAE,ECgPjB,mBAAA,CA8BM,KAAA,EA9BN,WA8BM,EAAA;gBA1BJ,mBAAA,CAyBM,KAAA,EAzBN,WAyBM,EAAA;oBD3QA,CCsPIC,IAAAA,CAAAA,UAAU,CAAA;wBDrPZ,CAAC,CAAC,CAAC,UAAU,EAAE,ECoPrB,YAAA,CAUE,wBAAA,EAAA;4BD7PQ,GAAG,EAAE,CAAC;4BCqPb,KAAK,EAAEC,IAAAA,CAAAA,MAAM;4BACb,IAAI,EAAEb,IAAAA,CAAAA,IAAI;4BACV,OAAO,EAAEc,IAAAA,CAAAA,UAAU;4BACnB,OAAO,EAAEC,IAAAA,CAAAA,UAAU;4BACnB,KAAK,EAAEC,IAAAA,CAAAA,CAAC,CAAA,8CAAA,CAAA;4BACT,aAAW,EAAC,kBAAkB;4BAC7B,QAAQ,EAAEhB,IAAAA,CAAAA,IAAI,KAAA,QAAA;4BACd,gBAAY,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEE,IAAAA,CAAAA,KAAK,CAAA,eAAA,EAAkB,MAAM,CAAA,CAAA,CAAA;yBDnPrC,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC;wBACxF,CAAC,CAAC,CAAC,UAAU,EAAE,ECqPnB,YAAA,CAQE,uBAAA,EAAA;4BD5PM,GAAG,EAAE,CAAC;4BCqPX,KAAK,EAAEW,IAAAA,CAAAA,MAAM;4BACb,IAAI,EAAEb,IAAAA,CAAAA,IAAI;4BACV,KAAK,EAAEgB,IAAAA,CAAAA,CAAC,CAAA,8CAAA,CAAA;4BACR,OAAO,EAAEA,IAAAA,CAAAA,CAAC,CAAA,6CAAA,CAAA;4BACX,aAAW,EAAC,eAAe;4BAC1B,QAAQ,EAAEhB,IAAAA,CAAAA,IAAI,KAAA,QAAA;4BACd,gBAAY,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEE,IAAAA,CAAAA,KAAK,CAAA,eAAA,EAAkB,MAAM,CAAA,CAAA,CAAA;yBDnPvC,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC;iBAChF,CAAC;aACH,CAAC,CAAC;YACL,CAAC,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;QACrC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;QCqP/C,mBAAA,CAYM,KAAA,EAZN,WAYM,EAAA;YAXJ,YAAA,CAUW,mBAAA,EAAA;gBATR,KAAK,EAAEe,IAAAA,CAAAA,IAAI;gBACX,IAAI,EAAEjB,IAAAA,CAAAA,IAAI;gBACV,QAAM,EAAE,IAAI;gBACZ,cAAY,EAAE,KAAK;gBACnB,gBAAY,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEE,IAAAA,CAAAA,KAAK,CAAA,aAAA,EAAgB,MAAM,CAAA,CAAA,CAAA;aDnP3C,EAAE;gBCqPU,KAAK,EAAA,QAAA,CACd,GAA6B,EAAA,CAAA;oBDpP7B,eAAe,CCoPf,mBAAA,CAA6B,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,GAAA,CAAA,gBAAA,CAAA,EAAA;wBDnP3B,CAAC,YAAY,ECmPN,gBAAgB,CAAA;qBDlPxB,CAAC;iBACH,CAAC;gBACF,CAAC,EAAE,CAAC,CAAC,YAAY;aAClB,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;SACrC,CAAC;KACH,CAAC,CAAC,CAAA;AACL,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Config.vue.tsx", "sourceRoot": "", "sourcesContent": ["import { resolveComponent as _resolveComponent, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createVNode as _createVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, createElement<PERSON>lock as _createElementBlock, resolveDirective as _resolveDirective, withDirectives as _withDirectives, withCtx as _withCtx } from \"vue\"\n\nconst _hoisted_1 = {\n  style: {'display':'flex',\n               'align-items':'center'},\n  class: \"row mb-10\"\n}\nconst _hoisted_2 = { class: \"col span-6\" }\nconst _hoisted_3 = { class: \"col span-3\" }\nconst _hoisted_4 = { class: \"col span-3\" }\nconst _hoisted_5 = { class: \"row mb-10\" }\nconst _hoisted_6 = { class: \"col span-6\" }\nconst _hoisted_7 = { class: \"col span-6\" }\nconst _hoisted_8 = { class: \"row mb-10\" }\nconst _hoisted_9 = { class: \"col span-6\" }\nconst _hoisted_10 = {\n  key: 1,\n  class: \"row mb-10\"\n}\nconst _hoisted_11 = { class: \"col span-6\" }\nconst _hoisted_12 = { class: \"col span-6 mt-20\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_Banner = _resolveComponent(\"Banner\")!\n  const _component_LabeledSelect = _resolveComponent(\"LabeledSelect\")!\n  const _component_Checkbox = _resolveComponent(\"Checkbox\")!\n  const _component_RadioGroup = _resolveComponent(\"RadioGroup\")!\n  const _component_LabeledInput = _resolveComponent(\"LabeledInput\")!\n  const _component_KeyValue = _resolveComponent(\"KeyValue\")!\n  const _directive_t = _resolveDirective(\"t\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", null, [\n    (!_ctx.canUpgrade && _ctx.hasUpgradesAvailable)\n      ? (_openBlock(), _createBlock(_component_Banner, {\n          key: 0,\n          color: \"info\",\n          \"label-key\": \"eks.version.upgradeDisallowed\",\n          \"data-testid\": \"eks-version-upgrade-disallowed-banner\"\n        }))\n      : _createCommentVNode(\"v-if\", true),\n    _cache[12] || (_cache[12] = _createTextVNode()),\n    _createElementVNode(\"div\", _hoisted_1, [\n      _createElementVNode(\"div\", _hoisted_2, [\n        _createVNode(_component_LabeledSelect, {\n          value: _ctx.kubernetesVersion,\n          options: _ctx.versionOptions,\n          \"label-key\": \"eks.version.label\",\n          mode: _ctx.mode,\n          loading: _ctx.loadingVersions,\n          taggable: true,\n          searchable: true,\n          \"data-testid\": \"eks-version-dropdown\",\n          disabled: !_ctx.canUpgrade && _ctx.hasUpgradesAvailable,\n          \"onUpdate:value\": _cache[0] || (_cache[0] = ($event: any) => (_ctx.$emit('update:kubernetesVersion', $event)))\n        }, null, 8 /* PROPS */, [\"value\", \"options\", \"mode\", \"loading\", \"disabled\"])\n      ]),\n      _cache[9] || (_cache[9] = _createTextVNode()),\n      _createElementVNode(\"div\", _hoisted_3, [\n        _createVNode(_component_Checkbox, {\n          mode: _ctx.mode,\n          \"label-key\": \"eks.enableNetworkPolicy.label\",\n          value: _ctx.enableNetworkPolicy,\n          disabled: !_ctx.isNewOrUnprovisioned,\n          \"onUpdate:value\": _cache[1] || (_cache[1] = ($event: any) => (_ctx.$emit('update:enableNetworkPolicy', $event)))\n        }, null, 8 /* PROPS */, [\"mode\", \"value\", \"disabled\"])\n      ]),\n      _cache[10] || (_cache[10] = _createTextVNode()),\n      _createElementVNode(\"div\", _hoisted_4, [\n        _createVNode(_component_Checkbox, {\n          mode: _ctx.mode,\n          \"label-key\": \"eks.ebsCSIDriver.label\",\n          value: _ctx.ebsCSIDriver,\n          disabled: !_ctx.isNewOrUnprovisioned,\n          \"onUpdate:value\": _cache[2] || (_cache[2] = ($event: any) => (_ctx.$emit('update:ebsCSIDriver', $event)))\n        }, null, 8 /* PROPS */, [\"mode\", \"value\", \"disabled\"])\n      ])\n    ]),\n    _cache[13] || (_cache[13] = _createTextVNode()),\n    _createElementVNode(\"div\", _hoisted_5, [\n      _createElementVNode(\"div\", _hoisted_6, [\n        _createVNode(_component_RadioGroup, {\n          value: _ctx.customServiceRole,\n          \"onUpdate:value\": _cache[3] || (_cache[3] = ($event: any) => ((_ctx.customServiceRole) = $event)),\n          mode: _ctx.mode,\n          options: _ctx.serviceRoleOptions,\n          name: \"serviceRoleMode\",\n          \"data-testid\": \"eks-service-role-radio\",\n          disabled: _ctx.mode!=='create'\n        }, null, 8 /* PROPS */, [\"value\", \"mode\", \"options\", \"disabled\"])\n      ]),\n      _cache[11] || (_cache[11] = _createTextVNode()),\n      _createElementVNode(\"div\", _hoisted_7, [\n        (_ctx.customServiceRole)\n          ? (_openBlock(), _createBlock(_component_LabeledSelect, {\n              key: 0,\n              value: _ctx.serviceRole,\n              mode: _ctx.mode,\n              disabled: _ctx.mode!=='create',\n              options: _ctx.eksRoles,\n              \"option-label\": \"RoleName\",\n              \"option-key\": \"RoleId\",\n              \"label-key\": \"eks.serviceRole.label\",\n              loading: _ctx.loadingIam,\n              \"data-testid\": \"eks-service-role-dropdown\",\n              \"onUpdate:value\": _cache[4] || (_cache[4] = ($event: any) => (_ctx.$emit('update:serviceRole', $event.RoleName)))\n            }, null, 8 /* PROPS */, [\"value\", \"mode\", \"disabled\", \"options\", \"loading\"]))\n          : _createCommentVNode(\"v-if\", true)\n      ])\n    ]),\n    _cache[14] || (_cache[14] = _createTextVNode()),\n    _createElementVNode(\"div\", _hoisted_8, [\n      _createElementVNode(\"div\", _hoisted_9, [\n        _createVNode(_component_Checkbox, {\n          value: _ctx.secretsEncryption,\n          disabled: _ctx.mode!=='create',\n          mode: _ctx.mode,\n          \"label-key\": \"eks.encryptSecrets.label\",\n          \"data-testid\": \"eks-secrets-encryption-checkbox\",\n          \"onUpdate:value\": _cache[5] || (_cache[5] = ($event: any) => (_ctx.$emit('update:secretsEncryption', $event)))\n        }, null, 8 /* PROPS */, [\"value\", \"disabled\", \"mode\"])\n      ])\n    ]),\n    _cache[15] || (_cache[15] = _createTextVNode()),\n    (_ctx.secretsEncryption)\n      ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [\n          _createElementVNode(\"div\", _hoisted_11, [\n            (_ctx.canReadKms)\n              ? (_openBlock(), _createBlock(_component_LabeledSelect, {\n                  key: 0,\n                  value: _ctx.kmsKey,\n                  mode: _ctx.mode,\n                  options: _ctx.kmsOptions,\n                  loading: _ctx.loadingKms,\n                  label: _ctx.t('cluster.machineConfig.amazonEc2.kmsKey.label'),\n                  \"data-testid\": \"eks-kms-dropdown\",\n                  disabled: _ctx.mode!=='create',\n                  \"onUpdate:value\": _cache[6] || (_cache[6] = ($event: any) => (_ctx.$emit('update:kmsKey', $event)))\n                }, null, 8 /* PROPS */, [\"value\", \"mode\", \"options\", \"loading\", \"label\", \"disabled\"]))\n              : (_openBlock(), _createBlock(_component_LabeledInput, {\n                  key: 1,\n                  value: _ctx.kmsKey,\n                  mode: _ctx.mode,\n                  label: _ctx.t('cluster.machineConfig.amazonEc2.kmsKey.label'),\n                  tooltip: _ctx.t('cluster.machineConfig.amazonEc2.kmsKey.text'),\n                  \"data-testid\": \"eks-kms-input\",\n                  disabled: _ctx.mode!=='create',\n                  \"onUpdate:value\": _cache[7] || (_cache[7] = ($event: any) => (_ctx.$emit('update:kmsKey', $event)))\n                }, null, 8 /* PROPS */, [\"value\", \"mode\", \"label\", \"tooltip\", \"disabled\"]))\n          ])\n        ]))\n      : _createCommentVNode(\"v-if\", true),\n    _cache[16] || (_cache[16] = _createTextVNode()),\n    _createElementVNode(\"div\", _hoisted_12, [\n      _createVNode(_component_KeyValue, {\n        value: _ctx.tags,\n        mode: _ctx.mode,\n        \"as-map\": true,\n        \"read-allowed\": false,\n        \"onUpdate:value\": _cache[8] || (_cache[8] = ($event: any) => (_ctx.$emit('update:tags', $event)))\n      }, {\n        title: _withCtx(() => [\n          _withDirectives(_createElementVNode(\"h3\", null, null, 512 /* NEED_PATCH */), [\n            [_directive_t, 'eks.tags.label']\n          ])\n        ]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"value\", \"mode\"])\n    ])\n  ]))\n}", "<script lang=\"ts\">\nimport { defineComponent, PropType } from 'vue';\nimport { EKSConfig, AWS } from '@/types';\nimport { _EDIT, _VIEW, _CREATE } from '@shell/config/query-params';\nimport semver from 'semver';\nimport { Store, mapGetters } from 'vuex';\nimport { sortable } from '@shell/utils/version';\nimport { sortBy } from '@shell/utils/sort';\n\nimport { MANAGEMENT } from '@shell/config/types';\nimport { SETTING } from '@shell/config/settings';\nimport RadioGroup from '@components/Form/Radio/RadioGroup.vue';\nimport Banner from '@components/Banner/Banner.vue';\n\nimport LabeledSelect from '@shell/components/form/LabeledSelect.vue';\nimport KeyValue from '@shell/components/form/KeyValue.vue';\nimport Checkbox from '@components/Form/Checkbox/Checkbox.vue';\nimport LabeledInput from '@components/Form/LabeledInput/LabeledInput.vue';\nimport eksVersions from '../../assets/data/eks-versions';\n\nexport default defineComponent({\n  name: 'EKSConfig',\n\n  emits: ['update:kmsKey', 'update:serviceRole', 'update:kubernetesVersion', 'update:enableNetworkPolicy', 'update:ebsCSIDriver', 'update:serviceRole', 'update:secretsEncryption', 'update:kmsKey', 'update:tags'],\n\n  components: {\n    LabeledSelect,\n    RadioGroup,\n    KeyValue,\n    Checkbox,\n    LabeledInput,\n    Banner\n  },\n\n  props: {\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n\n    isNewOrUnprovisioned: {\n      type:    Boolean,\n      default: true\n    },\n\n    loadingIam: {\n      type:    Boolean,\n      default: false\n    },\n\n    eksRoles: {\n      type:    Array,\n      default: () => []\n    },\n\n    tags: {\n      type:    Object,\n      default: () => {}\n    },\n\n    kmsKey: {\n      type:    String,\n      default: ''\n    },\n\n    secretsEncryption: {\n      type:    Boolean,\n      default: false\n    },\n\n    serviceRole: {\n      type:    String,\n      default: ''\n    },\n\n    kubernetesVersion: {\n      type:    String,\n      default: ''\n    },\n\n    enableNetworkPolicy: {\n      type:    Boolean,\n      default: false\n    },\n\n    ebsCSIDriver: {\n      type:    Boolean,\n      default: false\n    },\n\n    config: {\n      type:     Object as PropType<EKSConfig>,\n      required: true\n    },\n\n    originalVersion: {\n      type:    String,\n      default: ''\n    }\n  },\n\n  data() {\n    const store = this.$store as Store<any>;\n    // This setting is used by RKE1 AKS GKE and EKS - rke2/k3s have a different mechanism for fetching supported versions\n    const supportedVersionRange = store.getters['management/byId'](MANAGEMENT.SETTING, SETTING.UI_SUPPORTED_K8S_VERSIONS)?.value;\n    const t = store.getters['i18n/t'];\n\n    return {\n      kmsKeys:               [] as AWS.KmsKey[],\n      canReadKms:            false,\n      supportedVersionRange,\n      customServiceRole:     !!this.serviceRole && !!this.serviceRole.length,\n      loadingVersions:       false,\n      loadingKms:            false,\n      allKubernetesVersions: eksVersions as string[],\n      serviceRoleOptions:    [{ value: false, label: t('eks.serviceRole.options.standard') }, { value: true, label: t('eks.serviceRole.options.custom') }],\n\n    };\n  },\n\n  watch: {\n    'config.region': {\n      handler() {\n        if (this.mode !== _VIEW) {\n          this.fetchKubernetesVersions();\n          this.fetchKMSKeys();\n        }\n      },\n      immediate: true\n    },\n\n    'config.amazonCredentialSecret': {\n      handler() {\n        if (this.mode !== _VIEW) {\n          this.fetchKubernetesVersions();\n          this.fetchKMSKeys();\n        }\n      },\n      immediate: true\n    },\n\n    'secretsEncryption'(neu) {\n      if (!neu) {\n        this.$emit('update:kmsKey', '');\n      }\n    },\n\n    'customServiceRole'(neu) {\n      if (!neu) {\n        this.$emit('update:serviceRole', '');\n      }\n    },\n\n    versionOptions: {\n      handler(neu) {\n        if (neu && neu.length && !this.kubernetesVersion) {\n          this.$emit('update:kubernetesVersion', neu[0].value);\n        }\n      },\n      immediate: true\n    },\n\n  },\n\n  computed: {\n    ...mapGetters({ t: 'i18n/t' }),\n\n    // the control plane k8s version can't be more than one minor version ahead of any node pools\n    // verify that all nodepools are on the same version as the control plane before showing upgrade optiopns\n    canUpgrade(): boolean {\n      if (this.mode === _CREATE) {\n        return true;\n      }\n      const nodeGroups = this.config?.nodeGroups || [];\n\n      const needsUpgrade = nodeGroups.filter((group) => semver.gt(semver.coerce(this.originalVersion), semver.coerce(group.version)) || group._isUpgrading);\n\n      return !needsUpgrade.length;\n    },\n\n    hasUpgradesAvailable() {\n      return this.versionOptions.filter((opt) => !opt.disabled).length > 1;\n    },\n\n    versionOptions(): {value: string, label: string, sort?: string, disabled?:boolean}[] {\n      return this.allKubernetesVersions.reduce((versions, v: string) => {\n        const coerced = semver.coerce(v);\n\n        if (this.supportedVersionRange && !semver.satisfies(coerced, this.supportedVersionRange)) {\n          return versions;\n        }\n        if (!this.originalVersion) {\n          versions.push({ value: v, label: v });\n        } else if (semver.lte(semver.coerce(this.originalVersion), coerced)) {\n          const withinOneMinor = semver.inc(semver.coerce(this.originalVersion), 'minor');\n\n          if (semver.gt(coerced, withinOneMinor)) {\n            versions.push({\n              value: v, label: `${ v } ${ this.t('eks.version.upgradeWarning') }`, disabled: true\n            });\n          } else {\n            versions.push({ value: v, label: v });\n          }\n        }\n\n        // Generate sort field for each version\n        versions.forEach((v) => {\n          v.sort = sortable(v.value);\n        });\n\n        return sortBy(versions, 'sort', true);\n      }, [] as {value: string, label: string, sort?: string, disabled?:boolean}[]);\n    },\n\n    kmsOptions(): string[] {\n      return (this.kmsKeys || []).map((k) => k.KeyArn);\n    }\n  },\n\n  methods: {\n    // there is no api for fetching eks versions\n    // fetch addons and look at which versions they support\n    // this assumes that all k8s versions are compatible with at least one addon\n    async fetchKubernetesVersions() {\n      if (!this.config.region || !this.config.amazonCredentialSecret) {\n        return;\n      }\n      this.loadingVersions = true;\n      try {\n        const eksClient = await this.$store.dispatch('aws/eks', { region: this.config.region, cloudCredentialId: this.config.amazonCredentialSecret });\n        const addons = await this.$store.dispatch('aws/depaginateList', { client: eksClient, cmd: 'describeAddonVersions' });\n\n        if (!addons) {\n          return;\n        }\n        this.allKubernetesVersions = addons.reduce((versions: string[], addon: AWS.EKSAddon) => {\n          (addon?.addonVersions || []).forEach((addonVersion) => {\n            (addonVersion?.compatibilities || []).forEach((c) => {\n              if (!versions.includes(c.clusterVersion)) {\n                versions.push(c.clusterVersion);\n              }\n            });\n          });\n\n          return versions;\n        }, []);\n      } catch (err) {\n        // if the user doesn't have permission to describe addon versions swallow the error and use a fallback list of eks versions\n      }\n\n      this.loadingVersions = false;\n    },\n\n    async fetchKMSKeys() {\n      const { region, amazonCredentialSecret } = this.config;\n\n      if (!region || !amazonCredentialSecret) {\n        return;\n      }\n      this.loadingKms = true;\n      const store = this.$store as Store<any>;\n      const kmsClient = await store.dispatch('aws/kms', { region, cloudCredentialId: amazonCredentialSecret });\n\n      try {\n        this.kmsKeys = await this.$store.dispatch('aws/depaginateList', { client: kmsClient, cmd: 'listKeys' });\n\n        this.canReadKms = true;\n      } catch (e) {\n        this.canReadKms = false;\n      }\n      this.loadingKms = false;\n    },\n\n  }\n});\n\n</script>\n\n<template>\n  <div>\n    <Banner\n      v-if=\"!canUpgrade && hasUpgradesAvailable\"\n      color=\"info\"\n      label-key=\"eks.version.upgradeDisallowed\"\n      data-testid=\"eks-version-upgrade-disallowed-banner\"\n    />\n    <div\n      :style=\"{'display':'flex',\n               'align-items':'center'}\"\n      class=\"row mb-10\"\n    >\n      <div class=\"col span-6\">\n        <LabeledSelect\n          :value=\"kubernetesVersion\"\n          :options=\"versionOptions\"\n          label-key=\"eks.version.label\"\n          :mode=\"mode\"\n          :loading=\"loadingVersions\"\n          :taggable=\"true\"\n          :searchable=\"true\"\n          data-testid=\"eks-version-dropdown\"\n          :disabled=\"!canUpgrade && hasUpgradesAvailable\"\n          @update:value=\"$emit('update:kubernetesVersion', $event)\"\n        />\n      </div>\n      <div class=\"col span-3\">\n        <Checkbox\n          :mode=\"mode\"\n          label-key=\"eks.enableNetworkPolicy.label\"\n          :value=\"enableNetworkPolicy\"\n          :disabled=\"!isNewOrUnprovisioned\"\n          @update:value=\"$emit('update:enableNetworkPolicy', $event)\"\n        />\n      </div>\n      <div class=\"col span-3\">\n        <Checkbox\n          :mode=\"mode\"\n          label-key=\"eks.ebsCSIDriver.label\"\n          :value=\"ebsCSIDriver\"\n          :disabled=\"!isNewOrUnprovisioned\"\n          @update:value=\"$emit('update:ebsCSIDriver', $event)\"\n        />\n      </div>\n    </div>\n    <div class=\"row mb-10\">\n      <div class=\"col span-6\">\n        <RadioGroup\n          v-model:value=\"customServiceRole\"\n          :mode=\"mode\"\n          :options=\"serviceRoleOptions\"\n          name=\"serviceRoleMode\"\n          data-testid=\"eks-service-role-radio\"\n          :disabled=\"mode!=='create'\"\n        />\n      </div>\n      <div class=\"col span-6\">\n        <LabeledSelect\n          v-if=\"customServiceRole\"\n          :value=\"serviceRole\"\n          :mode=\"mode\"\n          :disabled=\"mode!=='create'\"\n          :options=\"eksRoles\"\n          option-label=\"RoleName\"\n          option-key=\"RoleId\"\n          label-key=\"eks.serviceRole.label\"\n          :loading=\"loadingIam\"\n          data-testid=\"eks-service-role-dropdown\"\n          @update:value=\"$emit('update:serviceRole', $event.RoleName)\"\n        />\n      </div>\n    </div>\n\n    <div class=\"row mb-10\">\n      <div class=\"col span-6\">\n        <Checkbox\n          :value=\"secretsEncryption\"\n          :disabled=\"mode!=='create'\"\n          :mode=\"mode\"\n          label-key=\"eks.encryptSecrets.label\"\n          data-testid=\"eks-secrets-encryption-checkbox\"\n          @update:value=\"$emit('update:secretsEncryption', $event)\"\n        />\n      </div>\n    </div>\n    <div\n      v-if=\"secretsEncryption\"\n      class=\"row mb-10\"\n    >\n      <div\n        class=\"col span-6\"\n      >\n        <LabeledSelect\n          v-if=\"canReadKms\"\n          :value=\"kmsKey\"\n          :mode=\"mode\"\n          :options=\"kmsOptions\"\n          :loading=\"loadingKms\"\n          :label=\"t('cluster.machineConfig.amazonEc2.kmsKey.label')\"\n          data-testid=\"eks-kms-dropdown\"\n          :disabled=\"mode!=='create'\"\n          @update:value=\"$emit('update:kmsKey', $event)\"\n        />\n        <template v-else>\n          <LabeledInput\n            :value=\"kmsKey\"\n            :mode=\"mode\"\n            :label=\"t('cluster.machineConfig.amazonEc2.kmsKey.label')\"\n            :tooltip=\"t('cluster.machineConfig.amazonEc2.kmsKey.text')\"\n            data-testid=\"eks-kms-input\"\n            :disabled=\"mode!=='create'\"\n            @update:value=\"$emit('update:kmsKey', $event)\"\n          />\n        </template>\n      </div>\n    </div>\n\n    <div class=\"col span-6 mt-20\">\n      <KeyValue\n        :value=\"tags\"\n        :mode=\"mode\"\n        :as-map=\"true\"\n        :read-allowed=\"false\"\n        @update:value=\"$emit('update:tags', $event)\"\n      >\n        <template #title>\n          <h3 v-t=\"'eks.tags.label'\" />\n        </template>\n      </KeyValue>\n    </div>\n  </div>\n</template>\n"]}]}