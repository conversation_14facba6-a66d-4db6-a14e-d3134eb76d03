{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Networking.vue?vue&type=template&id=4157f2ad&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Networking.vue", "mtime": 1754992537319}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Networking.vue?vue&type=template&id=4157f2ad&ts=true", "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Networking.vue"], "names": ["publicAccess", "mode", "$emit", "privateAccess", "publicAccessSources", "t", "isNew", "chooseSubnet", "displaySubnets", "vpcOptions", "loadingVpcs", "rules", "securityGroupOptions", "securityGroups", "loadingSecurityGroups"], "mappings": "AAAA,OAAO,EAAE,gBAAgB,IAAI,iBAAiB,EAAE,WAAW,IAAI,YAAY,EAAE,eAAe,IAAI,gBAAgB,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,eAAe,IAAI,gBAAgB,EAAE,OAAO,IAAI,QAAQ,EAAE,SAAS,IAAI,UAAU,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,cAAc,IAAI,eAAe,EAAE,MAAM,KAAK,CAAA;AAEpX,MAAM,UAAU,GAAG,ECqQV,KAAK,EAAC,WAAW,EAAA,CAAA;ADpQ1B,MAAM,UAAU,GAAG,ECqQR,KAAK,EAAC,YAAY,EAAA,CAAA;ADpQ7B,MAAM,UAAU,GAAG,ECmRV,KAAK,EAAC,WAAW,EAAA,CAAA;ADlR1B,MAAM,UAAU,GAAG,ECmRR,KAAK,EAAC,YAAY,EAAA,CAAA;ADlR7B,MAAM,UAAU,GAAG,ECkSV,KAAK,EAAC,iBAAiB,EAAA,CAAA;ADjShC,MAAM,UAAU,GAAG;IACjB,GAAG,EAAE,CAAC;ICmSA,KAAK,EAAC,YAAY;CDjSzB,CAAA;AACD,MAAM,UAAU,GAAG,EC6Sb,KAAK,EAAC,WAAW,EAAA,CAAA;AD5SvB,MAAM,UAAU,GAAG;IACjB,GAAG,EAAE,CAAC;IC+SA,KAAK,EAAC,YAAY;CD7SzB,CAAA;AACD,MAAM,UAAU,GAAG;IACjB,GAAG,EAAE,CAAC;ICgUA,KAAK,EAAC,YAAY;CD9TzB,CAAA;AAED,MAAM,UAAU,MAAM,CAAC,IAAS,EAAC,MAAW,EAAC,MAAW,EAAC,MAAW,EAAC,KAAU,EAAC,QAAa;IAC3F,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,QAAQ,CAAE,CAAA;IACtD,MAAM,mBAAmB,GAAG,iBAAiB,CAAC,UAAU,CAAE,CAAA;IAC1D,MAAM,oBAAoB,GAAG,iBAAiB,CAAC,WAAW,CAAE,CAAA;IAC5D,MAAM,qBAAqB,GAAG,iBAAiB,CAAC,YAAY,CAAE,CAAA;IAC9D,MAAM,wBAAwB,GAAG,iBAAiB,CAAC,eAAe,CAAE,CAAA;IAEpE,OAAO,CAAC,UAAU,EAAE,ECsOpB,mBAAA,CA+FM,KAAA,EAAA,IAAA,EAAA;QA9FJ,YAAA,CAGE,iBAAA,EAAA;YAFA,KAAK,EAAC,MAAM;YACZ,WAAS,EAAC,0BAA0B;SDrOrC,CAAC;QACF,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;QCsO7C,mBAAA,CAeM,KAAA,EAfN,UAeM,EAAA;YAdJ,mBAAA,CAaM,KAAA,EAbN,UAaM,EAAA;gBAZJ,YAAA,CAKE,mBAAA,EAAA;oBAJC,KAAK,EAAEA,IAAAA,CAAAA,YAAY;oBACnB,IAAI,EAAEC,IAAAA,CAAAA,IAAI;oBACX,WAAS,EAAC,wBAAwB;oBACjC,gBAAY,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEC,IAAAA,CAAAA,KAAK,CAAA,qBAAA,EAAwB,MAAM,CAAA,CAAA,CAAA;iBDpOnD,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBAC1C,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;gBCqO7C,YAAA,CAKE,mBAAA,EAAA;oBAJC,KAAK,EAAEC,IAAAA,CAAAA,aAAa;oBACpB,IAAI,EAAEF,IAAAA,CAAAA,IAAI;oBACX,WAAS,EAAC,yBAAyB;oBAClC,gBAAY,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEC,IAAAA,CAAAA,KAAK,CAAA,sBAAA,EAAyB,MAAM,CAAA,CAAA,CAAA;iBDnOpD,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;aAC3C,CAAC;SACH,CAAC;QACF,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;QCoO7C,mBAAA,CAgBM,KAAA,EAhBN,UAgBM,EAAA;YAfJ,mBAAA,CAcM,KAAA,EAdN,UAcM,EAAA;gBAbJ,YAAA,CAYY,oBAAA,EAAA;oBAXT,KAAK,EAAEE,IAAAA,CAAAA,mBAAmB;oBAC1B,IAAI,EAAEH,IAAAA,CAAAA,IAAI;oBACV,QAAQ,EAAA,CAAGD,IAAAA,CAAAA,YAAY;oBACvB,aAAW,EAAEA,IAAAA,CAAAA,YAAY;oBACzB,WAAS,EAAEK,IAAAA,CAAAA,CAAC,CAAA,qCAAA,CAAA;oBACb,aAAW,EAAC,2BAA2B;oBACtC,gBAAY,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEH,IAAAA,CAAAA,KAAK,CAAA,4BAAA,EAA+B,MAAM,CAAA,CAAA,CAAA;iBDlO1D,EAAE;oBCoOU,KAAK,EAAA,QAAA,CACd,GAAwC,EAAA,CAAA;wBDnOxC,gBAAgB,CAAC,gBAAgB,CCmO9BG,IAAAA,CAAAA,CAAC,CAAA,+BAAA,CAAA,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;qBDlOL,CAAC;oBACF,CAAC,EAAE,CAAC,CAAC,YAAY;iBAClB,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;aAC7E,CAAC;SACH,CAAC;QACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;QCkO/C,mBAAA,CAcM,KAAA,EAdN,UAcM,EAAA;YD9OJ,CCkOQC,IAAAA,CAAAA,KAAK,CAAA;gBDjOX,CAAC,CAAC,CAAC,UAAU,EAAE,ECgOjB,mBAAA,CAYM,KAAA,EAZN,UAYM,EAAA;oBARJ,YAAA,CAOE,qBAAA,EAAA;wBANQ,KAAK,EAAEC,IAAAA,CAAAA,YAAY;wBDlOvB,gBAAgB,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAW,EAAE,EAAE,CAAC,CAAC,CCkOnDA,IAAAA,CAAAA,YAAY,CAAA,GAAA,MAAA,CAAA,CAAA;wBAC3B,IAAI,EAAC,aAAa;wBACjB,IAAI,EAAEN,IAAAA,CAAAA,IAAI;wBACV,OAAO,EAAA,CAAA,EAAA,KAAA,EAAWI,IAAAA,CAAAA,CAAC,CAAA,qBAAA,CAAA,EAAA,KAAA,EAAA,KAAA,EAAA,EAAA,EAAA,KAAA,EAA+CA,IAAAA,CAAAA,CAAC,CAAA,uBAAA,CAAA,EAAA,KAAA,EAAA,IAAA,EAAA,CAAA;wBACpE,WAAS,EAAC,mBAAmB;wBAC5B,QAAQ,EAAA,CAAGC,IAAAA,CAAAA,KAAK;qBDjOd,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;iBAClE,CAAC,CAAC;gBACL,CAAC,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;SACtC,CAAC;QACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;QCiO/C,mBAAA,CAyCM,KAAA,EAzCN,UAyCM,EAAA;YDxQJ,CCmOQC,IAAAA,CAAAA,YAAY,IAAA,CAAKD,IAAAA,CAAAA,KAAK,CAAA;gBDlO5B,CAAC,CAAC,CAAC,UAAU,EAAE,ECiOjB,mBAAA,CAoBM,KAAA,EApBN,UAoBM,EAAA;oBAhBJ,YAAA,CAegB,wBAAA,EAAA;wBAdN,KAAK,EAAEE,IAAAA,CAAAA,cAAc;wBDnOzB,gBAAgB,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAW,EAAE,EAAE,CAAC,CAAC,CCmOnDA,IAAAA,CAAAA,cAAc,CAAA,GAAA,MAAA,CAAA,CAAA;wBAC5B,QAAQ,EAAA,CAAGF,IAAAA,CAAAA,KAAK;wBAChB,IAAI,EAAEL,IAAAA,CAAAA,IAAI;wBACX,WAAS,EAAC,qBAAqB;wBAC9B,OAAO,EAAEQ,IAAAA,CAAAA,UAAU;wBACnB,OAAO,EAAEC,IAAAA,CAAAA,WAAW;wBACrB,YAAU,EAAC,KAAK;wBACf,QAAQ,EAAE,IAAI;wBACd,KAAK,EAAEC,IAAAA,CAAAA,KAAK,IAAIA,IAAAA,CAAAA,KAAK,CAAC,OAAO;wBAC9B,aAAW,EAAC,sBAAsB;qBDlO/B,EAAE;wBCoOM,MAAM,EAAA,QAAA,CACf,CAAoE,MAD7C,EAAA,EAAA,CAAA;4BACvB,mBAAA,CAAoE,MAAA,EAAA;gCAA7D,KAAK,EAAA,eAAA,CAAA,EAAA,OAAA,EAAY,MAAM,CAAC,SAAS,EAAA,CAAA;6BDjOnC,EAAE,gBAAgB,CCiOuB,MAAM,CAAC,KAAK,CAAA,EAAA,CAAA,CAAA,iBAAA,CAAA;yBDhOvD,CAAC;wBACF,CAAC,EAAE,CAAC,CAAC,YAAY;qBAClB,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;iBAChF,CAAC,CAAC;gBACL,CAAC,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;YACrC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;YAC7C,CC+NQJ,IAAAA,CAAAA,YAAY,CAAA;gBD9NlB,CAAC,CAAC,CAAC,UAAU,EAAE,EC6NjB,mBAAA,CAgBM,KAAA,EAhBN,UAgBM,EAAA;oBAZJ,YAAA,CAWE,wBAAA,EAAA;wBAVC,IAAI,EAAEN,IAAAA,CAAAA,IAAI;wBACV,QAAQ,EAAA,CAAGK,IAAAA,CAAAA,KAAK;wBACjB,WAAS,EAAC,0BAA0B;wBACnC,OAAO,EAAED,IAAAA,CAAAA,CAAC,CAAA,4BAAA,CAAA;wBACV,OAAO,EAAEO,IAAAA,CAAAA,oBAAoB;wBAC7B,QAAQ,EAAE,IAAI;wBACd,KAAK,EAAEC,IAAAA,CAAAA,cAAc;wBACrB,OAAO,EAAEC,IAAAA,CAAAA,qBAAqB;wBAC/B,aAAW,EAAC,8BAA8B;wBACzC,gBAAY,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEZ,IAAAA,CAAAA,KAAK,CAAA,uBAAA,EAA0B,MAAM,CAAA,CAAA,CAAA;qBD/NjD,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;iBACxF,CAAC,CAAC;gBACL,CAAC,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;SACtC,CAAC;KACH,CAAC,CAAC,CAAA;AACL,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Networking.vue.tsx", "sourceRoot": "", "sourcesContent": ["import { resolveComponent as _resolveComponent, createVNode as _createVNode, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, withCtx as _withCtx, openBlock as _openBlock, createElement<PERSON>lock as _createElementBlock, createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass } from \"vue\"\n\nconst _hoisted_1 = { class: \"row mb-10\" }\nconst _hoisted_2 = { class: \"col span-6\" }\nconst _hoisted_3 = { class: \"row mb-10\" }\nconst _hoisted_4 = { class: \"col span-6\" }\nconst _hoisted_5 = { class: \"row mb-10 mt-20\" }\nconst _hoisted_6 = {\n  key: 0,\n  class: \"col span-6\"\n}\nconst _hoisted_7 = { class: \"row mb-10\" }\nconst _hoisted_8 = {\n  key: 0,\n  class: \"col span-6\"\n}\nconst _hoisted_9 = {\n  key: 1,\n  class: \"col span-6\"\n}\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_Banner = _resolveComponent(\"Banner\")!\n  const _component_Checkbox = _resolveComponent(\"Checkbox\")!\n  const _component_ArrayList = _resolveComponent(\"ArrayList\")!\n  const _component_RadioGroup = _resolveComponent(\"RadioGroup\")!\n  const _component_LabeledSelect = _resolveComponent(\"LabeledSelect\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", null, [\n    _createVNode(_component_Banner, {\n      color: \"info\",\n      \"label-key\": \"eks.publicAccess.tooltip\"\n    }),\n    _cache[8] || (_cache[8] = _createTextVNode()),\n    _createElementVNode(\"div\", _hoisted_1, [\n      _createElementVNode(\"div\", _hoisted_2, [\n        _createVNode(_component_Checkbox, {\n          value: _ctx.publicAccess,\n          mode: _ctx.mode,\n          \"label-key\": \"eks.publicAccess.label\",\n          \"onUpdate:value\": _cache[0] || (_cache[0] = ($event: any) => (_ctx.$emit('update:publicAccess', $event)))\n        }, null, 8 /* PROPS */, [\"value\", \"mode\"]),\n        _cache[6] || (_cache[6] = _createTextVNode()),\n        _createVNode(_component_Checkbox, {\n          value: _ctx.privateAccess,\n          mode: _ctx.mode,\n          \"label-key\": \"eks.privateAccess.label\",\n          \"onUpdate:value\": _cache[1] || (_cache[1] = ($event: any) => (_ctx.$emit('update:privateAccess', $event)))\n        }, null, 8 /* PROPS */, [\"value\", \"mode\"])\n      ])\n    ]),\n    _cache[9] || (_cache[9] = _createTextVNode()),\n    _createElementVNode(\"div\", _hoisted_3, [\n      _createElementVNode(\"div\", _hoisted_4, [\n        _createVNode(_component_ArrayList, {\n          value: _ctx.publicAccessSources,\n          mode: _ctx.mode,\n          disabled: !_ctx.publicAccess,\n          \"add-allowed\": _ctx.publicAccess,\n          \"add-label\": _ctx.t('eks.publicAccessSources.addEndpoint'),\n          \"data-testid\": \"eks-public-access-sources\",\n          \"onUpdate:value\": _cache[2] || (_cache[2] = ($event: any) => (_ctx.$emit('update:publicAccessSources', $event)))\n        }, {\n          title: _withCtx(() => [\n            _createTextVNode(_toDisplayString(_ctx.t('eks.publicAccessSources.label')), 1 /* TEXT */)\n          ]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"value\", \"mode\", \"disabled\", \"add-allowed\", \"add-label\"])\n      ])\n    ]),\n    _cache[10] || (_cache[10] = _createTextVNode()),\n    _createElementVNode(\"div\", _hoisted_5, [\n      (_ctx.isNew)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [\n            _createVNode(_component_RadioGroup, {\n              value: _ctx.chooseSubnet,\n              \"onUpdate:value\": _cache[3] || (_cache[3] = ($event: any) => ((_ctx.chooseSubnet) = $event)),\n              name: \"subnet-mode\",\n              mode: _ctx.mode,\n              options: [{label: _ctx.t('eks.subnets.default'), value: false},{label: _ctx.t('eks.subnets.useCustom'), value: true}],\n              \"label-key\": \"eks.subnets.title\",\n              disabled: !_ctx.isNew\n            }, null, 8 /* PROPS */, [\"value\", \"mode\", \"options\", \"disabled\"])\n          ]))\n        : _createCommentVNode(\"v-if\", true)\n    ]),\n    _cache[11] || (_cache[11] = _createTextVNode()),\n    _createElementVNode(\"div\", _hoisted_7, [\n      (_ctx.chooseSubnet || !_ctx.isNew)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [\n            _createVNode(_component_LabeledSelect, {\n              value: _ctx.displaySubnets,\n              \"onUpdate:value\": _cache[4] || (_cache[4] = ($event: any) => ((_ctx.displaySubnets) = $event)),\n              disabled: !_ctx.isNew,\n              mode: _ctx.mode,\n              \"label-key\": \"eks.vpcSubnet.label\",\n              options: _ctx.vpcOptions,\n              loading: _ctx.loadingVpcs,\n              \"option-key\": \"key\",\n              multiple: true,\n              rules: _ctx.rules && _ctx.rules.subnets,\n              \"data-testid\": \"eks-subnets-dropdown\"\n            }, {\n              option: _withCtx((option) => [\n                _createElementVNode(\"span\", {\n                  class: _normalizeClass({'pl-30': option._isSubnet})\n                }, _toDisplayString(option.label), 3 /* TEXT, CLASS */)\n              ]),\n              _: 1 /* STABLE */\n            }, 8 /* PROPS */, [\"value\", \"disabled\", \"mode\", \"options\", \"loading\", \"rules\"])\n          ]))\n        : _createCommentVNode(\"v-if\", true),\n      _cache[7] || (_cache[7] = _createTextVNode()),\n      (_ctx.chooseSubnet)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [\n            _createVNode(_component_LabeledSelect, {\n              mode: _ctx.mode,\n              disabled: !_ctx.isNew,\n              \"label-key\": \"eks.securityGroups.label\",\n              tooltip: _ctx.t('eks.securityGroups.tooltip'),\n              options: _ctx.securityGroupOptions,\n              multiple: true,\n              value: _ctx.securityGroups,\n              loading: _ctx.loadingSecurityGroups,\n              \"data-testid\": \"eks-security-groups-dropdown\",\n              \"onUpdate:value\": _cache[5] || (_cache[5] = ($event: any) => (_ctx.$emit('update:securityGroups', $event)))\n            }, null, 8 /* PROPS */, [\"mode\", \"disabled\", \"tooltip\", \"options\", \"value\", \"loading\"])\n          ]))\n        : _createCommentVNode(\"v-if\", true)\n    ])\n  ]))\n}", "<script lang=\"ts\">\nimport { _CREATE, _EDIT, _VIEW } from '@shell/config/query-params';\nimport { PropType, defineComponent } from 'vue';\nimport { Store, mapGetters } from 'vuex';\nimport LabeledSelect from '@shell/components/form/LabeledSelect.vue';\nimport Checkbox from '@components/Form/Checkbox/Checkbox.vue';\nimport ArrayList from '@shell/components/form/ArrayList.vue';\nimport Banner from '@components/Banner/Banner.vue';\n\nimport RadioGroup from '@components/Form/Radio/RadioGroup.vue';\n\nimport { AWS } from '../../types';\n\nexport default defineComponent({\n  name: 'EKSNetworking',\n\n  emits: ['update:subnets', 'update:securityGroups', 'error', 'update:publicAccess', 'update:privateAccess', 'update:publicAccessSources'],\n\n  components: {\n    LabeledSelect,\n    ArrayList,\n    Checkbox,\n    RadioGroup,\n    Banner\n  },\n\n  props: {\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n\n    region: {\n      type:    String,\n      default: ''\n    },\n\n    amazonCredentialSecret: {\n      type:    String,\n      default: ''\n    },\n\n    subnets: {\n      type:    Array as PropType<string[]>,\n      default: () => []\n    },\n\n    securityGroups: {\n      type:    Array as PropType<string[]>,\n      default: () => []\n    },\n\n    publicAccess: {\n      type:    Boolean,\n      default: false\n    },\n\n    privateAccess: {\n      type:    Boolean,\n      default: false\n    },\n\n    publicAccessSources: {\n      type:    Array,\n      default: () => []\n    },\n\n    statusSubnets: {\n      type:    Array as PropType<string[]>,\n      default: () => []\n    },\n\n    rules: {\n      type:    Object,\n      default: () => {}\n    }\n  },\n\n  watch: {\n    amazonCredentialSecret: {\n      handler(neu) {\n        if (neu && !this.isView) {\n          this.fetchVpcs();\n          this.fetchSecurityGroups();\n        }\n      },\n      immediate: true\n    },\n    region: {\n      handler(neu ) {\n        if (neu && !this.isView) {\n          this.fetchVpcs();\n          this.fetchSecurityGroups();\n        }\n      },\n      immediate: true\n    },\n\n    'chooseSubnet'(neu: boolean) {\n      if (!neu) {\n        this.$emit('update:subnets', []);\n      }\n    },\n\n    selectedVpc(neu: string, old: string) {\n      if (!!old) {\n        this.$emit('update:securityGroups', []);\n      }\n    }\n\n  },\n\n  data() {\n    return {\n      loadingVpcs:           false,\n      loadingSecurityGroups: false,\n      vpcInfo:               [] as AWS.VPC[],\n      subnetInfo:            [] as AWS.Subnet[],\n      securityGroupInfo:     {} as {SecurityGroups: AWS.SecurityGroup[]},\n      chooseSubnet:          !!this.subnets && !!this.subnets.length\n    };\n  },\n\n  computed: {\n    ...mapGetters({ t: 'i18n/t' }),\n    // map subnets to VPCs\n    // {[vpc id]: [subnets]}\n    vpcOptions() {\n      const out: {key:string, label:string, _isSubnet?:boolean, kind?:string}[] = [];\n      const vpcs: AWS.VPC[] = this.vpcInfo || [];\n      const subnets: AWS.Subnet[] = this.subnetInfo || [];\n      const mappedSubnets: {[key:string]: AWS.Subnet[]} = {};\n\n      subnets.forEach((s) => {\n        if (!mappedSubnets[s.VpcId]) {\n          mappedSubnets[s.VpcId] = [s];\n        } else {\n          mappedSubnets[s.VpcId].push(s);\n        }\n      });\n      vpcs.forEach((v) => {\n        const { VpcId = '', Tags = [] } = v;\n        const nameTag = Tags.find((t) => {\n          return t.Key === 'Name';\n        })?.Value;\n\n        const formOption = {\n          key: VpcId, label: `${ nameTag } (${ VpcId })`, kind: 'group'\n        };\n\n        out.push(formOption);\n        if (mappedSubnets[VpcId]) {\n          mappedSubnets[VpcId].forEach((s) => {\n            const { SubnetId, Tags = [] } = s;\n            const nameTag = Tags.find((t) => {\n              return t.Key === 'Name';\n            })?.Value;\n\n            const subnetFormOption = {\n              key:       SubnetId,\n              label:     `${ nameTag } (${ SubnetId })`,\n              _isSubnet: true,\n              disabled:  !!this.selectedVpc && VpcId !== this.selectedVpc\n            };\n\n            out.push(subnetFormOption);\n          });\n        }\n      });\n\n      return out;\n    },\n\n    securityGroupOptions() {\n      const allGroups = this.securityGroupInfo?.SecurityGroups || [];\n\n      return allGroups.reduce((opts, sg) => {\n        if (sg.VpcId !== this.selectedVpc) {\n          return opts;\n        }\n        opts.push({\n          label: `${ sg.GroupName } (${ sg.GroupId })`,\n          value: sg.GroupId\n        });\n\n        return opts;\n      }, [] as {label: string, value: string}[]);\n    },\n\n    displaySubnets: {\n      get(): {key:string, label:string, _isSubnet?:boolean, kind?:string}[] | string[] {\n        const subnets: string[] = this.chooseSubnet ? this.subnets : this.statusSubnets;\n\n        // vpcOptions will be empty in 'view config' mode, where aws API requests are not made\n        return this.vpcOptions.length ? this.vpcOptions.filter((option) => subnets.includes(option.key)) : subnets;\n      },\n      set(neu: {key:string, label:string, _isSubnet?:boolean, kind?:string}[]) {\n        this.$emit('update:subnets', neu.map((s) => s.key));\n      }\n    },\n\n    selectedVpc() {\n      if (!this.chooseSubnet) {\n        return null;\n      }\n\n      return (this.subnetInfo || []).find((s) => this.subnets.includes(s.SubnetId))?.VpcId;\n    },\n\n    isNew(): boolean {\n      return this.mode === _CREATE;\n    },\n\n    isView():boolean {\n      return this.mode === _VIEW;\n    }\n  },\n\n  methods: {\n    async fetchVpcs() {\n      this.loadingVpcs = true;\n      const { region, amazonCredentialSecret } = this;\n\n      if (!region || !amazonCredentialSecret) {\n        return;\n      }\n      const ec2Client = await this.$store.dispatch('aws/ec2', { region, cloudCredentialId: amazonCredentialSecret });\n\n      try {\n        this.vpcInfo = await this.$store.dispatch('aws/depaginateList', { client: ec2Client, cmd: 'describeVpcs' });\n        this.subnetInfo = await this.$store.dispatch('aws/depaginateList', { client: ec2Client, cmd: 'describeSubnets' });\n      } catch (err) {\n        this.$emit('error', err);\n      }\n      this.loadingVpcs = false;\n    },\n\n    async fetchSecurityGroups() {\n      this.loadingSecurityGroups = true;\n      const { region, amazonCredentialSecret } = this;\n\n      if (!region || !amazonCredentialSecret) {\n        return;\n      }\n      const ec2Client = await this.$store.dispatch('aws/ec2', { region, cloudCredentialId: amazonCredentialSecret });\n\n      try {\n        this.securityGroupInfo = await ec2Client.describeSecurityGroups({ });\n      } catch (err) {\n        this.$emit('error', err);\n      }\n      this.loadingSecurityGroups = false;\n    }\n  }\n});\n</script>\n\n<template>\n  <div>\n    <Banner\n      color=\"info\"\n      label-key=\"eks.publicAccess.tooltip\"\n    />\n    <div class=\"row mb-10\">\n      <div class=\"col span-6\">\n        <Checkbox\n          :value=\"publicAccess\"\n          :mode=\"mode\"\n          label-key=\"eks.publicAccess.label\"\n          @update:value=\"$emit('update:publicAccess', $event)\"\n        />\n        <Checkbox\n          :value=\"privateAccess\"\n          :mode=\"mode\"\n          label-key=\"eks.privateAccess.label\"\n          @update:value=\"$emit('update:privateAccess', $event)\"\n        />\n      </div>\n    </div>\n    <div class=\"row mb-10\">\n      <div class=\"col span-6\">\n        <ArrayList\n          :value=\"publicAccessSources\"\n          :mode=\"mode\"\n          :disabled=\"!publicAccess\"\n          :add-allowed=\"publicAccess\"\n          :add-label=\"t('eks.publicAccessSources.addEndpoint')\"\n          data-testid=\"eks-public-access-sources\"\n          @update:value=\"$emit('update:publicAccessSources', $event)\"\n        >\n          <template #title>\n            {{ t('eks.publicAccessSources.label') }}\n          </template>\n        </ArrayList>\n      </div>\n    </div>\n    <div class=\"row mb-10 mt-20\">\n      <div\n        v-if=\"isNew\"\n        class=\"col span-6\"\n      >\n        <RadioGroup\n          v-model:value=\"chooseSubnet\"\n          name=\"subnet-mode\"\n          :mode=\"mode\"\n          :options=\"[{label: t('eks.subnets.default'), value: false},{label: t('eks.subnets.useCustom'), value: true}]\"\n          label-key=\"eks.subnets.title\"\n          :disabled=\"!isNew\"\n        />\n      </div>\n    </div>\n    <div\n      class=\"row mb-10\"\n    >\n      <div\n        v-if=\"chooseSubnet || !isNew\"\n        class=\"col span-6\"\n      >\n        <LabeledSelect\n          v-model:value=\"displaySubnets\"\n          :disabled=\"!isNew\"\n          :mode=\"mode\"\n          label-key=\"eks.vpcSubnet.label\"\n          :options=\"vpcOptions\"\n          :loading=\"loadingVpcs\"\n          option-key=\"key\"\n          :multiple=\"true\"\n          :rules=\"rules && rules.subnets\"\n          data-testid=\"eks-subnets-dropdown\"\n        >\n          <template #option=\"option\">\n            <span :class=\"{'pl-30': option._isSubnet}\">{{ option.label }}</span>\n          </template>\n        </LabeledSelect>\n      </div>\n      <div\n        v-if=\"chooseSubnet\"\n        class=\"col span-6\"\n      >\n        <LabeledSelect\n          :mode=\"mode\"\n          :disabled=\"!isNew\"\n          label-key=\"eks.securityGroups.label\"\n          :tooltip=\"t('eks.securityGroups.tooltip')\"\n          :options=\"securityGroupOptions\"\n          :multiple=\"true\"\n          :value=\"securityGroups\"\n          :loading=\"loadingSecurityGroups\"\n          data-testid=\"eks-security-groups-dropdown\"\n          @update:value=\"$emit('update:securityGroups', $event)\"\n        />\n      </div>\n    </div>\n  </div>\n</template>\n"]}]}