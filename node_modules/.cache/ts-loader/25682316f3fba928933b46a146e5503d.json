{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Logging.vue?vue&type=template&id=79287ef6&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Logging.vue", "mtime": 1754992537319}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Logging.vue?vue&type=template&id=79287ef6&ts=true", "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Logging.vue"], "names": ["typeEnabled", "mode", "t", "toggleType"], "mappings": "AAAA,OAAO,EAAE,gBAAgB,IAAI,iBAAiB,EAAE,WAAW,IAAI,YAAY,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,eAAe,IAAI,gBAAgB,EAAE,SAAS,IAAI,UAAU,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,MAAM,KAAK,CAAA;AAE5O,MAAM,UAAU,GAAG;ICwDZ,KAAK,EAAE,EAAA,iBAAA,EAAA,eAAA,EAAmC;IAC3C,KAAK,EAAC,WAAW;CDtDtB,CAAA;AACD,MAAM,UAAU,GAAG,ECuDR,KAAK,EAAC,YAAY,EAAA,CAAA;ADtD7B,MAAM,UAAU,GAAG,EC+DR,KAAK,EAAC,YAAY,EAAA,CAAA;AD9D7B,MAAM,UAAU,GAAG,ECwER,KAAK,EAAC,YAAY,EAAA,CAAA;ADvE7B,MAAM,UAAU,GAAG,ECiFR,KAAK,EAAC,YAAY,EAAA,CAAA;ADhF7B,MAAM,UAAU,GAAG,ECyFR,KAAK,EAAC,YAAY,EAAA,CAAA;ADvF7B,MAAM,UAAU,MAAM,CAAC,IAAS,EAAC,MAAW,EAAC,MAAW,EAAC,MAAW,EAAC,KAAU,EAAC,QAAa;IAC3F,MAAM,mBAAmB,GAAG,iBAAiB,CAAC,UAAU,CAAE,CAAA;IAE1D,OAAO,CAAC,UAAU,EAAE,ECyCpB,mBAAA,CAqDM,KAAA,EAAA,IAAA,EAAA;QApDJ,mBAAA,CAmDM,KAAA,EAnDN,UAmDM,EAAA;YA/CJ,mBAAA,CAQM,KAAA,EARN,UAQM,EAAA;gBAPJ,YAAA,CAME,mBAAA,EAAA;oBALC,KAAK,EAAEA,IAAAA,CAAAA,WAAW,CAAA,OAAA,CAAA;oBAClB,IAAI,EAAEC,IAAAA,CAAAA,IAAI;oBACX,WAAS,EAAC,iBAAiB;oBAC1B,OAAO,EAAEC,IAAAA,CAAAA,CAAC,CAAA,mBAAA,CAAA;oBACV,gBAAY,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEC,IAAAA,CAAAA,UAAU,CAAA,OAAA,CAAA,CAAA,CAAA;iBD3C1B,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;aACtD,CAAC;YACF,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;YC4C7C,mBAAA,CAQM,KAAA,EARN,UAQM,EAAA;gBAPJ,YAAA,CAME,mBAAA,EAAA;oBALC,KAAK,EAAEH,IAAAA,CAAAA,WAAW,CAAA,KAAA,CAAA;oBAClB,IAAI,EAAEC,IAAAA,CAAAA,IAAI;oBACX,WAAS,EAAC,eAAe;oBACxB,OAAO,EAAEC,IAAAA,CAAAA,CAAC,CAAA,iBAAA,CAAA;oBACV,gBAAY,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEC,IAAAA,CAAAA,UAAU,CAAA,KAAA,CAAA,CAAA,CAAA;iBD1C1B,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;aACtD,CAAC;YACF,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;YC4C7C,mBAAA,CAQM,KAAA,EARN,UAQM,EAAA;gBAPJ,YAAA,CAME,mBAAA,EAAA;oBALC,KAAK,EAAEH,IAAAA,CAAAA,WAAW,CAAA,WAAA,CAAA;oBAClB,IAAI,EAAEC,IAAAA,CAAAA,IAAI;oBACX,WAAS,EAAC,qBAAqB;oBAC9B,OAAO,EAAEC,IAAAA,CAAAA,CAAC,CAAA,uBAAA,CAAA;oBACV,gBAAY,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEC,IAAAA,CAAAA,UAAU,CAAA,WAAA,CAAA,CAAA,CAAA;iBD1C1B,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;aACtD,CAAC;YACF,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;YC4C7C,mBAAA,CAQM,KAAA,EARN,UAQM,EAAA;gBAPJ,YAAA,CAME,mBAAA,EAAA;oBALC,KAAK,EAAEH,IAAAA,CAAAA,WAAW,CAAA,mBAAA,CAAA;oBAClB,IAAI,EAAEC,IAAAA,CAAAA,IAAI;oBACX,WAAS,EAAC,6BAA6B;oBACtC,OAAO,EAAEC,IAAAA,CAAAA,CAAC,CAAA,+BAAA,CAAA;oBACV,gBAAY,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEC,IAAAA,CAAAA,UAAU,CAAA,mBAAA,CAAA,CAAA,CAAA;iBD1C1B,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;aACtD,CAAC;YACF,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;YC2C7C,mBAAA,CAQM,KAAA,EARN,UAQM,EAAA;gBAPJ,YAAA,CAME,mBAAA,EAAA;oBALC,KAAK,EAAEH,IAAAA,CAAAA,WAAW,CAAA,eAAA,CAAA;oBAClB,IAAI,EAAEC,IAAAA,CAAAA,IAAI;oBACX,WAAS,EAAC,yBAAyB;oBAClC,OAAO,EAAEC,IAAAA,CAAAA,CAAC,CAAA,2BAAA,CAAA;oBACV,gBAAY,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEC,IAAAA,CAAAA,UAAU,CAAA,eAAA,CAAA,CAAA,CAAA;iBDzC1B,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;aACtD,CAAC;SACH,CAAC;KACH,CAAC,CAAC,CAAA;AACL,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Logging.vue.tsx", "sourceRoot": "", "sourcesContent": ["import { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = {\n  style: {'justify-content':'space-between'},\n  class: \"row mb-10\"\n}\nconst _hoisted_2 = { class: \"col span-2\" }\nconst _hoisted_3 = { class: \"col span-2\" }\nconst _hoisted_4 = { class: \"col span-2\" }\nconst _hoisted_5 = { class: \"col span-2\" }\nconst _hoisted_6 = { class: \"col span-2\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_Checkbox = _resolveComponent(\"Checkbox\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", null, [\n    _createElementVNode(\"div\", _hoisted_1, [\n      _createElementVNode(\"div\", _hoisted_2, [\n        _createVNode(_component_Checkbox, {\n          value: _ctx.typeEnabled('audit'),\n          mode: _ctx.mode,\n          \"label-key\": \"eks.audit.label\",\n          tooltip: _ctx.t('eks.audit.tooltip'),\n          \"onUpdate:value\": _cache[0] || (_cache[0] = ($event: any) => (_ctx.toggleType('audit')))\n        }, null, 8 /* PROPS */, [\"value\", \"mode\", \"tooltip\"])\n      ]),\n      _cache[5] || (_cache[5] = _createTextVNode()),\n      _createElementVNode(\"div\", _hoisted_3, [\n        _createVNode(_component_Checkbox, {\n          value: _ctx.typeEnabled('api'),\n          mode: _ctx.mode,\n          \"label-key\": \"eks.api.label\",\n          tooltip: _ctx.t('eks.api.tooltip'),\n          \"onUpdate:value\": _cache[1] || (_cache[1] = ($event: any) => (_ctx.toggleType('api')))\n        }, null, 8 /* PROPS */, [\"value\", \"mode\", \"tooltip\"])\n      ]),\n      _cache[6] || (_cache[6] = _createTextVNode()),\n      _createElementVNode(\"div\", _hoisted_4, [\n        _createVNode(_component_Checkbox, {\n          value: _ctx.typeEnabled('scheduler'),\n          mode: _ctx.mode,\n          \"label-key\": \"eks.scheduler.label\",\n          tooltip: _ctx.t('eks.scheduler.tooltip'),\n          \"onUpdate:value\": _cache[2] || (_cache[2] = ($event: any) => (_ctx.toggleType('scheduler')))\n        }, null, 8 /* PROPS */, [\"value\", \"mode\", \"tooltip\"])\n      ]),\n      _cache[7] || (_cache[7] = _createTextVNode()),\n      _createElementVNode(\"div\", _hoisted_5, [\n        _createVNode(_component_Checkbox, {\n          value: _ctx.typeEnabled('controllerManager'),\n          mode: _ctx.mode,\n          \"label-key\": \"eks.controllerManager.label\",\n          tooltip: _ctx.t('eks.controllerManager.tooltip'),\n          \"onUpdate:value\": _cache[3] || (_cache[3] = ($event: any) => (_ctx.toggleType('controllerManager')))\n        }, null, 8 /* PROPS */, [\"value\", \"mode\", \"tooltip\"])\n      ]),\n      _cache[8] || (_cache[8] = _createTextVNode()),\n      _createElementVNode(\"div\", _hoisted_6, [\n        _createVNode(_component_Checkbox, {\n          value: _ctx.typeEnabled('authenticator'),\n          mode: _ctx.mode,\n          \"label-key\": \"eks.authenticator.label\",\n          tooltip: _ctx.t('eks.authenticator.tooltip'),\n          \"onUpdate:value\": _cache[4] || (_cache[4] = ($event: any) => (_ctx.toggleType('authenticator')))\n        }, null, 8 /* PROPS */, [\"value\", \"mode\", \"tooltip\"])\n      ])\n    ])\n  ]))\n}", "<script lang=\"ts\">\nimport { defineComponent, PropType } from 'vue';\nimport { mapGetters } from 'vuex';\nimport { EKSConfig } from '../../types';\nimport { _EDIT } from '@shell/config/query-params';\n\nimport Checkbox from '@components/Form/Checkbox/Checkbox.vue';\nimport { removeObject } from '@shell/utils/array';\n\nexport default defineComponent({\n  name: 'EKSLogging',\n\n  emits: ['update:loggingTypes'],\n\n  components: { Checkbox },\n\n  props: {\n    config: {\n      type:     Object as PropType<EKSConfig>,\n      required: true\n    },\n\n    loggingTypes: {\n      type:    Array,\n      default: () => []\n    },\n\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n  },\n\n  computed: { ...mapGetters({ t: 'i18n/t' }) },\n\n  methods: {\n    typeEnabled(type: string) {\n      return (this.loggingTypes || []).includes(type);\n    },\n\n    toggleType(type:string) {\n      const out = [...(this.loggingTypes || [])];\n\n      if (out.includes(type)) {\n        removeObject(out, type);\n      } else {\n        out.push(type);\n      }\n\n      this.$emit('update:loggingTypes', out);\n    }\n  },\n});\n</script>\n\n<template>\n  <div>\n    <div\n      :style=\"{'justify-content':'space-between'}\"\n      class=\"row mb-10\"\n    >\n      <div class=\"col span-2\">\n        <Checkbox\n          :value=\"typeEnabled('audit')\"\n          :mode=\"mode\"\n          label-key=\"eks.audit.label\"\n          :tooltip=\"t('eks.audit.tooltip')\"\n          @update:value=\"toggleType('audit')\"\n        />\n      </div>\n      <div class=\"col span-2\">\n        <Checkbox\n          :value=\"typeEnabled('api')\"\n          :mode=\"mode\"\n          label-key=\"eks.api.label\"\n          :tooltip=\"t('eks.api.tooltip')\"\n          @update:value=\"toggleType('api')\"\n        />\n      </div>\n\n      <div class=\"col span-2\">\n        <Checkbox\n          :value=\"typeEnabled('scheduler')\"\n          :mode=\"mode\"\n          label-key=\"eks.scheduler.label\"\n          :tooltip=\"t('eks.scheduler.tooltip')\"\n          @update:value=\"toggleType('scheduler')\"\n        />\n      </div>\n\n      <div class=\"col span-2\">\n        <Checkbox\n          :value=\"typeEnabled('controllerManager')\"\n          :mode=\"mode\"\n          label-key=\"eks.controllerManager.label\"\n          :tooltip=\"t('eks.controllerManager.tooltip')\"\n          @update:value=\"toggleType('controllerManager')\"\n        />\n      </div>\n      <div class=\"col span-2\">\n        <Checkbox\n          :value=\"typeEnabled('authenticator')\"\n          :mode=\"mode\"\n          label-key=\"eks.authenticator.label\"\n          :tooltip=\"t('eks.authenticator.tooltip')\"\n          @update:value=\"toggleType('authenticator')\"\n        />\n      </div>\n    </div>\n  </div>\n</template>\n"]}]}