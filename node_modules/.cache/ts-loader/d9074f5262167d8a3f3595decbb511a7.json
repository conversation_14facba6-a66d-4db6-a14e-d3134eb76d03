{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/SimpleNodeGroup.vue?vue&type=template&id=794d0ea2&scoped=true&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/SimpleNodeGroup.vue", "mtime": 1755002461158}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/SimpleNodeGroup.vue?vue&type=template&id=794d0ea2&scoped=true&ts=true", "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/SimpleNodeGroup.vue"], "names": ["nodeGroup", "recommendedInstanceTypes", "rules", "estimatedMonthlyCost", "updateNodeCount"], "mappings": "AAAA,OAAO,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,eAAe,IAAI,gBAAgB,EAAE,SAAS,IAAI,UAAU,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,eAAe,IAAI,gBAAgB,EAAE,gBAAgB,IAAI,iBAAiB,EAAE,OAAO,IAAI,QAAQ,EAAE,WAAW,IAAI,YAAY,EAAE,MAAM,KAAK,CAAA;AAEjV,MAAM,UAAU,GAAG,EC2GZ,KAAK,EAAC,mBAAmB,EAAA,CAAA;AD1GhC,MAAM,UAAU,GAAG,EC2GV,KAAK,EAAC,qBAAqB,EAAA,CAAA;AD1GpC,MAAM,UAAU,GAAG,ECyHJ,KAAK,EAAC,iBAAiB,EAAA,CAAA;ADxHtC,MAAM,UAAU,GAAG;IACjB,GAAG,EAAE,CAAC;IC2HM,KAAK,EAAC,mBAAmB;CDzHtC,CAAA;AACD,MAAM,UAAU,GAAG;IACjB,GAAG,EAAE,CAAC;ICiIA,KAAK,EAAC,qBAAqB;CD/HlC,CAAA;AACD,MAAM,UAAU,GAAG,ECqIV,KAAK,EAAC,2BAA2B,EAAA,CAAA;ADpI1C,MAAM,UAAU,GAAG,EC0IR,KAAK,EAAC,KAAK,EAAA,CAAA;ADzItB,MAAM,UAAU,GAAG,EC0IN,KAAK,EAAC,YAAY,EAAA,CAAA;ADzI/B,MAAM,UAAU,GAAG,ECkJN,KAAK,EAAC,YAAY,EAAA,CAAA;ADjJ/B,MAAM,WAAW,GAAG,EC0JP,KAAK,EAAC,YAAY,EAAA,CAAA;ADzJ/B,MAAM,WAAW,GAAG,ECgLX,KAAK,EAAC,2BAA2B,EAAA,CAAA;AD/K1C,MAAM,WAAW,GAAG,ECqLT,KAAK,EAAC,KAAK,EAAA,CAAA;ADpLtB,MAAM,WAAW,GAAG,ECqLP,KAAK,EAAC,YAAY,EAAA,CAAA;ADnL/B,MAAM,UAAU,MAAM,CAAC,IAAS,EAAC,MAAW,EAAC,MAAW,EAAC,MAAW,EAAC,KAAU,EAAC,QAAa;IAC3F,MAAM,wBAAwB,GAAG,iBAAiB,CAAC,eAAe,CAAE,CAAA;IACpE,MAAM,oBAAoB,GAAG,iBAAiB,CAAC,WAAW,CAAE,CAAA;IAC5D,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,QAAQ,CAAE,CAAA;IAEtD,OAAO,CAAC,UAAU,EAAE,ECkFpB,mBAAA,CAsGM,KAAA,EAtGN,UAsGM,EAAA;QArGJ,mBAAA,CAkCM,KAAA,EAlCN,UAkCM,EAAA;YDnHJ,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GCkFzB,mBAAA,CAAsB,IAAA,EAAA,IAAA,EAAlB,eAAa,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA;YDjFjB,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;YAC/C,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GCiFzB,mBAAA,CAEI,GAAA,EAAA,EAFD,KAAK,EAAC,kBAAkB,EAAA,EAAC,oEAE5B,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA;YDlFA,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;YCoF/C,YAAA,CAmBgB,wBAAA,EAAA;gBAlBN,KAAK,EAAEA,IAAAA,CAAAA,SAAS,CAAC,YAAY;gBDlFrC,gBAAgB,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAW,EAAE,EAAE,CAAC,CAAC,CCkF/CA,IAAAA,CAAAA,SAAS,CAAC,YAAY,CAAA,GAAA,MAAA,CAAA,CAAA;gBACrC,KAAK,EAAC,eAAe;gBACpB,OAAO,EAAEC,IAAAA,CAAAA,wBAAwB;gBACjC,KAAK,EAAEC,IAAAA,CAAAA,KAAK,CAAC,YAAY;gBACzB,UAAU,EAAE,IAAI;gBACjB,WAAW,EAAC,yBAAyB;aDjFtC,EAAE;gBCmFU,MAAM,EAAA,QAAA,CACf,CAQM,EATa,MAAM,EAAA,EAAA,EAAA;;oBAAA,OAAA;wBACzB,mBAAA,CAQM,KAAA,EARN,UAQM,EAAA;4BAPJ,mBAAA,CAA+B,MAAA,EAAA,IAAA,EAAA,gBAAA,CAAtB,MAAM,CAAC,KAAK,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;4BDjFrB,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;4BAC7C,CCkFQ,CAAA,MAAA,MAAM,CAAC,KAAK,0CAAE,UAAU,CAAA,KAAA,CAAA,MAAW,MAAA,MAAM,CAAC,KAAK,0CAAE,UAAU,CAAA,MAAA,CAAA,CAAA,CAAA;gCDjFjE,CAAC,CAAC,CAAC,UAAU,EAAE,ECgFjB,mBAAA,CAKO,MAAA,EALP,UAKO,EAFN,2CAED,CAAA,CAAA;gCDpFE,CAAC,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;yBACtC,CAAC;qBACH,CAAA;iBAAA,CAAC;gBACF,CAAC,EAAE,CAAC,CAAC,YAAY;aAClB,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;YAChD,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;YAC/C,CCoFQF,IAAAA,CAAAA,SAAS,CAAC,YAAY,CAAA;gBDnF5B,CAAC,CAAC,CAAC,UAAU,EAAE,ECkFjB,mBAAA,CAMM,KAAA,EANN,UAMM,EAAA;oBDvFA,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GCqF7B,mBAAA,CAA8B,GAAA,EAAA,EAA3B,KAAK,EAAC,kBAAkB,EAAA,EAAA,IAAA,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA;oBDpFvB,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;oBCqFnD,mBAAA,CAAwE,MAAA,EAAA,IAAA,EAAA;wBDnFlE,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CCmF1C,kBAAgB,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA;wBAAA,mBAAA,CAA2C,QAAA,EAAA,IAAA,EAAA,gBAAA,CAAhCG,IAAAA,CAAAA,oBAAoB,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;qBDjFhD,CAAC;iBACH,CAAC,CAAC;gBACL,CAAC,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;SACtC,CAAC;QACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;QCiF/C,mBAAA,CA8CM,KAAA,EA9CN,UA8CM,EAAA;YD7HJ,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GCgFzB,mBAAA,CAAqB,IAAA,EAAA,IAAA,EAAjB,cAAY,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA;YD/EhB,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;YAC/C,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GC+EzB,mBAAA,CAEI,GAAA,EAAA,EAFD,KAAK,EAAC,kBAAkB,EAAA,EAAC,2DAE5B,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA;YDhFA,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;YCkF/C,mBAAA,CA4BM,KAAA,EA5BN,UA4BM,EAAA;gBA3BJ,mBAAA,CAQM,KAAA,EARN,UAQM,EAAA;oBAPJ,YAAA,CAME,oBAAA,EAAA;wBALQ,KAAK,EAAEH,IAAAA,CAAAA,SAAS,CAAC,OAAO;wBDhFhC,gBAAgB,EAAE;4BAChB,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAW,EAAE,EAAE,CAAC,CAAC,CC+E/BA,IAAAA,CAAAA,SAAS,CAAC,OAAO,CAAA,GAAA,MAAA,CAAA,CAAA;4BD9E9B,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAW,EAAE,EAAE,CAAC,CCkF9BI,IAAAA,CAAAA,eAAe,CAAA,SAAA,EAAY,MAAM,CAAA,CAAA,CAAA;yBDjF/C;wBC8ED,KAAK,EAAC,eAAe;wBACrB,MAAM,EAAC,OAAO;wBACb,KAAK,EAAEF,IAAAA,CAAAA,KAAK,CAAC,OAAO;qBD5EtB,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;iBAC5C,CAAC;gBACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;gBC8E/C,mBAAA,CAQM,KAAA,EARN,UAQM,EAAA;oBAPJ,YAAA,CAME,oBAAA,EAAA;wBALQ,KAAK,EAAEF,IAAAA,CAAAA,SAAS,CAAC,WAAW;wBD5EpC,gBAAgB,EAAE;4BAChB,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAW,EAAE,EAAE,CAAC,CAAC,CC2E/BA,IAAAA,CAAAA,SAAS,CAAC,WAAW,CAAA,GAAA,MAAA,CAAA,CAAA;4BD1ElC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAW,EAAE,EAAE,CAAC,CC8E9BI,IAAAA,CAAAA,eAAe,CAAA,aAAA,EAAgB,MAAM,CAAA,CAAA,CAAA;yBD7EnD;wBC0ED,KAAK,EAAC,eAAe;wBACrB,MAAM,EAAC,OAAO;wBACb,KAAK,EAAEF,IAAAA,CAAAA,KAAK,CAAC,WAAW;qBDxE1B,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;iBAC5C,CAAC;gBACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;gBC0E/C,mBAAA,CAQM,KAAA,EARN,WAQM,EAAA;oBAPJ,YAAA,CAME,oBAAA,EAAA;wBALQ,KAAK,EAAEF,IAAAA,CAAAA,SAAS,CAAC,OAAO;wBDxEhC,gBAAgB,EAAE;4BAChB,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAW,EAAE,EAAE,CAAC,CAAC,CCuE/BA,IAAAA,CAAAA,SAAS,CAAC,OAAO,CAAA,GAAA,MAAA,CAAA,CAAA;4BDtE9B,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAW,EAAE,EAAE,CAAC,CC0E9BI,IAAAA,CAAAA,eAAe,CAAA,SAAA,EAAY,MAAM,CAAA,CAAA,CAAA;yBDzE/C;wBCsED,KAAK,EAAC,eAAe;wBACrB,MAAM,EAAC,OAAO;wBACb,KAAK,EAAEF,IAAAA,CAAAA,KAAK,CAAC,OAAO;qBDpEtB,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;iBAC5C,CAAC;aACH,CAAC;YACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;YCuE/C,YAAA,CASS,iBAAA,EAAA;gBARP,KAAK,EAAC,MAAM;gBACZ,KAAK,EAAC,OAAO;aDrEd,EAAE;gBACD,OAAO,EAAE,QAAQ,CCsEjB,GAII,EAAA,CAAA;oBAJJ,mBAAA,CAII,GAAA,EAAA,IAAA,EAAA;wBDxEA,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,CCoE7C,wEAED,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA;wBAAA,mBAAA,CAAwC,QAAA,EAAA,IAAA,EAAA,gBAAA,CAA7BF,IAAAA,CAAAA,SAAS,CAAC,OAAO,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;wBDpE1B,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,CCoEN,mBACxC,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA;wBAAA,mBAAA,CAAwC,QAAA,EAAA,IAAA,EAAA,gBAAA,CAA7BA,IAAAA,CAAAA,SAAS,CAAC,OAAO,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;wBDnE1B,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,CCmEN,qCAC1C,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA;qBDnEG,CAAC;iBACH,CAAC;gBACF,CAAC,EAAE,CAAC,CAAC,YAAY;aAClB,CAAC;SACH,CAAC;QACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;QCkE/C,mBAAA,CAgBM,KAAA,EAhBN,WAgBM,EAAA;YDhFJ,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GCiEzB,mBAAA,CAAgB,IAAA,EAAA,IAAA,EAAZ,SAAO,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA;YDhEX,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;YAC/C,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GCgEzB,mBAAA,CAEI,GAAA,EAAA,EAFD,KAAK,EAAC,kBAAkB,EAAA,EAAC,4CAE5B,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA;YDjEA,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;YCmE/C,mBAAA,CASM,KAAA,EATN,WASM,EAAA;gBARJ,mBAAA,CAOM,KAAA,EAPN,WAOM,EAAA;oBANJ,YAAA,CAKE,oBAAA,EAAA;wBAJQ,KAAK,EAAEA,IAAAA,CAAAA,SAAS,CAAC,QAAQ;wBDjEjC,gBAAgB,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAW,EAAE,EAAE,CAAC,CAAC,CCiE/CA,IAAAA,CAAAA,SAAS,CAAC,QAAQ,CAAA,GAAA,MAAA,CAAA,CAAA;wBACjC,KAAK,EAAC,WAAW;wBACjB,MAAM,EAAC,IAAI;wBACV,KAAK,EAAEE,IAAAA,CAAAA,KAAK,CAAC,QAAQ;qBDhEvB,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;iBAC5C,CAAC;aACH,CAAC;SACH,CAAC;KACH,CAAC,CAAC,CAAA;AACL,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/SimpleNodeGroup.vue.tsx", "sourceRoot": "", "sourcesContent": ["import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode } from \"vue\"\n\nconst _hoisted_1 = { class: \"simple-node-group\" }\nconst _hoisted_2 = { class: \"node-config-section\" }\nconst _hoisted_3 = { class: \"instance-option\" }\nconst _hoisted_4 = {\n  key: 0,\n  class: \"badge-recommended\"\n}\nconst _hoisted_5 = {\n  key: 0,\n  class: \"cost-estimate mt-10\"\n}\nconst _hoisted_6 = { class: \"node-config-section mt-20\" }\nconst _hoisted_7 = { class: \"row\" }\nconst _hoisted_8 = { class: \"col span-4\" }\nconst _hoisted_9 = { class: \"col span-4\" }\nconst _hoisted_10 = { class: \"col span-4\" }\nconst _hoisted_11 = { class: \"node-config-section mt-20\" }\nconst _hoisted_12 = { class: \"row\" }\nconst _hoisted_13 = { class: \"col span-6\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_LabeledSelect = _resolveComponent(\"LabeledSelect\")!\n  const _component_UnitInput = _resolveComponent(\"UnitInput\")!\n  const _component_Banner = _resolveComponent(\"Banner\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _cache[12] || (_cache[12] = _createElementVNode(\"h4\", null, \"Instance Type\", -1 /* CACHED */)),\n      _cache[13] || (_cache[13] = _createTextVNode()),\n      _cache[14] || (_cache[14] = _createElementVNode(\"p\", { class: \"text-muted mb-10\" }, \"\\n        Select the computing power for your worker nodes\\n      \", -1 /* CACHED */)),\n      _cache[15] || (_cache[15] = _createTextVNode()),\n      _createVNode(_component_LabeledSelect, {\n        value: _ctx.nodeGroup.instanceType,\n        \"onUpdate:value\": _cache[0] || (_cache[0] = ($event: any) => ((_ctx.nodeGroup.instanceType) = $event)),\n        label: \"Instance Type\",\n        options: _ctx.recommendedInstanceTypes,\n        rules: _ctx.rules.instanceType,\n        searchable: true,\n        placeholder: \"Select an instance type\"\n      }, {\n        option: _withCtx(({ option }) => [\n          _createElementVNode(\"div\", _hoisted_3, [\n            _createElementVNode(\"span\", null, _toDisplayString(option.label), 1 /* TEXT */),\n            _cache[8] || (_cache[8] = _createTextVNode()),\n            (option.value?.startsWith('t3.') || option.value?.startsWith('t4g.'))\n              ? (_openBlock(), _createElementBlock(\"span\", _hoisted_4, \"\\n              Recommended\\n            \"))\n              : _createCommentVNode(\"v-if\", true)\n          ])\n        ]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"value\", \"options\", \"rules\"]),\n      _cache[16] || (_cache[16] = _createTextVNode()),\n      (_ctx.nodeGroup.instanceType)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [\n            _cache[10] || (_cache[10] = _createElementVNode(\"i\", { class: \"icon icon-dollar\" }, null, -1 /* CACHED */)),\n            _cache[11] || (_cache[11] = _createTextVNode()),\n            _createElementVNode(\"span\", null, [\n              _cache[9] || (_cache[9] = _createTextVNode(\"Estimated cost: \", -1 /* CACHED */)),\n              _createElementVNode(\"strong\", null, _toDisplayString(_ctx.estimatedMonthlyCost), 1 /* TEXT */)\n            ])\n          ]))\n        : _createCommentVNode(\"v-if\", true)\n    ]),\n    _cache[31] || (_cache[31] = _createTextVNode()),\n    _createElementVNode(\"div\", _hoisted_6, [\n      _cache[22] || (_cache[22] = _createElementVNode(\"h4\", null, \"Cluster Size\", -1 /* CACHED */)),\n      _cache[23] || (_cache[23] = _createTextVNode()),\n      _cache[24] || (_cache[24] = _createElementVNode(\"p\", { class: \"text-muted mb-10\" }, \"\\n        Configure auto-scaling for your cluster\\n      \", -1 /* CACHED */)),\n      _cache[25] || (_cache[25] = _createTextVNode()),\n      _createElementVNode(\"div\", _hoisted_7, [\n        _createElementVNode(\"div\", _hoisted_8, [\n          _createVNode(_component_UnitInput, {\n            value: _ctx.nodeGroup.minSize,\n            \"onUpdate:value\": [\n              _cache[1] || (_cache[1] = ($event: any) => ((_ctx.nodeGroup.minSize) = $event)),\n              _cache[2] || (_cache[2] = ($event: any) => (_ctx.updateNodeCount('minSize', $event)))\n            ],\n            label: \"Minimum Nodes\",\n            suffix: \"nodes\",\n            rules: _ctx.rules.minSize\n          }, null, 8 /* PROPS */, [\"value\", \"rules\"])\n        ]),\n        _cache[17] || (_cache[17] = _createTextVNode()),\n        _createElementVNode(\"div\", _hoisted_9, [\n          _createVNode(_component_UnitInput, {\n            value: _ctx.nodeGroup.desiredSize,\n            \"onUpdate:value\": [\n              _cache[3] || (_cache[3] = ($event: any) => ((_ctx.nodeGroup.desiredSize) = $event)),\n              _cache[4] || (_cache[4] = ($event: any) => (_ctx.updateNodeCount('desiredSize', $event)))\n            ],\n            label: \"Desired Nodes\",\n            suffix: \"nodes\",\n            rules: _ctx.rules.desiredSize\n          }, null, 8 /* PROPS */, [\"value\", \"rules\"])\n        ]),\n        _cache[18] || (_cache[18] = _createTextVNode()),\n        _createElementVNode(\"div\", _hoisted_10, [\n          _createVNode(_component_UnitInput, {\n            value: _ctx.nodeGroup.maxSize,\n            \"onUpdate:value\": [\n              _cache[5] || (_cache[5] = ($event: any) => ((_ctx.nodeGroup.maxSize) = $event)),\n              _cache[6] || (_cache[6] = ($event: any) => (_ctx.updateNodeCount('maxSize', $event)))\n            ],\n            label: \"Maximum Nodes\",\n            suffix: \"nodes\",\n            rules: _ctx.rules.maxSize\n          }, null, 8 /* PROPS */, [\"value\", \"rules\"])\n        ])\n      ]),\n      _cache[26] || (_cache[26] = _createTextVNode()),\n      _createVNode(_component_Banner, {\n        color: \"info\",\n        class: \"mt-10\"\n      }, {\n        default: _withCtx(() => [\n          _createElementVNode(\"p\", null, [\n            _cache[19] || (_cache[19] = _createTextVNode(\"\\n          Your cluster will automatically scale between \\n          \", -1 /* CACHED */)),\n            _createElementVNode(\"strong\", null, _toDisplayString(_ctx.nodeGroup.minSize), 1 /* TEXT */),\n            _cache[20] || (_cache[20] = _createTextVNode(\" and \\n          \", -1 /* CACHED */)),\n            _createElementVNode(\"strong\", null, _toDisplayString(_ctx.nodeGroup.maxSize), 1 /* TEXT */),\n            _cache[21] || (_cache[21] = _createTextVNode(\" nodes based on workload.\\n        \", -1 /* CACHED */))\n          ])\n        ]),\n        _: 1 /* STABLE */\n      })\n    ]),\n    _cache[32] || (_cache[32] = _createTextVNode()),\n    _createElementVNode(\"div\", _hoisted_11, [\n      _cache[27] || (_cache[27] = _createElementVNode(\"h4\", null, \"Storage\", -1 /* CACHED */)),\n      _cache[28] || (_cache[28] = _createTextVNode()),\n      _cache[29] || (_cache[29] = _createElementVNode(\"p\", { class: \"text-muted mb-10\" }, \"\\n        Disk space for each node\\n      \", -1 /* CACHED */)),\n      _cache[30] || (_cache[30] = _createTextVNode()),\n      _createElementVNode(\"div\", _hoisted_12, [\n        _createElementVNode(\"div\", _hoisted_13, [\n          _createVNode(_component_UnitInput, {\n            value: _ctx.nodeGroup.diskSize,\n            \"onUpdate:value\": _cache[7] || (_cache[7] = ($event: any) => ((_ctx.nodeGroup.diskSize) = $event)),\n            label: \"Disk Size\",\n            suffix: \"GB\",\n            rules: _ctx.rules.diskSize\n          }, null, 8 /* PROPS */, [\"value\", \"rules\"])\n        ])\n      ])\n    ])\n  ]))\n}", "<script lang=\"ts\">\nimport { defineComponent, PropType } from 'vue';\nimport LabeledInput from '@components/Form/LabeledInput/LabeledInput.vue';\nimport LabeledSelect from '@shell/components/form/LabeledSelect.vue';\nimport UnitInput from '@shell/components/form/UnitInput.vue';\nimport Banner from '@components/Banner/Banner.vue';\nimport { EKSNodeGroup, AWS } from '../../types';\n\nexport default defineComponent({\n  name: 'SimpleNodeGroup',\n\n  components: {\n    LabeledInput,\n    LabeledSelect,\n    UnitInput,\n    Banner\n  },\n\n  props: {\n    modelValue: {\n      type:     Object as PropType<EKSNodeGroup>,\n      required: true\n    },\n    instanceTypeOptions: {\n      type:    Array as PropType<AWS.InstanceTypeOption[]>,\n      default: () => []\n    },\n    rules: {\n      type:    Object,\n      default: () => ({})\n    }\n  },\n\n  emits: ['update:modelValue'],\n\n  computed: {\n    nodeGroup: {\n      get(): EKSNodeGroup {\n        return this.modelValue;\n      },\n      set(value: EKSNodeGroup) {\n        this.$emit('update:modelValue', value);\n      }\n    },\n\n    recommendedInstanceTypes(): AWS.InstanceTypeOption[] {\n      // Filter to show only recommended instance types for PLG users\n      return this.instanceTypeOptions.filter((opt: AWS.InstanceTypeOption) => {\n        if (opt.kind === 'group') return false;\n        const value = opt.value || '';\n        // Recommend t3/t4g series for cost-effectiveness\n        return value.startsWith('t3.') || value.startsWith('t4g.') || \n               value.startsWith('m5.') || value.startsWith('m6i.');\n      });\n    },\n\n    estimatedMonthlyCost(): string {\n      // Simple cost estimation based on instance type and count\n      const costMap: Record<string, number> = {\n        't3.small':   15,\n        't3.medium':  30,\n        't3.large':   60,\n        't3.xlarge':  120,\n        't4g.small':  12,\n        't4g.medium': 24,\n        't4g.large':  48,\n        'm5.large':   70,\n        'm5.xlarge':  140,\n        'm6i.large':  70,\n        'm6i.xlarge': 140,\n      };\n\n      const instanceCost = this.nodeGroup.instanceType ? costMap[this.nodeGroup.instanceType] || 50 : 50;\n      const totalCost = instanceCost * (this.nodeGroup.desiredSize || 2);\n      \n      return `~$${totalCost}/month`;\n    }\n  },\n\n  methods: {\n    updateInstanceType(value: string) {\n      this.nodeGroup.instanceType = value;\n    },\n\n    updateNodeCount(field: 'minSize' | 'maxSize' | 'desiredSize', value: string) {\n      const numValue = parseInt(value, 10) || 0;\n      this.nodeGroup[field] = numValue;\n      \n      // Ensure logical constraints\n      if (field === 'minSize' && numValue > (this.nodeGroup.maxSize || 0)) {\n        this.nodeGroup.maxSize = numValue;\n      }\n      if (field === 'maxSize' && numValue < (this.nodeGroup.minSize || 0)) {\n        this.nodeGroup.minSize = numValue;\n      }\n      if (field === 'desiredSize') {\n        if (numValue < (this.nodeGroup.minSize || 0)) {\n          this.nodeGroup.minSize = numValue;\n        }\n        if (numValue > (this.nodeGroup.maxSize || 0)) {\n          this.nodeGroup.maxSize = numValue;\n        }\n      }\n    }\n  }\n});\n</script>\n\n<template>\n  <div class=\"simple-node-group\">\n    <div class=\"node-config-section\">\n      <h4>Instance Type</h4>\n      <p class=\"text-muted mb-10\">\n        Select the computing power for your worker nodes\n      </p>\n      \n      <LabeledSelect\n        v-model:value=\"nodeGroup.instanceType\"\n        label=\"Instance Type\"\n        :options=\"recommendedInstanceTypes\"\n        :rules=\"rules.instanceType\"\n        :searchable=\"true\"\n        placeholder=\"Select an instance type\"\n      >\n        <template #option=\"{ option }\">\n          <div class=\"instance-option\">\n            <span>{{ option.label }}</span>\n            <span\n              v-if=\"option.value?.startsWith('t3.') || option.value?.startsWith('t4g.')\"\n              class=\"badge-recommended\"\n            >\n              Recommended\n            </span>\n          </div>\n        </template>\n      </LabeledSelect>\n\n      <div\n        v-if=\"nodeGroup.instanceType\"\n        class=\"cost-estimate mt-10\"\n      >\n        <i class=\"icon icon-dollar\" />\n        <span>Estimated cost: <strong>{{ estimatedMonthlyCost }}</strong></span>\n      </div>\n    </div>\n\n    <div class=\"node-config-section mt-20\">\n      <h4>Cluster Size</h4>\n      <p class=\"text-muted mb-10\">\n        Configure auto-scaling for your cluster\n      </p>\n\n      <div class=\"row\">\n        <div class=\"col span-4\">\n          <UnitInput\n            v-model:value=\"nodeGroup.minSize\"\n            label=\"Minimum Nodes\"\n            suffix=\"nodes\"\n            :rules=\"rules.minSize\"\n            @update:value=\"updateNodeCount('minSize', $event)\"\n          />\n        </div>\n        <div class=\"col span-4\">\n          <UnitInput\n            v-model:value=\"nodeGroup.desiredSize\"\n            label=\"Desired Nodes\"\n            suffix=\"nodes\"\n            :rules=\"rules.desiredSize\"\n            @update:value=\"updateNodeCount('desiredSize', $event)\"\n          />\n        </div>\n        <div class=\"col span-4\">\n          <UnitInput\n            v-model:value=\"nodeGroup.maxSize\"\n            label=\"Maximum Nodes\"\n            suffix=\"nodes\"\n            :rules=\"rules.maxSize\"\n            @update:value=\"updateNodeCount('maxSize', $event)\"\n          />\n        </div>\n      </div>\n\n      <Banner\n        color=\"info\"\n        class=\"mt-10\"\n      >\n        <p>\n          Your cluster will automatically scale between \n          <strong>{{ nodeGroup.minSize }}</strong> and \n          <strong>{{ nodeGroup.maxSize }}</strong> nodes based on workload.\n        </p>\n      </Banner>\n    </div>\n\n    <div class=\"node-config-section mt-20\">\n      <h4>Storage</h4>\n      <p class=\"text-muted mb-10\">\n        Disk space for each node\n      </p>\n      \n      <div class=\"row\">\n        <div class=\"col span-6\">\n          <UnitInput\n            v-model:value=\"nodeGroup.diskSize\"\n            label=\"Disk Size\"\n            suffix=\"GB\"\n            :rules=\"rules.diskSize\"\n          />\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.simple-node-group {\n  .node-config-section {\n    padding: 15px;\n    background: var(--body-bg);\n    border: 1px solid var(--border);\n    border-radius: var(--border-radius);\n\n    h4 {\n      margin: 0 0 5px 0;\n      font-size: 16px;\n      font-weight: 600;\n    }\n\n    .text-muted {\n      color: var(--text-muted);\n      font-size: 14px;\n    }\n  }\n\n  .instance-option {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    width: 100%;\n\n    .badge-recommended {\n      background: var(--success);\n      color: var(--success-text);\n      padding: 2px 8px;\n      border-radius: 3px;\n      font-size: 11px;\n      font-weight: 600;\n      text-transform: uppercase;\n    }\n  }\n\n  .cost-estimate {\n    padding: 10px;\n    background: var(--info-banner-bg);\n    border: 1px solid var(--info);\n    border-radius: var(--border-radius);\n    display: flex;\n    align-items: center;\n    gap: 10px;\n    color: var(--text-default);\n\n    i {\n      color: var(--info);\n    }\n\n    strong {\n      color: var(--text-default);\n      font-weight: 600;\n    }\n  }\n}\n</style>\n\n"]}]}