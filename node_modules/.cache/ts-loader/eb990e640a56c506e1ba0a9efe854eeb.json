{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/composables/useClickOutside.ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/composables/useClickOutside.ts", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/composables/useClickOutside.ts", "sourceRoot": "", "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/composables/useClickOutside.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;GAWG;AACH,OAAO,EAAE,SAAS,EAAE,eAAe,EAAE,MAAM,KAAK,CAAC;AASjD,MAAM,CAAC,MAAM,eAAe,GAAG,CAC7B,SAAc,EACd,QAAa,EACb,UAAa,EAAO,EACpB,EAAE;IACF,MAAM,EAAE,MAAM,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;IAEhC,IAAI,YAAY,GAAG,IAAI,CAAC;IAExB,MAAM,YAAY,GAAG,CAAC,KAAmB,EAAE,EAAE;QAC3C,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;YAC5B,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;gBAC/B,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;qBACxD,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;YAC5E,CAAC;iBAAM,CAAC;gBACN,MAAM,EAAE,GAAG,MAAM,CAAC;gBAElB,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,EAAE,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;YAC1E,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,QAAQ,GAAG,CAAC,KAAmB,EAAE,EAAE;QACvC,MAAM,EAAE,GAAG,SAAS,CAAC,KAAK,CAAC;QAE3B,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;YACpE,OAAO;QACT,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,YAAY,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,YAAY,GAAG,IAAI,CAAC;YAEpB,OAAO;QACT,CAAC;QAED,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE,CAAC;YACnC,QAAQ,EAAE,CAAC;QACb,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,eAAe,GAAG,CAAC,CAAM,EAAE,EAAE;QACjC,MAAM,EAAE,GAAG,SAAS,CAAC,KAAK,CAAC;QAE3B,YAAY,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9E,CAAC,CAAC;IAEF,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAe,CAAC,CAAC;QAClD,MAAM,CAAC,gBAAgB,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;IAC1D,CAAC,CAAC,CAAC;IAEH,eAAe,CAAC,GAAG,EAAE;QACnB,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,QAAe,CAAC,CAAC;QACrD,MAAM,CAAC,mBAAmB,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC;AACL,CAAC,CAAC", "sourcesContent": ["/**\n * useClickOutside is based on onClickOutside from VueUse (https://github.com/vueuse/vueuse/blob/main/packages/core/onClickOutside/index.ts)\n *\n * This was originally reimplemented due to a resolution bug found in Yarn 1.x\n * that involves mapping a html-webpack-plugin-5 alias to html-webpack-plugin.\n * This bug is unrelated to VueUse, but would break vue/vue-cli as they rely on\n * an un-aliased version of html-webpack-plugin.\n *\n * @note Although there are minor differences between this implementation and\n * the original, we can easily replace this implementation with VueUse if we\n * find that we will benefit from importing the library in the future.\n */\nimport { onMounted, onBeforeUnmount } from 'vue';\n\nexport interface OnClickOutsideOptions {\n  /**\n   * List of elements that should not trigger the event.\n   */\n  ignore?: string[]\n}\n\nexport const useClickOutside = <T extends OnClickOutsideOptions>(\n  component: any,\n  callback: any,\n  options: T = {} as T,\n) => {\n  const { ignore = [] } = options;\n\n  let shouldListen = true;\n\n  const shouldIgnore = (event: PointerEvent) => {\n    return ignore.some((target) => {\n      if (typeof target === 'string') {\n        return Array.from(window.document.querySelectorAll(target))\n          .some((el) => el === event.target || event.composedPath().includes(el));\n      } else {\n        const el = target;\n\n        return el && (event.target === el || event.composedPath().includes(el));\n      }\n    });\n  };\n\n  const listener = (event: PointerEvent) => {\n    const el = component.value;\n\n    if (!el || el === event.target || event.composedPath().includes(el)) {\n      return;\n    }\n\n    if (event.detail === 0) {\n      shouldListen = !shouldIgnore(event);\n    }\n\n    if (!shouldListen) {\n      shouldListen = true;\n\n      return;\n    }\n\n    if (typeof callback === 'function') {\n      callback();\n    }\n  };\n\n  const setShouldListen = (e: any) => {\n    const el = component.value;\n\n    shouldListen = !shouldIgnore(e) && !!(el && !e.composedPath().includes(el));\n  };\n\n  onMounted(() => {\n    window.addEventListener('click', listener as any);\n    window.addEventListener('pointerdown', setShouldListen);\n  });\n\n  onBeforeUnmount(() => {\n    window.removeEventListener('click', listener as any);\n    window.removeEventListener('pointerDown', setShouldListen);\n  });\n};\n"]}]}