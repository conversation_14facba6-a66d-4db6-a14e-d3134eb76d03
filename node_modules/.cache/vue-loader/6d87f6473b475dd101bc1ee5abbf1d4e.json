{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceTabs/index.vue?vue&type=template&id=6b6e3567", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceTabs/index.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPFRhYmJlZAogICAgdi1iaW5kPSIkYXR0cnMiCiAgICA6ZGVmYXVsdC10YWI9ImRlZmF1bHRUYWIiCiAgICBAY2hhbmdlZD0idGFiQ2hhbmdlIgogID4KICAgIDxzbG90IC8+CgogICAgPFRhYgogICAgICB2LWlmPSJzaG93Q29uZGl0aW9ucyIKICAgICAgbGFiZWwta2V5PSJyZXNvdXJjZVRhYnMuY29uZGl0aW9ucy50YWIiCiAgICAgIG5hbWU9ImNvbmRpdGlvbnMiCiAgICAgIDp3ZWlnaHQ9Ii0xIgogICAgICA6ZGlzcGxheS1hbGVydC1pY29uPSJjb25kaXRpb25zSGF2ZUlzc3VlcyIKICAgID4KICAgICAgPENvbmRpdGlvbnMgOnZhbHVlPSJ2YWx1ZSIgLz4KICAgIDwvVGFiPgoKICAgIDxUYWIKICAgICAgdi1pZj0ic2hvd0V2ZW50cyIKICAgICAgbGFiZWwta2V5PSJyZXNvdXJjZVRhYnMuZXZlbnRzLnRhYiIKICAgICAgbmFtZT0iZXZlbnRzIgogICAgICA6d2VpZ2h0PSItMiIKICAgID4KICAgICAgPCEtLSBuYW1lc3BhY2VkOiBmYWxzZSBnaXZlbiB3ZSBkb24ndCB3YW50IHRoZSBkZWZhdWx0IGhhbmRsaW5nIG9mIG5hbWVzcGFjZWQgcmVzb3VyY2UgKGFwcGx5IGhlYWRlciBmaWx0ZXIpICAtLT4KICAgICAgPFBhZ2luYXRlZFJlc291cmNlVGFibGUKICAgICAgICB2LWlmPSJzZWxlY3RlZFRhYiA9PT0gJ2V2ZW50cyciCiAgICAgICAgOnNjaGVtYT0iZXZlbnRTY2hlbWEiCiAgICAgICAgOmxvY2FsLWZpbHRlcj0iZmlsdGVyRXZlbnRzTG9jYWwiCiAgICAgICAgOmFwaS1maWx0ZXI9ImZpbHRlckV2ZW50c0FwaSIKICAgICAgICA6dXNlLXF1ZXJ5LXBhcmFtcy1mb3Itc2ltcGxlLWZpbHRlcmluZz0iZmFsc2UiCiAgICAgICAgOmhlYWRlcnM9ImV2ZW50SGVhZGVycyIKICAgICAgICA6cGFnaW5hdGlvbkhlYWRlcnM9InBhZ2luYXRpb25IZWFkZXJzIgogICAgICAgIDpuYW1lc3BhY2VkPSJmYWxzZSIKICAgICAgLz4KICAgIDwvVGFiPgoKICAgIDxUYWIKICAgICAgdi1pZj0ic2hvd1JlbGF0ZWQiCiAgICAgIG5hbWU9InJlbGF0ZWQiCiAgICAgIGxhYmVsLWtleT0icmVzb3VyY2VUYWJzLnJlbGF0ZWQudGFiIgogICAgICA6d2VpZ2h0PSItMyIKICAgID4KICAgICAgPGgzIHYtdD0iJ3Jlc291cmNlVGFicy5yZWxhdGVkLmZyb20nIiAvPgogICAgICA8UmVsYXRlZFJlc291cmNlcwogICAgICAgIDppZ25vcmUtdHlwZXM9Ilt2YWx1ZS50eXBlXSIKICAgICAgICA6dmFsdWU9InZhbHVlIgogICAgICAgIGRpcmVjdGlvbj0iZnJvbSIKICAgICAgLz4KCiAgICAgIDxoMwogICAgICAgIHYtdD0iJ3Jlc291cmNlVGFicy5yZWxhdGVkLnRvJyIKICAgICAgICBjbGFzcz0ibXQtMjAiCiAgICAgIC8+CiAgICAgIDxSZWxhdGVkUmVzb3VyY2VzCiAgICAgICAgOmlnbm9yZS10eXBlcz0iW3ZhbHVlLnR5cGVdIgogICAgICAgIDp2YWx1ZT0idmFsdWUiCiAgICAgICAgZGlyZWN0aW9uPSJ0byIKICAgICAgLz4KICAgIDwvVGFiPgogIDwvVGFiYmVkPgo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceTabs/index.vue"], "names": [], "mappings": ";EAuOE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB;IACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEP,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C;MACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb;MACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MAChH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb;MACE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC;;MAED,CAAC,CAAC;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceTabs/index.vue", "sourceRoot": "", "sourcesContent": ["<script>\n/*\n    Tab component for resource CRU pages featuring:\n    Labels and Annotation tabs with content filtered by create-edit-view mixin\n*/\nimport Tabbed from '@shell/components/Tabbed';\nimport Tab from '@shell/components/Tabbed/Tab';\nimport CreateEditView from '@shell/mixins/create-edit-view';\nimport Conditions from '@shell/components/form/Conditions';\nimport { EVENT } from '@shell/config/types';\nimport PaginatedResourceTable from '@shell/components/PaginatedResourceTable.vue';\nimport { _VIEW } from '@shell/config/query-params';\nimport RelatedResources from '@shell/components/RelatedResources';\nimport { isConditionReadyAndWaiting } from '@shell/plugins/dashboard-store/resource-class';\nimport { PaginationParamFilter } from '@shell/types/store/pagination.types';\nimport { MESSAGE, REASON } from '@shell/config/table-headers';\nimport { STEVE_EVENT_LAST_SEEN, STEVE_EVENT_TYPE, STEVE_NAME_COL } from '@shell/config/pagination-table-headers';\nimport { headerFromSchemaColString } from '@shell/store/type-map.utils';\n\nexport default {\n\n  name: 'ResourceTabs',\n\n  components: {\n    Tabbed,\n    Tab,\n    Conditions,\n    PaginatedResourceTable,\n    RelatedResources,\n  },\n\n  mixins: [CreateEditView],\n\n  props: {\n    // resource instance\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    // create-edit-view mode\n    mode: {\n      type:    String,\n      default: _VIEW,\n    },\n\n    defaultTab: {\n      type:    String,\n      default: null,\n    },\n\n    needConditions: {\n      type:    Boolean,\n      default: true\n    },\n\n    needEvents: {\n      type:    Boolean,\n      default: true\n    },\n\n    needRelated: {\n      type:    Boolean,\n      default: true\n    },\n\n    extensionParams: {\n      type:    Object,\n      default: null\n    }\n  },\n\n  data() {\n    const inStore = this.$store.getters['currentStore'](EVENT);\n    const eventSchema = this.$store.getters[`${ inStore }/schemaFor`](EVENT); // @TODO be smarter about which resources actually ever have events\n\n    const paginationHeaders = eventSchema ? [\n      STEVE_EVENT_LAST_SEEN,\n      STEVE_EVENT_TYPE,\n      REASON,\n      headerFromSchemaColString('Subobject', eventSchema, this.$store.getters, true),\n      headerFromSchemaColString('Source', eventSchema, this.$store.getters, true),\n      MESSAGE,\n      headerFromSchemaColString('First Seen', eventSchema, this.$store.getters, true),\n      headerFromSchemaColString('Count', eventSchema, this.$store.getters, true),\n      STEVE_NAME_COL,\n    ] : [];\n\n    return {\n      eventSchema,\n      EVENT,\n      selectedTab:    this.defaultTab,\n      inStore,\n      showConditions: false,\n      paginationHeaders\n    };\n  },\n\n  beforeUnmount() {\n    this.$store.dispatch('cluster/forgetType', EVENT);\n  },\n\n  fetch() {\n    // By this stage the `value` should be set. Taking a chance that this is true\n    // The alternative is have an expensive watch on the `value` and trigger there (as well)\n    this.setShowConditions();\n  },\n\n  computed: {\n    showEvents() {\n      return this.isView && this.needEvents && this.eventSchema;\n    },\n    showRelated() {\n      return this.isView && this.needRelated;\n    },\n    eventHeaders() {\n      return [\n        {\n          name:  'type',\n          label: this.t('tableHeaders.type'),\n          value: 'eventType',\n          sort:  'eventType',\n        },\n        {\n          name:  'reason',\n          label: this.t('tableHeaders.reason'),\n          value: 'reason',\n          sort:  'reason',\n        },\n        {\n          name:          'date',\n          label:         this.t('tableHeaders.updated'),\n          value:         'date',\n          sort:          'date:desc',\n          formatter:     'LiveDate',\n          formatterOpts: { addSuffix: true },\n          width:         125\n        },\n        {\n          name:  'message',\n          label: this.t('tableHeaders.message'),\n          value: 'message',\n          sort:  'message',\n        },\n      ];\n    },\n    conditionsHaveIssues() {\n      if (this.showConditions) {\n        return this.value.status?.conditions?.filter((cond) => !isConditionReadyAndWaiting(cond)).some((cond) => cond.error);\n      }\n\n      return false;\n    }\n  },\n\n  methods: {\n    // Ensures we only fetch events and show the table when the events tab has been activated\n    tabChange(neu) {\n      this.selectedTab = neu?.selectedName;\n    },\n\n    /**\n    * Conditions come from a resource's `status`. They are used by both core resources like workloads as well as those from CRDs\n    * - Workloads\n    * - Nodes\n    * - Fleet git repo\n    * - Cluster (provisioning)\n    *\n    * Check here if the resource type contains conditions via the schema resourceFields\n    */\n    async setShowConditions() {\n      if (this.isView && this.needConditions && !!this.value?.type && !!this.schema?.fetchResourceFields) {\n        await this.schema.fetchResourceFields();\n\n        this.showConditions = this.$store.getters[`${ this.inStore }/pathExistsInSchema`](this.value.type, 'status.conditions');\n      }\n    },\n\n    /**\n     * Filter out hidden repos from list of all repos\n     */\n    filterEventsLocal(rows) {\n      return rows.filter((event) => event.involvedObject?.uid === this.value?.metadata?.uid);\n    },\n\n    /**\n     * Filter out hidden repos via api\n     *\n     * pagination: PaginationArgs\n     * returns: PaginationArgs\n     */\n    filterEventsApi(pagination) {\n      if (!pagination.filters) {\n        pagination.filters = [];\n      }\n\n      const field = `involvedObject.uid`; // Pending API Support - https://github.com/rancher/rancher/issues/48603\n\n      // of type PaginationParamFilter\n      let existing = null;\n\n      for (let i = 0; i < pagination.filters.length; i++) {\n        const filter = pagination.filters[i];\n\n        if (!!filter.fields.find((f) => f.field === field)) {\n          existing = filter;\n          break;\n        }\n      }\n\n      const required = PaginationParamFilter.createSingleField({\n        field,\n        exact:  true,\n        value:  this.value.metadata.uid,\n        equals: true\n      });\n\n      if (!!existing) {\n        Object.assign(existing, required);\n      } else {\n        pagination.filters.push(required);\n      }\n\n      return pagination;\n    }\n  }\n};\n</script>\n\n<template>\n  <Tabbed\n    v-bind=\"$attrs\"\n    :default-tab=\"defaultTab\"\n    @changed=\"tabChange\"\n  >\n    <slot />\n\n    <Tab\n      v-if=\"showConditions\"\n      label-key=\"resourceTabs.conditions.tab\"\n      name=\"conditions\"\n      :weight=\"-1\"\n      :display-alert-icon=\"conditionsHaveIssues\"\n    >\n      <Conditions :value=\"value\" />\n    </Tab>\n\n    <Tab\n      v-if=\"showEvents\"\n      label-key=\"resourceTabs.events.tab\"\n      name=\"events\"\n      :weight=\"-2\"\n    >\n      <!-- namespaced: false given we don't want the default handling of namespaced resource (apply header filter)  -->\n      <PaginatedResourceTable\n        v-if=\"selectedTab === 'events'\"\n        :schema=\"eventSchema\"\n        :local-filter=\"filterEventsLocal\"\n        :api-filter=\"filterEventsApi\"\n        :use-query-params-for-simple-filtering=\"false\"\n        :headers=\"eventHeaders\"\n        :paginationHeaders=\"paginationHeaders\"\n        :namespaced=\"false\"\n      />\n    </Tab>\n\n    <Tab\n      v-if=\"showRelated\"\n      name=\"related\"\n      label-key=\"resourceTabs.related.tab\"\n      :weight=\"-3\"\n    >\n      <h3 v-t=\"'resourceTabs.related.from'\" />\n      <RelatedResources\n        :ignore-types=\"[value.type]\"\n        :value=\"value\"\n        direction=\"from\"\n      />\n\n      <h3\n        v-t=\"'resourceTabs.related.to'\"\n        class=\"mt-20\"\n      />\n      <RelatedResources\n        :ignore-types=\"[value.type]\"\n        :value=\"value\"\n        direction=\"to\"\n      />\n    </Tab>\n  </Tabbed>\n</template>\n"]}]}