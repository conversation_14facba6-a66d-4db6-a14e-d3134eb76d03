{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayListGrouped.vue?vue&type=style&index=0&id=32af8c7f&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayListGrouped.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5hcnJheS1saXN0LWdyb3VwZWQgewogICAgJiA+IC5ib3ggewogICAgICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgICAgIGRpc3BsYXk6IGJsb2NrOwoKICAgICAgJiA+IC5yZW1vdmUgewogICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKCiAgICAgICAgdG9wOiAwOwogICAgICAgIHJpZ2h0OiAwOwogICAgICB9CgogICAgICAmID4gLmluZm8tYm94IHsKICAgICAgICBtYXJnaW4tYm90dG9tOiAwOwogICAgICAgIHBhZGRpbmctcmlnaHQ6IDI1cHg7CiAgICAgIH0KICAgIH0KfQoK"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayListGrouped.vue"], "names": [], "mappings": ";AAyHA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEd,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAElB,CAAC,CAAC,CAAC,EAAE,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACV;;MAEA,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACrB;IACF;AACJ", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayListGrouped.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport ArrayList from '@shell/components/form/ArrayList';\nimport InfoBox from '@shell/components/InfoBox';\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\n\nexport default {\n  name:       'ArrayListGrouped',\n  components: { ArrayList, InfoBox },\n  props:      {\n    /**\n     * Allow to remove items by value or computation\n     */\n    canRemove: {\n      type:    [Boolean, Function],\n      default: true,\n    },\n\n    /**\n     * Allow to extend list\n     */\n    canAdd: {\n      type:    Boolean,\n      default: true,\n    },\n    /**\n     * Start with empty row\n     */\n    initialEmptyRow: {\n      type:    Boolean,\n      default: false,\n    },\n\n    /**\n     * Form mode for the component\n     */\n    mode: {\n      type:    String,\n      default: _EDIT,\n    },\n\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      },\n    },\n  },\n\n  emits: ['update:value', 'add', 'remove'],\n\n  computed: {\n    isView() {\n      return this.mode === _VIEW;\n    }\n  },\n\n  methods: {\n    /**\n     * Verify if row can be removed by mode, function and declaration\n     */\n    canRemoveRow(row, idx) {\n      if ( this.isView ) {\n        return false;\n      }\n\n      if ( typeof this.canRemove === 'function' ) {\n        return this.canRemove(row, idx);\n      }\n\n      return this.canRemove;\n    },\n  }\n};\n</script>\n\n<template>\n  <ArrayList\n    class=\"array-list-grouped\"\n    :value=\"value\"\n    v-bind=\"$attrs\"\n    :add-allowed=\"canAdd && !isView\"\n    :mode=\"mode\"\n    :initial-empty-row=\"initialEmptyRow\"\n    @update:value=\"$emit('update:value', $event)\"\n    @add=\"$emit('add')\"\n    @remove=\"$emit('remove', $event)\"\n  >\n    <template v-slot:columns=\"scope\">\n      <InfoBox>\n        <slot v-bind=\"scope\" />\n      </InfoBox>\n    </template>\n    <template v-slot:remove-button=\"scope\">\n      <button\n        v-if=\"canRemoveRow(scope.row, scope.i)\"\n        type=\"button\"\n        class=\"btn role-link close btn-sm\"\n        :data-testid=\"`remove-item-${scope.i}`\"\n        @click=\"scope.remove\"\n      >\n        <i class=\"icon icon-x\" />\n      </button>\n      <span v-else />\n    </template>\n    <!-- Pass down templates provided by the caller -->\n    <template\n      v-for=\"(_, slot) of $slots\"\n      #[slot]=\"scope\"\n      :key=\"slot\"\n    >\n      <template v-if=\"typeof $slots[slot] === 'function'\">\n        <slot\n          :name=\"slot\"\n          v-bind=\"scope\"\n        />\n      </template>\n    </template>\n  </ArrayList>\n</template>\n\n<style lang=\"scss\">\n.array-list-grouped {\n    & > .box {\n      position: relative;\n      display: block;\n\n      & > .remove {\n        position: absolute;\n\n        top: 0;\n        right: 0;\n      }\n\n      & > .info-box {\n        margin-bottom: 0;\n        padding-right: 25px;\n      }\n    }\n}\n\n</style>\n"]}]}