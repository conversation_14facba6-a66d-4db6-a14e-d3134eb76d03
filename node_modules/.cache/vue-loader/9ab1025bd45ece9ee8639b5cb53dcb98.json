{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Bar.vue", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Bar.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVuZGVyIH0gZnJvbSAiLi9CYXIudnVlP3Z1ZSZ0eXBlPXRlbXBsYXRlJmlkPTFjOWI5OGRmJnNjb3BlZD10cnVlIgppbXBvcnQgc2NyaXB0IGZyb20gIi4vQmFyLnZ1ZT92dWUmdHlwZT1zY3JpcHQmbGFuZz1qcyIKZXhwb3J0ICogZnJvbSAiLi9CYXIudnVlP3Z1ZSZ0eXBlPXNjcmlwdCZsYW5nPWpzIgoKaW1wb3J0ICIuL0Jhci52dWU/dnVlJnR5cGU9c3R5bGUmaW5kZXg9MCZpZD0xYzliOThkZiZsYW5nPXNjc3Mmc2NvcGVkPXRydWUiCgppbXBvcnQgZXhwb3J0Q29tcG9uZW50IGZyb20gIi4uLy4uLy4uLy4uL3Z1ZS1sb2FkZXIvZGlzdC9leHBvcnRIZWxwZXIuanMiCmNvbnN0IF9fZXhwb3J0c19fID0gLyojX19QVVJFX18qL2V4cG9ydENvbXBvbmVudChzY3JpcHQsIFtbJ3JlbmRlcicscmVuZGVyXSxbJ19fc2NvcGVJZCcsImRhdGEtdi0xYzliOThkZiJdLFsnX19maWxlJywibm9kZV9tb2R1bGVzL0ByYW5jaGVyL3NoZWxsL2NvbXBvbmVudHMvZ3JhcGgvQmFyLnZ1ZSJdXSkKLyogaG90IHJlbG9hZCAqLwppZiAobW9kdWxlLmhvdCkgewogIF9fZXhwb3J0c19fLl9faG1ySWQgPSAiMWM5Yjk4ZGYiCiAgY29uc3QgYXBpID0gX19WVUVfSE1SX1JVTlRJTUVfXwogIG1vZHVsZS5ob3QuYWNjZXB0KCkKICBpZiAoIWFwaS5jcmVhdGVSZWNvcmQoJzFjOWI5OGRmJywgX19leHBvcnRzX18pKSB7CiAgICBhcGkucmVsb2FkKCcxYzliOThkZicsIF9fZXhwb3J0c19fKQogIH0KICAKICBtb2R1bGUuaG90LmFjY2VwdCgiLi9CYXIudnVlP3Z1ZSZ0eXBlPXRlbXBsYXRlJmlkPTFjOWI5OGRmJnNjb3BlZD10cnVlIiwgKCkgPT4gewogICAgYXBpLnJlcmVuZGVyKCcxYzliOThkZicsIHJlbmRlcikKICB9KQoKfQoKCmV4cG9ydCBkZWZhdWx0IF9fZXhwb3J0c19f"}]}