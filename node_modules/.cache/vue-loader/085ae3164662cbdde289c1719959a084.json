{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/NodeGroup.vue?vue&type=template&id=151d0a74&scoped=true&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/NodeGroup.vue", "mtime": 1754992537319}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdj4KICAgIDxoMz57eyB0KCdla3Mubm9kZUdyb3Vwcy5ncm91cERldGFpbHMnKSB9fTwvaDM+CiAgICA8ZGl2IGNsYXNzPSJyb3cgbWItMTAiPgogICAgICA8ZGl2IGNsYXNzPSJjb2wgc3Bhbi02Ij4KICAgICAgICA8TGFiZWxlZElucHV0CiAgICAgICAgICA6dmFsdWU9Im5vZGVncm91cE5hbWUiCiAgICAgICAgICBsYWJlbC1rZXk9ImVrcy5ub2RlR3JvdXBzLm5hbWUubGFiZWwiCiAgICAgICAgICA6bW9kZT0ibW9kZSIKICAgICAgICAgIDpkaXNhYmxlZD0iIXBvb2xJc1VucHJvdmlzaW9uZWQiCiAgICAgICAgICA6cnVsZXM9InJ1bGVzLm5vZGVncm91cE5hbWUiCiAgICAgICAgICBkYXRhLXRlc3RpZD0iZWtzLW5vZGVncm91cC1uYW1lIgogICAgICAgICAgcmVxdWlyZWQKICAgICAgICAgIEB1cGRhdGU6dmFsdWU9IiRlbWl0KCd1cGRhdGU6bm9kZWdyb3VwTmFtZScsICRldmVudCkiCiAgICAgICAgLz4KICAgICAgPC9kaXY+CgogICAgICA8ZGl2IGNsYXNzPSJjb2wgc3Bhbi02Ij4KICAgICAgICA8TGFiZWxlZFNlbGVjdAogICAgICAgICAgdi1tb2RlbDp2YWx1ZT0iZGlzcGxheU5vZGVSb2xlIgogICAgICAgICAgOm1vZGU9Im1vZGUiCiAgICAgICAgICBkYXRhLXRlc3RpZD0iZWtzLW5vZGVyb2xlIgogICAgICAgICAgbGFiZWwta2V5PSJla3Mubm9kZUdyb3Vwcy5ub2RlUm9sZS5sYWJlbCIKICAgICAgICAgIDpvcHRpb25zPSJbZGVmYXVsdE5vZGVSb2xlT3B0aW9uLCAuLi5lYzJSb2xlc10iCiAgICAgICAgICBvcHRpb24tbGFiZWw9IlJvbGVOYW1lIgogICAgICAgICAgb3B0aW9uLWtleT0iQXJuIgogICAgICAgICAgOmRpc2FibGVkPSIhcG9vbElzVW5wcm92aXNpb25lZCIKICAgICAgICAgIDpsb2FkaW5nPSJsb2FkaW5nUm9sZXMiCiAgICAgICAgLz4KICAgICAgPC9kaXY+CiAgICA8L2Rpdj4KICAgIDxkaXYgY2xhc3M9InJvdyBtYi0xMCI+CiAgICAgIDxkaXYgY2xhc3M9ImNvbCBzcGFuLTQiPgogICAgICAgIDxMYWJlbGVkSW5wdXQKICAgICAgICAgIHR5cGU9Im51bWJlciIKICAgICAgICAgIDp2YWx1ZT0iZGVzaXJlZFNpemUiCiAgICAgICAgICBsYWJlbC1rZXk9ImVrcy5ub2RlR3JvdXBzLmRlc2lyZWRTaXplLmxhYmVsIgogICAgICAgICAgOm1vZGU9Im1vZGUiCiAgICAgICAgICA6cnVsZXM9InJ1bGVzLmRlc2lyZWRTaXplIgogICAgICAgICAgQHVwZGF0ZTp2YWx1ZT0iJGVtaXQoJ3VwZGF0ZTpkZXNpcmVkU2l6ZScsIHBhcnNlSW50KCRldmVudCkpIgogICAgICAgIC8+CiAgICAgIDwvZGl2PgogICAgICA8ZGl2IGNsYXNzPSJjb2wgc3Bhbi00Ij4KICAgICAgICA8TGFiZWxlZElucHV0CiAgICAgICAgICB0eXBlPSJudW1iZXIiCiAgICAgICAgICA6dmFsdWU9Im1pblNpemUiCiAgICAgICAgICBsYWJlbC1rZXk9ImVrcy5ub2RlR3JvdXBzLm1pblNpemUubGFiZWwiCiAgICAgICAgICA6bW9kZT0ibW9kZSIKICAgICAgICAgIDpydWxlcz0icnVsZXMubWluU2l6ZSIKICAgICAgICAgIEB1cGRhdGU6dmFsdWU9IiRlbWl0KCd1cGRhdGU6bWluU2l6ZScsIHBhcnNlSW50KCRldmVudCkpIgogICAgICAgIC8+CiAgICAgIDwvZGl2PgogICAgICA8ZGl2IGNsYXNzPSJjb2wgc3Bhbi00Ij4KICAgICAgICA8TGFiZWxlZElucHV0CiAgICAgICAgICB0eXBlPSJudW1iZXIiCiAgICAgICAgICA6dmFsdWU9Im1heFNpemUiCiAgICAgICAgICBsYWJlbC1rZXk9ImVrcy5ub2RlR3JvdXBzLm1heFNpemUubGFiZWwiCiAgICAgICAgICA6bW9kZT0ibW9kZSIKICAgICAgICAgIDpydWxlcz0icnVsZXMubWF4U2l6ZSIKICAgICAgICAgIEB1cGRhdGU6dmFsdWU9IiRlbWl0KCd1cGRhdGU6bWF4U2l6ZScsIHBhcnNlSW50KCRldmVudCkpIgogICAgICAgIC8+CiAgICAgIDwvZGl2PgogICAgPC9kaXY+CiAgICA8QmFubmVyCiAgICAgIHYtaWY9IiEhbWluTWF4RGVzaXJlZEVycm9ycyIKICAgICAgY29sb3I9ImVycm9yIgogICAgICA6bGFiZWw9Im1pbk1heERlc2lyZWRFcnJvcnMiCiAgICAvPgogICAgPGRpdiBjbGFzcz0icm93IG1iLTEwIj4KICAgICAgPGRpdiBjbGFzcz0iY29sIHNwYW4tNiBtdC0yMCI+CiAgICAgICAgPEtleVZhbHVlCiAgICAgICAgICA6bW9kZT0ibW9kZSIKICAgICAgICAgIDp0aXRsZT0idCgnZWtzLm5vZGVHcm91cHMuZ3JvdXBMYWJlbHMubGFiZWwnKSIKICAgICAgICAgIDpyZWFkLWFsbG93ZWQ9ImZhbHNlIgogICAgICAgICAgOnZhbHVlPSJsYWJlbHMiCiAgICAgICAgICA6YXMtbWFwPSJ0cnVlIgogICAgICAgICAgQHVwZGF0ZTp2YWx1ZT0iJGVtaXQoJ3VwZGF0ZTpsYWJlbHMnLCAkZXZlbnQpIgogICAgICAgID4KICAgICAgICAgIDx0ZW1wbGF0ZSAjdGl0bGU+CiAgICAgICAgICAgIDxoND4KICAgICAgICAgICAgICB7eyB0KCdla3Mubm9kZUdyb3Vwcy5ncm91cExhYmVscy5sYWJlbCcpIH19CiAgICAgICAgICAgIDwvaDQ+CiAgICAgICAgICA8L3RlbXBsYXRlPgogICAgICAgIDwvS2V5VmFsdWU+CiAgICAgIDwvZGl2PgogICAgICA8ZGl2IGNsYXNzPSJjb2wgc3Bhbi02IG10LTIwIj4KICAgICAgICA8S2V5VmFsdWUKICAgICAgICAgIDptb2RlPSJtb2RlIgogICAgICAgICAgOnRpdGxlPSJ0KCdla3Mubm9kZUdyb3Vwcy5ncm91cFRhZ3MubGFiZWwnKSIKICAgICAgICAgIDpyZWFkLWFsbG93ZWQ9ImZhbHNlIgogICAgICAgICAgOmFzLW1hcD0idHJ1ZSIKICAgICAgICAgIDp2YWx1ZT0idGFncyIKICAgICAgICAgIGRhdGEtdGVzdGlkPSJla3MtcmVzb3VyY2UtdGFncy1pbnB1dCIKICAgICAgICAgIEB1cGRhdGU6dmFsdWU9IiRlbWl0KCd1cGRhdGU6dGFncycsICRldmVudCkiCiAgICAgICAgPgogICAgICAgICAgPHRlbXBsYXRlICN0aXRsZT4KICAgICAgICAgICAgPGg0Pnt7IHQoJ2Vrcy5ub2RlR3JvdXBzLmdyb3VwVGFncy5sYWJlbCcpIH19PC9oND4KICAgICAgICAgIDwvdGVtcGxhdGU+CiAgICAgICAgPC9LZXlWYWx1ZT4KICAgICAgPC9kaXY+CiAgICA8L2Rpdj4KICAgIDxocgogICAgICBjbGFzcz0ibWItMjAiCiAgICAgIHJvbGU9Im5vbmUiCiAgICA+CiAgICA8aDM+e3sgdCgnZWtzLm5vZGVHcm91cHMudGVtcGxhdGVEZXRhaWxzJykgfX08L2gzPgogICAgPEJhbm5lcgogICAgICB2LWlmPSJjbHVzdGVyV2lsbFVwZ3JhZGUgJiYgIXBvb2xJc1VucHJvdmlzaW9uZWQiCiAgICAgIGNvbG9yPSJpbmZvIgogICAgICBsYWJlbC1rZXk9ImVrcy5ub2RlR3JvdXBzLmt1YmVybmV0ZXNWZXJzaW9uLmNsdXN0ZXJXaWxsVXBncmFkZSIKICAgICAgZGF0YS10ZXN0aWQ9ImVrcy12ZXJzaW9uLXVwZ3JhZGUtYmFubmVyIgogICAgLz4KICAgIDxkaXYgY2xhc3M9InJvdyBtYi0xMCI+CiAgICAgIDxkaXYgY2xhc3M9ImNvbCBzcGFuLTQgdXBncmFkZS12ZXJzaW9uIj4KICAgICAgICA8TGFiZWxlZElucHV0CiAgICAgICAgICB2LWlmPSIhbm9kZUNhblVwZ3JhZGUiCiAgICAgICAgICBsYWJlbC1rZXk9ImVrcy5ub2RlR3JvdXBzLmt1YmVybmV0ZXNWZXJzaW9uLmxhYmVsIgogICAgICAgICAgOmRpc2FibGVkPSJ0cnVlIgogICAgICAgICAgOnZhbHVlPSJ2ZXJzaW9uIgogICAgICAgICAgZGF0YS10ZXN0aWQ9ImVrcy12ZXJzaW9uLWRpc3BsYXkiCiAgICAgICAgLz4KICAgICAgICA8Q2hlY2tib3gKICAgICAgICAgIHYtZWxzZQogICAgICAgICAgdi1tb2RlbDp2YWx1ZT0id2lsbFVwZ3JhZGUiCiAgICAgICAgICA6bGFiZWw9InQoJ2Vrcy5ub2RlR3JvdXBzLmt1YmVybmV0ZXNWZXJzaW9uLnVwZ3JhZGUnLCB7ZnJvbTogb3JpZ2luYWxOb2RlVmVyc2lvbiwgdG86IGNsdXN0ZXJWZXJzaW9ufSkiCiAgICAgICAgICBkYXRhLXRlc3RpZD0iZWtzLXZlcnNpb24tdXBncmFkZS1jaGVja2JveCIKICAgICAgICAgIDpkaXNhYmxlZD0iaXNWaWV3IgogICAgICAgIC8+CiAgICAgIDwvZGl2PgogICAgICA8ZGl2IGNsYXNzPSJjb2wgc3Bhbi00Ij4KICAgICAgICA8TGFiZWxlZFNlbGVjdAogICAgICAgICAgdi1tb2RlbDp2YWx1ZT0ic2VsZWN0ZWRMYXVuY2hUZW1wbGF0ZSIKICAgICAgICAgIDptb2RlPSJtb2RlIgogICAgICAgICAgbGFiZWwta2V5PSJla3Mubm9kZUdyb3Vwcy5sYXVuY2hUZW1wbGF0ZS5sYWJlbCIKICAgICAgICAgIDpvcHRpb25zPSJsYXVuY2hUZW1wbGF0ZU9wdGlvbnMiCiAgICAgICAgICBvcHRpb24tbGFiZWw9IkxhdW5jaFRlbXBsYXRlTmFtZSIKICAgICAgICAgIG9wdGlvbi1rZXk9IkxhdW5jaFRlbXBsYXRlSWQiCiAgICAgICAgICA6ZGlzYWJsZWQ9IiFwb29sSXNVbnByb3Zpc2lvbmVkIgogICAgICAgICAgOmxvYWRpbmc9ImxvYWRpbmdMYXVuY2hUZW1wbGF0ZXMiCiAgICAgICAgICBkYXRhLXRlc3RpZD0iZWtzLWxhdW5jaC10ZW1wbGF0ZS1kcm9wZG93biIKICAgICAgICAvPgogICAgICA8L2Rpdj4KICAgICAgPGRpdiBjbGFzcz0iY29sIHNwYW4tNCI+CiAgICAgICAgPExhYmVsZWRTZWxlY3QKICAgICAgICAgIHYtaWY9Imhhc1VzZXJMYXVuY2hUZW1wbGF0ZSIKICAgICAgICAgIDp2YWx1ZT0ibGF1bmNoVGVtcGxhdGUudmVyc2lvbiIKICAgICAgICAgIDptb2RlPSJtb2RlIgogICAgICAgICAgbGFiZWwta2V5PSJla3Mubm9kZUdyb3Vwcy5sYXVuY2hUZW1wbGF0ZS52ZXJzaW9uIgogICAgICAgICAgOm9wdGlvbnM9ImxhdW5jaFRlbXBsYXRlVmVyc2lvbk9wdGlvbnMiCiAgICAgICAgICBkYXRhLXRlc3RpZD0iZWtzLWxhdW5jaC10ZW1wbGF0ZS12ZXJzaW9uLWRyb3Bkb3duIgogICAgICAgICAgQHVwZGF0ZTp2YWx1ZT0iJGVtaXQoJ3VwZGF0ZTpsYXVuY2hUZW1wbGF0ZScsIHsuLi5sYXVuY2hUZW1wbGF0ZSwgdmVyc2lvbjogJGV2ZW50fSkiCiAgICAgICAgLz4KICAgICAgPC9kaXY+CiAgICA8L2Rpdj4KICAgIDxCYW5uZXIKICAgICAgY29sb3I9ImluZm8iCiAgICAgIGxhYmVsLWtleT0iZWtzLm5vZGVHcm91cHMuaW1hZ2VJZC50b29sdGlwIgogICAgLz4KICAgIDxkaXYgY2xhc3M9InJvdyBtYi0xMCI+CiAgICAgIDxkaXYgY2xhc3M9ImNvbCBzcGFuLTQiPgogICAgICAgIDxMYWJlbGVkSW5wdXQKICAgICAgICAgIGxhYmVsLWtleT0iZWtzLm5vZGVHcm91cHMuaW1hZ2VJZC5sYWJlbCIKICAgICAgICAgIDptb2RlPSJtb2RlIgogICAgICAgICAgOnZhbHVlPSJpbWFnZUlkIgogICAgICAgICAgOmRpc2FibGVkPSJoYXNVc2VyTGF1bmNoVGVtcGxhdGUiCiAgICAgICAgICBkYXRhLXRlc3RpZD0iZWtzLWltYWdlLWlkLWlucHV0IgogICAgICAgICAgQHVwZGF0ZTp2YWx1ZT0iJGVtaXQoJ3VwZGF0ZTppbWFnZUlkJywgJGV2ZW50KSIKICAgICAgICAvPgogICAgICA8L2Rpdj4KICAgICAgPGRpdiBjbGFzcz0iY29sIHNwYW4tNCI+CiAgICAgICAgPExhYmVsZWRTZWxlY3QKICAgICAgICAgIDpyZXF1aXJlZD0iIXJlcXVlc3RTcG90SW5zdGFuY2VzICYmICF0ZW1wbGF0ZVZhbHVlKCdpbnN0YW5jZVR5cGUnKSIKICAgICAgICAgIDptb2RlPSJtb2RlIgogICAgICAgICAgbGFiZWwta2V5PSJla3Mubm9kZUdyb3Vwcy5pbnN0YW5jZVR5cGUubGFiZWwiCiAgICAgICAgICA6b3B0aW9ucz0iaW5zdGFuY2VUeXBlT3B0aW9ucyIKICAgICAgICAgIDpsb2FkaW5nPSJsb2FkaW5nSW5zdGFuY2VUeXBlcyIKICAgICAgICAgIDp2YWx1ZT0iaW5zdGFuY2VUeXBlIgogICAgICAgICAgOmRpc2FibGVkPSIhIXRlbXBsYXRlVmFsdWUoJ2luc3RhbmNlVHlwZScpIHx8IHJlcXVlc3RTcG90SW5zdGFuY2VzIgogICAgICAgICAgOnRvb2x0aXA9IihyZXF1ZXN0U3BvdEluc3RhbmNlcyAmJiAhdGVtcGxhdGVWYWx1ZSgnaW5zdGFuY2VUeXBlJykpID8gdCgnZWtzLm5vZGVHcm91cHMuaW5zdGFuY2VUeXBlLnRvb2x0aXAnKTogJyciCiAgICAgICAgICA6cnVsZXM9IiFyZXF1ZXN0U3BvdEluc3RhbmNlcyA/IHJ1bGVzLmluc3RhbmNlVHlwZSA6IFtdIgogICAgICAgICAgZGF0YS10ZXN0aWQ9ImVrcy1pbnN0YW5jZS10eXBlLWRyb3Bkb3duIgogICAgICAgICAgQHVwZGF0ZTp2YWx1ZT0iJGVtaXQoJ3VwZGF0ZTppbnN0YW5jZVR5cGUnLCAkZXZlbnQpIgogICAgICAgIC8+CiAgICAgIDwvZGl2PgoKICAgICAgPGRpdiBjbGFzcz0iY29sIHNwYW4tNCI+CiAgICAgICAgPFVuaXRJbnB1dAogICAgICAgICAgOnJlcXVpcmVkPSIhdGVtcGxhdGVWYWx1ZSgnZGlza1NpemUnKSIKICAgICAgICAgIGxhYmVsLWtleT0iZWtzLm5vZGVHcm91cHMuZGlza1NpemUubGFiZWwiCiAgICAgICAgICA6bW9kZT0ibW9kZSIKICAgICAgICAgIDp2YWx1ZT0iZGlza1NpemUiCiAgICAgICAgICBzdWZmaXg9IkdCIgogICAgICAgICAgOmxvYWRpbmc9ImxvYWRpbmdTZWxlY3RlZFZlcnNpb24iCiAgICAgICAgICA6ZGlzYWJsZWQ9IiEhdGVtcGxhdGVWYWx1ZSgnZGlza1NpemUnKSB8fCBsb2FkaW5nU2VsZWN0ZWRWZXJzaW9uIgogICAgICAgICAgOnJ1bGVzPSJydWxlcy5kaXNrU2l6ZSIKICAgICAgICAgIGRhdGEtdGVzdGlkPSJla3MtZGlza3NpemUtaW5wdXQiCiAgICAgICAgICBAdXBkYXRlOnZhbHVlPSIkZW1pdCgndXBkYXRlOmRpc2tTaXplJywgJGV2ZW50KSIKICAgICAgICAvPgogICAgICA8L2Rpdj4KICAgIDwvZGl2PgogICAgPEJhbm5lcgogICAgICB2LWlmPSJyZXF1ZXN0U3BvdEluc3RhbmNlcyAmJiBoYXNVc2VyTGF1bmNoVGVtcGxhdGUiCiAgICAgIGNvbG9yPSJ3YXJuaW5nIgogICAgICA6bGFiZWw9InQoJ2Vrcy5ub2RlR3JvdXBzLnJlcXVlc3RTcG90SW5zdGFuY2VzLndhcm5pbmcnKSIKICAgICAgZGF0YS10ZXN0aWQ9ImVrcy1zcG90LWluc3RhbmNlLWJhbm5lciIKICAgIC8+CiAgICA8ZGl2IGNsYXNzPSJyb3cgbWItMTAiPgogICAgICA8ZGl2IGNsYXNzPSJjb2wgc3Bhbi00Ij4KICAgICAgICA8Q2hlY2tib3gKICAgICAgICAgIDptb2RlPSJtb2RlIgogICAgICAgICAgbGFiZWwta2V5PSJla3Mubm9kZUdyb3Vwcy5ncHUubGFiZWwiCiAgICAgICAgICA6dmFsdWU9ImdwdSIKICAgICAgICAgIDpkaXNhYmxlZD0iISF0ZW1wbGF0ZVZhbHVlKCdpbWFnZUlkJykgfHwgaGFzUmFuY2hlckxhdW5jaFRlbXBsYXRlIgogICAgICAgICAgOnRvb2x0aXA9InRlbXBsYXRlVmFsdWUoJ2ltYWdlSWQnKSA/IHQoJ2Vrcy5ub2RlR3JvdXBzLmdwdS50b29sdGlwJykgOiAnJyIKICAgICAgICAgIGRhdGEtdGVzdGlkPSJla3MtZ3B1LWlucHV0IgogICAgICAgICAgQHVwZGF0ZTp2YWx1ZT0iJGVtaXQoJ3VwZGF0ZTpncHUnLCAkZXZlbnQpIgogICAgICAgIC8+CiAgICAgIDwvZGl2PgogICAgICA8ZGl2IGNsYXNzPSJjb2wgc3Bhbi00Ij4KICAgICAgICA8Q2hlY2tib3gKICAgICAgICAgIDp2YWx1ZT0icmVxdWVzdFNwb3RJbnN0YW5jZXMiCiAgICAgICAgICA6bW9kZT0ibW9kZSIKICAgICAgICAgIGxhYmVsLWtleT0iZWtzLm5vZGVHcm91cHMucmVxdWVzdFNwb3RJbnN0YW5jZXMubGFiZWwiCiAgICAgICAgICA6ZGlzYWJsZWQ9Imhhc1JhbmNoZXJMYXVuY2hUZW1wbGF0ZSIKICAgICAgICAgIEB1cGRhdGU6dmFsdWU9IiRlbWl0KCd1cGRhdGU6cmVxdWVzdFNwb3RJbnN0YW5jZXMnLCAkZXZlbnQpIgogICAgICAgIC8+CiAgICAgIDwvZGl2PgogICAgPC9kaXY+CiAgICA8ZGl2CiAgICAgIHYtaWY9InJlcXVlc3RTcG90SW5zdGFuY2VzICYmICF0ZW1wbGF0ZVZhbHVlKCdpbnN0YW5jZVR5cGUnKSIKICAgICAgY2xhc3M9InJvdyBtYi0xMCIKICAgID4KICAgICAgPGRpdgogICAgICAgIGNsYXNzPSJjb2wgc3Bhbi02IgogICAgICA+CiAgICAgICAgPExhYmVsZWRTZWxlY3QKICAgICAgICAgIDptb2RlPSJtb2RlIgogICAgICAgICAgOnZhbHVlPSJzcG90SW5zdGFuY2VUeXBlcyIKICAgICAgICAgIGxhYmVsLWtleT0iZWtzLm5vZGVHcm91cHMuc3BvdEluc3RhbmNlVHlwZXMubGFiZWwiCiAgICAgICAgICA6b3B0aW9ucz0ic3BvdEluc3RhbmNlVHlwZU9wdGlvbnMiCiAgICAgICAgICA6bXVsdGlwbGU9InRydWUiCiAgICAgICAgICA6bG9hZGluZz0ibG9hZGluZ1NlbGVjdGVkVmVyc2lvbiB8fCBsb2FkaW5nSW5zdGFuY2VUeXBlcyIKICAgICAgICAgIGRhdGEtdGVzdGlkPSJla3Mtc3BvdC1pbnN0YW5jZS10eXBlLWRyb3Bkb3duIgogICAgICAgICAgQHVwZGF0ZTp2YWx1ZT0iJGVtaXQoJ3VwZGF0ZTpzcG90SW5zdGFuY2VUeXBlcycsICRldmVudCkiCiAgICAgICAgLz4KICAgICAgPC9kaXY+CiAgICA8L2Rpdj4KICAgIDxkaXYgY2xhc3M9InJvdyBtYi0xNSI+CiAgICAgIDxkaXYgY2xhc3M9ImNvbCBzcGFuLTYgdXNlci1kYXRhIj4KICAgICAgICA8TGFiZWxlZElucHV0CiAgICAgICAgICBsYWJlbC1rZXk9ImVrcy5ub2RlR3JvdXBzLnVzZXJEYXRhLmxhYmVsIgogICAgICAgICAgOm1vZGU9Im1vZGUiCiAgICAgICAgICB0eXBlPSJtdWx0aWxpbmUiCiAgICAgICAgICA6dmFsdWU9InVzZXJEYXRhIgogICAgICAgICAgOmRpc2FibGVkPSJoYXNVc2VyTGF1bmNoVGVtcGxhdGUiCiAgICAgICAgICA6cGxhY2Vob2xkZXI9InVzZXJEYXRhUGxhY2Vob2xkZXIiCiAgICAgICAgICA6c3ViLWxhYmVsPSJ0KCdla3Mubm9kZUdyb3Vwcy51c2VyRGF0YS50b29sdGlwJywge30sIHRydWUpIgogICAgICAgICAgQHVwZGF0ZTp2YWx1ZT0iJGVtaXQoJ3VwZGF0ZTp1c2VyRGF0YScsICRldmVudCkiCiAgICAgICAgLz4KICAgICAgICA8RmlsZVNlbGVjdG9yCiAgICAgICAgICA6bW9kZT0ibW9kZSIKICAgICAgICAgIDpsYWJlbD0idCgnZ2VuZXJpYy5yZWFkRnJvbUZpbGUnKSIKICAgICAgICAgIGNsYXNzPSJyb2xlLXRlcnRpYXJ5IG10LTIwIgogICAgICAgICAgQHNlbGVjdGVkPSIkZW1pdCgndXBkYXRlOnVzZXJEYXRhJywgJGV2ZW50KSIKICAgICAgICAvPgogICAgICA8L2Rpdj4KICAgICAgPGRpdiBjbGFzcz0iY29sIHNwYW4tNiI+CiAgICAgICAgPExhYmVsZWRTZWxlY3QKICAgICAgICAgIDpsb2FkaW5nPSJsb2FkaW5nU3NoS2V5UGFpcnMiCiAgICAgICAgICA6dmFsdWU9ImVjMlNzaEtleSIKICAgICAgICAgIDpvcHRpb25zPSJzc2hLZXlQYWlycyIKICAgICAgICAgIGxhYmVsLWtleT0iZWtzLm5vZGVHcm91cHMuZWMyU3NoS2V5LmxhYmVsIgogICAgICAgICAgOm1vZGU9Im1vZGUiCiAgICAgICAgICA6ZGlzYWJsZWQ9Imhhc1VzZXJMYXVuY2hUZW1wbGF0ZSIKICAgICAgICAgIDp0YWdnYWJsZT0idHJ1ZSIKICAgICAgICAgIDpzZWFyY2hhYmxlPSJ0cnVlIgogICAgICAgICAgZGF0YS10ZXN0aWQ9ImVrcy1ub2RlZ3JvdXAtZWMyLWtleS1zZWxlY3QiCiAgICAgICAgICBAdXBkYXRlOnZhbHVlPSIkZW1pdCgndXBkYXRlOmVjMlNzaEtleScsICRldmVudCkiCiAgICAgICAgLz4KICAgICAgPC9kaXY+CiAgICA8L2Rpdj4KICAgIDxkaXYgcm93PSJtYi0xMCI+CiAgICAgIDxkaXYgY2xhc3M9ImNvbCBzcGFuLTEyIG10LTIwIj4KICAgICAgICA8S2V5VmFsdWUKICAgICAgICAgIDptb2RlPSJtb2RlIgogICAgICAgICAgbGFiZWwta2V5PSJla3Mubm9kZUdyb3Vwcy5yZXNvdXJjZVRhZ3MubGFiZWwiCiAgICAgICAgICA6dmFsdWU9InJlc291cmNlVGFncyIKICAgICAgICAgIDpkaXNhYmxlZD0iaGFzVXNlckxhdW5jaFRlbXBsYXRlIgogICAgICAgICAgOnJlYWQtYWxsb3dlZD0iZmFsc2UiCiAgICAgICAgICA6YXMtbWFwPSJ0cnVlIgogICAgICAgICAgQHVwZGF0ZTp2YWx1ZT0iJGVtaXQoJ3VwZGF0ZTpyZXNvdXJjZVRhZ3MnLCAkZXZlbnQpIgogICAgICAgID4KICAgICAgICAgIDx0ZW1wbGF0ZSAjdGl0bGU+CiAgICAgICAgICAgIDxoND4KICAgICAgICAgICAgICB7eyB0KCdla3Mubm9kZUdyb3Vwcy5yZXNvdXJjZVRhZ3MubGFiZWwnKSB9fQogICAgICAgICAgICA8L2g0PgogICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICA8L0tleVZhbHVlPgogICAgICA8L2Rpdj4KICAgIDwvZGl2PgogIDwvZGl2Pgo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/NodeGroup.vue"], "names": [], "mappings": ";EA+hBE,CAAC,CAAC,CAAC,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9D,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;IACD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC;cACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC5C,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC;IACD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrF,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C,CAAC;IACD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACjH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;UACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC;IACD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;UACzE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7D,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB;MACE,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB;QACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC;cACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC7C,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/NodeGroup.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent, PropType } from 'vue';\nimport { mapGetters, Store } from 'vuex';\nimport debounce from 'lodash/debounce';\n\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\nimport { randomStr } from '@shell/utils/string';\nimport { isEmpty } from '@shell/utils/object';\n\nimport LabeledSelect from '@shell/components/form/LabeledSelect.vue';\nimport LabeledInput from '@components/Form/LabeledInput/LabeledInput.vue';\nimport Checkbox from '@components/Form/Checkbox/Checkbox.vue';\nimport KeyValue from '@shell/components/form/KeyValue.vue';\nimport Banner from '@components/Banner/Banner.vue';\nimport UnitInput from '@shell/components/form/UnitInput.vue';\nimport FileSelector from '@shell/components/form/FileSelector.vue';\n\nimport { MANAGED_TEMPLATE_PREFIX, parseTags } from '../../util/aws';\nimport { AWS } from '../../types';\nimport { DEFAULT_NODE_GROUP_CONFIG } from './CruEKS.vue';\n\n// map between fields in rancher eksConfig and amazon launch templates\nconst launchTemplateFieldMapping: {[key: string]: string} = {\n  imageId:      'ImageId',\n  userData:     'UserData',\n  instanceType: 'InstanceType',\n  ec2SshKey:    '',\n  resourceTags: 'TagSpecifications',\n  diskSize:     'BlockDeviceMappings'\n};\n\nconst DEFAULT_USER_DATA =\n`MIME-Version: 1.0\nContent-Type: multipart/mixed; boundary=\"==MYBOUNDARY==\"\n\n--==MYBOUNDARY==\nContent-Type: text/x-shellscript; charset=\"us-ascii\"\n\n#!/bin/bash\necho \"Running custom user data script\"\n\n--==MYBOUNDARY==--\\\\`;\n\nexport default defineComponent({\n  name: 'EKSNodePool',\n\n  emits: ['update:instanceType', 'update:spotInstanceTypes', 'update:ec2SshKey', 'update:launchTemplate', 'update:nodeRole', 'update:nodeRole', 'update:version', 'update:poolIsUpgrading', 'error', 'update:resourceTags', 'update:diskSize', 'update:nodegroupName', 'update:desiredSize', 'update:minSize', 'update:maxSize', 'update:labels', 'update:tags', 'update:imageId', 'update:gpu', 'update:requestSpotInstances', 'update:userData', 'update:ec2SshKey'],\n\n  components: {\n    LabeledInput,\n    LabeledSelect,\n    KeyValue,\n    Banner,\n    Checkbox,\n    UnitInput,\n    FileSelector\n  },\n\n  props: {\n    nodeRole: {\n      type:    String,\n      default: ''\n    },\n    resourceTags: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    requestSpotInstances: {\n      type:    Boolean,\n      default: false\n    },\n    spotInstanceTypes: {\n      type:    Array,\n      default: () => []\n    },\n    labels: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    tags: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    gpu: {\n      type:    Boolean,\n      default: false\n    },\n    userData: {\n      type:    String,\n      default: ''\n    },\n    instanceType: {\n      type:    String,\n      default: ''\n    },\n    imageId: {\n      type:    [String, null],\n      default: ''\n    },\n    desiredSize: {\n      type:    [Number, String],\n      default: null\n    },\n    minSize: {\n      type:    [Number, String],\n      default: null\n    },\n    maxSize: {\n      type:    [Number, String],\n      default: null\n    },\n    diskSize: {\n      type:    Number,\n      default: null\n    },\n    ec2SshKey: {\n      type:    String,\n      default: ''\n    },\n    nodegroupName: {\n      type:    String,\n      default: ''\n    },\n    region: {\n      type:    String,\n      default: ''\n    },\n    amazonCredentialSecret: {\n      type:    String,\n      default: ''\n    },\n\n    launchTemplate: {\n      type:    Object,\n      default: () => {}\n    },\n\n    version: {\n      type:    String,\n      default: ''\n    },\n\n    clusterVersion: {\n      type:    String,\n      default: ''\n    },\n\n    originalClusterVersion: {\n      type:    String,\n      default: ''\n    },\n\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n\n    ec2Roles: {\n      type:    Array as PropType<AWS.IamRole[]>,\n      default: () => []\n    },\n\n    isNewOrUnprovisioned: {\n      type:    Boolean,\n      default: true\n    },\n\n    poolIsNew: {\n      type:    Boolean,\n      default: false\n    },\n\n    instanceTypeOptions: {\n      type:    Array,\n      default: () => []\n    },\n\n    spotInstanceTypeOptions: {\n      type:    Array,\n      default: () => []\n    },\n\n    launchTemplates: {\n      type:    Array as PropType<AWS.LaunchTemplate[]>,\n      default: () => []\n    },\n\n    sshKeyPairs: {\n      type:    Array as PropType<string[]>,\n      default: () => []\n    },\n\n    normanCluster: {\n      type:    Object,\n      default: null\n    },\n\n    poolIsUpgrading: {\n      type:    Boolean,\n      default: false\n    },\n\n    loadingInstanceTypes: {\n      type:    Boolean,\n      default: false\n    },\n\n    loadingRoles: {\n      type:    Boolean,\n      default: false\n    },\n\n    loadingLaunchTemplates: {\n      type:    Boolean,\n      default: false\n    },\n\n    loadingSshKeyPairs: {\n      type:    Boolean,\n      default: false\n    },\n\n    rules: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  created() {\n    this.debouncedSetValuesFromTemplate = debounce(this.setValuesFromTemplate, 500);\n  },\n\n  data() {\n    const store = this.$store as Store<any>;\n    const t = store.getters['i18n/t'];\n\n    return {\n      originalNodeVersion:   this.version,\n      defaultTemplateOption: { LaunchTemplateName: t('eks.defaultCreateOne') } as AWS.LaunchTemplate,\n\n      defaultNodeRoleOption:          { RoleName: t('eks.defaultCreateOne') },\n      loadingSelectedVersion:         false,\n      // once a specific lt has been selected, an additional query is made to get full information on every version of it\n      selectedLaunchTemplateInfo:     {} as AWS.LaunchTemplateDetail,\n      debouncedSetValuesFromTemplate: null as Function | null,\n      // the keyvalue component needs to be re-rendered if the value prop is updated by parent component when as-map=true\n      // TODO nb file an issue\n      resourceTagKey:                 randomStr()\n    };\n  },\n\n  watch: {\n    selectedLaunchTemplate: {\n      handler(neu) {\n        if (neu && neu.LaunchTemplateId && this.amazonCredentialSecret) {\n          this.fetchLaunchTemplateVersionInfo(this.selectedLaunchTemplate);\n        }\n      },\n      immediate: true\n    },\n\n    amazonCredentialSecret: {\n      handler() {\n        this.fetchLaunchTemplateVersionInfo(this.selectedLaunchTemplate);\n      },\n      immediate: true\n    },\n\n    'selectedVersionData'(neu = {}, old = {}) {\n      this.loadingSelectedVersion = true;\n      if (this.debouncedSetValuesFromTemplate) {\n        this.debouncedSetValuesFromTemplate(neu, old);\n      }\n    },\n\n    'requestSpotInstances'(neu) {\n      if (neu && !this.templateValue('instanceType')) {\n        this.$emit('update:instanceType', null);\n      } else {\n        this.$emit('update:spotInstanceTypes', null);\n      }\n    },\n\n    sshKeyPairs: {\n      handler(neu) {\n        if (!neu.includes(this.ec2SshKey)) {\n          this.$emit('update:ec2SshKey', '');\n        }\n      },\n      deep: true\n    }\n  },\n\n  computed: {\n    ...mapGetters({ t: 'i18n/t' }),\n\n    rancherTemplate() {\n      const eksStatus = this.normanCluster?.eksStatus || {};\n\n      return eksStatus.managedLaunchTemplateID;\n    },\n\n    hasRancherLaunchTemplate() {\n      const eksStatus = this.normanCluster?.eksStatus || {};\n      const nodegroupName = this.nodegroupName;\n      const nodeGroupTemplateVersion = (eksStatus?.managedLaunchTemplateVersions || {})[nodegroupName];\n\n      return isEmpty(this.launchTemplate) && !isEmpty(eksStatus.managedLaunchTemplateID) && !isEmpty(nodeGroupTemplateVersion);\n    },\n\n    hasUserLaunchTemplate() {\n      const { launchTemplate = {} } = this;\n\n      return !!launchTemplate?.id && !!launchTemplate?.version;\n    },\n\n    hasNoLaunchTemplate() {\n      return !this.hasRancherLaunchTemplate && !this.hasUserLaunchTemplate;\n    },\n\n    launchTemplateOptions(): AWS.LaunchTemplate[] {\n      return [this.defaultTemplateOption, ...this.launchTemplates.filter((template) => !(template?.LaunchTemplateName || '').startsWith(MANAGED_TEMPLATE_PREFIX))];\n    },\n\n    selectedLaunchTemplate: {\n      get(): AWS.LaunchTemplate {\n        if (this.hasRancherLaunchTemplate) {\n          return { LaunchTemplateId: this.rancherTemplate, LaunchTemplateName: this.t('eks.nodeGroups.launchTemplate.rancherManaged', { name: this.rancherTemplate }) };\n        }\n        const id = this.launchTemplate?.id;\n\n        return this.launchTemplateOptions.find((lt: AWS.LaunchTemplate) => lt.LaunchTemplateId && lt.LaunchTemplateId === id) || this.defaultTemplateOption;\n      },\n      set(neu: AWS.LaunchTemplate) {\n        if (neu.LaunchTemplateName === this.defaultTemplateOption.LaunchTemplateName) {\n          this.$emit('update:launchTemplate', {});\n\n          return;\n        }\n        const name = neu.LaunchTemplateName;\n        const id = neu.LaunchTemplateId;\n        const version = neu.DefaultVersionNumber;\n\n        this.$emit('update:launchTemplate', {\n          name, id, version\n        });\n      }\n    },\n\n    launchTemplateVersionOptions(): number[] {\n      if (this.selectedLaunchTemplateInfo && this.selectedLaunchTemplateInfo?.LaunchTemplateVersions) {\n        return this.selectedLaunchTemplateInfo.LaunchTemplateVersions.map((version) => version.VersionNumber).sort();\n      }\n\n      return [];\n    },\n\n    selectedVersionInfo(): AWS.LaunchTemplateVersion | null {\n      return (this.selectedLaunchTemplateInfo?.LaunchTemplateVersions || []).find((v: any) => v.VersionNumber === this.launchTemplate?.version) || null;\n    },\n\n    selectedVersionData(): AWS.LaunchTemplateVersionData | undefined {\n      return this.selectedVersionInfo?.LaunchTemplateData;\n    },\n\n    displayNodeRole: {\n      get() {\n        const arn = this.nodeRole;\n\n        if (!arn) {\n          return this.defaultNodeRoleOption;\n        }\n\n        return this.ec2Roles.find((role: AWS.IamRole) => role.Arn === arn) ;\n      },\n      set(neu: AWS.IamRole) {\n        if (neu.Arn) {\n          this.$emit('update:nodeRole', neu.Arn);\n        } else {\n          this.$emit('update:nodeRole', '');\n        }\n      }\n    },\n\n    userDataPlaceholder() {\n      return DEFAULT_USER_DATA;\n    },\n\n    poolIsUnprovisioned() {\n      return this.isNewOrUnprovisioned || this.poolIsNew;\n    },\n\n    clusterWillUpgrade() {\n      return this.clusterVersion !== this.originalClusterVersion;\n    },\n\n    nodeCanUpgrade() {\n      return !this.clusterWillUpgrade && this.originalNodeVersion !== this.clusterVersion && !this.poolIsNew;\n    },\n\n    willUpgrade: {\n      get() {\n        return this.nodeCanUpgrade && this.version === this.clusterVersion;\n      },\n      set(neu: boolean) {\n        if (neu) {\n          this.$emit('update:version', this.clusterVersion);\n          this.$emit('update:poolIsUpgrading', true);\n        } else {\n          this.$emit('update:version', this.originalNodeVersion);\n          this.$emit('update:poolIsUpgrading', false);\n        }\n      }\n    },\n\n    minMaxDesiredErrors() {\n      const errs = (this.rules?.minMaxDesired || []).reduce((errs: string[], rule: Function) => {\n        const err = rule({\n          minSize: this.minSize, maxSize: this.maxSize, desiredSize: this.desiredSize\n        });\n\n        if (err) {\n          errs.push(err);\n        }\n\n        return errs;\n      }, [] as string[]);\n\n      return errs.length ? errs.join(' ') : null;\n    },\n\n    isView() {\n      return this.mode === _VIEW;\n    }\n  },\n\n  methods: {\n    async fetchLaunchTemplateVersionInfo(launchTemplate: AWS.LaunchTemplate) {\n      const { region, amazonCredentialSecret } = this;\n\n      if (!region || !amazonCredentialSecret || this.isView) {\n        return;\n      }\n      const store = this.$store as Store<any>;\n      const ec2Client = await store.dispatch('aws/ec2', { region, cloudCredentialId: amazonCredentialSecret });\n\n      try {\n        if (launchTemplate.LaunchTemplateName !== this.defaultTemplateOption.LaunchTemplateName) {\n          if (launchTemplate.LaunchTemplateId) {\n            this.selectedLaunchTemplateInfo = await ec2Client.describeLaunchTemplateVersions({ LaunchTemplateId: launchTemplate.LaunchTemplateId });\n          } else {\n            this.selectedLaunchTemplateInfo = await ec2Client.describeLaunchTemplateVersions({ LaunchTemplateName: launchTemplate.LaunchTemplateName });\n          }\n        }\n      } catch (err) {\n        this.$emit('error', err);\n      }\n    },\n\n    setValuesFromTemplate(neu = {} as AWS.LaunchTemplateVersionData, old = {} as AWS.LaunchTemplateVersionData) {\n      Object.keys(launchTemplateFieldMapping).forEach((rancherKey: string) => {\n        const awsKey = launchTemplateFieldMapping[rancherKey];\n\n        if (awsKey === 'TagSpecifications') {\n          const { TagSpecifications } = neu;\n\n          if (TagSpecifications) {\n            const tags = {} as {[key:string]: string};\n\n            TagSpecifications.forEach((tag: {Tags?: {Key: string, Value: string}[], ResourceType?: string}) => {\n              if (tag.ResourceType === 'instance' && tag.Tags && tag.Tags.length) {\n                Object.assign(tags, parseTags(tag.Tags));\n              }\n            });\n            this.$emit('update:resourceTags', tags);\n          } else {\n            this.$emit('update:resourceTags', { ...DEFAULT_NODE_GROUP_CONFIG.resourceTags });\n          }\n        } else if (awsKey === 'BlockDeviceMappings') {\n          const { BlockDeviceMappings } = neu;\n\n          if (BlockDeviceMappings && BlockDeviceMappings.length) {\n            const size = BlockDeviceMappings[0]?.Ebs?.VolumeSize;\n\n            this.$emit('update:diskSize', size);\n          } else {\n            this.$emit('update:diskSize', DEFAULT_NODE_GROUP_CONFIG.diskSize);\n          }\n        } else if (this.templateValue(rancherKey)) {\n          this.$emit(`update:${ rancherKey }`, this.templateValue(rancherKey));\n        } else {\n          this.$emit(`update:${ rancherKey }`, DEFAULT_NODE_GROUP_CONFIG[rancherKey as keyof typeof DEFAULT_NODE_GROUP_CONFIG]);\n        }\n      });\n\n      this.$nextTick(() => {\n        this.resourceTagKey = randomStr();\n        this.loadingSelectedVersion = false;\n      });\n    },\n\n    templateValue(field: string): string | null | AWS.TagSpecification | AWS.TagSpecification[] | AWS.BlockDeviceMapping[] {\n      if (this.hasNoLaunchTemplate) {\n        return null;\n      }\n\n      const launchTemplateKey = launchTemplateFieldMapping[field] as keyof AWS.LaunchTemplateVersionData;\n\n      if (!launchTemplateKey) {\n        return null;\n      }\n      const launchTemplateVal = this.selectedVersionData?.[launchTemplateKey];\n\n      if (launchTemplateVal !== undefined && (!(typeof launchTemplateVal === 'object') || !isEmpty(launchTemplateVal))) {\n        if (field === 'diskSize') {\n          const blockMapping = launchTemplateVal[0] as AWS.BlockDeviceMapping;\n\n          return blockMapping?.Ebs?.VolumeSize || null;\n        }\n        if (field === 'resourceTags') {\n          const tags = (launchTemplateVal || []) as AWS.TagSpecification[];\n\n          return tags.filter((tag: AWS.TagSpecification) => tag.ResourceType === 'instance')[0];\n        }\n\n        return launchTemplateVal;\n      }\n\n      return null;\n    },\n  },\n});\n</script>\n\n<template>\n  <div>\n    <h3>{{ t('eks.nodeGroups.groupDetails') }}</h3>\n    <div class=\"row mb-10\">\n      <div class=\"col span-6\">\n        <LabeledInput\n          :value=\"nodegroupName\"\n          label-key=\"eks.nodeGroups.name.label\"\n          :mode=\"mode\"\n          :disabled=\"!poolIsUnprovisioned\"\n          :rules=\"rules.nodegroupName\"\n          data-testid=\"eks-nodegroup-name\"\n          required\n          @update:value=\"$emit('update:nodegroupName', $event)\"\n        />\n      </div>\n\n      <div class=\"col span-6\">\n        <LabeledSelect\n          v-model:value=\"displayNodeRole\"\n          :mode=\"mode\"\n          data-testid=\"eks-noderole\"\n          label-key=\"eks.nodeGroups.nodeRole.label\"\n          :options=\"[defaultNodeRoleOption, ...ec2Roles]\"\n          option-label=\"RoleName\"\n          option-key=\"Arn\"\n          :disabled=\"!poolIsUnprovisioned\"\n          :loading=\"loadingRoles\"\n        />\n      </div>\n    </div>\n    <div class=\"row mb-10\">\n      <div class=\"col span-4\">\n        <LabeledInput\n          type=\"number\"\n          :value=\"desiredSize\"\n          label-key=\"eks.nodeGroups.desiredSize.label\"\n          :mode=\"mode\"\n          :rules=\"rules.desiredSize\"\n          @update:value=\"$emit('update:desiredSize', parseInt($event))\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <LabeledInput\n          type=\"number\"\n          :value=\"minSize\"\n          label-key=\"eks.nodeGroups.minSize.label\"\n          :mode=\"mode\"\n          :rules=\"rules.minSize\"\n          @update:value=\"$emit('update:minSize', parseInt($event))\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <LabeledInput\n          type=\"number\"\n          :value=\"maxSize\"\n          label-key=\"eks.nodeGroups.maxSize.label\"\n          :mode=\"mode\"\n          :rules=\"rules.maxSize\"\n          @update:value=\"$emit('update:maxSize', parseInt($event))\"\n        />\n      </div>\n    </div>\n    <Banner\n      v-if=\"!!minMaxDesiredErrors\"\n      color=\"error\"\n      :label=\"minMaxDesiredErrors\"\n    />\n    <div class=\"row mb-10\">\n      <div class=\"col span-6 mt-20\">\n        <KeyValue\n          :mode=\"mode\"\n          :title=\"t('eks.nodeGroups.groupLabels.label')\"\n          :read-allowed=\"false\"\n          :value=\"labels\"\n          :as-map=\"true\"\n          @update:value=\"$emit('update:labels', $event)\"\n        >\n          <template #title>\n            <h4>\n              {{ t('eks.nodeGroups.groupLabels.label') }}\n            </h4>\n          </template>\n        </KeyValue>\n      </div>\n      <div class=\"col span-6 mt-20\">\n        <KeyValue\n          :mode=\"mode\"\n          :title=\"t('eks.nodeGroups.groupTags.label')\"\n          :read-allowed=\"false\"\n          :as-map=\"true\"\n          :value=\"tags\"\n          data-testid=\"eks-resource-tags-input\"\n          @update:value=\"$emit('update:tags', $event)\"\n        >\n          <template #title>\n            <h4>{{ t('eks.nodeGroups.groupTags.label') }}</h4>\n          </template>\n        </KeyValue>\n      </div>\n    </div>\n    <hr\n      class=\"mb-20\"\n      role=\"none\"\n    >\n    <h3>{{ t('eks.nodeGroups.templateDetails') }}</h3>\n    <Banner\n      v-if=\"clusterWillUpgrade && !poolIsUnprovisioned\"\n      color=\"info\"\n      label-key=\"eks.nodeGroups.kubernetesVersion.clusterWillUpgrade\"\n      data-testid=\"eks-version-upgrade-banner\"\n    />\n    <div class=\"row mb-10\">\n      <div class=\"col span-4 upgrade-version\">\n        <LabeledInput\n          v-if=\"!nodeCanUpgrade\"\n          label-key=\"eks.nodeGroups.kubernetesVersion.label\"\n          :disabled=\"true\"\n          :value=\"version\"\n          data-testid=\"eks-version-display\"\n        />\n        <Checkbox\n          v-else\n          v-model:value=\"willUpgrade\"\n          :label=\"t('eks.nodeGroups.kubernetesVersion.upgrade', {from: originalNodeVersion, to: clusterVersion})\"\n          data-testid=\"eks-version-upgrade-checkbox\"\n          :disabled=\"isView\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <LabeledSelect\n          v-model:value=\"selectedLaunchTemplate\"\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.launchTemplate.label\"\n          :options=\"launchTemplateOptions\"\n          option-label=\"LaunchTemplateName\"\n          option-key=\"LaunchTemplateId\"\n          :disabled=\"!poolIsUnprovisioned\"\n          :loading=\"loadingLaunchTemplates\"\n          data-testid=\"eks-launch-template-dropdown\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <LabeledSelect\n          v-if=\"hasUserLaunchTemplate\"\n          :value=\"launchTemplate.version\"\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.launchTemplate.version\"\n          :options=\"launchTemplateVersionOptions\"\n          data-testid=\"eks-launch-template-version-dropdown\"\n          @update:value=\"$emit('update:launchTemplate', {...launchTemplate, version: $event})\"\n        />\n      </div>\n    </div>\n    <Banner\n      color=\"info\"\n      label-key=\"eks.nodeGroups.imageId.tooltip\"\n    />\n    <div class=\"row mb-10\">\n      <div class=\"col span-4\">\n        <LabeledInput\n          label-key=\"eks.nodeGroups.imageId.label\"\n          :mode=\"mode\"\n          :value=\"imageId\"\n          :disabled=\"hasUserLaunchTemplate\"\n          data-testid=\"eks-image-id-input\"\n          @update:value=\"$emit('update:imageId', $event)\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <LabeledSelect\n          :required=\"!requestSpotInstances && !templateValue('instanceType')\"\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.instanceType.label\"\n          :options=\"instanceTypeOptions\"\n          :loading=\"loadingInstanceTypes\"\n          :value=\"instanceType\"\n          :disabled=\"!!templateValue('instanceType') || requestSpotInstances\"\n          :tooltip=\"(requestSpotInstances && !templateValue('instanceType')) ? t('eks.nodeGroups.instanceType.tooltip'): ''\"\n          :rules=\"!requestSpotInstances ? rules.instanceType : []\"\n          data-testid=\"eks-instance-type-dropdown\"\n          @update:value=\"$emit('update:instanceType', $event)\"\n        />\n      </div>\n\n      <div class=\"col span-4\">\n        <UnitInput\n          :required=\"!templateValue('diskSize')\"\n          label-key=\"eks.nodeGroups.diskSize.label\"\n          :mode=\"mode\"\n          :value=\"diskSize\"\n          suffix=\"GB\"\n          :loading=\"loadingSelectedVersion\"\n          :disabled=\"!!templateValue('diskSize') || loadingSelectedVersion\"\n          :rules=\"rules.diskSize\"\n          data-testid=\"eks-disksize-input\"\n          @update:value=\"$emit('update:diskSize', $event)\"\n        />\n      </div>\n    </div>\n    <Banner\n      v-if=\"requestSpotInstances && hasUserLaunchTemplate\"\n      color=\"warning\"\n      :label=\"t('eks.nodeGroups.requestSpotInstances.warning')\"\n      data-testid=\"eks-spot-instance-banner\"\n    />\n    <div class=\"row mb-10\">\n      <div class=\"col span-4\">\n        <Checkbox\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.gpu.label\"\n          :value=\"gpu\"\n          :disabled=\"!!templateValue('imageId') || hasRancherLaunchTemplate\"\n          :tooltip=\"templateValue('imageId') ? t('eks.nodeGroups.gpu.tooltip') : ''\"\n          data-testid=\"eks-gpu-input\"\n          @update:value=\"$emit('update:gpu', $event)\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <Checkbox\n          :value=\"requestSpotInstances\"\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.requestSpotInstances.label\"\n          :disabled=\"hasRancherLaunchTemplate\"\n          @update:value=\"$emit('update:requestSpotInstances', $event)\"\n        />\n      </div>\n    </div>\n    <div\n      v-if=\"requestSpotInstances && !templateValue('instanceType')\"\n      class=\"row mb-10\"\n    >\n      <div\n        class=\"col span-6\"\n      >\n        <LabeledSelect\n          :mode=\"mode\"\n          :value=\"spotInstanceTypes\"\n          label-key=\"eks.nodeGroups.spotInstanceTypes.label\"\n          :options=\"spotInstanceTypeOptions\"\n          :multiple=\"true\"\n          :loading=\"loadingSelectedVersion || loadingInstanceTypes\"\n          data-testid=\"eks-spot-instance-type-dropdown\"\n          @update:value=\"$emit('update:spotInstanceTypes', $event)\"\n        />\n      </div>\n    </div>\n    <div class=\"row mb-15\">\n      <div class=\"col span-6 user-data\">\n        <LabeledInput\n          label-key=\"eks.nodeGroups.userData.label\"\n          :mode=\"mode\"\n          type=\"multiline\"\n          :value=\"userData\"\n          :disabled=\"hasUserLaunchTemplate\"\n          :placeholder=\"userDataPlaceholder\"\n          :sub-label=\"t('eks.nodeGroups.userData.tooltip', {}, true)\"\n          @update:value=\"$emit('update:userData', $event)\"\n        />\n        <FileSelector\n          :mode=\"mode\"\n          :label=\"t('generic.readFromFile')\"\n          class=\"role-tertiary mt-20\"\n          @selected=\"$emit('update:userData', $event)\"\n        />\n      </div>\n      <div class=\"col span-6\">\n        <LabeledSelect\n          :loading=\"loadingSshKeyPairs\"\n          :value=\"ec2SshKey\"\n          :options=\"sshKeyPairs\"\n          label-key=\"eks.nodeGroups.ec2SshKey.label\"\n          :mode=\"mode\"\n          :disabled=\"hasUserLaunchTemplate\"\n          :taggable=\"true\"\n          :searchable=\"true\"\n          data-testid=\"eks-nodegroup-ec2-key-select\"\n          @update:value=\"$emit('update:ec2SshKey', $event)\"\n        />\n      </div>\n    </div>\n    <div row=\"mb-10\">\n      <div class=\"col span-12 mt-20\">\n        <KeyValue\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.resourceTags.label\"\n          :value=\"resourceTags\"\n          :disabled=\"hasUserLaunchTemplate\"\n          :read-allowed=\"false\"\n          :as-map=\"true\"\n          @update:value=\"$emit('update:resourceTags', $event)\"\n        >\n          <template #title>\n            <h4>\n              {{ t('eks.nodeGroups.resourceTags.label') }}\n            </h4>\n          </template>\n        </KeyValue>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.user-data{\n  &>button{\n    float: right;\n  }\n}\n\n.upgrade-version {\n  display: flex;\n  align-items: center;\n}\n</style>\n"]}]}