{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Reference.vue?vue&type=template&id=57fe6436", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Reference.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Reference.vue"], "names": [], "mappings": ";EAkHE,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ;IACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtE,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ;IACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtE,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC3C,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Reference.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport ResourceLabeledSelect from '@shell/components/form/ResourceLabeledSelect.vue';\nimport { PaginationParamFilter } from '@shell/types/store/pagination.types';\n\nimport { PVC, STORAGE_CLASS } from '@shell/config/types';\nimport Question from './Question';\n\n// Older versions of rancher document these words as valid types\nconst LEGACY_MAP = {\n  storageclass: STORAGE_CLASS,\n  pvc:          PVC,\n};\n\nexport default {\n  emits: ['update:value'],\n\n  components: { LabeledInput, ResourceLabeledSelect },\n  mixins:     [Question],\n\n  props: {\n    inStore: {\n      type:    String,\n      default: 'cluster',\n    },\n\n    targetNamespace: {\n      type:    String,\n      default: null,\n    },\n  },\n\n  data() {\n    const t = this.question.type;\n\n    let typeName;\n\n    const match = t.match(/^reference\\[(.*)\\]$/);\n\n    if ( match ) {\n      typeName = match?.[1];\n    } else {\n      typeName = LEGACY_MAP[t] || t;\n    }\n\n    let typeSchema;\n\n    if ( typeName ) {\n      typeSchema = this.$store.getters[`${ this.inStore }/schemaFor`](typeName);\n    }\n\n    return {\n      typeName,\n      typeSchema,\n      all:                 [],\n      allResourceSettings: {\n        updateResources: (all) => {\n          // Filter to only include required namespaced resources\n          const resources = this.isNamespaced ? all.filter((r) => r.metadata.namespace === this.targetNamespace) : all;\n\n          return this.mapResourcesToOptions(resources);\n        }\n      },\n      paginateResourceSetting: {\n        updateResources: (resources) => {\n          return this.mapResourcesToOptions(resources);\n        },\n        /**\n          * of type PaginateTypeOverridesFn\n          * @param [LabelSelectPaginationFunctionOptions] opts\n          * @returns LabelSelectPaginationFunctionOptions\n         */\n        requestSettings: (opts) => {\n          // Filter to only include required namespaced resources\n          const filters = this.isNamespaced ? [\n            PaginationParamFilter.createSingleField({ field: 'metadata.namespace', value: this.targetNamespace }),\n          ] : [];\n\n          return {\n            ...opts,\n            filters,\n            groupByNamespace: false,\n            classify:         true,\n          };\n        }\n      },\n    };\n  },\n\n  methods: {\n    mapResourcesToOptions(resources) {\n      return resources.map((r) => {\n        if (r.id) {\n          return {\n            label: r.nameDisplay || r.metadata.name,\n            value: r.metadata.name\n          };\n        } else {\n          return r;\n        }\n      });\n    },\n\n  },\n\n  computed: {\n    isNamespaced() {\n      return !!this.typeSchema?.attributes?.namespaced;\n    },\n  },\n};\n</script>\n\n<template>\n  <div\n    v-if=\"typeSchema\"\n    class=\"row\"\n  >\n    <div class=\"col span-6\">\n      <ResourceLabeledSelect\n        :resource-type=\"typeName\"\n        :in-store=\"inStore\"\n        :disabled=\"$fetchState.pending || disabled\"\n        :label=\"displayLabel\"\n        :placeholder=\"question.description\"\n        :required=\"question.required\"\n        :value=\"value\"\n        :tooltip=\"displayTooltip\"\n        :paginated-resource-settings=\"paginateResourceSetting\"\n        :all-resources-settings=\"allResourceSettings\"\n        @update:value=\"!$fetchState.pending && $emit('update:value', $event)\"\n      />\n    </div>\n    <div class=\"col span-6 mt-10\">\n      {{ typeSchema.attributes.kind }}<span v-if=\"isNamespaced\"> in namespace {{ targetNamespace }}</span>\n      <div v-if=\"showDescription\">\n        {{ question.description }}\n      </div>\n    </div>\n  </div>\n  <div\n    v-else\n    class=\"row\"\n  >\n    <div class=\"col span-6\">\n      <LabeledInput\n        :mode=\"mode\"\n        :disabled=\"$fetchState.pending || disabled\"\n        :label=\"displayLabel\"\n        :placeholder=\"question.description\"\n        :required=\"question.required\"\n        :value=\"value\"\n        :tooltip=\"displayTooltip\"\n        @update:value=\"!$fetchState.pending && $emit('update:value', $event)\"\n      />\n    </div>\n    <div class=\"col span-6 mt-10\">\n      {{ question.type }}<span v-if=\"isNamespaced\"> in namespace {{ targetNamespace }}</span>\n      <div v-if=\"showDescription\">\n        {{ question.description }}\n      </div>\n      <div class=\"text-error\">\n        (You do not have access to list this type)\n      </div>\n    </div>\n  </div>\n</template>\n"]}]}