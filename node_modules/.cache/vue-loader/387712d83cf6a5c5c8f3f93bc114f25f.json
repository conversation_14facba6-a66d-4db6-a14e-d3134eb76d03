{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Accordion/Accordion.vue?vue&type=style&index=0&id=42964d6c&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Accordion/Accordion.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5hY2NvcmRpb24tY29udGFpbmVyIHsKICBib3JkZXI6IDFweCBzb2xpZCB2YXIoLS1ib3JkZXIpCn0KLmFjY29yZGlvbi1oZWFkZXIgewogIHBhZGRpbmc6IDE2cHggMTZweCAxNnB4IDExcHg7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogICY+KnsKICAgIHBhZGRpbmc6IDVweCAwcHggNXB4IDBweDsKICB9CiAgSSB7CiAgICBtYXJnaW46IDBweCAxMHB4IDBweCAxMHB4OwogIH0KfQouYWNjb3JkaW9uLWJvZHkgewogIHBhZGRpbmc6IDBweCAxNnB4IDE2cHg7Cn0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Accordion/Accordion.vue"], "names": [], "mappings": ";AAoEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC1B;EACA,EAAE;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC3B;AACF;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACxB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Accordion/Accordion.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent } from 'vue';\nimport { mapGetters } from 'vuex';\n\nexport default defineComponent({\n  props: {\n    title: {\n      type:    String,\n      default: ''\n    },\n\n    titleKey: {\n      type:    String,\n      default: null\n    },\n\n    openInitially: {\n      type:    Boolean,\n      default: false\n    }\n  },\n\n  data() {\n    return { isOpen: this.openInitially };\n  },\n\n  computed: { ...mapGetters({ t: 'i18n/t' }) },\n\n  methods: {\n    toggle() {\n      this.isOpen = !this.isOpen;\n    }\n  }\n});\n</script>\n\n<template>\n  <div class=\"accordion-container\">\n    <div\n      class=\"accordion-header\"\n      data-testid=\"accordion-header\"\n      @click=\"toggle\"\n    >\n      <i\n        class=\"icon text-primary\"\n        :class=\"{'icon-chevron-down':isOpen, 'icon-chevron-up':!isOpen}\"\n        data-testid=\"accordion-chevron\"\n      />\n      <slot name=\"header\">\n        <h2\n          data-testid=\"accordion-title-slot-content\"\n          class=\"mb-0\"\n        >\n          {{ titleKey ? t(titleKey) : title }}\n        </h2>\n      </slot>\n    </div>\n    <div\n      v-show=\"isOpen\"\n      class=\"accordion-body\"\n      data-testid=\"accordion-body\"\n    >\n      <slot />\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.accordion-container {\n  border: 1px solid var(--border)\n}\n.accordion-header {\n  padding: 16px 16px 16px 11px;\n  display: flex;\n  align-items: center;\n  &>*{\n    padding: 5px 0px 5px 0px;\n  }\n  I {\n    margin: 0px 10px 0px 10px;\n  }\n}\n.accordion-body {\n  padding: 0px 16px 16px;\n}\n</style>\n"]}]}