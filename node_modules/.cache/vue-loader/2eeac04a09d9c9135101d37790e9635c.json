{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ValueFromResource.vue?vue&type=template&id=a56e893e&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ValueFromResource.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ValueFromResource.vue"], "names": [], "mappings": ";EAuQE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB;MACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;UACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7C,CAAC,CAAC,CAAC,CAAC;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B;QACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ValueFromResource.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { CONFIG_MAP, SECRET, NAMESPACE } from '@shell/config/types';\nimport { get } from '@shell/utils/object';\nimport { _VIEW } from '@shell/config/query-params';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport { LabeledInput } from '@components/Form/LabeledInput';\n\nexport default {\n  emits: ['update:value', 'remove'],\n\n  components: {\n    LabeledSelect,\n    LabeledInput\n  },\n\n  props: {\n    mode: {\n      type:    String,\n      default: 'create'\n    },\n    value: {\n      type:    Object,\n      default: () => {\n        return { valueFrom: {} };\n      }\n    },\n    allConfigMaps: {\n      type:    Array,\n      default: () => []\n    },\n    allSecrets: {\n      type:    Array,\n      default: () => []\n    },\n    // filter resource options by namespace(s) selected in top nav\n    namespaced: {\n      type:    Boolean,\n      default: true\n    },\n    loading: {\n      default: false,\n      type:    Boolean\n    },\n  },\n\n  data() {\n    const typeOpts = [\n      { value: 'simple', label: 'Key/Value Pair' },\n      { value: 'resourceFieldRef', label: 'Resource' },\n      { value: 'configMapKeyRef', label: 'ConfigMap Key' },\n      { value: 'secretKeyRef', label: 'Secret key' },\n      { value: 'fieldRef', label: 'Pod Field' },\n      { value: 'secretRef', label: 'Secret' },\n      { value: 'configMapRef', label: 'ConfigMap' },\n    ];\n\n    const resourceKeyOpts = ['limits.cpu', 'limits.ephemeral-storage', 'limits.memory', 'requests.cpu', 'requests.ephemeral-storage', 'requests.memory'];\n    let type;\n\n    if (this.value.secretRef) {\n      type = 'secretRef';\n    } else if (this.value.configMapRef) {\n      type = 'configMapRef';\n    } else if (this.value.value) {\n      type = 'simple';\n    } else if (this.value.valueFrom) {\n      type = Object.keys((this.value.valueFrom))[0] || 'simple';\n    }\n\n    let refName;\n    let name;\n    let fieldPath;\n    let referenced;\n    let key;\n    let valStr;\n    const keys = [];\n\n    switch (type) {\n    case 'resourceFieldRef':\n      name = this.value.name;\n      refName = this.value.valueFrom[type].containerName;\n      key = this.value.valueFrom[type].resource || '';\n      break;\n    case 'configMapKeyRef':\n      name = this.value.name;\n      key = this.value.valueFrom[type].key || '';\n      refName = this.value.valueFrom[type].name;\n      referenced = this.allConfigMaps.filter((resource) => {\n        return resource.metadata.name === refName;\n      })[0];\n      if (referenced && referenced.data) {\n        keys.push(...Object.keys(referenced.data));\n      }\n      break;\n    case 'secretRef':\n    case 'configMapRef':\n      name = this.value.prefix;\n      refName = this.value[type].name;\n      break;\n    case 'secretKeyRef':\n      name = this.value.name;\n      key = this.value.valueFrom[type].key || '';\n      refName = this.value.valueFrom[type].name;\n      referenced = this.allSecrets.filter((resource) => {\n        return resource.metadata.name === refName;\n      })[0];\n      if (referenced && referenced.data) {\n        keys.push(...Object.keys(referenced.data));\n      }\n      break;\n    case 'fieldRef':\n      fieldPath = get(this.value.valueFrom, `${ type }.fieldPath`) || '';\n      name = this.value.name;\n      break;\n    default:\n      name = this.value.name;\n      valStr = this.value.value;\n      break;\n    }\n\n    return {\n      typeOpts, type, refName, referenced: refName, secrets: this.allSecrets, keys, key, fieldPath, name, resourceKeyOpts, valStr\n    };\n  },\n  computed: {\n    isView() {\n      return this.mode === _VIEW;\n    },\n\n    namespaces() {\n      if (this.namespaced) {\n        const map = this.$store.getters.namespaces();\n\n        return Object.keys(map).filter((key) => map[key]);\n      } else {\n        const inStore = this.$store.getters['currentStore'](NAMESPACE);\n\n        return this.$store.getters[`${ inStore }/all`](NAMESPACE);\n      }\n    },\n\n    sourceOptions() {\n      if (this.type === 'configMapKeyRef' || this.type === 'configMapRef') {\n        return this.allConfigMaps.filter((map) => this.namespaces.includes(map?.metadata?.namespace));\n      } else if (this.type === 'secretRef' || this.type === 'secretKeyRef') {\n        return this.allSecrets.filter((secret) => this.namespaces.includes(secret?.metadata?.namespace));\n      } else {\n        return [];\n      }\n    },\n\n    needsSource() {\n      return this.type !== 'simple' && this.type !== 'resourceFieldRef' && this.type !== 'fieldRef' && !!this.type;\n    },\n\n    sourceLabel() {\n      let out;\n      const { type } = this;\n\n      if (!type) {\n        return;\n      }\n\n      switch (type) {\n      case 'secretKeyRef':\n      case 'secretRef':\n        out = 'workload.container.command.fromResource.secret';\n        break;\n      case 'configMapKeyRef':\n      case 'configMapRef':\n        out = 'workload.container.command.fromResource.configMap';\n        break;\n      default:\n        out = 'workload.container.command.fromResource.source.label';\n      }\n\n      return this.t(out);\n    },\n\n    nameLabel() {\n      if (this.type === 'configMapRef' || this.type === 'secretRef') {\n        return this.t('workload.container.command.fromResource.prefix');\n      } else {\n        return this.t('workload.container.command.fromResource.name.label');\n      }\n    },\n\n    extraColumn() {\n      return ['resourceFieldRef', 'configMapKeyRef', 'secretKeyRef'].includes(this.type);\n    },\n  },\n\n  watch: {\n    type() {\n      this.referenced = null;\n      this.key = '';\n      this.refName = '';\n      this.keys = [];\n      this.key = '';\n      this.valStr = '';\n      this.fieldPath = '';\n    },\n\n    referenced(neu, old) {\n      if (neu) {\n        if ((neu.type === SECRET || neu.type === CONFIG_MAP) && neu.data) {\n          this.keys = Object.keys(neu.data);\n        }\n        this.refName = neu?.metadata?.name;\n      }\n      this.updateRow();\n    },\n  },\n\n  methods: {\n    updateRow() {\n      if (!this.name?.length && !this.refName?.length) {\n        if (this.type !== 'fieldRef') {\n          this.$emit('update:value', null);\n\n          return;\n        }\n      }\n      let out = { name: this.name || this.refName };\n\n      switch (this.type) {\n      case 'configMapKeyRef':\n      case 'secretKeyRef':\n        out.valueFrom = {\n          [this.type]: {\n            key: this.key, name: this.refName, optional: false\n          }\n        };\n        break;\n      case 'resourceFieldRef':\n        out.valueFrom = {\n          [this.type]: {\n            containerName: this.refName, divisor: 1, resource: this.key\n          }\n        };\n        break;\n      case 'fieldRef':\n        if (!this.fieldPath || !this.fieldPath.length) {\n          out = null; break;\n        }\n        out.valueFrom = { [this.type]: { apiVersion: 'v1', fieldPath: this.fieldPath } };\n        break;\n      case 'simple':\n        out.value = this.valStr;\n        break;\n      default:\n        delete out.name;\n        out.prefix = this.name;\n        out[this.type] = { name: this.refName, optional: false };\n      }\n      this.$emit('update:value', out);\n    },\n    get\n  }\n};\n</script>\n\n<template>\n  <div class=\"var-row\">\n    <div class=\"type\">\n      <LabeledSelect\n        v-model:value=\"type\"\n        :mode=\"mode\"\n        :multiple=\"false\"\n        :options=\"typeOpts\"\n        option-label=\"label\"\n        :searchable=\"false\"\n        :reduce=\"e=>e.value\"\n        :label=\"t('workload.container.command.fromResource.type')\"\n        @update:value=\"updateRow\"\n      />\n    </div>\n\n    <div class=\"name\">\n      <LabeledInput\n        v-model:value=\"name\"\n        :label=\"nameLabel\"\n        :placeholder=\"t('workload.container.command.fromResource.name.placeholder')\"\n        :mode=\"mode\"\n        @update:value=\"updateRow\"\n      />\n    </div>\n\n    <div\n      v-if=\"type==='simple'\"\n      class=\"single-value\"\n    >\n      <LabeledInput\n        v-model:value=\"valStr\"\n        :label=\"t('workload.container.command.fromResource.value.label')\"\n        :placeholder=\"t('workload.container.command.fromResource.value.placeholder')\"\n        :mode=\"mode\"\n        @update:value=\"updateRow\"\n      />\n    </div>\n\n    <template v-else-if=\"needsSource\">\n      <div :class=\"{'single-value': type === 'configMapRef' || type === 'secretRef'}\">\n        <LabeledSelect\n          v-model:value=\"referenced\"\n          :options=\"sourceOptions\"\n          :multiple=\"false\"\n          :get-option-label=\"opt=>get(opt, 'metadata.name') || opt\"\n          :get-option-key=\"opt=>opt.id|| opt\"\n          :mode=\"mode\"\n          :label=\"sourceLabel\"\n          :loading=\"loading\"\n        />\n      </div>\n      <div v-if=\"type!=='secretRef' && type!== 'configMapRef'\">\n        <LabeledSelect\n          v-model:value=\"key\"\n          :multiple=\"false\"\n          :options=\"keys\"\n          :mode=\"mode\"\n          option-label=\"label\"\n          :label=\"t('workload.container.command.fromResource.key.label')\"\n          :loading=\"loading\"\n          @update:value=\"updateRow\"\n        />\n      </div>\n    </template>\n\n    <template v-else-if=\"type==='resourceFieldRef'\">\n      <div>\n        <LabeledInput\n          v-model:value=\"refName\"\n          :label=\"t('workload.container.command.fromResource.containerName')\"\n          :placeholder=\"t('workload.container.command.fromResource.source.placeholder')\"\n          :mode=\"mode\"\n          @update:value=\"updateRow\"\n        />\n      </div>\n      <div>\n        <LabeledSelect\n          v-model:value=\"key\"\n          :label=\"t('workload.container.command.fromResource.key.label')\"\n          :multiple=\"false\"\n          :options=\"resourceKeyOpts\"\n          :mode=\"mode\"\n          :searchable=\"false\"\n          :placeholder=\"t('workload.container.command.fromResource.key.placeholder', null, true)\"\n          @update:value=\"updateRow\"\n        />\n      </div>\n    </template>\n\n    <template v-else>\n      <div class=\"single-value\">\n        <LabeledInput\n          v-model:value=\"fieldPath\"\n          :placeholder=\"t('workload.container.command.fromResource.key.placeholder', null, true)\"\n          :label=\"t('workload.container.command.fromResource.key.label')\"\n          :mode=\"mode\"\n          @update:value=\"updateRow\"\n        />\n      </div>\n    </template>\n    <div class=\"remove\">\n      <button\n        v-if=\"!isView\"\n        type=\"button\"\n        class=\"btn role-link\"\n        @click.stop=\"$emit('remove')\"\n      >\n        {{ t('generic.remove') }}\n      </button>\n    </div>\n  </div>\n</template>\n\n<style lang='scss' scoped>\n.var-row{\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr 1fr 100px;\n  grid-column-gap: 20px;\n  margin-bottom: 10px;\n  align-items: center;\n\n  .single-value {\n    grid-column: span 2;\n  }\n\n  .remove BUTTON {\n    padding: 0px;\n  }\n}\n\n</style>\n"]}]}