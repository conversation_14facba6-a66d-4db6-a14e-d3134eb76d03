{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SortableTable/index.vue?vue&type=style&index=1&id=6e475b86&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SortableTable/index.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SortableTable/index.vue"], "names": [], "mappings": ";EAsxDE,CAAC;EACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EAC5E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACvE,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;IAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB;;IAEA,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;MAET,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACrB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACnB;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,EAAE;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAClB;;QAEA,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAClB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/C;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,EAAE;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAClB;;QAEA,CAAC,EAAE;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;UAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxD;QACF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;UAElC,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1C;QACF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACb;MACF;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAExB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAClB;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACnB;EACF;;IAEE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB;EACF;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7F;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAErB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1G;;IAEA,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEV,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC;;MAEA,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACjB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAExB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B;QACF;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG;QACZ,CAAC,CAAC,CAAC,CAAC,EAAE;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACtB;MACF;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,EAAE;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB;QACA,CAAC,EAAE;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B;MACF;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEhC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC;;QAEA,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEhC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpC;QACF;MACF;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAElB,CAAC,CAAC,CAAC,EAAE;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SortableTable/index.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapGetters } from 'vuex';\nimport { defineAsyncComponent, ref, onMounted, onBeforeUnmount } from 'vue';\nimport day from 'dayjs';\nimport semver from 'semver';\nimport isEmpty from 'lodash/isEmpty';\nimport { dasherize, ucFirst } from '@shell/utils/string';\nimport { get, clone } from '@shell/utils/object';\nimport { removeObject } from '@shell/utils/array';\nimport { Checkbox } from '@components/Form/Checkbox';\nimport AsyncButton, { ASYNC_BUTTON_STATES } from '@shell/components/AsyncButton';\nimport ActionDropdown from '@shell/components/ActionDropdown';\nimport throttle from 'lodash/throttle';\nimport debounce from 'lodash/debounce';\nimport THead from './THead';\nimport filtering from './filtering';\nimport selection from './selection';\nimport sorting from './sorting';\nimport paging from './paging';\nimport grouping from './grouping';\nimport actions from './actions';\nimport AdvancedFiltering from './advanced-filtering';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport { getParent } from '@shell/utils/dom';\nimport { FORMATTERS } from '@shell/components/SortableTable/sortable-config';\nimport ButtonMultiAction from '@shell/components/ButtonMultiAction.vue';\nimport ActionMenu from '@shell/components/ActionMenuShell.vue';\nimport { getVersionInfo } from '@shell/utils/version';\n\n// Uncomment for table performance debugging\n// import tableDebug from './debug';\n\n// @TODO:\n// Fixed header/scrolling\n\n// Data Flow:\n// rows prop\n// --> sorting.js arrangedRows\n// --> filtering.js handleFiltering()\n// --> filtering.js filteredRows\n// --> paging.js pageRows\n// --> grouping.js groupedRows\n// --> index.vue displayRows\n\nexport default {\n  name: 'SortableTable',\n\n  emits: [\n    'clickedActionButton',\n    'pagination-changed',\n    'group-value-change',\n    'selection',\n    'rowClick',\n    'enter',\n  ],\n\n  components: {\n    THead,\n    Checkbox,\n    AsyncButton,\n    ActionDropdown,\n    LabeledSelect,\n    ButtonMultiAction,\n    ActionMenu,\n  },\n  mixins: [\n    filtering,\n    sorting,\n    paging,\n    grouping,\n    selection,\n    actions,\n    AdvancedFiltering,\n    // For table performance debugging - uncomment and uncomment the corresponding import\n    // tableDebug,\n  ],\n\n  props: {\n    headers: {\n      // {\n      //    name:   Name for the column (goes in query param) and for defaultSortBy\n      //    label:  Displayed column header\n      //    sort:   string|array[string] Field name(s) to sort by, default: [name, keyField]\n      //              fields can be suffixed with ':desc' to flip the normal sort order\n      //    search: string|array[string] Field name(s) to search in, default: [name]\n      //    width:  number\n      // }\n      type:     Array,\n      required: true\n    },\n\n    rows: {\n      // The array of objects to show\n      type:     Array,\n      required: true\n    },\n\n    keyField: {\n      // Field that is unique for each row.\n      type:    String,\n      default: '_key',\n    },\n\n    loading: {\n      type:     Boolean,\n      required: false\n    },\n\n    /**\n     * Alt Loading - True: Always show table rows and obscure them when `loading`. Intended for use with server-side pagination.\n     *\n     * Alt Loading - False: Hide the table rows when `loading`. Intended when all resources are provided up front.\n     */\n    altLoading: {\n      type:     Boolean,\n      required: false\n    },\n\n    groupBy: {\n      // Field to group rows by, row[groupBy] must be something that can be a map key\n      type:    String,\n      default: null\n    },\n    groupRef: {\n      // Object to provide as the reference for rendering the grouping row\n      type:    String,\n      default: null,\n    },\n    groupSort: {\n      // Field to order groups by, defaults to groupBy\n      type:    Array,\n      default: null\n    },\n\n    defaultSortBy: {\n      // Default field to sort by if none is specified\n      // uses name on headers\n      type:    String,\n      default: null\n    },\n\n    tableActions: {\n      // Show bulk table actions\n      type:    Boolean,\n      default: true\n    },\n\n    rowActions: {\n      // Show action dropdown on the end of each row\n      type:    Boolean,\n      default: true\n    },\n\n    mangleActionResources: {\n      type:    Function,\n      default: null,\n    },\n\n    rowActionsWidth: {\n      // How wide the action dropdown column should be\n      type:    Number,\n      default: 40\n    },\n\n    search: {\n      // Show search input to filter rows\n      type:    Boolean,\n      default: true\n    },\n\n    extraSearchFields: {\n      // Additional fields that aren't defined in the headers to search in on each row\n      type:    Array,\n      default: null\n    },\n\n    subRows: {\n      // If there are sub-rows, your main row must have <tr class=\"main-row\"> to identify it\n      type:    Boolean,\n      default: false,\n    },\n\n    subRowsDescription: {\n      type:    Boolean,\n      default: true,\n    },\n\n    subExpandable: {\n      type:    Boolean,\n      default: false,\n    },\n\n    subExpandColumn: {\n      type:    Boolean,\n      default: false,\n    },\n\n    subSearch: {\n      // A field containing an array of sub-items to also search in for each row\n      type:    String,\n      default: null,\n    },\n\n    subFields: {\n      // Search this list of fields within the items in \"subSearch\" of each row\n      type:    Array,\n      default: null,\n    },\n\n    /**\n     * Show the divider between the thead and tbody.\n     */\n    topDivider: {\n      type:    Boolean,\n      default: true\n    },\n\n    /**\n     * Show the dividers between rows\n     */\n    bodyDividers: {\n      type:    Boolean,\n      default: false\n    },\n\n    overflowX: {\n      type:    Boolean,\n      default: false\n    },\n    overflowY: {\n      type:    Boolean,\n      default: false\n    },\n\n    /**\n     * If pagination of the data is enabled or not\n     */\n    paging: {\n      type:    Boolean,\n      default: false,\n    },\n\n    /**\n     * What translation key to use for displaying the '1 - 10 of 100 Things' pagination info\n     */\n    pagingLabel: {\n      type:    String,\n      default: 'sortableTable.paging.generic'\n    },\n\n    /**\n     * Additional params to pass to the pagingLabel translation\n     */\n    pagingParams: {\n      type:    Object,\n      default: null,\n    },\n\n    /**\n     * Allows you to override the default preference of the number of\n     * items to display per page. This is used by ./paging.js if you're\n     * looking for a reference.\n     */\n    rowsPerPage: {\n      type:    Number,\n      default: null, // Default comes from the user preference\n    },\n\n    /**\n     * Allows you to override the default translation text of no rows view\n     */\n    noRowsKey: {\n      type:    String,\n      default: 'sortableTable.noRows'\n    },\n\n    /**\n     * Allows you to hide the no rows messaging.\n     */\n    showNoRows: {\n      type:    Boolean,\n      default: true\n    },\n\n    /**\n     * Allows you to override the default translation text of no search data view\n     */\n    noDataKey: {\n      type:    String,\n      default: 'sortableTable.noData' // i18n-uses sortableTable.noData\n    },\n\n    /**\n     * Allows you to override showing the THEAD section.\n     */\n    showHeaders: {\n      type:    Boolean,\n      default: true\n    },\n\n    /**\n     * Provide a unique key that will provide a new value given changes to the environment that\n     * should kick off an update to table rows (for instance resource list generation or change of namespace)\n     *\n     * This does not have to update given internal facets like sort order or direction\n     */\n    sortGenerationFn: {\n      type:    Function,\n      default: null,\n    },\n\n    /**\n     * Can be used in place of sortGenerationFn\n     */\n    sortGeneration: {\n      type:    String,\n      default: null\n    },\n\n    /**\n     * The list will always be sorted by these regardless of what the user has selected\n     */\n    mandatorySort: {\n      type:    Array,\n      default: null,\n    },\n\n    /**\n     * Allows you to link to a custom detail page for data that\n     * doesn't have a class model. For example, a receiver configuration\n     * block within an AlertmanagerConfig resource.\n     */\n    getCustomDetailLink: {\n      type:    Function,\n      default: null\n    },\n\n    /**\n     * Inherited global identifier prefix for tests\n     * Define a term based on the parent component to avoid conflicts on multiple components\n     */\n    componentTestid: {\n      type:    String,\n      default: 'sortable-table'\n    },\n    /**\n     * Allows for the usage of a query param to work for simple filtering (q)\n     */\n    useQueryParamsForSimpleFiltering: {\n      type:    Boolean,\n      default: false\n    },\n    /**\n     * Manaul force the update of live and delayed cells. Change this number to kick off the update\n     */\n    forceUpdateLiveAndDelayed: {\n      type:    Number,\n      default: 0\n    },\n\n    /**\n     * True if pagination is executed outside of the component\n     */\n    externalPaginationEnabled: {\n      type:    Boolean,\n      default: false\n    },\n\n    /**\n     * If `externalPaginationEnabled` is true this will be used as the current page\n     */\n    externalPaginationResult: {\n      type:    Object,\n      default: null\n    },\n\n    manualRefreshButtonSize: {\n      type:    String,\n      default: ''\n    }\n\n  },\n\n  data() {\n    let searchQuery = '';\n    let eventualSearchQuery = '';\n\n    // only allow for filter query param for simple filtering for now...\n    if (!this.hasAdvancedFiltering && this.useQueryParamsForSimpleFiltering && this.$route.query?.q) {\n      searchQuery = this.$route.query?.q;\n      eventualSearchQuery = this.$route.query?.q;\n    }\n\n    const isLoading = this.loading || false;\n\n    return {\n      refreshButtonPhase:         isLoading ? ASYNC_BUTTON_STATES.WAITING : ASYNC_BUTTON_STATES.ACTION,\n      expanded:                   {},\n      searchQuery,\n      eventualSearchQuery,\n      subMatches:                 null,\n      actionOfInterest:           null,\n      loadingDelay:               false,\n      debouncedPaginationChanged: null,\n      /**\n       * The is the bool the DOM uses to show loading state. it's proxied from `loading` to avoid blipping the indicator (see usages)\n       */\n      isLoading\n    };\n  },\n\n  mounted() {\n    this._loadingDelayTimer = setTimeout(() => {\n      this.loadingDelay = true;\n    }, 200);\n\n    // Add scroll listener to the main element\n    const $main = document.querySelector('main');\n\n    this._onScroll = this.onScroll.bind(this);\n    $main?.addEventListener('scroll', this._onScroll);\n\n    this.debouncedPaginationChanged();\n  },\n\n  beforeUnmount() {\n    clearTimeout(this._scrollTimer);\n    clearTimeout(this._loadingDelayTimer);\n    clearTimeout(this._altLoadingDelayTimer);\n    clearTimeout(this._liveColumnsTimer);\n    clearTimeout(this._delayedColumnsTimer);\n    clearTimeout(this.manualRefreshTimer);\n\n    const $main = document.querySelector('main');\n\n    $main?.removeEventListener('scroll', this._onScroll);\n  },\n\n  watch: {\n    eventualSearchQuery: debounce(function(q) {\n      this.searchQuery = q;\n\n      if (!this.hasAdvancedFiltering && this.useQueryParamsForSimpleFiltering) {\n        const route = {\n          name:   this.$route.name,\n          params: { ...this.$route.params },\n          query:  { ...this.$route.query, q }\n        };\n\n        if (!q && this.$route.query?.q) {\n          route.query = {};\n        }\n\n        this.$router.replace(route);\n      }\n    }, 200),\n\n    descending(neu, old) {\n      this.watcherUpdateLiveAndDelayed(neu, old);\n    },\n\n    searchQuery(neu, old) {\n      this.watcherUpdateLiveAndDelayed(neu, old);\n    },\n\n    sortFields(neu, old) {\n      this.watcherUpdateLiveAndDelayed(neu, old);\n    },\n\n    groupBy(neu, old) {\n      this.watcherUpdateLiveAndDelayed(neu, old);\n    },\n\n    namespaces(neu, old) {\n      this.watcherUpdateLiveAndDelayed(neu, old);\n    },\n\n    page(neu, old) {\n      this.watcherUpdateLiveAndDelayed(neu, old);\n    },\n\n    forceUpdateLiveAndDelayed(neu, old) {\n      this.watcherUpdateLiveAndDelayed(neu, old);\n    },\n\n    // Ensure we update live and delayed columns on first load\n    initalLoad: {\n      handler(neu) {\n        if (neu) {\n          this._didinit = true;\n          this.$nextTick(() => this.updateLiveAndDelayed());\n        }\n      },\n      immediate: true\n    },\n\n    // this is the flag that indicates that manual refresh data has been loaded\n    // and we should update the deferred cols\n    manualRefreshLoadingFinished: {\n      handler(neu, old) {\n        // this is merely to update the manual refresh button status\n        this.refreshButtonPhase = !neu ? ASYNC_BUTTON_STATES.WAITING : ASYNC_BUTTON_STATES.ACTION;\n        if (neu && neu !== old) {\n          this.$nextTick(() => this.updateLiveAndDelayed());\n        }\n      },\n      immediate: true\n    },\n\n    loading: {\n      handler(neu, old) {\n        // Always ensure the Refresh button phase aligns with loading state (to ensure external phase changes which can then reset the internal phase changed by click)\n        this.refreshButtonPhase = neu ? ASYNC_BUTTON_STATES.WAITING : ASYNC_BUTTON_STATES.ACTION;\n\n        if (this.altLoading) {\n          // Delay setting the actual loading indicator. This should avoid flashing up the indicator if the API responds quickly\n          if (neu) {\n            this._altLoadingDelayTimer = setTimeout(() => {\n              this.isLoading = true;\n            }, 200); // this should be higher than the targeted quick response\n          } else {\n            clearTimeout(this._altLoadingDelayTimer);\n            this.isLoading = false;\n          }\n        } else {\n          this.isLoading = neu;\n        }\n      },\n      immediate: true\n    },\n  },\n  setup(_props, { emit }) {\n    const table = ref(null);\n\n    const handleEnterKey = (event) => {\n      if (event.key === 'Enter' && !event.target?.classList?.contains('checkbox-custom')) {\n        emit('enter', event);\n      }\n    };\n\n    onMounted(() => {\n      table.value.addEventListener('keyup', handleEnterKey);\n    });\n\n    onBeforeUnmount(() => {\n      table.value.removeEventListener('keyup', handleEnterKey);\n    });\n\n    return { table };\n  },\n\n  created() {\n    this.debouncedRefreshTableData = debounce(this.refreshTableData, 500);\n    this.debouncedPaginationChanged = debounce(this.paginationChanged, 50);\n  },\n\n  computed: {\n    ...mapGetters({ isTooManyItemsToAutoUpdate: 'resource-fetch/isTooManyItemsToAutoUpdate' }),\n    ...mapGetters({ isManualRefreshLoading: 'resource-fetch/manualRefreshIsLoading' }),\n    namespaces() {\n      return this.$store.getters['activeNamespaceCache'];\n    },\n\n    initalLoad() {\n      return !!(!this.isLoading && !this._didinit && this.rows?.length);\n    },\n\n    manualRefreshLoadingFinished() {\n      const res = !!(!this.isLoading && this._didinit && this.rows?.length && !this.isManualRefreshLoading);\n\n      return res;\n    },\n\n    fullColspan() {\n      let span = 0;\n\n      for ( let i = 0 ; i < this.columns.length ; i++ ) {\n        if (!this.columns[i].hide) {\n          span++;\n        }\n      }\n\n      if ( this.tableActions ) {\n        span++;\n      }\n\n      if ( this.subExpandColumn ) {\n        span++;\n      }\n\n      if ( this.rowActions ) {\n        span++;\n      }\n\n      return span;\n    },\n\n    noResults() {\n      return !!this.searchQuery && this.pagedRows.length === 0;\n    },\n\n    noRows() {\n      return !this.noResults && (this.rows || []).length === 0;\n    },\n\n    showHeaderRow() {\n      // All of these are used to show content in the header\n      return this.search ||\n        this.tableActions ||\n        this.$slots['header-left'] ||\n        this.$slots['header-middle'] ||\n        this.$slots['header-right'] ||\n        this.isTooManyItemsToAutoUpdate;\n    },\n\n    columns() {\n      // Filter out any columns that are too heavy to show for large page sizes\n      const out = this.headers.slice().filter((c) => !c.maxPageSize || (c.maxPageSize && c.maxPageSize >= this.perPage));\n\n      if ( this.groupBy ) {\n        const entry = out.find((x) => x.name === this.groupBy);\n\n        if ( entry ) {\n          removeObject(out, entry);\n        }\n      }\n\n      // If all columns have a width, try to remove it from a column that can be variable (name)\n      const missingWidth = out.find((x) => !x.width);\n\n      if ( !missingWidth ) {\n        const variable = out.find((x) => x.canBeVariable);\n\n        if ( variable ) {\n          const neu = clone(variable);\n\n          delete neu.width;\n\n          out.splice(out.indexOf(variable), 1, neu);\n        }\n      }\n\n      // handle cols visibility and filtering if there is advanced filtering\n      if (this.hasAdvancedFiltering) {\n        const cols = this.handleColsVisibilyAndFiltering(out);\n\n        return cols;\n      }\n\n      return out;\n    },\n\n    // For data-title properties on <td>s\n    dt() {\n      const out = {\n        check:   `Select: `,\n        actions: `Actions: `,\n      };\n\n      this.columns.forEach((col) => {\n        out[col.name] = `${ (col.label || col.name) }:`;\n      });\n\n      return out;\n    },\n\n    classObject() {\n      return {\n        'top-divider':   this.topDivider,\n        'body-dividers': this.bodyDividers,\n        'overflow-y':    this.overflowY,\n        'overflow-x':    this.overflowX,\n        'alt-loading':   this.altLoading && this.isLoading\n      };\n    },\n\n    // Do we have any live columns?\n    hasLiveColumns() {\n      const liveColumns = this.columns.find((c) => c.formatter?.startsWith('Live') || c.liveUpdates);\n\n      return !!liveColumns;\n    },\n\n    hasDelayedColumns() {\n      const delaeydColumns = this.columns.find((c) => c.delayLoading);\n\n      return !!delaeydColumns;\n    },\n\n    columnFormmatterIDs() {\n      const columnsIds = {};\n\n      this.columns.forEach((c) => {\n        if (c.formatter) {\n          columnsIds[c.formatter] = dasherize(c.formatter);\n        }\n      });\n\n      return columnsIds;\n    },\n\n    // Generate row and column data for easier rendering in the template\n    // ensures we only call methods like `valueFor` once\n    displayRows() {\n      const rows = [];\n      const columnFormmatterIDs = this.columnFormmatterIDs;\n\n      this.groupedRows.forEach((grp) => {\n        const group = {\n          grp,\n          key:  grp.key,\n          ref:  grp.ref,\n          rows: [],\n        };\n\n        rows.push(group);\n\n        grp.rows.forEach((row) => {\n          const rowData = {\n            row,\n            key:                        this.get(row, this.keyField),\n            showSubRow:                 this.showSubRow(row, this.keyField),\n            canRunBulkActionOfInterest: this.canRunBulkActionOfInterest(row),\n            columns:                    []\n          };\n\n          group.rows.push(rowData);\n\n          this.columns.forEach((c) => {\n            const value = c.delayLoading ? undefined : this.valueFor(row, c, c.isLabel);\n            let component;\n            let formatted = value;\n            let needRef = false;\n\n            if (Array.isArray(value)) {\n              formatted = value.join(', ');\n            }\n\n            if (c.formatter) {\n              if (FORMATTERS[c.formatter]) {\n                component = FORMATTERS[c.formatter];\n                needRef = true;\n              } else {\n                // Check if we have a formatter from a plugin\n                const pluginFormatter = this.$plugin?.getDynamic('formatters', c.formatter);\n\n                if (pluginFormatter) {\n                  component = defineAsyncComponent(pluginFormatter);\n                  needRef = true;\n                }\n              }\n            }\n\n            rowData.columns.push({\n              col:       c,\n              value,\n              formatted,\n              component,\n              needRef,\n              delayed:   c.delayLoading,\n              live:      c.formatter?.startsWith('Live') || c.liveUpdates,\n              label:     this.labelFor(c),\n              dasherize: columnFormmatterIDs[c.formatter] || '',\n            });\n          });\n        });\n      });\n\n      return rows;\n    },\n\n    featureDropdownMenu() {\n      const { fullVersion } = getVersionInfo(this.$store);\n\n      return semver.gte(semver.coerce(fullVersion).version, '2.11.0');\n    }\n  },\n\n  methods: {\n    refreshTableData() {\n      this.$store.dispatch('resource-fetch/doManualRefresh');\n    },\n    get,\n    dasherize,\n\n    onScroll() {\n      if (this.hasLiveColumns || this.hasDelayedColumns) {\n        clearTimeout(this._liveColumnsTimer);\n        clearTimeout(this._scrollTimer);\n        clearTimeout(this._delayedColumnsTimer);\n        this._scrollTimer = setTimeout(() => {\n          this.updateLiveColumns();\n          this.updateDelayedColumns();\n        }, 300);\n      }\n    },\n\n    watcherUpdateLiveAndDelayed(neu, old) {\n      if (neu !== old) {\n        this.$nextTick(() => this.updateLiveAndDelayed());\n      }\n    },\n\n    updateLiveAndDelayed() {\n      if (this.hasLiveColumns) {\n        this.updateLiveColumns();\n      }\n\n      if (this.hasDelayedColumns) {\n        this.updateDelayedColumns();\n      }\n    },\n\n    updateDelayedColumns() {\n      clearTimeout(this._delayedColumnsTimer);\n\n      if (!this.$refs.column || this.pagedRows.length === 0) {\n        return;\n      }\n\n      const delayedColumns = this.$refs.column.filter((c) => c.startDelayedLoading && !c.__delayedLoading);\n      // We add 100 pixels here - so we will render the delayed columns for a few extra rows below what is visible\n      // This way if you scroll slowly, you won't see the columns being loaded\n      const clientHeight = (window.innerHeight || document.documentElement.clientHeight) + 100;\n\n      let scheduled = 0;\n\n      for (let i = 0; i < delayedColumns.length; i++) {\n        const dc = delayedColumns[i];\n        const y = dc.$el.getBoundingClientRect().y;\n\n        if (y >= 0 && y <= clientHeight) {\n          dc.startDelayedLoading(true);\n          dc.__delayedLoading = true;\n\n          scheduled++;\n\n          // Only update 4 at a time\n          if (scheduled === 4) {\n            this._delayedColumnsTimer = setTimeout(this.updateDelayedColumns, 100);\n\n            return;\n          }\n        }\n      }\n    },\n\n    updateLiveColumns() {\n      clearTimeout(this._liveColumnsTimer);\n\n      if (!this.$refs.column || !this.hasLiveColumns || this.pagedRows.length === 0) {\n        return;\n      }\n\n      const clientHeight = window.innerHeight || document.documentElement.clientHeight;\n      const liveColumns = this.$refs.column.filter((c) => !!c.liveUpdate);\n      const now = day();\n      let next = Number.MAX_SAFE_INTEGER;\n\n      for (let i = 0; i < liveColumns.length; i++) {\n        const column = liveColumns[i];\n        const y = column.$el.getBoundingClientRect().y;\n\n        if (y >= 0 && y <= clientHeight) {\n          const diff = column.liveUpdate(now);\n\n          if (diff < next) {\n            next = diff;\n          }\n        }\n      }\n\n      if (next < 1 ) {\n        next = 1;\n      }\n\n      // Schedule again\n      this._liveColumnsTimer = setTimeout(() => this.updateLiveColumns(), next * 1000);\n    },\n\n    labelFor(col) {\n      if ( col.labelKey ) {\n        return this.t(col.labelKey, undefined, true);\n      } else if ( col.label ) {\n        return col.label;\n      }\n\n      return ucFirst(col.name);\n    },\n\n    valueFor(row, col, isLabel) {\n      if (typeof col.value === 'function') {\n        return col.value(row);\n      }\n\n      if (isLabel) {\n        if (row.metadata?.labels && row.metadata?.labels[col.label]) {\n          return row.metadata?.labels[col.label];\n        }\n\n        return '';\n      }\n\n      // Use to debug table columns using expensive value getters\n      // console.warn(`Performance: Table valueFor: ${ col.name } ${ col.value }`); // eslint-disable-line no-console\n\n      const expr = col.value || col.name;\n\n      if (!expr) {\n        console.error('No path has been defined for this column, unable to get value of cell', col); // eslint-disable-line no-console\n\n        return '';\n      }\n      const out = get(row, expr);\n\n      if ( out === null || out === undefined ) {\n        return '';\n      }\n\n      return out;\n    },\n\n    isExpanded(row) {\n      const key = row[this.keyField];\n\n      return !!this.expanded[key];\n    },\n\n    toggleExpand(row) {\n      const key = row[this.keyField];\n      const val = !this.expanded[key];\n\n      this.expanded[key] = val;\n      this.expanded = { ...this.expanded };\n\n      return val;\n    },\n\n    setBulkActionOfInterest(action) {\n      this.actionOfInterest = action;\n    },\n\n    // Can the action of interest be applied to the specified resource?\n    canRunBulkActionOfInterest(resource) {\n      if ( !this.actionOfInterest || isEmpty(resource?.availableActions) ) {\n        return false;\n      }\n\n      const matchingResourceAction = resource.availableActions?.find((a) => a.action === this.actionOfInterest.action);\n\n      return matchingResourceAction?.enabled;\n    },\n\n    focusSearch() {\n      if ( this.$refs.searchQuery ) {\n        this.$refs.searchQuery.focus();\n        this.$refs.searchQuery.select();\n      }\n    },\n\n    nearestCheckbox() {\n      return document.activeElement.closest('tr.main-row')?.querySelector('.checkbox-custom');\n    },\n\n    focusAdjacent(next = true) {\n      const all = Array.from(this.$el.querySelectorAll('.checkbox-custom'));\n\n      const cur = this.nearestCheckbox();\n      let idx = -1;\n\n      if ( cur ) {\n        idx = all.indexOf(cur) + (next ? 1 : -1 );\n      } else if ( next ) {\n        idx = 1;\n      } else {\n        idx = all.length - 1;\n      }\n\n      if ( idx < 1 ) { // Don't go up to the check all button\n        idx = 1;\n\n        return null;\n      }\n\n      if ( idx >= all.length ) {\n        idx = all.length - 1;\n\n        return null;\n      }\n\n      if ( all[idx] ) {\n        all[idx].focus();\n\n        return all[idx];\n      }\n    },\n\n    focusNext: throttle(function(event, more = false) {\n      const elem = this.focusAdjacent(true);\n      const row = getParent(elem, 'tr');\n\n      if (row?.classList.contains('row-selected')) {\n        return;\n      }\n\n      this.keySelectRow(row, more);\n    }, 50),\n\n    focusPrevious: throttle(function(event, more = false) {\n      const elem = this.focusAdjacent(false);\n      const row = getParent(elem, 'tr');\n\n      if (row?.classList.contains('row-selected')) {\n        return;\n      }\n\n      this.keySelectRow(row, more);\n    }, 50),\n\n    showSubRow(row, keyField) {\n      const hasInjectedSubRows = this.subRows && (!this.subExpandable || this.expanded[get(row, keyField)]);\n      const hasStateDescription = this.subRowsDescription && row.stateDescription;\n\n      return hasInjectedSubRows || hasStateDescription;\n    },\n\n    handleActionButtonClick(i, event) {\n      // Each row in the table gets its own ref with\n      // a number based on its index. If you are using\n      // an ActionMenu that doen't have a dependency on Vuex,\n      // these refs are useful because you can reuse the\n      // same ActionMenu component on a page with many different\n      // target elements in a list,\n      // so you can still avoid the performance problems that\n      // could result if the ActionMenu was in every row. The menu\n      // will open on whichever target element is clicked.\n      this.$emit('clickedActionButton', {\n        event,\n        targetElement: this.$refs[`actionButton${ i }`][0],\n      });\n    },\n\n    paginationChanged() {\n      if (!this.externalPaginationEnabled) {\n        return;\n      }\n\n      this.$emit('pagination-changed', {\n        page:    this.page,\n        perPage: this.perPage,\n        filter:  {\n          searchFields: this.searchFields,\n          searchQuery:  this.searchQuery\n        },\n        sort:       this.sortFields,\n        descending: this.descending\n      });\n    }\n  }\n};\n</script>\n\n<template>\n  <div\n    ref=\"container\"\n    :data-testid=\"componentTestid + '-list-container'\"\n  >\n    <div\n      :class=\"{'titled': $slots.title && $slots.title.length}\"\n      class=\"sortable-table-header\"\n    >\n      <slot name=\"title\" />\n      <div\n        v-if=\"showHeaderRow\"\n        class=\"fixed-header-actions\"\n        :class=\"{button: !!$slots['header-button'], 'advanced-filtering': hasAdvancedFiltering}\"\n      >\n        <div\n          :class=\"bulkActionsClass\"\n          class=\"bulk\"\n        >\n          <slot name=\"header-left\">\n            <template v-if=\"tableActions\">\n              <button\n                v-for=\"(act) in availableActions\"\n                :id=\"act.action\"\n                :key=\"act.action\"\n                v-clean-tooltip=\"actionTooltip\"\n                type=\"button\"\n                class=\"btn role-primary\"\n                :class=\"{[bulkActionClass]:true}\"\n                :disabled=\"!act.enabled\"\n                :data-testid=\"componentTestid + '-' + act.action\"\n                role=\"button\"\n                :aria-label=\"act.label\"\n                @click=\"applyTableAction(act, null, $event)\"\n                @keydown.enter.stop\n                @mouseover=\"setBulkActionOfInterest(act)\"\n                @mouseleave=\"setBulkActionOfInterest(null)\"\n              >\n                <i\n                  v-if=\"act.icon\"\n                  :class=\"act.icon\"\n                />\n                <span v-clean-html=\"act.label\" />\n              </button>\n              <ActionDropdown\n                :class=\"bulkActionsDropdownClass\"\n                class=\"bulk-actions-dropdown\"\n                :disable-button=\"!selectedRows.length\"\n                size=\"sm\"\n              >\n                <template #button-content>\n                  <button\n                    ref=\"actionDropDown\"\n                    class=\"btn bg-primary mr-0\"\n                    :disabled=\"!selectedRows.length\"\n                  >\n                    <i class=\"icon icon-gear\" />\n                    <span>{{ t('sortableTable.bulkActions.collapsed.label') }}</span>\n                    <i class=\"ml-10 icon icon-chevron-down\" />\n                  </button>\n                </template>\n                <template #popover-content>\n                  <ul class=\"list-unstyled menu\">\n                    <li\n                      v-for=\"(act, i) in hiddenActions\"\n                      :key=\"i\"\n                      v-close-popper\n                      v-clean-tooltip=\"{\n                        content: actionTooltip,\n                        placement: 'right'\n                      }\"\n                      :class=\"{ disabled: !act.enabled }\"\n                      @click=\"applyTableAction(act, null, $event)\"\n                      @mouseover=\"setBulkActionOfInterest(act)\"\n                      @mouseleave=\"setBulkActionOfInterest(null)\"\n                    >\n                      <i\n                        v-if=\"act.icon\"\n                        :class=\"act.icon\"\n                      />\n                      <span v-clean-html=\"act.label\" />\n                    </li>\n                  </ul>\n                </template>\n              </ActionDropdown>\n              <label\n                v-if=\"selectedRowsText\"\n                :class=\"bulkActionAvailabilityClass\"\n                class=\"action-availability\"\n              >\n                {{ selectedRowsText }}\n              </label>\n            </template>\n          </slot>\n        </div>\n        <div\n          v-if=\"!hasAdvancedFiltering && $slots['header-middle']\"\n          class=\"middle\"\n        >\n          <slot name=\"header-middle\" />\n        </div>\n\n        <div\n          v-if=\"search || hasAdvancedFiltering || isTooManyItemsToAutoUpdate || $slots['header-right']\"\n          class=\"search row\"\n          data-testid=\"search-box-filter-row\"\n        >\n          <ul\n            v-if=\"hasAdvancedFiltering\"\n            class=\"advanced-filters-applied\"\n          >\n            <li\n              v-for=\"(filter, i) in advancedFilteringValues\"\n              :key=\"i\"\n            >\n              <span class=\"label\">{{ `\"${filter.value}\" ${ t('sortableTable.in') } ${filter.label}` }}</span>\n              <span\n                class=\"cross\"\n                @click=\"clearAdvancedFilter(i)\"\n              >&#10005;</span>\n              <div class=\"bg\" />\n            </li>\n          </ul>\n          <slot name=\"header-right\" />\n          <AsyncButton\n            v-if=\"isTooManyItemsToAutoUpdate\"\n            mode=\"manual-refresh\"\n            :size=\"manualRefreshButtonSize\"\n            :current-phase=\"refreshButtonPhase\"\n            @click=\"debouncedRefreshTableData\"\n          />\n          <div\n            v-if=\"hasAdvancedFiltering\"\n            ref=\"advanced-filter-group\"\n            class=\"advanced-filter-group\"\n          >\n            <button\n              class=\"btn role-primary\"\n              @click=\"advancedFilteringVisibility = !advancedFilteringVisibility;\"\n            >\n              {{ t('sortableTable.addFilter') }}\n            </button>\n            <div\n              v-show=\"advancedFilteringVisibility\"\n              class=\"advanced-filter-container\"\n            >\n              <input\n                ref=\"advancedSearchQuery\"\n                v-model=\"advFilterSearchTerm\"\n                type=\"search\"\n                class=\"advanced-search-box\"\n                :placeholder=\"t('sortableTable.filterFor')\"\n              >\n              <div class=\"middle-block\">\n                <span>{{ t('sortableTable.in') }}</span>\n                <LabeledSelect\n                  v-model:value=\"advFilterSelectedProp\"\n                  class=\"filter-select\"\n                  :clearable=\"true\"\n                  :options=\"advFilterSelectOptions\"\n                  :disabled=\"false\"\n                  :searchable=\"false\"\n                  mode=\"edit\"\n                  :multiple=\"false\"\n                  :taggable=\"false\"\n                  :placeholder=\"t('sortableTable.selectCol')\"\n                  @selecting=\"(col) => advFilterSelectedLabel = col.label\"\n                />\n              </div>\n              <div class=\"bottom-block\">\n                <button\n                  class=\"btn role-secondary\"\n                  :disabled=\"!advancedFilteringValues.length\"\n                  @click=\"clearAllAdvancedFilters\"\n                >\n                  {{ t('sortableTable.resetFilters') }}\n                </button>\n                <button\n                  class=\"btn role-primary\"\n                  @click=\"addAdvancedFilter\"\n                >\n                  {{ t('sortableTable.add') }}\n                </button>\n              </div>\n            </div>\n          </div>\n          <p\n            v-else-if=\"search\"\n            id=\"describe-filter-sortable-table\"\n            hidden\n          >\n            {{ t('sortableTable.filteringDescription') }}\n          </p>\n          <input\n            v-if=\"search\"\n            ref=\"searchQuery\"\n            v-model=\"eventualSearchQuery\"\n            type=\"search\"\n            class=\"input-sm search-box\"\n            :aria-label=\"t('sortableTable.searchLabel')\"\n            aria-describedby=\"describe-filter-sortable-table\"\n            :placeholder=\"t('sortableTable.search')\"\n          >\n          <slot name=\"header-button\" />\n        </div>\n      </div>\n    </div>\n    <table\n      ref=\"table\"\n      class=\"sortable-table\"\n      :class=\"classObject\"\n      width=\"100%\"\n      role=\"table\"\n    >\n      <THead\n        v-if=\"showHeaders\"\n        :label-for=\"labelFor\"\n        :columns=\"columns\"\n        :group=\"group\"\n        :group-options=\"advGroupOptions\"\n        :has-advanced-filtering=\"hasAdvancedFiltering\"\n        :adv-filter-hide-labels-as-cols=\"advFilterHideLabelsAsCols\"\n        :table-actions=\"tableActions\"\n        :table-cols-options=\"columnOptions\"\n        :row-actions=\"rowActions\"\n        :sub-expand-column=\"subExpandColumn\"\n        :row-actions-width=\"rowActionsWidth\"\n        :how-much-selected=\"howMuchSelected\"\n        :sort-by=\"sortBy\"\n        :default-sort-by=\"_defaultSortBy\"\n        :descending=\"descending\"\n        :no-rows=\"noRows\"\n        :loading=\"isLoading && !loadingDelay\"\n        :no-results=\"noResults\"\n        @on-toggle-all=\"onToggleAll\"\n        @on-sort-change=\"changeSort\"\n        @col-visibility-change=\"changeColVisibility\"\n        @group-value-change=\"(val) => $emit('group-value-change', val)\"\n        @update-cols-options=\"updateColsOptions\"\n      />\n\n      <!-- Don't display anything if we're loading and the delay has yet to pass -->\n      <div v-if=\"isLoading && !loadingDelay\" />\n\n      <tbody v-else-if=\"isLoading && !altLoading\">\n        <slot name=\"loading\">\n          <tr>\n            <td :colspan=\"fullColspan\">\n              <div class=\"data-loading\">\n                <i class=\"icon-spin icon icon-spinner\" />\n                <t\n                  k=\"generic.loading\"\n                  :raw=\"true\"\n                />\n              </div>\n            </td>\n          </tr>\n        </slot>\n      </tbody>\n      <tbody v-else-if=\"noRows\">\n        <slot name=\"no-rows\">\n          <tr class=\"no-rows\">\n            <td :colspan=\"fullColspan\">\n              <t\n                v-if=\"showNoRows\"\n                :k=\"noRowsKey\"\n              />\n            </td>\n          </tr>\n        </slot>\n      </tbody>\n      <tbody v-else-if=\"noResults\">\n        <slot name=\"no-results\">\n          <tr class=\"no-results\">\n            <td\n              :colspan=\"fullColspan\"\n              class=\"text-center\"\n            >\n              <t :k=\"noDataKey\" />\n            </td>\n          </tr>\n        </slot>\n      </tbody>\n      <tbody\n        v-for=\"(groupedRows) in displayRows\"\n        v-else\n        :key=\"groupedRows.key\"\n        tabindex=\"-1\"\n        :class=\"{ group: groupBy }\"\n      >\n        <slot\n          v-if=\"groupBy\"\n          name=\"group-row\"\n          :group=\"groupedRows\"\n          :fullColspan=\"fullColspan\"\n        >\n          <tr class=\"group-row\">\n            <td :colspan=\"fullColspan\">\n              <slot\n                name=\"group-by\"\n                :group=\"groupedRows.grp\"\n              >\n                <div\n                  v-trim-whitespace\n                  class=\"group-tab\"\n                >\n                  {{ groupedRows.ref }}\n                </div>\n              </slot>\n            </td>\n          </tr>\n        </slot>\n        <template\n          v-for=\"(row, i) in groupedRows.rows\"\n          :key=\"i\"\n        >\n          <slot\n            name=\"main-row\"\n            :row=\"row.row\"\n          >\n            <slot\n              :name=\"'main-row:' + (row.row.mainRowKey || i)\"\n              :full-colspan=\"fullColspan\"\n            >\n              <!-- The data-cant-run-bulk-action-of-interest attribute is being used instead of :class because\n                because our selection.js invokes toggleClass and :class clobbers what was added by toggleClass if\n                the value of :class changes. -->\n              <tr\n                class=\"main-row\"\n                :data-testid=\"componentTestid + '-' + i + '-row'\"\n                :class=\"{ 'has-sub-row': row.showSubRow}\"\n                :data-node-id=\"row.key\"\n                :data-cant-run-bulk-action-of-interest=\"actionOfInterest && !row.canRunBulkActionOfInterest\"\n              >\n                <td\n                  v-if=\"tableActions\"\n                  class=\"row-check\"\n                  align=\"middle\"\n                >\n                  {{ row.mainRowKey }}\n                  <Checkbox\n                    class=\"selection-checkbox\"\n                    :data-node-id=\"row.key\"\n                    :data-testid=\"componentTestid + '-' + i + '-checkbox'\"\n                    :value=\"selectedRows.includes(row.row)\"\n                    :alternate-label=\"t('sortableTable.genericRowCheckbox', { item: row && row.row ? row.row.id : '' })\"\n                  />\n                </td>\n                <td\n                  v-if=\"subExpandColumn\"\n                  class=\"row-expand\"\n                  align=\"middle\"\n                >\n                  <i\n                    data-title=\"Toggle Expand\"\n                    :class=\"{\n                      icon: true,\n                      'icon-chevron-right': !expanded[row.row[keyField]],\n                      'icon-chevron-down': !!expanded[row.row[keyField]]\n                    }\"\n                    @click.stop=\"toggleExpand(row.row)\"\n                  />\n                </td>\n                <template\n                  v-for=\"(col, j) in row.columns\"\n                  :key=\"j\"\n                >\n                  <slot\n                    :name=\"'col:' + col.col.name\"\n                    :row=\"row.row\"\n                    :col=\"col.col\"\n                    :dt=\"dt\"\n                    :expanded=\"expanded\"\n                    :rowKey=\"row.key\"\n                  >\n                    <td\n                      v-show=\"!hasAdvancedFiltering || (hasAdvancedFiltering && col.col.isColVisible)\"\n                      :key=\"col.col.name\"\n                      :data-title=\"col.col.label\"\n                      :data-testid=\"`sortable-cell-${ i }-${ j }`\"\n                      :align=\"col.col.align || 'left'\"\n                      :class=\"{['col-'+col.dasherize]: !!col.col.formatter, [col.col.breakpoint]: !!col.col.breakpoint, ['skip-select']: col.col.skipSelect}\"\n                      :width=\"col.col.width\"\n                    >\n                      <slot\n                        :name=\"'cell:' + col.col.name\"\n                        :row=\"row.row\"\n                        :col=\"col.col\"\n                        :value=\"col.value\"\n                      >\n                        <component\n                          :is=\"col.component\"\n                          v-if=\"col.component && col.needRef\"\n                          ref=\"column\"\n                          :value=\"col.value\"\n                          :row=\"row.row\"\n                          :col=\"col.col\"\n                          v-bind=\"col.col.formatterOpts\"\n                          :row-key=\"row.key\"\n                          :get-custom-detail-link=\"getCustomDetailLink\"\n                        />\n                        <component\n                          :is=\"col.component\"\n                          v-else-if=\"col.component\"\n                          :value=\"col.value\"\n                          :row=\"row.row\"\n                          :col=\"col.col\"\n                          v-bind=\"col.col.formatterOpts\"\n                          :row-key=\"row.key\"\n                        />\n                        <component\n                          :is=\"col.col.formatter\"\n                          v-else-if=\"col.col.formatter\"\n                          :value=\"col.value\"\n                          :row=\"row.row\"\n                          :col=\"col.col\"\n                          v-bind=\"col.col.formatterOpts\"\n                          :row-key=\"row.key\"\n                        />\n                        <template v-else-if=\"col.value !== ''\">\n                          {{ col.formatted }}\n                        </template>\n                        <template v-else-if=\"col.col.dashIfEmpty\">\n                          <span class=\"text-muted\">&mdash;</span>\n                        </template>\n                      </slot>\n                    </td>\n                  </slot>\n                </template>\n                <td\n                  v-if=\"rowActions\"\n                >\n                  <slot\n                    name=\"row-actions\"\n                    :row=\"row.row\"\n                    :index=\"i\"\n                  >\n                    <template v-if=\"featureDropdownMenu\">\n                      <ActionMenu\n                        :resource=\"row.row\"\n                        :data-testid=\"componentTestid + '-' + i + '-action-button'\"\n                        :button-aria-label=\"t('sortableTable.tableActionsLabel', { resource: row?.row?.id || '' })\"\n                      />\n                    </template>\n                    <template v-else>\n                      <ButtonMultiAction\n                        :id=\"`actionButton+${i}+${(row.row && row.row.name) ? row.row.name : ''}`\"\n                        :ref=\"`actionButton${i}`\"\n                        aria-haspopup=\"true\"\n                        aria-expanded=\"false\"\n                        :aria-label=\"t('sortableTable.tableActionsLabel', { resource: row?.row?.id || '' })\"\n                        :data-testid=\"componentTestid + '-' + i + '-action-button'\"\n                        :borderless=\"true\"\n                        @click=\"handleActionButtonClick(i, $event)\"\n                        @keyup.enter=\"handleActionButtonClick(i, $event)\"\n                        @keyup.space=\"handleActionButtonClick(i, $event)\"\n                      />\n                    </template>\n                  </slot>\n                </td>\n              </tr>\n            </slot>\n          </slot>\n          <slot\n            v-if=\"row.showSubRow\"\n            name=\"sub-row\"\n            :full-colspan=\"fullColspan\"\n            :row=\"row.row\"\n            :sub-matches=\"subMatches\"\n            :keyField=\"keyField\"\n            :componentTestid=\"componentTestid\"\n            :i=\"i\"\n            :onRowMouseEnter=\"onRowMouseEnter\"\n            :onRowMouseLeave=\"onRowMouseLeave\"\n          >\n            <tr\n              v-if=\"row.row.stateDescription\"\n              :key=\"row.row[keyField] + '-description'\"\n              :data-testid=\"componentTestid + '-' + i + '-row-description'\"\n              class=\"state-description sub-row\"\n              @mouseenter=\"onRowMouseEnter\"\n              @mouseleave=\"onRowMouseLeave\"\n            >\n              <td\n                v-if=\"tableActions\"\n                class=\"row-check\"\n                align=\"middle\"\n              />\n              <td\n                :colspan=\"fullColspan - (tableActions ? 1: 0)\"\n                :class=\"{ 'text-error' : row.row.stateObj.error }\"\n              >\n                {{ row.row.stateDescription }}\n              </td>\n            </tr>\n          </slot>\n        </template>\n      </tbody>\n    </table>\n    <div\n      v-if=\"showPaging\"\n      class=\"paging\"\n    >\n      <button\n        type=\"button\"\n        class=\"btn btn-sm role-multi-action\"\n        data-testid=\"pagination-first\"\n        :disabled=\"page == 1 || loading\"\n        role=\"button\"\n        :aria-label=\"t('sortableTable.ariaLabel.firstPageBtn')\"\n        @click=\"goToPage('first')\"\n      >\n        <i\n          class=\"icon icon-chevron-beginning\"\n          :alt=\"t('sortableTable.alt.firstPageBtn')\"\n        />\n      </button>\n      <button\n        type=\"button\"\n        class=\"btn btn-sm role-multi-action\"\n        data-testid=\"pagination-prev\"\n        :disabled=\"page == 1 || loading\"\n        role=\"button\"\n        :aria-label=\"t('sortableTable.ariaLabel.prevPageBtn')\"\n        @click=\"goToPage('prev')\"\n      >\n        <i\n          class=\"icon icon-chevron-left\"\n          :alt=\"t('sortableTable.alt.prevPageBtn')\"\n        />\n      </button>\n      <span>\n        {{ pagingDisplay }}\n      </span>\n      <button\n        type=\"button\"\n        class=\"btn btn-sm role-multi-action\"\n        data-testid=\"pagination-next\"\n        :disabled=\"page == totalPages || loading\"\n        role=\"button\"\n        :aria-label=\"t('sortableTable.ariaLabel.nextPageBtn')\"\n        @click=\"goToPage('next')\"\n      >\n        <i\n          class=\"icon icon-chevron-right\"\n          :alt=\"t('sortableTable.alt.nextPageBtn')\"\n        />\n      </button>\n      <button\n        type=\"button\"\n        class=\"btn btn-sm role-multi-action\"\n        data-testid=\"pagination-last\"\n        :disabled=\"page == totalPages || loading\"\n        role=\"button\"\n        :aria-label=\"t('sortableTable.ariaLabel.lastPageBtn')\"\n        @click=\"goToPage('last')\"\n      >\n        <i\n          class=\"icon icon-chevron-end\"\n          :alt=\"t('sortableTable.alt.lastPageBtn')\"\n        />\n      </button>\n    </div>\n    <button\n      v-if=\"search\"\n      v-shortkey.once=\"['/']\"\n      class=\"hide\"\n      @shortkey=\"focusSearch()\"\n    />\n    <template v-if=\"tableActions\">\n      <button\n        v-shortkey=\"['j']\"\n        class=\"hide\"\n        @shortkey=\"focusNext($event)\"\n      />\n      <button\n        v-shortkey=\"['k']\"\n        class=\"hide\"\n        @shortkey=\"focusPrevious($event)\"\n      />\n      <button\n        v-shortkey=\"['shift','j']\"\n        class=\"hide\"\n        @shortkey=\"focusNext($event, true)\"\n      />\n      <button\n        v-shortkey=\"['shift','k']\"\n        class=\"hide\"\n        @shortkey=\"focusPrevious($event, true)\"\n      />\n      <slot name=\"shortkeys\" />\n    </template>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .sortable-table.alt-loading {\n    opacity: 0.5;\n    pointer-events: none;\n  }\n  .advanced-filter-group {\n    position: relative;\n    margin-left: 10px;\n    .advanced-filter-container {\n      position: absolute;\n      top: 38px;\n      right: 0;\n      width: 300px;\n      border: 1px solid var(--primary);\n      background-color: var(--body-bg);\n      padding: 20px;\n      z-index: 2;\n\n      .middle-block {\n        display: flex;\n        align-items: center;\n        margin-top: 20px;\n\n        span {\n          margin-right: 20px;\n        }\n\n        button {\n          margin-left: 20px;\n        }\n      }\n\n      .bottom-block {\n        display: flex;\n        align-items: center;\n        margin-top: 40px;\n        justify-content: space-between;\n      }\n    }\n  }\n\n  .advanced-filters-applied {\n    display: inline-flex;\n    margin: 0;\n    padding: 0;\n    list-style: none;\n    max-width: 100%;\n    flex-wrap: wrap;\n    justify-content: flex-end;\n\n    li {\n      margin: 0 20px 10px 0;\n      padding: 2px 5px;\n      border: 1px solid;\n      display: flex;\n      align-items: center;\n      position: relative;\n      height: 20px;\n\n      &:nth-child(4n+1) {\n        border-color: var(--success);\n\n        .bg {\n          background-color: var(--success);\n        }\n      }\n\n      &:nth-child(4n+2) {\n        border-color: var(--warning);\n\n        .bg {\n          background-color: var(--warning);\n        }\n      }\n\n      &:nth-child(4n+3) {\n        border-color: var(--info);\n\n        .bg {\n          background-color: var(--info);\n        }\n      }\n\n      &:nth-child(4n+4) {\n        border-color: var(--error);\n\n        .bg {\n          background-color: var(--error);\n        }\n      }\n\n      .bg {\n        position: absolute;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n       opacity: 0.2;\n        z-index: -1;\n      }\n\n      .label {\n        margin-right: 10px;\n        font-size: 11px;\n      }\n      .cross {\n        font-size: 12px;\n        font-weight: bold;\n        cursor: pointer;\n      }\n    }\n  }\n\n  td {\n    // Aligns with COLUMN_BREAKPOINTS\n    @media only screen and (max-width: map-get($breakpoints, '--viewport-4')) {\n      // HIDE column on sizes below 480px\n      &.tablet, &.laptop, &.desktop {\n        display: none;\n      }\n    }\n    @media only screen and (max-width: map-get($breakpoints, '--viewport-9')) {\n      // HIDE column on sizes below 992px\n      &.laptop, &.desktop {\n        display: none;\n      }\n    }\n    @media only screen and (max-width: map-get($breakpoints, '--viewport-12')) {\n      // HIDE column on sizes below 1281px\n      &.desktop {\n        display: none;\n      }\n    }\n  }\n\n  // Loading indicator row\n  tr td div.data-loading {\n    align-items: center;\n    display: flex;\n    justify-content: center;\n    padding: 20px 0;\n    > i {\n      font-size: 20px;\n      height: 20px;\n      margin-right: 5px;\n      width: 20px;\n    }\n  }\n\n  .search-box {\n    height: 40px;\n    margin-left: 10px;\n    min-width: 180px;\n  }\n</style>\n\n<style lang=\"scss\">\n  //\n  // Important: Almost all selectors in here need to be \">\"-ed together so they\n  // apply only to the current table, not one nested inside another table.\n  //\n\n  $group-row-height: 40px;\n  $group-separation: 40px;\n  $divider-height: 1px;\n\n  $separator: 20;\n  $remove: 100;\n  $spacing: 10px;\n\n  .filter-select .vs__selected-options .vs__selected {\n    text-align: left;\n  }\n\n  .sortable-table {\n    border-collapse: collapse;\n    min-width: 400px;\n    border-radius: 5px 5px 0 0;\n    outline: 1px solid var(--border);\n    background: var(--sortable-table-bg);\n    border-radius: 4px;\n\n    &.overflow-x {\n      overflow-x: visible;\n    }\n    &.overflow-y {\n      overflow-y: visible;\n    }\n\n    td {\n      padding: 8px 5px;\n      border: 0;\n\n      &:first-child {\n        padding-left: 10px;\n      }\n\n      &:last-child {\n        padding-right: 10px;\n      }\n\n      &.row-check {\n        padding-top: 12px;\n      }\n    }\n\n    tbody {\n      tr {\n        border-bottom: 1px solid var(--sortable-table-top-divider);\n        background-color: var(--sortable-table-row-bg);\n\n        &.main-row.has-sub-row {\n          border-bottom: 0;\n        }\n\n        // if a main-row is hovered also hover it's sibling sub row. note - the reverse is handled in selection.js\n        &.main-row:not(.row-selected):hover + .sub-row {\n          background-color: var(--sortable-table-hover-bg);\n        }\n\n        &:last-of-type {\n          border-bottom: 0;\n        }\n\n        &:hover, &.sub-row-hovered {\n          background-color: var(--sortable-table-hover-bg);\n        }\n\n        &.state-description > td {\n          font-size: 13px;\n          padding-top: 0;\n          overflow-wrap: anywhere;\n        }\n      }\n\n      tr.active-row {\n        color: var(--sortable-table-header-bg);\n      }\n\n      tr.row-selected {\n        background: var(--sortable-table-selected-bg);\n      }\n\n      .no-rows {\n        td {\n          padding: 30px 0;\n          text-align: center;\n        }\n      }\n\n      .no-rows, .no-results {\n        &:hover {\n          background-color: var(--body-bg);\n        }\n      }\n\n      &.group {\n        &:before {\n          content: \"\";\n          display: block;\n          height: 20px;\n          background-color: transparent;\n        }\n      }\n\n      tr.group-row {\n        background-color: initial;\n\n        &:first-child {\n          border-bottom: 2px solid var(--sortable-table-row-bg);\n        }\n\n        &:not(:first-child) {\n          margin-top: 20px;\n        }\n\n        td {\n          padding: 0;\n\n          &:first-of-type {\n            border-left: 1px solid var(--sortable-table-accent-bg);\n          }\n        }\n\n        .group-tab {\n          @include clearfix;\n          height: $group-row-height;\n          line-height: $group-row-height;\n          padding: 0 10px;\n          border-radius: 4px 4px 0px 0px;\n          background-color: var(--sortable-table-row-bg);\n          position: relative;\n          top: 1px;\n          display: inline-block;\n          z-index: z-index('tableGroup');\n          min-width: $group-row-height * 1.8;\n\n          > SPAN {\n            color: var(--sortable-table-group-label);\n          }\n        }\n\n        .group-tab:after {\n          height: $group-row-height;\n          width: 70px;\n          border-radius: 5px 5px 0px 0px;\n          background-color: var(--sortable-table-row-bg);\n          content: \"\";\n          position: absolute;\n          right: -15px;\n          top: 0px;\n          transform: skewX(40deg);\n          z-index: -1;\n        }\n      }\n    }\n  }\n\n  .for-inputs{\n    & TABLE.sortable-table {\n    width: 100%;\n    border-collapse: collapse;\n    margin-bottom: $spacing;\n\n    >TBODY>TR>TD, >THEAD>TR>TH {\n      padding-right: $spacing;\n      padding-bottom: $spacing;\n\n      &:last-of-type {\n        padding-right: 0;\n      }\n    }\n\n    >TBODY>TR:first-of-type>TD {\n      padding-top: $spacing;\n    }\n\n    >TBODY>TR:last-of-type>TD {\n      padding-bottom: 0;\n    }\n  }\n\n    &.edit, &.create, &.clone {\n      TABLE.sortable-table>THEAD>TR>TH {\n      border-color: transparent;\n      }\n    }\n  }\n\n  .sortable-table-header {\n    position: relative;\n    z-index: z-index('fixedTableHeader');\n\n    &.titled {\n      display: flex;\n      align-items: center;\n    }\n  }\n  .fixed-header-actions.button{\n    grid-template-columns: [bulk] auto [middle] min-content [search] minmax(min-content, 350px);\n  }\n\n  .fixed-header-actions {\n    padding: 0 0 20px 0;\n    width: 100%;\n    z-index: z-index('fixedTableHeader');\n    background: transparent;\n    display: grid;\n    grid-template-columns: [bulk] auto [middle] min-content [search] minmax(min-content, 200px);\n    grid-column-gap: 10px;\n\n    &.advanced-filtering {\n      grid-template-columns: [bulk] auto [middle] minmax(min-content, auto) [search] minmax(min-content, auto);\n    }\n\n    .bulk {\n      grid-area: bulk;\n\n      $gap: 10px;\n\n      & > BUTTON {\n        display: none; // Handled dynamically\n      }\n\n      & > BUTTON:not(:last-of-type) {\n        margin-right: $gap;\n      }\n\n      .action-availability {\n        display: none; // Handled dynamically\n        margin-left: $gap;\n        vertical-align: middle;\n        margin-top: 2px;\n      }\n\n      .dropdown-button {\n        $disabled-color: var(--disabled-text);\n        $disabled-cursor: not-allowed;\n        li.disabled {\n          color: $disabled-color;\n          cursor: $disabled-cursor;\n\n          &:hover {\n            color: $disabled-color;\n            background-color: unset;\n            cursor: $disabled-cursor;\n          }\n        }\n      }\n\n      .bulk-action  {\n        .icon {\n          vertical-align: -10%;\n        }\n      }\n    }\n\n    .middle {\n      grid-area: middle;\n      white-space: nowrap;\n\n      .icon.icon-backup.animate {\n        animation-name: spin;\n        animation-duration: 1000ms;\n        animation-iteration-count: infinite;\n        animation-timing-function: linear;\n      }\n\n      @keyframes spin {\n        from {\n          transform:rotate(0deg);\n        }\n        to {\n          transform:rotate(360deg);\n        }\n      }\n    }\n\n    .search {\n      grid-area: search;\n      text-align: right;\n      justify-content: flex-end;\n    }\n\n    .bulk-actions-dropdown {\n      display: none; // Handled dynamically\n\n      .dropdown-button {\n        background-color: var(--primary);\n\n        &:hover {\n          background-color: var(--primary-hover-bg);\n          color: var(--primary-hover-text);\n        }\n\n        > *, .icon-chevron-down {\n          color: var(--primary-text);\n        }\n\n        .button-divider {\n          border-color: var(--primary-text);\n        }\n\n        &.disabled {\n          border-color: var(--disabled-bg);\n\n          .icon-chevron-down {\n            color: var(--disabled-text) !important;\n          }\n\n          .button-divider {\n            border-color: var(--disabled-text);\n          }\n        }\n      }\n    }\n  }\n\n  .paging {\n    margin-top: 10px;\n    text-align: center;\n\n    SPAN {\n      display: inline-block;\n      min-width: 200px;\n    }\n  }\n</style>\n"]}]}