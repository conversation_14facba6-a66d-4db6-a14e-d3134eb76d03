{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/SimpleNodeGroup.vue?vue&type=template&id=794d0ea2&scoped=true&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/SimpleNodeGroup.vue", "mtime": 1755002461158}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/SimpleNodeGroup.vue"], "names": [], "mappings": ";EA6GE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjD,CAAC,CAAC,CAAC;;MAEH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACtC;QACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC,CAAC;cACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B;cACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEf,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B;QACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzE,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC,CAAC;;MAEH,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvD,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd;QACE,CAAC,CAAC;UACA,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC;;MAEH,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/SimpleNodeGroup.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent, PropType } from 'vue';\nimport LabeledInput from '@components/Form/LabeledInput/LabeledInput.vue';\nimport LabeledSelect from '@shell/components/form/LabeledSelect.vue';\nimport UnitInput from '@shell/components/form/UnitInput.vue';\nimport Banner from '@components/Banner/Banner.vue';\nimport { EKSNodeGroup, AWS } from '../../types';\n\nexport default defineComponent({\n  name: 'SimpleNodeGroup',\n\n  components: {\n    LabeledInput,\n    LabeledSelect,\n    UnitInput,\n    Banner\n  },\n\n  props: {\n    modelValue: {\n      type:     Object as PropType<EKSNodeGroup>,\n      required: true\n    },\n    instanceTypeOptions: {\n      type:    Array as PropType<AWS.InstanceTypeOption[]>,\n      default: () => []\n    },\n    rules: {\n      type:    Object,\n      default: () => ({})\n    }\n  },\n\n  emits: ['update:modelValue'],\n\n  computed: {\n    nodeGroup: {\n      get(): EKSNodeGroup {\n        return this.modelValue;\n      },\n      set(value: EKSNodeGroup) {\n        this.$emit('update:modelValue', value);\n      }\n    },\n\n    recommendedInstanceTypes(): AWS.InstanceTypeOption[] {\n      // Filter to show only recommended instance types for PLG users\n      return this.instanceTypeOptions.filter((opt: AWS.InstanceTypeOption) => {\n        if (opt.kind === 'group') return false;\n        const value = opt.value || '';\n        // Recommend t3/t4g series for cost-effectiveness\n        return value.startsWith('t3.') || value.startsWith('t4g.') || \n               value.startsWith('m5.') || value.startsWith('m6i.');\n      });\n    },\n\n    estimatedMonthlyCost(): string {\n      // Simple cost estimation based on instance type and count\n      const costMap: Record<string, number> = {\n        't3.small':   15,\n        't3.medium':  30,\n        't3.large':   60,\n        't3.xlarge':  120,\n        't4g.small':  12,\n        't4g.medium': 24,\n        't4g.large':  48,\n        'm5.large':   70,\n        'm5.xlarge':  140,\n        'm6i.large':  70,\n        'm6i.xlarge': 140,\n      };\n\n      const instanceCost = this.nodeGroup.instanceType ? costMap[this.nodeGroup.instanceType] || 50 : 50;\n      const totalCost = instanceCost * (this.nodeGroup.desiredSize || 2);\n      \n      return `~$${totalCost}/month`;\n    }\n  },\n\n  methods: {\n    updateInstanceType(value: string) {\n      this.nodeGroup.instanceType = value;\n    },\n\n    updateNodeCount(field: 'minSize' | 'maxSize' | 'desiredSize', value: string) {\n      const numValue = parseInt(value, 10) || 0;\n      this.nodeGroup[field] = numValue;\n      \n      // Ensure logical constraints\n      if (field === 'minSize' && numValue > (this.nodeGroup.maxSize || 0)) {\n        this.nodeGroup.maxSize = numValue;\n      }\n      if (field === 'maxSize' && numValue < (this.nodeGroup.minSize || 0)) {\n        this.nodeGroup.minSize = numValue;\n      }\n      if (field === 'desiredSize') {\n        if (numValue < (this.nodeGroup.minSize || 0)) {\n          this.nodeGroup.minSize = numValue;\n        }\n        if (numValue > (this.nodeGroup.maxSize || 0)) {\n          this.nodeGroup.maxSize = numValue;\n        }\n      }\n    }\n  }\n});\n</script>\n\n<template>\n  <div class=\"simple-node-group\">\n    <div class=\"node-config-section\">\n      <h4>Instance Type</h4>\n      <p class=\"text-muted mb-10\">\n        Select the computing power for your worker nodes\n      </p>\n      \n      <LabeledSelect\n        v-model:value=\"nodeGroup.instanceType\"\n        label=\"Instance Type\"\n        :options=\"recommendedInstanceTypes\"\n        :rules=\"rules.instanceType\"\n        :searchable=\"true\"\n        placeholder=\"Select an instance type\"\n      >\n        <template #option=\"{ option }\">\n          <div class=\"instance-option\">\n            <span>{{ option.label }}</span>\n            <span\n              v-if=\"option.value?.startsWith('t3.') || option.value?.startsWith('t4g.')\"\n              class=\"badge-recommended\"\n            >\n              Recommended\n            </span>\n          </div>\n        </template>\n      </LabeledSelect>\n\n      <div\n        v-if=\"nodeGroup.instanceType\"\n        class=\"cost-estimate mt-10\"\n      >\n        <i class=\"icon icon-dollar\" />\n        <span>Estimated cost: <strong>{{ estimatedMonthlyCost }}</strong></span>\n      </div>\n    </div>\n\n    <div class=\"node-config-section mt-20\">\n      <h4>Cluster Size</h4>\n      <p class=\"text-muted mb-10\">\n        Configure auto-scaling for your cluster\n      </p>\n\n      <div class=\"row\">\n        <div class=\"col span-4\">\n          <UnitInput\n            v-model:value=\"nodeGroup.minSize\"\n            label=\"Minimum Nodes\"\n            suffix=\"nodes\"\n            :rules=\"rules.minSize\"\n            @update:value=\"updateNodeCount('minSize', $event)\"\n          />\n        </div>\n        <div class=\"col span-4\">\n          <UnitInput\n            v-model:value=\"nodeGroup.desiredSize\"\n            label=\"Desired Nodes\"\n            suffix=\"nodes\"\n            :rules=\"rules.desiredSize\"\n            @update:value=\"updateNodeCount('desiredSize', $event)\"\n          />\n        </div>\n        <div class=\"col span-4\">\n          <UnitInput\n            v-model:value=\"nodeGroup.maxSize\"\n            label=\"Maximum Nodes\"\n            suffix=\"nodes\"\n            :rules=\"rules.maxSize\"\n            @update:value=\"updateNodeCount('maxSize', $event)\"\n          />\n        </div>\n      </div>\n\n      <Banner\n        color=\"info\"\n        class=\"mt-10\"\n      >\n        <p>\n          Your cluster will automatically scale between \n          <strong>{{ nodeGroup.minSize }}</strong> and \n          <strong>{{ nodeGroup.maxSize }}</strong> nodes based on workload.\n        </p>\n      </Banner>\n    </div>\n\n    <div class=\"node-config-section mt-20\">\n      <h4>Storage</h4>\n      <p class=\"text-muted mb-10\">\n        Disk space for each node\n      </p>\n      \n      <div class=\"row\">\n        <div class=\"col span-6\">\n          <UnitInput\n            v-model:value=\"nodeGroup.diskSize\"\n            label=\"Disk Size\"\n            suffix=\"GB\"\n            :rules=\"rules.diskSize\"\n          />\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.simple-node-group {\n  .node-config-section {\n    padding: 15px;\n    background: var(--body-bg);\n    border: 1px solid var(--border);\n    border-radius: var(--border-radius);\n\n    h4 {\n      margin: 0 0 5px 0;\n      font-size: 16px;\n      font-weight: 600;\n    }\n\n    .text-muted {\n      color: var(--text-muted);\n      font-size: 14px;\n    }\n  }\n\n  .instance-option {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    width: 100%;\n\n    .badge-recommended {\n      background: var(--success);\n      color: var(--success-text);\n      padding: 2px 8px;\n      border-radius: 3px;\n      font-size: 11px;\n      font-weight: 600;\n      text-transform: uppercase;\n    }\n  }\n\n  .cost-estimate {\n    padding: 10px;\n    background: var(--info-banner-bg);\n    border: 1px solid var(--info);\n    border-radius: var(--border-radius);\n    display: flex;\n    align-items: center;\n    gap: 10px;\n    color: var(--text-default);\n\n    i {\n      color: var(--info);\n    }\n\n    strong {\n      color: var(--text-default);\n      font-weight: 600;\n    }\n  }\n}\n</style>\n\n"]}]}