{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/KnownHostsEditDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/KnownHostsEditDialog.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCB7IF9FRElULCBfVklFVyB9IGZyb20gJ0BzaGVsbC9jb25maWcvcXVlcnktcGFyYW1zJzsKaW1wb3J0IENvZGVNaXJyb3IgZnJvbSAnQHNoZWxsL2NvbXBvbmVudHMvQ29kZU1pcnJvcic7CmltcG9ydCBGaWxlU2VsZWN0b3IgZnJvbSAnQHNoZWxsL2NvbXBvbmVudHMvZm9ybS9GaWxlU2VsZWN0b3IudnVlJzsKaW1wb3J0IEFwcE1vZGFsIGZyb20gJ0BzaGVsbC9jb21wb25lbnRzL0FwcE1vZGFsLnZ1ZSc7CgpleHBvcnQgZGVmYXVsdCB7CiAgZW1pdHM6IFsnY2xvc2VkJ10sCgogIGNvbXBvbmVudHM6IHsKICAgIEZpbGVTZWxlY3RvciwKICAgIEFwcE1vZGFsLAogICAgQ29kZU1pcnJvciwKICB9LAoKICBwcm9wczogewogICAgdmFsdWU6IHsKICAgICAgdHlwZTogICAgIFN0cmluZywKICAgICAgcmVxdWlyZWQ6IHRydWUKICAgIH0sCgogICAgbW9kZTogewogICAgICB0eXBlOiAgICBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6IF9FRElUCiAgICB9LAogIH0sCgogIGRhdGEoKSB7CiAgICBjb25zdCBjb2RlTWlycm9yT3B0aW9ucyA9IHsKICAgICAgcmVhZE9ubHk6ICAgICAgICB0aGlzLmlzVmlldywKICAgICAgZ3V0dGVyczogICAgICAgICBbJ0NvZGVNaXJyb3ItZm9sZGd1dHRlciddLAogICAgICBtb2RlOiAgICAgICAgICAgICd0ZXh0L3gtcHJvcGVydGllcycsCiAgICAgIGxpbnQ6ICAgICAgICAgICAgZmFsc2UsCiAgICAgIGxpbmVOdW1iZXJzOiAgICAgIXRoaXMuaXNWaWV3LAogICAgICBzdHlsZUFjdGl2ZUxpbmU6IGZhbHNlLAogICAgICB0YWJTaXplOiAgICAgICAgIDIsCiAgICAgIGluZGVudFdpdGhUYWJzOiAgZmFsc2UsCiAgICAgIGN1cnNvckJsaW5rUmF0ZTogNTMwLAogICAgfTsKCiAgICByZXR1cm4gewogICAgICBjb2RlTWlycm9yT3B0aW9ucywKICAgICAgdGV4dDogICAgICB0aGlzLnZhbHVlLAogICAgICBzaG93TW9kYWw6IGZhbHNlLAogICAgfTsKICB9LAoKICBjb21wdXRlZDogewogICAgaXNWaWV3KCkgewogICAgICByZXR1cm4gdGhpcy5tb2RlID09PSBfVklFVzsKICAgIH0KICB9LAoKICBtZXRob2RzOiB7CiAgICBvblRleHRDaGFuZ2UodmFsdWUpIHsKICAgICAgdGhpcy50ZXh0ID0gdmFsdWU/LnRyaW0oKTsKICAgIH0sCgogICAgc2hvd0RpYWxvZygpIHsKICAgICAgdGhpcy5zaG93TW9kYWwgPSB0cnVlOwogICAgfSwKCiAgICBjbG9zZURpYWxvZyhyZXN1bHQpIHsKICAgICAgaWYgKCFyZXN1bHQpIHsKICAgICAgICB0aGlzLnRleHQgPSB0aGlzLnZhbHVlOwogICAgICB9CgogICAgICB0aGlzLnNob3dNb2RhbCA9IGZhbHNlOwoKICAgICAgdGhpcy4kZW1pdCgnY2xvc2VkJywgewogICAgICAgIHN1Y2Nlc3M6IHJlc3VsdCwKICAgICAgICB2YWx1ZTogICB0aGlzLnRleHQsCiAgICAgIH0pOwogICAgfSwKICB9Cn07Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/KnownHostsEditDialog.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAErD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC;IACJ,CAAC;EACH;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/KnownHostsEditDialog.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\nimport CodeMirror from '@shell/components/CodeMirror';\nimport FileSelector from '@shell/components/form/FileSelector.vue';\nimport AppModal from '@shell/components/AppModal.vue';\n\nexport default {\n  emits: ['closed'],\n\n  components: {\n    FileSelector,\n    AppModal,\n    CodeMirror,\n  },\n\n  props: {\n    value: {\n      type:     String,\n      required: true\n    },\n\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n  },\n\n  data() {\n    const codeMirrorOptions = {\n      readOnly:        this.isView,\n      gutters:         ['CodeMirror-foldgutter'],\n      mode:            'text/x-properties',\n      lint:            false,\n      lineNumbers:     !this.isView,\n      styleActiveLine: false,\n      tabSize:         2,\n      indentWithTabs:  false,\n      cursorBlinkRate: 530,\n    };\n\n    return {\n      codeMirrorOptions,\n      text:      this.value,\n      showModal: false,\n    };\n  },\n\n  computed: {\n    isView() {\n      return this.mode === _VIEW;\n    }\n  },\n\n  methods: {\n    onTextChange(value) {\n      this.text = value?.trim();\n    },\n\n    showDialog() {\n      this.showModal = true;\n    },\n\n    closeDialog(result) {\n      if (!result) {\n        this.text = this.value;\n      }\n\n      this.showModal = false;\n\n      this.$emit('closed', {\n        success: result,\n        value:   this.text,\n      });\n    },\n  }\n};\n</script>\n\n<template>\n  <app-modal\n    v-if=\"showModal\"\n    ref=\"sshKnownHostsDialog\"\n    data-testid=\"sshKnownHostsDialog\"\n    height=\"auto\"\n    :scrollable=\"true\"\n    @close=\"closeDialog(false)\"\n  >\n    <div\n      class=\"ssh-known-hosts-dialog\"\n    >\n      <h4 class=\"mt-10\">\n        {{ t('secret.ssh.editKnownHosts.title') }}\n      </h4>\n      <div class=\"custom mt-10\">\n        <div class=\"dialog-panel\">\n          <CodeMirror\n            :value=\"text\"\n            data-testid=\"ssh-known-hosts-dialog_code-mirror\"\n            :options=\"codeMirrorOptions\"\n            :showKeyMapBox=\"true\"\n            @onInput=\"onTextChange\"\n          />\n        </div>\n        <div class=\"dialog-actions\">\n          <div class=\"action-pannel file-selector\">\n            <FileSelector\n              class=\"btn role-secondary\"\n              data-testid=\"ssh-known-hosts-dialog_file-selector\"\n              :label=\"t('generic.readFromFile')\"\n              @selected=\"onTextChange\"\n            />\n          </div>\n          <div class=\"action-pannel form-actions\">\n            <button\n              class=\"btn role-secondary\"\n              data-testid=\"ssh-known-hosts-dialog_cancel-btn\"\n              @click=\"closeDialog(false)\"\n            >\n              {{ t('generic.cancel') }}\n            </button>\n            <button\n              class=\"btn role-primary\"\n              data-testid=\"ssh-known-hosts-dialog_save-btn\"\n              @click=\"closeDialog(true)\"\n            >\n              {{ t('generic.save') }}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </app-modal>\n</template>\n\n<style lang=\"scss\" scoped>\n  .ssh-known-hosts-dialog {\n    padding: 15px;\n\n    h4 {\n      font-weight: bold;\n      margin-bottom: 20px;\n    }\n\n    .dialog-panel {\n      display: flex;\n      flex-direction: column;\n      min-height: 100px;\n\n      :deep() .code-mirror {\n        display: flex;\n        flex-direction: column;\n        resize: none;\n\n        .codemirror-container {\n          border: 1px solid var(--border);\n        }\n\n        .CodeMirror,\n        .CodeMirror-gutters {\n          min-height: 400px;\n          max-height: 400px;\n          background-color: var(--yaml-editor-bg);\n        }\n\n        .CodeMirror-gutters {\n          width: 25px;\n        }\n\n        .CodeMirror-linenumber {\n          padding-left: 0;\n        }\n      }\n    }\n\n    .dialog-actions {\n      display: flex;\n      justify-content: space-between;\n\n      .action-pannel {\n        margin-top: 10px;\n      }\n\n      .form-actions {\n        display: flex;\n        justify-content: flex-end;\n\n        > *:not(:last-child) {\n          margin-right: 10px;\n        }\n      }\n    }\n  }\n</style>\n"]}]}