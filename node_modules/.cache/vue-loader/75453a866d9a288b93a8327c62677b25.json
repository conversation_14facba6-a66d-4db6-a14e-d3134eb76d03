{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceLabeledSelect.vue?vue&type=template&id=0cbb850e&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceLabeledSelect.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPExhYmVsZWRTZWxlY3QKICAgIHYtYmluZD0ibGFiZWxTZWxlY3RBdHRyaWJ1dGVzIgogICAgOmxvYWRpbmc9IiRmZXRjaFN0YXRlLnBlbmRpbmciCiAgICA6b3B0aW9ucz0iYWxsT2ZUeXBlIgogICAgOnBhZ2luYXRlPSJwYWdpbmF0ZVR5cGUiCiAgICBAdXBkYXRlOnZhbHVlPSIkZW1pdCgndXBkYXRlOnZhbHVlJywgJGV2ZW50KSIKICAvPgo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceLabeledSelect.vue"], "names": [], "mappings": ";EAkJE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9C,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceLabeledSelect.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { PropType, defineComponent } from 'vue';\nimport LabeledSelect from '@shell/components/form/LabeledSelect.vue';\nimport { PaginationParamFilter } from '@shell/types/store/pagination.types';\nimport { labelSelectPaginationFunction, LabelSelectPaginationFunctionOptions } from '@shell/components/form/labeled-select-utils/labeled-select.utils';\nimport { LabelSelectPaginateFnOptions, LabelSelectPaginateFnResponse } from '@shell/types/components/labeledSelect';\nimport { RESOURCE_LABEL_SELECT_MODE, ResourceLabeledSelectPaginateSettings, ResourceLabeledSelectSettings } from '@shell/types/components/resourceLabeledSelect';\n\n/**\n * Convenience  wrapper around the LabelSelect component to support pagination\n *\n * Handles\n *\n * 1) Conditionally enabling the pagination feature given system settings\n * 2) Helper function to fetch the pagination result\n *\n * A number of ways can be provided to override the conveniences (see props)\n */\nexport default defineComponent({\n  name: 'ResourceLabeledSelect',\n\n  components: { LabeledSelect },\n\n  emits: ['update:value'],\n\n  props: {\n    /**\n     * Resource to show\n     */\n    resourceType: {\n      type:     String,\n      required: true\n    },\n\n    inStore: {\n      type:    String,\n      default: 'cluster',\n    },\n\n    /**\n     * Determine if pagination is used via settings (DYNAMIC) or hardcode off\n     */\n    paginateMode: {\n      type:    String as PropType<RESOURCE_LABEL_SELECT_MODE>,\n      default: RESOURCE_LABEL_SELECT_MODE.DYNAMIC,\n    },\n\n    /**\n     * Specific settings to use when we're showing all results in the drop down\n     */\n    allResourcesSettings: {\n      type:    Object as PropType<ResourceLabeledSelectSettings>,\n      default: null,\n    },\n\n    /**\n     * Specific settings to use when we're showing paginated results in the drop down\n     */\n    paginatedResourceSettings: {\n      type:    Object as PropType<ResourceLabeledSelectPaginateSettings>,\n      default: null,\n    },\n  },\n\n  data() {\n    return { paginate: false };\n  },\n\n  async fetch() {\n    switch (this.paginateMode) {\n    case RESOURCE_LABEL_SELECT_MODE.ALL_RESOURCES:\n      this.paginate = false;\n      break;\n    case RESOURCE_LABEL_SELECT_MODE.DYNAMIC:\n      this.paginate = this.$store.getters[`${ this.inStore }/paginationEnabled`](this.resourceType);\n      break;\n    }\n\n    if (!this.paginate) {\n      // The resource won't be paginated and component expects everything up front\n      await this.$store.dispatch(`${ this.inStore }/findAll`, { type: this.resourceType });\n    }\n  },\n\n  computed: {\n    labelSelectAttributes() {\n      // This component is a wrapper for LabelSelect, so pass through everything\n      const allAttrs = {\n        ...this.$attrs, // Attributes (other than props)\n        ...this.$props, // Attributes that are props\n      };\n\n      return this.paginate ? {\n        ...allAttrs,\n        ...this.paginatedResourceSettings?.labelSelectOptions || {}\n      } : {\n        ...allAttrs,\n        ...this.allResourcesSettings?.labelSelectOptions || {}\n      };\n    },\n\n    allOfType() {\n      if (this.$fetchState.pending || this.paginate) {\n        return [];\n      }\n\n      const all = this.$store.getters[`${ this.inStore }/all`](this.resourceType);\n\n      return this.allResourcesSettings?.updateResources ? this.allResourcesSettings.updateResources(all) : all;\n    }\n  },\n\n  methods: {\n    /**\n     * Make the request to fetch the resource given the state of the label select (filter, page, page size, etc see LabelSelectPaginateFn)\n     * opts: Typeof LabelSelectPaginateFn\n     */\n    async paginateType(opts: LabelSelectPaginateFnOptions): Promise<LabelSelectPaginateFnResponse> {\n      if (this.paginatedResourceSettings?.overrideRequest) {\n        return await this.paginatedResourceSettings.overrideRequest(opts);\n      }\n\n      const { filter } = opts;\n      const filters = !!filter ? [PaginationParamFilter.createSingleField({\n        field: 'metadata.name', value: filter, exact: false\n      })] : [];\n      const defaultOptions: LabelSelectPaginationFunctionOptions = {\n        opts,\n        filters,\n        type: this.resourceType,\n        ctx:  { getters: this.$store.getters, dispatch: this.$store.dispatch },\n        sort: [{ asc: true, field: 'metadata.name' }],\n      };\n      const options = this.paginatedResourceSettings?.requestSettings ? this.paginatedResourceSettings.requestSettings(defaultOptions) : defaultOptions;\n      const res = await labelSelectPaginationFunction(options);\n\n      return this.paginatedResourceSettings?.updateResources ? {\n        ...res,\n        page: this.paginatedResourceSettings.updateResources(res.page)\n      } : res;\n    },\n  },\n});\n</script>\n\n<template>\n  <LabeledSelect\n    v-bind=\"labelSelectAttributes\"\n    :loading=\"$fetchState.pending\"\n    :options=\"allOfType\"\n    :paginate=\"paginateType\"\n    @update:value=\"$emit('update:value', $event)\"\n  />\n</template>\n"]}]}