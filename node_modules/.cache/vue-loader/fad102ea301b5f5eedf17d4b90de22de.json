{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/SelectPrincipal.vue?vue&type=style&index=0&id=09260a6e&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/SelectPrincipal.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLnNlYXJjaC1zbG90ewogICAgY29sb3I6IHZhcigtLWJvZHktdGV4dCk7CiAgfQoKICAuc2VsZWN0LXByaW5jaXBhbCB7CiAgICAmLnJldGFpbi1zZWxlY3Rpb24gewogICAgICBtaW4taGVpZ2h0OiA5MXB4OwogICAgICAmLmZvY3VzZWQgewogICAgICAgIC5wcmluY2lwYWwgewogICAgICAgICAgZGlzcGxheTogbm9uZTsKICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/SelectPrincipal.vue"], "names": [], "mappings": ";EAuOE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf;MACF;IACF;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/SelectPrincipal.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport Principal from '@shell/components/auth/Principal';\nimport debounce from 'lodash/debounce';\nimport { _EDIT } from '@shell/config/query-params';\nimport { NORMAN } from '@shell/config/types';\n\nexport default {\n  emits: ['add'],\n\n  components: {\n    LabeledSelect,\n    Principal,\n  },\n\n  props: {\n    mode: {\n      type:    String,\n      default: _EDIT,\n    },\n\n    showMyGroupTypes: {\n      type: Array,\n      default(props) {\n        return ['group', 'user'];\n      },\n    },\n\n    searchGroupTypes: {\n      type:    String,\n      default: null,\n      validator(val) {\n        return val === null || val === 'group' || val === 'user';\n      }\n    },\n\n    retainSelection: {\n      type:    Boolean,\n      default: false\n    },\n\n    project: {\n      type:    Boolean,\n      default: false\n    }\n  },\n\n  async fetch() {\n    this.principals = await this.$store.dispatch('rancher/findAll', {\n      type: NORMAN.PRINCIPAL,\n      opt:  { url: '/v3/principals' }\n    });\n\n    this.options = this.suggested;\n  },\n\n  data() {\n    return {\n      principals:     null,\n      searchStr:      '',\n      options:        [],\n      newValue:       '',\n      tooltipContent: null,\n    };\n  },\n\n  computed: {\n    suggested() {\n      const out = this.principals.filter((x) => {\n        if ( !x.memberOf ) {\n          return false;\n        }\n\n        if ( !this.showMyGroupTypes.includes(x.principalType) ) {\n          return false;\n        }\n\n        return true;\n      })\n        .sort((a, b) => a.name.localeCompare(b.name))\n        .map((x) => x.id);\n\n      return out;\n    },\n\n    label() {\n      return this.retainSelection ? this.t('cluster.memberRoles.addClusterMember.labelSelect') : this.t('cluster.memberRoles.addClusterMember.labelAdd');\n    },\n\n    placeholder() {\n      return this.project ? this.t('projectMembers.projectPermissions.searchForMember') : this.t('cluster.memberRoles.addClusterMember.placeholder');\n    }\n  },\n\n  created() {\n    this.debouncedSearch = debounce(this.search, 200);\n  },\n\n  methods: {\n    setTooltipContent() {\n      if (!this.retainSelection) {\n        return;\n      }\n      if (this.principals) {\n        const selected = this.principals.find((p) => p.id === this.newValue);\n\n        this.tooltipContent = selected?.name;\n      } else {\n        this.tooltipContent = null;\n      }\n    },\n    resetTooltipContent() {\n      if (!this.retainSelection) {\n        return;\n      }\n      this.tooltipContent = null;\n    },\n    add(id) {\n      if (!id) {\n        // Ignore attempts to select an invalid principal\n        return;\n      }\n\n      this.$emit('add', id);\n      if (!this.retainSelection) {\n        this.newValue = '';\n      }\n    },\n\n    onSearch(str, loading) {\n      str = (str || '').trim();\n\n      this.searchStr = str;\n\n      if ( str ) {\n        loading(true);\n        this.debouncedSearch(str, loading);\n      } else {\n        this.search(null, loading);\n      }\n    },\n\n    async search(str, loading) {\n      if ( !str ) {\n        this.options = this.suggested.slice();\n        loading(false);\n\n        return;\n      }\n\n      try {\n        const res = await this.$store.dispatch('rancher/collectionAction', {\n          type:       NORMAN.PRINCIPAL,\n          actionName: 'search',\n          opt:        { url: '/v3/principals?action=search' },\n          body:       {\n            name:          str,\n            principalType: this.searchGroupTypes\n          }\n        });\n\n        if ( this.searchStr === str ) {\n          // If not, they've already typed something else\n          this.options = res.map((x) => x.id);\n        }\n      } catch (e) {\n        this.options = [];\n      } finally {\n        loading(false);\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <LabeledSelect\n    ref=\"labeled-select\"\n    v-model:value=\"newValue\"\n    v-clean-tooltip=\"{\n      content: tooltipContent,\n      placement: 'bottom',\n      popperClass: ['select-principal-tooltip']\n    }\"\n    :mode=\"mode\"\n    :label=\"label\"\n    :placeholder=\"placeholder\"\n    :options=\"options\"\n    :searchable=\"true\"\n    :filterable=\"false\"\n    class=\"select-principal\"\n    :class=\"{'retain-selection': retainSelection}\"\n    @update:value=\"add\"\n    @search=\"onSearch\"\n    @on-open=\"resetTooltipContent()\"\n    @on-close=\"setTooltipContent()\"\n  >\n    <template v-slot:no-options=\"{ searching }\">\n      <template v-if=\"searching\">\n        <span class=\"search-slot\">\n          {{ t('cluster.memberRoles.addClusterMember.noResults') }}\n        </span>\n      </template>\n      <div v-else>\n        <em class=\"search-slot\">\n          {{ t('cluster.memberRoles.addClusterMember.searchPlaceholder') }}\n        </em>\n      </div>\n    </template>\n\n    <template #option=\"option\">\n      <Principal\n        :value=\"option.label\"\n        :use-muted=\"false\"\n      />\n    </template>\n\n    <template\n      v-if=\"retainSelection\"\n      #selected-option=\"option\"\n    >\n      <Principal\n        :value=\"option.label\"\n        :use-muted=\"false\"\n        class=\"mt-10 mb-10\"\n      />\n    </template>\n  </LabeledSelect>\n</template>\n\n<style lang=\"scss\" scoped>\n  .search-slot{\n    color: var(--body-text);\n  }\n\n  .select-principal {\n    &.retain-selection {\n      min-height: 91px;\n      &.focused {\n        .principal {\n          display: none;\n        }\n      }\n    }\n  }\n</style>\n<style lang=\"scss\">\n  .vs__dropdown-menu {\n    width: 0%;\n    * {\n      overflow-x: hidden;\n      text-overflow: ellipsis;\n    }\n  }\n\n  .select-principal-tooltip {\n    max-width: 580px;\n    word-wrap: break-word;\n  }\n</style>\n"]}]}