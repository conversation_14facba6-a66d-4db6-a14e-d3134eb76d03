{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/NamespaceRow.vue?vue&type=style&index=0&id=2b81bebc&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/NamespaceRow.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLnJlc291cmNlLWF2YWlsYWJpbGl0eSB7CiAgICBhbGlnbi1zZWxmOiBjZW50ZXI7CiAgfQogIC5yb3cgewogICAgZGlzcGxheTogZmxleDsKICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7CiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWV2ZW5seTsKCiAgICAmID4gKiB7CiAgICAgIHdpZHRoOiAxMDAlOwogICAgfQogIH0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/NamespaceRow.vue"], "names": [], "mappings": ";EA+ME,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB;EACA,CAAC,CAAC,CAAC,EAAE;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE7B,EAAE,EAAE,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/NamespaceRow.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport Select from '@shell/components/form/Select';\nimport UnitInput from '@shell/components/form/UnitInput';\nimport PercentageBar from '@shell/components/PercentageBar';\nimport { formatSi, parseSi } from '@shell/utils/units';\nimport { ROW_COMPUTED } from './shared';\n\nexport default {\n  emits: ['update:value'],\n\n  components: {\n    Select, PercentageBar, UnitInput\n  },\n\n  props: {\n    mode: {\n      type:     String,\n      required: true,\n    },\n    types: {\n      type:    Array,\n      default: () => []\n    },\n    type: {\n      type:     String,\n      required: true\n    },\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    namespace: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    projectResourceQuotaLimits: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    namespaceResourceQuotaLimits: {\n      type:    Array,\n      default: () => {\n        return [];\n      }\n    },\n    defaultResourceQuotaLimits: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  mounted() {\n    // We want to update the value first so that the value will be rounded to the project limit.\n    // This is relevant when switching projects. If the value is 1200 and the project that it was\n    // switched to only has capacity for 800 more this will force the value to be set to 800.\n    if (this.value?.limit?.[this.type]) {\n      this.update(this.value.limit[this.type]);\n    }\n\n    if (!this.value?.limit?.[this.type]) {\n      this.update(this.defaultResourceQuotaLimits[this.type]);\n    }\n  },\n\n  computed: {\n    ...ROW_COMPUTED,\n    limitValue() {\n      return parseSi(this.projectResourceQuotaLimits[this.type]);\n    },\n    siOptions() {\n      return {\n        maxExponent: this.typeOption.inputExponent,\n        minExponent: this.typeOption.inputExponent,\n        increment:   this.typeOption.increment,\n        suffix:      this.typeOption.increment === 1024 ? 'i' : ''\n      };\n    },\n    namespaceLimits() {\n      return this.namespaceResourceQuotaLimits\n        .filter((resourceQuota) => resourceQuota[this.type] && resourceQuota.id !== this.namespace.id)\n        .map((resourceQuota) => parseSi(resourceQuota[this.type], this.siOptions));\n    },\n    namespaceContribution() {\n      return this.namespaceLimits.reduce((sum, limit) => sum + limit, 0);\n    },\n    totalContribution() {\n      return this.namespaceContribution + parseSi(this.value.limit[this.type] || '0', this.siOptions);\n    },\n    percentageUsed() {\n      return Math.min(this.totalContribution * 100 / this.projectLimit, 100);\n    },\n    projectLimit() {\n      return parseSi(this.projectResourceQuotaLimits[this.type] || 0, this.siOptions);\n    },\n    max() {\n      return this.projectLimit - this.namespaceContribution;\n    },\n    availableResourceQuotas() {\n      return formatSi(this.projectLimit - this.totalContribution, { ...this.siOptions, addSuffixSpace: false });\n    },\n    slices() {\n      const out = [];\n\n      this.namespaceLimits.forEach((limit, i) => {\n        const lastValue = i > 0 ? this.namespaceLimits[i - 1] : 0;\n        const sliceTotal = lastValue + limit;\n\n        out.push(sliceTotal * 100 / this.projectLimit);\n      });\n\n      return out;\n    },\n    tooltip() {\n      const t = this.$store.getters['i18n/t'];\n      const out = [\n        {\n          label: t('resourceQuota.tooltip.reserved'),\n          value: formatSi(this.namespaceContribution, { ...this.siOptions, addSuffixSpace: false }),\n        },\n        {\n          label: t('resourceQuota.tooltip.namespace'),\n          value: this.value.limit[this.type]\n        },\n        {\n          label: t('resourceQuota.tooltip.available'),\n          value: this.availableResourceQuotas\n        },\n        {\n          label: t('resourceQuota.tooltip.max'),\n          value: this.projectResourceQuotaLimits[this.type]\n        }\n      ];\n\n      let formattedTooltip = '<div class=\"quota-percentage-tooltip\">';\n\n      (out || []).forEach((v) => {\n        formattedTooltip += `\n        <div style='margin-top: 5px; display: flex; justify-content: space-between;'>\n          ${ v.label }\n          <span style='margin-left: 20px;'>${ v.value }</span>\n        </div>`;\n      });\n      formattedTooltip += '</div>';\n\n      return formattedTooltip;\n    },\n\n  },\n\n  methods: {\n    update(newValue) {\n      const parsedNewValue = parseSi(newValue, this.siOptions) || 0;\n      const min = Math.max(parsedNewValue, 0);\n      const max = Math.min(min, this.max);\n      const value = formatSi(max, {\n        ...this.siOptions,\n        addSuffixSpace: false\n      });\n\n      this.$emit('update:value', this.type, value);\n    }\n  }\n};\n</script>\n<template>\n  <div\n    v-if=\"typeOption\"\n    class=\"row\"\n  >\n    <Select\n      class=\"mr-10\"\n      :mode=\"mode\"\n      :value=\"type\"\n      :disabled=\"true\"\n      :options=\"types\"\n    />\n    <div class=\"resource-availability mr-10\">\n      <PercentageBar\n        v-clean-tooltip=\"tooltip\"\n        class=\"percentage-bar\"\n        :modelValue=\"percentageUsed\"\n        :slices=\"slices\"\n        :color-stops=\"{'100': '--primary'}\"\n      />\n    </div>\n    <UnitInput\n      :value=\"value.limit[type]\"\n      :mode=\"mode\"\n      :placeholder=\"typeOption.placeholder\"\n      :increment=\"typeOption.increment\"\n      :input-exponent=\"typeOption.inputExponent\"\n      :base-unit=\"typeOption.baseUnit\"\n      :output-modifier=\"true\"\n      @update:value=\"update\"\n    />\n  </div>\n</template>\n\n<style lang='scss' scoped>\n  .resource-availability {\n    align-self: center;\n  }\n  .row {\n    display: flex;\n    flex-direction: row;\n    justify-content: space-evenly;\n\n    & > * {\n      width: 100%;\n    }\n  }\n</style>\n"]}]}