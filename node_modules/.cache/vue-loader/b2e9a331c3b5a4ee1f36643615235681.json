{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Circle.vue?vue&type=template&id=d1de6198&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Circle.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Circle.vue"], "names": [], "mappings": ";EAsFE,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB;IACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR;UACE,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/D,CAAC;UACD,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR;UACE,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE,CAAC;UACD,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC;IACH,CAAC,CAAC,CAAC;;IAEH,CAAC,CAAC,CAAC,CAAC;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC;MACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACR,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Circle.vue", "sourceRoot": "", "sourcesContent": ["<script>\nlet id = 0;\n\nexport default {\n  props: {\n    percentage: {\n      type:    Number,\n      default: 0.75\n    },\n    strokeWidth: {\n      type:    Number,\n      default: 22\n    },\n    primaryStrokeColor: {\n      type:     String,\n      required: true\n    },\n    primaryStrokeGradientColor: {\n      type:    String,\n      default: null\n    },\n    secondaryStrokeColor: {\n      type:     String,\n      required: true\n    },\n    secondaryStrokeGradientColor: {\n      type:    String,\n      default: null\n    },\n    rotate: {\n      type:    Number,\n      default: 90\n    },\n    showText: {\n      type:    Boolean,\n      default: false\n    },\n  },\n  data() {\n    return { id: id++ };\n  },\n  computed: {\n    viewportSize() {\n      return 100;\n    },\n    radius() {\n      const outerRadius = this.viewportSize / 2;\n      const halfStrokeWidth = this.strokeWidth / 2;\n\n      return outerRadius - halfStrokeWidth;\n    },\n    center() {\n      return this.viewportSize / 2;\n    },\n    viewBox() {\n      return `0 0 ${ this.viewportSize } ${ this.viewportSize }`;\n    },\n    circumference() {\n      return 2 * Math.PI * this.radius;\n    },\n    transform() {\n      return `rotate(${ this.rotate }, ${ this.center }, ${ this.center })`;\n    },\n    strokeDasharray() {\n      // This needs to be the circumference of the circle in order to allow the path to be filled\n      return this.circumference;\n    },\n    strokeDashoffset() {\n      // This needs to be the percentage of the circumference that we won't show as it will hide that portion of the path\n      return this.circumference * (1 - this.percentage);\n    },\n    primaryStrokeColorId() {\n      return `primary-${ id }`;\n    },\n    secondaryStrokeColorId() {\n      return `secondary-${ id }`;\n    },\n    parsePercentage() {\n      return parseInt(this.percentage * 100) || 0;\n    },\n  }\n};\n\n</script>\n\n<template>\n  <svg\n    class=\"circle\"\n    width=\"100%\"\n    height=\"100%\"\n    :viewBox=\"viewBox\"\n  >\n    <g :transform=\"transform\">\n      <defs>\n        <linearGradient\n          :id=\"primaryStrokeColorId\"\n          x1=\"0%\"\n          y1=\"0%\"\n          x2=\"100%\"\n          y2=\"0%\"\n        >\n          <stop\n            offset=\"50%\"\n            :stop-color=\"primaryStrokeGradientColor || primaryStrokeColor\"\n          />\n          <stop\n            offset=\"100%\"\n            :stop-color=\"primaryStrokeColor\"\n          />\n        </linearGradient>\n        <linearGradient\n          :id=\"secondaryStrokeColorId\"\n          x1=\"0%\"\n          y1=\"0%\"\n          x2=\"100%\"\n          y2=\"0%\"\n        >\n          <stop\n            offset=\"50%\"\n            :stop-color=\"secondaryStrokeGradientColor || secondaryStrokeColor\"\n          />\n          <stop\n            offset=\"100%\"\n            :stop-color=\"secondaryStrokeColor\"\n          />\n        </linearGradient>\n      </defs>\n      <circle\n        :r=\"radius\"\n        :cy=\"center\"\n        :cx=\"center\"\n        :stroke-width=\"strokeWidth\"\n        :stroke=\"`url(#${secondaryStrokeColorId})`\"\n        fill=\"none\"\n      />\n      <circle\n        :r=\"radius\"\n        :cy=\"center\"\n        :cx=\"center\"\n        :stroke-width=\"strokeWidth\"\n        :stroke=\"`url(#${primaryStrokeColorId})`\"\n        :stroke-dasharray=\"circumference\"\n        :stroke-dashoffset=\"circumference * (1 - percentage)\"\n        fill=\"none\"\n      />\n    </g>\n\n    <text\n      v-if=\"showText\"\n      :x=\"center\"\n      :y=\"center\"\n      style=\"font-size: 25; dominant-baseline:  middle; text-anchor:middle;\"\n      :fill=\"`url(#${primaryStrokeColorId})`\"\n    >\n      {{ parsePercentage }}%\n    </text>\n  </svg>\n</template>\n\n<style lang=\"scss\" scoped>\nsvg.text {\n  fill: red\n}\n</style>\n"]}]}