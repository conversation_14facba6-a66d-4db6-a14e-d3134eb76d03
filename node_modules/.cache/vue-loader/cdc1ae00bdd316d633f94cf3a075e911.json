{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Card/Card.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Card/Card.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Card/Card.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAErE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE7B,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;KAClB,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;IACD,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KACtC,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;IACD,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAClE,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;IACzB,CAAC;IACD,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACxC,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;IACD,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;KAC3C,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;IACD,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACpC,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9C,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC9D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC;IACJ;EACF;AACF,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Card/Card.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent, PropType } from 'vue';\nimport { useBasicSetupFocusTrap } from '@shell/composables/focusTrap';\n\nexport default defineComponent({\n\n  name:  'Card',\n  props: {\n    /**\n     * The card's title.\n     */\n    title: {\n      type:    String,\n      default: ''\n    },\n    /**\n     * The text content for the card's body.\n     */\n    content: {\n      type:    String,\n      default: ''\n    },\n    /**\n     * The function to invoke when the default action button is clicked.\n     */\n    buttonAction: {\n      type:    Function as PropType<(event: MouseEvent) => void>,\n      default: (): void => { }\n    },\n    /**\n     * The text for the default action button.\n     */\n    buttonText: {\n      type:    String,\n      default: 'go'\n    },\n    /**\n     * Toggles the card's highlight-border class.\n     */\n    showHighlightBorder: {\n      type:    Boolean,\n      default: true\n    },\n    /**\n     * Toggles the card's Actions section.\n     */\n    showActions: {\n      type:    Boolean,\n      default: true\n    },\n    sticky: {\n      type:    Boolean,\n      default: false,\n    },\n    triggerFocusTrap: {\n      type:    Boolean,\n      default: false,\n    },\n  },\n  setup(props) {\n    if (props.triggerFocusTrap) {\n      useBasicSetupFocusTrap('#focus-trap-card-container-element', {\n        // needs to be false because of import YAML modal from header\n        // where the YAML editor itself is a focus trap\n        // and we can't have it superseed the \"escape key\" to blur that UI element\n        // In this case the focus trap moves the focus out of the modal\n        // correctly once it closes because of the \"onBeforeUnmount\" trigger\n        escapeDeactivates: false,\n        allowOutsideClick: true,\n      });\n    }\n  }\n});\n</script>\n\n<template>\n  <div\n    id=\"focus-trap-card-container-element\"\n    class=\"card-container\"\n    :class=\"{'highlight-border': showHighlightBorder, 'card-sticky': sticky}\"\n    data-testid=\"card\"\n  >\n    <div class=\"card-wrap\">\n      <div\n        class=\"card-title\"\n        data-testid=\"card-title-slot\"\n      >\n        <slot name=\"title\">\n          {{ title }}\n        </slot>\n      </div>\n      <hr>\n      <div\n        class=\"card-body\"\n        data-testid=\"card-body-slot\"\n      >\n        <slot name=\"body\">\n          {{ content }}\n        </slot>\n      </div>\n      <div\n        v-if=\"showActions\"\n        class=\"card-actions\"\n        data-testid=\"card-actions-slot\"\n      >\n        <slot name=\"actions\">\n          <button\n            class=\"btn role-primary\"\n            @click=\"buttonAction\"\n          >\n            {{ buttonText }}\n          </button>\n        </slot>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang='scss'>\n .card-container {\n  &.highlight-border {\n    border-left: 5px solid var(--primary);\n  }\n  border-radius: var(--border-radius);\n  display: flex;\n  flex-basis: 40%;\n  margin: 10px;\n  min-height: 100px;\n  padding: 10px;\n  box-shadow: 0 0 20px var(--shadow);\n  &:not(.top) {\n    align-items: top;\n    flex-direction: row;\n    justify-content: start;\n  }\n  .card-wrap {\n    width: 100%;\n  }\n   & .card-body {\n    color: var(--input-label);\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n   }\n   & .card-actions {\n     align-self: end;\n     display: flex;\n     padding-top: 20px;\n   }\n   & .card-title {\n    align-items: center;\n    display: flex;\n    width: 100%;\n     h5 {\n       margin: 0;\n     }\n    .flex-right {\n      margin-left: auto;\n    }\n   }\n\n  // Sticky mode will stick header and footer to top and bottom with content in the middle scrolling\n   &.card-sticky {\n      // display: flex;\n      // flex-direction: column;\n      overflow: hidden;\n\n    .card-wrap {\n      display: flex;\n      flex-direction: column;\n\n      .card-body {\n        justify-content: flex-start;\n        overflow: auto;\n      }\n\n      > * {\n        flex: 0;\n      }\n\n      .card-body {\n        flex: 1;\n      }\n    }\n   }\n }\n</style>\n"]}]}