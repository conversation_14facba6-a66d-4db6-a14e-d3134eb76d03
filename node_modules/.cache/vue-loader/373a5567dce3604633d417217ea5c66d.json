{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetStatus.vue?vue&type=style&index=0&id=0abf3d2c&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetStatus.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetStatus.vue"], "names": [], "mappings": ";EA+ME,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;;EAE7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACpB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEf;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpD;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACjB;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACd;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB;;IAEA,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,EAAE;QACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACpB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrB;CACH", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetStatus.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { sortBy } from '@shell/utils/sort';\nimport { get } from '@shell/utils/object';\nimport { stateSort, STATES_ENUM } from '@shell/plugins/dashboard-store/resource-class';\n\nexport default {\n\n  name: 'FleetStatus',\n\n  props: {\n    values: {\n      type:     Array,\n      required: true,\n    },\n    colorKey: {\n      type:    String,\n      default: 'color',\n    },\n    labelKey: {\n      type:    String,\n      default: 'label',\n    },\n    valueKey: {\n      type:    String,\n      default: 'value',\n    },\n    min: {\n      type:    Number,\n      default: 0\n    },\n    max: {\n      type:    Number,\n      default: null,\n    },\n    minPercent: {\n      type:    Number,\n      default: 5,\n    },\n    showZeros: {\n      type:    Boolean,\n      default: false,\n    },\n\n    title: {\n      type:    String,\n      default: 'Resources'\n    }\n  },\n\n  computed: {\n    meta() {\n      return {\n        total:      this.values.map((x) => x.value).reduce((a, b) => a + b, 0),\n        readyCount: this.values.filter((x) => x.status === STATES_ENUM.SUCCESS || x.status === STATES_ENUM.READY).map((x) => x.value).reduce((a, b) => a + b, 0)\n      };\n    },\n\n    pieces() {\n      let out = [...this.values].reduce((prev, obj) => {\n        const color = get(obj, this.colorKey);\n        const label = get(obj, this.labelKey);\n        const value = get(obj, this.valueKey);\n\n        if ( obj[this.valueKey] === 0 && !this.showZeros) {\n          return prev;\n        }\n\n        prev.push({\n          color,\n          label,\n          value,\n          sort: stateSort(color),\n        });\n\n        return prev;\n      }, []);\n\n      const minPercent = this.minPercent || 0;\n      const min = this.min || 0;\n      let max = this.max;\n      let sum = 0;\n\n      if ( !this.max ) {\n        max = 100;\n        if ( out.length ) {\n          max = out.map((x) => x.value).reduce((a, b) => a + b);\n        }\n      }\n\n      out = this.values.map((obj) => {\n        if (obj.value === 0 ) {\n          obj.percent = 0;\n\n          return obj;\n        }\n        const percent = Math.max(minPercent, toPercent(obj.value, min, max));\n\n        obj.percent = percent;\n        sum += percent;\n\n        return obj;\n      });\n\n      // If the sum is bigger than 100%, take it out of the biggest piece\n      if ( sum > 100 ) {\n        sortBy(out, 'percent', true)[0].percent -= sum - 100;\n      }\n\n      out = this.values.map((obj) => {\n        obj.style = `width: ${ obj.percent }%; background: var(--${ obj.color })`;\n\n        return obj;\n      });\n\n      return [...out].filter((obj) => obj.percent);\n    },\n  },\n\n  methods: {\n    showMenu(show) {\n      if (this.$refs.popover) {\n        if (show) {\n          this.$refs.popover.show();\n        } else {\n          this.$refs.popover.hide();\n        }\n      }\n    },\n  }\n};\n\nfunction toPercent(value, min, max) {\n  value = Math.max(min, Math.min(max, value));\n  let per = value / (max - min) * 100; // Percent 0-100\n\n  per = Math.floor(per * 100) / 100; // Round to 2 decimal places\n\n  return per;\n}\n\n</script>\n<template>\n  <div class=\"fleet-status\">\n    <div class=\"count\">\n      {{ meta.total }}\n    </div>\n    <div class=\"progress-container\">\n      <div class=\"header\">\n        <div class=\"title\">\n          {{ title }}\n        </div>\n        <div\n          class=\"resources-dropdown\"\n          tabindex=\"0\"\n          @blur=\"showMenu(false)\"\n          @click=\"showMenu(true)\"\n          @focus.capture=\"showMenu(true)\"\n        >\n          <v-dropdown\n            ref=\"popover\"\n            placement=\"bottom\"\n            offset=\"-10\"\n            :triggers=\"[]\"\n            :delay=\"{show: 0, hide: 0}\"\n            :flip=\"false\"\n            :container=\"false\"\n            popper-class=\"fleet-summary-tooltip\"\n          >\n            <div class=\"meta-title\">\n              {{ meta.readyCount }} / {{ meta.total }} {{ title }} ready <i class=\"icon toggle icon-chevron-down\" />\n            </div>\n            <template #popper>\n              <div class=\"resources-status-list\">\n                <ul\n                  class=\"list-unstyled dropdown\"\n                  @click.stop=\"showMenu(false)\"\n                >\n                  <li\n                    v-for=\"(val, idx) in values\"\n                    :key=\"idx\"\n                  >\n                    <span>{{ val.label }}</span><span class=\"list-count\">{{ val.count }}</span>\n                  </li>\n                </ul>\n              </div>\n            </template>\n          </v-dropdown>\n        </div>\n      </div>\n      <div\n        v-trim-whitespace\n        :class=\"{progress: true, multi: pieces.length > 1}\"\n      >\n        <div\n          v-for=\"(piece, idx) of pieces\"\n          :key=\"idx\"\n          v-trim-whitespace\n          :primary-color-var=\"piece.color\"\n          :class=\"{'piece': true, [piece.color]: true}\"\n          :style=\"piece.style\"\n        />\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  $progress-divider-width: 1px;\n  $progress-border-radius: 90px;\n  $progress-height:        10px;\n  $progress-width:         100%;\n\n  .fleet-status {\n    display: flex;\n    width: 100%;\n    border: 1px solid var(--border);\n    border-radius: 10px\n  }\n\n  .header {\n    display: flex;\n    margin-bottom: 15px;\n    justify-content: space-between;\n  }\n\n  .progress-container {\n    width: 100%;\n    padding: 15px;\n\n  }\n\n  .count {\n    padding: 15px;\n    background-color: var(--tabbed-container-bg);\n    border-radius: 10px 0 0 10px;\n    display: flex;\n    align-items: center;\n    min-width: 60px;\n    justify-content: center;\n    font-size: $font-size-h2\n  }\n\n  .progress {\n    display: block;\n    border-radius: $progress-border-radius;\n    background-color: var(--progress-bg);\n    height: $progress-height;\n    width: $progress-width;\n\n    .piece {\n      display: inline-block;\n      vertical-align: top;\n      height: $progress-height;\n      border-radius: 0;\n      border-right: $progress-divider-width solid var(--progress-divider);\n      vertical-align: top;\n\n      &:first-child {\n        border-top-left-radius: $progress-border-radius;\n        border-bottom-left-radius: $progress-border-radius;\n      }\n\n      &:last-child {\n        border-top-right-radius: $progress-border-radius;\n        border-bottom-right-radius: $progress-border-radius;\n        border-right: 0;\n      }\n    }\n  }\n\n  .piece.bg-success:only-child {\n    opacity: 0.5;\n  }\n\n  .meta-title {\n    display: flex;\n    align-items: center;\n\n    &:hover {\n      cursor: pointer;\n      color: var(--link);\n    }\n\n    .icon {\n      margin: 4px 0 0 5px;\n      opacity: 0.3;\n    }\n  }\n\n  .resources-dropdown {\n    li {\n        display: flex;\n        justify-content: space-between;\n        margin: 10px 5px;\n    }\n\n    .list-count {\n        margin-left: 30px;\n    }\n }\n</style>\n"]}]}