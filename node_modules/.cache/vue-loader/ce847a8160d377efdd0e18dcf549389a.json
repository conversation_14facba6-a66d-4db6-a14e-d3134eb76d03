{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/RoleDetailEdit.vue?vue&type=template&id=1a42da65&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/RoleDetailEdit.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/RoleDetailEdit.vue"], "names": [], "mappings": ";EA0hBE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB;IACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC;MACD,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb;QACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACrB,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;UACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC,CAAC;MACD,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ;QACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ;UACE,CAAC,CAAC,CAAC,CAAC,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACzF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACR,CAAC,CAAC,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACtE,CAAC;sBACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC9E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACvB,CAAC;oBACD,CAAC,CAAC,CAAC,CAAC;sBACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACR,CAAC,CAAC,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxG,CAAC,CAAC,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;kBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnB;kBACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9G,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;kBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACpE,CAAC;gBACH,CAAC,CAAC,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACjE,CAAC;gBACH,CAAC,CAAC,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACpE,CAAC;gBACH,CAAC,CAAC,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;kBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnB;kBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC1E,CAAC;gBACH,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC/F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;kBAChC,CAAC;gBACH,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/RoleDetailEdit.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { MANAGEMENT, RBAC } from '@shell/config/types';\nimport CruResource from '@shell/components/CruResource';\nimport CreateEditView from '@shell/mixins/create-edit-view';\nimport FormValidation from '@shell/mixins/form-validation';\nimport Error from '@shell/components/form/Error';\nimport { RadioGroup } from '@components/Form/Radio';\nimport Select from '@shell/components/form/Select';\nimport ArrayList from '@shell/components/form/ArrayList';\nimport NameNsDescription from '@shell/components/form/NameNsDescription';\nimport Tab from '@shell/components/Tabbed/Tab';\nimport Tabbed from '@shell/components/Tabbed';\nimport { ucFirst } from '@shell/utils/string';\nimport SortableTable from '@shell/components/SortableTable';\nimport { _CLONE, _DETAIL } from '@shell/config/query-params';\nimport { SCOPED_RESOURCES, SCOPED_RESOURCE_GROUPS } from '@shell/config/roles';\nimport LabeledInput from '@components/Form/LabeledInput/LabeledInput.vue';\n\nimport { SUBTYPE_MAPPING, VERBS } from '@shell/models/management.cattle.io.roletemplate';\nimport Loading from '@shell/components/Loading';\n\nconst GLOBAL = SUBTYPE_MAPPING.GLOBAL.key;\nconst CLUSTER = SUBTYPE_MAPPING.CLUSTER.key;\nconst NAMESPACE = SUBTYPE_MAPPING.NAMESPACE.key;\nconst RBAC_ROLE = SUBTYPE_MAPPING.RBAC_ROLE.key;\n\n/**\n * Handles the View, Create and Edit of\n * - management.cattle.io.globalrole\n * - management.cattle.io.roletemplate\n * - rbac.authorization.k8s.io.role\n * - rbac.authorization.k8s.io.clusterrole\n *\n * management.cattle.io.roletemplate is further split into two types\n * - Cluster\n * - Project/Namespace\n *\n * The above means there are 4 types ==> 5 subtypes handled by this component\n *\n * This component is used in these five forms:\n *\n * 1. Cluster Explorer > More Resources > RBAC > ClusterRoles\n *   - Should show list of cluster scoped resources and namespaced resources\n * 2. Cluster Explorer > More Resources > RBAC > Roles\n *   - Should show list of namespaced resources\n * 3. Users & Authentication > Roles > Global\n *   - Should show global, cluster and namespace scoped resources\n * 4. Users & Authentication > Roles > Cluster\n *   - Should show cluster and namespace scoped resources\n * 5. Users & Authentication > Roles > Projects & Namespaces\n *   - Should show only namespace scoped resources\n */\nexport default {\n  emits: ['set-subtype', 'input'],\n\n  components: {\n    ArrayList,\n    CruResource,\n    RadioGroup,\n    Select,\n    NameNsDescription,\n    Tab,\n    Tabbed,\n    SortableTable,\n    Loading,\n    Error,\n    LabeledInput\n  },\n\n  mixins: [CreateEditView, FormValidation],\n\n  async fetch() {\n    // We don't want to get all schemas from the cluster because there are\n    // two problems with that:\n    // - In the local cluster, that yields over 500-1,000 schemas, most of which aren't meant to\n    //   be edited by humans.\n    // - Populating the list directly from the schemas wouldn't include resources that may\n    //   be in downstream clusters but not the local cluster. For example, if the logging\n    //   application isn't installed in the local cluster, you wouldn't see logging resources\n    //   such as Flows in the resource list, which you might want in order to\n    //   create a role that is intended to be used by someone with access to a cluster where\n    //   logging is installed.\n    // Therefore we use a hardcoded list that is essentially intended\n    // to be in-app documentation for convenience only, while allowing\n    // users to freely type in resources that are not shown in the list.\n\n    if (this.value.subtype === CLUSTER || this.value.subtype === NAMESPACE) {\n      (await this.$store.dispatch(`management/findAll`, { type: MANAGEMENT.ROLE_TEMPLATE })).forEach((template) => {\n        // Ensure we have quick access to a specific template. This allows unselected drop downs to show the correct value\n        this.keyedTemplateOptions[template.id] = {\n          label: template.nameDisplay,\n          value: template.id\n        };\n      });\n      this.templateOptions = Object.values(this.keyedTemplateOptions);\n    }\n    if (this.realMode === _CLONE) {\n      this.value.displayName = '';\n      this.value.builtin = false;\n    }\n  },\n\n  data() {\n    return {\n      defaultRule: {\n        apiGroups:       [''],\n        nonResourceURLs: [],\n        resourceNames:   [],\n        resources:       [],\n        verbs:           []\n      },\n      verbOptions:          VERBS,\n      templateOptions:      [],\n      keyedTemplateOptions: {},\n      resources:            this.value.resources,\n      scopedResources:      SCOPED_RESOURCES,\n      defaultValue:         false,\n      selectFocused:        null,\n      fvFormRuleSets:       [\n        { path: 'displayName', rules: ['required'] }\n      ],\n    };\n  },\n\n  created() {\n    this.value['rules'] = this.value.rules || [];\n    const query = { ...this.$route.query };\n    const { roleContext } = query;\n\n    if (roleContext && this.value.updateSubtype) {\n      this.value.updateSubtype(roleContext);\n    }\n\n    // Set the default value for the mapped subtype\n    this.defaultValue = !!this.value[SUBTYPE_MAPPING[this.value.subtype].defaultKey];\n\n    switch (this.value.subtype) {\n    case CLUSTER:\n    case NAMESPACE:\n      this.value['roleTemplateNames'] = this.value.roleTemplateNames || [];\n      this.value['locked'] = !!this.value.locked;\n      break;\n    }\n\n    // On save hook request\n    if (this.registerBeforeHook) {\n      this.registerBeforeHook(() => {\n        // Map default value back to its own key for given subtype\n        this.value[SUBTYPE_MAPPING[this.value.subtype].defaultKey] = !!this.defaultValue;\n      });\n    }\n\n    if (this.value?.metadata?.name && !this.value.displayName) {\n      this.value['displayName'] = this.value.metadata.name;\n    }\n\n    this.$nextTick(() => {\n      this.$emit('set-subtype', this.label);\n    });\n  },\n\n  computed: {\n    label() {\n      return this.t(`rbac.roletemplate.subtypes.${ this.value.subtype }.label`);\n    },\n    defaultLabel() {\n      return this.t(`rbac.roletemplate.subtypes.${ this.value.subtype }.defaultLabel`);\n    },\n    lockedOptions() {\n      return [\n        {\n          value: true,\n          label: this.t('rbac.roletemplate.locked.yes')\n        },\n        {\n          value: false,\n          label: this.t('rbac.roletemplate.locked.no')\n        }\n      ];\n    },\n    resourceOptions() {\n      const options = [];\n\n      const scopes = Object.keys(this.scopedResources);\n\n      scopes.forEach((scope) => {\n        if (scope === SCOPED_RESOURCE_GROUPS.GLOBAL && this.value.type !== MANAGEMENT.GLOBAL_ROLE) {\n          // If we are not in the global role creation form,\n          // skip adding the global-scoped resources.\n          return;\n        }\n        if (scope === SCOPED_RESOURCE_GROUPS.CLUSTER && (this.value.type === RBAC.ROLE || this.value.subtype === NAMESPACE)) {\n          // If we are in a project/namespace role creation form,\n          // additionally skip adding the cluster-scoped resources.\n          return;\n        }\n\n        const apiGroupsInScope = this.scopedResources[scope];\n\n        const apiGroupNames = Object.keys(apiGroupsInScope);\n\n        // Put each API group as a header and put its resources under it.\n        apiGroupNames.forEach((apiGroup) => {\n          // Add API group as the header for a group of related resources.\n          let apiGroupLabel = apiGroup;\n          let apiGroupValue = apiGroup;\n\n          if (apiGroup === 'coreKubernetesApi') {\n            // If a resource belongs to the core Kubernetes API,\n            // the API group is technically an empty string but\n            // we will label it \"Core K8s API, Cluster Scoped.\"\n\n            // Some core Kubernetes resources are namespaced,\n            // in which case they go under a different heading\n            // \"Core K8s API, Namespaced.\" This lets us\n            // separate them by scope.\n            const labelForNoApiGroup = this.t('rbac.roletemplate.tabs.grantResources.noApiGroupClusterScope');\n            const labelForNamespacedResourcesWithNoApiGroup = this.t('rbac.roletemplate.tabs.grantResources.noApiGroupNamespaceScope');\n\n            apiGroupLabel = scope.includes('cluster') ? labelForNoApiGroup : labelForNamespacedResourcesWithNoApiGroup;\n            apiGroupValue = '';\n          }\n\n          if (apiGroup === 'neuvectorApi') {\n            // Some NeuVector resources are namespaced, in which case they go under a different heading\n            const labelForClusterScoped = this.t('rbac.roletemplate.tabs.grantResources.neuvector.labelClusterScoped');\n            const labelForNamespaceScoped = this.t('rbac.roletemplate.tabs.grantResources.neuvector.labelNamespaceScoped');\n\n            apiGroupLabel = scope.includes('cluster') ? labelForClusterScoped : labelForNamespaceScoped;\n            apiGroupValue = 'permission.neuvector.com';\n          }\n\n          options.push({\n            kind:      'group',\n            optionKey: apiGroupLabel,\n            label:     apiGroupLabel,\n            value:     apiGroupValue,\n            disabled:  true,\n          });\n\n          const resourcesInApiGroup = this.scopedResources[scope][apiGroup].resources;\n\n          // Add non-deprecated resources to the resource options list\n          resourcesInApiGroup.forEach((resourceName) => {\n            options.push({\n              label:     resourceName,\n              // Use unique key for resource list in the Select dropdown\n              optionKey: apiGroupValue.concat(resourceName),\n              value:     {\n                resourceName: resourceName.toLowerCase(),\n                apiGroupValue\n              }\n            });\n          });\n\n          // If the API group has any deprecated options,\n          // list them as \"Resource Name (deprecated)\"\n          if (this.scopedResources[scope][apiGroup].deprecatedResources) {\n            const deprecatedResourcesInApiGroup = this.scopedResources[scope][apiGroup].deprecatedResources;\n            const deprecatedLabel = this.t('rbac.roletemplate.tabs.grantResources.deprecatedLabel');\n\n            deprecatedResourcesInApiGroup.forEach((resourceName) => {\n              options.push({\n                label:     `${ resourceName } ${ deprecatedLabel }`,\n                optionKey: apiGroupValue.concat(resourceName),\n                value:     {\n                  resourceName: resourceName.toLowerCase(),\n                  apiGroupValue\n                }\n              });\n            });\n          }\n        });\n      });\n\n      options.push({\n        // This hidden option is to work around a bug in the Select\n        // component where an option marked as disabled\n        // is still selected by default if is value is an empty string.\n\n        // In the Role or Project Role form, an API group will be the\n        // default choice for the namespace value because there's only\n        // one option with the value as an empty string and that's the default\n        // value for the namespace. It's not good to have an API\n        // group as the default value for a resource.\n        // Adding this option means there are least two values with an\n        // empty string in the options, preventing the Select from\n        // selecting a disabled header group as the resource by default.\n\n        // This bug is such an edge case that I can't imagine anyone\n        // else hitting it, so I figured the workaround is better\n        // than fixing the select.\n        kind:      'group',\n        optionKey: 'hiddenOption',\n        label:     '_',\n        value:     '',\n        disabled:  true,\n      });\n\n      return options;\n    },\n\n    newUserDefaultOptions() {\n      return [\n        {\n          value: true,\n          label: this.t(`rbac.roletemplate.subtypes.${ this.value.subtype }.yes`)\n        },\n        {\n          value: false,\n          label: this.t('rbac.roletemplate.newUserDefault.no')\n        }\n      ];\n    },\n    isRancherRoleTemplate() {\n      return this.value.subtype === CLUSTER || this.value.subtype === NAMESPACE;\n    },\n    isNamespaced() {\n      return this.value.subtype === RBAC_ROLE;\n    },\n    isRancherType() {\n      return this.value.subtype === GLOBAL || this.value.subtype === CLUSTER || this.value.subtype === NAMESPACE;\n    },\n    isDetail() {\n      return this.as === _DETAIL;\n    },\n    isBuiltin() {\n      return this.value.builtin;\n    },\n    doneLocationOverride() {\n      return this.value.listLocation;\n    },\n    ruleClass() {\n      return `col ${ this.isNamespaced ? 'span-4' : 'span-3' }`;\n    },\n    // Detail View\n    rules() {\n      return this.createRules(this.value);\n    },\n    ruleHeaders() {\n      const verbHeaders = VERBS.map((verb) => ({\n        name:      verb,\n        key:       ucFirst(verb),\n        value:     this.verbKey(verb),\n        formatter: 'Checked',\n        align:     'center'\n      }));\n\n      return [\n        ...verbHeaders,\n        {\n          name:      'custom',\n          labelKey:  'tableHeaders.customVerbs',\n          key:       ucFirst('custom'),\n          value:     'hasCustomVerbs',\n          formatter: 'Checked',\n          align:     'center'\n        },\n        {\n          name:      'resources',\n          labelKey:  'tableHeaders.resources',\n          value:     'resources',\n          formatter: 'list',\n        },\n        {\n          name:      'url',\n          labelKey:  'tableHeaders.url',\n          value:     'nonResourceURLs',\n          formatter: 'list',\n        },\n        {\n          name:      'apiGroups',\n          labelKey:  'tableHeaders.apiGroup',\n          value:     'apiGroups',\n          formatter: 'list',\n        }\n      ];\n    },\n    inheritedRules() {\n      return this.createInheritedRules(this.value, [], false);\n    }\n  },\n\n  methods: {\n\n    setRule(key, rule, event) {\n      // The key is the aspect of a permissions rule\n      // that is being set, for example, \"verbs,\" \"resources\",\n      // \"apiGroups\" or \"nonResourceUrls.\"\n\n      // The event/value contains name of a resource,\n      // for example, \"Apps.\"\n\n      // The 'rule' contains the the contents of each row of the\n      // role creation form under Grant Resources. Each\n      // rule contains these fields:\n      // - apiGroups\n      // - nonResourceURLs\n      // - resourceNames\n      // - resources\n      // - verbs\n\n      switch (key) {\n      case 'apiGroups':\n\n        if (event || (event === '')) {\n          rule['apiGroups'] = [event];\n        }\n\n        break;\n\n      case 'verbs':\n\n        if (event) {\n          rule['verbs'] = [event];\n        } else {\n          rule['verbs'] = [];\n        }\n        break;\n\n      case 'resources':\n        if (event?.resourceName) {\n          // If we are updating the resources defined in a rule,\n          // the event will be an object with the\n          // properties apiGroupValue and resourceName.\n          rule['resources'] = [event.resourceName];\n          // Automatically fill in the API group of the\n          // selected resource.\n          rule['apiGroups'] = [event.apiGroupValue];\n        } else if (event?.label) {\n          // When the user creates a new resource name in the resource\n          // field instead of selecting an existing one,\n          // we have to treat that differently because the incoming event\n          // is shaped like {\"label\":\"something\"} instead of\n          // the same format as the other options:\n          // { resourceName: \"something\", apiGroupValue: \"\" }\n          rule['resources'] = [event.label];\n        } else {\n          rule['resources'] = [];\n          rule['apiGroups'] = [];\n        }\n        break;\n\n      case 'nonResourceURLs':\n        if (event) {\n          rule['nonResourceURLs'] = [event];\n        } else {\n          rule['nonResourceURLs'] = [];\n        }\n        break;\n\n      default:\n        break;\n      }\n    },\n    getRule(key, rule) {\n      return rule[key]?.[0] || null;\n    },\n    updateSelectValue(row, key, event) {\n      const value = event.label ? event.value : event;\n\n      row[key] = value;\n    },\n    cancel() {\n      this.done();\n    },\n    async actuallySave(url) {\n      // Go through all of the grules and replace double quote apiGroups\n      // k8S documentation shows using empty rules as \"\" - we change this to empty string when used\n      this.value.rules?.forEach((rule) => {\n        if (rule.apiGroups) {\n          rule.apiGroups = rule.apiGroups.map((group) => {\n            // If the group is two double quotes (\"\") replace if with empty string\n            if (group.trim() === '\\\"\\\"') {\n              group = '';\n            }\n\n            return group;\n          });\n        }\n      });\n\n      if ( this.isCreate ) {\n        url = url || this.schema.linkFor('collection');\n        await this.value.save({ url, redirectUnauthorized: false });\n      } else {\n        await this.value.save({ redirectUnauthorized: false });\n      }\n    },\n    // Detail View\n    verbKey(verb) {\n      return `has${ ucFirst(verb) }`;\n    },\n    createRules(role) {\n      return (role.rules || []).map((rule, i) => {\n        const tableRule = {\n          index:           i,\n          apiGroups:       rule.apiGroups || [''],\n          resources:       rule.resources || [],\n          nonResourceURLs: rule.nonResourceURLs || []\n        };\n\n        VERBS.forEach((verb) => {\n          const key = this.verbKey(verb);\n\n          tableRule[key] = rule.verbs[0] === '*' || rule.verbs.includes(verb);\n          tableRule.hasCustomVerbs = rule.verbs.some((verb) => !VERBS.includes(verb));\n        });\n\n        return tableRule;\n      });\n    },\n    createInheritedRules(parent, res = [], showParent = true) {\n      if (!parent.roleTemplateNames) {\n        return [];\n      }\n\n      parent.roleTemplateNames\n        .map((rtn) => this.$store.getters[`management/byId`](MANAGEMENT.ROLE_TEMPLATE, rtn))\n        .forEach((rt) => {\n          // Add Self\n          res.push({\n            showParent,\n            parent,\n            template: rt,\n            rules:    this.createRules(rt)\n          });\n          // Add inherited\n          this.createInheritedRules(rt, res);\n        });\n\n      return res;\n    },\n  }\n};\n</script>\n\n<template>\n  <Loading v-if=\"$fetchState.pending\" />\n  <CruResource\n    v-else\n    class=\"receiver\"\n    :can-yaml=\"!isCreate\"\n    :mode=\"mode\"\n    :resource=\"value\"\n    :errors=\"fvUnreportedValidationErrors\"\n    :validation-passed=\"fvFormIsValid\"\n    :cancel-event=\"true\"\n    @error=\"e=>errors = e\"\n    @finish=\"save\"\n    @cancel=\"cancel\"\n  >\n    <template v-if=\"isDetail\">\n      <SortableTable\n        key-field=\"index\"\n        :rows=\"rules\"\n        :headers=\"ruleHeaders\"\n        :table-actions=\"false\"\n        :row-actions=\"false\"\n        :search=\"false\"\n      />\n      <div\n        v-for=\"(inherited, index) of inheritedRules\"\n        :key=\"index\"\n      >\n        <div class=\"spacer\" />\n        <h3>\n          Inherited from {{ inherited.template.nameDisplay }}\n          <template v-if=\"inherited.showParent\">\n            {{ inherited.parent ? '(' + inherited.parent.nameDisplay + ')' : '' }}\n          </template>\n        </h3>\n        <SortableTable\n          key-field=\"index\"\n          :rows=\"inherited.rules\"\n          :headers=\"ruleHeaders\"\n          :table-actions=\"false\"\n          :row-actions=\"false\"\n          :search=\"false\"\n        />\n      </div>\n    </template>\n    <template v-else>\n      <NameNsDescription\n        :value=\"value\"\n        :namespaced=\"isNamespaced\"\n        :mode=\"mode\"\n        name-key=\"displayName\"\n        description-key=\"description\"\n        label=\"Name\"\n        :rules=\"{ name: fvGetAndReportPathRules('displayName') }\"\n        @update:value=\"$emit('input', $event)\"\n      />\n      <div\n        v-if=\"isRancherType\"\n        class=\"row\"\n      >\n        <div class=\"col span-6\">\n          <RadioGroup\n            v-model:value=\"defaultValue\"\n            name=\"storageSource\"\n            :label=\"defaultLabel\"\n            class=\"mb-10\"\n            data-testid=\"roletemplate-creator-default-options\"\n            :options=\"newUserDefaultOptions\"\n            :mode=\"mode\"\n          />\n        </div>\n        <div\n          v-if=\"isRancherRoleTemplate\"\n          class=\"col span-6\"\n        >\n          <RadioGroup\n            v-model:value=\"value.locked\"\n            name=\"storageSource\"\n            :label=\"t('rbac.roletemplate.locked.label')\"\n            class=\"mb-10\"\n            data-testid=\"roletemplate-locked-options\"\n            :options=\"lockedOptions\"\n            :mode=\"mode\"\n          />\n        </div>\n      </div>\n      <div class=\"spacer\" />\n      <Tabbed :side-tabs=\"true\">\n        <Tab\n          name=\"grant-resources\"\n          :label=\"t('rbac.roletemplate.tabs.grantResources.label')\"\n          :weight=\"1\"\n        >\n          <Error\n            :value=\"value.rules\"\n            :rules=\"fvGetAndReportPathRules('rules')\"\n            as-banner\n          />\n          <ArrayList\n            v-model:value=\"value.rules\"\n            label=\"Resources\"\n            :disabled=\"isBuiltin\"\n            :remove-allowed=\"!isBuiltin\"\n            :add-allowed=\"!isBuiltin\"\n            :default-add-value=\"defaultRule\"\n            :initial-empty-row=\"true\"\n            :show-header=\"true\"\n            add-label=\"Add Resource\"\n            :mode=\"mode\"\n          >\n            <template #column-headers>\n              <div class=\"column-headers row\">\n                <div :class=\"ruleClass\">\n                  <span class=\"text-label\">{{ t('rbac.roletemplate.tabs.grantResources.tableHeaders.verbs') }}\n                    <span class=\"required\">*</span>\n                  </span>\n                </div>\n                <div :class=\"ruleClass\">\n                  <span class=\"text-label\">\n                    {{ t('rbac.roletemplate.tabs.grantResources.tableHeaders.resources') }}\n                    <i\n                      v-clean-tooltip=\"t('rbac.roletemplate.tabs.grantResources.resourceOptionInfo')\"\n                      class=\"icon icon-info\"\n                    />\n                    <span\n                      v-if=\"isNamespaced\"\n                      class=\"required\"\n                    >*</span>\n                  </span>\n                </div>\n                <div :class=\"ruleClass\">\n                  <span class=\"text-label\">{{ t('rbac.roletemplate.tabs.grantResources.tableHeaders.apiGroups') }}</span>\n                </div>\n                <div\n                  v-if=\"!isNamespaced\"\n                  :class=\"ruleClass\"\n                >\n                  <span class=\"text-label\">{{ t('rbac.roletemplate.tabs.grantResources.tableHeaders.nonResourceUrls') }}</span>\n                </div>\n              </div>\n            </template>\n            <template #columns=\"props\">\n              <div class=\"columns row mr-20\">\n                <div :class=\"ruleClass\">\n                  <!-- Select verbs -->\n                  <Select\n                    :value=\"props.row.value.verbs\"\n                    class=\"lg\"\n                    :disabled=\"isBuiltin\"\n                    :taggable=\"true\"\n                    :searchable=\"true\"\n                    :options=\"verbOptions\"\n                    :multiple=\"true\"\n                    :mode=\"mode\"\n                    :compact=\"true\"\n                    :data-testid=\"`grant-resources-verbs${props.i}`\"\n                    @update:value=\"updateSelectValue(props.row.value, 'verbs', $event)\"\n                  />\n                </div>\n                <div :class=\"ruleClass\">\n                  <Select\n                    :value=\"getRule('resources', props.row.value)\"\n                    :disabled=\"isBuiltin\"\n                    :options=\"resourceOptions\"\n                    option-key=\"optionKey\"\n                    :searchable=\"true\"\n                    :taggable=\"true\"\n                    :mode=\"mode\"\n                    :compact=\"true\"\n                    :data-testid=\"`grant-resources-resources${props.i}`\"\n                    @update:value=\"setRule('resources', props.row.value, $event)\"\n                    @createdListItem=\"setRule('resources', props.row.value, $event)\"\n                  />\n                </div>\n                <div :class=\"ruleClass\">\n                  <LabeledInput\n                    :value=\"getRule('apiGroups', props.row.value)\"\n                    :disabled=\"isBuiltin\"\n                    :mode=\"mode\"\n                    :data-testid=\"`grant-resources-api-groups${props.i}`\"\n                    @input=\"setRule('apiGroups', props.row.value, $event.target.value)\"\n                  />\n                </div>\n                <div\n                  v-if=\"!isNamespaced\"\n                  :class=\"ruleClass\"\n                >\n                  <LabeledInput\n                    :value=\"getRule('nonResourceURLs', props.row.value)\"\n                    :disabled=\"isBuiltin\"\n                    :mode=\"mode\"\n                    :data-testid=\"`grant-resources-non-resource-urls${props.i}`\"\n                    @input=\"setRule('nonResourceURLs', props.row.value, $event.target.value)\"\n                  />\n                </div>\n              </div>\n            </template>\n          </ArrayList>\n        </Tab>\n        <Tab\n          v-if=\"isRancherRoleTemplate\"\n          name=\"inherit-from\"\n          label=\"Inherit From\"\n          :weight=\"0\"\n        >\n          <ArrayList\n            v-model:value=\"value.roleTemplateNames\"\n            :disabled=\"isBuiltin\"\n            :remove-allowed=\"!isBuiltin\"\n            :add-allowed=\"!isBuiltin\"\n            label=\"Resources\"\n            add-label=\"Add Resource\"\n            :mode=\"mode\"\n          >\n            <template #columns=\"props\">\n              <div class=\"columns row mr-20\">\n                <div class=\"col span-12\">\n                  <Select\n                    v-model:value=\"props.row.value\"\n                    class=\"lg\"\n                    :taggable=\"false\"\n                    :disabled=\"isBuiltin\"\n                    :searchable=\"true\"\n                    :options=\"selectFocused === props.i ? templateOptions : [keyedTemplateOptions[props.row.value]]\"\n                    option-key=\"value\"\n                    option-label=\"label\"\n                    :mode=\"mode\"\n                    :compact=\"true\"\n                    @on-focus=\"selectFocused = props.i\"\n                    @on-blur=\"selectFocused = null\"\n                  />\n                </div>\n              </div>\n            </template>\n          </ArrayList>\n        </Tab>\n      </Tabbed>\n    </template>\n  </CruResource>\n</template>\n\n<style lang=\"scss\" scoped>\n  .required {\n    color: var(--error);\n  }\n\n  :deep() {\n    .column-headers {\n      margin-right: 75px;\n      margin-bottom: 5px;\n    }\n\n    .box {\n      align-items: initial;\n\n      .remove {\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        align-items: flex-end;\n      }\n    }\n\n    .columns {\n      .col > .unlabeled-select:not(.taggable) {\n        // override the odd padding-top from shell/assets/styles/global/_select.scss\n        padding: $unlabaled-select-padding\n      }\n    }\n  }\n</style>\n"]}]}