{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/HardwareResourceGauge.vue?vue&type=style&index=0&id=6a1684e0&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/HardwareResourceGauge.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLmhhcmR3YXJlLXJlc291cmNlLWdhdWdlIHsKICAgICRzcGFjaW5nOiAxMHB4OwogICAgJGxhcmdlLXNwYWNpbmc6ICRzcGFjaW5nICogMS41OwoKICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwoKICAgIC5ody1nYXVnZTpub3QoOmZpcnN0LW9mLXR5cGUpIHsKICAgICAgbWFyZ2luLXRvcDogMjBweDsKICAgIH0KCiAgICAudmFsdWVzIHsKICAgICAgZm9udC1zaXplOiAxMnB4OwogICAgICBwYWRkaW5nLWxlZnQ6IDEwcHg7CiAgICB9CiAgfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/HardwareResourceGauge.vue"], "names": [], "mappings": ";EAwJE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;IAE9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACpB;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/HardwareResourceGauge.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport ConsumptionGauge from '@shell/components/ConsumptionGauge';\nimport SimpleBox from '@shell/components/SimpleBox';\n\nexport default {\n  components: { ConsumptionGauge, SimpleBox },\n  props:      {\n    name: {\n      type:     String,\n      required: true\n    },\n\n    units: {\n      type:    String,\n      default: ''\n    },\n\n    used: {\n      type:    Object,\n      default: null\n    },\n\n    usedTitle: {\n      type:    String,\n      default: null\n    },\n\n    reserved: {\n      type:    Object,\n      default: null\n    },\n\n    reservedTitle: {\n      type:    String,\n      default: null\n    }\n  },\n  computed: {\n    colorStops() {\n      return {\n        0: '--success', 30: '--warning', 70: '--error'\n      };\n    }\n  },\n  methods: {\n    maxDecimalPlaces(n) {\n      return Math.round(n * 100) / 100;\n    },\n\n    strokes(primary, secondary) {\n      return {\n        primaryStrokeColor:           this.rgba(primary, 1),\n        primaryStrokeGradientColor:   this.rgba(secondary, 1),\n        secondaryStrokeColor:         this.rgba(primary, 0.1),\n        secondaryStrokeGradientColor: this.rgba(secondary, 0.1)\n      };\n    },\n\n    rgba(variable, opacity) {\n      return `rgba(var(${ variable }), ${ opacity })`;\n    },\n\n    percentage(resource) {\n      if (resource.total === 0) {\n        return 0;\n      }\n\n      return `${ (resource.useful / resource.total * 100).toFixed(2) }%`;\n    }\n  }\n};\n</script>\n\n<template>\n  <SimpleBox class=\"hardware-resource-gauge\">\n    <div class=\"chart\">\n      <h3>\n        {{ name }}\n      </h3>\n      <div\n        v-if=\"reserved && (reserved.total !== undefined || reserved.useful !== undefined)\"\n        class=\"hw-gauge\"\n      >\n        <ConsumptionGauge\n          :capacity=\"reserved.total\"\n          :used=\"reserved.useful\"\n          :color-stops=\"colorStops\"\n        >\n          <template #title>\n            <span>\n              {{ reservedTitle ?? t('clusterIndexPage.hardwareResourceGauge.reserved') }}\n              <span class=\"values text-muted\">\n                <span v-if=\"reserved.formattedUseful\">\n                  {{ reserved.formattedUseful }}\n                </span>\n                <span v-else>\n                  {{ maxDecimalPlaces(reserved.useful) }}\n                </span>\n                /\n                <span v-if=\"reserved.formattedTotal\">\n                  {{ reserved.formattedTotal }}\n                </span>\n                <span v-else>\n                  {{ maxDecimalPlaces(reserved.total) }} {{ reserved.units }}\n                </span>\n              </span>\n            </span>\n            <span>\n              {{ percentage(reserved) }}\n            </span>\n          </template>\n        </ConsumptionGauge>\n      </div>\n      <div\n        v-if=\"used && used.useful !== undefined\"\n        class=\"hw-gauge\"\n      >\n        <ConsumptionGauge\n          :capacity=\"used.total\"\n          :used=\"used.useful\"\n          :color-stops=\"colorStops\"\n        >\n          <template #title>\n            <span>\n              {{ usedTitle ?? t('clusterIndexPage.hardwareResourceGauge.used') }}\n              <span class=\"values text-muted\">\n                <span v-if=\"used.formattedUseful\">\n                  {{ used.formattedUseful }}\n                </span>\n                <span v-else>\n                  {{ maxDecimalPlaces(used.useful) }}\n                </span>\n                /\n                <span v-if=\"used.formattedTotal\">\n                  {{ used.formattedTotal }}\n                </span>\n                <span v-else>\n                  {{ maxDecimalPlaces(used.total) }} {{ used.units }}\n                </span>\n              </span>\n            </span>\n            <span>\n              {{ percentage(used) }}\n            </span>\n          </template>\n        </ConsumptionGauge>\n      </div>\n    </div>\n  </SimpleBox>\n</template>\n\n<style lang=\"scss\" scoped>\n  .hardware-resource-gauge {\n    $spacing: 10px;\n    $large-spacing: $spacing * 1.5;\n\n    position: relative;\n    display: flex;\n    flex-direction: column;\n\n    .hw-gauge:not(:first-of-type) {\n      margin-top: 20px;\n    }\n\n    .values {\n      font-size: 12px;\n      padding-left: 10px;\n    }\n  }\n</style>\n"]}]}