{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CountBox.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CountBox.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0NvdW50Qm94JywKCiAgcHJvcHM6IHsKICAgIG5hbWU6IHsKICAgICAgdHlwZTogICAgIFN0cmluZywKICAgICAgcmVxdWlyZWQ6IHRydWUKICAgIH0sCiAgICBjb3VudDogewogICAgICB0eXBlOiAgICAgTnVtYmVyLAogICAgICByZXF1aXJlZDogdHJ1ZQogICAgfSwKICAgIHByaW1hcnlDb2xvclZhcjogewogICAgICB0eXBlOiAgICAgU3RyaW5nLAogICAgICByZXF1aXJlZDogdHJ1ZQogICAgfSwKICAgIGNvbXBhY3Q6IHsKICAgICAgdHlwZTogICAgQm9vbGVhbiwKICAgICAgZGVmYXVsdDogZmFsc2UKICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICBzaWRlU3R5bGUoKSB7CiAgICAgIHJldHVybiBgYm9yZGVyLWxlZnQ6IDlweCBzb2xpZCAkeyB0aGlzLmN1c3RvbWl6ZVByaW1hcnlDb2xvck9wYWNpdHkoMSkgfTtgOwogICAgfSwKCiAgICBtYWluU3R5bGUoKSB7CiAgICAgIHJldHVybiBgYm9yZGVyLWNvbG9yOiAkeyB0aGlzLmN1c3RvbWl6ZVByaW1hcnlDb2xvck9wYWNpdHkoMC4yNSkgfTtgOwogICAgfQogIH0sCgogIG1ldGhvZHM6IHsKICAgIGN1c3RvbWl6ZVByaW1hcnlDb2xvck9wYWNpdHkob3BhY2l0eSkgewogICAgICByZXR1cm4gYHJnYmEodmFyKCR7IHRoaXMucHJpbWFyeUNvbG9yVmFyIH0pLCAkeyBvcGFjaXR5IH0pYDsKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CountBox.vue"], "names": [], "mappings": ";;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5E,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtE;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7D;EACF;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CountBox.vue", "sourceRoot": "", "sourcesContent": ["<script>\n\nexport default {\n  name: 'CountB<PERSON>',\n\n  props: {\n    name: {\n      type:     String,\n      required: true\n    },\n    count: {\n      type:     Number,\n      required: true\n    },\n    primaryColorVar: {\n      type:     String,\n      required: true\n    },\n    compact: {\n      type:    Boolean,\n      default: false\n    }\n  },\n  computed: {\n    sideStyle() {\n      return `border-left: 9px solid ${ this.customizePrimaryColorOpacity(1) };`;\n    },\n\n    mainStyle() {\n      return `border-color: ${ this.customizePrimaryColorOpacity(0.25) };`;\n    }\n  },\n\n  methods: {\n    customizePrimaryColorOpacity(opacity) {\n      return `rgba(var(${ this.primaryColorVar }), ${ opacity })`;\n    }\n  }\n};\n</script>\n\n<template>\n  <div\n    class=\"count-container\"\n    :style=\"sideStyle\"\n  >\n    <div\n      class=\"count\"\n      :primary-color-var=\"primaryColorVar\"\n      :style=\"mainStyle\"\n    >\n      <div\n        class=\"data\"\n        :class=\"{ 'compact': compact }\"\n      >\n        <h1>{{ count }}</h1>\n        <label>{{ name }}</label>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n    .count {\n      $padding: 10px;\n\n      padding: $padding;\n      position: relative;\n      display: flex;\n      flex-direction: row;\n      align-items: center;\n      border-width: 2px;\n      border-style: solid;\n      border-left: 0;\n\n      .data {\n        display: flex;\n        flex-direction: column;\n        flex: 1;\n\n        label {\n          opacity: 0.7;\n        }\n\n        &.compact {\n          align-items: center;\n          flex-direction: row;\n\n          h1 {\n            margin-bottom: 0;\n            padding-bottom: 0;\n          }\n\n          label {\n            margin-left: 5px;\n          }\n        }\n      }\n\n      h1 {\n        font-size: 40px;\n        line-height: 36px;\n        padding-bottom: math.div($padding, 2);\n        margin-bottom: 5px;\n      }\n\n      @media only screen and (min-width: map-get($breakpoints, '--viewport-7')) {\n        h1 {\n          font-size: 40px;\n          line-height: 36px;\n        }\n      }\n    }\n</style>\n"]}]}