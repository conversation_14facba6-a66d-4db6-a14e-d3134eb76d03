{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Members/ClusterMembershipEditor.vue?vue&type=template&id=67d14a5f", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Members/ClusterMembershipEditor.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxNZW1iZXJzaGlwRWRpdG9yDQogICAgdi1iaW5kPSIkYXR0cnMiDQogICAgYWRkLW1lbWJlci1kaWFsb2ctbmFtZT0iQWRkQ2x1c3Rlck1lbWJlckRpYWxvZyINCiAgICA6ZGVmYXVsdC1iaW5kaW5nLWhhbmRsZXI9ImRlZmF1bHRCaW5kaW5nSGFuZGxlciINCiAgICA6dHlwZT0iTk9STUFOLkNMVVNURVJfUk9MRV9URU1QTEFURV9CSU5ESU5HIg0KICAgIDptb2RlPSJtb2RlIg0KICAgIHBhcmVudC1rZXk9ImNsdXN0ZXJJZCINCiAgICA6cGFyZW50LWlkPSJwYXJlbnRJZCIKICAvPg0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Members/ClusterMembershipEditor.vue"], "names": [], "mappings": ";EAmDE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Members/ClusterMembershipEditor.vue", "sourceRoot": "", "sourcesContent": ["<script>\r\nimport { NORMAN } from '@shell/config/types';\r\nimport { _CREATE, _VIEW } from '@shell/config/query-params';\r\nimport MembershipEditor, { canViewMembershipEditor } from '@shell/components/form/Members/MembershipEditor';\r\n\r\nexport function canViewClusterMembershipEditor(store) {\r\n  return canViewMembershipEditor(store);\r\n}\r\n\r\nexport default {\r\n  components: { MembershipEditor },\r\n\r\n  props: {\r\n    parentId: {\r\n      type:    String,\r\n      default: null\r\n    },\r\n\r\n    mode: {\r\n      type:     String,\r\n      required: true\r\n    }\r\n  },\r\n\r\n  data() {\r\n    return {\r\n      NORMAN, bindings: [], lastSavedBindings: []\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    isCreate() {\r\n      return this.mode === _CREATE;\r\n    },\r\n\r\n    isView() {\r\n      return this.mode === _VIEW;\r\n    }\r\n  },\r\n  methods: {\r\n    defaultBindingHandler() {\r\n      return this.$store.dispatch(`rancher/create`, {\r\n        type:            NORMAN.CLUSTER_ROLE_TEMPLATE_BINDING,\r\n        roleTemplateId:  'cluster-owner',\r\n        userPrincipalId: this.$store.getters['auth/principalId']\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n<template>\r\n  <MembershipEditor\r\n    v-bind=\"$attrs\"\r\n    add-member-dialog-name=\"AddClusterMemberDialog\"\r\n    :default-binding-handler=\"defaultBindingHandler\"\r\n    :type=\"NORMAN.CLUSTER_ROLE_TEMPLATE_BINDING\"\r\n    :mode=\"mode\"\r\n    parent-key=\"clusterId\"\r\n    :parent-id=\"parentId\"\n  />\r\n</template>\r\n"]}]}