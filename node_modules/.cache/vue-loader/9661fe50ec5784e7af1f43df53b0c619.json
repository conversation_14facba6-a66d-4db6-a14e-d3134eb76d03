{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/AccountAccess.vue?vue&type=template&id=19e09548&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/AccountAccess.vue", "mtime": *************}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": *************}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": *************}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": *************}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": *************}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": *************}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": *************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdgogICAgOmNsYXNzPSJ7J3Nob3dpbmctZm9ybSc6ICFjcmVkZW50aWFsfSIKICAgIGNsYXNzPSJjcmVkZW50aWFsLXJlZ2lvbiIKICA+CiAgICA8ZGl2IGNsYXNzPSJyZWdpb24gbWItMTAiPgogICAgICA8TGFiZWxlZFNlbGVjdAogICAgICAgIDpkaXNhYmxlZD0ibW9kZSE9PSdjcmVhdGUnIgogICAgICAgIDp2YWx1ZT0icmVnaW9uIgogICAgICAgIGRhdGEtdGVzdGlkPSJla3NfcmVnaW9uIgogICAgICAgIGxhYmVsLWtleT0iZWtzLnJlZ2lvbi5sYWJlbCIKICAgICAgICA6b3B0aW9ucz0icmVnaW9uT3B0aW9ucyIKICAgICAgICBAdXBkYXRlOnZhbHVlPSIkZW1pdCgndXBkYXRlLXJlZ2lvbicsICRldmVudCkiCiAgICAgIC8+CiAgICA8L2Rpdj4KICAgIDxkaXYKICAgICAgY2xhc3M9InNlbGVjdC1jcmVkZW50aWFsLWNvbnRhaW5lciIKICAgID4KICAgICAgPFNlbGVjdENyZWRlbnRpYWwKICAgICAgICA6dmFsdWU9ImNyZWRlbnRpYWwiCiAgICAgICAgZGF0YS10ZXN0aWQ9ImNydWVrcy1zZWxlY3QtY3JlZGVudGlhbCIKICAgICAgICA6bW9kZT0ibW9kZSA9PT0gVklFVyA/IFZJRVcgOiBDUkVBVEUiCiAgICAgICAgcHJvdmlkZXI9ImF3cyIKICAgICAgICA6ZGVmYXVsdC1vbi1jYW5jZWw9InRydWUiCiAgICAgICAgOnNob3dpbmctZm9ybT0iIWNyZWRlbnRpYWwiCiAgICAgICAgY2xhc3M9InNlbGVjdC1jcmVkZW50aWFsIgogICAgICAgIDpjYW5jZWw9IigpPT4kZW1pdCgnY2FuY2VsLWNyZWRlbnRpYWwnKSIKICAgICAgICBAdXBkYXRlOnZhbHVlPSIkZW1pdCgndXBkYXRlLWNyZWRlbnRpYWwnLCAkZXZlbnQpIgogICAgICAvPgogICAgPC9kaXY+CiAgPC9kaXY+Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/AccountAccess.vue"], "names": [], "mappings": ";EAoIE,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1B;IACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/C,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC;MACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnD,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/AccountAccess.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent } from 'vue';\nimport { _CREATE, _VIEW } from '@shell/config/query-params';\nimport LabeledSelect from '@shell/components/form/LabeledSelect.vue';\nimport SelectCredential from '@shell/edit/provisioning.cattle.io.cluster/SelectCredential.vue';\nimport { DEFAULT_REGION } from './CruEKS.vue';\nimport { mapGetters } from 'vuex';\nimport { AWS } from '../../types';\nimport { NORMAN } from '@shell/config/types';\n\n\nexport default defineComponent({\n  name: 'EKSAccountAccess',\n\n  emits: ['update-region', 'error', 'cancel-credential', 'update-credential'],\n\n  components: {\n    LabeledSelect,\n    SelectCredential\n  },\n\n  props: {\n    mode: {\n      type:    String,\n      default: _CREATE\n    },\n\n    credential: {\n      type:    String,\n      default: null\n    },\n\n    region: {\n      type:    String,\n      default: ''\n    }\n  },\n\n  async fetch() {\n    if (this.mode !== _VIEW) {\n      this.defaultRegions = await this.$store.dispatch('aws/defaultRegions');\n      if (this.defaultRegions.length && !this.region) {\n        this.$emit('update-region', DEFAULT_REGION);\n      }\n    }\n  },\n\n  data() {\n    return { regions: [] as string[], defaultRegions: [] as string[] };\n  },\n\n  watch: {\n    isAuthenticated: {\n      async handler(neu) {\n        if (neu && this.mode !== _VIEW) {\n          // Auto-default to credential's region only once on initialization if no region is set\n          const credentialDefaultRegion = await this.getCredentialDefaultRegion();\n          if (credentialDefaultRegion) {\n            this.$emit('update-region', credentialDefaultRegion);\n          }\n          await this.fetchRegions();\n        } else {\n          if (this.defaultRegions.length && !this.defaultRegions.includes(this.region)) {\n            if (this.defaultRegions.includes(DEFAULT_REGION)) {\n              this.$emit('update-region', DEFAULT_REGION);\n            } else {\n              this.$emit('update-region', this.defaultRegions[0]);\n            }\n          }\n        }\n      },\n      immediate: true\n    }\n  },\n\n  methods: {\n    async fetchRegions() {\n      const { region, credential }: { region: string, credential: string} = this;\n\n      if (!!region && !!credential) {\n        try {\n          const ec2Client = await this.$store.dispatch('aws/ec2', { region, cloudCredentialId: credential });\n\n          const res: {Regions: AWS.EC2Region[]} = await ec2Client.describeRegions({});\n\n          this.regions = (res?.Regions || []).map((r) => r.RegionName);\n        } catch (err) {\n          this.$emit('error', this.t('eks.errors.fetchingRegions', { err }));\n        }\n      }\n    },\n    \n    async getCredentialDefaultRegion(): Promise<string|null> {\n      if (!this.credential) return null;\n            // Use the correct store and schema type consistent with rest of codebase\n      return this.$store.dispatch('rancher/find', {\n        type: NORMAN.CLOUD_CREDENTIAL,\n        id: this.credential\n      }).then(credentialResource => {\n        return credentialResource?.decodedData?.defaultRegion || null;\n      }).catch(err => {\n        console.warn('Failed to fetch credential default region:', err);\n        return null;\n      });\n    },\n  },\n\n  computed: {\n    ...mapGetters({ t: 'i18n/t' }),\n\n    // once the credential is validated we can fetch a list of available regions\n    isAuthenticated(): boolean {\n      return !!this.credential;\n    },\n\n    regionOptions(): string[] {\n      return this.regions.length ? this.regions : this.defaultRegions;\n    },\n\n    CREATE(): string {\n      return _CREATE;\n    },\n\n    VIEW(): string {\n      return _VIEW;\n    },\n\n  },\n});\n</script>\n\n<template>\n  <div\n    :class=\"{'showing-form': !credential}\"\n    class=\"credential-region\"\n  >\n    <div class=\"region mb-10\">\n      <LabeledSelect\n        :disabled=\"mode!=='create'\"\n        :value=\"region\"\n        data-testid=\"eks_region\"\n        label-key=\"eks.region.label\"\n        :options=\"regionOptions\"\n        @update:value=\"$emit('update-region', $event)\"\n      />\n    </div>\n    <div\n      class=\"select-credential-container\"\n    >\n      <SelectCredential\n        :value=\"credential\"\n        data-testid=\"crueks-select-credential\"\n        :mode=\"mode === VIEW ? VIEW : CREATE\"\n        provider=\"aws\"\n        :default-on-cancel=\"true\"\n        :showing-form=\"!credential\"\n        class=\"select-credential\"\n        :cancel=\"()=>$emit('cancel-credential')\"\n        @update:value=\"$emit('update-credential', $event)\"\n      />\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\">\n  .credential-region {\n    display: flex;\n\n    .region {\n      flex-basis: 50%;\n      flex-grow: 0;\n      margin: 0 1.75% 0 0;\n    }\n\n    &.showing-form {\n      flex-direction: column;\n      flex-grow: 1;\n\n      &>.region {\n        margin: 0;\n      }\n\n      &>.select-credential-container{\n      display:flex;\n      flex-direction: column;\n      flex-grow: 1;\n      }\n    }\n\n    &>.select-credential-container{\n      flex-basis: 50%;\n\n      &>.select-credential{\n        flex-grow: 1;\n      }\n\n    }\n  }\n\n</style>\n"]}]}