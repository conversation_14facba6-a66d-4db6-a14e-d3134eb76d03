{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetStatus.vue?vue&type=template&id=0abf3d2c&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetStatus.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetStatus.vue"], "names": [], "mappings": ";EA8IE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC;YACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACvG,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChC,CAAC,CAAC;kBACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9B;kBACE,CAAC,CAAC;oBACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACX;oBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC5E,CAAC,CAAC,CAAC,CAAC;gBACN,CAAC,CAAC,CAAC,CAAC;cACN,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACpD;QACE,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetStatus.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { sortBy } from '@shell/utils/sort';\nimport { get } from '@shell/utils/object';\nimport { stateSort, STATES_ENUM } from '@shell/plugins/dashboard-store/resource-class';\n\nexport default {\n\n  name: 'FleetStatus',\n\n  props: {\n    values: {\n      type:     Array,\n      required: true,\n    },\n    colorKey: {\n      type:    String,\n      default: 'color',\n    },\n    labelKey: {\n      type:    String,\n      default: 'label',\n    },\n    valueKey: {\n      type:    String,\n      default: 'value',\n    },\n    min: {\n      type:    Number,\n      default: 0\n    },\n    max: {\n      type:    Number,\n      default: null,\n    },\n    minPercent: {\n      type:    Number,\n      default: 5,\n    },\n    showZeros: {\n      type:    Boolean,\n      default: false,\n    },\n\n    title: {\n      type:    String,\n      default: 'Resources'\n    }\n  },\n\n  computed: {\n    meta() {\n      return {\n        total:      this.values.map((x) => x.value).reduce((a, b) => a + b, 0),\n        readyCount: this.values.filter((x) => x.status === STATES_ENUM.SUCCESS || x.status === STATES_ENUM.READY).map((x) => x.value).reduce((a, b) => a + b, 0)\n      };\n    },\n\n    pieces() {\n      let out = [...this.values].reduce((prev, obj) => {\n        const color = get(obj, this.colorKey);\n        const label = get(obj, this.labelKey);\n        const value = get(obj, this.valueKey);\n\n        if ( obj[this.valueKey] === 0 && !this.showZeros) {\n          return prev;\n        }\n\n        prev.push({\n          color,\n          label,\n          value,\n          sort: stateSort(color),\n        });\n\n        return prev;\n      }, []);\n\n      const minPercent = this.minPercent || 0;\n      const min = this.min || 0;\n      let max = this.max;\n      let sum = 0;\n\n      if ( !this.max ) {\n        max = 100;\n        if ( out.length ) {\n          max = out.map((x) => x.value).reduce((a, b) => a + b);\n        }\n      }\n\n      out = this.values.map((obj) => {\n        if (obj.value === 0 ) {\n          obj.percent = 0;\n\n          return obj;\n        }\n        const percent = Math.max(minPercent, toPercent(obj.value, min, max));\n\n        obj.percent = percent;\n        sum += percent;\n\n        return obj;\n      });\n\n      // If the sum is bigger than 100%, take it out of the biggest piece\n      if ( sum > 100 ) {\n        sortBy(out, 'percent', true)[0].percent -= sum - 100;\n      }\n\n      out = this.values.map((obj) => {\n        obj.style = `width: ${ obj.percent }%; background: var(--${ obj.color })`;\n\n        return obj;\n      });\n\n      return [...out].filter((obj) => obj.percent);\n    },\n  },\n\n  methods: {\n    showMenu(show) {\n      if (this.$refs.popover) {\n        if (show) {\n          this.$refs.popover.show();\n        } else {\n          this.$refs.popover.hide();\n        }\n      }\n    },\n  }\n};\n\nfunction toPercent(value, min, max) {\n  value = Math.max(min, Math.min(max, value));\n  let per = value / (max - min) * 100; // Percent 0-100\n\n  per = Math.floor(per * 100) / 100; // Round to 2 decimal places\n\n  return per;\n}\n\n</script>\n<template>\n  <div class=\"fleet-status\">\n    <div class=\"count\">\n      {{ meta.total }}\n    </div>\n    <div class=\"progress-container\">\n      <div class=\"header\">\n        <div class=\"title\">\n          {{ title }}\n        </div>\n        <div\n          class=\"resources-dropdown\"\n          tabindex=\"0\"\n          @blur=\"showMenu(false)\"\n          @click=\"showMenu(true)\"\n          @focus.capture=\"showMenu(true)\"\n        >\n          <v-dropdown\n            ref=\"popover\"\n            placement=\"bottom\"\n            offset=\"-10\"\n            :triggers=\"[]\"\n            :delay=\"{show: 0, hide: 0}\"\n            :flip=\"false\"\n            :container=\"false\"\n            popper-class=\"fleet-summary-tooltip\"\n          >\n            <div class=\"meta-title\">\n              {{ meta.readyCount }} / {{ meta.total }} {{ title }} ready <i class=\"icon toggle icon-chevron-down\" />\n            </div>\n            <template #popper>\n              <div class=\"resources-status-list\">\n                <ul\n                  class=\"list-unstyled dropdown\"\n                  @click.stop=\"showMenu(false)\"\n                >\n                  <li\n                    v-for=\"(val, idx) in values\"\n                    :key=\"idx\"\n                  >\n                    <span>{{ val.label }}</span><span class=\"list-count\">{{ val.count }}</span>\n                  </li>\n                </ul>\n              </div>\n            </template>\n          </v-dropdown>\n        </div>\n      </div>\n      <div\n        v-trim-whitespace\n        :class=\"{progress: true, multi: pieces.length > 1}\"\n      >\n        <div\n          v-for=\"(piece, idx) of pieces\"\n          :key=\"idx\"\n          v-trim-whitespace\n          :primary-color-var=\"piece.color\"\n          :class=\"{'piece': true, [piece.color]: true}\"\n          :style=\"piece.style\"\n        />\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  $progress-divider-width: 1px;\n  $progress-border-radius: 90px;\n  $progress-height:        10px;\n  $progress-width:         100%;\n\n  .fleet-status {\n    display: flex;\n    width: 100%;\n    border: 1px solid var(--border);\n    border-radius: 10px\n  }\n\n  .header {\n    display: flex;\n    margin-bottom: 15px;\n    justify-content: space-between;\n  }\n\n  .progress-container {\n    width: 100%;\n    padding: 15px;\n\n  }\n\n  .count {\n    padding: 15px;\n    background-color: var(--tabbed-container-bg);\n    border-radius: 10px 0 0 10px;\n    display: flex;\n    align-items: center;\n    min-width: 60px;\n    justify-content: center;\n    font-size: $font-size-h2\n  }\n\n  .progress {\n    display: block;\n    border-radius: $progress-border-radius;\n    background-color: var(--progress-bg);\n    height: $progress-height;\n    width: $progress-width;\n\n    .piece {\n      display: inline-block;\n      vertical-align: top;\n      height: $progress-height;\n      border-radius: 0;\n      border-right: $progress-divider-width solid var(--progress-divider);\n      vertical-align: top;\n\n      &:first-child {\n        border-top-left-radius: $progress-border-radius;\n        border-bottom-left-radius: $progress-border-radius;\n      }\n\n      &:last-child {\n        border-top-right-radius: $progress-border-radius;\n        border-bottom-right-radius: $progress-border-radius;\n        border-right: 0;\n      }\n    }\n  }\n\n  .piece.bg-success:only-child {\n    opacity: 0.5;\n  }\n\n  .meta-title {\n    display: flex;\n    align-items: center;\n\n    &:hover {\n      cursor: pointer;\n      color: var(--link);\n    }\n\n    .icon {\n      margin: 4px 0 0 5px;\n      opacity: 0.3;\n    }\n  }\n\n  .resources-dropdown {\n    li {\n        display: flex;\n        justify-content: space-between;\n        margin: 10px 5px;\n    }\n\n    .list-count {\n        margin-left: 30px;\n    }\n }\n</style>\n"]}]}