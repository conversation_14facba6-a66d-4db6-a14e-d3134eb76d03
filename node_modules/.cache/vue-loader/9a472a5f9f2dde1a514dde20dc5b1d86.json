{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/TableDataUserIcon.vue?vue&type=template&id=6ac9c6a9&scoped=true&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/TableDataUserIcon.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdiBjbGFzcz0iaWNvbi1jZW50ZXIiPgogICAgPGkKICAgICAgdi1jbGVhbi10b29sdGlwPSJ1Y0ZpcnN0KHVzZXJTdGF0ZSkiCiAgICAgIDpjbGFzcz0iaWNvbkNsYXNzIgogICAgLz4KICA8L2Rpdj4K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/TableDataUserIcon.vue"], "names": [], "mappings": ";EAwBE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC;MACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;EACH,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/TableDataUserIcon.vue", "sourceRoot": "", "sourcesContent": ["<script setup lang=\"ts\">\nimport { computed } from 'vue';\n\nimport { ucFirst } from '@shell/utils/string';\n\nconst props = defineProps<{\n  userState: string,\n  isActive: boolean\n}>();\n\nconst iconClass = computed(() => {\n  const userIcon = `icon-user-${ props.isActive ? 'check' : 'xmark' }`;\n  const iconColor = `icon-color-${ props.isActive ? 'green' : 'red' }`;\n\n  return {\n    icon:        true,\n    'icon-lg':   true,\n    [userIcon]:  true,\n    [iconColor]: true,\n  };\n});\n</script>\n\n<template>\n  <div class=\"icon-center\">\n    <i\n      v-clean-tooltip=\"ucFirst(userState)\"\n      :class=\"iconClass\"\n    />\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .icon-center {\n    display: flex;\n    flex-direction: row;\n    justify-content: center;\n  }\n\n  .icon-color-green {\n    color: var(--success);\n  }\n\n  .icon-color-red {\n    color: var(--error);\n  }\n</style>\n"]}]}