{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceSummary.vue?vue&type=style&index=0&id=77d9ce05&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceSummary.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLmhhcy1saW5rIHsKICAgIGN1cnNvcjogcG9pbnRlcjsKCiAgICAmOmhvdmVyIHsKICAgICAgYm9yZGVyLWNvbG9yOiB2YXIoLS1saW5rKTsKICAgIH0KICB9CgogIDpkZWVwKCkgLmNvbnRlbnR7CiAgICBkaXNwbGF5OiBmbGV4OwogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICYgSDEsIEgzIHsKICAgICAgICBtYXJnaW46IDA7CiAgICB9CgogICAgJiAuY2hpcHsKICAgICAgYm9yZGVyLXJhZGl1czogMmVtOwogICAgICBjb2xvcjogdmFyKC0tYm9keS1iZyk7CiAgICAgIHBhZGRpbmc6IDBweCAxZW07CgogICAgICAmLndhcm4tY291bnQgewogICAgICAgICAgYmFja2dyb3VuZDogdmFyKC0td2FybmluZykKICAgICAgfQoKICAgICAgJi5lcnJvci1jb3VudCB7CiAgICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1lcnJvcikKICAgICAgfQogICAgfQp9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceSummary.vue"], "names": [], "mappings": ";EA8IE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACb;;IAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B;IACF;AACJ", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceSummary.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport SimpleBox from '@shell/components/SimpleBox';\nimport { COUNT } from '@shell/config/types';\nimport { colorForState } from '@shell/plugins/dashboard-store/resource-class';\n\nexport function colorToCountName(color) {\n  switch (color) {\n  case 'text-success':\n  case 'text-info':\n    return 'useful';\n  case 'text-warning':\n    return 'warningCount';\n  default:\n    return 'errorCount';\n  }\n}\n\nexport function resourceCounts(store, resource) {\n  const inStore = store.getters['currentStore'](COUNT);\n  const clusterCounts = store.getters[`${ inStore }/all`](COUNT)?.[0]?.counts;\n  const summary = clusterCounts?.[resource]?.summary || {};\n\n  const counts = {\n    total:        summary.count || 0,\n    useful:       summary.count || 0,\n    warningCount: 0,\n    errorCount:   0\n  };\n\n  Object.entries(summary.states || {}).forEach((entry) => {\n    const color = colorForState(entry[0]);\n    const count = entry[1];\n    const countName = colorToCountName(color);\n\n    counts['useful'] -= count;\n    counts[countName] += count;\n  });\n\n  return counts;\n}\n\nexport default {\n  components: { SimpleBox },\n\n  props: {\n    resource: {\n      type:    String,\n      default: ''\n    },\n\n    spoofedCounts: {\n      type:    Object,\n      default: null\n    },\n\n    cluster: {\n      type:    String,\n      default: null,\n    },\n  },\n\n  computed: {\n    resourceCounts() {\n      if (this.spoofedCounts) {\n        return this.spoofedCounts;\n      }\n\n      return resourceCounts(this.$store, this.resource);\n    },\n\n    location() {\n      if (this.spoofedCounts) {\n        return this.spoofedCounts.location;\n      }\n\n      const route = {\n        name:   'c-cluster-product-resource',\n        params: {\n          product:  this.$store.getters['currentProduct'].name,\n          resource: this.resource,\n        }\n      };\n\n      if (this.cluster) {\n        route.params.cluster = this.cluster;\n      }\n\n      return route;\n    },\n\n    name() {\n      if (this.spoofedCounts) {\n        return this.spoofedCounts.name;\n      }\n      const inStore = this.$store.getters['currentStore'](this.resource);\n      const schema = this.$store.getters[`${ inStore }/schemaFor`](this.resource);\n\n      return this.$store.getters['type-map/labelFor'](schema, this.resourceCounts.useful);\n    },\n  },\n\n  methods: {\n    goToResource() {\n      if (this.location) {\n        this.$router.push(this.location);\n      }\n    },\n  }\n\n};\n</script>\n\n<template>\n  <div>\n    <SimpleBox\n      class=\"container\"\n      :class=\"{'has-link': !!location}\"\n      @click=\"goToResource\"\n    >\n      <h1>{{ resourceCounts.total }}</h1>\n      <h3>\n        {{ name }}\n      </h3>\n      <div class=\"warnings\">\n        <div\n          v-if=\"resourceCounts.warningCount\"\n          class=\"warn-count mb-10 chip\"\n        >\n          {{ resourceCounts.warningCount }}\n        </div>\n        <div\n          v-if=\"resourceCounts.errorCount\"\n          class=\"error-count chip\"\n        >\n          {{ resourceCounts.errorCount }}\n        </div>\n      </div>\n    </SimpleBox>\n  </div>\n</template>\n\n<style lang='scss' scoped>\n  .has-link {\n    cursor: pointer;\n\n    &:hover {\n      border-color: var(--link);\n    }\n  }\n\n  :deep() .content{\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    & H1, H3 {\n        margin: 0;\n    }\n\n    & .chip{\n      border-radius: 2em;\n      color: var(--body-bg);\n      padding: 0px 1em;\n\n      &.warn-count {\n          background: var(--warning)\n      }\n\n      &.error-count {\n          background: var(--error)\n      }\n    }\n}\n</style>\n"]}]}