{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/WorkspaceSwitcher.vue?vue&type=template&id=656b2637&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/WorkspaceSwitcher.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdgogICAgY2xhc3M9ImZpbHRlciIKICAgIGRhdGEtdGVzdGlkPSJ3b3Jrc3BhY2Utc3dpdGNoZXIiCiAgPgogICAgPFNlbGVjdAogICAgICByZWY9InNlbGVjdCIKICAgICAgdi1tb2RlbDp2YWx1ZT0idmFsdWUiCiAgICAgIGxhYmVsPSJsYWJlbCIKICAgICAgOm9wdGlvbnM9Im9wdGlvbnMiCiAgICAgIDpjbGVhcmFibGU9ImZhbHNlIgogICAgICA6cmVkdWNlPSIob3B0KSA9PiBvcHQudmFsdWUiCiAgICAvPgogICAgPCEtLWJ1dHRvbiB2LXNob3J0a2V5Lm9uY2U9IlsndyddIiBjbGFzcz0iaGlkZSIgQHNob3J0a2V5PSJmb2N1cygpIiAvLS0+CiAgPC9kaXY+Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/WorkspaceSwitcher.vue"], "names": [], "mappings": ";EAsFE,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjC;IACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/WorkspaceSwitcher.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { LAST_NAMESPACE, WORKSPACE } from '@shell/store/prefs';\nimport { mapState } from 'vuex';\nimport Select from '@shell/components/form/Select';\nimport { WORKSPACE_ANNOTATION } from '@shell/config/labels-annotations';\n\nexport default {\n  name:       'WorkspaceSwitcher',\n  components: { Select },\n\n  computed: {\n    ...mapState(['allWorkspaces', 'workspace', 'allNamespaces', 'defaultNamespace', 'getActiveNamespaces']),\n\n    value: {\n      get() {\n        return this.workspace || this.namespace || this.options[0]?.value;\n      },\n\n      set(value) {\n        if (value !== this.value) {\n          this.$store.commit('updateWorkspace', { value, getters: this.$store.getters });\n          this.$store.dispatch('prefs/set', { key: WORKSPACE, value });\n        }\n      },\n    },\n\n    options() {\n      if (this.allWorkspaces.length) {\n        const out = this.allWorkspaces.map((obj) => {\n          return {\n            label: obj.nameDisplay,\n            value: obj.id,\n          };\n        });\n\n        return out;\n      }\n\n      // If doesn't have workspaces (e.g. no permissions)\n      // Then find the workspaces from the annotation.\n      return this.allNamespaces.filter((item) => {\n        return item.metadata.annotations[WORKSPACE_ANNOTATION] === WORKSPACE;\n      }).map((obj) => {\n        return {\n          label: obj.nameDisplay,\n          value: obj.id,\n        };\n      });\n    },\n  },\n\n  watch: {\n    options(curr, prev) {\n      if (curr.length === 0) {\n        this.value = '';\n      }\n\n      const currentExists = curr.find((item) => item.value === this.value);\n\n      if (curr.length && !currentExists) {\n        this.value = curr[0]?.value;\n      }\n    },\n  },\n\n  created() {\n    // in fleet standard user with just the project owner and global git repo permissions\n    // returns 'default'\n    const initValue = !this.workspace ? this.$store.getters['prefs/get'](LAST_NAMESPACE) : '';\n\n    this.value = (initValue === 'default' || initValue === '') && this.options.length ? this.options[0].value : initValue;\n  },\n\n  data() {\n    return { namespace: this.$store.getters['prefs/get'](LAST_NAMESPACE) };\n  },\n\n  methods: {\n    focus() {\n      this.$refs.select.$refs.search.focus();\n    },\n  },\n};\n</script>\n\n<template>\n  <div\n    class=\"filter\"\n    data-testid=\"workspace-switcher\"\n  >\n    <Select\n      ref=\"select\"\n      v-model:value=\"value\"\n      label=\"label\"\n      :options=\"options\"\n      :clearable=\"false\"\n      :reduce=\"(opt) => opt.value\"\n    />\n    <!--button v-shortkey.once=\"['w']\" class=\"hide\" @shortkey=\"focus()\" /-->\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.filter {\n  min-width: 220px;\n  max-width: 100%;\n  display: inline-block;\n}\n\n.filter.show-masked :deep() .unlabeled-select:not(.masked-dropdown) {\n  position: absolute;\n  left: 0;\n  top: 0;\n  height: 0;\n  opacity: 0;\n  visibility: hidden;\n}\n\n.filter:not(.show-masked) :deep() .unlabeled-select.masked-dropdown {\n  position: absolute;\n  left: 0;\n  top: 0;\n  height: 0;\n  opacity: 0;\n  visibility: hidden;\n}\n\n.filter :deep() .unlabeled-select.has-more .v-select:not(.vs--open) .vs__dropdown-toggle {\n  overflow: hidden;\n}\n\n.filter :deep() .unlabeled-select.has-more .v-select.vs--open .vs__dropdown-toggle {\n  height: max-content;\n  background-color: var(--header-bg);\n}\n\n.filter :deep() .unlabeled-select {\n  background-color: transparent;\n  border: 0;\n}\n\n.filter :deep() .unlabeled-select:not(.focused) {\n  min-height: 0;\n}\n\n.filter :deep() .unlabeled-select:not(.view):hover .vs__dropdown-menu {\n  background: var(--dropdown-bg);\n}\n\n.filter :deep() .unlabeled-select .v-select.inline {\n  margin: 0;\n}\n\n.filter :deep() .unlabeled-select .v-select .vs__selected {\n  margin: $input-padding-sm;\n  user-select: none;\n  color: var(--header-btn-text);\n}\n\n.filter :deep() .unlabeled-select .vs__search::placeholder {\n  color: var(--header-btn-text);\n}\n\n.filter :deep() .unlabeled-select INPUT:hover {\n  background-color: transparent;\n}\n\n.filter :deep() .unlabeled-select .vs__dropdown-toggle {\n  background: rgba(0, 0, 0, 0.05);\n  border-radius: var(--border-radius);\n  border: 1px solid var(--header-btn-bg);\n  color: var(--header-btn-text);\n  height: 40px;\n  max-width: 100%;\n  padding-top: 0;\n}\n\n.filter :deep() .unlabeled-select .vs__deselect:after {\n  color: var(--header-btn-text);\n}\n\n.filter :deep() .unlabeled-select .v-select .vs__actions:after {\n  fill: var(--header-btn-text) !important;\n  color: var(--header-btn-text) !important;\n}\n\n.filter :deep() .unlabeled-select INPUT[type='search'] {\n  padding: 7px;\n}\n</style>\n"]}]}