{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DetailText.vue?vue&type=style&index=0&id=6cb9a03e&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DetailText.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci53aXRoLWNvcHkgewogIGJvcmRlcjogc29saWQgMXB4IHZhcigtLWJvcmRlcik7CiAgYm9yZGVyLXJhZGl1czogdmFyKC0tYm9yZGVyLXJhZGl1cyk7CiAgcGFkZGluZzogMTBweDsKICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0taW5wdXQtYmcpOwogIGJvcmRlci1yYWRpdXM6IHZhcigtLWJvcmRlci1yYWRpdXMpOwogIGJvcmRlcjogc29saWQgdmFyKC0tYm9yZGVyLXdpZHRoKSB2YXIoLS1pbnB1dC1ib3JkZXIpOwoKICA+IGJ1dHRvbiB7CiAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICB0b3A6IC0xcHg7CiAgICByaWdodDogLTFweDsKICAgIGJvcmRlci1yYWRpdXM6IDAgMCAwIHZhcigtLWJvcmRlci1yYWRpdXMpOwogIH0KfQoKLm1vbm9zcGFjZSB7CiAgd2hpdGUtc3BhY2U6IHByZS13cmFwOwogIHdvcmQtd3JhcDogYnJlYWstYWxsCn0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DetailText.vue"], "names": [], "mappings": ";AAiMA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAErD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3C;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DetailText.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapGetters } from 'vuex';\nimport { asciiLike, nlToBr } from '@shell/utils/string';\nimport { HIDE_SENSITIVE } from '@shell/store/prefs';\nimport CopyToClipboard from '@shell/components/CopyToClipboard';\nimport CodeMirror from '@shell/components/CodeMirror';\nimport { binarySize } from '@shell/utils/crypto';\n\nexport default {\n  components: { CopyToClipboard, CodeMirror },\n\n  props: {\n    label: {\n      type:    String,\n      default: null,\n    },\n\n    labelKey: {\n      type:    String,\n      default: null,\n    },\n\n    value: {\n      type:    String,\n      default: null,\n    },\n\n    maxLength: {\n      type:    Number,\n      default: 640, // Ought to be enough for anybody\n    },\n\n    binary: {\n      type:    Boolean,\n      default: null, // Autodetect\n    },\n\n    conceal: {\n      type:    Boolean,\n      default: false\n    },\n\n    monospace: {\n      type:    Boolean,\n      default: true\n    },\n\n    copy: {\n      type:    Boolean,\n      default: true\n    }\n  },\n\n  data() {\n    const expanded = this.value.length <= this.maxLength;\n\n    return { expanded };\n  },\n\n  computed: {\n    isBinary() {\n      if ( this.binary === null ) {\n        return typeof this.value === 'string' && !asciiLike(this.value);\n      }\n\n      return this.binary;\n    },\n\n    size() {\n      return `${ this.value }`.length;\n    },\n\n    isLong() {\n      return this.size > this.maxLength;\n    },\n\n    isEmpty() {\n      return this.size === 0;\n    },\n\n    body() {\n      if (this.isBinary) {\n        return this.t('detailText.binary', { n: this.value.length ? binarySize(this.value) : 0 }, true);\n      }\n\n      if (this.expanded) {\n        return this.value;\n      }\n\n      return this.value.slice(0, this.maxLength);\n    },\n\n    jsonStr() {\n      const value = this.value;\n\n      if ( value && ( value.startsWith('{') || value.startsWith('[') ) ) {\n        try {\n          let parsed = JSON.parse(value);\n\n          parsed = JSON.stringify(parsed, null, 2);\n\n          return parsed;\n        } catch {\n        }\n      }\n\n      return null;\n    },\n\n    bodyHtml() {\n      // Includes escapeHtml()\n      return nlToBr(this.body);\n    },\n\n    plusMore() {\n      if (this.expanded) {\n        return this.t('detailText.collapse');\n      }\n\n      const more = Math.max(this.size - this.maxLength, 0);\n\n      return this.t('detailText.plusMore', { n: more }).trim();\n    },\n\n    hideSensitiveData() {\n      return this.$store.getters['prefs/get'](HIDE_SENSITIVE);\n    },\n\n    concealed() {\n      return this.conceal && this.hideSensitiveData && !this.isBinary;\n    },\n\n    ...mapGetters({ t: 'i18n/t' })\n  },\n  methods: {\n    expand() {\n      this.expanded = !this.expanded;\n    },\n  }\n};\n</script>\n\n<template>\n  <div :class=\"{'force-wrap': true, 'with-copy':copy}\">\n    <h5\n      v-if=\"labelKey\"\n      v-t=\"labelKey\"\n    />\n    <h5 v-else-if=\"label\">\n      {{ label }}\n    </h5>\n\n    <span\n      v-if=\"isEmpty\"\n      v-t=\"'detailText.empty'\"\n      class=\"text-italic\"\n    />\n    <span\n      v-else-if=\"isBinary\"\n      class=\"text-italic\"\n    >{{ body }}</span>\n\n    <CodeMirror\n      v-else-if=\"jsonStr\"\n      :options=\"{mode:{name:'javascript', json:true}, lineNumbers:false, foldGutter:false, readOnly:true}\"\n      :value=\"jsonStr\"\n      :class=\"{'conceal': concealed}\"\n    />\n\n    <span\n      v-else\n      v-clean-html=\"bodyHtml\"\n      data-testid=\"detail-top_html\"\n      :class=\"{'conceal': concealed, 'monospace': monospace && !isBinary}\"\n    />\n\n    <template v-if=\"!isBinary && !jsonStr && isLong && !expanded\">\n      <a\n        href=\"#\"\n        @click.prevent=\"expand\"\n      >{{ plusMore }}</a>\n    </template>\n\n    <CopyToClipboard\n      v-if=\"copy && !isBinary\"\n      :text=\"value\"\n      class=\"role-tertiary\"\n      action-color=\"\"\n    />\n  </div>\n</template>\n\n<style lang='scss' scoped>\n.with-copy {\n  border: solid 1px var(--border);\n  border-radius: var(--border-radius);\n  padding: 10px;\n  position: relative;\n  background-color: var(--input-bg);\n  border-radius: var(--border-radius);\n  border: solid var(--border-width) var(--input-border);\n\n  > button {\n    position: absolute;\n    top: -1px;\n    right: -1px;\n    border-radius: 0 0 0 var(--border-radius);\n  }\n}\n\n.monospace {\n  white-space: pre-wrap;\n  word-wrap: break-all\n}\n</style>\n"]}]}