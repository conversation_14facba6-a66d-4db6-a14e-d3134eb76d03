{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/ConfigSummary.vue?vue&type=style&index=0&id=479b2bdb&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/ConfigSummary.vue", "mtime": 1755002061656}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/ConfigSummary.vue"], "names": [], "mappings": ";AAmOA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEnC,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE1B,EAAE;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAET,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClB;MACF;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5E;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACX;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEvB,EAAE;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAChB;;MAEA,CAAC,EAAE;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClB;;MAEA,EAAE;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;MACpB;;MAEA,CAAC,EAAE;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAElB,CAAC,EAAE;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QACf;MACF;IACF;EACF;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/ConfigSummary.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent, PropType } from 'vue';\nimport { EKSConfig, EKSNodeGroup, NormanCluster } from '../../types';\nimport LabelValue from '@shell/components/LabelValue.vue';\nimport Banner from '@components/Banner/Banner.vue';\n\nexport default defineComponent({\n  name: 'ConfigSummary',\n\n  components: {\n    LabelValue,\n    Banner\n  },\n\n  props: {\n    normanCluster: {\n      type:     Object as PropType<NormanCluster>,\n      required: true\n    },\n    config: {\n      type:     Object as PropType<EKSConfig>,\n      required: true\n    },\n    nodeGroups: {\n      type:    Array as PropType<EKSNodeGroup[]>,\n      default: () => []\n    },\n    region: {\n      type:    String,\n      default: ''\n    }\n  },\n\n  computed: {\n    clusterName(): string {\n      return this.normanCluster?.name || 'Not set';\n    },\n\n    awsRegion(): string {\n      return this.config?.region || this.region || 'Not set';\n    },\n\n    kubernetesVersion(): string {\n      return this.config?.kubernetesVersion || 'Latest stable';\n    },\n\n    totalNodes(): { min: number; max: number; desired: number } {\n      const totals = this.nodeGroups.reduce((acc, group) => {\n        return {\n          min:     acc.min + (group.minSize || 0),\n          max:     acc.max + (group.maxSize || 0),\n          desired: acc.desired + (group.desiredSize || 0)\n        };\n      }, { min: 0, max: 0, desired: 0 });\n\n      return totals;\n    },\n\n    primaryNodeGroup(): EKSNodeGroup | null {\n      return this.nodeGroups[0] || null;\n    },\n\n    estimatedMonthlyCost(): string {\n      // Simple cost estimation\n      const costMap: Record<string, number> = {\n        't3.small':   15,\n        't3.medium':  30,\n        't3.large':   60,\n        't3.xlarge':  120,\n        't4g.small':  12,\n        't4g.medium': 24,\n        't4g.large':  48,\n        'm5.large':   70,\n        'm5.xlarge':  140,\n        'm6i.large':  70,\n        'm6i.xlarge': 140,\n      };\n\n      let totalCost = 0;\n      this.nodeGroups.forEach(group => {\n        const instanceCost = group.instanceType ? costMap[group.instanceType] || 50 : 50;\n        totalCost += instanceCost * (group.desiredSize || 2);\n      });\n\n      // Add EKS control plane cost ($0.10/hour = ~$73/month)\n      totalCost += 73;\n\n      return `$${totalCost}`;\n    },\n\n    networkingMode(): string {\n      if (this.config?.publicAccess && !this.config?.privateAccess) {\n        return 'Public';\n      } else if (!this.config?.publicAccess && this.config?.privateAccess) {\n        return 'Private';\n      } else if (this.config?.publicAccess && this.config?.privateAccess) {\n        return 'Public and Private';\n      }\n      return 'Default (Public)';\n    }\n  }\n});\n</script>\n\n<template>\n  <div class=\"config-summary\">\n    <div class=\"summary-section\">\n      <h3>\n        <i class=\"icon icon-cluster\" />\n        Cluster Configuration\n      </h3>\n      \n      <div class=\"summary-grid\">\n        <LabelValue\n          name=\"Cluster Name\"\n          :value=\"clusterName\"\n          class=\"summary-item\"\n        />\n        \n        <LabelValue\n          name=\"AWS Region\"\n          :value=\"awsRegion\"\n          class=\"summary-item\"\n        />\n        \n        <LabelValue\n          name=\"Kubernetes Version\"\n          :value=\"kubernetesVersion\"\n          class=\"summary-item\"\n        />\n        \n        <LabelValue\n          name=\"Network Access\"\n          :value=\"networkingMode\"\n          class=\"summary-item\"\n        />\n      </div>\n    </div>\n\n    <div class=\"summary-section mt-20\">\n      <h3>\n        <i class=\"icon icon-nodes\" />\n        Node Configuration\n      </h3>\n      \n      <div\n        v-if=\"primaryNodeGroup\"\n        class=\"node-summary\"\n      >\n        <div class=\"summary-grid\">\n          <LabelValue\n            name=\"Instance Type\"\n            :value=\"primaryNodeGroup.instanceType\"\n            class=\"summary-item\"\n          />\n          \n          <LabelValue\n            name=\"Disk Size\"\n            :value=\"`${primaryNodeGroup.diskSize} GB`\"\n            class=\"summary-item\"\n          />\n          \n          <LabelValue\n            name=\"Auto-scaling\"\n            :value=\"`${totalNodes.min} - ${totalNodes.max} nodes`\"\n            class=\"summary-item\"\n          />\n          \n          <LabelValue\n            name=\"Initial Size\"\n            :value=\"`${totalNodes.desired} nodes`\"\n            class=\"summary-item\"\n          />\n        </div>\n      </div>\n\n      <div\n        v-if=\"nodeGroups.length > 1\"\n        class=\"mt-10\"\n      >\n        <Banner color=\"info\">\n          <p>{{ nodeGroups.length }} node groups configured</p>\n        </Banner>\n      </div>\n    </div>\n\n    <div class=\"summary-section mt-20 cost-section\">\n      <h3>\n        <i class=\"icon icon-dollar\" />\n        Estimated Cost\n      </h3>\n      \n      <div class=\"cost-breakdown\">\n        <div class=\"cost-main\">\n          <span class=\"cost-label\">Monthly estimate:</span>\n          <span class=\"cost-value\">{{ estimatedMonthlyCost }}</span>\n        </div>\n        <p class=\"cost-disclaimer\">\n          * This is a rough estimate based on instance types and does not include data transfer, storage, or other AWS services.\n        </p>\n      </div>\n    </div>\n\n    <div class=\"summary-section mt-20\">\n      <Banner\n        color=\"success\"\n        class=\"ready-banner\"\n      >\n        <div class=\"ready-content\">\n          <i class=\"icon icon-checkmark icon-2x\" />\n          <div>\n            <h4>Ready to create your cluster!</h4>\n            <p>Your cluster will be created with production-ready defaults including:</p>\n            <ul>\n              <li>Automatic security updates</li>\n              <li>Network isolation and security groups</li>\n              <li>CloudWatch logging enabled</li>\n              <li>IAM roles for service accounts (IRSA)</li>\n            </ul>\n          </div>\n        </div>\n      </Banner>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.config-summary {\n  .summary-section {\n    padding: 20px;\n    background: var(--body-bg);\n    border: 1px solid var(--border);\n    border-radius: var(--border-radius);\n\n    h3 {\n      margin: 0 0 20px 0;\n      font-size: 18px;\n      font-weight: 600;\n      display: flex;\n      align-items: center;\n      gap: 10px;\n      color: var(--text-default);\n\n      i {\n        color: var(--primary);\n      }\n    }\n  }\n\n  .summary-grid {\n    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n    gap: 20px;\n\n    .summary-item {\n      padding: 10px;\n      background: var(--nav-bg);\n      border-radius: var(--border-radius);\n      \n      ::v-deep .labeled-value {\n        .label {\n          color: var(--text-muted);\n          font-size: 12px;\n          text-transform: uppercase;\n          margin-bottom: 5px;\n        }\n\n        .value {\n          color: var(--text-default);\n          font-size: 16px;\n          font-weight: 500;\n        }\n      }\n    }\n  }\n\n  .cost-section {\n    background: linear-gradient(135deg, var(--body-bg) 0%, var(--nav-bg) 100%);\n  }\n\n  .cost-breakdown {\n    .cost-main {\n      display: flex;\n      align-items: baseline;\n      gap: 15px;\n      margin-bottom: 10px;\n\n      .cost-label {\n        font-size: 16px;\n        color: var(--text-muted);\n      }\n\n      .cost-value {\n        font-size: 32px;\n        font-weight: 600;\n        color: var(--success);\n      }\n    }\n\n    .cost-disclaimer {\n      font-size: 12px;\n      color: var(--text-muted);\n      font-style: italic;\n      margin: 0;\n    }\n  }\n\n  .ready-banner {\n    .ready-content {\n      display: flex;\n      gap: 20px;\n      align-items: flex-start;\n\n      i {\n        color: var(--success);\n        flex-shrink: 0;\n      }\n\n      h4 {\n        margin: 0 0 10px 0;\n        font-size: 18px;\n        font-weight: 600;\n      }\n\n      p {\n        margin: 0 0 10px 0;\n      }\n\n      ul {\n        margin: 0;\n        padding-left: 20px;\n\n        li {\n          margin: 5px 0;\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}