{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Members/MembershipEditor.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Members/MembershipEditor.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Members/MembershipEditor.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACpC;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACnE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC3G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1D;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;;IAE1E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAC9H;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACpF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAC9E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACtE,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEtD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEpF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEzC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACvB,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/G,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1D,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE5C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC;;UAEF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE1E,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D;MACF,CAAC;IACH,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxD;IACF;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC;IACJ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC;EACH;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Members/MembershipEditor.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { MANAGEMENT, NORMAN } from '@shell/config/types';\nimport ArrayList from '@shell/components/form/ArrayList';\nimport Principal from '@shell/components/auth/Principal';\nimport Loading from '@shell/components/Loading';\nimport { _CREATE, _VIEW } from '@shell/config/query-params';\nimport { get, set } from '@shell/utils/object';\n\nfunction normalizeId(id) {\n  return id?.replace(':', '/') || id;\n}\n\nexport function canViewMembershipEditor(store, needsProject = false) {\n  return (!!store.getters['management/schemaFor'](MANAGEMENT.PROJECT_ROLE_TEMPLATE_BINDING) || !needsProject) &&\n    !!store.getters['management/schemaFor'](MANAGEMENT.ROLE_TEMPLATE) &&\n    !!store.getters['rancher/schemaFor'](NORMAN.PRINCIPAL);\n}\n\nexport default {\n  emits: ['membership-update'],\n\n  components: {\n    ArrayList, Loading, Principal\n  },\n\n  props: {\n    addMemberDialogName: {\n      type:     String,\n      required: true\n    },\n\n    parentKey: {\n      type:     String,\n      required: true\n    },\n\n    parentId: {\n      type:    String,\n      default: null\n    },\n\n    mode: {\n      type:     String,\n      required: true\n    },\n\n    type: {\n      type:     String,\n      required: true\n    },\n\n    defaultBindingHandler: {\n      type:    Function,\n      default: null,\n    },\n\n    modalSticky: {\n      type:    Boolean,\n      default: false,\n    }\n  },\n\n  async fetch() {\n    const roleBindingRequestParams = { type: this.type, opt: { force: true } };\n\n    if (this.type === NORMAN.PROJECT_ROLE_TEMPLATE_BINDING && this.parentId) {\n      Object.assign(roleBindingRequestParams, { opt: { filter: { projectId: this.parentId.split('/').join(':') }, force: true } });\n    }\n    const userHydration = [\n      this.schema ? this.$store.dispatch(`rancher/findAll`, roleBindingRequestParams) : [],\n      this.$store.dispatch('rancher/findAll', { type: NORMAN.PRINCIPAL }),\n      this.$store.dispatch(`management/findAll`, { type: MANAGEMENT.ROLE_TEMPLATE }),\n      this.$store.dispatch(`management/findAll`, { type: MANAGEMENT.USER })\n    ];\n    const [allBindings] = await Promise.all(userHydration);\n\n    const bindings = allBindings\n      .filter((b) => normalizeId(get(b, this.parentKey)) === normalizeId(this.parentId));\n\n    this['lastSavedBindings'] = [...bindings];\n\n    // Add the current user as the project owner. This will get created by default\n    if (this.mode === _CREATE && bindings.length === 0 && this.defaultBindingHandler) {\n      const defaultBinding = await this.defaultBindingHandler();\n\n      defaultBinding.isDefaultBinding = true;\n      bindings.push(defaultBinding);\n    }\n\n    this['bindings'] = bindings;\n  },\n\n  data() {\n    return {\n      schema:            this.$store.getters[`rancher/schemaFor`](this.type),\n      bindings:          [],\n      lastSavedBindings: [],\n    };\n  },\n\n  computed: {\n    newBindings() {\n      return this.bindings\n        .filter((binding) => !binding.id && !this.lastSavedBindings.includes(binding) && !binding.isDefaultBinding);\n    },\n    removedBindings() {\n      return this.lastSavedBindings\n        .filter((binding) => !this.bindings.includes(binding));\n    },\n    membershipUpdate() {\n      const newBindings = this.newBindings;\n      const removedBindings = this.removedBindings;\n\n      return {\n        newBindings:     this.newBindings,\n        removedBindings: this.removedBindings,\n        save:            (parentId) => {\n          const savedPromises = newBindings.map((binding) => {\n            set(binding, this.parentKey, parentId);\n\n            return binding.save();\n          });\n\n          const removedPromises = removedBindings.map((binding) => binding.remove());\n\n          return Promise.all([...savedPromises, ...removedPromises]);\n        }\n      };\n    },\n\n    isCreate() {\n      return this.mode === _CREATE;\n    },\n\n    isView() {\n      return this.mode === _VIEW;\n    },\n  },\n  watch: {\n    membershipUpdate: {\n      deep: true,\n      handler() {\n        this.$emit('membership-update', this.membershipUpdate);\n      }\n    }\n  },\n\n  methods: {\n    addMember() {\n      this.$store.dispatch('cluster/promptModal', {\n        component:      this.addMemberDialogName,\n        componentProps: { onAdd: this.onAddMember },\n        modalSticky:    this.modalSticky\n      });\n    },\n\n    onAddMember(bindings) {\n      this['bindings'] = [...this.bindings, ...bindings];\n    },\n  }\n};\n</script>\n<template>\n  <Loading v-if=\"$fetchState.pending\" />\n  <ArrayList\n    v-else\n    v-model:value=\"bindings\"\n    :mode=\"mode\"\n    :show-header=\"true\"\n  >\n    <template #column-headers>\n      <div class=\"box mb-0\">\n        <div class=\"column-headers row\">\n          <div class=\"col span-6\">\n            <label class=\"text-label\">{{ t('membershipEditor.user') }}</label>\n          </div>\n          <div class=\"col span-6\">\n            <label class=\"text-label\">{{ t('membershipEditor.role') }}</label>\n          </div>\n        </div>\n      </div>\n    </template>\n    <template #columns=\"{row, i}\">\n      <div class=\"columns row\">\n        <div class=\"col span-6\">\n          <Principal\n            :value=\"row.value.principalId\"\n          />\n        </div>\n        <div\n          :data-testid=\"`role-item-${i}`\"\n          class=\"col span-6 role\"\n        >\n          {{ row.value.roleDisplay }}\n        </div>\n      </div>\n    </template>\n    <template #add>\n      <button\n        type=\"button\"\n        class=\"btn role-primary mt-10\"\n        data-testid=\"add-item\"\n        @click=\"addMember\"\n      >\n        {{ t('generic.add') }}\n      </button>\n    </template>\n    <template #remove-button=\"{remove, i}\">\n      <span v-if=\"(isCreate && i === 0) || isView\" />\n      <button\n        v-else\n        type=\"button\"\n        :disabled=\"isView\"\n        class=\"btn role-link\"\n        :data-testid=\"`remove-item-${i}`\"\n        @click=\"remove\"\n      >\n        {{ t('generic.remove') }}\n      </button>\n    </template>\n  </ArrayList>\n</template>\n\n<style lang=\"scss\" scoped>\n.role {\n  display: flex;\n  align-items: center;\n  flex-direction: row;\n}\n</style>\n"]}]}