{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DetailText.vue?vue&type=template&id=6cb9a03e&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DetailText.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdiA6Y2xhc3M9InsnZm9yY2Utd3JhcCc6IHRydWUsICd3aXRoLWNvcHknOmNvcHl9Ij4KICAgIDxoNQogICAgICB2LWlmPSJsYWJlbEtleSIKICAgICAgdi10PSJsYWJlbEtleSIKICAgIC8+CiAgICA8aDUgdi1lbHNlLWlmPSJsYWJlbCI+CiAgICAgIHt7IGxhYmVsIH19CiAgICA8L2g1PgoKICAgIDxzcGFuCiAgICAgIHYtaWY9ImlzRW1wdHkiCiAgICAgIHYtdD0iJ2RldGFpbFRleHQuZW1wdHknIgogICAgICBjbGFzcz0idGV4dC1pdGFsaWMiCiAgICAvPgogICAgPHNwYW4KICAgICAgdi1lbHNlLWlmPSJpc0JpbmFyeSIKICAgICAgY2xhc3M9InRleHQtaXRhbGljIgogICAgPnt7IGJvZHkgfX08L3NwYW4+CgogICAgPENvZGVNaXJyb3IKICAgICAgdi1lbHNlLWlmPSJqc29uU3RyIgogICAgICA6b3B0aW9ucz0ie21vZGU6e25hbWU6J2phdmFzY3JpcHQnLCBqc29uOnRydWV9LCBsaW5lTnVtYmVyczpmYWxzZSwgZm9sZEd1dHRlcjpmYWxzZSwgcmVhZE9ubHk6dHJ1ZX0iCiAgICAgIDp2YWx1ZT0ianNvblN0ciIKICAgICAgOmNsYXNzPSJ7J2NvbmNlYWwnOiBjb25jZWFsZWR9IgogICAgLz4KCiAgICA8c3BhbgogICAgICB2LWVsc2UKICAgICAgdi1jbGVhbi1odG1sPSJib2R5SHRtbCIKICAgICAgZGF0YS10ZXN0aWQ9ImRldGFpbC10b3BfaHRtbCIKICAgICAgOmNsYXNzPSJ7J2NvbmNlYWwnOiBjb25jZWFsZWQsICdtb25vc3BhY2UnOiBtb25vc3BhY2UgJiYgIWlzQmluYXJ5fSIKICAgIC8+CgogICAgPHRlbXBsYXRlIHYtaWY9IiFpc0JpbmFyeSAmJiAhanNvblN0ciAmJiBpc0xvbmcgJiYgIWV4cGFuZGVkIj4KICAgICAgPGEKICAgICAgICBocmVmPSIjIgogICAgICAgIEBjbGljay5wcmV2ZW50PSJleHBhbmQiCiAgICAgID57eyBwbHVzTW9yZSB9fTwvYT4KICAgIDwvdGVtcGxhdGU+CgogICAgPENvcHlUb0NsaXBib2FyZAogICAgICB2LWlmPSJjb3B5ICYmICFpc0JpbmFyeSIKICAgICAgOnRleHQ9InZhbHVlIgogICAgICBjbGFzcz0icm9sZS10ZXJ0aWFyeSIKICAgICAgYWN0aW9uLWNvbG9yPSIiCiAgICAvPgogIDwvZGl2Pgo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DetailText.vue"], "names": [], "mappings": ";EA+IE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC,CAAC;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC;;IAEJ,CAAC,CAAC,CAAC,CAAC;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC;MACF,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrE,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3D,CAAC;QACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;EACH,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DetailText.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapGetters } from 'vuex';\nimport { asciiLike, nlToBr } from '@shell/utils/string';\nimport { HIDE_SENSITIVE } from '@shell/store/prefs';\nimport CopyToClipboard from '@shell/components/CopyToClipboard';\nimport CodeMirror from '@shell/components/CodeMirror';\nimport { binarySize } from '@shell/utils/crypto';\n\nexport default {\n  components: { CopyToClipboard, CodeMirror },\n\n  props: {\n    label: {\n      type:    String,\n      default: null,\n    },\n\n    labelKey: {\n      type:    String,\n      default: null,\n    },\n\n    value: {\n      type:    String,\n      default: null,\n    },\n\n    maxLength: {\n      type:    Number,\n      default: 640, // Ought to be enough for anybody\n    },\n\n    binary: {\n      type:    Boolean,\n      default: null, // Autodetect\n    },\n\n    conceal: {\n      type:    Boolean,\n      default: false\n    },\n\n    monospace: {\n      type:    Boolean,\n      default: true\n    },\n\n    copy: {\n      type:    Boolean,\n      default: true\n    }\n  },\n\n  data() {\n    const expanded = this.value.length <= this.maxLength;\n\n    return { expanded };\n  },\n\n  computed: {\n    isBinary() {\n      if ( this.binary === null ) {\n        return typeof this.value === 'string' && !asciiLike(this.value);\n      }\n\n      return this.binary;\n    },\n\n    size() {\n      return `${ this.value }`.length;\n    },\n\n    isLong() {\n      return this.size > this.maxLength;\n    },\n\n    isEmpty() {\n      return this.size === 0;\n    },\n\n    body() {\n      if (this.isBinary) {\n        return this.t('detailText.binary', { n: this.value.length ? binarySize(this.value) : 0 }, true);\n      }\n\n      if (this.expanded) {\n        return this.value;\n      }\n\n      return this.value.slice(0, this.maxLength);\n    },\n\n    jsonStr() {\n      const value = this.value;\n\n      if ( value && ( value.startsWith('{') || value.startsWith('[') ) ) {\n        try {\n          let parsed = JSON.parse(value);\n\n          parsed = JSON.stringify(parsed, null, 2);\n\n          return parsed;\n        } catch {\n        }\n      }\n\n      return null;\n    },\n\n    bodyHtml() {\n      // Includes escapeHtml()\n      return nlToBr(this.body);\n    },\n\n    plusMore() {\n      if (this.expanded) {\n        return this.t('detailText.collapse');\n      }\n\n      const more = Math.max(this.size - this.maxLength, 0);\n\n      return this.t('detailText.plusMore', { n: more }).trim();\n    },\n\n    hideSensitiveData() {\n      return this.$store.getters['prefs/get'](HIDE_SENSITIVE);\n    },\n\n    concealed() {\n      return this.conceal && this.hideSensitiveData && !this.isBinary;\n    },\n\n    ...mapGetters({ t: 'i18n/t' })\n  },\n  methods: {\n    expand() {\n      this.expanded = !this.expanded;\n    },\n  }\n};\n</script>\n\n<template>\n  <div :class=\"{'force-wrap': true, 'with-copy':copy}\">\n    <h5\n      v-if=\"labelKey\"\n      v-t=\"labelKey\"\n    />\n    <h5 v-else-if=\"label\">\n      {{ label }}\n    </h5>\n\n    <span\n      v-if=\"isEmpty\"\n      v-t=\"'detailText.empty'\"\n      class=\"text-italic\"\n    />\n    <span\n      v-else-if=\"isBinary\"\n      class=\"text-italic\"\n    >{{ body }}</span>\n\n    <CodeMirror\n      v-else-if=\"jsonStr\"\n      :options=\"{mode:{name:'javascript', json:true}, lineNumbers:false, foldGutter:false, readOnly:true}\"\n      :value=\"jsonStr\"\n      :class=\"{'conceal': concealed}\"\n    />\n\n    <span\n      v-else\n      v-clean-html=\"bodyHtml\"\n      data-testid=\"detail-top_html\"\n      :class=\"{'conceal': concealed, 'monospace': monospace && !isBinary}\"\n    />\n\n    <template v-if=\"!isBinary && !jsonStr && isLong && !expanded\">\n      <a\n        href=\"#\"\n        @click.prevent=\"expand\"\n      >{{ plusMore }}</a>\n    </template>\n\n    <CopyToClipboard\n      v-if=\"copy && !isBinary\"\n      :text=\"value\"\n      class=\"role-tertiary\"\n      action-color=\"\"\n    />\n  </div>\n</template>\n\n<style lang='scss' scoped>\n.with-copy {\n  border: solid 1px var(--border);\n  border-radius: var(--border-radius);\n  padding: 10px;\n  position: relative;\n  background-color: var(--input-bg);\n  border-radius: var(--border-radius);\n  border: solid var(--border-width) var(--input-border);\n\n  > button {\n    position: absolute;\n    top: -1px;\n    right: -1px;\n    border-radius: 0 0 0 var(--border-radius);\n  }\n}\n\n.monospace {\n  white-space: pre-wrap;\n  word-wrap: break-all\n}\n</style>\n"]}]}