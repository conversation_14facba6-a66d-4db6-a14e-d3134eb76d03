{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DashboardOptions.vue?vue&type=style&index=0&id=40e0fc26&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DashboardOptions.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouZ3JhcGgtb3B0aW9ucyB7DQogICAgJiwgLnJhbmdlLXJlZnJlc2ggew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7DQogICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kOw0KICAgIH0NCg0KICAgICYgew0KICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICB9DQoNCiAgICAubGFiZWxlZC1zZWxlY3Qgew0KICAgICAgICB3aWR0aDogMTAwcHg7DQogICAgICAgIG1hcmdpbi1sZWZ0OiAxMHB4Ow0KICAgIH0NCn0NCg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DashboardOptions.vue"], "names": [], "mappings": ";AAiIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B;;IAEA,EAAE;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrB;AACJ", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DashboardOptions.vue", "sourceRoot": "", "sourcesContent": ["<script>\r\nimport ButtonGroup from '@shell/components/ButtonGroup';\r\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\r\n\r\nexport default {\r\n  components: { ButtonGroup, LabeledSelect },\r\n  props:      {\r\n    value: {\r\n      type:     Object,\r\n      required: true,\r\n    },\r\n    hasSummaryAndDetail: {\r\n      type:    Boolean,\r\n      default: true,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      range:        null,\r\n      rangeOptions: [\r\n        {\r\n          label: this.t('generic.units.time.5m'),\r\n          value: '5m',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.1h'),\r\n          value: '1h',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.6h'),\r\n          value: '6h',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.1d'),\r\n          value: '1d',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.7d'),\r\n          value: '7d',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.30d'),\r\n          value: '30d',\r\n        },\r\n      ],\r\n      refreshOptions: [\r\n        {\r\n          label: this.t('generic.units.time.5s'),\r\n          value: '5s',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.10s'),\r\n          value: '10s',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.30s'),\r\n          value: '30s',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.1m'),\r\n          value: '1m',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.5m'),\r\n          value: '5m',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.15m'),\r\n          value: '15m',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.30m'),\r\n          value: '30m',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.1h'),\r\n          value: '1h',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.2h'),\r\n          value: '2h',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.1d'),\r\n          value: '1d',\r\n        }\r\n      ],\r\n      detailSummaryOptions: [\r\n        {\r\n          label: this.t('graphOptions.detail'),\r\n          value: 'detail'\r\n        },\r\n        {\r\n          label: this.t('graphOptions.summary'),\r\n          value: 'summary'\r\n        }\r\n      ]\r\n    };\r\n  }\r\n};\r\n</script>\r\n\r\n<template>\r\n  <div class=\"graph-options\">\r\n    <div v-if=\"hasSummaryAndDetail\">\r\n      <ButtonGroup\n        v-model:value=\"value.type\"\n        :options=\"detailSummaryOptions\"\n      />\r\n    </div>\r\n    <div v-else>\r\n      <div />\r\n    </div>\r\n    <div class=\"range-refresh\">\r\n      <LabeledSelect\n        v-model:value=\"value.range\"\n        :options=\"rangeOptions\"\n        :label=\"t('graphOptions.range')\"\n      />\r\n      <LabeledSelect\n        v-model:value=\"value.refreshRate\"\n        :options=\"refreshOptions\"\n        :label=\"t('graphOptions.refresh')\"\n      />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<style lang='scss' scoped>\r\n.graph-options {\r\n    &, .range-refresh {\r\n      display: flex;\r\n      flex-direction: row;\r\n      justify-content: flex-end;\r\n    }\r\n\r\n    & {\r\n      justify-content: space-between;\r\n      align-items: center;\r\n    }\r\n\r\n    .labeled-select {\r\n        width: 100px;\r\n        margin-left: 10px;\r\n    }\r\n}\r\n</style>\r\n"]}]}