{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/FileDiff.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/FileDiff.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/FileDiff.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEtE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAElC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,EAAE;MACH,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACpB,CAAC,EAAE,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAExB,CAAC,EAAE,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAExE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR;;MAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEjC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR;;MAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEhD,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR;;MAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAElE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACxD,CAAC;EACH,CAAC;AACH,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/FileDiff.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { Diff2HtmlUI } from 'diff2html/lib/ui/js/diff2html-ui-slim.js';\n\nimport { createPatch } from 'diff';\n\nexport default {\n  props: {\n    filename: {\n      type:    String,\n      default: 'file.txt',\n    },\n\n    sideBySide: {\n      type:    Boolean,\n      default: false,\n    },\n\n    orig: {\n      type:     String,\n      required: true,\n    },\n\n    neu: {\n      type:     String,\n      required: true,\n    },\n\n    autoResize: {\n      type:    Boolean,\n      default: true,\n    },\n    footerSpace: {\n      type:    Number,\n      default: 0,\n    },\n    minHeight: {\n      type:    Number,\n      default: 200,\n    }\n  },\n\n  mounted() {\n    this.draw();\n  },\n\n  watch: {\n    sideBySide() {\n      this.draw();\n    }\n  },\n\n  methods: {\n    draw() {\n      const targetElement = document.getElementById('diffElement');\n      const patch = createPatch(\n        this.filename,\n        this.orig,\n        this.neu\n      );\n      const configuration = {\n        // UI\n        synchronisedScroll: true,\n\n        // Base\n        outputFormat: this.sideBySide ? 'side-by-side' : 'line-by-line',\n        drawFileList: false,\n        matching:     'words',\n      };\n\n      const diff2htmlUi = new Diff2HtmlUI(targetElement, patch, configuration);\n\n      diff2htmlUi.draw();\n      this.fit();\n    },\n\n    fit() {\n      if ( !this.autoResize ) {\n        return;\n      }\n\n      const container = this.$refs.root;\n\n      if ( !container ) {\n        return;\n      }\n\n      const offset = container.getBoundingClientRect();\n\n      if ( !offset ) {\n        return;\n      }\n\n      const desired = window.innerHeight - offset.top - this.footerSpace;\n\n      container.style.height = `${ Math.max(0, desired) }px`;\n    },\n  },\n};\n</script>\n\n<template>\n  <div>\n    <resize-observer @notify=\"fit\" />\n    <div\n      id=\"diffElement\"\n      ref=\"root\"\n      class=\"root\"\n    />\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.root {\n  max-width: 100%;\n  position: relative;\n  overflow: auto;\n}\n</style>\n\n<style scoped lang=\"scss\">\n@import 'node_modules/diff2html/bundles/css/diff2html.min.css';\n\n:deep() .d2h-wrapper {\n  .d2h-file-header {\n    display: none;\n  }\n\n  .d2h-file-wrapper {\n    border-color: var(--diff-border);\n  }\n\n  .d2h-diff-table {\n    font-family: Menlo,Consolas,monospace;\n    font-size: 13px;\n  }\n\n  .d2h-emptyplaceholder, .d2h-code-side-emptyplaceholder {\n    border-color: var(--diff-linenum-border);\n    background-color: var(--diff-empty-placeholder);\n  }\n\n  .d2h-code-linenumber,\n  .d2h-code-side-linenumber {\n    background-color: var(--diff-linenum-bg);\n    color: var(--diff-linenum);\n    border-color: var(--diff-linenum-border);\n    border-left: 0;\n  }\n\n  .d2h-code-line del,.d2h-code-side-line del {\n    background-color: var(--diff-line-del-bg);\n  }\n\n  .d2h-code-line ins,.d2h-code-side-line ins {\n    background-color: var(--diff-line-ins-bg);\n  }\n\n  .d2h-del {\n    background-color: var(--diff-del-bg);\n    border-color: var(--diff-del-border);\n    color: var(--body-text);\n  }\n\n  .d2h-ins {\n    background-color: var(--diff-ins-bg);\n    border-color: var(--diff-ins-border);\n    color: var(--body-text);\n  }\n\n  .d2h-info {\n    background-color: var(--diff-header-bg);\n    color: var(--diff-header);\n    border-color: var(--diff-header-border);\n  }\n\n  .d2h-file-diff .d2h-del.d2h-change {\n    background-color: var(--diff-chg-del);\n  }\n\n  .d2h-file-diff .d2h-ins.d2h-change {\n    background-color: var(--diff-chg-ins);\n  }\n}\n</style>\n"]}]}