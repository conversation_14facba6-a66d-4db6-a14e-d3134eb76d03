{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/CloudCredential.vue?vue&type=template&id=774c6697", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/CloudCredential.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdiBjbGFzcz0icm93Ij4KICAgIDxkaXYgY2xhc3M9ImNvbCBzcGFuLTYiPgogICAgICA8TGFiZWxlZFNlbGVjdAogICAgICAgIDptb2RlPSJtb2RlIgogICAgICAgIDpvcHRpb25zPSJvcHRpb25zIgogICAgICAgIDpkaXNhYmxlZD0iJGZldGNoU3RhdGUucGVuZGluZyB8fCBkaXNhYmxlZCIKICAgICAgICA6bGFiZWw9ImRpc3BsYXlMYWJlbCIKICAgICAgICA6cGxhY2Vob2xkZXI9InF1ZXN0aW9uLmRlc2NyaXB0aW9uIgogICAgICAgIDpyZXF1aXJlZD0icXVlc3Rpb24ucmVxdWlyZWQiCiAgICAgICAgOnZhbHVlPSJ2YWx1ZSIKICAgICAgICA6dG9vbHRpcD0iZGlzcGxheVRvb2x0aXAiCiAgICAgICAgQHVwZGF0ZTp2YWx1ZT0iISRmZXRjaFN0YXRlLnBlbmRpbmcgJiYgJGVtaXQoJ3VwZGF0ZTp2YWx1ZScsICRldmVudCkiCiAgICAgIC8+CiAgICA8L2Rpdj4KICAgIDxkaXYgY2xhc3M9ImNvbCBzcGFuLTYgbXQtMTAiPgogICAgICA8ZGl2IHYtaWY9InNob3dEZXNjcmlwdGlvbiI+CiAgICAgICAge3sgcXVlc3Rpb24uZGVzY3JpcHRpb24gfX0KICAgICAgPC9kaXY+CiAgICA8L2Rpdj4KICA8L2Rpdj4K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/CloudCredential.vue"], "names": [], "mappings": ";EAiCE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtE,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/CloudCredential.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport { NORMAN } from '@shell/config/types';\nimport Question from './Question';\n\nexport default {\n  emits: ['update:value'],\n\n  components: { LabeledSelect },\n  mixins:     [Question],\n\n  async fetch() {\n    this.all = await this.$store.dispatch('rancher/findAll', { type: NORMAN.CLOUD_CREDENTIAL });\n  },\n\n  data() {\n    return { all: [] };\n  },\n\n  computed: {\n    options() {\n      return this.all.map((x) => {\n        return {\n          label: x.nameDisplay || x.name || x.metadata.name,\n          value: x.id\n        };\n      });\n    }\n  },\n};\n</script>\n\n<template>\n  <div class=\"row\">\n    <div class=\"col span-6\">\n      <LabeledSelect\n        :mode=\"mode\"\n        :options=\"options\"\n        :disabled=\"$fetchState.pending || disabled\"\n        :label=\"displayLabel\"\n        :placeholder=\"question.description\"\n        :required=\"question.required\"\n        :value=\"value\"\n        :tooltip=\"displayTooltip\"\n        @update:value=\"!$fetchState.pending && $emit('update:value', $event)\"\n      />\n    </div>\n    <div class=\"col span-6 mt-10\">\n      <div v-if=\"showDescription\">\n        {{ question.description }}\n      </div>\n    </div>\n  </div>\n</template>\n"]}]}