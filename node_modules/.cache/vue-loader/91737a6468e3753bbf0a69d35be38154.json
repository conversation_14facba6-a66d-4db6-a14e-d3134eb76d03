{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/PromptModal.vue?vue&type=style&index=0&id=436d0ff6&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/PromptModal.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLnByb21wdE1vZGFsLW1vZGFsIHsKICAgIGJvcmRlci1yYWRpdXM6IHZhcigtLWJvcmRlci1yYWRpdXMpOwogICAgb3ZlcmZsb3c6IHNjcm9sbDsKICAgIG1heC1oZWlnaHQ6IDEwMHZoOwogICAgJiA6Oi13ZWJraXQtc2Nyb2xsYmFyLWNvcm5lciB7CiAgICAgIGJhY2tncm91bmQ6IHJnYmEoMCwwLDAsMCk7CiAgICB9CiAgfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/PromptModal.vue"], "names": [], "mappings": ";EAoGE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/PromptModal.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapState } from 'vuex';\nimport { isArray } from '@shell/utils/array';\nimport AppModal from '@shell/components/AppModal.vue';\n\n/**\n * @name PromptModal\n * @description Modal component.\n */\nexport default {\n  name: 'PromptModal',\n\n  components: { AppModal },\n\n  data() {\n    return { opened: false, backgroundClosing: null };\n  },\n\n  computed: {\n    ...mapState('action-menu', ['showModal', 'modalData']),\n\n    resources() {\n      let resources = this.modalData?.resources;\n\n      if (!isArray(resources)) {\n        resources = [resources];\n      }\n\n      return resources || [];\n    },\n\n    modalWidth() {\n      // property set from workload.js to overwrite modal default width of 600px, with fallback value as well\n      return this.modalData?.modalWidth || '600px';\n    },\n    component() {\n      // Looks for a dialog component by looking up in plugins and @shell/dialog/${name}.\n      return this.$store.getters['type-map/importDialog'](this.modalData?.component);\n    },\n    cssProps() {\n      // this computed property lets us generate a scss var that we can use in the style\n      return `--prompt-modal-width: ${ this.modalWidth }`;\n    },\n    stickyProps() {\n      const isSticky = !!this.modalData?.modalSticky;\n\n      return !isSticky ? '' : 'display: flex; flex-direction: column; ';\n    },\n    closeOnClickOutside() {\n      return this.modalData?.closeOnClickOutside;\n    }\n  },\n\n  watch: {\n    showModal(show) {\n      this.opened = show;\n    },\n  },\n\n  methods: {\n    close() {\n      if (!this.opened) {\n        return;\n      }\n\n      this.errors = [];\n      this.$store.commit('action-menu/togglePromptModal');\n      if (this.backgroundClosing) {\n        this.backgroundClosing();\n      }\n\n      this.opened = false;\n    },\n\n    // We're using register instead of just making use of $refs because the $refs is always undefined when referencing the component\n    registerBackgroundClosing(fn) {\n      this['backgroundClosing'] = fn;\n    }\n  },\n};\n</script>\n\n<template>\n  <app-modal\n    v-if=\"opened && component\"\n    :click-to-close=\"closeOnClickOutside\"\n    :width=\"modalWidth\"\n    @close=\"close()\"\n  >\n    <component\n      v-bind=\"modalData.componentProps || {}\"\n      :is=\"component\"\n      :resources=\"resources\"\n      :register-background-closing=\"registerBackgroundClosing\"\n      @close=\"close()\"\n    />\n  </app-modal>\n</template>\n\n<style lang='scss'>\n  .promptModal-modal {\n    border-radius: var(--border-radius);\n    overflow: scroll;\n    max-height: 100vh;\n    & ::-webkit-scrollbar-corner {\n      background: rgba(0,0,0,0);\n    }\n  }\n</style>\n"]}]}