{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ServicePorts.vue?vue&type=template&id=27fd5f15&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ServicePorts.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ServicePorts.vue"], "names": [], "mappings": ";EA6HE,CAAC,CAAC,CAAC,CAAC;IACF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvE;QACE,CAAC,CAAC,CAAC,CAAC;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAC3C,CAAC;YACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB;UACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACxC,CAAC;YACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAElC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB;UACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvE;QACE,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC;QACD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB;QACF,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB;QACF,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,CAAC,CAAC,CAAC,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB;QACF,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB;QACF,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB;YACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf;MACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf;QACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ServicePorts.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport debounce from 'lodash/debounce';\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\nimport { removeAt } from '@shell/utils/array';\nimport { clone } from '@shell/utils/object';\nimport Select from '@shell/components/form/Select';\nimport Error from '@shell/components/form/Error';\n\nexport default {\n  emits: ['update:value'],\n\n  components: { Select, Error },\n  props:      {\n    value: {\n      type:    Array,\n      default: null,\n    },\n    mode: {\n      type:    String,\n      default: _EDIT,\n    },\n    specType: {\n      type:    String,\n      default: 'ClusterIP',\n    },\n    padLeft: {\n      type:    Boolean,\n      default: false,\n    },\n    autoAddIfEmpty: {\n      type:    Boolean,\n      default: true,\n    },\n    rules: {\n      default:   () => [],\n      type:      Array,\n      // we only want functions in the rules array\n      validator: (rules) => rules.every((rule) => ['function'].includes(typeof rule))\n    }\n  },\n\n  data() {\n    const rows = clone(this.value || []);\n\n    return { rows };\n  },\n\n  computed: {\n    isView() {\n      return this.mode === _VIEW;\n    },\n\n    showAdd() {\n      return !this.isView;\n    },\n\n    showRemove() {\n      return !this.isView;\n    },\n\n    showProtocol() {\n      return this.specType !== 'NodePort';\n    },\n\n    showNodePort() {\n      return this.specType === 'NodePort' || this.specType === 'LoadBalancer';\n    },\n\n    protocolOptions() {\n      return ['TCP', 'UDP'];\n    },\n  },\n\n  created() {\n    this.queueUpdate = debounce(this.update, 500);\n  },\n\n  mounted() {\n    if ( this.isView ) {\n      return;\n    }\n\n    if (this.autoAddIfEmpty && this.mode !== _EDIT && this?.rows.length < 1) {\n      // don't focus on mount because we'll pull focus from name/namespace input\n      this.add(false);\n    }\n  },\n\n  methods: {\n    add(focus = true) {\n      this.rows.push({\n        name:       '',\n        port:       null,\n        protocol:   'TCP',\n        targetPort: null,\n      });\n\n      this.queueUpdate();\n\n      if (this.rows.length > 0 && focus) {\n        this.$nextTick(() => {\n          const inputs = this.$refs['port-name'];\n\n          inputs[inputs.length - 1].focus();\n        });\n      }\n    },\n\n    remove(idx) {\n      removeAt(this.rows, idx);\n      this.queueUpdate();\n    },\n\n    update() {\n      if ( this.isView ) {\n        return;\n      }\n\n      this.$emit('update:value', this.rows);\n    }\n  },\n};\n</script>\n\n<template>\n  <div>\n    <div v-if=\"rows.length\">\n      <div\n        class=\"ports-headers\"\n        :class=\"{'show-protocol':showProtocol, 'show-node-port':showNodePort}\"\n      >\n        <span\n          v-if=\"padLeft\"\n          class=\"left\"\n        />\n        <span class=\"port-name\">\n          <t k=\"servicePorts.rules.name.label\" />\n        </span>\n        <span class=\"port\">\n          <t k=\"servicePorts.rules.listening.label\" />\n          <i\n            v-clean-tooltip=\"t('servicesPage.listeningPorts')\"\n            class=\"icon icon-info flex\"\n          />\n          <span class=\"text-error\">*</span>\n        </span>\n        <span\n          v-if=\"showProtocol\"\n          class=\"port-protocol\"\n        >\n          <t k=\"servicePorts.rules.protocol.label\" />\n        </span>\n        <span class=\"target-port\">\n          <t k=\"servicePorts.rules.target.label\" />\n          <i\n            v-clean-tooltip=\"t('servicesPage.targetPorts')\"\n            class=\"icon icon-info flex\"\n          />\n          <span class=\"text-error\">*</span>\n\n        </span>\n        <span\n          v-if=\"showNodePort\"\n          class=\"node-port\"\n        >\n          <t k=\"servicePorts.rules.node.label\" />\n        </span>\n        <span\n          v-if=\"showRemove\"\n          class=\"remove\"\n        />\n      </div>\n      <div\n        v-for=\"(row, idx) in rows\"\n        :key=\"idx\"\n        class=\"ports-row\"\n        :class=\"{'show-protocol':showProtocol, 'show-node-port':showNodePort}\"\n      >\n        <div\n          v-if=\"padLeft\"\n          class=\"left\"\n        />\n        <div class=\"port-name\">\n          <span v-if=\"isView\">{{ row.name }}</span>\n          <input\n            v-else\n            ref=\"port-name\"\n            v-model.number=\"row.name\"\n            type=\"text\"\n            :placeholder=\"t('servicePorts.rules.name.placeholder')\"\n            @input=\"queueUpdate\"\n          >\n        </div>\n        <div class=\"port\">\n          <span v-if=\"isView\">{{ row.port }}</span>\n          <input\n            v-else\n            ref=\"port\"\n            v-model.number=\"row.port\"\n            type=\"number\"\n            min=\"1\"\n            max=\"65535\"\n            :placeholder=\"t('servicePorts.rules.listening.placeholder')\"\n            @input=\"queueUpdate\"\n          >\n        </div>\n        <div\n          v-if=\"showProtocol\"\n          class=\"port-protocol\"\n        >\n          <span v-if=\"isView\">{{ row.protocol }}</span>\n          <Select\n            v-else\n            v-model:value=\"row.protocol\"\n            :options=\"protocolOptions\"\n            @update:value=\"queueUpdate\"\n          />\n        </div>\n        <div class=\"target-port\">\n          <span v-if=\"isView\">{{ row.targetPort }}</span>\n          <input\n            v-else\n            v-model=\"row.targetPort\"\n            :placeholder=\"t('servicePorts.rules.target.placeholder')\"\n            @input=\"queueUpdate\"\n          >\n        </div>\n        <div\n          v-if=\"showNodePort\"\n          class=\"node-port\"\n        >\n          <span v-if=\"isView\">{{ row.nodePort }}</span>\n          <input\n            v-else\n            v-model.number=\"row.nodePort\"\n            type=\"number\"\n            min=\"1\"\n            max=\"65535\"\n            :placeholder=\"t('servicePorts.rules.node.placeholder')\"\n            @input=\"queueUpdate\"\n          >\n        </div>\n        <div\n          v-if=\"showRemove\"\n          class=\"remove\"\n        >\n          <button\n            type=\"button\"\n            class=\"btn role-link\"\n            @click=\"remove(idx)\"\n          >\n            <t k=\"generic.remove\" />\n          </button>\n        </div>\n        <Error\n          :value=\"{...row, idx}\"\n          :rules=\"rules\"\n        />\n      </div>\n    </div>\n    <div\n      v-if=\"showAdd\"\n      class=\"footer\"\n    >\n      <button\n        type=\"button\"\n        class=\"btn role-tertiary add\"\n        @click=\"add()\"\n      >\n        <t k=\"generic.add\" />\n      </button>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n $remove: 75;\n  $checkbox: 75;\n\n  .title {\n    margin-bottom: 10px;\n\n    .read-from-file {\n      float: right;\n    }\n  }\n\n  .ports-headers, .ports-row{\n    display: grid;\n    grid-column-gap: $column-gutter;\n    margin-bottom: 10px;\n    align-items: center;\n\n    &.show-protocol{\n      grid-template-columns: 23% 23% 10% 15% 15% 10%;\n      &:not(.show-node-port){\n        grid-template-columns: 31% 31% 10% 15% 10%;\n      }\n    }\n    &.show-node-port:not(.show-protocol){\n      grid-template-columns: 28% 28% 15% 15% 10%;\n    }\n  }\n\n  .ports-headers {\n    color: var(--input-label);\n  }\n\n  .toggle-host-ports {\n    color: var(--primary);\n  }\n\n  .remove BUTTON {\n    padding: 0px;\n  }\n\n  .ports-row {\n    > div {\n      height: 100%;\n    }\n\n    .port-protocol :deep() {\n      .unlabeled-select {\n        .v-select.inline {\n          margin-top: 2px;\n        }\n      }\n    }\n  }\n\n  .footer {\n    margin-top: 10px;\n    margin-left: 5px;\n\n    .protip {\n      float: right;\n      padding: 5px 0;\n    }\n  }\n</style>\n"]}]}