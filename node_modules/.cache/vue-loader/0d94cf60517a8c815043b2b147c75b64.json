{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SimpleSecretSelector.vue?vue&type=style&index=0&id=6f0b37b9&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SimpleSecretSelector.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5zZWNyZXQtc2VsZWN0b3IgewogIHdpZHRoOiAxMDAlOwogIGxhYmVsIHsKICAgIGRpc3BsYXk6IGJsb2NrOwogIH0KCiAgJiAubGFiZWxlZC1zZWxlY3QgewogICAgbWluLWhlaWdodDogJGlucHV0LWhlaWdodDsKICB9CgogICYgLnZzX19zZWxlY3RlZC1vcHRpb25zIHsKICAgIHBhZGRpbmc6IDhweCAwIDdweCAwOwogIH0KCiAgJiBsYWJlbCB7CiAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7CiAgfQoKICAmLnNob3cta2V5LXNlbGVjdG9yIHsKICAgIC5pbnB1dC1jb250YWluZXIgPiAqIHsKICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrOwogICAgICB3aWR0aDogNTAlOwoKICAgICAgJi5sYWJlbGVkLXNlbGVjdC5mb2N1c2VkIHsKICAgICAgICB6LWluZGV4OiAxMDsKICAgICAgfQoKICAgICAgJjpmaXJzdC1jaGlsZCB7CiAgICAgICAgYm9yZGVyLXRvcC1yaWdodC1yYWRpdXM6IDA7CiAgICAgICAgYm9yZGVyLWJvdHRvbS1yaWdodC1yYWRpdXM6IDA7CiAgICAgICAgbWFyZ2luLXJpZ2h0OiAwOwogICAgICB9CgogICAgICAmOmxhc3QtY2hpbGQgewogICAgICAgIGJvcmRlci10b3AtbGVmdC1yYWRpdXM6IDA7CiAgICAgICAgYm9yZGVyLWJvdHRvbS1sZWZ0LXJhZGl1czogMDsKICAgICAgICBib3JkZXItbGVmdDogbm9uZTsKICAgICAgICBmbG9hdDogcmlnaHQ7CiAgICAgIH0KICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SimpleSecretSelector.vue"], "names": [], "mappings": ";AAuNA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B;;EAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC;EACtB;;EAEA,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACb;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACjB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd;IACF;EACF;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SimpleSecretSelector.vue", "sourceRoot": "", "sourcesContent": ["<script>\n/**\n * I created this component because the regular secret\n * selector assumes secrets need to be in this format:\n *\n *  valueFrom:\n      secretKeyRef:\n        name: example-secret-name\n        key: example-secret-key\n\n   But for secrets for receivers in AlertmanagerConfigs,\n   it needed to be in this format:\n\n   name: example-secret-name\n   key: example-secret-key\n\n   FIXME: The solution to above would have been to have a configurable path to set/get name and key from.\n   This would have avoided a lot of copy and paste\n */\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport ResourceLabeledSelect from '@shell/components/form/ResourceLabeledSelect';\nimport { SECRET } from '@shell/config/types';\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\nimport { TYPES } from '@shell/models/secret';\nimport { LABEL_SELECT_KINDS } from '@shell/types/components/labeledSelect';\nimport { PaginationParamFilter } from '@shell/types/store/pagination.types';\n\nconst NONE = '__[[NONE]]__';\n\nexport default {\n  emits: ['updateSecretName', 'updateSecretKey'],\n\n  components: { LabeledSelect, ResourceLabeledSelect },\n\n  props: {\n    test:        { type: String, default: '' },\n    initialName: {\n      type:     String,\n      required: true\n    },\n    initialKey: {\n      type:     String,\n      required: true\n    },\n    namespace: {\n      type:     String,\n      required: true\n    },\n    types: {\n      type:    Array,\n      default: () => Object.values(TYPES)\n    },\n    disabled: {\n      type:    Boolean,\n      default: false\n    },\n    secretNameLabel: {\n      type:    String,\n      default: 'Secret Name'\n    },\n    keyNameLabel: {\n      type:    String,\n      default: 'Key'\n    },\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n  },\n\n  data(props) {\n    return {\n      secrets:            [],\n      name:               props.initialName,\n      key:                props.initialKey,\n      none:               NONE,\n      SECRET,\n      allSecretsSettings: {\n        mapResult: (secrets) => {\n          const allSecretsInNamespace = secrets.filter((secret) => this.types.includes(secret._type) && secret.namespace === this.namespace);\n          const mappedSecrets = this.mapSecrets(allSecretsInNamespace.sort((a, b) => a.name.localeCompare(b.name)));\n\n          this.secrets = allSecretsInNamespace; // We need the key from the selected secret\n\n          return mappedSecrets;\n        }\n      },\n      paginateSecretsSetting: {\n        requestSettings: this.paginatePageOptions,\n        mapResult:       (secrets) => {\n          const mappedSecrets = this.mapSecrets(secrets);\n\n          this.secrets = secrets; // We need the key from the selected secret. When paginating we won't touch the store, so just pass back here\n\n          return mappedSecrets;\n        }\n      }\n    };\n  },\n\n  computed: {\n    keys() {\n      const secret = (this.secrets || []).find((secret) => secret.name === this.name) || {};\n\n      return Object.keys(secret.data || {}).map((key) => ({\n        label: key,\n        value: key\n      }));\n    },\n    isView() {\n      return this.mode === _VIEW;\n    },\n    isKeyDisabled() {\n      return !this.isView && (!this.name || this.name === NONE || this.disabled);\n    }\n  },\n\n  methods: {\n    /**\n     * Provide a set of options for the LabelSelect ([none, ...{label, value}])\n     */\n    mapSecrets(secrets) {\n      const mappedSecrets = secrets\n        .reduce((res, s) => {\n          if (s.kind === LABEL_SELECT_KINDS.NONE) {\n            return res;\n          }\n\n          if (s.id) {\n            res.push({ label: s.name, value: s.name });\n          } else {\n            res.push(s);\n          }\n\n          return res;\n        }, []);\n\n      return [\n        {\n          label: 'None', value: NONE, kind: LABEL_SELECT_KINDS.NONE\n        },\n        ...mappedSecrets\n      ];\n    },\n\n    /**\n     * @param [LabelSelectPaginationFunctionOptions] opts\n     * @returns LabelSelectPaginationFunctionOptions\n     */\n    paginatePageOptions(opts) {\n      const { opts: { filter } } = opts;\n\n      const filters = !!filter ? [PaginationParamFilter.createSingleField({ field: 'metadata.name', value: filter })] : [];\n\n      filters.push(\n        PaginationParamFilter.createSingleField({ field: 'metadata.namespace', value: this.namespace }),\n        PaginationParamFilter.createSingleField({ field: 'metadata.fields.1', value: this.types.join(',') })\n      );\n\n      return {\n        ...opts,\n        filters,\n        groupByNamespace: false,\n        classify:         true,\n        sort:             [{ asc: true, field: 'metadata.name' }],\n      };\n    },\n\n    updateSecretName(e) {\n      if (e.value === this.none) {\n        // The key should appear blank if the secret name is cleared\n        this.key = '';\n      }\n      if (e.value) {\n        this.$emit('updateSecretName', e.value);\n      }\n    },\n    updateSecretKey(e) {\n      if (e.value) {\n        this.$emit('updateSecretKey', e.value);\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <div class=\"secret-selector show-key-selector\">\n    <div class=\"input-container\">\n      <ResourceLabeledSelect\n        v-model:value=\"name\"\n        class=\"col span-6\"\n        :disabled=\"!isView && disabled\"\n        :loading=\"$fetchState.pending\"\n        :label=\"secretNameLabel\"\n        :mode=\"mode\"\n        :resource-type=\"SECRET\"\n        :paginated-resource-settings=\"paginateSecretsSetting\"\n        :all-resources-settings=\"allSecretsSettings\"\n        @selecting=\"updateSecretName\"\n      />\n      <LabeledSelect\n        v-model:value=\"key\"\n        class=\"col span-6\"\n        :disabled=\"isKeyDisabled\"\n        :options=\"keys\"\n        :label=\"keyNameLabel\"\n        :mode=\"mode\"\n        @selecting=\"updateSecretKey\"\n      />\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\">\n.secret-selector {\n  width: 100%;\n  label {\n    display: block;\n  }\n\n  & .labeled-select {\n    min-height: $input-height;\n  }\n\n  & .vs__selected-options {\n    padding: 8px 0 7px 0;\n  }\n\n  & label {\n    display: inline-block;\n  }\n\n  &.show-key-selector {\n    .input-container > * {\n      display: inline-block;\n      width: 50%;\n\n      &.labeled-select.focused {\n        z-index: 10;\n      }\n\n      &:first-child {\n        border-top-right-radius: 0;\n        border-bottom-right-radius: 0;\n        margin-right: 0;\n      }\n\n      &:last-child {\n        border-top-left-radius: 0;\n        border-bottom-left-radius: 0;\n        border-left: none;\n        float: right;\n      }\n    }\n  }\n}\n</style>\n"]}]}