{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/LogItem.vue?vue&type=template&id=845e8778&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/LogItem.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdiBjbGFzcz0ibGluZSI+CiAgICA8c3BhbiBjbGFzcz0idGltZSI+e3sgZm9ybWF0KHNvdXJjZS50aW1lKSB9fTwvc3Bhbj4KICAgIDxzcGFuCiAgICAgIHYtY2xlYW4taHRtbD0ic291cmNlLm1zZyIKICAgICAgY2xhc3M9Im1zZyIKICAgIC8+CiAgPC9kaXY+Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/LogItem.vue"], "names": [], "mappings": ";EAmCE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC,CAAC;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,<PERSON>AC,<PERSON>AC,CAAC;IACZ,CAAC;EACH,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/LogItem.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport day from 'dayjs';\nimport { DATE_FORMAT, TIME_FORMAT } from '@shell/store/prefs';\nimport { escapeHtml } from '@shell/utils/string';\n\nexport default {\n  props: {\n    source: {\n      type:    Object,\n      default: () => {}\n    }\n  },\n\n  computed: {\n    timeFormatStr() {\n      const dateFormat = escapeHtml( this.$store.getters['prefs/get'](DATE_FORMAT));\n      const timeFormat = escapeHtml( this.$store.getters['prefs/get'](TIME_FORMAT));\n\n      return `${ dateFormat } ${ timeFormat }`;\n    },\n  },\n\n  methods: {\n    format(time) {\n      if ( !time ) {\n        return '';\n      }\n\n      return day(time).format(this.timeFormatStr);\n    }\n  }\n};\n</script>\n\n<template>\n  <div class=\"line\">\n    <span class=\"time\">{{ format(source.time) }}</span>\n    <span\n      v-clean-html=\"source.msg\"\n      class=\"msg\"\n    />\n  </div>\n</template>\n\n<style lang='scss' scoped>\n.line {\n  font-family: <PERSON><PERSON>,Consolas,monospace;\n  color: var(--logs-text);\n  display:flex;\n}\n\n.time {\n  white-space: nowrap;\n  display: none;\n  width: 0;\n  padding-right: 15px;\n  user-select: none;\n}\n\n.msg {\n  white-space: pre;\n\n  .highlight {\n    color: var(--logs-highlight);\n    background-color: var(--logs-highlight-bg);\n  }\n}\n\n</style>\n"]}]}