{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ClusterBadge.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ClusterBadge.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmV4cG9ydCBkZWZhdWx0IHsKICBwcm9wczogewogICAgY2x1c3RlcjogewogICAgICB0eXBlOiAgICAgT2JqZWN0LAogICAgICByZXF1aXJlZDogdHJ1ZSwKICAgIH0sCiAgfSwKICBjb21wdXRlZDogewogICAgaGFzQmFkZ2UoKSB7CiAgICAgIHJldHVybiAhIXRoaXMuY2x1c3Rlcj8uYmFkZ2U/LnRleHQ7CiAgICB9LAogICAgc2hvd0JvcmRlcnMoKSB7CiAgICAgIHJldHVybiB0aGlzLmNsdXN0ZXI/LmJhZGdlPy5jb2xvciA9PT0gJ3RyYW5zcGFyZW50JzsKICAgIH0sCiAgfQp9Owo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ClusterBadge.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC;EACH;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ClusterBadge.vue", "sourceRoot": "", "sourcesContent": ["<script>\nexport default {\n  props: {\n    cluster: {\n      type:     Object,\n      required: true,\n    },\n  },\n  computed: {\n    hasBadge() {\n      return !!this.cluster?.badge?.text;\n    },\n    showBorders() {\n      return this.cluster?.badge?.color === 'transparent';\n    },\n  }\n};\n</script>\n\n<template>\n  <div\n    v-if=\"hasBadge\"\n    :style=\"{ backgroundColor: cluster.badge.color, color: cluster.badge.textColor }\"\n    class=\"cluster-badge\"\n    :class=\"{'cluster-badge-border': showBorders}\"\n    :aria-label=\"t('clusterBadge.clusterComment', { text: cluster.badge?.text || '' })\"\n  >\n    {{ cluster.badge.text }}\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .cluster-badge {\n    cursor: default;\n    border-radius: 10px;\n    font-size: 12px;\n    padding: 2px 10px;\n    max-width: 250px;\n    text-overflow: ellipsis;\n    overflow: hidden;\n\n     &-border {\n      border: 1px solid var(--border);\n      border-radius: 5px;\n      color: var(--body-text) !important; // !important is needed to override the color set by the badge when there's a transparent background.\n    }\n  }\n</style>\n"]}]}