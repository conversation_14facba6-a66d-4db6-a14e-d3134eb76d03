{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/MoveModal.vue?vue&type=style&index=0&id=41b4bd2e&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/MoveModal.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIC5tb3ZlLW1vZGFsIHsNCiAgICAubmFtZXNwYWNlcyB7DQogICAgICBtYXgtaGVpZ2h0OiAyMDBweDsNCiAgICAgIG92ZXJmbG93LXk6IHNjcm9sbDsNCiAgICB9DQoNCiAgICAubW92ZS1tb2RhbC1jYXJkIHsNCiAgICAgICAgYm94LXNoYWRvdzogbm9uZTsNCg0KICAgICAgICBib3JkZXItcmFkaXVzOiB2YXIoLS1ib3JkZXItcmFkaXVzKTsNCiAgICB9DQoNCiAgICAuYWN0aW9ucyB7DQogICAgICB0ZXh0LWFsaWduOiByaWdodDsNCiAgICB9DQogICAgLmNhcmQtYWN0aW9ucyB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogICAgfQ0KICB9DQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/MoveModal.vue"], "names": [], "mappings": ";EAiJE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/MoveModal.vue", "sourceRoot": "", "sourcesContent": ["<script>\r\nimport { mapState, mapGetters } from 'vuex';\r\nimport { Card } from '@components/Card';\r\nimport AsyncButton from '@shell/components/AsyncButton';\r\nimport AppModal from '@shell/components/AppModal.vue';\r\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\r\nimport { MANAGEMENT } from '@shell/config/types';\r\nimport Loading from '@shell/components/Loading';\r\nimport { PROJECT } from '@shell/config/labels-annotations';\r\n\r\nexport default {\r\n  emits: ['moving'],\r\n\r\n  components: {\r\n    AsyncButton, Card, LabeledSelect, Loading, AppModal\r\n  },\r\n\r\n  async fetch() {\r\n    this.projects = await this.$store.dispatch('management/findAll', { type: MANAGEMENT.PROJECT });\r\n  },\r\n\r\n  data() {\r\n    return {\r\n      modalName: 'move-modal', projects: [], targetProject: null, showModal: false\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    ...mapState('action-menu', ['showPromptMove', 'toMove']),\r\n    ...mapGetters(['currentCluster']),\r\n\r\n    excludedProjects() {\r\n      return this.toMove.filter((namespace) => !!namespace.project).map((namespace) => namespace.project.shortId);\r\n    },\r\n\r\n    projectOptions() {\r\n      return this.projects.reduce((inCluster, project) => {\r\n        if (!this.excludedProjects.includes(project.shortId) && project.spec?.clusterName === this.currentCluster.id) {\r\n          inCluster.push({\r\n            value: project.shortId,\r\n            label: project.nameDisplay\r\n          });\r\n        }\r\n\r\n        return inCluster;\r\n      }, []);\r\n    }\r\n  },\r\n\r\n  watch: {\r\n    showPromptMove(show) {\r\n      if (show) {\r\n        this.showModal = true;\r\n      } else {\r\n        this.showModal = false;\r\n      }\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    close() {\r\n      this.$store.commit('action-menu/togglePromptMove');\r\n    },\r\n\r\n    async move(finish) {\r\n      const cluster = this.$store.getters['currentCluster'];\r\n      const clusterWithProjectId = `${ cluster.id }:${ this.targetProject }`;\r\n\r\n      const promises = this.toMove.map((namespace) => {\r\n        namespace.setLabel(PROJECT, this.targetProject);\r\n        namespace.setAnnotation(PROJECT, clusterWithProjectId);\r\n\r\n        return namespace.save();\r\n      });\r\n\r\n      try {\r\n        this.$emit('moving');\r\n        await Promise.all(promises);\r\n        finish(true);\r\n        this.targetProject = null;\r\n        this.close();\r\n      } catch (ex) {\r\n        finish(false);\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n<template>\r\n  <app-modal\r\n    v-if=\"showModal\"\r\n    class=\"move-modal\"\r\n    :name=\"modalName\"\r\n    :width=\"440\"\r\n    height=\"auto\"\r\n    @close=\"close\"\r\n  >\r\n    <Loading v-if=\"$fetchState.pending\" />\r\n    <Card\r\n      v-else\r\n      class=\"move-modal-card\"\r\n      :show-highlight-border=\"false\"\r\n    >\r\n      <template #title>\r\n        <h4 class=\"text-default-text\">\r\n          {{ t('moveModal.title') }}\r\n        </h4>\r\n      </template>\r\n      <template #body>\r\n        <div>\r\n          {{ t('moveModal.description') }}\r\n          <ul class=\"namespaces\">\r\n            <li\r\n              v-for=\"(namespace, i) in toMove\"\r\n              :key=\"i\"\r\n            >\r\n              {{ namespace.nameDisplay }}\r\n            </li>\r\n          </ul>\r\n        </div>\r\n        <LabeledSelect\r\n          v-model:value=\"targetProject\"\r\n          :options=\"projectOptions\"\r\n          :label=\"t('moveModal.targetProject')\"\r\n        />\r\n      </template>\r\n      <template #actions>\r\n        <button\r\n          class=\"btn role-secondary\"\r\n          @click=\"close\"\r\n        >\r\n          {{ t('generic.cancel') }}\r\n        </button>\r\n        <AsyncButton\r\n          :action-label=\"t('moveModal.moveButtonLabel')\"\r\n          class=\"btn bg-primary ml-10\"\r\n          :disabled=\"!targetProject\"\r\n          @click=\"move\"\r\n        />\r\n      </template>\r\n    </Card>\r\n  </app-modal>\r\n</template>\r\n\r\n<style lang='scss'>\r\n  .move-modal {\r\n    .namespaces {\r\n      max-height: 200px;\r\n      overflow-y: scroll;\r\n    }\r\n\r\n    .move-modal-card {\r\n        box-shadow: none;\r\n\r\n        border-radius: var(--border-radius);\r\n    }\r\n\r\n    .actions {\r\n      text-align: right;\r\n    }\r\n    .card-actions {\r\n      display: flex;\r\n      justify-content: center;\r\n    }\r\n  }\r\n</style>\r\n"]}]}