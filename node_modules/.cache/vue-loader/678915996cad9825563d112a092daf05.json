{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/BannerGraphic.vue?vue&type=style&index=0&id=3a3a97a9&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/BannerGraphic.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgJGJhbm5lci1oZWlnaHQ6IDI0MHB4OwogICRiYW5uZXItaGVpZ2h0LXNtYWxsOiAyMDBweDsKCiAgLmJhbm5lci1ncmFwaGljIHsKICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsKCiAgICAuZ3JhcGhpYyB7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICAgIGhlaWdodDogJGJhbm5lci1oZWlnaHQ7CiAgICAgIG92ZXJmbG93OiBoaWRkZW47CiAgICAgID4gaW1nLmJhbm5lciB7CiAgICAgICAgZmxleDogMTsKICAgICAgICBvYmplY3QtZml0OiBjb3ZlcjsKICAgICAgfQogICAgfQogICAgLnRpdGxlIHsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgICAgdGV4dC1hbGlnbjogY2VudGVyOwogICAgICB0b3A6IDA7CiAgICAgIGhlaWdodDogMTAwJTsKICAgICAgd2lkdGg6IDEwMCU7CiAgICAgIG1hcmdpbi10b3A6IC0yMHB4OwogICAgfQogICAgJi5zbWFsbCB7CiAgICAgIC5ncmFwaGljIHsKICAgICAgICBoZWlnaHQ6ICRiYW5uZXItaGVpZ2h0LXNtYWxsOwogICAgICAgIGltZy5iYW5uZXIgewogICAgICAgICAgbWFyZ2luLXRvcDogbWF0aC5kaXYoKCRiYW5uZXItaGVpZ2h0LXNtYWxsIC0gJGJhbm5lci1oZWlnaHQpLCAyKTsKICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/BannerGraphic.vue"], "names": [], "mappings": ";EAyDE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB;IACF;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,EAAE,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAClE;MACF;IACF;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/BannerGraphic.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport Closeable from '@shell/mixins/closeable';\nimport BrandImage from '@shell/components/BrandImage';\n\nexport default {\n  components: { BrandImage },\n  mixins:     [Closeable],\n\n  props: {\n    title: {\n      type:    String,\n      default: null,\n    },\n    titleKey: {\n      type:    String,\n      default: null,\n    },\n\n    small: {\n      type:    Boolean,\n      default: false\n    }\n  },\n};\n</script>\n\n<template>\n  <div\n    v-if=\"shown\"\n    class=\"banner-graphic\"\n    :class=\"{'small': small}\"\n  >\n    <div class=\"graphic\">\n      <BrandImage\n        class=\"banner\"\n        data-testid=\"banner-brand__img\"\n        file-name=\"banner.svg\"\n        :draggable=\"false\"\n      />\n    </div>\n    <div\n      v-if=\"titleKey\"\n      data-testid=\"banner-title-key\"\n      class=\"title\"\n    >\n      <t :k=\"titleKey\" />\n    </div>\n    <h1\n      v-else-if=\"title\"\n      v-clean-html=\"title\"\n      data-testid=\"banner-title\"\n      class=\"title\"\n    />\n  </div>\n</template>\n\n<style lang=\"scss\">\n  $banner-height: 240px;\n  $banner-height-small: 200px;\n\n  .banner-graphic {\n    position: relative;\n\n    .graphic {\n      display: flex;\n      flex-direction: column;\n      height: $banner-height;\n      overflow: hidden;\n      > img.banner {\n        flex: 1;\n        object-fit: cover;\n      }\n    }\n    .title {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      position: absolute;\n      text-align: center;\n      top: 0;\n      height: 100%;\n      width: 100%;\n      margin-top: -20px;\n    }\n    &.small {\n      .graphic {\n        height: $banner-height-small;\n        img.banner {\n          margin-top: math.div(($banner-height-small - $banner-height), 2);\n        }\n      }\n    }\n  }\n</style>\n"]}]}