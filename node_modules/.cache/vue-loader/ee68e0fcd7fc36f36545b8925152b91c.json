{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/AdvancedSection.vue?vue&type=template&id=34adb530", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/AdvancedSection.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdiBjbGFzcz0ibXQtMjAiPgogICAgPGEKICAgICAgdi10PSJzaG93ID8gJ2dlbmVyaWMuaGlkZUFkdmFuY2VkJyA6ICdnZW5lcmljLnNob3dBZHZhbmNlZCciCiAgICAgIGNsYXNzPSJoYW5kIGJsb2NrIgogICAgICA6Y2xhc3M9InsnbWItMTAnOiBzaG93fSIKICAgICAgQGNsaWNrPSJ0b2dnbGUiCiAgICAvPgoKICAgIDxzbG90IHYtaWY9InNob3ciIC8+CiAgPC9kaXY+Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/AdvancedSection.vue"], "names": [], "mappings": ";EA6BE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;MACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/AdvancedSection.vue", "sourceRoot": "", "sourcesContent": ["<script>\nexport default {\n  props: {\n    mode: {\n      type:     String,\n      required: true,\n    },\n    isOpenByDefault: {\n      // It may be useful to keep the advanced options open\n      // if the form is in edit mode and it has non-default\n      // advanced options configured.\n      type:    <PERSON>olean,\n      default: false\n    }\n  },\n\n  data(props) {\n    return { show: props.isOpenByDefault };\n  },\n\n  methods: {\n    toggle() {\n      this.show = !this.show;\n    },\n  }\n};\n</script>\n\n<template>\n  <div class=\"mt-20\">\n    <a\n      v-t=\"show ? 'generic.hideAdvanced' : 'generic.showAdvanced'\"\n      class=\"hand block\"\n      :class=\"{'mb-10': show}\"\n      @click=\"toggle\"\n    />\n\n    <slot v-if=\"show\" />\n  </div>\n</template>\n"]}]}