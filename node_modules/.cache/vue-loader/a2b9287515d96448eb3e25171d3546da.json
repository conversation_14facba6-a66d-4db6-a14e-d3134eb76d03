{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/StatusTable.vue?vue&type=template&id=3bbee1c9", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/StatusTable.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxTb3J0YWJsZVRhYmxlDQogICAgOnJvd3M9InN0YXR1c1Jvd3MiDQogICAgOmhlYWRlcnM9InN0YXR1c0hlYWRlcnMiDQogICAgOnRhYmxlLWFjdGlvbnM9ImZhbHNlIg0KICAgIDpyb3ctYWN0aW9ucz0iZmFsc2UiDQogICAga2V5LWZpZWxkPSJrZXkiDQogICAgZGVmYXVsdC1zb3J0LWJ5PSJzdGF0ZSINCiAgLz4NCg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/StatusTable.vue"], "names": [], "mappings": ";EAoDE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/StatusTable.vue", "sourceRoot": "", "sourcesContent": ["<script>\r\nimport {\r\n  LAST_UPDATED, TYPE, REASON, MESSAGE, STATUS\r\n} from '@shell/config/table-headers';\r\nimport SortableTable from '@shell/components/SortableTable';\r\nimport { copyTextToClipboard } from '@shell/utils/clipboard';\r\nimport { exceptionToErrorsArray } from '@shell/utils/error';\r\nexport default {\r\n  emits: ['error'],\r\n\r\n  components: { SortableTable },\r\n  props:      {\r\n    resource: {\r\n      type:     Object,\r\n      required: true\r\n    }\r\n  },\r\n  data() {\r\n    const statusHeaders = [\r\n      TYPE,\r\n      STATUS,\r\n      LAST_UPDATED,\r\n      REASON,\r\n      MESSAGE\r\n    ];\r\n\r\n    return {\r\n      statusRows: this.resource.status.conditions || [],\r\n      statusHeaders\r\n    };\r\n  },\r\n\r\n  methods: {\r\n    clicked($event) {\r\n      $event.stopPropagation();\r\n      $event.preventDefault();\r\n\r\n      copyTextToClipboard(this.$slots.default()[0].text).then(() => {\r\n        this.copied = true;\r\n\r\n        setTimeout(() => {\r\n          this.copied = false;\r\n        }, 2000);\r\n      }).catch((e) => {\r\n        this.$emit('error', exceptionToErrorsArray(e));\r\n      });\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<template>\r\n  <SortableTable\r\n    :rows=\"statusRows\"\r\n    :headers=\"statusHeaders\"\r\n    :table-actions=\"false\"\r\n    :row-actions=\"false\"\r\n    key-field=\"key\"\r\n    default-sort-by=\"state\"\r\n  />\r\n</template>\r\n"]}]}