{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/LabeledTooltip/LabeledTooltip.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/LabeledTooltip/LabeledTooltip.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCB7IGRlZmluZUNvbXBvbmVudCB9IGZyb20gJ3Z1ZSc7CgpleHBvcnQgZGVmYXVsdCBkZWZpbmVDb21wb25lbnQoewogIHByb3BzOiB7CiAgICAvKioKICAgICAqIFRoZSBMYWJlbGVkIFRvb2x0aXAgdmFsdWUuCiAgICAgKi8KICAgIHZhbHVlOiB7CiAgICAgIHR5cGU6ICAgIFtTdHJpbmcsIE9iamVjdF0sCiAgICAgIGRlZmF1bHQ6IG51bGwKICAgIH0sCgogICAgLyoqCiAgICAgKiBUaGUgc3RhdHVzIGZvciB0aGUgTGFiZWxlZCBUb29sdGlwLiBDb250cm9scyB0aGUgTGFiZWxlZCBUb29sdGlwIGNsYXNzLgogICAgICogQHZhbHVlcyBpbmZvLCBzdWNjZXNzLCB3YXJuaW5nLCBlcnJvcgogICAgICovCiAgICBzdGF0dXM6IHsKICAgICAgdHlwZTogICAgU3RyaW5nLAogICAgICBkZWZhdWx0OiAnZXJyb3InCiAgICB9LAoKICAgIC8qKgogICAgICogRGlzcGxheXMgdGhlIExhYmVsZWQgVG9vbHRpcCBvbiBtb3VzZSBob3Zlci4KICAgICAqLwogICAgaG92ZXI6IHsKICAgICAgdHlwZTogICAgQm9vbGVhbiwKICAgICAgZGVmYXVsdDogdHJ1ZQogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGljb25DbGFzcygpOiBzdHJpbmcgewogICAgICByZXR1cm4gdGhpcy5zdGF0dXMgPT09ICdlcnJvcicgPyAnaWNvbi13YXJuaW5nJyA6ICdpY29uLWluZm8nOwogICAgfSwKICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tZXhwbGljaXQtYW55CiAgICB0b29sdGlwQ29udGVudCgpOiB7W2tleTogc3RyaW5nXTogYW55fSB8IHN0cmluZyB7CiAgICAgIGlmICh0aGlzLmlzT2JqZWN0KHRoaXMudmFsdWUpKSB7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIC4uLnsgY29udGVudDogdGhpcy52YWx1ZS5jb250ZW50LCBwb3BwZXJDbGFzczogW2B0b29sdGlwLSR7IHN0YXR1cyB9YF0gfSwgLi4udGhpcy52YWx1ZSwgdHJpZ2dlcnM6IFsnaG92ZXInLCAndG91Y2gnLCAnZm9jdXMnXQogICAgICAgIH07CiAgICAgIH0KCiAgICAgIHJldHVybiB0aGlzLnZhbHVlID8geyBjb250ZW50OiB0aGlzLnZhbHVlLCB0cmlnZ2VyczogWydob3ZlcicsICd0b3VjaCcsICdmb2N1cyddIH0gOiAnJzsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIGlzT2JqZWN0KHZhbHVlOiBzdHJpbmcgfCBSZWNvcmQ8c3RyaW5nLCB1bmtub3duPik6IHZhbHVlIGlzIFJlY29yZDxzdHJpbmcsIHVua25vd24+IHsKICAgICAgcmV0dXJuIHR5cGVvZiB2YWx1ZSA9PT0gJ29iamVjdCcgJiYgdmFsdWUgIT09IG51bGwgJiYgISF2YWx1ZS5jb250ZW50OwogICAgfQogIH0KfSk7Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/LabeledTooltip/LabeledTooltip.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;AAErC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;KAC3B,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;KACxE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KACtC,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;KAC7C,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/D,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC9C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/H,CAAC;MACH;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACzF;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvE;EACF;AACF,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/LabeledTooltip/LabeledTooltip.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent } from 'vue';\n\nexport default defineComponent({\n  props: {\n    /**\n     * The Labeled Tooltip value.\n     */\n    value: {\n      type:    [String, Object],\n      default: null\n    },\n\n    /**\n     * The status for the Labeled Tooltip. Controls the Labeled Tooltip class.\n     * @values info, success, warning, error\n     */\n    status: {\n      type:    String,\n      default: 'error'\n    },\n\n    /**\n     * Displays the Labeled Tooltip on mouse hover.\n     */\n    hover: {\n      type:    Boolean,\n      default: true\n    }\n  },\n  computed: {\n    iconClass(): string {\n      return this.status === 'error' ? 'icon-warning' : 'icon-info';\n    },\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    tooltipContent(): {[key: string]: any} | string {\n      if (this.isObject(this.value)) {\n        return {\n          ...{ content: this.value.content, popperClass: [`tooltip-${ status }`] }, ...this.value, triggers: ['hover', 'touch', 'focus']\n        };\n      }\n\n      return this.value ? { content: this.value, triggers: ['hover', 'touch', 'focus'] } : '';\n    }\n  },\n  methods: {\n    isObject(value: string | Record<string, unknown>): value is Record<string, unknown> {\n      return typeof value === 'object' && value !== null && !!value.content;\n    }\n  }\n});\n</script>\n\n<template>\n  <div\n    ref=\"container\"\n    class=\"labeled-tooltip\"\n    :class=\"{[status]: true, hoverable: hover}\"\n  >\n    <template v-if=\"hover\">\n      <i\n        v-clean-tooltip=\"tooltipContent\"\n        v-stripped-aria-label=\"isObject(value) ? value.content : value\"\n        :class=\"{'hover':!value, [iconClass]: true}\"\n        class=\"icon status-icon\"\n        tabindex=\"0\"\n      />\n    </template>\n    <template v-else>\n      <i\n        :class=\"{'hover':!value}\"\n        class=\"icon status-icon\"\n      />\n      <div\n        v-if=\"value\"\n        class=\"tooltip\"\n        x-placement=\"bottom\"\n      >\n        <div class=\"tooltip-arrow\" />\n        <div class=\"tooltip-inner\">\n          {{ value }}\n        </div>\n      </div>\n    </template>\n  </div>\n</template>\n\n<style lang='scss'>\n.labeled-tooltip {\n    position: absolute;\n    width: 100%;\n    height: 100%;\n    left: 0;\n    top: 0;\n\n    &.hoverable {\n      height: 0%;\n    }\n\n     .status-icon {\n        position:  absolute;\n        right: 30px;\n        top: $input-padding-lg;\n        z-index: z-index(hoverOverContent);\n     }\n\n    @mixin tooltipColors($color) {\n        .status-icon {\n            color: $color;\n        }\n    }\n\n    &.error {\n        @include tooltipColors(var(--error));\n\n        .status-icon {\n          top: 7px;\n          right: 5px;\n        }\n    }\n\n    &.warning {\n        @include tooltipColors(var(--warning));\n    }\n\n    &.success {\n        @include tooltipColors(var(--success));\n    }\n}\n\n// Ensure code blocks inside tootips don't look awful\n.v-popper__popper.v-popper--theme-tooltip {\n  .v-popper__inner {\n    pre {\n      padding: 2px;\n      vertical-align: middle;\n    }\n  }\n}\n</style>\n"]}]}