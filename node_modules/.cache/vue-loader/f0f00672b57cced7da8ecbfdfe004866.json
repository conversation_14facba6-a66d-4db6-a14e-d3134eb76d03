{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DashboardOptions.vue?vue&type=template&id=40e0fc26&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DashboardOptions.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxkaXYgY2xhc3M9ImdyYXBoLW9wdGlvbnMiPg0KICAgIDxkaXYgdi1pZj0iaGFzU3VtbWFyeUFuZERldGFpbCI+DQogICAgICA8QnV0dG9uR3JvdXAKICAgICAgICB2LW1vZGVsOnZhbHVlPSJ2YWx1ZS50eXBlIgogICAgICAgIDpvcHRpb25zPSJkZXRhaWxTdW1tYXJ5T3B0aW9ucyIKICAgICAgLz4NCiAgICA8L2Rpdj4NCiAgICA8ZGl2IHYtZWxzZT4NCiAgICAgIDxkaXYgLz4NCiAgICA8L2Rpdj4NCiAgICA8ZGl2IGNsYXNzPSJyYW5nZS1yZWZyZXNoIj4NCiAgICAgIDxMYWJlbGVkU2VsZWN0CiAgICAgICAgdi1tb2RlbDp2YWx1ZT0idmFsdWUucmFuZ2UiCiAgICAgICAgOm9wdGlvbnM9InJhbmdlT3B0aW9ucyIKICAgICAgICA6bGFiZWw9InQoJ2dyYXBoT3B0aW9ucy5yYW5nZScpIgogICAgICAvPg0KICAgICAgPExhYmVsZWRTZWxlY3QKICAgICAgICB2LW1vZGVsOnZhbHVlPSJ2YWx1ZS5yZWZyZXNoUmF0ZSIKICAgICAgICA6b3B0aW9ucz0icmVmcmVzaE9wdGlvbnMiCiAgICAgICAgOmxhYmVsPSJ0KCdncmFwaE9wdGlvbnMucmVmcmVzaCcpIgogICAgICAvPg0KICAgIDwvZGl2Pg0KICA8L2Rpdj4NCg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DashboardOptions.vue"], "names": [], "mappings": ";EAuGE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,EAAE,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DashboardOptions.vue", "sourceRoot": "", "sourcesContent": ["<script>\r\nimport ButtonGroup from '@shell/components/ButtonGroup';\r\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\r\n\r\nexport default {\r\n  components: { ButtonGroup, LabeledSelect },\r\n  props:      {\r\n    value: {\r\n      type:     Object,\r\n      required: true,\r\n    },\r\n    hasSummaryAndDetail: {\r\n      type:    Boolean,\r\n      default: true,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      range:        null,\r\n      rangeOptions: [\r\n        {\r\n          label: this.t('generic.units.time.5m'),\r\n          value: '5m',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.1h'),\r\n          value: '1h',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.6h'),\r\n          value: '6h',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.1d'),\r\n          value: '1d',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.7d'),\r\n          value: '7d',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.30d'),\r\n          value: '30d',\r\n        },\r\n      ],\r\n      refreshOptions: [\r\n        {\r\n          label: this.t('generic.units.time.5s'),\r\n          value: '5s',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.10s'),\r\n          value: '10s',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.30s'),\r\n          value: '30s',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.1m'),\r\n          value: '1m',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.5m'),\r\n          value: '5m',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.15m'),\r\n          value: '15m',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.30m'),\r\n          value: '30m',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.1h'),\r\n          value: '1h',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.2h'),\r\n          value: '2h',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.1d'),\r\n          value: '1d',\r\n        }\r\n      ],\r\n      detailSummaryOptions: [\r\n        {\r\n          label: this.t('graphOptions.detail'),\r\n          value: 'detail'\r\n        },\r\n        {\r\n          label: this.t('graphOptions.summary'),\r\n          value: 'summary'\r\n        }\r\n      ]\r\n    };\r\n  }\r\n};\r\n</script>\r\n\r\n<template>\r\n  <div class=\"graph-options\">\r\n    <div v-if=\"hasSummaryAndDetail\">\r\n      <ButtonGroup\n        v-model:value=\"value.type\"\n        :options=\"detailSummaryOptions\"\n      />\r\n    </div>\r\n    <div v-else>\r\n      <div />\r\n    </div>\r\n    <div class=\"range-refresh\">\r\n      <LabeledSelect\n        v-model:value=\"value.range\"\n        :options=\"rangeOptions\"\n        :label=\"t('graphOptions.range')\"\n      />\r\n      <LabeledSelect\n        v-model:value=\"value.refreshRate\"\n        :options=\"refreshOptions\"\n        :label=\"t('graphOptions.refresh')\"\n      />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<style lang='scss' scoped>\r\n.graph-options {\r\n    &, .range-refresh {\r\n      display: flex;\r\n      flex-direction: row;\r\n      justify-content: flex-end;\r\n    }\r\n\r\n    & {\r\n      justify-content: space-between;\r\n      align-items: center;\r\n    }\r\n\r\n    .labeled-select {\r\n        width: 100px;\r\n        margin-left: 10px;\r\n    }\r\n}\r\n</style>\r\n"]}]}