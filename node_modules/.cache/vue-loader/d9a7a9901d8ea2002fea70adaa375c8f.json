{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Import.vue?vue&type=style&index=0&id=24491468&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Import.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgJG1pbjogNTB2aDsKICAkbWF4OiA1MHZoOwoKICAueWFtbC1lZGl0b3IgewogICAgZmxleDogMTsKICAgIG1pbi1oZWlnaHQ6ICRtaW47CiAgICBtYXgtaGVpZ2h0OiAkbWF4OwoKICAgIDpkZWVwKCkgLmNvZGUtbWlycm9yIHsKICAgICAgLkNvZGVNaXJyb3IgewogICAgICAgIHBvc2l0aW9uOiBpbml0aWFsOwogICAgICB9CgogICAgICAuQ29kZU1pcnJvciwKICAgICAgLkNvZGVNaXJyb3Itc2Nyb2xsLAogICAgICAuQ29kZU1pcnJvci1ndXR0ZXJzIHsKICAgICAgICBtaW4taGVpZ2h0OiAkbWluOwogICAgICAgIG1heC1oZWlnaHQ6ICRtYXg7CiAgICAgIH0KICAgIH0KICB9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Import.vue"], "names": [], "mappings": ";EA8OE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAClB;IACF;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Import.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapGetters } from 'vuex';\nimport { Card } from '@components/Card';\nimport { Banner } from '@components/Banner';\nimport Loading from '@shell/components/Loading';\nimport YamlEditor from '@shell/components/YamlEditor';\nimport FileSelector from '@shell/components/form/FileSelector';\nimport AsyncButton from '@shell/components/AsyncButton';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport SortableTable from '@shell/components/SortableTable';\nimport { sortBy } from '@shell/utils/sort';\nimport { exceptionToErrorsArray } from '@shell/utils/error';\nimport { NAMESPACE } from '@shell/config/types';\nimport { NAME as NAME_COL, TYPE, NAMESPACE as NAMESPACE_COL, AGE } from '@shell/config/table-headers';\n\nexport default {\n  emits: ['close', 'onReadyYamlEditor'],\n\n  components: {\n    AsyncButton,\n    Banner,\n    Card,\n    Loading,\n    YamlEditor,\n    FileSelector,\n    LabeledSelect,\n    SortableTable\n  },\n\n  props: {\n    defaultNamespace: {\n      type:    String,\n      default: 'default'\n    },\n  },\n\n  async fetch() {\n    this.allNamespaces = await this.$store.dispatch('cluster/findAll', { type: NAMESPACE, opt: { url: 'namespaces' } });\n  },\n\n  data() {\n    return {\n      currentYaml:   '',\n      allNamespaces: [],\n      errors:        null,\n      rows:          null,\n      done:          false,\n    };\n  },\n\n  computed: {\n    ...mapGetters(['currentCluster']),\n\n    namespaceOptions() {\n      const out = this.allNamespaces.map((obj) => {\n        return {\n          label: obj.name,\n          value: obj.name,\n        };\n      });\n\n      return sortBy(out, 'label');\n    },\n\n    headers() {\n      return [\n        TYPE,\n        NAME_COL,\n        NAMESPACE_COL,\n        AGE\n      ];\n    },\n  },\n\n  methods: {\n    close() {\n      this.$emit('close');\n    },\n\n    onFileSelected(value) {\n      const component = this.$refs.yamleditor;\n\n      if (component) {\n        this.errors = null;\n        component.updateValue(value);\n      }\n    },\n\n    async importYaml(btnCb) {\n      try {\n        this.errors = [];\n\n        const res = await this.currentCluster.doAction('apply', {\n          yaml:             this.currentYaml,\n          defaultNamespace: this.defaultNamespace,\n        });\n\n        btnCb(true);\n\n        this.rows = res;\n        this.done = true;\n      } catch (err) {\n        this.errors = exceptionToErrorsArray(err);\n        this.done = false;\n        btnCb(false);\n      }\n    },\n\n    rowClick(e) {\n      if ( e.target.tagName === 'A' ) {\n        this.close();\n      }\n    },\n\n    onReadyYamlEditor(arg) {\n      this.$emit('onReadyYamlEditor', arg);\n    }\n  },\n};\n</script>\n\n<template>\n  <Loading v-if=\"$fetchState.pending\" />\n  <Card\n    v-else\n    :show-highlight-border=\"false\"\n    data-testid=\"import-yaml\"\n    :trigger-focus-trap=\"true\"\n  >\n    <template #title>\n      <div style=\"display: block; width: 100%;\">\n        <template v-if=\"done\">\n          <h4 data-testid=\"import-yaml-success\">\n            {{ t('import.success', {count: rows.length}) }}\n          </h4>\n        </template>\n        <template v-else>\n          <h4 v-t=\"'import.title'\" />\n          <div class=\"row\">\n            <div class=\"col span-6\">\n              <FileSelector\n                role=\"button\"\n                :aria-label=\"t('generic.readFromFileArea', { area: t('import.title') })\"\n                class=\"btn role-secondary pull-left\"\n                :label=\"t('generic.readFromFile')\"\n                @selected=\"onFileSelected\"\n              />\n            </div>\n            <div class=\"col span-6\">\n              <LabeledSelect\n                :value=\"defaultNamespace\"\n                :options=\"namespaceOptions\"\n                label-key=\"import.defaultNamespace.label\"\n                mode=\"edit\"\n                @update:value=\"newValue => defaultNamespace = newValue\"\n              />\n            </div>\n          </div>\n        </template>\n      </div>\n    </template>\n    <template #body>\n      <template v-if=\"done\">\n        <div class=\"results\">\n          <SortableTable\n            :rows=\"rows\"\n            :headers=\"headers\"\n            mode=\"view\"\n            key-field=\"_key\"\n            :search=\"false\"\n            :paging=\"true\"\n            :row-actions=\"false\"\n            :table-actions=\"false\"\n            :sub-rows-description=\"false\"\n            @rowClick=\"rowClick\"\n          />\n        </div>\n      </template>\n      <YamlEditor\n        v-else\n        ref=\"yamleditor\"\n        v-model:value=\"currentYaml\"\n        class=\"yaml-editor\"\n        @onReady=\"onReadyYamlEditor\"\n      />\n      <Banner\n        v-for=\"(err, i) in errors\"\n        :key=\"i\"\n        color=\"error\"\n        :label=\"err\"\n      />\n    </template>\n    <template #actions>\n      <div\n        v-if=\"done\"\n        class=\"text-center\"\n        style=\"width: 100%\"\n      >\n        <button\n          :aria-label=\"t('generic.close')\"\n          role=\"button\"\n          type=\"button\"\n          class=\"btn role-primary\"\n          data-testid=\"import-yaml-close\"\n          @click=\"close\"\n        >\n          {{ t('generic.close') }}\n        </button>\n      </div>\n      <div\n        v-else\n        class=\"text-center\"\n        style=\"width: 100%\"\n      >\n        <button\n          :aria-label=\"t('generic.cancel')\"\n          role=\"button\"\n          type=\"button\"\n          class=\"btn role-secondary mr-10\"\n          data-testid=\"import-yaml-cancel\"\n          @click=\"close\"\n        >\n          {{ t('generic.cancel') }}\n        </button>\n        <AsyncButton\n          v-if=\"!done\"\n          mode=\"import\"\n          :disabled=\"!currentYaml.length\"\n          data-testid=\"import-yaml-import-action\"\n          :aria-label=\"t('import.title')\"\n          @click=\"importYaml\"\n        />\n      </div>\n    </template>\n  </Card>\n</template>\n\n<style lang='scss' scoped>\n  $min: 50vh;\n  $max: 50vh;\n\n  .yaml-editor {\n    flex: 1;\n    min-height: $min;\n    max-height: $max;\n\n    :deep() .code-mirror {\n      .CodeMirror {\n        position: initial;\n      }\n\n      .CodeMirror,\n      .CodeMirror-scroll,\n      .CodeMirror-gutters {\n        min-height: $min;\n        max-height: $max;\n      }\n    }\n  }\n</style>\n"]}]}