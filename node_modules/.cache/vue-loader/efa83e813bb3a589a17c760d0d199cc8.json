{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Select.vue?vue&type=style&index=0&id=83930b98&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Select.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLnVubGFiZWxlZC1zZWxlY3QgewogICAgcG9zaXRpb246IHJlbGF0aXZlOwoKICAgIDpkZWVwKCkgLnYtc2VsZWN0LnNlbGVjdC1pbnB1dC12aWV3IHsKICAgICAgLnZzX19hY3Rpb25zIHsKICAgICAgICB2aXNpYmlsaXR5OiBoaWRkZW47CiAgICAgIH0KICAgIH0KCiAgICAmIC52cy0tbXVsdGlwbGUgOmRlZXAoKSAudnNfX3NlbGVjdGVkLW9wdGlvbnMgLnZzX19zZWxlY3RlZCB7CiAgICAgIHdpZHRoOiBhdXRvOwogICAgfQoKICAgIDpkZWVwKCkgLmxhYmVsZWQtdG9vbHRpcC5lcnJvciAuc3RhdHVzLWljb24gewogICAgICB0b3A6IDdweDsKICAgICAgcmlnaHQ6IDJweDsKICAgIH0KCiAgICA6ZGVlcCgpIC52c19fc2VsZWN0ZWQtb3B0aW9ucyB7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIG1hcmdpbjogM3B4OwoKICAgICAgLnZzX19zZWxlY3RlZCB7CiAgICAgICAgICB3aWR0aDogaW5pdGlhbDsKICAgICAgfQogICAgfQoKICAgIDpkZWVwKCkgLnYtc2VsZWN0LnZzLS1vcGVuIHsKICAgICAgLnZzX19kcm9wZG93bi10b2dnbGUgewogICAgICAgIGNvbG9yOiB2YXIoLS1vdXRsaW5lKSAhaW1wb3J0YW50OwogICAgICB9CiAgICB9CgogICAgOmRlZXAoKSAudi1zZWxlY3QudnMtLW9wZW4gewogICAgICAudnNfX2Ryb3Bkb3duLXRvZ2dsZSB7CiAgICAgICAgY29sb3I6IHZhcigtLW91dGxpbmUpICFpbXBvcnRhbnQ7CiAgICAgIH0KICAgIH0KCiAgICBAaW5jbHVkZSBpbnB1dC1zdGF0dXMtY29sb3I7CgogICAgJi5jb21wYWN0LWlucHV0IHsKICAgICAgbWluLWhlaWdodDogJHVubGFiZWxlZC1pbnB1dC1oZWlnaHQ7CiAgICAgIGxpbmUtaGVpZ2h0OiAkaW5wdXQtbGluZS1oZWlnaHQ7CiAgICB9CiAgfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Select.vue"], "names": [], "mappings": ";EA4UE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CA<PERSON>;MACpB;IACF;;IAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACZ;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Select.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { get } from '@shell/utils/object';\nimport LabeledFormElement from '@shell/mixins/labeled-form-element';\nimport VueSelectOverrides from '@shell/mixins/vue-select-overrides';\nimport { LabeledTooltip } from '@components/LabeledTooltip';\nimport { onClickOption, calculatePosition } from '@shell/utils/select';\n\nexport default {\n  emits: ['update:value', 'createdListItem'],\n\n  components: { LabeledTooltip },\n  mixins:     [\n    LabeledFormElement,\n    VueSelectOverrides,\n  ],\n  props: {\n    appendToBody: {\n      default: true,\n      type:    Boolean,\n    },\n    disabled: {\n      default: false,\n      type:    Boolean,\n    },\n    getKeyForOption: {\n      default: null,\n      type:    Function\n    },\n    mode: {\n      default: 'edit',\n      type:    String,\n    },\n    optionKey: {\n      default: null,\n      type:    String,\n    },\n    optionLabel: {\n      default: 'label',\n      type:    String,\n    },\n    placement: {\n      default: null,\n      type:    String,\n    },\n    placeholder: {\n      type:    String,\n      default: '',\n    },\n    popperOverride: {\n      type:    Function,\n      default: null,\n    },\n    reduce: {\n      default: (e) => {\n        if (e && typeof e === 'object' && e.value !== undefined) {\n          return e.value;\n        }\n\n        return e;\n      },\n      type: Function,\n    },\n    tooltip: {\n      type:    String,\n      default: null,\n    },\n\n    hoverTooltip: {\n      type:    Boolean,\n      default: true,\n    },\n\n    status: {\n      type:    String,\n      default: null,\n    },\n    value: {\n      default: null,\n      type:    [String, Object, Number, Array, Boolean],\n    },\n    closeOnSelect: {\n      type:    Boolean,\n      default: true\n    },\n\n    compact: {\n      type:    Boolean,\n      default: null\n    },\n    isLangSelect: {\n      type:    Boolean,\n      default: false\n    },\n  },\n\n  methods: {\n    // resizeHandler = in mixin\n    getOptionLabel(option) {\n      if (this.$attrs['get-option-label']) {\n        return this.$attrs['get-option-label'](option);\n      }\n      if (get(option, this.optionLabel)) {\n        if (this.localizedLabel) {\n          return this.$store.getters['i18n/t'](get(option, this.optionLabel));\n        } else {\n          return get(option, this.optionLabel);\n        }\n      } else {\n        return option;\n      }\n    },\n\n    positionDropdown(dropdownList, component, { width }) {\n      if (this.popperOverride) {\n        return this.popperOverride(dropdownList, component, { width });\n      }\n\n      calculatePosition(dropdownList, component, width, this.placement);\n    },\n\n    focusSearch() {\n      // we need this override as in a \"closeOnSelect\" type of component\n      // if we don't have this override, it would open again\n      if (this.overridesMixinPreventDoubleTriggerKeysOpen) {\n        this.$nextTick(() => {\n          const el = this.$refs['select'];\n\n          if ( el ) {\n            el.focus();\n          }\n\n          this.overridesMixinPreventDoubleTriggerKeysOpen = false;\n        });\n\n        return;\n      }\n      this.$refs['select-input'].open = true;\n\n      this.$nextTick(() => {\n        const el = this.$refs['select-input']?.searchEl;\n\n        if ( el ) {\n          el.focus();\n        }\n      });\n    },\n\n    get,\n\n    onClickOption(option, event) {\n      onClickOption.call(this, option, event);\n    },\n    selectable(opt) {\n      // Lets you disable options that are used\n      // for headings on groups of options.\n      if ( opt ) {\n        if ( opt.disabled || opt.kind === 'group' || opt.kind === 'divider' || opt.loading ) {\n          return false;\n        }\n      }\n\n      return true;\n    },\n    /**\n     * Get a unique value to represent the option\n     */\n    getOptionKey(opt) {\n      // Use the property from a component level key\n      if (opt && this.optionKey) {\n        return get(opt, this.optionKey);\n      }\n\n      // Use the property from an option level key\n      // This doesn't seem right, think it was meant to represent the actual option key... rather than the key to find the option key\n      // This approach also doesn't appear in LabeledSelect\n      if (opt?.optionKey) {\n        // opt.optionKey should in theory be optionKeyKey\n        return get(opt, opt.optionKey);\n      }\n\n      // There's no configuration to help us get a sensible key. Fall back on ..\n      // - the label\n      // - something random\n\n      const label = this.getOptionLabel(opt);\n\n      // label may be type of object\n      if (typeof label === 'string' || typeof label === 'number') {\n        return label;\n      } else {\n        return Math.random(100000);\n      }\n    },\n    report(e) {\n      alert(e);\n    },\n    handleDropdownOpen(args) {\n      // function that prevents the \"opening dropdown on focus\"\n      // default behaviour of v-select\n      return args.noDrop || args.disabled ? false : args.open;\n    }\n  },\n  computed: {\n    requiredField() {\n      // using \"any\" for a type on \"rule\" here is dirty but the use of the optional chaining operator makes it safe for what we're doing here.\n      return (this.required || this.rules.some((rule) => rule?.name === 'required'));\n    },\n    validationMessage() {\n      // we want to grab the required rule passed in if we can but if it's not there then we can just grab it from the formRulesGenerator\n      const requiredRule = this.rules.find((rule) => rule?.name === 'required');\n      const ruleMessages = [];\n      const value = this?.value;\n\n      if (requiredRule && this.blurred && !this.focused) {\n        const message = requiredRule(value);\n\n        if (!!message) {\n          return message;\n        }\n      }\n\n      for (const rule of this.rules) {\n        const message = rule(value);\n\n        if (!!message && rule.name !== 'required') { // we're catching 'required' above so we can ignore it here\n          ruleMessages.push(message);\n        }\n      }\n      if (ruleMessages.length > 0 && (this.blurred || this.focused)) {\n        return ruleMessages.join(', ');\n      } else {\n        return undefined;\n      }\n    },\n    canPaginate() {\n      return false;\n    },\n    deClassedAttrs() {\n      const { class: _, ...rest } = this.$attrs;\n\n      return rest;\n    }\n  }\n};\n</script>\n\n<template>\n  <div\n    ref=\"select\"\n    class=\"unlabeled-select\"\n    :class=\"{\n      disabled: disabled || isView,\n      focused,\n      [mode]: true,\n      [status]: status,\n      taggable: $attrs.taggable,\n      taggable: $attrs.multiple,\n      'compact-input': compact,\n      [$attrs.class]: $attrs.class\n    }\"\n    :tabindex=\"disabled || isView ? -1 : 0\"\n    @click=\"focusSearch\"\n    @keydown.enter=\"focusSearch\"\n    @keydown.down.prevent=\"focusSearch\"\n    @keydown.space.prevent=\"focusSearch\"\n  >\n    <v-select\n      ref=\"select-input\"\n      v-bind=\"deClassedAttrs\"\n      class=\"inline\"\n      :class=\"{'select-input-view': mode === 'view'}\"\n      :autoscroll=\"true\"\n      :append-to-body=\"appendToBody\"\n      :calculate-position=\"positionDropdown\"\n      :disabled=\"isView || disabled\"\n      :get-option-key=\"(opt) => getOptionKey(opt)\"\n      :get-option-label=\"(opt) => getOptionLabel(opt)\"\n      :label=\"optionLabel\"\n      :options=\"options\"\n      :close-on-select=\"closeOnSelect\"\n      :map-keydown=\"mappedKeys\"\n      :placeholder=\"placeholder\"\n      :reduce=\"(x) => reduce(x)\"\n      :searchable=\"isSearchable\"\n      :selectable=\"selectable\"\n      :modelValue=\"value != null ? value : ''\"\n      :dropdownShouldOpen=\"handleDropdownOpen\"\n      :tabindex=\"-1\"\n      role=\"listbox\"\n      @update:modelValue=\"$emit('update:value', $event)\"\n      @search:blur=\"onBlur\"\n      @search:focus=\"onFocus\"\n      @open=\"resizeHandler\"\n      @option:created=\"(e) => $emit('createdListItem', e)\"\n    >\n      <template\n        #option=\"option\"\n      >\n        <div\n          :lang=\"isLangSelect ? option.value : undefined\"\n          @mousedown=\"(e) => onClickOption(option, e)\"\n        >\n          {{ getOptionLabel(option.label) }}\n        </div>\n      </template>\n      <!-- Pass down templates provided by the caller -->\n      <template\n        v-for=\"(_, slot) of $slots\"\n        :key=\"slot\"\n        v-slot:[slot]=\"scope\"\n      >\n        <slot\n          :name=\"slot\"\n          v-bind=\"scope\"\n        />\n      </template>\n    </v-select>\n    <LabeledTooltip\n      v-if=\"tooltip && !focused\"\n      :hover=\"hoverTooltip\"\n      :value=\"tooltip\"\n      :status=\"status\"\n    />\n    <LabeledTooltip\n      v-if=\"!!validationMessage\"\n      :hover=\"hoverTooltip\"\n      :value=\"validationMessage\"\n    />\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .unlabeled-select {\n    position: relative;\n\n    :deep() .v-select.select-input-view {\n      .vs__actions {\n        visibility: hidden;\n      }\n    }\n\n    & .vs--multiple :deep() .vs__selected-options .vs__selected {\n      width: auto;\n    }\n\n    :deep() .labeled-tooltip.error .status-icon {\n      top: 7px;\n      right: 2px;\n    }\n\n    :deep() .vs__selected-options {\n      display: flex;\n      margin: 3px;\n\n      .vs__selected {\n          width: initial;\n      }\n    }\n\n    :deep() .v-select.vs--open {\n      .vs__dropdown-toggle {\n        color: var(--outline) !important;\n      }\n    }\n\n    :deep() .v-select.vs--open {\n      .vs__dropdown-toggle {\n        color: var(--outline) !important;\n      }\n    }\n\n    @include input-status-color;\n\n    &.compact-input {\n      min-height: $unlabeled-input-height;\n      line-height: $input-line-height;\n    }\n  }\n</style>\n"]}]}