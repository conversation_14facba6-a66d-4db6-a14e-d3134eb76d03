{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/NamespaceFilter.vue?vue&type=template&id=4d1ee814&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/NamespaceFilter.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/NamespaceFilter.vue"], "names": [], "mappings": ";EAirBE,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;IACE,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;;IAED,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB;MACE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACrC,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB;QACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAC/D,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB;QACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB;QACE,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB;UACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACrD,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAChF,CAAC;YACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;YAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACnD,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB;QACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MAClD,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC;QACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC;MACD,CAAC;QACC,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;;IAED,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B;MACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC;UACA,CAAC;YACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;UACrB,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B;UACE,CAAC;YACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB;UACE,CAAC;YACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC9B,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ;QACE,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjE,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACvC;UACE,CAAC,CAAC,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC;UACD,CAAC,CAAC,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB;YACE,CAAC;cACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC;cACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC;UACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7C,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/NamespaceFilter.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapGetters } from 'vuex';\nimport { NAMESPACE_FILTERS, ALL_NAMESPACES } from '@shell/store/prefs';\nimport { NAMESPACE, MANAGEMENT } from '@shell/config/types';\nimport { sortBy } from '@shell/utils/sort';\nimport { isArray, addObjects, findBy, filterBy } from '@shell/utils/array';\nimport {\n  NAMESPACE_FILTER_ALL_USER as ALL_USER,\n  NAMESPACE_FILTER_ALL as ALL,\n  NAMESPACE_FILTER_ALL_SYSTEM as ALL_SYSTEM,\n  NAMESPACE_FILTER_ALL_ORPHANS as ALL_ORPHANS,\n  NAMESPACE_FILTER_NAMESPACED_YES as NAMESPACED_YES,\n  NAMESPACE_FILTER_NAMESPACED_NO as NAMESPACED_NO,\n  createNamespaceFilterKey,\n  NAMESPACE_FILTER_KINDS,\n  NAMESPACE_FILTER_NS_FULL_PREFIX,\n  NAMESPACE_FILTER_P_FULL_PREFIX,\n} from '@shell/utils/namespace-filter';\nimport { KEY } from '@shell/utils/platform';\nimport pAndNFiltering from '@shell/plugins/steve/projectAndNamespaceFiltering.utils';\nimport { SETTING } from '@shell/config/settings';\nimport paginationUtils from '@shell/utils/pagination-utils';\n\nconst forcedNamespaceValidTypes = [NAMESPACE_FILTER_KINDS.DIVIDER, NAMESPACE_FILTER_KINDS.PROJECT, NAMESPACE_FILTER_KINDS.NAMESPACE];\n\nexport default {\n\n  data() {\n    return {\n      isOpen:              false,\n      filter:              '',\n      hidden:              0,\n      total:               0,\n      activeElement:       null,\n      cachedFiltered:      [],\n      NAMESPACE_FILTER_KINDS,\n      namespaceFilterMode: undefined,\n    };\n  },\n\n  async fetch() {\n    // Determine if filtering by specific namespaces/projects is required\n    // This is done once and up front\n    // - it doesn't need to be re-active\n    // - added it as a computed caused massive amounts of churn around the `filtered` watcher\n    await this.$store.dispatch('management/find', { type: MANAGEMENT.SETTING, id: SETTING.UI_PERFORMANCE });\n    this.namespaceFilterMode = this.calcNamespaceFilterMode();\n  },\n\n  computed: {\n    ...mapGetters(['currentProduct']),\n\n    hasFilter() {\n      return this.filter.length > 0;\n    },\n\n    paginatedListFilterMode() {\n      return this.$store.getters[`${ this.currentProduct.inStore }/paginationEnabled`](this.$route.params?.resource) ? paginationUtils.validNsProjectFilters : null;\n    },\n\n    filtered() {\n      let out = this.options;\n\n      out = out.filter((item) => {\n        // Filter out anything not applicable to singleton selection\n        if (this.namespaceFilterMode?.length) {\n          // We always show dividers, projects and namespaces\n          if (!forcedNamespaceValidTypes.includes(item.kind)) {\n            const validCustomType = this.namespaceFilterMode.find((prefix) => item.kind.startsWith(prefix));\n\n            if (!validCustomType) {\n              // Hide any invalid option that's not selected\n              return this.value.findIndex((v) => v.id === item.id) >= 0;\n            }\n          }\n        }\n\n        // Filter by the current filter\n        if (this.hasFilter) {\n          return item.kind !== NAMESPACE_FILTER_KINDS.SPECIAL && item.label.toLowerCase().includes(this.filter.toLowerCase());\n        }\n\n        return true;\n      });\n\n      if (out?.[0]?.kind === NAMESPACE_FILTER_KINDS.DIVIDER) {\n        out.splice(0, 1);\n      }\n\n      const mapped = this.value.reduce((m, v) => {\n        m[v.id] = v;\n\n        return m;\n      }, {});\n\n      // Mark all of the selected options\n      out.forEach((i) => {\n        i.selected = !!mapped[i.id] || (i.id === ALL && this.value && this.value.length === 0);\n        i.elementId = (i.id || '').replace('://', '_');\n        i.enabled = true;\n        // Are we in restricted resource type mode, if so is this an allowed type?\n        if (this.namespaceFilterMode?.length) {\n          const isLastSelected = i.selected && (i.id === ALL || this.value.length === 1);\n          const kindAllowed = this.namespaceFilterMode.find((f) => f === i.kind);\n          const isNotInProjectGroup = i.id === ALL_ORPHANS;\n\n          i.enabled = (!isLastSelected && kindAllowed) && !isNotInProjectGroup;\n        } else if (this.paginatedListFilterMode?.length) {\n          i.enabled = !!i.id && paginationUtils.validateNsProjectFilter(i.id);\n        }\n      });\n\n      return out;\n    },\n\n    tooltip() {\n      if (this.isOpen || (this.total + this.hidden) === 0) {\n        return null;\n      }\n\n      let tooltip = '<div class=\"ns-filter-tooltip\">';\n\n      (this.value || []).forEach((v) => {\n        tooltip += `<div class=\"ns-filter-tooltip-item\"><div>${ v.label }</div></div>`;\n      });\n\n      tooltip += '</div>';\n\n      return {\n        content:   tooltip,\n        placement: 'bottom',\n        delay:     { show: 500 }\n      };\n    },\n\n    key() {\n      return createNamespaceFilterKey(this.$store.getters['clusterId'], this.currentProduct);\n    },\n\n    options() {\n      const t = this.$store.getters['i18n/t'];\n      let out = [];\n      const inStore = this.$store.getters['currentStore'](NAMESPACE);\n\n      const params = { ...this.$route.params };\n      const resource = params.resource;\n\n      // Sometimes, different pages may have different namespaces to filter\n      const notFilterNamespaces = this.$store.getters[`type-map/optionsFor`](resource).notFilterNamespace || [];\n\n      // TODO: Add return info\n      if (this.currentProduct?.customNamespaceFilter && this.currentProduct?.inStore) {\n        // Sometimes the component can show before the 'currentProduct' has caught up, so access the product via the getter rather\n        // than caching it in the `fetch`\n\n        // The namespace display on the list and edit pages should be the same as in the namespaceFilter component\n        if (this.$store.getters[`${ this.currentProduct.inStore }/filterNamespace`]) {\n          const allNamespaces = this.$store.getters[`${ this.currentProduct.inStore }/filterNamespace`](notFilterNamespaces);\n\n          this.$store.commit('changeAllNamespaces', allNamespaces);\n        }\n\n        return this.$store.getters[`${ this.currentProduct.inStore }/namespaceFilterOptions`]({\n          addNamespace,\n          divider,\n          notFilterNamespaces\n        });\n      }\n\n      // TODO: Add return info\n      if (!this.currentProduct?.hideSystemResources) {\n        out = [\n          {\n            id:    ALL,\n            kind:  NAMESPACE_FILTER_KINDS.SPECIAL,\n            label: t('nav.ns.all'),\n          },\n          {\n            id:    ALL_USER,\n            kind:  NAMESPACE_FILTER_KINDS.SPECIAL,\n            label: t('nav.ns.user'),\n          },\n          {\n            id:    ALL_SYSTEM,\n            kind:  NAMESPACE_FILTER_KINDS.SPECIAL,\n            label: t('nav.ns.system'),\n          },\n          {\n            id:    NAMESPACED_YES,\n            kind:  NAMESPACE_FILTER_KINDS.SPECIAL,\n            label: t('nav.ns.namespaced'),\n          },\n          {\n            id:    NAMESPACED_NO,\n            kind:  NAMESPACE_FILTER_KINDS.SPECIAL,\n            label: t('nav.ns.clusterLevel'),\n          },\n        ];\n\n        divider(out);\n      }\n\n      if (!inStore) {\n        return out;\n      }\n\n      let namespaces = sortBy(\n        this.$store.getters[`${ inStore }/all`](NAMESPACE),\n        ['nameDisplay']\n      );\n\n      namespaces = this.filterNamespaces(namespaces);\n\n      // isRancher = mgmt schemas are loaded and there's a project schema\n      if (this.$store.getters['isRancher']) {\n        const cluster = this.$store.getters['currentCluster'];\n        let projects = this.$store.getters['management/all'](\n          MANAGEMENT.PROJECT\n        );\n\n        projects = projects.filter((p) => {\n          return this.currentProduct?.hideSystemResources ? !p.isSystem && p.spec.clusterName === cluster.id : p.spec.clusterName === cluster.id;\n        });\n        projects = sortBy(filterBy(projects, 'spec.clusterName', cluster.id), [\n          'nameDisplay',\n        ]);\n        const projectsById = {};\n        const namespacesByProject = {};\n        let firstProject = true;\n\n        namespacesByProject[null] = []; // For namespaces not in a project\n        for (const project of projects) {\n          projectsById[project.metadata.name] = project;\n        }\n\n        for (const namespace of namespaces) {\n          let projectId = namespace.projectId;\n\n          if (!projectId || !projectsById[projectId]) {\n            // If there's a projectId but that project doesn't exist, treat it like no project\n            projectId = null;\n          }\n\n          let entry = namespacesByProject[projectId];\n\n          if (!entry) {\n            entry = [];\n            namespacesByProject[namespace.projectId] = entry;\n          }\n          entry.push(namespace);\n        }\n\n        for (const project of projects) {\n          const id = project.metadata.name;\n\n          if (firstProject) {\n            firstProject = false;\n          } else {\n            divider(out);\n          }\n\n          out.push({\n            id:    `${ NAMESPACE_FILTER_P_FULL_PREFIX }${ id }`,\n            kind:  NAMESPACE_FILTER_KINDS.PROJECT,\n            label: t('nav.ns.project', { name: project.nameDisplay }),\n          });\n\n          const forThisProject = namespacesByProject[id] || [];\n\n          addNamespace(out, forThisProject);\n        }\n\n        const orphans = namespacesByProject[null];\n\n        if (orphans.length) {\n          if (!firstProject) {\n            divider(out);\n          }\n\n          out.push({\n            id:       ALL_ORPHANS,\n            kind:     NAMESPACE_FILTER_KINDS.PROJECT,\n            label:    t('nav.ns.orphan'),\n            disabled: true,\n          });\n\n          addNamespace(out, orphans);\n        }\n      } else {\n        addNamespace(out, namespaces);\n      }\n\n      return out;\n\n      function addNamespace(out, namespaces) {\n        if (!isArray(namespaces)) {\n          namespaces = [namespaces];\n        }\n\n        addObjects(\n          out,\n          namespaces.map((namespace) => {\n            return {\n              id:    `${ NAMESPACE_FILTER_NS_FULL_PREFIX }${ namespace.id }`,\n              kind:  NAMESPACE_FILTER_KINDS.NAMESPACE,\n              label: t('nav.ns.namespace', { name: namespace.nameDisplay }),\n            };\n          })\n        );\n      }\n\n      function divider(out) {\n        out.push({\n          kind:     NAMESPACE_FILTER_KINDS.DIVIDER,\n          label:    `Divider ${ out.length }`,\n          disabled: true,\n        });\n      }\n    },\n\n    isSingleSpecial() {\n      return this.value && this.value.length === 1 && this.value[0].kind === NAMESPACE_FILTER_KINDS.SPECIAL;\n    },\n\n    value: {\n      get() {\n        // Use last picked filter from user preferences\n        const prefs = this.$store.getters['prefs/get'](NAMESPACE_FILTERS);\n        const values = prefs && prefs[this.key] ? prefs[this.key] : this.defaultOption();\n        const options = this.options;\n\n        // Remove values that are not valid options\n        const filters = values\n          .map((value) => {\n            return findBy(options, 'id', value);\n          })\n          .filter((x) => !!x);\n\n        return filters;\n      },\n\n      set(neu) {\n        const old = (this.value || []).slice();\n\n        neu = neu.filter((x) => !!x.id);\n\n        const last = neu[neu.length - 1];\n        const lastIsSpecial = last?.kind === NAMESPACE_FILTER_KINDS.SPECIAL;\n        const hadUser = !!old.find((x) => x.id === ALL_USER);\n        const hadAll = !!old.find((x) => x.id === ALL);\n\n        if (lastIsSpecial) {\n          neu = [last];\n        }\n\n        if (neu.length > 1) {\n          neu = neu.filter((x) => x.kind !== NAMESPACE_FILTER_KINDS.SPECIAL);\n        }\n\n        if (neu.find((x) => x.id === 'all')) {\n          neu = [];\n        }\n\n        let ids;\n\n        // If there was something selected and you remove it, go back to user by default\n        // Unless it was user or all\n        if (neu.length === 0 && !hadUser && !hadAll) {\n          ids = this.defaultOption();\n        } else {\n          ids = neu.map((x) => x.id);\n        }\n\n        this.$nextTick(() => {\n          this.$store.dispatch('switchNamespaces', {\n            ids,\n            key: this.key\n          });\n        });\n      },\n    }\n  },\n\n  beforeUnmount() {\n    this.removeCloseKeyHandler();\n  },\n\n  mounted() {\n    this.layout();\n  },\n\n  watch: {\n    value(neu) {\n      this.layout();\n    },\n\n    /**\n     * When there are thousands of entries certain actions (drop down opened, selection changed, etc) take a long time to complete (upwards\n     * of 5 seconds)\n     *\n     * This is caused by churn of the filtered and options computed properties causing multiple renders for each action.\n     *\n     * To break this multiple-render per cycle behaviour detatch `filtered` from the value used in `v-for`.\n     *\n     */\n    filtered(neu) {\n      if (!!neu) {\n        this.cachedFiltered = neu;\n      }\n    }\n  },\n\n  methods: {\n    filterNamespaces(namespaces) {\n      if (this.$store.getters['prefs/get'](ALL_NAMESPACES)) {\n        // If all namespaces options are turned on in the user preferences,\n        // return all namespaces including system namespaces and RBAC\n        // management namespaces.\n        return namespaces;\n      }\n\n      return namespaces.filter((namespace) => {\n        // Otherwise only filter out obscure namespaces, such as namespaces\n        // that Rancher uses to manage RBAC for projects, which should not be\n        // edited or deleted by Rancher users.\n        return !namespace.isObscure;\n      });\n    },\n    // Layout the content in the dropdown box to best use the space to show the selection\n    layout() {\n      this.$nextTick(() => {\n        // One we have re-rendered, see what we can fit in the control to show the selected namespaces\n        if (this.$refs.values) {\n          const container = this.$refs.values;\n          const overflow = container.scrollWidth > container.offsetWidth;\n          let hidden = 0;\n\n          const dropdown = this.$refs.dropdown;\n          // Remove padding and dropdown arrow size\n          const maxWidth = dropdown.offsetWidth - 20 - 24;\n\n          // If we are overflowing, then allow some space for the +N indicator\n          const itemCount = this.$refs.value ? this.$refs.value.length : 0;\n          let currentWidth = 0;\n          let show = true;\n\n          this.total = 0;\n\n          for (let i = 0; i < itemCount; i++) {\n            const item = this.$refs.value[i];\n            let itemWidth = item.offsetWidth + 10;\n\n            // If this is the first item and we have overflow then add on some space for the +N\n            if (i === 0 && overflow) {\n              itemWidth += 40;\n            }\n\n            currentWidth += itemWidth;\n\n            if (show) {\n              if (i === 0) {\n                // Can't even fit the first item in\n                if (itemWidth > maxWidth) {\n                  show = false;\n                  this.total = this.value.length;\n                }\n              } else {\n                show = currentWidth < maxWidth;\n              }\n            }\n\n            hidden += show ? 0 : 1;\n            item.style.visibility = show ? 'visible' : 'hidden';\n          }\n\n          this.hidden = this.total > 0 ? 0 : hidden;\n        }\n      });\n    },\n    addCloseKeyHandler() {\n      document.addEventListener('keyup', this.closeKeyHandler);\n    },\n    removeCloseKeyHandler() {\n      document.removeEventListener('keyup', this.closeKeyHandler);\n    },\n    closeKeyHandler(e) {\n      if (e.keyCode === KEY.ESCAPE ) {\n        this.close();\n      }\n    },\n    // Keyboard support\n    itemKeyHandler(e, opt) {\n      if (e.keyCode === KEY.DOWN ) {\n        e.preventDefault();\n        e.stopPropagation();\n        this.down();\n      } else if (e.keyCode === KEY.UP ) {\n        e.preventDefault();\n        e.stopPropagation();\n        this.up();\n      } else if (e.keyCode === KEY.SPACE || e.keyCode === KEY.CR) {\n        if (this.namespaceFilterMode && !opt.enabled) {\n          return;\n        }\n        e.preventDefault();\n        e.stopPropagation();\n        this.selectOption(opt);\n        e.target.focus();\n      }\n    },\n    inputKeyHandler(e) {\n      switch (e.keyCode) {\n      case KEY.DOWN:\n        e.preventDefault();\n        e.stopPropagation();\n        this.down(true);\n        break;\n      case KEY.TAB:\n        // Tab out of the input box\n        this.close();\n        e.target.blur();\n        break;\n      case KEY.CR:\n        if (this.filtered.length === 1) {\n          this.selectOption(this.filtered[0]);\n          this.filter = '';\n        }\n        break;\n      }\n    },\n    mouseOver(event) {\n      const el = event?.path?.find((e) => e.classList.contains('ns-option'));\n\n      this.activeElement = el;\n    },\n    setActiveElement(el) {\n      if (!el?.focus) {\n        return;\n      }\n\n      el.focus();\n      this.activeElement = null;\n    },\n    down(input) {\n      const exising = this.activeElement || document.activeElement;\n\n      // Focus the first element in the list\n      if (input || !exising) {\n        if (this.$refs.options) {\n          const c = this.$refs.options.children;\n\n          if (c && c.length > 0) {\n            this.setActiveElement(c[0]);\n          }\n        }\n      } else {\n        let next = exising.nextSibling;\n\n        if (next?.children?.length) {\n          const item = next.children[0];\n\n          // Skip over dividers (assumes we don't have two dividers next to each other)\n          if (item.classList.contains('ns-divider')) {\n            next = next.nextSibling;\n          }\n        }\n\n        if (next?.focus) {\n          this.setActiveElement(next);\n        }\n      }\n    },\n    up() {\n      if (document.activeElement) {\n        let prev = document.activeElement.previousSibling;\n\n        if (prev?.children?.length) {\n          const item = prev.children[0];\n\n          if (item.classList.contains('ns-divider')) {\n            prev = prev.previousSibling;\n          }\n        }\n\n        if (prev?.focus) {\n          this.setActiveElement(prev);\n        }\n      }\n    },\n    toggle() {\n      if (this.isOpen) {\n        this.close();\n      } else {\n        this.open();\n      }\n    },\n    open() {\n      this.isOpen = true;\n      this.$nextTick(() => {\n        this.focusFilter();\n      });\n      this.addCloseKeyHandler();\n      this.layout();\n    },\n    focusFilter() {\n      this.$refs.filter.focus();\n    },\n    close() {\n      this.isOpen = false;\n      this.activeElement = null;\n      this.removeCloseKeyHandler();\n      this.layout();\n    },\n    clear() {\n      this.value = [];\n    },\n    selectOption(option) {\n      // Ignore click for a divider\n      if (option.kind === NAMESPACE_FILTER_KINDS.DIVIDER) {\n        return;\n      }\n\n      const current = this.value;\n\n      // Remove invalid\n      if (!!this.namespaceFilterMode?.length) {\n        this.value.forEach((v) => {\n          if (!this.namespaceFilterMode.find((f) => f === v.kind)) {\n            const index = current.findIndex((c) => c.id === v.id);\n\n            current.splice(index, 1);\n          }\n        });\n      }\n\n      const exists = current.findIndex((v) => v.id === option.id);\n\n      // Remove if it exists (or always add if in singleton mode - we've reset the list above)\n      if (exists !== -1) {\n        current.splice(exists, 1);\n      } else {\n        current.push(option);\n      }\n\n      this.value = current;\n\n      if (document.activeElement) {\n        document.activeElement.blur();\n      }\n    },\n    handleValueMouseDown(ns, event) {\n      this.removeOption(ns, event);\n\n      if (this.value.length === 0) {\n        this.open();\n      }\n    },\n\n    removeOption(ns, event) {\n      this.selectOption(ns);\n      event.preventDefault();\n      event.stopPropagation();\n    },\n\n    defaultOption() {\n      // Note - This is one place where a default ns/project filter value is provided (ALL_USER)\n      // There's also..\n      // - dashboard root store `loadCluster` --> when `updateNamespaces` is dispatched\n      // - harvester root store `loadCluster` --> when `updateNamespaces` is dispatched (can be discarded)\n      // Due to this, we can't really set a nicer default when forced ns/project filtering is on (ALL_USER is invalid)\n      if (this.currentProduct?.customNamespaceFilter) {\n        return [];\n      }\n\n      return [ALL_USER];\n    },\n\n    calcNamespaceFilterMode() {\n      if (pAndNFiltering.isEnabled(this.$store.getters)) {\n        return [NAMESPACE_FILTER_KINDS.NAMESPACE, NAMESPACE_FILTER_KINDS.PROJECT];\n      }\n\n      return null;\n    },\n  }\n};\n</script>\n\n<template>\n  <div\n    v-if=\"!$fetchState.pending\"\n    class=\"ns-filter\"\n    data-testid=\"namespaces-filter\"\n    tabindex=\"0\"\n    @mousedown.prevent\n    @focus=\"open()\"\n  >\n    <div\n      v-if=\"isOpen\"\n      class=\"ns-glass\"\n      @click=\"close()\"\n    />\n\n    <!-- Select Dropdown control -->\n    <div\n      ref=\"dropdown\"\n      class=\"ns-dropdown\"\n      data-testid=\"namespaces-dropdown\"\n      :class=\"{ 'ns-open': isOpen }\"\n      @click=\"toggle()\"\n    >\n      <!-- No filters found or available -->\n      <div\n        v-if=\"value.length === 0\"\n        ref=\"values\"\n        data-testid=\"namespaces-values-none\"\n        class=\"ns-values\"\n      >\n        {{ t('nav.ns.all') }}\n      </div>\n\n      <!-- Filtered by set with custom label E.g. \"All namespaces\" -->\n      <div\n        v-else-if=\"isSingleSpecial\"\n        ref=\"values\"\n        data-testid=\"namespaces-values-label\"\n        class=\"ns-values\"\n      >\n        {{ value[0].label }}\n      </div>\n\n      <!-- All the selected namespaces -->\n      <div\n        v-else\n        ref=\"values\"\n        v-clean-tooltip=\"tooltip\"\n        data-testid=\"namespaces-values\"\n        class=\"ns-values\"\n      >\n        <div\n          v-if=\"total\"\n          ref=\"total\"\n          data-testid=\"namespaces-values-total\"\n          class=\"ns-value ns-abs\"\n        >\n          {{ t('namespaceFilter.selected.label', { total }) }}\n        </div>\n        <div\n          v-for=\"(ns, j) in value\"\n          ref=\"value\"\n          :key=\"ns.id\"\n          :data-testid=\"`namespaces-value-${j}`\"\n          class=\"ns-value\"\n        >\n          <div>{{ ns.label }}</div>\n          <!-- block user from removing the last selection if ns forced filtering is on -->\n          <i\n            v-if=\"!namespaceFilterMode || value.length > 1\"\n            class=\"icon icon-close\"\n            :data-testid=\"`namespaces-values-close-${j}`\"\n            @click=\"removeOption(ns, $event)\"\n            @mousedown=\"handleValueMouseDown(ns, $event)\"\n          />\n        </div>\n      </div>\n\n      <!-- Inform user if more namespaces are selected -->\n      <div\n        v-if=\"hidden > 0\"\n        ref=\"more\"\n        v-clean-tooltip=\"tooltip\"\n        class=\"ns-more\"\n      >\n        {{ t('namespaceFilter.more', { more: hidden }) }}\n      </div>\n      <i\n        v-if=\"!isOpen\"\n        class=\"icon icon-chevron-down\"\n      />\n      <i\n        v-else\n        class=\"icon icon-chevron-up\"\n      />\n    </div>\n    <button\n      v-shortkey.once=\"['n']\"\n      class=\"hide\"\n      @shortkey=\"open()\"\n    />\n\n    <!-- Dropdown menu -->\n    <div\n      v-if=\"isOpen\"\n      class=\"ns-dropdown-menu\"\n      data-testid=\"namespaces-menu\"\n    >\n      <div class=\"ns-controls\">\n        <div class=\"ns-input\">\n          <input\n            ref=\"filter\"\n            v-model=\"filter\"\n            tabindex=\"0\"\n            class=\"ns-filter-input\"\n            @click=\"focusFilter\"\n            @keydown=\"inputKeyHandler($event)\"\n          >\n          <i\n            v-if=\"hasFilter\"\n            class=\"ns-filter-clear icon icon-close\"\n            @click=\"filter = ''\"\n          />\n        </div>\n        <div\n          v-if=\"namespaceFilterMode\"\n          class=\"ns-singleton-info\"\n        >\n          <i\n            v-clean-tooltip=\"t('resourceList.nsFilterToolTip')\"\n            class=\"icon icon-info\"\n          />\n        </div>\n        <div\n          v-else\n          class=\"ns-clear\"\n        >\n          <i\n            class=\"icon icon-close\"\n            @click=\"clear()\"\n          />\n        </div>\n      </div>\n      <div class=\"ns-divider mt-0\" />\n      <div\n        ref=\"options\"\n        class=\"ns-options\"\n        role=\"list\"\n      >\n        <div\n          v-for=\"(opt, i) in cachedFiltered\"\n          :id=\"opt.elementId\"\n          :key=\"opt.id\"\n          tabindex=\"0\"\n          class=\"ns-option\"\n          :disabled=\"opt.enabled ? null : true\"\n          :class=\"{\n            'ns-selected': opt.selected,\n            'ns-single-match': cachedFiltered.length === 1 && !opt.selected,\n          }\"\n          :data-testid=\"`namespaces-option-${i}`\"\n          @click=\"opt.enabled && selectOption(opt)\"\n          @mouseover=\"opt.enabled && mouseOver($event)\"\n          @keydown=\"itemKeyHandler($event, opt)\"\n        >\n          <div\n            v-if=\"opt.kind === NAMESPACE_FILTER_KINDS.DIVIDER\"\n            class=\"ns-divider\"\n          />\n          <div\n            v-else\n            class=\"ns-item\"\n          >\n            <i\n              v-if=\"opt.kind === NAMESPACE_FILTER_KINDS.NAMESPACE\"\n              class=\"icon icon-folder\"\n            />\n            <div>{{ opt.label }}</div>\n            <i\n              v-if=\"opt.selected\"\n              class=\"icon icon-checkmark\"\n            />\n          </div>\n        </div>\n        <div\n          v-if=\"cachedFiltered.length === 0\"\n          class=\"ns-none\"\n          data-testid=\"namespaces-option-none\"\n        >\n          {{ t('namespaceFilter.noMatchingOptions') }}\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  $ns_dropdown_size: 24px;\n\n  .ns-abs {\n    position: absolute;\n  }\n\n  .ns-filter {\n    width: 280px;\n    display: inline-block;\n\n    .ns-glass {\n      top: 0;\n      bottom: 0;\n      left: 0;\n      right: 0;\n      opacity: 0;\n      position: fixed;\n\n      z-index: z-index('overContent');\n    }\n\n    .ns-controls {\n      align-items: center;\n      display: flex;\n    }\n\n    .ns-clear {\n      &:hover {\n        color: var(--primary);\n        cursor: pointer;\n      }\n    }\n\n    .ns-singleton-info, .ns-clear {\n      align-items: center;\n      display: flex;\n      > i {\n        padding-right: 5px;\n      }\n    }\n\n    .ns-input {\n      flex: 1;\n      padding: 5px;\n      position: relative;\n    }\n\n    .ns-filter-input {\n      height: 24px;\n    }\n\n    .ns-filter-clear {\n      cursor: pointer;\n      position: absolute;\n      right: 10px;\n      top: 5px;\n      line-height: 24px;\n      text-align: center;\n      width: 24px;\n    }\n\n    .ns-dropdown-menu {\n      background-color: var(--header-bg);\n      border: 1px solid var(--primary-border);\n      border-bottom-left-radius: var(--border-radius);\n      border-bottom-right-radius: var(--border-radius);\n      color: var(--header-btn-text);\n      margin-top: -1px;\n      padding-bottom: 10px;\n      position: relative;\n      z-index: z-index('dropdownOverlay');\n\n      .ns-options {\n        max-height: 50vh;\n        overflow-y: auto;\n\n        .ns-none {\n          color: var(--muted);\n          padding: 0 10px;\n        }\n      }\n\n      .ns-divider {\n        border-top: 1px solid var(--border);\n        cursor: default;\n        margin-top: 10px;\n        padding-bottom: 10px;\n      }\n\n      .ns-option {\n\n        &[disabled] {\n          cursor: default;\n        }\n\n        &:not([disabled]) {\n          &:focus {\n            background-color: var(--dropdown-hover-bg);\n            color: var(--dropdown-hover-text);\n          }\n          .ns-item {\n             &:hover, &:focus {\n              background-color: var(--dropdown-hover-bg);\n              color: var(--dropdown-hover-text);\n              cursor: pointer;\n\n              > i {\n                color: var(--dropdown-hover-text);\n              }\n            }\n          }\n\n          &.ns-selected {\n            &:hover,&:focus {\n              .ns-item {\n                > * {\n                  background-color: var(--dropdown-hover-bg);\n                  color: var(--dropdown-hover-text);\n                }\n              }\n            }\n          }\n\n          &.ns-selected:not(:hover) {\n            .ns-item {\n              > * {\n                color: var(--primary);\n              }\n            }\n\n            &:focus {\n              .ns-item {\n                > * {\n                  color: var(--dropdown-hover-text);\n                }\n              }\n            }\n          }\n        }\n\n        .ns-item {\n          align-items: center;\n          display: flex;\n          height: 24px;\n          line-height: 24px;\n          padding: 0 10px;\n\n          > i {\n            color: var(--muted);\n            margin: 0 5px;\n          }\n\n          > div {\n            flex: 1;\n            overflow: hidden;\n            text-overflow: ellipsis;\n            white-space: nowrap;\n          }\n\n        }\n\n        &.ns-single-match {\n          .ns-item {\n            background-color: var(--dropdown-hover-bg);\n            > * {\n              color: var(--dropdown-hover-text);\n            }\n          }\n        }\n      }\n    }\n\n    .ns-dropdown {\n      align-items: center;\n      display: flex;\n      border: 1px solid var(--header-border);\n      border-radius: var(--border-radius);\n      color: var(--header-btn-text);\n      cursor: pointer;\n      height: 40px;\n      padding: 0 10px;\n      position: relative;\n      z-index: z-index('dropdownOverlay');\n\n      &.ns-open {\n        border-bottom-left-radius: 0;\n        border-bottom-right-radius: 0;\n        border-color: var(--primary-border);\n      }\n\n      > .ns-values {\n        flex: 1;\n      }\n\n      &:hover {\n        > i {\n          color: var(--primary);\n        }\n      }\n\n      > i {\n        height: $ns_dropdown_size;\n        width: $ns_dropdown_size;\n        cursor: pointer;\n        text-align: center;\n        line-height: $ns_dropdown_size;\n      }\n\n      .ns-more {\n        border: 1px solid var(--header-border);\n        border-radius: 5px;\n        padding: 2px 8px;\n        margin-left: 4px;\n      }\n\n      .ns-values {\n        display: flex;\n        overflow: hidden;\n\n        .ns-value {\n          align-items: center;\n          background-color: rgba(0,0,0,.05);\n          border: 1px solid var(--header-border);\n          border-radius: 5px;\n          color: var(--tag-text);\n          display: flex;\n          line-height: 20px;\n          padding: 2px 5px;\n          white-space: nowrap;\n\n          > i {\n            margin-left: 5px;\n\n            &:hover {\n              color: var(--primary);\n            };\n          }\n\n          // Spacing between tags\n          &:not(:last-child) {\n            margin-right: 5px;\n          }\n        }\n      }\n    }\n  }\n</style>\n<style lang=\"scss\">\n  .v-popper__popper {\n    .ns-filter-tooltip {\n      background-color: var(--body-bg);\n      margin: -6px;\n      padding: 6px;\n\n      .ns-filter-tooltip-item {\n        > div {\n          background-color: rgba(0,0,0,.05);\n          border: 1px solid var(--header-border);\n          border-radius: 5px;\n          color: var(--tag-text);\n          display: inline-block;\n          line-height: 20px;\n          padding: 2px 5px;\n          white-space: nowrap;\n          margin: 4px 0;\n        }\n      }\n    }\n  }\n</style>\n"]}]}