{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceSelector.vue?vue&type=template&id=52ac3ac5", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceSelector.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdj4KICAgIDxkaXYgY2xhc3M9InJvdyI+CiAgICAgIDxkaXYgY2xhc3M9ImNvbCBzcGFuLTEyIj4KICAgICAgICA8TWF0Y2hFeHByZXNzaW9ucwogICAgICAgICAgdi1tb2RlbDp2YWx1ZT0ic2VsZWN0b3JFeHByZXNzaW9ucyIKICAgICAgICAgIDptb2RlPSJtb2RlIgogICAgICAgICAgOnNob3ctcmVtb3ZlPSJmYWxzZSIKICAgICAgICAgIDp0eXBlPSJ0eXBlIgogICAgICAgICAgOnRhcmdldC1yZXNvdXJjZXM9ImFsbFJlc291cmNlc0luU2NvcGUiCiAgICAgICAgLz4KICAgICAgPC9kaXY+CiAgICA8L2Rpdj4KICAgIDxkaXYgY2xhc3M9InJvdyI+CiAgICAgIDxkaXYgY2xhc3M9ImNvbCBzcGFuLTEyIj4KICAgICAgICA8QmFubmVyIDpjb2xvcj0iKG1hdGNoaW5nUmVzb3VyY2VzLm5vbmUgPyAnd2FybmluZycgOiAnc3VjY2VzcycpIj4KICAgICAgICAgIDxzcGFuIHYtY2xlYW4taHRtbD0idCgnZ2VuZXJpYy5zZWxlY3RvcnMubWF0Y2hpbmdSZXNvdXJjZXMubWF0Y2hlc1NvbWUnLCBtYXRjaGluZ1Jlc291cmNlcykiIC8+CiAgICAgICAgPC9CYW5uZXI+CiAgICAgIDwvZGl2PgogICAgPC9kaXY+CiAgICA8ZGl2IGNsYXNzPSJyb3ciPgogICAgICA8ZGl2IGNsYXNzPSJjb2wgc3Bhbi0xMiI+CiAgICAgICAgPFJlc291cmNlVGFibGUKICAgICAgICAgIDpyb3dzPSJtYXRjaGluZ1Jlc291cmNlcy5tYXRjaGVzIgogICAgICAgICAgOmhlYWRlcnM9InRhYmxlSGVhZGVycyIKICAgICAgICAgIGtleS1maWVsZD0iaWQiCiAgICAgICAgICA6dGFibGUtYWN0aW9ucz0iZmFsc2UiCiAgICAgICAgICA6c2NoZW1hPSJzY2hlbWEiCiAgICAgICAgICA6Z3JvdXBhYmxlPSJmYWxzZSIKICAgICAgICAgIDpzZWFyY2g9ImZhbHNlIgogICAgICAgIC8+CiAgICAgIDwvZGl2PgogICAgPC9kaXY+CiAgPC9kaXY+Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceSelector.vue"], "names": [], "mappings": ";EAgHE,CAAC,CAAC,CAAC,CAAC;IACF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceSelector.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { Banner } from '@components/Banner';\nimport MatchExpressions from '@shell/components/form/MatchExpressions';\nimport ResourceTable from '@shell/components/ResourceTable';\nimport { allHash } from '@shell/utils/promise';\nimport { _EDIT } from '@shell/config/query-params';\nimport { convert, matching, simplify } from '@shell/utils/selector';\nimport throttle from 'lodash/throttle';\n\nexport default {\n  name: 'ResourceSelector',\n\n  components: {\n    Banner,\n    MatchExpressions,\n    ResourceTable,\n  },\n\n  props: {\n    mode: {\n      type:    String,\n      default: _EDIT,\n    },\n    type: {\n      type:     String,\n      required: true,\n    },\n    value: {\n      type:     Object,\n      required: true,\n    },\n    namespace: {\n      type:    String,\n      default: '',\n    },\n  },\n\n  async fetch() {\n    // Used in conjunction with `matches/match/label selectors`. Requires https://github.com/rancher/dashboard/issues/10417 to fix\n    const hash = await allHash({ allResources: this.$store.dispatch('cluster/findAll', { type: this.type }) });\n\n    this.allResources = hash.allResources;\n\n    this.updateMatchingResources();\n  },\n\n  data() {\n    const matchingResources = {\n      matched: 0,\n      matches: [],\n      none:    true,\n      sample:  null,\n      total:   0,\n    };\n\n    return {\n      matchingResources,\n      allResources:        [],\n      allResourcesInScope: [],\n      tableHeaders:        this.$store.getters['type-map/headersFor'](\n        this.$store.getters['cluster/schemaFor'](this.type)\n      ),\n    };\n  },\n\n  watch: {\n    namespace:                'updateMatchingResources',\n    'value.matchLabels':      'updateMatchingResources',\n    'value.matchExpressions': 'updateMatchingResources',\n  },\n\n  computed: {\n    schema() {\n      return this.$store.getters['cluster/schemaFor'](this.type);\n    },\n    selectorExpressions: {\n      get() {\n        return convert(\n          this.value.matchLabels || {},\n          this.value.matchExpressions || []\n        );\n      },\n      set(selectorExpressions) {\n        const { matchLabels, matchExpressions } = simplify(selectorExpressions);\n\n        this.value['matchLabels'] = matchLabels;\n        this.value['matchExpressions'] = matchExpressions;\n      }\n    },\n  },\n\n  methods: {\n    updateMatchingResources: throttle(function() {\n      this.allResourcesInScope = this.namespace ? this.allResources.filter((res) => res.metadata.namespace === this.namespace) : this.allResources;\n      const match = matching(this.allResourcesInScope, this.selectorExpressions);\n      const matched = match.length || 0;\n      const sample = match[0]?.nameDisplay;\n\n      this.matchingResources = {\n        matched,\n        matches: match,\n        none:    matched === 0,\n        sample,\n        total:   this.allResourcesInScope.length,\n      };\n    }, 250, { leading: true }),\n  }\n\n};\n</script>\n\n<template>\n  <div>\n    <div class=\"row\">\n      <div class=\"col span-12\">\n        <MatchExpressions\n          v-model:value=\"selectorExpressions\"\n          :mode=\"mode\"\n          :show-remove=\"false\"\n          :type=\"type\"\n          :target-resources=\"allResourcesInScope\"\n        />\n      </div>\n    </div>\n    <div class=\"row\">\n      <div class=\"col span-12\">\n        <Banner :color=\"(matchingResources.none ? 'warning' : 'success')\">\n          <span v-clean-html=\"t('generic.selectors.matchingResources.matchesSome', matchingResources)\" />\n        </Banner>\n      </div>\n    </div>\n    <div class=\"row\">\n      <div class=\"col span-12\">\n        <ResourceTable\n          :rows=\"matchingResources.matches\"\n          :headers=\"tableHeaders\"\n          key-field=\"id\"\n          :table-actions=\"false\"\n          :schema=\"schema\"\n          :groupable=\"false\"\n          :search=\"false\"\n        />\n      </div>\n    </div>\n  </div>\n</template>\n"]}]}