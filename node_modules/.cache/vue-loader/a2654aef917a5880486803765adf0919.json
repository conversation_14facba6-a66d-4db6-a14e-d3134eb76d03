{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CreateDriver.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CreateDriver.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCB7IExhYmVsZWRJbnB1dCB9IGZyb20gJ0Bjb21wb25lbnRzL0Zvcm0vTGFiZWxlZElucHV0JzsKaW1wb3J0IEFycmF5TGlzdCBmcm9tICdAc2hlbGwvY29tcG9uZW50cy9mb3JtL0FycmF5TGlzdCc7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnRHJpdmVyQ3JlYXRlJywKCiAgY29tcG9uZW50czogewogICAgTGFiZWxlZElucHV0LAogICAgQXJyYXlMaXN0CiAgfSwKICBwcm9wczogewogICAgbW9kZTogewogICAgICB0eXBlOiAgICAgU3RyaW5nLAogICAgICByZXF1aXJlZDogdHJ1ZSwKICAgIH0sCiAgICB2YWx1ZTogewogICAgICB0eXBlOiAgICBPYmplY3QsCiAgICAgIGRlZmF1bHQ6ICgpID0+IHsKICAgICAgICByZXR1cm4ge307CiAgICAgIH0KICAgIH0sCiAgICBydWxlczogewogICAgICBkZWZhdWx0OiAoKSA9PiAoewogICAgICAgIHVybDogICAgICAgICAgICAgIFtdLAogICAgICAgIHVpVXJsOiAgICAgICAgICAgIFtdLAogICAgICAgIGNoZWNrc3VtOiAgICAgICAgIFtdLAogICAgICAgIHdoaXRlbGlzdERvbWFpbnM6IFtdCiAgICAgIH0pLAogICAgICB0eXBlOiBPYmplY3QsCiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CreateDriver.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACV,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QACd,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACrB,CAAC,CAAC;MACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd;EACF;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CreateDriver.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport ArrayList from '@shell/components/form/ArrayList';\nexport default {\n  name: 'DriverCreate',\n\n  components: {\n    LabeledInput,\n    ArrayList\n  },\n  props: {\n    mode: {\n      type:     String,\n      required: true,\n    },\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    rules: {\n      default: () => ({\n        url:              [],\n        uiUrl:            [],\n        checksum:         [],\n        whitelistDomains: []\n      }),\n      type: Object,\n    }\n  }\n};\n</script>\n\n<template>\n  <div>\n    <div class=\"row mb-20\">\n      <LabeledInput\n        v-model:value.trim=\"value.url\"\n        :label=\"t('drivers.add.downloadUrl.label')\"\n        :placeholder=\"t('drivers.add.downloadUrl.placeholder', null, true)\"\n        :tooltip=\"t('drivers.add.downloadUrl.tooltip', null, true)\"\n        :mode=\"mode\"\n        :rules=\"rules.url\"\n        :data-testid=\"'driver-create-url-field'\"\n        required\n      />\n    </div>\n    <div class=\"row mb-20\">\n      <LabeledInput\n        v-model:value.trim=\"value.checksum\"\n        :label=\"t('drivers.add.checksum.label')\"\n        :tooltip=\"t('drivers.add.checksum.tooltip', null, true)\"\n        :mode=\"mode\"\n        :rules=\"rules.checksum\"\n        :data-testid=\"'driver-create-checksum-field'\"\n      />\n    </div>\n    <div class=\"row mb-20\">\n      <LabeledInput\n        v-model:value.trim=\"value.uiUrl\"\n        :label=\"t('drivers.add.customUiUrl.label')\"\n        :tooltip=\"t('drivers.add.customUiUrl.tooltip', null, true)\"\n        :placeholder=\"t('drivers.add.customUiUrl.placeholder', null, true)\"\n        :mode=\"mode\"\n        :rules=\"rules.uiUrl\"\n        :data-testid=\"'driver-create-uiurl-field'\"\n      />\n    </div>\n    <div class=\"col span-6\">\n      <ArrayList\n        v-model:value=\"value.whitelistDomains\"\n        :protip=\"false\"\n        :mode=\"mode\"\n        :title=\"t('drivers.add.whitelist')\"\n        :rules=\"rules.whitelistDomains\"\n        :data-testid=\"'driver-create-whitelist-list'\"\n      />\n    </div>\n  </div>\n</template>\n"]}]}