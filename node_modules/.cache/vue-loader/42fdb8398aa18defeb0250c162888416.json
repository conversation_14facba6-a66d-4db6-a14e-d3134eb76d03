{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/SetupUIPlugins.vue?vue&type=style&index=0&id=18917af9&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/SetupUIPlugins.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLmVuYWJsZS1wbHVnaW4tc3VwcG9ydCB7CiAgICBmb250LXNpemU6IDE0cHg7CiAgICBtYXJnaW4tdG9wOiAyMHB4OwogIH0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/SetupUIPlugins.vue"], "names": [], "mappings": ";EA8EE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/SetupUIPlugins.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport IconMessage from '@shell/components/IconMessage.vue';\nimport { MANAGEMENT } from '@shell/config/types';\nimport { UIEXTENSION } from '@shell/store/features';\n\nexport default {\n  props: {\n    hasFeatureFlag: {\n      type:     Boolean,\n      required: true\n    }\n  },\n\n  components: { IconMessage },\n\n  data() {\n    return {\n      UIEXTENSION,\n      loading:            true,\n      showFeaturesButton: false,\n    };\n  },\n\n  created() {\n    if ( !this.hasFeatureFlag && this.$store.getters['management/schemaFor'](MANAGEMENT.FEATURE) ) {\n      this.showFeaturesButton = true;\n    }\n\n    this.loading = false;\n  },\n\n  methods: {\n    redirectToFeatureFlags() {\n      this.$router.push({\n        path:   '/c/local/settings/management.cattle.io.feature',\n        params: {\n          product:  'settings',\n          resource: 'management.cattle.io.feature',\n          cluster:  'local',\n        }\n      });\n    }\n  }\n};\n</script>\n\n<template>\n  <div data-testid=\"extension-feature-enable-container\">\n    <IconMessage\n      :vertical=\"true\"\n      :subtle=\"false\"\n      icon=\"icon-gear\"\n    >\n      <template v-slot:message>\n        <h2>\n          {{ t('plugins.setup.title') }}\n        </h2>\n        <div v-if=\"!loading\">\n          <div>\n            <div v-clean-html=\"t('plugins.setup.prompt.can', { ff: UIEXTENSION }, true)\" />\n            <button\n              v-if=\"showFeaturesButton\"\n              class=\"btn role-primary enable-plugin-support\"\n              data-testid=\"extension-feature-button\"\n              role=\"button\"\n              :aria-label=\"t('plugins.setup.install.featuresButton')\"\n              @click=\"redirectToFeatureFlags\"\n            >\n              {{ t('plugins.setup.install.featuresButton') }}\n            </button>\n          </div>\n        </div>\n      </template>\n    </IconMessage>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .enable-plugin-support {\n    font-size: 14px;\n    margin-top: 20px;\n  }\n</style>\n"]}]}