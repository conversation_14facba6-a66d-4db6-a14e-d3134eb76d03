{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Group.vue?vue&type=template&id=5dc959c0&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Group.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Group.vue"], "names": [], "mappings": ";EA6ME,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3H;IACE,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B;MACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf;UACE,CAAC,CAAC,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAC1D,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC;UACA,CAAC,CAAC,CAAC,CAAC,CAAC;QACP;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC1D,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACN,CAAC;QACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB;MACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX;QACE,CAAC,CAAC;UACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX;UACE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzF,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACT,CAAC,CAAC;UACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB;UACE,CAAC,CAAC,CAAC,CAAC,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC;QACH,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Group.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport Type from '@shell/components/nav/Type';\nexport default {\n  name: 'Group',\n\n  components: { Type },\n\n  emits: ['expand', 'close'],\n\n  props: {\n    depth: {\n      type:    Number,\n      default: 0,\n    },\n\n    idPrefix: {\n      type:     String,\n      required: true,\n    },\n\n    group: {\n      type:     Object,\n      required: true,\n    },\n\n    childrenKey: {\n      type:    String,\n      default: 'children',\n    },\n\n    canCollapse: {\n      type:    Boolean,\n      default: true,\n    },\n\n    showHeader: {\n      type:    Boolean,\n      default: true,\n    },\n\n    fixedOpen: {\n      type:    Boolean,\n      default: false,\n    }\n  },\n\n  data() {\n    const id = (this.idPrefix || '') + this.group.name;\n\n    return { id, expanded: false };\n  },\n\n  computed: {\n    isGroupActive() {\n      return this.isOverview || (this.hasActiveRoute() && this.isExpanded && this.showHeader);\n    },\n\n    hasChildren() {\n      return this.group.children?.length > 0;\n    },\n\n    hasOverview() {\n      return this.group.children?.[0]?.overview;\n    },\n\n    onlyHasOverview() {\n      return this.group.children && this.group.children.length === 1 && this.hasOverview;\n    },\n\n    isOverview() {\n      if (this.group.children && this.group.children.length > 0) {\n        const grp = this.group.children[0];\n        const overviewRoute = grp?.route;\n\n        if (overviewRoute && grp.overview) {\n          const route = this.$router.resolve(overviewRoute || {});\n\n          return this.$route.fullPath.split('#')[0] === route?.fullPath;\n        }\n      }\n\n      return false;\n    },\n\n    isExpanded: {\n      get() {\n        return this.fixedOpen || this.group.isRoot || !!this.expanded;\n      },\n      set(v) {\n        this.expanded = v;\n      }\n    }\n  },\n\n  methods: {\n    expandGroup() {\n      this.isExpanded = true;\n      this.$emit('expand', this.group);\n    },\n\n    groupSelected() {\n      // Don't auto-select first group entry if we're already expanded and contain the currently-selected nav item\n      if (this.hasActiveRoute() && this.isExpanded) {\n        return;\n      } else {\n        // Remove all active class if click on group header and not active route\n        const headerEl = document.querySelectorAll('.header');\n\n        headerEl.forEach((el) => {\n          el.classList.remove('active');\n        });\n      }\n      this.expandGroup();\n\n      const items = this.group[this.childrenKey];\n\n      // Navigate to one of the child items (by default the first child)\n      if (items && items.length > 0) {\n        let index = 0;\n\n        // If there is a default type, use it\n        if (this.group.defaultType) {\n          const found = items.findIndex((i) => i.name === this.group.defaultType);\n\n          index = (found === -1) ? 0 : found;\n        }\n\n        const route = items[index].route;\n\n        if (route) {\n          this.$router.replace(route);\n        }\n      }\n    },\n\n    selectType() {\n      this.groupSelected();\n      this.close();\n    },\n\n    close() {\n      this.$emit('close');\n    },\n\n    // User clicked on the expander icon, so toggle the expansion so the user can see inside the group\n    peek($event) {\n      // Add active class to the current header if click on chevron icon\n      $event.target.parentElement.classList.remove('active');\n      if (this.hasActiveRoute() && this.isExpanded) {\n        $event.target.parentElement.classList.add('active');\n      }\n      this.isExpanded = !this.isExpanded;\n      $event.stopPropagation();\n    },\n\n    hasActiveRoute(items) {\n      if (!items) {\n        items = this.group;\n      }\n\n      for (const item of items.children) {\n        if (item.children && this.hasActiveRoute(item)) {\n          return true;\n        } else if (item.route) {\n          const navLevels = ['cluster', 'product', 'resource'];\n          const matchesNavLevel = navLevels.filter((param) => !this.$route.params[param] || this.$route.params[param] !== item.route.params[param]).length === 0;\n          const withoutHash = this.$route.hash ? this.$route.fullPath.slice(0, this.$route.fullPath.indexOf(this.$route.hash)) : this.$route.fullPath;\n          const withoutQuery = withoutHash.split('?')[0];\n\n          if (matchesNavLevel || this.$router.resolve(item.route).fullPath === withoutQuery) {\n            return true;\n          }\n        }\n      }\n\n      return false;\n    },\n\n    syncNav() {\n      const refs = this.$refs.groups;\n\n      if (refs) {\n        // Only expand one group - so after the first has been expanded, no more will\n        let canExpand = true;\n\n        refs.forEach((grp) => {\n          if (!grp.group.isRoot) {\n            if (canExpand) {\n              const isActive = this.hasActiveRoute(grp.group);\n\n              if (isActive) {\n                grp.isExpanded = true;\n                canExpand = false;\n                this.$nextTick(() => grp.syncNav());\n              }\n            }\n          }\n        });\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <div\n    class=\"accordion\"\n    :class=\"{[`depth-${depth}`]: true, 'expanded': isExpanded, 'has-children': hasChildren, 'group-highlight': isGroupActive}\"\n  >\n    <div\n      v-if=\"showHeader\"\n      class=\"header\"\n      :class=\"{'active': isOverview, 'noHover': !canCollapse}\"\n      role=\"button\"\n      tabindex=\"0\"\n      :aria-label=\"group.labelDisplay || group.label || ''\"\n      @click=\"groupSelected()\"\n      @keyup.enter=\"groupSelected()\"\n      @keyup.space=\"groupSelected()\"\n    >\n      <slot name=\"header\">\n        <router-link\n          v-if=\"hasOverview\"\n          :to=\"group.children[0].route\"\n          :exact=\"group.children[0].exact\"\n          :tabindex=\"-1\"\n        >\n          <h6>\n            <span v-clean-html=\"group.labelDisplay || group.label\" />\n          </h6>\n        </router-link>\n        <h6\n          v-else\n        >\n          <span v-clean-html=\"group.labelDisplay || group.label\" />\n        </h6>\n      </slot>\n      <i\n        v-if=\"!onlyHasOverview && canCollapse\"\n        class=\"icon toggle toggle-accordion\"\n        :class=\"{'icon-chevron-right': !isExpanded, 'icon-chevron-down': isExpanded}\"\n        role=\"button\"\n        tabindex=\"0\"\n        :aria-label=\"t('nav.ariaLabel.collapseExpand')\"\n        @click=\"peek($event, true)\"\n        @keyup.enter=\"peek($event, true)\"\n        @keyup.space=\"peek($event, true)\"\n      />\n    </div>\n    <ul\n      v-if=\"isExpanded\"\n      class=\"list-unstyled body\"\n      v-bind=\"$attrs\"\n    >\n      <template\n        v-for=\"(child, idx) in group[childrenKey]\"\n        :key=\"idx\"\n      >\n        <li\n          v-if=\"child.divider\"\n          :key=\"idx\"\n        >\n          <hr>\n        </li>\n        <!-- <div v-else-if=\"child[childrenKey] && hideGroup(child[childrenKey])\" :key=\"child.name\">\n          HIDDEN\n        </div> -->\n        <li\n          v-else-if=\"child[childrenKey]\"\n          :key=\"child.name\"\n        >\n          <Group\n            ref=\"groups\"\n            :key=\"id+'_'+child.name+'_children'\"\n            :id-prefix=\"id+'_'\"\n            :depth=\"depth + 1\"\n            :children-key=\"childrenKey\"\n            :can-collapse=\"canCollapse\"\n            :group=\"child\"\n            :fixed-open=\"fixedOpen\"\n            @selected=\"groupSelected($event)\"\n            @expand=\"expandGroup($event)\"\n            @close=\"close($event)\"\n          />\n        </li>\n        <Type\n          v-else-if=\"!child.overview || group.name === 'starred'\"\n          :key=\"id+'_' + child.name + '_type'\"\n          :is-root=\"depth == 0 && !showHeader\"\n          :type=\"child\"\n          :depth=\"depth\"\n          @selected=\"selectType($event)\"\n        />\n      </template>\n    </ul>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .header {\n    position: relative;\n    cursor: pointer;\n    color: var(--body-text);\n    height: 33px;\n    outline: none;\n\n    H6 {\n      color: var(--body-text);\n      user-select: none;\n      text-transform: none;\n      font-size: 14px;\n    }\n\n    > A {\n      display: block;\n      box-sizing:border-box;\n      height: 100%;\n      &:hover{\n        text-decoration: none;\n      }\n      &:focus{\n        outline:none;\n      }\n      > H6 {\n        text-transform: none;\n        padding: 8px 0 8px 16px;\n      }\n    }\n  }\n\n  .accordion {\n    .header {\n      &:focus-visible {\n        h6 span {\n          @include focus-outline;\n          outline-offset: 2px;\n        }\n      }\n      .toggle-accordion:focus-visible {\n        @include focus-outline;\n        outline-offset: -6px;\n      }\n\n      &.active {\n        color: var(--primary-hover-text);\n        background-color: var(--primary-hover-bg);\n\n        h6 {\n          padding: 8px 0 8px 16px;\n          font-weight: bold;\n          color: var(--primary-hover-text);\n        }\n\n        &:hover {\n          background-color: var(--primary-hover-bg);\n        }\n      }\n      &:hover:not(.active) {\n        background-color: var(--nav-hover);\n      }\n    }\n  }\n\n  .accordion {\n    &.depth-0 {\n      > .header {\n\n        &.noHover {\n          cursor: default;\n        }\n\n        > H6 {\n          text-transform: none;\n          padding: 8px 0 8px 16px;\n        }\n\n        > I {\n          position: absolute;\n          right: 0;\n          top: 0;\n          padding: 10px 10px 9px 7px;\n          user-select: none;\n        }\n      }\n\n      > .body {\n        margin-left: 0;\n      }\n\n      &.group-highlight {\n        background: var(--nav-active);\n      }\n    }\n\n    &.depth-1 {\n      > .header {\n        padding-left: 20px;\n        > H6 {\n          line-height: 18px;\n          padding: 8px 0 7px 5px !important;\n        }\n        > I {\n          padding: 10px 7px 9px 7px !important;\n        }\n      }\n\n      &:deep() .type-link > .label {\n        padding-left: 10px;\n      }\n    }\n\n    &:not(.depth-0) {\n      > .header {\n        > H6 {\n          // Child groups that aren't linked themselves\n          display: inline-block;\n          padding: 5px 0 5px 5px;\n        }\n\n        > I {\n          position: absolute;\n          right: 0;\n          top: 0;\n          padding: 6px 8px 6px 8px;\n        }\n      }\n    }\n  }\n\n  .body :deep() > .child.router-link-active,\n  .header :deep() > .child.router-link-exact-active {\n    padding: 0;\n\n    A, A I {\n      color: var(--primary-hover-text);\n    }\n\n    A {\n      color: var(--primary-hover-text);\n      background-color: var(--primary-hover-bg);\n      font-weight: bold;\n    }\n  }\n\n  .body :deep() > .child {\n    A {\n      border-left: solid 5px transparent;\n      line-height: 16px;\n      font-size: 14px;\n      padding-left: 24px;\n      display: flex;\n      justify-content: space-between;\n    }\n\n    A:focus {\n      outline: none;\n    }\n\n    &.root {\n      background: transparent;\n      A {\n        padding-left: 14px;\n      }\n    }\n  }\n</style>\n"]}]}