{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/PromptRemove.vue?vue&type=style&index=0&id=771f23ea&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/PromptRemove.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLnByb21wdC1yZW1vdmUgewogICAgJi5jYXJkLWNvbnRhaW5lciB7CiAgICAgIGJveC1zaGFkb3c6IG5vbmU7CiAgICB9CiAgICAjY29uZmlybSB7CiAgICAgIHdpZHRoOiA5MCU7CiAgICAgIG1hcmdpbi1sZWZ0OiAzcHg7CiAgICB9CgogICAgLmFjdGlvbnMgewogICAgICB0ZXh0LWFsaWduOiByaWdodDsKICAgIH0KCiAgICAuY2FyZC1hY3Rpb25zIHsKICAgICAgZGlzcGxheTogZmxleDsKCiAgICAgIC5zcGFjZXIgewogICAgICAgIGZsZXg6IDE7CiAgICAgIH0KICAgIH0KICB9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/PromptRemove.vue"], "names": [], "mappings": ";EAicE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACT;IACF;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/PromptRemove.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { shallowRef } from 'vue';\nimport { mapState, mapGetters } from 'vuex';\nimport { get, isEmpty } from '@shell/utils/object';\nimport { escapeHtml, resourceNames } from '@shell/utils/string';\nimport { Card } from '@components/Card';\nimport { Checkbox } from '@components/Form/Checkbox';\nimport { alternateLabel } from '@shell/utils/platform';\nimport { uniq } from '@shell/utils/array';\nimport AsyncButton from '@shell/components/AsyncButton';\nimport { CATALOG as CATALOG_ANNOTATIONS } from '@shell/config/labels-annotations';\nimport { CATALOG } from '@shell/config/types';\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport AppModal from '@shell/components/AppModal.vue';\n\nexport default {\n  name: 'PromptRemove',\n\n  components: {\n    Card, Checkbox, AsyncButton, LabeledInput, AppModal\n  },\n  props: {\n    /**\n     * Inherited global identifier prefix for tests\n     * Define a term based on the parent component to avoid conflicts on multiple components\n     */\n    componentTestid: {\n      type:    String,\n      default: 'prompt-remove'\n    }\n  },\n  data() {\n    const { resource } = this.$route.params;\n\n    return {\n      hasCustomRemove:     false,\n      randomPosition:      Math.random(),\n      confirmName:         '',\n      error:               '',\n      warning:             '',\n      preventDelete:       false,\n      removeComponent:     shallowRef(this.$store.getters['type-map/importCustomPromptRemove'](resource)),\n      chartsToRemoveIsApp: false,\n      chartsDeleteCrd:     false,\n      showModal:           false,\n    };\n  },\n  computed: {\n    names() {\n      return this.toRemove.map((obj) => obj.nameDisplay);\n    },\n\n    nameToMatchPosition() {\n      const visibleNames = Math.min(5, this.names.length);\n      const randomNamePos = Math.floor(this.randomPosition * visibleNames);\n\n      return randomNamePos;\n    },\n\n    nameToMatch() {\n      return this.names[this.nameToMatchPosition];\n    },\n\n    type() {\n      const types = new Set(this.toRemove.reduce((array, each) => {\n        array.push(each.type);\n\n        return array;\n      }, []));\n\n      if (types.size > 1) {\n        return this.t('generic.resource', { count: this.toRemove.length });\n      }\n\n      const schema = this.toRemove[0]?.schema;\n\n      if ( !schema ) {\n        return `resource${ this.toRemove.length === 1 ? '' : 's' }`;\n      }\n\n      return this.$store.getters['type-map/labelFor'](schema, this.toRemove.length);\n    },\n\n    selfLinks() {\n      return this.toRemove.map((resource) => {\n        return get(resource, 'links.self');\n      });\n    },\n\n    needsConfirm() {\n      const first = this.toRemove[0];\n\n      return first?.confirmRemove;\n    },\n\n    // if the current route ends with the ID of the resource being deleted, whatever page this is wont be valid after successful deletion: navigate away\n    doneLocation() {\n      // if deleting more than one resource, this is happening in list view and shouldn't redirect anywhere\n      if (this.toRemove.length > 1) {\n        return null;\n      }\n\n      if (this.toRemove[0].doneOverride) {\n        return this.toRemove[0].doneOverride;\n      }\n\n      const currentRoute = this.toRemove[0].currentRoute();\n      const out = {};\n      const params = { ...currentRoute.params };\n\n      if (params.id && (params.id === this.toRemove[0]?.metadata?.name || params.id === this.toRemove[0].id)) {\n        let { name = '' } = currentRoute;\n\n        const idIndex = name.indexOf('-id');\n\n        if (idIndex !== -1) {\n          name = name.slice(0, idIndex);\n        }\n\n        if (params.namespace) {\n          const namespaceIndex = name.indexOf('-namespace');\n\n          if (namespaceIndex !== -1) {\n            name = name.slice(0, namespaceIndex);\n          }\n          delete params.namespace;\n        }\n        delete params.id;\n\n        out.params = params;\n        out.name = name;\n      }\n\n      return out;\n    },\n\n    currentRouter() {\n      // ...don't need a router if there's no route to go to\n      if (!this.doneLocation) {\n        return null;\n      } else {\n        return this.toRemove[0].currentRouter();\n      }\n    },\n\n    protip() {\n      return this.t('promptRemove.protip', { alternateLabel });\n    },\n\n    deleteDisabled() {\n      const confirmFailed = this.needsConfirm && this.confirmName !== this.nameToMatch;\n\n      return this.preventDelete || confirmFailed;\n    },\n\n    ...mapState('action-menu', ['showPromptRemove', 'toRemove']),\n    ...mapGetters({ t: 'i18n/t' }),\n  },\n\n  watch: {\n    showPromptRemove(show) {\n      if (show) {\n        const selected = this.toRemove[0];\n\n        if (this.currentRouter?.currentRoute?.value?.name === 'c-cluster-explorer-tools' &&\n            selected.type === CATALOG.APP &&\n            selected.spec?.chart?.metadata?.annotations[CATALOG_ANNOTATIONS.AUTO_INSTALL]) {\n          this.chartsToRemoveIsApp = true;\n        }\n\n        this.showModal = true;\n\n        let { resource } = this.$route.params;\n\n        if (this.toRemove.length > 0) {\n          resource = selected.type;\n        }\n\n        this.hasCustomRemove = this.$store.getters['type-map/hasCustomPromptRemove'](resource);\n\n        this.removeComponent = shallowRef(this.$store.getters['type-map/importCustomPromptRemove'](resource));\n      } else {\n        this.showModal = false;\n      }\n    },\n\n    // check for any resources with a deletion prevention message,\n    // if none found (delete is allowed), then check for any resources with a warning message\n    toRemove(neu) {\n      let message;\n      const preventDeletionMessages = neu.filter((item) => item.preventDeletionMessage);\n\n      this.preventDelete = false;\n\n      if (!!preventDeletionMessages.length) {\n        this.preventDelete = true;\n        message = preventDeletionMessages[0].preventDeletionMessage;\n      } else {\n        const warnDeletionMessages = neu.filter((item) => item.warnDeletionMessage);\n\n        if (!!warnDeletionMessages.length) {\n          message = warnDeletionMessages[0].warnDeletionMessage;\n        }\n      }\n      if (typeof message === 'function' ) {\n        this.warning = message(this.toRemove);\n      } else if (!!message) {\n        this.warning = message;\n      } else {\n        this.warning = '';\n      }\n    }\n  },\n\n  methods: {\n    resourceNames,\n    escapeHtml,\n    close() {\n      this.confirmName = '';\n      this.error = '';\n      this.chartsDeleteCrd = false;\n      this.chartsToRemoveIsApp = false;\n      this.$store.commit('action-menu/togglePromptRemove');\n    },\n\n    async remove(btnCB) {\n      if (this.doneLocation) {\n        // doneLocation will recompute to undefined when delete request completes\n        this.cachedDoneLocation = { ...this.doneLocation };\n      }\n\n      if (this.hasCustomRemove && this.$refs?.customPrompt?.remove) {\n        let handled = this.$refs.customPrompt.remove(btnCB);\n\n        // If the response is a promise, then wait for the promise\n        if (handled && handled.then) {\n          try {\n            handled = await handled;\n          } catch (err) {\n            this.error = err;\n            btnCB(false);\n\n            return;\n          }\n        }\n\n        // If the remove function for the custom dialog handled the request, it can return true or not return anything\n        // if it returned false, then it wants us to continue with the deletion logic below - this is useful\n        // where the custom dialog needs to delete additional resources - it handles those and retrurns false to get us\n        // to delete the main resource\n        if (handled === undefined || handled) {\n          return;\n        }\n      }\n      const serialRemove = this.toRemove.some((resource) => resource.removeSerially);\n\n      if (serialRemove) {\n        this.serialRemove(btnCB);\n      } else {\n        this.parallelRemove(btnCB);\n      }\n    },\n    async serialRemove(btnCB) {\n      try {\n        const spoofedTypes = this.getSpoofedTypes(this.toRemove);\n\n        for (const resource of this.toRemove) {\n          await resource.remove();\n        }\n\n        await this.refreshSpoofedTypes(spoofedTypes);\n        this.done();\n      } catch (err) {\n        this.error = err.message || err;\n        btnCB(false);\n      }\n    },\n    async parallelRemove(btnCB) {\n      try {\n        if (typeof this.toRemove[0].bulkRemove !== 'undefined') {\n          await this.toRemove[0].bulkRemove(this.toRemove, {});\n        } else {\n          await Promise.all(this.toRemove.map((resource) => resource.remove()));\n        }\n\n        const spoofedTypes = this.getSpoofedTypes(this.toRemove);\n\n        await this.refreshSpoofedTypes(spoofedTypes);\n\n        this.done();\n      } catch (err) {\n        this.error = err.message || err;\n        btnCB(false);\n      }\n    },\n    done() {\n      if ( this.cachedDoneLocation && !isEmpty(this.cachedDoneLocation) ) {\n        this.currentRouter.push(this.cachedDoneLocation);\n      }\n      this.close();\n    },\n    getSpoofedTypes(resources) {\n      const uniqueResourceTypes = uniq(this.toRemove.map((resource) => resource.type));\n\n      return uniqueResourceTypes.filter(this.$store.getters['type-map/isSpoofed']);\n    },\n\n    // If spoofed we need to reload the values as the server can't have watchers for them.\n    refreshSpoofedTypes(types) {\n      const inStore = this.$store.getters['currentProduct'].inStore;\n      const promises = types.map((type) => this.$store.dispatch(`${ inStore }/findAll`, { type, opt: { force: true } }, { root: true }));\n\n      return Promise.all(promises);\n    },\n\n    async chartAddCrdToRemove() {\n      try {\n        const res = await this.toRemove[0].relatedResourcesToRemove();\n\n        if (!this.toRemove.includes(res)) {\n          this.toRemove.push(res);\n        } else if (!this.chartsDeleteCrd) {\n          this.toRemove.pop(res);\n        }\n      } catch (err) {\n        this.error = err;\n        this.chartsDeleteCrd = false;\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <app-modal\n    v-if=\"showModal\"\n    custom-class=\"remove-modal\"\n    name=\"promptRemove\"\n    :width=\"400\"\n    height=\"auto\"\n    styles=\"max-height: 100vh;\"\n    @close=\"close\"\n  >\n    <Card\n      class=\"prompt-remove\"\n      :show-highlight-border=\"false\"\n    >\n      <template #title>\n        <h4 class=\"text-default-text\">\n          {{ t('promptRemove.title') }}\n        </h4>\n      </template>\n      <template #body>\n        <div class=\"mb-10\">\n          <template v-if=\"!hasCustomRemove\">\n            {{ t('promptRemove.attemptingToRemove', { type }) }} <span\n              v-clean-html=\"resourceNames(names, null, t)\"\n            />\n          </template>\n\n          <component\n            :is=\"removeComponent\"\n            v-if=\"hasCustomRemove\"\n            ref=\"customPrompt\"\n            v-model:value=\"toRemove\"\n            v-bind=\"$data\"\n            :close=\"close\"\n            :needs-confirm=\"needsConfirm\"\n            :value=\"toRemove\"\n            :names=\"names\"\n            :type=\"type\"\n            :done-location=\"doneLocation\"\n            @errors=\"e => error = e\"\n            @done=\"done\"\n          />\n          <div\n            v-if=\"needsConfirm\"\n            class=\"mt-10\"\n          >\n            <span\n              v-clean-html=\"t('promptRemove.confirmName', { nameToMatch: escapeHtml(nameToMatch) }, true)\"\n            />\n          </div>\n        </div>\n        <LabeledInput\n          v-if=\"needsConfirm\"\n          id=\"confirm\"\n          v-model:value=\"confirmName\"\n          v-focus\n          :data-testid=\"componentTestid + '-input'\"\n          type=\"text\"\n          :aria-label=\"t('promptRemove.confirmName', { nameToMatch: escapeHtml(nameToMatch) })\"\n        >\n          <div class=\"text-warning mb-10 mt-10\">\n            {{ warning }}\n          </div>\n          <div class=\"text-error mb-10 mt-10\">\n            {{ error }}\n          </div>\n          <div\n            v-if=\"!needsConfirm\"\n            class=\"text-info mt-20\"\n          >\n            {{ protip }}\n          </div>\n        </LabeledInput>\n        <div v-else-if=\"!hasCustomRemove\">\n          <div\n            v-if=\"warning\"\n            class=\"text-warning mb-10 mt-10\"\n          >\n            {{ warning }}\n          </div>\n          <div\n            v-if=\"error\"\n            class=\"text-error mb-10 mt-10\"\n          >\n            {{ error }}\n          </div>\n        </div>\n        <Checkbox\n          v-if=\"chartsToRemoveIsApp\"\n          v-model:value=\"chartsDeleteCrd\"\n          label-key=\"promptRemoveApp.removeCrd\"\n          class=\"mt-10 type\"\n          @update:value=\"chartAddCrdToRemove\"\n        />\n      </template>\n      <template #actions>\n        <button\n          class=\"btn role-secondary\"\n          @click=\"close\"\n        >\n          {{ t('generic.cancel') }}\n        </button>\n        <div class=\"spacer\" />\n        <AsyncButton\n          mode=\"delete\"\n          class=\"btn bg-error ml-10\"\n          :disabled=\"deleteDisabled\"\n          :data-testid=\"componentTestid + '-confirm-button'\"\n          @click=\"remove\"\n        />\n      </template>\n    </Card>\n  </app-modal>\n</template>\n\n<style lang='scss'>\n  .prompt-remove {\n    &.card-container {\n      box-shadow: none;\n    }\n    #confirm {\n      width: 90%;\n      margin-left: 3px;\n    }\n\n    .actions {\n      text-align: right;\n    }\n\n    .card-actions {\n      display: flex;\n\n      .spacer {\n        flex: 1;\n      }\n    }\n  }\n</style>\n"]}]}