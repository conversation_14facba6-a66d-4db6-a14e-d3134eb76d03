{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetClusters.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetClusters.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetClusters.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC,CAAC,CAAC,CAAC,<PERSON>AC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7E,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEvD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;;EAElC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC;QACJ;UACE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb;UACE,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;QACpB,CAAC;QACD,CAAC,CAAC,CAAC;MACL,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACZ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEzE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACrE,CAAC;IACH,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvD;EACF;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetClusters.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport ResourceTable from '@shell/components/ResourceTable';\nimport Tag from '@shell/components/Tag.vue';\nimport { STATE, NAME, AGE, FLEET_SUMMARY } from '@shell/config/table-headers';\nimport { FLEET, MANAGEMENT } from '@shell/config/types';\n\nexport default {\n  components: { ResourceTable, Tag },\n\n  props: {\n    rows: {\n      type:     Array,\n      required: true,\n    },\n\n    schema: {\n      type:    Object,\n      default: null,\n    },\n\n    loading: {\n      type:    Boolean,\n      default: false,\n    },\n    useQueryParamsForSimpleFiltering: {\n      type:    <PERSON>olean,\n      default: false\n    }\n  },\n\n  computed: {\n    MANAGEMENT_CLUSTER() {\n      return MANAGEMENT.CLUSTER;\n    },\n\n    headers() {\n      const out = [\n        STATE,\n        NAME,\n        {\n          name:     'bundlesReady',\n          labelKey: 'tableHeaders.bundlesReady',\n          value:    'status.display.readyBundles',\n          sort:     'status.summary.ready',\n          search:   false,\n        },\n        {\n          name:     'reposReady',\n          labelKey: 'tableHeaders.reposReady',\n          value:    'status.readyGitRepos',\n          sort:     'status.summary.ready',\n          search:   false,\n        },\n        FLEET_SUMMARY,\n        {\n          name:          'lastSeen',\n          labelKey:      'tableHeaders.lastSeen',\n          value:         'status.agent.lastSeen',\n          sort:          'status.agent.lastSeen',\n          search:        false,\n          formatter:     'LiveDate',\n          formatterOpts: { addSuffix: true },\n          width:         120,\n        },\n        AGE,\n      ];\n\n      return out;\n    },\n\n    pagingParams() {\n      const schema = this.$store.getters[`management/schemaFor`](FLEET.CLUSTER);\n\n      return {\n        singularLabel: this.$store.getters['type-map/labelFor'](schema),\n        pluralLabel:   this.$store.getters['type-map/labelFor'](schema, 99),\n      };\n    },\n  },\n\n  methods: {\n    toggleCustomLabels(row) {\n      row['displayCustomLabels'] = !row.displayCustomLabels;\n    }\n  }\n};\n</script>\n\n<template>\n  <ResourceTable\n    v-bind=\"$attrs\"\n    :schema=\"schema\"\n    :headers=\"headers\"\n    :rows=\"rows\"\n    :sub-rows=\"true\"\n    :loading=\"loading\"\n    :use-query-params-for-simple-filtering=\"useQueryParamsForSimpleFiltering\"\n    key-field=\"_key\"\n  >\n    <template #cell:workspace=\"{row}\">\n      <span v-if=\"row.type !== MANAGEMENT_CLUSTER && row.metadata.namespace\">{{ row.metadata.namespace }}</span>\n      <span\n        v-else\n        class=\"text-muted\"\n      >&mdash;</span>\n    </template>\n\n    <template #cell:reposReady=\"{row}\">\n      <span\n        v-if=\"!row.repoInfo\"\n        class=\"text-muted\"\n      >&mdash;</span>\n      <span\n        v-else-if=\"row.repoInfo.unready\"\n        class=\"text-warning\"\n      >{{ row.repoInfo.ready }}/{{ row.repoInfo.total }}</span>\n      <span v-else>{{ row.repoInfo.total }}</span>\n    </template>\n\n    <template #cell:bundlesReady=\"{row}\">\n      <span\n        v-if=\"row.bundleInfo.noValidData\"\n        class=\"text-muted\"\n      >&mdash;</span>\n      <span\n        v-else-if=\"row.bundleInfo.ready !== row.bundleInfo.total\"\n        class=\"text-warning\"\n      >{{ row.bundleInfo.ready }}/{{ row.bundleInfo.total }}</span>\n      <span\n        v-else\n        :class=\"{'text-error': !row.bundleInfo.total}\"\n      >{{ row.bundleInfo.total }}</span>\n    </template>\n\n    <template #sub-row=\"{fullColspan, row, onRowMouseEnter, onRowMouseLeave}\">\n      <tr\n        class=\"labels-row sub-row\"\n        @mouseenter=\"onRowMouseEnter\"\n        @mouseleave=\"onRowMouseLeave\"\n      >\n        <template v-if=\"row.customLabels.length\">\n          <td>&nbsp;</td>\n          <td>&nbsp;</td>\n          <td :colspan=\"fullColspan-2\">\n            <span\n              v-if=\"row.customLabels.length\"\n              class=\"mt-5\"\n            > {{ t('fleet.cluster.labels') }}:\n              <span\n                v-for=\"(label, i) in row.customLabels\"\n                :key=\"i\"\n                class=\"mt-5 labels\"\n              >\n                <Tag\n                  v-if=\"i < 7\"\n                  class=\"mr-5 label\"\n                >\n                  {{ label }}\n                </Tag>\n                <Tag\n                  v-else-if=\"i > 6 && row.displayCustomLabels\"\n                  class=\"mr-5 label\"\n                >\n                  {{ label }}\n                </Tag>\n              </span>\n              <a\n                v-if=\"row.customLabels.length > 7\"\n                href=\"#\"\n                @click.prevent=\"toggleCustomLabels(row)\"\n              >\n                {{ t(`fleet.cluster.${row.displayCustomLabels? 'hideLabels' : 'showLabels'}`) }}\n              </a>\n            </span>\n          </td>\n        </template>\n        <td\n          v-else\n          :colspan=\"fullColspan\"\n        >\n&nbsp;\n        </td>\n      </tr>\n    </template>\n  </ResourceTable>\n</template>\n\n<style lang='scss' scoped>\n  .labels-row {\n    td {\n      padding-top:0;\n      .tag {\n        margin-right: 5px;\n        display: inline-block;\n        margin-top: 2px;\n      }\n    }\n  }\n  .labels {\n    display: inline;\n    flex-wrap: wrap;\n\n    .label {\n      display: inline-block;\n      margin-top: 2px;\n    }\n  }\n</style>\n"]}]}