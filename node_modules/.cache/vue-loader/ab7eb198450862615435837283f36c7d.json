{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ValueFromResource.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ValueFromResource.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ValueFromResource.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE5D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MAC1B;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACf,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC5C,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAChD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;MACpD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;MAC9C,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACzC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACvC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC/C,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAER,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACxB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC3B,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC/B,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D;;IAEA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;IAEf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClD,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC/C,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3C,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5C;MACA,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAChD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3C,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5C;MACA,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAClE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC;IACP;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5H,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE5C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnD,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE9D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3D;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/F,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACpE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClG,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9G,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;MAErB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9D;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjE,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrE;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpF,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACrB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC/C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEhC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR;MACF;MACA,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;MAE7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACnD;QACF,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5D;QACF,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC7C,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAChF,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC1D;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IACD,CAAC,CAAC;EACJ;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ValueFromResource.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { CONFIG_MAP, SECRET, NAMESPACE } from '@shell/config/types';\nimport { get } from '@shell/utils/object';\nimport { _VIEW } from '@shell/config/query-params';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport { LabeledInput } from '@components/Form/LabeledInput';\n\nexport default {\n  emits: ['update:value', 'remove'],\n\n  components: {\n    LabeledSelect,\n    LabeledInput\n  },\n\n  props: {\n    mode: {\n      type:    String,\n      default: 'create'\n    },\n    value: {\n      type:    Object,\n      default: () => {\n        return { valueFrom: {} };\n      }\n    },\n    allConfigMaps: {\n      type:    Array,\n      default: () => []\n    },\n    allSecrets: {\n      type:    Array,\n      default: () => []\n    },\n    // filter resource options by namespace(s) selected in top nav\n    namespaced: {\n      type:    Boolean,\n      default: true\n    },\n    loading: {\n      default: false,\n      type:    Boolean\n    },\n  },\n\n  data() {\n    const typeOpts = [\n      { value: 'simple', label: 'Key/Value Pair' },\n      { value: 'resourceFieldRef', label: 'Resource' },\n      { value: 'configMapKeyRef', label: 'ConfigMap Key' },\n      { value: 'secretKeyRef', label: 'Secret key' },\n      { value: 'fieldRef', label: 'Pod Field' },\n      { value: 'secretRef', label: 'Secret' },\n      { value: 'configMapRef', label: 'ConfigMap' },\n    ];\n\n    const resourceKeyOpts = ['limits.cpu', 'limits.ephemeral-storage', 'limits.memory', 'requests.cpu', 'requests.ephemeral-storage', 'requests.memory'];\n    let type;\n\n    if (this.value.secretRef) {\n      type = 'secretRef';\n    } else if (this.value.configMapRef) {\n      type = 'configMapRef';\n    } else if (this.value.value) {\n      type = 'simple';\n    } else if (this.value.valueFrom) {\n      type = Object.keys((this.value.valueFrom))[0] || 'simple';\n    }\n\n    let refName;\n    let name;\n    let fieldPath;\n    let referenced;\n    let key;\n    let valStr;\n    const keys = [];\n\n    switch (type) {\n    case 'resourceFieldRef':\n      name = this.value.name;\n      refName = this.value.valueFrom[type].containerName;\n      key = this.value.valueFrom[type].resource || '';\n      break;\n    case 'configMapKeyRef':\n      name = this.value.name;\n      key = this.value.valueFrom[type].key || '';\n      refName = this.value.valueFrom[type].name;\n      referenced = this.allConfigMaps.filter((resource) => {\n        return resource.metadata.name === refName;\n      })[0];\n      if (referenced && referenced.data) {\n        keys.push(...Object.keys(referenced.data));\n      }\n      break;\n    case 'secretRef':\n    case 'configMapRef':\n      name = this.value.prefix;\n      refName = this.value[type].name;\n      break;\n    case 'secretKeyRef':\n      name = this.value.name;\n      key = this.value.valueFrom[type].key || '';\n      refName = this.value.valueFrom[type].name;\n      referenced = this.allSecrets.filter((resource) => {\n        return resource.metadata.name === refName;\n      })[0];\n      if (referenced && referenced.data) {\n        keys.push(...Object.keys(referenced.data));\n      }\n      break;\n    case 'fieldRef':\n      fieldPath = get(this.value.valueFrom, `${ type }.fieldPath`) || '';\n      name = this.value.name;\n      break;\n    default:\n      name = this.value.name;\n      valStr = this.value.value;\n      break;\n    }\n\n    return {\n      typeOpts, type, refName, referenced: refName, secrets: this.allSecrets, keys, key, fieldPath, name, resourceKeyOpts, valStr\n    };\n  },\n  computed: {\n    isView() {\n      return this.mode === _VIEW;\n    },\n\n    namespaces() {\n      if (this.namespaced) {\n        const map = this.$store.getters.namespaces();\n\n        return Object.keys(map).filter((key) => map[key]);\n      } else {\n        const inStore = this.$store.getters['currentStore'](NAMESPACE);\n\n        return this.$store.getters[`${ inStore }/all`](NAMESPACE);\n      }\n    },\n\n    sourceOptions() {\n      if (this.type === 'configMapKeyRef' || this.type === 'configMapRef') {\n        return this.allConfigMaps.filter((map) => this.namespaces.includes(map?.metadata?.namespace));\n      } else if (this.type === 'secretRef' || this.type === 'secretKeyRef') {\n        return this.allSecrets.filter((secret) => this.namespaces.includes(secret?.metadata?.namespace));\n      } else {\n        return [];\n      }\n    },\n\n    needsSource() {\n      return this.type !== 'simple' && this.type !== 'resourceFieldRef' && this.type !== 'fieldRef' && !!this.type;\n    },\n\n    sourceLabel() {\n      let out;\n      const { type } = this;\n\n      if (!type) {\n        return;\n      }\n\n      switch (type) {\n      case 'secretKeyRef':\n      case 'secretRef':\n        out = 'workload.container.command.fromResource.secret';\n        break;\n      case 'configMapKeyRef':\n      case 'configMapRef':\n        out = 'workload.container.command.fromResource.configMap';\n        break;\n      default:\n        out = 'workload.container.command.fromResource.source.label';\n      }\n\n      return this.t(out);\n    },\n\n    nameLabel() {\n      if (this.type === 'configMapRef' || this.type === 'secretRef') {\n        return this.t('workload.container.command.fromResource.prefix');\n      } else {\n        return this.t('workload.container.command.fromResource.name.label');\n      }\n    },\n\n    extraColumn() {\n      return ['resourceFieldRef', 'configMapKeyRef', 'secretKeyRef'].includes(this.type);\n    },\n  },\n\n  watch: {\n    type() {\n      this.referenced = null;\n      this.key = '';\n      this.refName = '';\n      this.keys = [];\n      this.key = '';\n      this.valStr = '';\n      this.fieldPath = '';\n    },\n\n    referenced(neu, old) {\n      if (neu) {\n        if ((neu.type === SECRET || neu.type === CONFIG_MAP) && neu.data) {\n          this.keys = Object.keys(neu.data);\n        }\n        this.refName = neu?.metadata?.name;\n      }\n      this.updateRow();\n    },\n  },\n\n  methods: {\n    updateRow() {\n      if (!this.name?.length && !this.refName?.length) {\n        if (this.type !== 'fieldRef') {\n          this.$emit('update:value', null);\n\n          return;\n        }\n      }\n      let out = { name: this.name || this.refName };\n\n      switch (this.type) {\n      case 'configMapKeyRef':\n      case 'secretKeyRef':\n        out.valueFrom = {\n          [this.type]: {\n            key: this.key, name: this.refName, optional: false\n          }\n        };\n        break;\n      case 'resourceFieldRef':\n        out.valueFrom = {\n          [this.type]: {\n            containerName: this.refName, divisor: 1, resource: this.key\n          }\n        };\n        break;\n      case 'fieldRef':\n        if (!this.fieldPath || !this.fieldPath.length) {\n          out = null; break;\n        }\n        out.valueFrom = { [this.type]: { apiVersion: 'v1', fieldPath: this.fieldPath } };\n        break;\n      case 'simple':\n        out.value = this.valStr;\n        break;\n      default:\n        delete out.name;\n        out.prefix = this.name;\n        out[this.type] = { name: this.refName, optional: false };\n      }\n      this.$emit('update:value', out);\n    },\n    get\n  }\n};\n</script>\n\n<template>\n  <div class=\"var-row\">\n    <div class=\"type\">\n      <LabeledSelect\n        v-model:value=\"type\"\n        :mode=\"mode\"\n        :multiple=\"false\"\n        :options=\"typeOpts\"\n        option-label=\"label\"\n        :searchable=\"false\"\n        :reduce=\"e=>e.value\"\n        :label=\"t('workload.container.command.fromResource.type')\"\n        @update:value=\"updateRow\"\n      />\n    </div>\n\n    <div class=\"name\">\n      <LabeledInput\n        v-model:value=\"name\"\n        :label=\"nameLabel\"\n        :placeholder=\"t('workload.container.command.fromResource.name.placeholder')\"\n        :mode=\"mode\"\n        @update:value=\"updateRow\"\n      />\n    </div>\n\n    <div\n      v-if=\"type==='simple'\"\n      class=\"single-value\"\n    >\n      <LabeledInput\n        v-model:value=\"valStr\"\n        :label=\"t('workload.container.command.fromResource.value.label')\"\n        :placeholder=\"t('workload.container.command.fromResource.value.placeholder')\"\n        :mode=\"mode\"\n        @update:value=\"updateRow\"\n      />\n    </div>\n\n    <template v-else-if=\"needsSource\">\n      <div :class=\"{'single-value': type === 'configMapRef' || type === 'secretRef'}\">\n        <LabeledSelect\n          v-model:value=\"referenced\"\n          :options=\"sourceOptions\"\n          :multiple=\"false\"\n          :get-option-label=\"opt=>get(opt, 'metadata.name') || opt\"\n          :get-option-key=\"opt=>opt.id|| opt\"\n          :mode=\"mode\"\n          :label=\"sourceLabel\"\n          :loading=\"loading\"\n        />\n      </div>\n      <div v-if=\"type!=='secretRef' && type!== 'configMapRef'\">\n        <LabeledSelect\n          v-model:value=\"key\"\n          :multiple=\"false\"\n          :options=\"keys\"\n          :mode=\"mode\"\n          option-label=\"label\"\n          :label=\"t('workload.container.command.fromResource.key.label')\"\n          :loading=\"loading\"\n          @update:value=\"updateRow\"\n        />\n      </div>\n    </template>\n\n    <template v-else-if=\"type==='resourceFieldRef'\">\n      <div>\n        <LabeledInput\n          v-model:value=\"refName\"\n          :label=\"t('workload.container.command.fromResource.containerName')\"\n          :placeholder=\"t('workload.container.command.fromResource.source.placeholder')\"\n          :mode=\"mode\"\n          @update:value=\"updateRow\"\n        />\n      </div>\n      <div>\n        <LabeledSelect\n          v-model:value=\"key\"\n          :label=\"t('workload.container.command.fromResource.key.label')\"\n          :multiple=\"false\"\n          :options=\"resourceKeyOpts\"\n          :mode=\"mode\"\n          :searchable=\"false\"\n          :placeholder=\"t('workload.container.command.fromResource.key.placeholder', null, true)\"\n          @update:value=\"updateRow\"\n        />\n      </div>\n    </template>\n\n    <template v-else>\n      <div class=\"single-value\">\n        <LabeledInput\n          v-model:value=\"fieldPath\"\n          :placeholder=\"t('workload.container.command.fromResource.key.placeholder', null, true)\"\n          :label=\"t('workload.container.command.fromResource.key.label')\"\n          :mode=\"mode\"\n          @update:value=\"updateRow\"\n        />\n      </div>\n    </template>\n    <div class=\"remove\">\n      <button\n        v-if=\"!isView\"\n        type=\"button\"\n        class=\"btn role-link\"\n        @click.stop=\"$emit('remove')\"\n      >\n        {{ t('generic.remove') }}\n      </button>\n    </div>\n  </div>\n</template>\n\n<style lang='scss' scoped>\n.var-row{\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr 1fr 100px;\n  grid-column-gap: 20px;\n  margin-bottom: 10px;\n  align-items: center;\n\n  .single-value {\n    grid-column: span 2;\n  }\n\n  .remove BUTTON {\n    padding: 0px;\n  }\n}\n\n</style>\n"]}]}