{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ActionDropdown.vue?vue&type=style&index=0&id=da1fb5ac&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ActionDropdown.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ActionDropdown.vue"], "names": [], "mappings": ";AA6FA,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CA<PERSON>;;EAEpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChC;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACjC;;EAEA,CAAC,CAAC,CAAC,EAAE;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACb;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB;;EAEA,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAExC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACjB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5B,EAAE;UACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B;MACF;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnB;IACF;;IAEA,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC9B;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACZ;QACF;MACF;IACF;EACF;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACd;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEhB,CAAC,EAAE;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;UAEnB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UAChB;QACF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB;MACF;;IAEF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ActionDropdown.vue", "sourceRoot": "", "sourcesContent": ["<script>\nexport default {\n  name: 'ActionDropdown',\n\n  props: {\n    size: {\n      type:    String,\n      default: '' // possible values are xs, sm, lg. empty is default .btn\n    },\n    // whether this is a button and dropdown (default) or dropdown that looks like a button/dropdown\n    dualAction: {\n      type:    Boolean,\n      default: true\n    },\n\n    disableButton: {\n      type:    Boolean,\n      default: false\n    }\n  },\n\n  computed: {\n    buttonSize() {\n      const { size } = this;\n      let out;\n\n      switch (size) {\n      case '':\n        out = 'btn';\n        break;\n      case 'xs':\n        out = 'btn btn-xs';\n        break;\n      case 'sm':\n        out = 'btn btn-sm';\n        break;\n      case 'lg':\n        out = 'btn btn-lg';\n        break;\n      default:\n      }\n\n      return out;\n    }\n  },\n\n  methods: {\n    hasSlot(name = 'default') {\n      return !!this.$slots[name] || !!this.$slots.name();\n    },\n\n    // allows parent components to programmatically open the dropdown\n    togglePopover() {\n      // this.$refs.popoverButton.click();\n    },\n  }\n};\n</script>\n<template>\n  <div class=\"dropdown-button-group\">\n    <div\n      class=\"dropdown-button bg-primary\"\n      :class=\"{'one-action':!dualAction, [buttonSize]:true, 'disabled': disableButton}\"\n    >\n      <v-dropdown\n        placement=\"bottom\"\n        :container=\"false\"\n        :disabled=\"disableButton\"\n        :flip=\"false\"\n      >\n        <slot\n          name=\"button-content\"\n          :buttonSize=\"buttonSize\"\n        >\n          <button\n            ref=\"popoverButton\"\n            class=\"icon-container bg-primary no-left-border-radius\"\n            :class=\"buttonSize\"\n            :disabled=\"disableButton\"\n            type=\"button\"\n          >\n            Button <i class=\"icon icon-chevron-down\" />\n          </button>\n        </slot>\n        <template #popper>\n          <slot name=\"popover-content\" />\n        </template>\n      </v-dropdown>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\">\n// load here instead of component so SSR render isn't all wonky\n.dropdown-button-group {\n  $xs-padding: 2px 3px;\n\n  .no-left-border-radius {\n    border-top-left-radius: 0px;\n    border-bottom-left-radius: 0px;\n  }\n\n  .no-right-border-radius {\n    border-top-right-radius: 0px;\n    border-bottom-right-radius: 0px;\n  }\n\n  .btn {\n    line-height: normal;\n    border: 0px;\n  }\n\n  .btn-xs,\n  .btn-group-xs > .btn,\n  .btn-xs .btn-label {\n      padding: $xs-padding;\n      font-size: 13px;\n  }\n\n  // this matches the top/bottom padding of the default button\n  $trigger-padding: 15px 10px 15px 10px;\n  $xs-trigger-padding: 2px 4px 4px 4px;\n  $sm-trigger-padding: 10px 10px 10px 10px;\n  $lg-trigger-padding: 18px 10px 10px 10px;\n\n  .v-popper {\n    .text-right {\n      margin-top: 5px;\n    }\n    .trigger {\n      height: 100%;\n      .icon-container {\n        height: 100%;\n        padding: 10px 10px 10px 10px;\n        i {\n          transform: scale(1);\n        }\n        &.btn-xs {\n          padding: $xs-trigger-padding;\n        }\n        &.btn-sm {\n          padding: $sm-trigger-padding;\n        }\n        &.btn-lg {\n          padding: $lg-trigger-padding;\n        }\n        &:focus {\n          outline-style: none;\n          box-shadow: none;\n          border-color: transparent;\n        }\n      }\n    }\n  }\n\n  .dropdown-button {\n    background: var(--tooltip-bg);\n    color: var(--link-text);\n    padding: 0;\n    display: inline-flex;\n\n    .wrapper-content {\n      button {\n        border-right: 0px;\n      }\n    }\n\n    &>*, .icon-chevron-down {\n      color: var(--primary);\n      background-color: rgba(0,0,0,0);\n    }\n\n    &.bg-primary:hover {\n      background: var(--accent-btn-hover);\n    }\n\n    &.one-action {\n      position: relative;\n      &>.btn {\n        padding: 15px 35px 15px 15px;\n      }\n      .v-popper{\n        .trigger{\n          position: absolute;\n          top: 0px;\n          right: 0px;\n          left: 0px;\n          bottom: 0px;\n          BUTTON {\n            position: absolute;\n            right: 0px;\n          }\n        }\n      }\n    }\n  }\n  .v-popper__popper {\n    border: none;\n  }\n  .v-popper__popper {\n    margin-top: 0px;\n\n    &[data-popper-placement^=\"bottom\"] {\n      .v-popper__arrow-container {\n        display: none;\n      }\n    }\n\n    .v-popper__inner {\n      color: var(--dropdown-text);\n      background-color: var(--dropdown-bg);\n      border: 1px solid var(--dropdown-border);\n      padding: 0px;\n      text-align: left;\n\n      LI {\n        padding: 10px;\n\n        &.divider {\n          padding-top: 0px;\n          padding-bottom: 0px;\n\n          > .divider-inner {\n            padding: 0;\n            border-bottom: 1px solid var(--dropdown-divider);\n            width: 125%;\n            margin: 0 auto;\n          }\n        }\n\n        &:not(.divider):hover {\n          background-color: var(--dropdown-hover-bg);\n          color: var(--dropdown-hover-text);\n          cursor: pointer;\n        }\n      }\n\n    }\n  }\n\n  //header\n  .user-info {\n    border-bottom: 1px solid var(--border);\n    display: block;\n  }\n}\n\n</style>\n"]}]}