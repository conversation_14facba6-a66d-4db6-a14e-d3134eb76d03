{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ServicePorts.vue?vue&type=style&index=0&id=27fd5f15&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ServicePorts.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAkcmVtb3ZlOiA3NTsKICAkY2hlY2tib3g6IDc1OwoKICAudGl0bGUgewogICAgbWFyZ2luLWJvdHRvbTogMTBweDsKCiAgICAucmVhZC1mcm9tLWZpbGUgewogICAgICBmbG9hdDogcmlnaHQ7CiAgICB9CiAgfQoKICAucG9ydHMtaGVhZGVycywgLnBvcnRzLXJvd3sKICAgIGRpc3BsYXk6IGdyaWQ7CiAgICBncmlkLWNvbHVtbi1nYXA6ICRjb2x1bW4tZ3V0dGVyOwogICAgbWFyZ2luLWJvdHRvbTogMTBweDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CgogICAgJi5zaG93LXByb3RvY29sewogICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDIzJSAyMyUgMTAlIDE1JSAxNSUgMTAlOwogICAgICAmOm5vdCguc2hvdy1ub2RlLXBvcnQpewogICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMzElIDMxJSAxMCUgMTUlIDEwJTsKICAgICAgfQogICAgfQogICAgJi5zaG93LW5vZGUtcG9ydDpub3QoLnNob3ctcHJvdG9jb2wpewogICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDI4JSAyOCUgMTUlIDE1JSAxMCU7CiAgICB9CiAgfQoKICAucG9ydHMtaGVhZGVycyB7CiAgICBjb2xvcjogdmFyKC0taW5wdXQtbGFiZWwpOwogIH0KCiAgLnRvZ2dsZS1ob3N0LXBvcnRzIHsKICAgIGNvbG9yOiB2YXIoLS1wcmltYXJ5KTsKICB9CgogIC5yZW1vdmUgQlVUVE9OIHsKICAgIHBhZGRpbmc6IDBweDsKICB9CgogIC5wb3J0cy1yb3cgewogICAgPiBkaXYgewogICAgICBoZWlnaHQ6IDEwMCU7CiAgICB9CgogICAgLnBvcnQtcHJvdG9jb2wgOmRlZXAoKSB7CiAgICAgIC51bmxhYmVsZWQtc2VsZWN0IHsKICAgICAgICAudi1zZWxlY3QuaW5saW5lIHsKICAgICAgICAgIG1hcmdpbi10b3A6IDJweDsKICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9CgogIC5mb290ZXIgewogICAgbWFyZ2luLXRvcDogMTBweDsKICAgIG1hcmdpbi1sZWZ0OiA1cHg7CgogICAgLnByb3RpcCB7CiAgICAgIGZsb2F0OiByaWdodDsKICAgICAgcGFkZGluZzogNXB4IDA7CiAgICB9CiAgfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ServicePorts.vue"], "names": [], "mappings": ";CAoRC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;EAEb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACd;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC5C;IACF;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5C;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACd;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,EAAE,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACd;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjB;MACF;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAChB;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ServicePorts.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport debounce from 'lodash/debounce';\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\nimport { removeAt } from '@shell/utils/array';\nimport { clone } from '@shell/utils/object';\nimport Select from '@shell/components/form/Select';\nimport Error from '@shell/components/form/Error';\n\nexport default {\n  emits: ['update:value'],\n\n  components: { Select, Error },\n  props:      {\n    value: {\n      type:    Array,\n      default: null,\n    },\n    mode: {\n      type:    String,\n      default: _EDIT,\n    },\n    specType: {\n      type:    String,\n      default: 'ClusterIP',\n    },\n    padLeft: {\n      type:    Boolean,\n      default: false,\n    },\n    autoAddIfEmpty: {\n      type:    Boolean,\n      default: true,\n    },\n    rules: {\n      default:   () => [],\n      type:      Array,\n      // we only want functions in the rules array\n      validator: (rules) => rules.every((rule) => ['function'].includes(typeof rule))\n    }\n  },\n\n  data() {\n    const rows = clone(this.value || []);\n\n    return { rows };\n  },\n\n  computed: {\n    isView() {\n      return this.mode === _VIEW;\n    },\n\n    showAdd() {\n      return !this.isView;\n    },\n\n    showRemove() {\n      return !this.isView;\n    },\n\n    showProtocol() {\n      return this.specType !== 'NodePort';\n    },\n\n    showNodePort() {\n      return this.specType === 'NodePort' || this.specType === 'LoadBalancer';\n    },\n\n    protocolOptions() {\n      return ['TCP', 'UDP'];\n    },\n  },\n\n  created() {\n    this.queueUpdate = debounce(this.update, 500);\n  },\n\n  mounted() {\n    if ( this.isView ) {\n      return;\n    }\n\n    if (this.autoAddIfEmpty && this.mode !== _EDIT && this?.rows.length < 1) {\n      // don't focus on mount because we'll pull focus from name/namespace input\n      this.add(false);\n    }\n  },\n\n  methods: {\n    add(focus = true) {\n      this.rows.push({\n        name:       '',\n        port:       null,\n        protocol:   'TCP',\n        targetPort: null,\n      });\n\n      this.queueUpdate();\n\n      if (this.rows.length > 0 && focus) {\n        this.$nextTick(() => {\n          const inputs = this.$refs['port-name'];\n\n          inputs[inputs.length - 1].focus();\n        });\n      }\n    },\n\n    remove(idx) {\n      removeAt(this.rows, idx);\n      this.queueUpdate();\n    },\n\n    update() {\n      if ( this.isView ) {\n        return;\n      }\n\n      this.$emit('update:value', this.rows);\n    }\n  },\n};\n</script>\n\n<template>\n  <div>\n    <div v-if=\"rows.length\">\n      <div\n        class=\"ports-headers\"\n        :class=\"{'show-protocol':showProtocol, 'show-node-port':showNodePort}\"\n      >\n        <span\n          v-if=\"padLeft\"\n          class=\"left\"\n        />\n        <span class=\"port-name\">\n          <t k=\"servicePorts.rules.name.label\" />\n        </span>\n        <span class=\"port\">\n          <t k=\"servicePorts.rules.listening.label\" />\n          <i\n            v-clean-tooltip=\"t('servicesPage.listeningPorts')\"\n            class=\"icon icon-info flex\"\n          />\n          <span class=\"text-error\">*</span>\n        </span>\n        <span\n          v-if=\"showProtocol\"\n          class=\"port-protocol\"\n        >\n          <t k=\"servicePorts.rules.protocol.label\" />\n        </span>\n        <span class=\"target-port\">\n          <t k=\"servicePorts.rules.target.label\" />\n          <i\n            v-clean-tooltip=\"t('servicesPage.targetPorts')\"\n            class=\"icon icon-info flex\"\n          />\n          <span class=\"text-error\">*</span>\n\n        </span>\n        <span\n          v-if=\"showNodePort\"\n          class=\"node-port\"\n        >\n          <t k=\"servicePorts.rules.node.label\" />\n        </span>\n        <span\n          v-if=\"showRemove\"\n          class=\"remove\"\n        />\n      </div>\n      <div\n        v-for=\"(row, idx) in rows\"\n        :key=\"idx\"\n        class=\"ports-row\"\n        :class=\"{'show-protocol':showProtocol, 'show-node-port':showNodePort}\"\n      >\n        <div\n          v-if=\"padLeft\"\n          class=\"left\"\n        />\n        <div class=\"port-name\">\n          <span v-if=\"isView\">{{ row.name }}</span>\n          <input\n            v-else\n            ref=\"port-name\"\n            v-model.number=\"row.name\"\n            type=\"text\"\n            :placeholder=\"t('servicePorts.rules.name.placeholder')\"\n            @input=\"queueUpdate\"\n          >\n        </div>\n        <div class=\"port\">\n          <span v-if=\"isView\">{{ row.port }}</span>\n          <input\n            v-else\n            ref=\"port\"\n            v-model.number=\"row.port\"\n            type=\"number\"\n            min=\"1\"\n            max=\"65535\"\n            :placeholder=\"t('servicePorts.rules.listening.placeholder')\"\n            @input=\"queueUpdate\"\n          >\n        </div>\n        <div\n          v-if=\"showProtocol\"\n          class=\"port-protocol\"\n        >\n          <span v-if=\"isView\">{{ row.protocol }}</span>\n          <Select\n            v-else\n            v-model:value=\"row.protocol\"\n            :options=\"protocolOptions\"\n            @update:value=\"queueUpdate\"\n          />\n        </div>\n        <div class=\"target-port\">\n          <span v-if=\"isView\">{{ row.targetPort }}</span>\n          <input\n            v-else\n            v-model=\"row.targetPort\"\n            :placeholder=\"t('servicePorts.rules.target.placeholder')\"\n            @input=\"queueUpdate\"\n          >\n        </div>\n        <div\n          v-if=\"showNodePort\"\n          class=\"node-port\"\n        >\n          <span v-if=\"isView\">{{ row.nodePort }}</span>\n          <input\n            v-else\n            v-model.number=\"row.nodePort\"\n            type=\"number\"\n            min=\"1\"\n            max=\"65535\"\n            :placeholder=\"t('servicePorts.rules.node.placeholder')\"\n            @input=\"queueUpdate\"\n          >\n        </div>\n        <div\n          v-if=\"showRemove\"\n          class=\"remove\"\n        >\n          <button\n            type=\"button\"\n            class=\"btn role-link\"\n            @click=\"remove(idx)\"\n          >\n            <t k=\"generic.remove\" />\n          </button>\n        </div>\n        <Error\n          :value=\"{...row, idx}\"\n          :rules=\"rules\"\n        />\n      </div>\n    </div>\n    <div\n      v-if=\"showAdd\"\n      class=\"footer\"\n    >\n      <button\n        type=\"button\"\n        class=\"btn role-tertiary add\"\n        @click=\"add()\"\n      >\n        <t k=\"generic.add\" />\n      </button>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n $remove: 75;\n  $checkbox: 75;\n\n  .title {\n    margin-bottom: 10px;\n\n    .read-from-file {\n      float: right;\n    }\n  }\n\n  .ports-headers, .ports-row{\n    display: grid;\n    grid-column-gap: $column-gutter;\n    margin-bottom: 10px;\n    align-items: center;\n\n    &.show-protocol{\n      grid-template-columns: 23% 23% 10% 15% 15% 10%;\n      &:not(.show-node-port){\n        grid-template-columns: 31% 31% 10% 15% 10%;\n      }\n    }\n    &.show-node-port:not(.show-protocol){\n      grid-template-columns: 28% 28% 15% 15% 10%;\n    }\n  }\n\n  .ports-headers {\n    color: var(--input-label);\n  }\n\n  .toggle-host-ports {\n    color: var(--primary);\n  }\n\n  .remove BUTTON {\n    padding: 0px;\n  }\n\n  .ports-row {\n    > div {\n      height: 100%;\n    }\n\n    .port-protocol :deep() {\n      .unlabeled-select {\n        .v-select.inline {\n          margin-top: 2px;\n        }\n      }\n    }\n  }\n\n  .footer {\n    margin-top: 10px;\n    margin-left: 5px;\n\n    .protip {\n      float: right;\n      padding: 5px 0;\n    }\n  }\n</style>\n"]}]}