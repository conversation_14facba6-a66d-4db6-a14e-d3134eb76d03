{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/WorkspaceSwitcher.vue?vue&type=style&index=0&id=656b2637&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/WorkspaceSwitcher.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/WorkspaceSwitcher.vue"], "names": [], "mappings": ";AAuGA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACP,CAAC,CAAC,CAAC,EAAE,CAAC;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACP,CAAC,CAAC,CAAC,EAAE,CAAC;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACvF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACX;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACf;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACX;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACzD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAChB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACpD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC7D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACd", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/WorkspaceSwitcher.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { LAST_NAMESPACE, WORKSPACE } from '@shell/store/prefs';\nimport { mapState } from 'vuex';\nimport Select from '@shell/components/form/Select';\nimport { WORKSPACE_ANNOTATION } from '@shell/config/labels-annotations';\n\nexport default {\n  name:       'WorkspaceSwitcher',\n  components: { Select },\n\n  computed: {\n    ...mapState(['allWorkspaces', 'workspace', 'allNamespaces', 'defaultNamespace', 'getActiveNamespaces']),\n\n    value: {\n      get() {\n        return this.workspace || this.namespace || this.options[0]?.value;\n      },\n\n      set(value) {\n        if (value !== this.value) {\n          this.$store.commit('updateWorkspace', { value, getters: this.$store.getters });\n          this.$store.dispatch('prefs/set', { key: WORKSPACE, value });\n        }\n      },\n    },\n\n    options() {\n      if (this.allWorkspaces.length) {\n        const out = this.allWorkspaces.map((obj) => {\n          return {\n            label: obj.nameDisplay,\n            value: obj.id,\n          };\n        });\n\n        return out;\n      }\n\n      // If doesn't have workspaces (e.g. no permissions)\n      // Then find the workspaces from the annotation.\n      return this.allNamespaces.filter((item) => {\n        return item.metadata.annotations[WORKSPACE_ANNOTATION] === WORKSPACE;\n      }).map((obj) => {\n        return {\n          label: obj.nameDisplay,\n          value: obj.id,\n        };\n      });\n    },\n  },\n\n  watch: {\n    options(curr, prev) {\n      if (curr.length === 0) {\n        this.value = '';\n      }\n\n      const currentExists = curr.find((item) => item.value === this.value);\n\n      if (curr.length && !currentExists) {\n        this.value = curr[0]?.value;\n      }\n    },\n  },\n\n  created() {\n    // in fleet standard user with just the project owner and global git repo permissions\n    // returns 'default'\n    const initValue = !this.workspace ? this.$store.getters['prefs/get'](LAST_NAMESPACE) : '';\n\n    this.value = (initValue === 'default' || initValue === '') && this.options.length ? this.options[0].value : initValue;\n  },\n\n  data() {\n    return { namespace: this.$store.getters['prefs/get'](LAST_NAMESPACE) };\n  },\n\n  methods: {\n    focus() {\n      this.$refs.select.$refs.search.focus();\n    },\n  },\n};\n</script>\n\n<template>\n  <div\n    class=\"filter\"\n    data-testid=\"workspace-switcher\"\n  >\n    <Select\n      ref=\"select\"\n      v-model:value=\"value\"\n      label=\"label\"\n      :options=\"options\"\n      :clearable=\"false\"\n      :reduce=\"(opt) => opt.value\"\n    />\n    <!--button v-shortkey.once=\"['w']\" class=\"hide\" @shortkey=\"focus()\" /-->\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.filter {\n  min-width: 220px;\n  max-width: 100%;\n  display: inline-block;\n}\n\n.filter.show-masked :deep() .unlabeled-select:not(.masked-dropdown) {\n  position: absolute;\n  left: 0;\n  top: 0;\n  height: 0;\n  opacity: 0;\n  visibility: hidden;\n}\n\n.filter:not(.show-masked) :deep() .unlabeled-select.masked-dropdown {\n  position: absolute;\n  left: 0;\n  top: 0;\n  height: 0;\n  opacity: 0;\n  visibility: hidden;\n}\n\n.filter :deep() .unlabeled-select.has-more .v-select:not(.vs--open) .vs__dropdown-toggle {\n  overflow: hidden;\n}\n\n.filter :deep() .unlabeled-select.has-more .v-select.vs--open .vs__dropdown-toggle {\n  height: max-content;\n  background-color: var(--header-bg);\n}\n\n.filter :deep() .unlabeled-select {\n  background-color: transparent;\n  border: 0;\n}\n\n.filter :deep() .unlabeled-select:not(.focused) {\n  min-height: 0;\n}\n\n.filter :deep() .unlabeled-select:not(.view):hover .vs__dropdown-menu {\n  background: var(--dropdown-bg);\n}\n\n.filter :deep() .unlabeled-select .v-select.inline {\n  margin: 0;\n}\n\n.filter :deep() .unlabeled-select .v-select .vs__selected {\n  margin: $input-padding-sm;\n  user-select: none;\n  color: var(--header-btn-text);\n}\n\n.filter :deep() .unlabeled-select .vs__search::placeholder {\n  color: var(--header-btn-text);\n}\n\n.filter :deep() .unlabeled-select INPUT:hover {\n  background-color: transparent;\n}\n\n.filter :deep() .unlabeled-select .vs__dropdown-toggle {\n  background: rgba(0, 0, 0, 0.05);\n  border-radius: var(--border-radius);\n  border: 1px solid var(--header-btn-bg);\n  color: var(--header-btn-text);\n  height: 40px;\n  max-width: 100%;\n  padding-top: 0;\n}\n\n.filter :deep() .unlabeled-select .vs__deselect:after {\n  color: var(--header-btn-text);\n}\n\n.filter :deep() .unlabeled-select .v-select .vs__actions:after {\n  fill: var(--header-btn-text) !important;\n  color: var(--header-btn-text) !important;\n}\n\n.filter :deep() .unlabeled-select INPUT[type='search'] {\n  padding: 7px;\n}\n</style>\n"]}]}