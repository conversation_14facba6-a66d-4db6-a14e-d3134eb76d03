{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetSummary.vue?vue&type=style&index=0&id=3dcd87dc&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetSummary.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgIC5mbGV4d3JhcCAuZmxlZXQtc3RhdHVzIHsKICAgIG1heC13aWR0aDogNTAlOwogICAgbWFyZ2luLXJpZ2h0OiAxNXB4OwoKICAgICY6bGFzdC1jaGlsZCB7CiAgICAgIG1hcmdpbjogMAogICAgfQogIH0KICAuY291bnRib3ggewogICAgbWluLXdpZHRoOiAxNTBweDsKICAgIHdpZHRoOiAxMi41JTsKICAgIG1hcmdpbi1ib3R0b206IDEwcHg7CiAgfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetSummary.vue"], "names": [], "mappings": ";GAoMG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV;EACF;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetSummary.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { STATES, STATES_ENUM } from '@shell/plugins/dashboard-store/resource-class';\nimport FleetStatus from '@shell/components/fleet/FleetStatus';\n\nconst getResourcesDefaultState = (labelGetter, stateKey) => {\n  return [\n    STATES_ENUM.READY,\n    STATES_ENUM.NOT_READY,\n    STATES_ENUM.WAIT_APPLIED,\n    STATES_ENUM.MODIFIED,\n    STATES_ENUM.MISSING,\n    STATES_ENUM.ORPHANED,\n    STATES_ENUM.UNKNOWN,\n  ].reduce((acc, state) => {\n    acc[state] = {\n      count:  0,\n      color:  STATES[state].color,\n      label:  labelGetter(`${ stateKey }.${ state }`, null, STATES[state].label ),\n      status: state\n    };\n\n    return acc;\n  }, {});\n};\n\nconst getBundlesDefaultState = (labelGetter, stateKey) => {\n  return [\n    STATES_ENUM.READY,\n    STATES_ENUM.INFO,\n    STATES_ENUM.WARNING,\n    STATES_ENUM.NOT_READY,\n    STATES_ENUM.ERROR,\n    STATES_ENUM.ERR_APPLIED,\n    STATES_ENUM.WAIT_APPLIED,\n    STATES_ENUM.UNKNOWN,\n  ].reduce((acc, state) => {\n    acc[state] = {\n      count:  0,\n      color:  STATES[state].color,\n      label:  labelGetter(`${ stateKey }.${ state }`, null, STATES[state].label ),\n      status: state\n    };\n\n    return acc;\n  }, {});\n};\n\nexport default {\n\n  name: 'FleetSummary',\n\n  components: { FleetStatus },\n\n  props: {\n    bundles: {\n      type:    Array,\n      default: () => [],\n    },\n    value: {\n      type:     Object,\n      required: true,\n    },\n\n    stateKey: {\n      type:    String,\n      default: 'fleet.fleetSummary.state'\n    },\n  },\n\n  computed: {\n\n    repoName() {\n      return this.value.metadata.name;\n    },\n\n    repoNamespace() {\n      return this.value.metadata.namespace;\n    },\n\n    bundleCounts() {\n      const resources = this.bundles.filter((item) => item.namespace === this.repoNamespace && item.repoName === this.repoName);\n\n      if (!resources.length) {\n        return [];\n      }\n\n      const out = { ...getBundlesDefaultState(this.$store.getters['i18n/withFallback'], this.stateKey) };\n\n      resources.forEach(({ status, metadata }) => {\n        if (!status) {\n          out[STATES_ENUM.UNKNOWN].count += 1;\n\n          return;\n        }\n\n        const k = status?.summary?.ready > 0 && status?.summary.desiredReady === status?.summary?.ready;\n\n        if (k) {\n          out.ready.count += 1;\n\n          return;\n        }\n\n        const state = metadata.state?.name?.toLowerCase();\n\n        if (state && out[state]) {\n          out[state].count += 1;\n\n          return;\n        }\n\n        const { conditions } = status;\n\n        const notReady = conditions.find(({ transitioning, message }) => {\n          return transitioning && !message.includes(STATES_ENUM.ERROR) && !message.toLowerCase().includes(STATES_ENUM.ERR_APPLIED);\n        });\n\n        if (!!notReady) {\n          out.notready.count += 1;\n\n          return;\n        }\n\n        // check conditions\n        const errApplied = conditions.find(({ error, message }) => !!error && message.toLowerCase().includes(STATES_ENUM.ERR_APPLIED));\n\n        if (errApplied) {\n          out[STATES_ENUM.ERR_APPLIED].count += 1;\n\n          return;\n        }\n\n        const errorState = conditions.find(({ error, message }) => !!error && message.toLowerCase().includes(STATES_ENUM.ERROR));\n\n        if (out[errorState]) {\n          out[errorState].count += 1;\n\n          return;\n        }\n\n        out.unknown.count += 1;\n      });\n\n      return Object.values(out).map((item) => {\n        item.value = item.count;\n\n        return item;\n      });\n    },\n\n    resourceCounts() {\n      const out = { ...getResourcesDefaultState(this.$store.getters['i18n/withFallback'], this.stateKey) };\n      const resourceStatuses = this.value.allResourceStatuses;\n\n      Object.entries(resourceStatuses.states)\n        .filter(([_, count]) => count > 0)\n        .forEach(([state, count]) => {\n          const k = state?.toLowerCase();\n\n          if (out[k]) {\n            out[k].count += count;\n          } else {\n            out.unknown.count += count;\n          }\n        });\n\n      return Object.values(out).map((item) => {\n        item.value = item.count;\n\n        return item;\n      });\n    },\n\n  },\n\n};\n</script>\n\n<template>\n  <div class=\"row flexwrap\">\n    <FleetStatus\n      v-if=\"bundleCounts.length\"\n      title=\"Bundles\"\n      :values=\"bundleCounts\"\n      value-key=\"count\"\n      data-testid=\"gitrepo-bundle-summary\"\n    />\n    <FleetStatus\n      title=\"Resources\"\n      :values=\"resourceCounts\"\n      value-key=\"count\"\n      data-testid=\"gitrepo-deployment-summary\"\n    />\n  </div>\n</template>\n<style lang=\"scss\" scoped>\n   .flexwrap .fleet-status {\n    max-width: 50%;\n    margin-right: 15px;\n\n    &:last-child {\n      margin: 0\n    }\n  }\n  .countbox {\n    min-width: 150px;\n    width: 12.5%;\n    margin-bottom: 10px;\n  }\n</style>\n"]}]}