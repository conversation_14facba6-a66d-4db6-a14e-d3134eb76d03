{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/UnitInput.vue?vue&type=style&index=0&id=1cc21b9e&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/UnitInput.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLmFkZG9uLndpdGgtdG9vbHRpcCB7CiAgICBwYWRkaW5nLXJpZ2h0OiA0MnB4OwogIH0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/UnitInput.vue"], "names": [], "mappings": ";EA6PE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/UnitInput.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { parseSi, formatSi, UNITS, FRACTIONAL } from '@shell/utils/units';\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport { _EDIT } from '@shell/config/query-params';\n\nexport default {\n  components: { LabeledInput },\n\n  emits: ['update:value', 'update:validation', 'change', 'blur'],\n\n  props: {\n    /**\n     * Convert output to string\n     * Output will also be a string regardless of this prop if outputModifier = true\n     */\n    outputAs: {\n      type:    String,\n      default: 'number',\n    },\n\n    /**\n     * Append exponential modifier in output, eg \"123Mi\"\n     * If this is false while inputExponent is true, the output val will be converted to base units\n     * eg user is views in terms of MiB but integer values corresponding to B are actually emitted\n     */\n    outputModifier: {\n      type:    Boolean,\n      default: false\n    },\n\n    /**\n     * Set modifier on base unit - positive vals map to UNITS array, negative to FRACTIONAL\n     * String input values with SI notation will be converted to this measurement unit,\n     * eg \"1Gi\" will become \"1024Mi\" if this is set to 2\n     * UNITS = ['', 'K', 'M', 'G', 'T', 'P', 'E', 'Z', 'Y'];\n     * FRACTIONAL = ['', 'm', 'u', 'n', 'p', 'f', 'a', 'z', 'y'];\n     */\n    inputExponent: {\n      type:    Number,\n      default: 0,\n    },\n\n    /**\n     * Combines with inputExponent to make displayed unit.\n     * Use 'suffix' if the input's units are strictly for display\n     */\n    baseUnit: {\n      type:    String,\n      default: 'B',\n    },\n\n    /**\n     * Hide arrows on number input when it overlaps with the unit\n     */\n    hideArrows: {\n      type:    Boolean,\n      default: false\n    },\n\n    /**\n     * If set to 1024, binary modifier will be used eg MiB instead of MB\n     */\n    increment: {\n      type:    Number,\n      default: 1000,\n    },\n\n    /**\n     * Ignore baseUnit and inputExponent in favor of a display-only suffix\n     * display/emit integers without SI conversion\n     */\n    suffix: {\n      type:    String,\n      default: null,\n    },\n\n    /**\n     * LabeledInput props\n     */\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n\n    value: {\n      type:    [Number, String],\n      default: null\n    },\n\n    label: {\n      type:    String,\n      default: null\n    },\n\n    labelKey: {\n      type:    String,\n      default: null\n    },\n\n    tooltip: {\n      type:    [String, Object],\n      default: null\n    },\n\n    tooltipKey: {\n      type:    String,\n      default: null\n    },\n\n    required: {\n      type:    Boolean,\n      default: false,\n    },\n\n    min: {\n      type:    [Number, String],\n      default: 0\n    },\n\n    placeholder: {\n      type:    [String, Number],\n      default: ''\n    },\n\n    /**\n     * Optionally delay on input while typing\n     */\n    delay: {\n      type:    Number,\n      default: 0\n    },\n\n    positive: {\n      type:    Boolean,\n      default: false,\n    },\n  },\n\n  computed: {\n    unit() {\n      let out;\n\n      if ( this.inputExponent >= 0 ) {\n        out = UNITS[this.inputExponent];\n      } else {\n        out = FRACTIONAL[-1 * this.inputExponent];\n      }\n      if (this.increment === 1024 && out) {\n        out += 'i';\n      }\n\n      return out;\n    },\n\n    /**\n     * Parse string with unit modifier to base unit eg \"1m\" -> 0.001\n     */\n    parsedValue() {\n      return typeof this.value === 'string' ? parseSi(this.value) : this.value;\n    },\n\n    /**\n     * Convert integer value\n     */\n    displayValue() {\n      let displayValue = '';\n\n      if ( this.parsedValue || this.parsedValue === 0) {\n        displayValue = formatSi(this.parsedValue, {\n          increment:   this.increment,\n          addSuffix:   false,\n          maxExponent: this.inputExponent,\n          minExponent: this.inputExponent,\n        });\n      }\n\n      return displayValue ;\n    },\n\n    /**\n     * Conditionally display value with unit or SI suffix\n     */\n    displayUnit() {\n      if (this.suffix) {\n        return this.suffix;\n      }\n\n      return this.unit + this.baseUnit;\n    }\n  },\n\n  methods: {\n    focus() {\n      const comp = this.$refs.value;\n\n      if (comp) {\n        comp.focus();\n      }\n    },\n\n    update(inputValue) {\n      let out = inputValue === '' ? null : inputValue;\n\n      if (this.positive && inputValue < 0) {\n        out = 0;\n      }\n\n      if (this.outputModifier) {\n        out = out === null ? null : `${ parseInt(inputValue) }${ this.unit }`;\n      } else if ( this.outputAs === 'string' ) {\n        out = out === null ? '' : `${ inputValue }`;\n      } else if (out) {\n        out = this.unit ? parseSi(`${ out }${ this.unit }`) : parseInt(out);\n      }\n\n      this.$emit('update:value', out);\n    },\n  }\n};\n</script>\n\n<template>\n  <LabeledInput\n    ref=\"value\"\n    :value=\"displayValue\"\n    v-bind=\"$attrs\"\n    type=\"number\"\n    :min=\"min\"\n    :mode=\"mode\"\n    :label=\"label\"\n    :delay=\"delay\"\n    :label-key=\"labelKey\"\n    :tooltip=\"tooltip\"\n    :tooltip-key=\"tooltipKey\"\n    :required=\"required\"\n    :placeholder=\"placeholder\"\n    :hide-arrows=\"hideArrows\"\n    @update:value=\"update\"\n    @blur=\"update($event.target.value)\"\n  >\n    <template #suffix>\n      <div\n        v-if=\"displayUnit\"\n        class=\"addon\"\n        :class=\"{'with-tooltip': tooltip || tooltipKey}\"\n      >\n        {{ displayUnit }}\n      </div>\n    </template>\n  </LabeledInput>\n</template>\n\n<style lang=\"scss\" scoped>\n  .addon.with-tooltip {\n    padding-right: 42px;\n  }\n</style>\n"]}]}