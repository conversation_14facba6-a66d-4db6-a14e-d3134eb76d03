{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Card/Card.vue?vue&type=template&id=35b78590&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Card/Card.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdgogICAgaWQ9ImZvY3VzLXRyYXAtY2FyZC1jb250YWluZXItZWxlbWVudCIKICAgIGNsYXNzPSJjYXJkLWNvbnRhaW5lciIKICAgIDpjbGFzcz0ieydoaWdobGlnaHQtYm9yZGVyJzogc2hvd0hpZ2hsaWdodEJvcmRlciwgJ2NhcmQtc3RpY2t5Jzogc3RpY2t5fSIKICAgIGRhdGEtdGVzdGlkPSJjYXJkIgogID4KICAgIDxkaXYgY2xhc3M9ImNhcmQtd3JhcCI+CiAgICAgIDxkaXYKICAgICAgICBjbGFzcz0iY2FyZC10aXRsZSIKICAgICAgICBkYXRhLXRlc3RpZD0iY2FyZC10aXRsZS1zbG90IgogICAgICA+CiAgICAgICAgPHNsb3QgbmFtZT0idGl0bGUiPgogICAgICAgICAge3sgdGl0bGUgfX0KICAgICAgICA8L3Nsb3Q+CiAgICAgIDwvZGl2PgogICAgICA8aHI+CiAgICAgIDxkaXYKICAgICAgICBjbGFzcz0iY2FyZC1ib2R5IgogICAgICAgIGRhdGEtdGVzdGlkPSJjYXJkLWJvZHktc2xvdCIKICAgICAgPgogICAgICAgIDxzbG90IG5hbWU9ImJvZHkiPgogICAgICAgICAge3sgY29udGVudCB9fQogICAgICAgIDwvc2xvdD4KICAgICAgPC9kaXY+CiAgICAgIDxkaXYKICAgICAgICB2LWlmPSJzaG93QWN0aW9ucyIKICAgICAgICBjbGFzcz0iY2FyZC1hY3Rpb25zIgogICAgICAgIGRhdGEtdGVzdGlkPSJjYXJkLWFjdGlvbnMtc2xvdCIKICAgICAgPgogICAgICAgIDxzbG90IG5hbWU9ImFjdGlvbnMiPgogICAgICAgICAgPGJ1dHRvbgogICAgICAgICAgICBjbGFzcz0iYnRuIHJvbGUtcHJpbWFyeSIKICAgICAgICAgICAgQGNsaWNrPSJidXR0b25BY3Rpb24iCiAgICAgICAgICA+CiAgICAgICAgICAgIHt7IGJ1dHRvblRleHQgfX0KICAgICAgICAgIDwvYnV0dG9uPgogICAgICAgIDwvc2xvdD4KICAgICAgPC9kaXY+CiAgICA8L2Rpdj4KICA8L2Rpdj4K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Card/Card.vue"], "names": [], "mappings": ";EA4EE,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB;IACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B;QACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC;MACH,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B;QACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC;QACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB;YACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Card/Card.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent, PropType } from 'vue';\nimport { useBasicSetupFocusTrap } from '@shell/composables/focusTrap';\n\nexport default defineComponent({\n\n  name:  'Card',\n  props: {\n    /**\n     * The card's title.\n     */\n    title: {\n      type:    String,\n      default: ''\n    },\n    /**\n     * The text content for the card's body.\n     */\n    content: {\n      type:    String,\n      default: ''\n    },\n    /**\n     * The function to invoke when the default action button is clicked.\n     */\n    buttonAction: {\n      type:    Function as PropType<(event: MouseEvent) => void>,\n      default: (): void => { }\n    },\n    /**\n     * The text for the default action button.\n     */\n    buttonText: {\n      type:    String,\n      default: 'go'\n    },\n    /**\n     * Toggles the card's highlight-border class.\n     */\n    showHighlightBorder: {\n      type:    Boolean,\n      default: true\n    },\n    /**\n     * Toggles the card's Actions section.\n     */\n    showActions: {\n      type:    Boolean,\n      default: true\n    },\n    sticky: {\n      type:    Boolean,\n      default: false,\n    },\n    triggerFocusTrap: {\n      type:    Boolean,\n      default: false,\n    },\n  },\n  setup(props) {\n    if (props.triggerFocusTrap) {\n      useBasicSetupFocusTrap('#focus-trap-card-container-element', {\n        // needs to be false because of import YAML modal from header\n        // where the YAML editor itself is a focus trap\n        // and we can't have it superseed the \"escape key\" to blur that UI element\n        // In this case the focus trap moves the focus out of the modal\n        // correctly once it closes because of the \"onBeforeUnmount\" trigger\n        escapeDeactivates: false,\n        allowOutsideClick: true,\n      });\n    }\n  }\n});\n</script>\n\n<template>\n  <div\n    id=\"focus-trap-card-container-element\"\n    class=\"card-container\"\n    :class=\"{'highlight-border': showHighlightBorder, 'card-sticky': sticky}\"\n    data-testid=\"card\"\n  >\n    <div class=\"card-wrap\">\n      <div\n        class=\"card-title\"\n        data-testid=\"card-title-slot\"\n      >\n        <slot name=\"title\">\n          {{ title }}\n        </slot>\n      </div>\n      <hr>\n      <div\n        class=\"card-body\"\n        data-testid=\"card-body-slot\"\n      >\n        <slot name=\"body\">\n          {{ content }}\n        </slot>\n      </div>\n      <div\n        v-if=\"showActions\"\n        class=\"card-actions\"\n        data-testid=\"card-actions-slot\"\n      >\n        <slot name=\"actions\">\n          <button\n            class=\"btn role-primary\"\n            @click=\"buttonAction\"\n          >\n            {{ buttonText }}\n          </button>\n        </slot>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang='scss'>\n .card-container {\n  &.highlight-border {\n    border-left: 5px solid var(--primary);\n  }\n  border-radius: var(--border-radius);\n  display: flex;\n  flex-basis: 40%;\n  margin: 10px;\n  min-height: 100px;\n  padding: 10px;\n  box-shadow: 0 0 20px var(--shadow);\n  &:not(.top) {\n    align-items: top;\n    flex-direction: row;\n    justify-content: start;\n  }\n  .card-wrap {\n    width: 100%;\n  }\n   & .card-body {\n    color: var(--input-label);\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n   }\n   & .card-actions {\n     align-self: end;\n     display: flex;\n     padding-top: 20px;\n   }\n   & .card-title {\n    align-items: center;\n    display: flex;\n    width: 100%;\n     h5 {\n       margin: 0;\n     }\n    .flex-right {\n      margin-left: auto;\n    }\n   }\n\n  // Sticky mode will stick header and footer to top and bottom with content in the middle scrolling\n   &.card-sticky {\n      // display: flex;\n      // flex-direction: column;\n      overflow: hidden;\n\n    .card-wrap {\n      display: flex;\n      flex-direction: column;\n\n      .card-body {\n        justify-content: flex-start;\n        overflow: auto;\n      }\n\n      > * {\n        flex: 0;\n      }\n\n      .card-body {\n        flex: 1;\n      }\n    }\n   }\n }\n</style>\n"]}]}