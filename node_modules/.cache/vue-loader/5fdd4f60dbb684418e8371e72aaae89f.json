{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/PodSecurityAdmission.vue?vue&type=template&id=0b918ae7&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/PodSecurityAdmission.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/PodSecurityAdmission.vue"], "names": [], "mappings": ";EAiNE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC3C,CAAC,CAAC,CAAC;;IAEH,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC;MACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC;QACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC;YACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC;QACH,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACpI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC;UACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChD,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACtD,CAAC,CAAC,CAAC;;MAEH,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC;QACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACnG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC5J,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/PodSecurityAdmission.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent } from 'vue';\nimport { _VIEW, _CREATE } from '@shell/config/query-params';\nimport LabeledSelect from '@shell/components/form/LabeledSelect.vue';\nimport Checkbox from '@components/Form/Checkbox/Checkbox.vue';\nimport LabeledInput from '@components/Form/LabeledInput/LabeledInput.vue';\nimport { PSADimension, PSAMode } from '@shell/types/resources/pod-security-admission';\nimport {\n  PSADefaultLevel,\n  PSADefaultVersion, PSADimensions, PSALevels, PSAModes\n} from '@shell/config/pod-security-admission';\nimport { pickBy, toDictionary } from '@shell/utils/object';\n\ninterface PSAControl { active: boolean, level: string, version: string }\nconst getPsaControl = (): PSAControl => ({\n  active:  false,\n  level:   PSADefaultLevel,\n  version: ''\n});\n\n// Type and function for exemptions form builder\ninterface PSAExemptionControl { active: boolean, value: string }\nconst getExemptionControl = (): PSAExemptionControl => ({\n  active: false,\n  value:  ''\n});\n\nexport default defineComponent({\n  emits: ['updateLabels', 'updateExemptions'],\n\n  components: {\n    Checkbox, LabeledSelect, LabeledInput\n  },\n  props: {\n    /**\n     * List of labels used for the resource\n     * Note: PSA labels are always paired\n     */\n    labels: {\n      type:    Object as () => Record<string, string>,\n      default: () => ({})\n    },\n\n    labelsAlwaysActive: {\n      type:    Boolean,\n      default: false\n    },\n\n    /**\n     * Map editing capabilities to the component\n     */\n    mode: {\n      type:     String,\n      required: true\n    },\n\n    /**\n     * List of exemptions used for the resource\n     */\n    exemptions: {\n      type:    Object as () => Record<PSADimension, string[]>,\n      default: () => ({} as Record<PSADimension, string[]>)\n    },\n\n    /**\n     * Prefix used for setting labels\n     */\n    labelsPrefix: {\n      type:    String,\n      default: ''\n    },\n\n    /**\n     * Inherited global identifier prefix for tests\n     * Define a term based on the parent component to avoid conflicts on multiple components\n     */\n    componentTestid: {\n      type:    String,\n      default: 'pod-security-admission'\n    }\n  },\n\n  data() {\n    return {\n      // Generate PSA form controls\n      psaControls:           toDictionary(PSAModes, getPsaControl) as Record<PSAMode, PSAControl>,\n      psaExemptionsControls: toDictionary(PSADimensions, getExemptionControl) as Record<PSADimension, PSAExemptionControl>,\n      options:               PSALevels.map((level) => ({\n        value: level,\n        label: this.t(`podSecurityAdmission.labels.${ level }`)\n      })),\n    };\n  },\n\n  watch: {},\n\n  computed: {\n    isView(): boolean {\n      return this.mode === _VIEW;\n    },\n\n    /**\n     * Enable exemption form if any\n     */\n    hasExemptions(): boolean {\n      return Object.keys(this.exemptions).length > 0;\n    },\n  },\n\n  created() {\n    // Assign values to the form, overriding existing values\n    this.psaControls = {\n      ...this.psaControls,\n      ...this.getPsaControls()\n    };\n\n    this.psaExemptionsControls = this.getPsaExemptions();\n\n    // Emit initial value on creation if labels always active, as default predefined values are required\n    if (this.mode === _CREATE && this.labelsAlwaysActive) {\n      this.updateLabels();\n      this.updateExemptions();\n    }\n  },\n\n  methods: {\n    /**\n     * Filter out existing PSA labels and emit existing labels with new PSA ones\n     */\n    updateLabels(): void {\n      const nonPSALabels = pickBy(this.labels, (_, key) => !key.includes(this.labelsPrefix));\n      const labels = PSAModes.reduce((acc, mode) => {\n        return this.psaControls[mode]?.active || this.labelsAlwaysActive ? {\n          ...acc,\n          // Set default level if none\n          [`${ this.labelsPrefix }${ mode }`]:         this.psaControls[mode].level || PSADefaultLevel,\n          // Set default version if none\n          [`${ this.labelsPrefix }${ mode }-version`]: this.psaControls[mode].version || PSADefaultVersion\n        } : acc;\n      }, nonPSALabels);\n\n      this.$emit('updateLabels', labels);\n    },\n\n    /**\n     * Emit active exemptions in required format\n     */\n    updateExemptions(): void {\n      const exemptions = PSADimensions.reduce((acc, dimension) => {\n        const value = this.psaExemptionsControls[dimension].value.split(',').map((value) => value.trim());\n        const active = this.psaExemptionsControls[dimension].active;\n\n        return {\n          ...acc,\n          [dimension]: active && value ? value : []\n        };\n      }, {});\n\n      this.$emit('updateExemptions', exemptions);\n    },\n\n    /**\n     * Generate form controls based on PSA labels in the provided dictionary\n     */\n    getPsaControls(): Record<PSAMode, PSAControl> {\n      return PSAModes.reduce((acc, mode) => {\n        const level = this.labels[`${ this.labelsPrefix }${ mode }`];\n        // Retrieve version, hiding the value 'latest' from the user\n        const version = (this.labels[`${ this.labelsPrefix }${ mode }-version`] || '').replace(PSADefaultVersion, '');\n\n        return level ? {\n          ...acc,\n          [mode]: {\n            active: true,\n            level,\n            version\n          }\n        } : acc;\n      }, {} as Record<PSAMode, PSAControl>);\n    },\n\n    /**\n     * Generate form exemptions based on PSA exemptions provided dictionary\n     */\n    getPsaExemptions(): Record<PSADimension, PSAExemptionControl> {\n      return PSADimensions.reduce((acc, dimension) => {\n        const values = (this.exemptions[dimension] || []).map((value) => value.trim()).join(',');\n\n        return {\n          ...acc,\n          [dimension]: {\n            active: !!values.length,\n            value:  values\n          }\n        };\n      }, {}) as Record<PSADimension, PSAExemptionControl>;\n    },\n\n    /**\n     * Add checks on input for PSA controls to be active or not, allowing white cases\n     */\n    isPsaControlDisabled(active: boolean): boolean {\n      return !this.labelsAlwaysActive && (!active || this.isView);\n    }\n  }\n});\n</script>\n\n<template>\n  <div class=\"psa\">\n    <!-- PSA -->\n    <p class=\"mb-30\">\n      <t k=\"podSecurityAdmission.description\" />\n    </p>\n\n    <div\n      v-for=\"(psaControl, level, i) in psaControls\"\n      :key=\"i\"\n      class=\"row row--y-center mb-20\"\n    >\n      <span class=\"col span-2\">\n        <Checkbox\n          v-if=\"!labelsAlwaysActive\"\n          v-model:value=\"psaControl.active\"\n          :data-testid=\"componentTestid + '--psaControl-' + i + '-active'\"\n          :label=\"level\"\n          :label-key=\"`podSecurityAdmission.labels.${ level }`\"\n          :disabled=\"isView\"\n          @update:value=\"updateLabels()\"\n        />\n        <p v-else>\n          <t\n            :id=\"`psa-label-for-level-${ level }`\"\n            :k=\"`podSecurityAdmission.labels.${level}`\"\n          />\n        </p>\n      </span>\n\n      <span class=\"col span-4\">\n        <LabeledSelect\n          v-model:value=\"psaControl.level\"\n          :aria-labelledby=\"`psa-label-for-level-${ level }`\"\n          :data-testid=\"componentTestid + '--psaControl-' + i + '-level'\"\n          :disabled=\"isPsaControlDisabled(psaControl.active)\"\n          :options=\"options\"\n          :mode=\"mode\"\n          @update:value=\"updateLabels()\"\n        />\n      </span>\n\n      <span class=\"col span-4\">\n        <LabeledInput\n          v-model:value=\"psaControl.version\"\n          :data-testid=\"componentTestid + '--psaControl-' + i + '-version'\"\n          :disabled=\"isPsaControlDisabled(psaControl.active)\"\n          :options=\"options\"\n          :placeholder=\"t('podSecurityAdmission.version.placeholder', { psaControl: mode })\"\n          :mode=\"mode\"\n          :aria-label=\"`${t(`podSecurityAdmission.labels.${level}`)} - ${t('podSecurityAdmission.version.placeholder', { psaControl: mode })}`\"\n          @update:value=\"updateLabels()\"\n        />\n      </span>\n    </div>\n\n    <!-- Exemptions -->\n    <template v-if=\"hasExemptions\">\n      <slot name=\"title\">\n        <h3>\n          <t k=\"podSecurityAdmission.exemptions.title\" />\n        </h3>\n      </slot>\n      <p class=\"mb-30\">\n        <t k=\"podSecurityAdmission.exemptions.description\" />\n      </p>\n\n      <div\n        v-for=\"(psaExemptionsControl, dimension, i) in psaExemptionsControls\"\n        :key=\"i\"\n        class=\"row row--y-center mb-20\"\n      >\n        <span class=\"col span-2\">\n          <Checkbox\n            v-model:value=\"psaExemptionsControl.active\"\n            :data-testid=\"componentTestid + '--psaExemptionsControl-' + i + '-active'\"\n            :label=\"dimension\"\n            :label-key=\"`podSecurityAdmission.labels.${ dimension }`\"\n            :disabled=\"isView\"\n            @update:value=\"updateExemptions()\"\n          />\n        </span>\n        <span class=\"col span-8\">\n          <LabeledInput\n            v-model:value=\"psaExemptionsControl.value\"\n            :data-testid=\"componentTestid + '--psaExemptionsControl-' + i + '-value'\"\n            :disabled=\"(isView || !psaExemptionsControl.active)\"\n            :options=\"options\"\n            :placeholder=\"t('podSecurityAdmission.exemptions.placeholder', { psaExemptionsControl: dimension })\"\n            :mode=\"mode\"\n            :aria-label=\"`${t(`podSecurityAdmission.labels.${ dimension }`)} - ${t('podSecurityAdmission.exemptions.placeholder', { psaExemptionsControl: dimension })}`\"\n            @update:value=\"updateExemptions()\"\n          />\n        </span>\n      </div>\n    </template>\n  </div>\n</template>\n"]}]}