{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/Project.vue?vue&type=template&id=0752acec&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/Project.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxkaXY+DQogICAgPGRpdiBjbGFzcz0iaGVhZGVycyBtYi0xMCI+DQogICAgICA8ZGl2IGNsYXNzPSJtci0xMCI+DQogICAgICAgIDxsYWJlbD57eyB0KCdyZXNvdXJjZVF1b3RhLmhlYWRlcnMucmVzb3VyY2VUeXBlJykgfX08L2xhYmVsPg0KICAgICAgPC9kaXY+DQogICAgICA8ZGl2IGNsYXNzPSJtci0yMCI+DQogICAgICAgIDxsYWJlbD57eyB0KCdyZXNvdXJjZVF1b3RhLmhlYWRlcnMucHJvamVjdExpbWl0JykgfX08L2xhYmVsPg0KICAgICAgPC9kaXY+DQogICAgICA8ZGl2IGNsYXNzPSJtci0xMCI+DQogICAgICAgIDxsYWJlbD57eyB0KCdyZXNvdXJjZVF1b3RhLmhlYWRlcnMubmFtZXNwYWNlRGVmYXVsdExpbWl0JykgfX08L2xhYmVsPg0KICAgICAgPC9kaXY+DQogICAgPC9kaXY+DQogICAgPEFycmF5TGlzdA0KICAgICAgdi1tb2RlbDp2YWx1ZT0idHlwZVZhbHVlcyINCiAgICAgIGxhYmVsPSJSZXNvdXJjZXMiDQogICAgICA6YWRkLWxhYmVsPSJ0KCdyZXNvdXJjZVF1b3RhLmFkZC5sYWJlbCcpIg0KICAgICAgOmRlZmF1bHQtYWRkLXZhbHVlPSJyZW1haW5pbmdUeXBlcygpWzBdID8gcmVtYWluaW5nVHlwZXMoKVswXS52YWx1ZSA6ICcnIg0KICAgICAgOmFkZC1hbGxvd2VkPSJyZW1haW5pbmdUeXBlcygpLmxlbmd0aCA+IDAiDQogICAgICA6bW9kZT0ibW9kZSINCiAgICAgIEByZW1vdmU9ImVtaXRSZW1vdmUiDQogICAgPg0KICAgICAgPHRlbXBsYXRlICNjb2x1bW5zPSJwcm9wcyI+DQogICAgICAgIDxSb3cNCiAgICAgICAgICA6dmFsdWU9InZhbHVlIg0KICAgICAgICAgIDptb2RlPSJtb2RlIg0KICAgICAgICAgIDp0eXBlcz0icmVtYWluaW5nVHlwZXModHlwZVZhbHVlc1twcm9wcy5pXSkiDQogICAgICAgICAgOnR5cGU9InR5cGVWYWx1ZXNbcHJvcHMuaV0iDQogICAgICAgICAgQGlucHV0PSIkZW1pdCgnaW5wdXQnLCAkZXZlbnQpIg0KICAgICAgICAgIEB0eXBlLWNoYW5nZT0idXBkYXRlVHlwZShwcm9wcy5pLCAkZXZlbnQpIg0KICAgICAgICAvPg0KICAgICAgPC90ZW1wbGF0ZT4NCiAgICA8L0FycmF5TGlzdD4NCiAgPC9kaXY+DQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/Project.vue"], "names": [], "mappings": ";EAsDE,CAAC,CAAC,CAAC,CAAC;IACF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7D,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7D,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtE,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACxE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB;MACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/Project.vue", "sourceRoot": "", "sourcesContent": ["<script>\r\nimport ArrayList from '@shell/components/form/ArrayList';\r\nimport Row from './ProjectRow';\r\nimport { QUOTA_COMPUTED } from './shared';\r\n\r\nexport default {\r\n  emits: ['remove', 'input'],\r\n\r\n  components: { ArrayList, Row },\r\n\r\n  props: {\r\n    mode: {\r\n      type:     String,\r\n      required: true,\r\n    },\r\n    value: {\r\n      type:    Object,\r\n      default: () => {\r\n        return {};\r\n      }\r\n    },\r\n    types: {\r\n      type:    Array,\r\n      default: () => {\r\n        return [];\r\n      }\r\n    }\r\n  },\r\n\r\n  data() {\r\n    this.value['spec'] = this.value.spec || {};\r\n    this.value.spec['namespaceDefaultResourceQuota'] = this.value.spec.namespaceDefaultResourceQuota || { limit: {} };\r\n    this.value.spec['resourceQuota'] = this.value.spec.resourceQuota || { limit: {} };\r\n\r\n    return { typeValues: Object.keys(this.value.spec.resourceQuota.limit) };\r\n  },\r\n\r\n  computed: { ...QUOTA_COMPUTED },\r\n\r\n  methods: {\r\n    updateType(i, type) {\r\n      this.typeValues[i] = type;\r\n    },\r\n    remainingTypes(currentType) {\r\n      return this.mappedTypes\r\n        .filter((mappedType) => !this.typeValues.includes(mappedType.value) || mappedType.value === currentType);\r\n    },\r\n    emitRemove(data) {\r\n      this.$emit('remove', data.row?.value);\r\n    }\r\n  },\r\n};\r\n</script>\r\n<template>\r\n  <div>\r\n    <div class=\"headers mb-10\">\r\n      <div class=\"mr-10\">\r\n        <label>{{ t('resourceQuota.headers.resourceType') }}</label>\r\n      </div>\r\n      <div class=\"mr-20\">\r\n        <label>{{ t('resourceQuota.headers.projectLimit') }}</label>\r\n      </div>\r\n      <div class=\"mr-10\">\r\n        <label>{{ t('resourceQuota.headers.namespaceDefaultLimit') }}</label>\r\n      </div>\r\n    </div>\r\n    <ArrayList\r\n      v-model:value=\"typeValues\"\r\n      label=\"Resources\"\r\n      :add-label=\"t('resourceQuota.add.label')\"\r\n      :default-add-value=\"remainingTypes()[0] ? remainingTypes()[0].value : ''\"\r\n      :add-allowed=\"remainingTypes().length > 0\"\r\n      :mode=\"mode\"\r\n      @remove=\"emitRemove\"\r\n    >\r\n      <template #columns=\"props\">\r\n        <Row\r\n          :value=\"value\"\r\n          :mode=\"mode\"\r\n          :types=\"remainingTypes(typeValues[props.i])\"\r\n          :type=\"typeValues[props.i]\"\r\n          @input=\"$emit('input', $event)\"\r\n          @type-change=\"updateType(props.i, $event)\"\r\n        />\r\n      </template>\r\n    </ArrayList>\r\n  </div>\r\n</template>\r\n<style lang=\"scss\" scoped>\r\n.headers {\r\n    display: flex;\r\n    flex-direction: row;\r\n    justify-content: space-evenly;\r\n    align-items: center;\r\n    border-bottom: 1px solid var(--border);\r\n    height: 30px;\r\n    width: calc(100% - 75px);\r\n\r\n    div {\r\n        width: 100%;\r\n    }\r\n}\r\n</style>\r\n"]}]}