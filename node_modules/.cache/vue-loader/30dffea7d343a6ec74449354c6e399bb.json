{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/LocaleSelector.vue?vue&type=style&index=0&id=3699b81c&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/LocaleSelector.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5hZHZhbmNlZCB7CiAgdXNlci1zZWxlY3Q6IG5vbmU7CiAgcGFkZGluZzogMCA1cHg7CiAgbGluZS1oZWlnaHQ6IDQwcHg7CiAgZm9udC1zaXplOiAxNXB4OwogIGZvbnQtd2VpZ2h0OiA1MDA7Cn0KLmNvbnRlbnQgewogIGJhY2tncm91bmQ6IHZhcigtLW5hdi1hY3RpdmUpOwogIHBhZGRpbmc6IDEwcHg7CiAgbWFyZ2luLXRvcDogNnB4OwogIGJvcmRlci1yYWRpdXM6IDRweDsKfQoKLmhhbmQ6Zm9jdXMtdmlzaWJsZSB7CiAgQGluY2x1ZGUgZm9jdXMtb3V0bGluZTsKICBvdXRsaW5lLW9mZnNldDogNHB4Owp9CgoubG9jYWxlLWNob29zZXIgewogIGN1cnNvcjogcG9pbnRlcjsKCiAgJjpob3ZlciB7CiAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7CiAgfQp9CgoubG9jYWxlLWxvZ2luLWNvbnRhaW5lcjpmb2N1cy12aXNpYmxlIHsKICBAaW5jbHVkZSBmb2N1cy1vdXRsaW5lOwogIG91dGxpbmUtb2Zmc2V0OiAycHg7Cn0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/LocaleSelector.vue"], "names": [], "mappings": ";AAiJA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAClB;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACpB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACrB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACvB;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACrB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/LocaleSelector.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapGetters } from 'vuex';\nimport Select from '@shell/components/form/Select.vue';\n\nexport default {\n  name: 'LocalSelector',\n\n  components: { Select },\n\n  props: {\n    mode: {\n      type:    String,\n      default: ''\n    },\n    showIcon: {\n      type:    Boolean,\n      default: true\n    }\n  },\n\n  data() {\n    return { isLocaleSelectorOpen: false };\n  },\n\n  computed: {\n    ...mapGetters('i18n', ['selectedLocaleLabel', 'availableLocales']),\n\n    localesOptions() {\n      return Object.keys(this.availableLocales).map((value) => {\n        return {\n          label: this.t(`locale.${ value }`),\n          value\n        };\n      });\n    },\n\n    selectedOption() {\n      return Object.keys(this.availableLocales)[Object.values(this.availableLocales).indexOf(this.selectedLocaleLabel)];\n    },\n\n    showLocale() {\n      return (this.availableLocales && Object.keys(this.availableLocales).length > 1) || this.dev;\n    },\n\n    showNone() {\n      return !!process.env.dev && this.dev;\n    },\n  },\n\n  methods: {\n    openLocaleSelector() {\n      this.isLocaleSelectorOpen = true;\n    },\n    closeLocaleSelector() {\n      this.isLocaleSelectorOpen = false;\n    },\n    switchLocale($event) {\n      this.$store.dispatch('i18n/switchTo', $event);\n      this.closeLocaleSelector();\n    },\n  }\n};\n</script>\n\n<template>\n  <div>\n    <div v-if=\"mode === 'login'\">\n      <div\n        v-if=\"showLocale\"\n        role=\"menu\"\n        :aria-label=\"t('locale.menu')\"\n        class=\"locale-login-container\"\n        tabindex=\"0\"\n        @click=\"openLocaleSelector\"\n        @blur.capture=\"closeLocaleSelector\"\n        @keyup.enter=\"openLocaleSelector\"\n        @keyup.space=\"openLocaleSelector\"\n      >\n        <v-dropdown\n          popperClass=\"localeSelector\"\n          :shown=\"isLocaleSelectorOpen\"\n          placement=\"top\"\n          distance=\"8\"\n          skidding=\"12\"\n          :triggers=\"[]\"\n          :autoHide=\"false\"\n          :flip=\"false\"\n          :container=\"false\"\n          @focus.capture=\"openLocaleSelector\"\n        >\n          <a\n            data-testid=\"locale-selector\"\n            class=\"locale-chooser\"\n          >\n            {{ selectedLocaleLabel }}\n            <i\n              v-if=\"showIcon\"\n              class=\"icon icon-fw icon-sort-down\"\n            />\n          </a>\n          <template #popper>\n            <ul\n              class=\"list-unstyled dropdown\"\n              style=\"margin: -1px;\"\n            >\n              <li\n                v-if=\"showNone\"\n                v-t=\"'locale.none'\"\n                class=\"hand\"\n                tabindex=\"0\"\n                role=\"menuitem\"\n                @click.stop=\"switchLocale('none')\"\n                @keyup.enter.stop=\"switchLocale('none')\"\n                @keyup.space.stop=\"switchLocale('none')\"\n              />\n              <li\n                v-for=\"(label, name) in availableLocales\"\n                :key=\"name\"\n                tabindex=\"0\"\n                role=\"menuitem\"\n                class=\"hand\"\n                :lang=\"name\"\n                @click.stop=\"switchLocale(name)\"\n                @keyup.enter.stop=\"switchLocale(name)\"\n                @keyup.space.stop=\"switchLocale(name)\"\n              >\n                {{ label }}\n              </li>\n            </ul>\n          </template>\n        </v-dropdown>\n      </div>\n    </div>\n    <div v-else>\n      <Select\n        :value=\"selectedOption\"\n        :options=\"localesOptions\"\n        :is-lang-select=\"true\"\n        @update:value=\"switchLocale($event)\"\n      />\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.advanced {\n  user-select: none;\n  padding: 0 5px;\n  line-height: 40px;\n  font-size: 15px;\n  font-weight: 500;\n}\n.content {\n  background: var(--nav-active);\n  padding: 10px;\n  margin-top: 6px;\n  border-radius: 4px;\n}\n\n.hand:focus-visible {\n  @include focus-outline;\n  outline-offset: 4px;\n}\n\n.locale-chooser {\n  cursor: pointer;\n\n  &:hover {\n    text-decoration: none;\n  }\n}\n\n.locale-login-container:focus-visible {\n  @include focus-outline;\n  outline-offset: 2px;\n}\n</style>\n"]}]}