{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ModalWithCard.vue?vue&type=style&index=0&id=4c410e60&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ModalWithCard.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5mb290ZXIgewogIHdpZHRoOiAxMDAlOwogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7Cn0KCi5iYW5uZXIgewogIG1hcmdpbi1ib3R0b206IDBweDsKfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ModalWithCard.vue"], "names": [], "mappings": ";AAoHA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACpB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ModalWithCard.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { Card } from '@components/Card';\nimport { Banner } from '@components/Banner';\nimport AsyncButton from '@shell/components/AsyncButton';\nimport AppModal from '@shell/components/AppModal.vue';\n\nexport default {\n  name: 'ModalWithCard',\n\n  emits: ['close', 'finish'],\n\n  components: {\n    Card, Banner, AsyncButton, AppModal\n  },\n\n  props: {\n    name: {\n      type:     String,\n      required: true\n    },\n\n    closeText: {\n      type:    String,\n      default: 'Close'\n    },\n\n    saveText: {\n      type:    String,\n      default: 'create'\n    },\n\n    width: {\n      type:    [String, Number],\n      default: '50%'\n    },\n\n    height: {\n      type:    [String, Number],\n      default: 'auto'\n    },\n\n    errors: {\n      type:    Array,\n      default: () => {\n        return [];\n      }\n    }\n  },\n\n  methods: {\n    hide() {\n      this.$emit('close');\n    },\n  }\n};\n\n</script>\n\n<template>\n  <app-modal\n    :name=\"name\"\n    :width=\"width\"\n    :click-to-close=\"false\"\n    :height=\"height\"\n    v-bind=\"$attrs\"\n    class=\"modal\"\n    data-testid=\"mvc__card\"\n    @close=\"$emit('finish', $event)\"\n  >\n    <Card\n      class=\"modal\"\n      :show-highlight-border=\"false\"\n    >\n      <template #title>\n        <h4 class=\"text-default-text\">\n          <slot name=\"title\" />\n        </h4>\n      </template>\n\n      <template #body>\n        <slot name=\"content\" />\n\n        <div\n          v-for=\"(err,idx) in errors\"\n          :key=\"idx\"\n        >\n          <Banner\n            class=\"banner\"\n            color=\"error\"\n            :label=\"err\"\n          />\n        </div>\n      </template>\n\n      <template #actions>\n        <slot name=\"footer\">\n          <div class=\"footer\">\n            <button\n              class=\"btn role-secondary mr-20\"\n              @click.prevent=\"hide\"\n            >\n              {{ closeText }}\n            </button>\n\n            <AsyncButton\n              :mode=\"saveText\"\n              @click=\"$emit('finish', $event)\"\n            />\n          </div>\n        </slot>\n      </template>\n    </Card>\n  </app-modal>\n</template>\n\n<style lang=\"scss\" scoped>\n.footer {\n  width: 100%;\n  display: flex;\n  justify-content: center;\n}\n\n.banner {\n  margin-bottom: 0px;\n}\n</style>\n\n<style lang=\"scss\">\n.modal {\n  border-radius: var(--border-radius);\n  max-height: 100vh;\n\n  &.card-container {\n    box-shadow: none;\n  }\n}\n</style>\n"]}]}