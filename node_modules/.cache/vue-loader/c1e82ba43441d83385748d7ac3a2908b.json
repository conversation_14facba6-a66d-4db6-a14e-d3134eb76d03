{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ShellInput.vue?vue&type=template&id=ffca249c", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ShellInput.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPExhYmVsZWRJbnB1dAogICAgdi1tb2RlbDp2YWx1ZT0idXNlclZhbHVlIgogICAgdi1iaW5kPSIkYXR0cnMiCiAgICBAdXBkYXRlOnZhbHVlPSJ1cGRhdGUoJGV2ZW50KSIKICAvPgo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ShellInput.vue"], "names": [], "mappings": ";EAqFE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/B,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ShellInput.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { LabeledInput } from '@components/Form/LabeledInput';\n\nexport default {\n  emits: ['update:value'],\n\n  components: { LabeledInput },\n\n  props: {\n    value: {\n      type:    Array,\n      default: null,\n    }\n  },\n  /*\n      userValue is a string representation of args array, with spaces between each array item and single quotes around any items with whitespace\n      value input of [\"-c\", \"sleep 600\"]\n      is displayed as: \"-c 'sleep 600'\"\n\n      user input of \"-c \"sleep 600\"\" or \"-c 'sleep 600'\"\n      causes $emit 'input' of [\"-c\", \"sleep 600\"]\n  */\n  data() {\n    let userValue = '';\n\n    if ( this.value ) {\n      userValue = this.value.reduce((str, each) => {\n        if (each.includes(' ')) {\n          str += `'${ each }'`;\n        } else {\n          str += each;\n        }\n        str += ' ';\n\n        return str;\n      }, '').trim();\n    }\n\n    return { userValue };\n  },\n\n  methods: {\n    update(userValue) {\n      let out = null;\n\n      if ( userValue ) {\n        out = userValue.match(/('[^']+')|(\"[^\"]+\")|\\S+/g).map((string) => string.replace(/^'|'$|^\"|\"$/g, ''));\n      }\n      this.$emit('update:value', out);\n    },\n  }\n};\n\nexport const OPS = ['||', '&&', ';;', '|&', '&', ';', '(', ')', '|', '<', '>'];\nexport function reop(xs) {\n  return xs.map((s) => {\n    if ( OPS.includes(s) ) {\n      return { op: s };\n    } else {\n      return s;\n    }\n  });\n}\n\nexport function unparse(xs) {\n  return xs.map((s) => {\n    if ( s && typeof s === 'object' ) {\n      if ( Object.prototype.hasOwnProperty.call(s, 'pattern') ) {\n        return `\"${ s.pattern }\"`;\n      } else {\n        return s.op;\n      }\n    } else if ( /[\"\\s]/.test(s) && !/'/.test(s) ) {\n      return `'${ s.replace(/(['\\\\])/g, '\\\\$1') }'`;\n    } else if ( /[\"'\\s]/.test(s) ) {\n      return `\"${ s.replace(/([\"\\\\$`!])/g, '\\\\$1') }\"`;\n    } else {\n      return String(s).replace(/([\\\\$`()!#&*|])/g, '\\\\$1');\n    }\n  }).join(' ');\n}\n\n</script>\n\n<template>\n  <LabeledInput\n    v-model:value=\"userValue\"\n    v-bind=\"$attrs\"\n    @update:value=\"update($event)\"\n  />\n</template>\n"]}]}