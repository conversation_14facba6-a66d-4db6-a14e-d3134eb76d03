{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/HookOption.vue?vue&type=style&index=0&id=348dcaa0&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/HookOption.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci52YXItcm93ewogIGRpc3BsYXk6IGdyaWQ7CiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnIgMWZyIDFmciAxZnIgMTAwcHg7CiAgZ3JpZC1jb2x1bW4tZ2FwOiAyMHB4OwogIG1hcmdpbi1ib3R0b206IDIwcHg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKCiAgLnNpbmdsZS12YWx1ZSB7CiAgICBncmlkLWNvbHVtbjogc3BhbiAyOwogIH0KCiAgLmxhYmVsZWQtc2VsZWN0IHsKICAgIG1pbi1oZWlnaHQ6ICRpbnB1dC1oZWlnaHQ7CiAgfQogIC5yZW1vdmUgQlVUVE9OIHsKICAgIHBhZGRpbmc6IDBweDsKICB9Cn0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/HookOption.vue"], "names": [], "mappings": ";AA+OA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACd;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/HookOption.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport debounce from 'lodash/debounce';\nimport { RadioGroup } from '@components/Form/Radio';\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport ShellInput from '@shell/components/form/ShellInput';\nimport { _VIEW } from '@shell/config/query-params';\nimport { isEmpty } from '@shell/utils/object';\n\nexport default {\n  emits: ['update:value'],\n\n  props: {\n    mode: {\n      type:     String,\n      required: true,\n    },\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  components: {\n    RadioGroup, LabeledInput, LabeledSelect, ShellInput\n  },\n\n  data() {\n    const selectHook = null;\n\n    const defaultExec = { exec: { command: [] } };\n    const defaultHttpGet = {\n      httpGet: {\n        host:        '',\n        path:        '',\n        port:        null,\n        scheme:      '',\n        httpHeaders: null\n      }\n    };\n\n    return {\n      selectHook,\n      defaultExec,\n      defaultHttpGet,\n      schemeOptions: ['HTTP', 'HTTPS']\n    };\n  },\n\n  computed: {\n    isView() {\n      return this.mode === _VIEW;\n    },\n  },\n\n  created() {\n    if (this.value) {\n      this.selectHook = Object.keys(this.value)[0];\n    }\n\n    if (isEmpty(this.value)) {\n      this.selectHook = 'none';\n    }\n\n    this.queueUpdate = debounce(this.update, 500);\n  },\n\n  methods: {\n    addHeader() {\n      const header = { name: '', value: '' };\n\n      if (!this.value.httpGet.httpHeaders) {\n        this.value.httpGet['httpHeaders'] = [];\n      }\n\n      this.value.httpGet.httpHeaders.push(header);\n    },\n\n    removeHeader(index) {\n      this.value.httpGet.httpHeaders.splice(index, 1);\n    },\n\n    update() {\n      const { ...leftovers } = this.value;\n\n      switch (this.selectHook) {\n      case 'none':\n        this.deleteLeftovers(leftovers);\n        break;\n      case 'exec':\n        this.deleteLeftovers(leftovers);\n        Object.assign(this.value, this.defaultExec);\n        break;\n      case 'httpGet':\n        this.deleteLeftovers(leftovers);\n        Object.assign(this.value, this.defaultHttpGet);\n        break;\n      default:\n        break;\n      }\n\n      this.$emit('update:value', this.value);\n    },\n\n    deleteLeftovers(leftovers) {\n      if (leftovers) {\n        for (const obj in leftovers) {\n          delete this.value[obj];\n        }\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <div>\n    <div class=\"row mb-10\">\n      <RadioGroup\n        v-model:value=\"selectHook\"\n        name=\"selectHook\"\n        :options=\"['none', 'exec', 'httpGet']\"\n        :labels=\"[\n          t('generic.none'),\n          t('workload.container.lifecycleHook.exec.add'),\n          t('workload.container.lifecycleHook.httpGet.add'),\n        ]\"\n        :mode=\"mode\"\n        @update:value=\"update\"\n      />\n    </div>\n\n    <template v-if=\"selectHook === 'exec'\">\n      <div class=\"mb-20 single-value\">\n        <h4>{{ t('workload.container.lifecycleHook.exec.title') }}</h4>\n        <div>\n          <ShellInput\n            v-model:value=\"value.exec.command\"\n            :mode=\"mode\"\n            :label=\"t('workload.container.lifecycleHook.exec.command.label')\"\n            :placeholder=\"t('workload.container.lifecycleHook.exec.command.placeholder', null, true)\"\n            required\n          />\n        </div>\n      </div>\n    </template>\n\n    <template v-if=\"selectHook === 'httpGet'\">\n      <h4>{{ t('workload.container.lifecycleHook.httpGet.title') }}</h4>\n      <div class=\"var-row\">\n        <LabeledInput\n          v-model:value=\"value.httpGet.host\"\n          :label=\"t('workload.container.lifecycleHook.httpGet.host.label')\"\n          :placeholder=\"t('workload.container.lifecycleHook.httpGet.host.placeholder')\"\n          :mode=\"mode\"\n          @update:value=\"update\"\n        />\n        <LabeledInput\n          v-model:value=\"value.httpGet.path\"\n          :label=\"t('workload.container.lifecycleHook.httpGet.path.label')\"\n          :placeholder=\"t('workload.container.lifecycleHook.httpGet.path.placeholder')\"\n          :mode=\"mode\"\n          @update:value=\"update\"\n        />\n        <LabeledInput\n          v-model:value.number=\"value.httpGet.port\"\n          type=\"number\"\n          :label=\"t('workload.container.lifecycleHook.httpGet.port.label')\"\n          :placeholder=\"t('workload.container.lifecycleHook.httpGet.port.placeholder')\"\n          :mode=\"mode\"\n          required\n          @update:value=\"update\"\n        />\n        <LabeledSelect\n          v-model:value=\"value.httpGet.scheme\"\n          :label=\"t('workload.container.lifecycleHook.httpGet.scheme.label')\"\n          :placeholder=\"t('workload.container.lifecycleHook.httpGet.scheme.placeholder')\"\n          :options=\"schemeOptions\"\n          :mode=\"mode\"\n          @update:value=\"update\"\n        />\n      </div>\n\n      <h4>{{ t('workload.container.lifecycleHook.httpHeaders.title') }}</h4>\n      <div\n        v-for=\"(header, index) in value.httpGet.httpHeaders\"\n        :key=\"index\"\n        class=\"var-row\"\n        data-testid=\"hookoption-header-row\"\n      >\n        <LabeledInput\n          v-model:value=\"value.httpGet.httpHeaders[index].name\"\n          :label=\"t('workload.container.lifecycleHook.httpHeaders.name.label')\"\n          :placeholder=\"t('workload.container.lifecycleHook.httpHeaders.name.placeholder')\"\n          class=\"single-value\"\n          :mode=\"mode\"\n          required\n          @update:value=\"update\"\n        />\n        <LabeledInput\n          v-model:value=\"value.httpGet.httpHeaders[index].value\"\n          :label=\"t('workload.container.lifecycleHook.httpHeaders.value.label')\"\n          :placeholder=\"t('workload.container.lifecycleHook.httpHeaders.value.placeholder')\"\n          class=\"single-value\"\n          :mode=\"mode\"\n          @update:value=\"update\"\n        />\n        <div class=\"remove\">\n          <button\n            v-if=\"!isView\"\n            type=\"button\"\n            class=\"btn role-link ml0\"\n            :disabled=\"mode==='view'\"\n            @click.stop=\"removeHeader(index)\"\n          >\n            <t k=\"generic.remove\" />\n          </button>\n        </div>\n      </div>\n\n      <div>\n        <button\n          v-if=\"!isView\"\n          type=\"button\"\n          class=\"btn role-link mb-20\"\n          :disabled=\"mode === 'view'\"\n          data-testid=\"hookoption-add-header-button\"\n          @click.stop=\"addHeader\"\n        >\n          Add Header\n        </button>\n      </div>\n    </template>\n  </div>\n</template>\n\n<style lang='scss' scoped>\n.var-row{\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr 1fr 100px;\n  grid-column-gap: 20px;\n  margin-bottom: 20px;\n  align-items: center;\n\n  .single-value {\n    grid-column: span 2;\n  }\n\n  .labeled-select {\n    min-height: $input-height;\n  }\n  .remove BUTTON {\n    padding: 0px;\n  }\n}\n</style>\n"]}]}