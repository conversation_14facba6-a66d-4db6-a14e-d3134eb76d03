{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ValueFromResource.vue?vue&type=style&index=0&id=a56e893e&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ValueFromResource.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci52YXItcm93ewogIGRpc3BsYXk6IGdyaWQ7CiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnIgMWZyIDFmciAxZnIgMTAwcHg7CiAgZ3JpZC1jb2x1bW4tZ2FwOiAyMHB4OwogIG1hcmdpbi1ib3R0b206IDEwcHg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKCiAgLnNpbmdsZS12YWx1ZSB7CiAgICBncmlkLWNvbHVtbjogc3BhbiAyOwogIH0KCiAgLnJlbW92ZSBCVVRUT04gewogICAgcGFkZGluZzogMHB4OwogIH0KfQoK"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ValueFromResource.vue"], "names": [], "mappings": ";AAyXA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CA<PERSON>;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACd;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ValueFromResource.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { CONFIG_MAP, SECRET, NAMESPACE } from '@shell/config/types';\nimport { get } from '@shell/utils/object';\nimport { _VIEW } from '@shell/config/query-params';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport { LabeledInput } from '@components/Form/LabeledInput';\n\nexport default {\n  emits: ['update:value', 'remove'],\n\n  components: {\n    LabeledSelect,\n    LabeledInput\n  },\n\n  props: {\n    mode: {\n      type:    String,\n      default: 'create'\n    },\n    value: {\n      type:    Object,\n      default: () => {\n        return { valueFrom: {} };\n      }\n    },\n    allConfigMaps: {\n      type:    Array,\n      default: () => []\n    },\n    allSecrets: {\n      type:    Array,\n      default: () => []\n    },\n    // filter resource options by namespace(s) selected in top nav\n    namespaced: {\n      type:    Boolean,\n      default: true\n    },\n    loading: {\n      default: false,\n      type:    Boolean\n    },\n  },\n\n  data() {\n    const typeOpts = [\n      { value: 'simple', label: 'Key/Value Pair' },\n      { value: 'resourceFieldRef', label: 'Resource' },\n      { value: 'configMapKeyRef', label: 'ConfigMap Key' },\n      { value: 'secretKeyRef', label: 'Secret key' },\n      { value: 'fieldRef', label: 'Pod Field' },\n      { value: 'secretRef', label: 'Secret' },\n      { value: 'configMapRef', label: 'ConfigMap' },\n    ];\n\n    const resourceKeyOpts = ['limits.cpu', 'limits.ephemeral-storage', 'limits.memory', 'requests.cpu', 'requests.ephemeral-storage', 'requests.memory'];\n    let type;\n\n    if (this.value.secretRef) {\n      type = 'secretRef';\n    } else if (this.value.configMapRef) {\n      type = 'configMapRef';\n    } else if (this.value.value) {\n      type = 'simple';\n    } else if (this.value.valueFrom) {\n      type = Object.keys((this.value.valueFrom))[0] || 'simple';\n    }\n\n    let refName;\n    let name;\n    let fieldPath;\n    let referenced;\n    let key;\n    let valStr;\n    const keys = [];\n\n    switch (type) {\n    case 'resourceFieldRef':\n      name = this.value.name;\n      refName = this.value.valueFrom[type].containerName;\n      key = this.value.valueFrom[type].resource || '';\n      break;\n    case 'configMapKeyRef':\n      name = this.value.name;\n      key = this.value.valueFrom[type].key || '';\n      refName = this.value.valueFrom[type].name;\n      referenced = this.allConfigMaps.filter((resource) => {\n        return resource.metadata.name === refName;\n      })[0];\n      if (referenced && referenced.data) {\n        keys.push(...Object.keys(referenced.data));\n      }\n      break;\n    case 'secretRef':\n    case 'configMapRef':\n      name = this.value.prefix;\n      refName = this.value[type].name;\n      break;\n    case 'secretKeyRef':\n      name = this.value.name;\n      key = this.value.valueFrom[type].key || '';\n      refName = this.value.valueFrom[type].name;\n      referenced = this.allSecrets.filter((resource) => {\n        return resource.metadata.name === refName;\n      })[0];\n      if (referenced && referenced.data) {\n        keys.push(...Object.keys(referenced.data));\n      }\n      break;\n    case 'fieldRef':\n      fieldPath = get(this.value.valueFrom, `${ type }.fieldPath`) || '';\n      name = this.value.name;\n      break;\n    default:\n      name = this.value.name;\n      valStr = this.value.value;\n      break;\n    }\n\n    return {\n      typeOpts, type, refName, referenced: refName, secrets: this.allSecrets, keys, key, fieldPath, name, resourceKeyOpts, valStr\n    };\n  },\n  computed: {\n    isView() {\n      return this.mode === _VIEW;\n    },\n\n    namespaces() {\n      if (this.namespaced) {\n        const map = this.$store.getters.namespaces();\n\n        return Object.keys(map).filter((key) => map[key]);\n      } else {\n        const inStore = this.$store.getters['currentStore'](NAMESPACE);\n\n        return this.$store.getters[`${ inStore }/all`](NAMESPACE);\n      }\n    },\n\n    sourceOptions() {\n      if (this.type === 'configMapKeyRef' || this.type === 'configMapRef') {\n        return this.allConfigMaps.filter((map) => this.namespaces.includes(map?.metadata?.namespace));\n      } else if (this.type === 'secretRef' || this.type === 'secretKeyRef') {\n        return this.allSecrets.filter((secret) => this.namespaces.includes(secret?.metadata?.namespace));\n      } else {\n        return [];\n      }\n    },\n\n    needsSource() {\n      return this.type !== 'simple' && this.type !== 'resourceFieldRef' && this.type !== 'fieldRef' && !!this.type;\n    },\n\n    sourceLabel() {\n      let out;\n      const { type } = this;\n\n      if (!type) {\n        return;\n      }\n\n      switch (type) {\n      case 'secretKeyRef':\n      case 'secretRef':\n        out = 'workload.container.command.fromResource.secret';\n        break;\n      case 'configMapKeyRef':\n      case 'configMapRef':\n        out = 'workload.container.command.fromResource.configMap';\n        break;\n      default:\n        out = 'workload.container.command.fromResource.source.label';\n      }\n\n      return this.t(out);\n    },\n\n    nameLabel() {\n      if (this.type === 'configMapRef' || this.type === 'secretRef') {\n        return this.t('workload.container.command.fromResource.prefix');\n      } else {\n        return this.t('workload.container.command.fromResource.name.label');\n      }\n    },\n\n    extraColumn() {\n      return ['resourceFieldRef', 'configMapKeyRef', 'secretKeyRef'].includes(this.type);\n    },\n  },\n\n  watch: {\n    type() {\n      this.referenced = null;\n      this.key = '';\n      this.refName = '';\n      this.keys = [];\n      this.key = '';\n      this.valStr = '';\n      this.fieldPath = '';\n    },\n\n    referenced(neu, old) {\n      if (neu) {\n        if ((neu.type === SECRET || neu.type === CONFIG_MAP) && neu.data) {\n          this.keys = Object.keys(neu.data);\n        }\n        this.refName = neu?.metadata?.name;\n      }\n      this.updateRow();\n    },\n  },\n\n  methods: {\n    updateRow() {\n      if (!this.name?.length && !this.refName?.length) {\n        if (this.type !== 'fieldRef') {\n          this.$emit('update:value', null);\n\n          return;\n        }\n      }\n      let out = { name: this.name || this.refName };\n\n      switch (this.type) {\n      case 'configMapKeyRef':\n      case 'secretKeyRef':\n        out.valueFrom = {\n          [this.type]: {\n            key: this.key, name: this.refName, optional: false\n          }\n        };\n        break;\n      case 'resourceFieldRef':\n        out.valueFrom = {\n          [this.type]: {\n            containerName: this.refName, divisor: 1, resource: this.key\n          }\n        };\n        break;\n      case 'fieldRef':\n        if (!this.fieldPath || !this.fieldPath.length) {\n          out = null; break;\n        }\n        out.valueFrom = { [this.type]: { apiVersion: 'v1', fieldPath: this.fieldPath } };\n        break;\n      case 'simple':\n        out.value = this.valStr;\n        break;\n      default:\n        delete out.name;\n        out.prefix = this.name;\n        out[this.type] = { name: this.refName, optional: false };\n      }\n      this.$emit('update:value', out);\n    },\n    get\n  }\n};\n</script>\n\n<template>\n  <div class=\"var-row\">\n    <div class=\"type\">\n      <LabeledSelect\n        v-model:value=\"type\"\n        :mode=\"mode\"\n        :multiple=\"false\"\n        :options=\"typeOpts\"\n        option-label=\"label\"\n        :searchable=\"false\"\n        :reduce=\"e=>e.value\"\n        :label=\"t('workload.container.command.fromResource.type')\"\n        @update:value=\"updateRow\"\n      />\n    </div>\n\n    <div class=\"name\">\n      <LabeledInput\n        v-model:value=\"name\"\n        :label=\"nameLabel\"\n        :placeholder=\"t('workload.container.command.fromResource.name.placeholder')\"\n        :mode=\"mode\"\n        @update:value=\"updateRow\"\n      />\n    </div>\n\n    <div\n      v-if=\"type==='simple'\"\n      class=\"single-value\"\n    >\n      <LabeledInput\n        v-model:value=\"valStr\"\n        :label=\"t('workload.container.command.fromResource.value.label')\"\n        :placeholder=\"t('workload.container.command.fromResource.value.placeholder')\"\n        :mode=\"mode\"\n        @update:value=\"updateRow\"\n      />\n    </div>\n\n    <template v-else-if=\"needsSource\">\n      <div :class=\"{'single-value': type === 'configMapRef' || type === 'secretRef'}\">\n        <LabeledSelect\n          v-model:value=\"referenced\"\n          :options=\"sourceOptions\"\n          :multiple=\"false\"\n          :get-option-label=\"opt=>get(opt, 'metadata.name') || opt\"\n          :get-option-key=\"opt=>opt.id|| opt\"\n          :mode=\"mode\"\n          :label=\"sourceLabel\"\n          :loading=\"loading\"\n        />\n      </div>\n      <div v-if=\"type!=='secretRef' && type!== 'configMapRef'\">\n        <LabeledSelect\n          v-model:value=\"key\"\n          :multiple=\"false\"\n          :options=\"keys\"\n          :mode=\"mode\"\n          option-label=\"label\"\n          :label=\"t('workload.container.command.fromResource.key.label')\"\n          :loading=\"loading\"\n          @update:value=\"updateRow\"\n        />\n      </div>\n    </template>\n\n    <template v-else-if=\"type==='resourceFieldRef'\">\n      <div>\n        <LabeledInput\n          v-model:value=\"refName\"\n          :label=\"t('workload.container.command.fromResource.containerName')\"\n          :placeholder=\"t('workload.container.command.fromResource.source.placeholder')\"\n          :mode=\"mode\"\n          @update:value=\"updateRow\"\n        />\n      </div>\n      <div>\n        <LabeledSelect\n          v-model:value=\"key\"\n          :label=\"t('workload.container.command.fromResource.key.label')\"\n          :multiple=\"false\"\n          :options=\"resourceKeyOpts\"\n          :mode=\"mode\"\n          :searchable=\"false\"\n          :placeholder=\"t('workload.container.command.fromResource.key.placeholder', null, true)\"\n          @update:value=\"updateRow\"\n        />\n      </div>\n    </template>\n\n    <template v-else>\n      <div class=\"single-value\">\n        <LabeledInput\n          v-model:value=\"fieldPath\"\n          :placeholder=\"t('workload.container.command.fromResource.key.placeholder', null, true)\"\n          :label=\"t('workload.container.command.fromResource.key.label')\"\n          :mode=\"mode\"\n          @update:value=\"updateRow\"\n        />\n      </div>\n    </template>\n    <div class=\"remove\">\n      <button\n        v-if=\"!isView\"\n        type=\"button\"\n        class=\"btn role-link\"\n        @click.stop=\"$emit('remove')\"\n      >\n        {{ t('generic.remove') }}\n      </button>\n    </div>\n  </div>\n</template>\n\n<style lang='scss' scoped>\n.var-row{\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr 1fr 100px;\n  grid-column-gap: 20px;\n  margin-bottom: 10px;\n  align-items: center;\n\n  .single-value {\n    grid-column: span 2;\n  }\n\n  .remove BUTTON {\n    padding: 0px;\n  }\n}\n\n</style>\n"]}]}