{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceYaml.vue?vue&type=style&index=1&id=ff56cf50&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceYaml.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5yZXNvdXJjZS15YW1sIHsKICAueWFtbC1lZGl0b3IgewogICAgbWluLWhlaWdodDogMjAwcHg7CiAgfQoKICBmb290ZXIgLmFjdGlvbnMgewogICAgdGV4dC1hbGlnbjogcmlnaHQ7CiAgfQoKICAuc3BhY2VyLXNtYWxsIHsKICAgIHBhZGRpbmc6IDA7CiAgfQp9Cgo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceYaml.vue"], "names": [], "mappings": ";AAoYA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACZ;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceYaml.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport jsyaml from 'js-yaml';\nimport YamlEditor, { EDITOR_MODES } from '@shell/components/YamlEditor';\nimport FileSelector from '@shell/components/form/FileSelector';\nimport Footer from '@shell/components/form/Footer';\nimport { ANNOTATIONS_TO_FOLD } from '@shell/config/labels-annotations';\nimport { ensureRegex } from '@shell/utils/string';\nimport { typeOf } from '@shell/utils/sort';\n\nimport {\n  _CREATE,\n  _VIEW,\n  PREVIEW,\n  _FLAGGED,\n  _UNFLAG,\n  _EDIT,\n} from '@shell/config/query-params';\nimport { BEFORE_SAVE_HOOKS, AFTER_SAVE_HOOKS } from '@shell/mixins/child-hook';\nimport { exceptionToErrorsArray } from '@shell/utils/error';\n\nexport default {\n  emits: ['error'],\n\n  components: {\n    Footer,\n    FileSelector,\n    YamlEditor\n  },\n\n  props: {\n    mode: {\n      type:     String,\n      required: true,\n    },\n\n    value: {\n      type:     Object,\n      required: true,\n    },\n\n    initialYamlForDiff: {\n      type:    String,\n      default: null,\n    },\n\n    yaml: {\n      type:     String,\n      required: true,\n    },\n\n    doneRoute: {\n      type:    [String, Object],\n      default: null,\n    },\n\n    offerPreview: {\n      type:    Boolean,\n      default: true,\n    },\n\n    parentParams: {\n      type:    Object,\n      default: null,\n    },\n\n    doneOverride: {\n      type:    [Function, Object],\n      default: null\n    },\n\n    showFooter: {\n      type:    Boolean,\n      default: true\n    },\n\n    applyHooks: {\n      type:    Function,\n      default: null,\n    }\n  },\n\n  data() {\n    // Initial load with a preview showing no diff isn't very useful\n    this.$router.applyQuery({ [PREVIEW]: _UNFLAG });\n\n    return {\n      initialYaml:  this.initialYamlForDiff || this.yaml,\n      currentYaml:  this.yaml,\n      showPreview:  false,\n      errors:       null,\n      cm:           null,\n      initialReady: true\n    };\n  },\n\n  computed: {\n    schema() {\n      const inStore = this.$store.getters['currentStore'](this.value.type);\n\n      return this.$store.getters[`${ inStore }/schemaFor`]( this.value.type );\n    },\n\n    isCreate() {\n      return this.mode === _CREATE;\n    },\n\n    isView() {\n      return this.mode === _VIEW;\n    },\n\n    isEdit() {\n      return this.mode === _EDIT;\n    },\n\n    editorMode() {\n      // Include the mode in the route as a dependency\n      // of this computed property so that the editor\n      // toggles when you navigate back and forth between\n      // edit and view.\n      if ( this.$route.query.mode === _VIEW || (this.isView && (this.$route.query.mode !== _EDIT || this.$route.query.mode !== _VIEW))) {\n        return EDITOR_MODES.VIEW_CODE;\n      } else if ( this.showPreview ) {\n        return EDITOR_MODES.DIFF_CODE;\n      }\n\n      return EDITOR_MODES.EDIT_CODE;\n    },\n\n    canDiff() {\n      return this.initialYaml !== this.currentYaml;\n    },\n  },\n\n  watch: {\n    yaml(neu) {\n      if ( this.mode === _VIEW ) {\n        this.currentYaml = neu;\n      }\n    },\n\n    mode(neu, old) {\n      // if this component is changing from viewing a resource to 'creating' that resource, it must actually be cloning\n      // clean yaml accordingly\n      if (neu === _CREATE && old === _VIEW) {\n        this.currentYaml = this.value.cleanYaml(this.yaml, neu);\n      }\n    }\n  },\n\n  methods: {\n    onInput(yaml) {\n      this.currentYaml = yaml;\n      this.onReady(this.cm);\n    },\n\n    onReady(cm) {\n      if (!this.initialReady) {\n        return;\n      }\n      this.initialReady = false;\n\n      this.cm = cm;\n\n      if ( this.isEdit ) {\n        cm.foldLinesMatching(/^status:\\s*$/);\n      }\n\n      try {\n        const parsed = jsyaml.load(this.currentYaml);\n        const annotations = Object.keys(parsed?.metadata?.annotations || {});\n        const regexes = ANNOTATIONS_TO_FOLD.map((x) => ensureRegex(x));\n\n        let foldAnnotations = false;\n\n        for ( const k of annotations ) {\n          if ( foldAnnotations ) {\n            break;\n          }\n\n          for ( const regex of regexes ) {\n            if ( k.match(regex) ) {\n              foldAnnotations = true;\n              break;\n            }\n          }\n        }\n\n        if ( foldAnnotations ) {\n          cm.foldLinesMatching(/^\\s+annotations:\\s*$/);\n        }\n      } catch (e) {}\n\n      cm.foldLinesMatching(/managedFields/);\n\n      // Allow the model to supply an array of json paths to fold other sections in the YAML for the given resource type\n      if (this.value?.yamlFolding) {\n        this.value.yamlFolding.forEach((path) => cm.foldYaml(path));\n      }\n\n      // regardless of edit or create we should probably fold all the comments so they dont get out of hand.\n      const saved = cm.getMode().fold;\n\n      cm.getMode().fold = 'yamlcomments';\n      cm.execCommand('foldAll');\n      cm.getMode().fold = saved;\n    },\n\n    updateValue(value) {\n      this.$refs.yamleditor.updateValue(value);\n    },\n\n    preview() {\n      this.updateValue(this.currentYaml);\n      this.showPreview = true;\n      this.$router.applyQuery({ [PREVIEW]: _FLAGGED });\n    },\n\n    unpreview() {\n      this.showPreview = false;\n      this.$router.applyQuery({ [PREVIEW]: _UNFLAG });\n    },\n\n    async save(buttonDone) {\n      const yaml = this.value.yamlForSave(this.currentYaml) || this.currentYaml;\n\n      try {\n        if ( this.applyHooks ) {\n          await this.applyHooks(BEFORE_SAVE_HOOKS);\n        }\n\n        try {\n          await this.value.saveYaml(yaml);\n        } catch (err) {\n          return onError.call(this, err);\n        }\n\n        if ( this.applyHooks ) {\n          await this.applyHooks(AFTER_SAVE_HOOKS);\n        }\n\n        buttonDone(true);\n        this.done();\n      } catch (err) {\n        return onError.call(this, err);\n      }\n\n      function onError(err) {\n        if ( err && err.response && err.response.data ) {\n          const body = err.response.data;\n\n          if ( body && body.message ) {\n            this.errors = [body.message];\n          } else {\n            this.errors = [err];\n          }\n        } else {\n          this.errors = [err];\n        }\n\n        buttonDone(false);\n\n        this.$emit('error', exceptionToErrorsArray(err));\n      }\n    },\n\n    done() {\n      if (this.doneOverride) {\n        return typeof (this.doneOverride) === 'function' ? this.doneOverride() : this.$router.replace(this.doneOverride);\n      }\n      if ( !this.doneRoute ) {\n        return;\n      }\n      if (typeOf(this.doneRoute) === 'object') {\n        this.$router.replace(this.doneRoute);\n\n        return;\n      }\n      this.$router.replace({\n        name:   this.doneRoute,\n        params: { resource: this.value.type }\n      });\n    },\n\n    onFileSelected(value) {\n      const component = this.$refs.yamleditor;\n\n      if (component) {\n        component.updateValue(value);\n      }\n    },\n\n  }\n};\n</script>\n\n<template>\n  <div class=\"root resource-yaml flex-content\">\n    <YamlEditor\n      ref=\"yamleditor\"\n      v-model:value=\"currentYaml\"\n      :mode=\"mode\"\n      :initial-yaml-values=\"initialYaml\"\n      class=\"yaml-editor flex-content\"\n      :editor-mode=\"editorMode\"\n      @onReady=\"onReady\"\n    />\n    <slot\n      name=\"yamlFooter\"\n      :currentYaml=\"currentYaml\"\n      :showPreview=\"showPreview\"\n      :yamlPreview=\"preview\"\n      :yamlSave=\"save\"\n      :yamlUnpreview=\"unpreview\"\n      :canDiff=\"canDiff\"\n    >\n      <Footer\n        v-if=\"showFooter\"\n        class=\"footer\"\n        :class=\"{ 'edit': !isView }\"\n        :mode=\"mode\"\n        :errors=\"errors\"\n        @save=\"save\"\n        @done=\"done\"\n      >\n        <template\n          v-if=\"!isView\"\n          #left\n        >\n          <FileSelector\n            class=\"btn role-secondary\"\n            :label=\"t('generic.readFromFile')\"\n            @selected=\"onFileSelected\"\n          />\n        </template>\n        <template\n          v-if=\"!isView\"\n          #middle\n        >\n          <button\n            v-if=\"showPreview\"\n            type=\"button\"\n            class=\"btn role-secondary\"\n            @click=\"unpreview\"\n          >\n            <t k=\"resourceYaml.buttons.continue\" />\n          </button>\n          <button\n            v-else-if=\"offerPreview\"\n            :disabled=\"!canDiff\"\n            type=\"button\"\n            class=\"btn role-secondary\"\n            @click=\"preview\"\n          >\n            <t k=\"resourceYaml.buttons.diff\" />\n          </button>\n        </template>\n      </Footer>\n    </slot>\n  </div>\n</template>\n\n<style lang='scss' scoped>\n  .flex-content {\n    display: flex;\n    flex-direction: column;\n    flex-grow: 1;\n  }\n\n  .footer {\n    margin-top: 20px;\n    right: 0;\n    position: sticky;\n    bottom: 0;\n    background-color: var(--header-bg);\n\n    // Overrides outlet padding\n    margin-left: -$space-m;\n    margin-right: -$space-m;\n    margin-bottom: -$space-m;\n    padding: $space-s $space-m;\n\n    &.edit {\n      border-top: var(--header-border-size) solid var(--header-border);\n    }\n  }\n</style>\n\n<style lang=\"scss\">\n.resource-yaml {\n  .yaml-editor {\n    min-height: 200px;\n  }\n\n  footer .actions {\n    text-align: right;\n  }\n\n  .spacer-small {\n    padding: 0;\n  }\n}\n\n</style>\n"]}]}