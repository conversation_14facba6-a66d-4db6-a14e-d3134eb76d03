{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/AddExtensionRepos.vue?vue&type=style&index=0&id=1af1046e&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/AddExtensionRepos.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLmVuYWJsZS1wbHVnaW4tc3VwcG9ydCB7CiAgICBmb250LXNpemU6IDE0cHg7CiAgICBtYXJnaW4tdG9wOiAyMHB4OwogIH0KCiAgLnBsdWdpbi1zZXR1cC1lcnJvciB7CiAgICBmb250LXNpemU6IDE0cHg7CiAgICBjb2xvcjogdmFyKC0tZXJyb3IpOwogICAgbWFyZ2luOiAxMHB4IDAgMCAwOwogIH0KCiAgLmNoZWNrYm94LWluZm8gewogICAgbWFyZ2luLWxlZnQ6IDIwcHg7CiAgICBvcGFjaXR5OiAwLjc7CiAgfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/AddExtensionRepos.vue"], "names": [], "mappings": ";EA+JE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC;EACpB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACd", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/AddExtensionRepos.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { CATALOG } from '@shell/config/types';\nimport Dialog from '@shell/components/Dialog.vue';\nimport Checkbox from '@components/Form/Checkbox/Checkbox.vue';\nimport { UI_PLUGINS_REPOS } from '@shell/config/uiplugins';\nimport { isRancherPrime } from '@shell/config/version';\n\nexport default {\n  emits: ['done'],\n\n  components: {\n    Checkbox,\n    Dialog,\n  },\n\n  async fetch() {\n    if (this.$store.getters['management/schemaFor'](CATALOG.CLUSTER_REPO)) {\n      this.repos = await this.$store.dispatch('management/findAll', { type: CATALOG.CLUSTER_REPO, opt: { force: true } });\n    }\n  },\n\n  data() {\n    return {\n      errors:   [],\n      repos:    [],\n      prime:    isRancherPrime(),\n      addRepos: {\n        official: true,\n        partners: true\n      },\n      reposInfo: {\n        official: {\n          repo:   undefined,\n          name:   UI_PLUGINS_REPOS.OFFICIAL.NAME,\n          url:    UI_PLUGINS_REPOS.OFFICIAL.URL,\n          branch: UI_PLUGINS_REPOS.OFFICIAL.BRANCH,\n        },\n        partners: {\n          repo:   undefined,\n          name:   UI_PLUGINS_REPOS.PARTNERS.NAME,\n          url:    UI_PLUGINS_REPOS.PARTNERS.URL,\n          branch: UI_PLUGINS_REPOS.PARTNERS.BRANCH,\n        }\n      },\n      isDialogActive:      false,\n      returnFocusSelector: '[data-testid=\"extensions-page-menu\"]'\n    };\n  },\n\n  computed: {\n    hasRancherUIPluginsRepo() {\n      return !!this.repos.find((r) => r.urlDisplay === UI_PLUGINS_REPOS.OFFICIAL.URL);\n    },\n    hasRancherUIPartnersPluginsRepo() {\n      return !!this.repos.find((r) => r.urlDisplay === UI_PLUGINS_REPOS.PARTNERS.URL);\n    }\n  },\n\n  methods: {\n    showDialog() {\n      this.addRepos = {\n        official: isRancherPrime() && !this.hasRancherUIPluginsRepo,\n        partners: !this.hasRancherUIPartnersPluginsRepo,\n      };\n      this.isDialogActive = true;\n    },\n\n    async doAddRepos(btnCb) {\n      this.errors = [];\n      const promises = [];\n\n      for (const key in this.addRepos) {\n        if (this.addRepos[key]) {\n          const pluginCR = await this.$store.dispatch('management/create', {\n            type:     CATALOG.CLUSTER_REPO,\n            metadata: { name: this.reposInfo[key].name },\n            spec:     {\n              gitBranch: this.reposInfo[key].branch,\n              gitRepo:   this.reposInfo[key].url,\n            }\n          });\n\n          promises.push(pluginCR.save());\n        }\n      }\n\n      if (promises.length) {\n        const res = await Promise.allSettled(promises);\n\n        res.forEach((result) => {\n          if (result.status === 'rejected') {\n            console.error(result.reason); // eslint-disable-line no-console\n\n            this.errors.push(result.reason);\n          }\n        });\n      }\n\n      this.$emit('done');\n\n      btnCb(true);\n    },\n  }\n};\n</script>\n<template>\n  <Dialog\n    v-if=\"isDialogActive\"\n    name=\"add-extensions-repos\"\n    :title=\"t('plugins.addRepos.title')\"\n    mode=\"add\"\n    data-testid=\"add-extensions-repos-modal\"\n    :return-focus-selector=\"returnFocusSelector\"\n    @okay=\"doAddRepos\"\n    @closed=\"isDialogActive = false\"\n  >\n    <p class=\"mb-20\">\n      {{ t('plugins.addRepos.prompt', {}, true) }}\n    </p>\n    <!-- Official repo -->\n    <div\n      v-if=\"prime\"\n      class=\"mb-15\"\n    >\n      <Checkbox\n        v-model:value=\"addRepos.official\"\n        :disabled=\"hasRancherUIPluginsRepo\"\n        :primary=\"true\"\n        label-key=\"plugins.setup.install.addRancherRepo\"\n        data-testid=\"add-extensions-repos-modal-add-official-repo\"\n      />\n      <div\n        v-if=\"hasRancherUIPluginsRepo\"\n        class=\"checkbox-info\"\n      >\n        ({{ t('plugins.setup.installed') }})\n      </div>\n    </div>\n    <!-- Partners repo -->\n    <div\n      class=\"mb-15\"\n    >\n      <Checkbox\n        v-model:value=\"addRepos.partners\"\n        :disabled=\"hasRancherUIPartnersPluginsRepo\"\n        :primary=\"true\"\n        label-key=\"plugins.setup.install.addPartnersRancherRepo\"\n        data-testid=\"add-extensions-repos-modal-add-partners-repo\"\n      />\n      <div\n        v-if=\"hasRancherUIPartnersPluginsRepo\"\n        class=\"checkbox-info\"\n      >\n        ({{ t('plugins.setup.installed') }})\n      </div>\n    </div>\n  </Dialog>\n</template>\n<style lang=\"scss\" scoped>\n  .enable-plugin-support {\n    font-size: 14px;\n    margin-top: 20px;\n  }\n\n  .plugin-setup-error {\n    font-size: 14px;\n    color: var(--error);\n    margin: 10px 0 0 0;\n  }\n\n  .checkbox-info {\n    margin-left: 20px;\n    opacity: 0.7;\n  }\n</style>\n"]}]}