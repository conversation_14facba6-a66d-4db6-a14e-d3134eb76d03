{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/IconOrSvg.vue?vue&type=style&index=0&id=4d8ba295&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/IconOrSvg.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLnN2Zy1pY29uIHsKICAgIGhlaWdodDogMjRweDsKICAgIHdpZHRoOiAyNHB4OwogIH0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/IconOrSvg.vue"], "names": [], "mappings": ";EAmLE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/IconOrSvg.vue", "sourceRoot": "", "sourcesContent": ["<script>\n\n/**\n * This component renders the icon in the top level menu.\n * Icon can either be via a font via the 'icon' property or an svg via the 'src' property\n *\n * The trickiness here is that we want the icon to be the correct color - both normally and when hovered\n * For a font icon, this is easy, since we just set the color css property\n * For an svg icon included with the <img> tag this is harder - there is no way to apply css to\n * the svg brought in this way - the workaround is to apply a css filter - in order to do this we\n * need to generate the css filter for the required color - the code for that is in the 'svg-filter' utility\n *\n * We cache filters and css for given colors, so we only generate them once.\n *\n * This makes the code here look complex - but we are essentially generating the css filters\n * and then injecting custom css into the document so that any icons included via svg will\n * show with the desired colors for the theme.\n */\nimport { Solver } from '@shell/utils/svg-filter';\nimport { colorToRgb, mapStandardColors, normalizeHex } from '@shell/utils/color';\n\nconst filterCache = {};\nconst cssCache = {};\n\nconst colors = {\n  header: {\n    color: '--header-btn-text',\n    hover: '--header-btn-text-hover'\n  },\n  primary: {\n    color: '--link',\n    hover: '--primary-hover-text'\n  }\n};\n\nexport default {\n  name:  'IconOrSvg',\n  props: {\n    src: {\n      type:    String,\n      default: () => undefined,\n    },\n    icon: {\n      type:    String,\n      default: () => undefined,\n    },\n    color: {\n      type:    String,\n      default: () => 'primary',\n    }\n  },\n\n  data() {\n    return { className: '' };\n  },\n\n  created() {\n    if (this.src) {\n      this.setColor();\n    }\n  },\n\n  methods: {\n    setColor() {\n      const currTheme = this.$store.getters['prefs/theme'];\n      let uiColor, hoverColor;\n\n      // grab css vars values based on the actual stylesheets, depending on the theme applied\n      // use for loops to minimize computation\n      for (let i = 0; i < Object.keys(document.styleSheets).length; i++) {\n        let found = false;\n        const stylesheet = document.styleSheets[i];\n\n        if (stylesheet && stylesheet.cssRules) {\n          for (let x = 0; x < Object.keys(stylesheet.cssRules).length; x++) {\n            const cssRules = stylesheet.cssRules[x];\n\n            if (cssRules.selectorText && ((currTheme === 'light' && (cssRules.selectorText.includes('body') || cssRules.selectorText.includes('BODY')) &&\n              cssRules.selectorText.includes('.theme-light') && cssRules.style.cssText.includes('--link:')) ||\n              (currTheme === 'dark' && cssRules.selectorText.includes('.theme-dark')))) {\n              // grab the colors to be used on the icon from the css rules\n              uiColor = mapStandardColors(cssRules.style.getPropertyValue(colors[this.color].color).trim());\n              hoverColor = mapStandardColors(cssRules.style.getPropertyValue(colors[this.color].hover).trim());\n\n              // normalize hex colors (#xxx to #xxxxxx)\n              uiColor = normalizeHex(uiColor);\n              hoverColor = normalizeHex(hoverColor);\n\n              found = true;\n              break;\n            }\n          }\n        }\n        if (found) {\n          break;\n        } else {\n          continue;\n        }\n      }\n\n      const uiColorRGB = colorToRgb(uiColor);\n      const hoverColorRGB = colorToRgb(hoverColor);\n      const uiColorStr = `${ uiColorRGB.r }-${ uiColorRGB.g }-${ uiColorRGB.b }`;\n      const hoverColorStr = `${ hoverColorRGB.r }-${ hoverColorRGB.g }-${ hoverColorRGB.b }`;\n\n      const className = `svg-icon-${ uiColorStr }-${ hoverColorStr }`;\n\n      if (!cssCache[className]) {\n        let hoverFilter = filterCache[hoverColor];\n\n        if (!hoverFilter) {\n          const solver = new Solver(hoverColorRGB);\n          const res = solver.solve();\n\n          hoverFilter = res?.filter;\n          filterCache[hoverColor] = hoverFilter;\n        }\n\n        let mainFilter = filterCache[uiColor];\n\n        if (!mainFilter) {\n          const solver = new Solver(uiColorRGB);\n          const res = solver.solve();\n\n          mainFilter = res?.filter;\n          filterCache[uiColor] = mainFilter;\n        }\n\n        // Add stylesheet (added as global styles)\n        const styles = `\n          img.${ className } {\n            ${ mainFilter };\n          }\n          img.${ className }:hover {\n            ${ hoverFilter };\n          }\n          button:hover > img.${ className } {\n            ${ hoverFilter };\n          }\n          li:hover > img.${ className } {\n            ${ hoverFilter };\n          }\n          a.option:hover > img.${ className } {\n            ${ hoverFilter };\n          }      `;\n\n        const styleSheet = document.createElement('style');\n\n        styleSheet.innerText = styles;\n        document.head.appendChild(styleSheet);\n\n        cssCache[className] = true;\n      }\n\n      this['className'] = className;\n    }\n  }\n};\n</script>\n\n<template>\n  <img\n    v-if=\"src\"\n    :src=\"src\"\n    class=\"svg-icon\"\n    :class=\"className\"\n  >\n  <i\n    v-else-if=\"icon\"\n    class=\"icon group-icon\"\n    :class=\"icon\"\n  />\n  <i\n    v-else\n    class=\"icon icon-extension\"\n  />\n</template>\n\n<style lang=\"scss\" scoped>\n  .svg-icon {\n    height: 24px;\n    width: 24px;\n  }\n</style>\n"]}]}