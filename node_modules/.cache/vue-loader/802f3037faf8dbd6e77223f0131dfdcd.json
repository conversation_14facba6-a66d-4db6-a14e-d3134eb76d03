{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ClusterAppearance.vue?vue&type=template&id=294d68f6&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ClusterAppearance.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdiBjbGFzcz0iY2x1c3Rlci1hcHBlYXJhbmNlIj4KICAgIDxsYWJlbCBmb3I9Im5hbWUiPgogICAgICB7eyB0KCdjbHVzdGVyQmFkZ2Uuc2V0Q2x1c3RlckFwcGVhcmFuY2UnKSB9fQogICAgPC9sYWJlbD4KICAgIDxkaXYgY2xhc3M9ImNsdXN0ZXItYXBwZWFyYW5jZS1wcmV2aWV3Ij4KICAgICAgPHNwYW4+CiAgICAgICAgPENsdXN0ZXJJY29uTWVudSA6Y2x1c3Rlcj0iY2x1c3RlclByZXZpZXciIC8+CiAgICAgIDwvc3Bhbj4KICAgICAgPGJ1dHRvbgogICAgICAgIDpkaXNhYmxlZD0iZGlzYWJsZSIKICAgICAgICBAY2xpY2s9ImN1c3RvbUJhZGdlRGlhbG9nIgogICAgICA+CiAgICAgICAgPGkgY2xhc3M9Imljb24gaWNvbi1icnVzaC1pY29uIiAvPgogICAgICAgIDxzcGFuPgogICAgICAgICAge3sgdCgnY2x1c3RlckJhZGdlLmN1c3RvbWl6ZScpIH19CiAgICAgICAgPC9zcGFuPgogICAgICA8L2J1dHRvbj4KICAgIDwvZGl2PgogIDwvZGl2Pgo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ClusterAppearance.vue"], "names": [], "mappings": ";EAkEE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,CAAC,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B;QACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC;UACH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ClusterAppearance.vue", "sourceRoot": "", "sourcesContent": ["\n<script>\nimport ClusterIconMenu from '@shell/components/ClusterIconMenu';\nimport { _CREATE, _VIEW } from '@shell/config/query-params';\nimport { CLUSTER_BADGE } from '@shell/config/labels-annotations';\nexport default {\n  title:      'ClusterAppearance',\n  components: { ClusterIconMenu },\n  props:      {\n    name:           { type: String, default: '' },\n    mode:           { type: String, default: _CREATE },\n    currentCluster: { type: Object, default: null }\n  },\n  created() {\n    this.$store.dispatch('customisation/setDefaultPreviewCluster');\n  },\n\n  computed: {\n    disable() {\n      if (this.mode === _VIEW) {\n        return true;\n      }\n\n      return this.name.length <= 1;\n    },\n    clusterPreview() {\n      if (this.mode !== _CREATE) {\n        return {\n          ...this.currentCluster,\n          badge: {\n            iconText: this.currentCluster.metadata.annotations[CLUSTER_BADGE.ICON_TEXT],\n            color:    this.currentCluster.metadata.annotations[CLUSTER_BADGE.COLOR],\n            text:     this.currentCluster.metadata.annotations[CLUSTER_BADGE.TEXT]\n          }\n        };\n      }\n\n      const obj = {\n        ...this.$store.getters['customisation/getPreviewCluster'],\n        label: this.name\n      };\n\n      return obj || {\n        label: this.name,\n        badge: { iconText: null }\n      };\n    },\n  },\n\n  methods: {\n    customBadgeDialog() {\n      this.$store.dispatch('cluster/promptModal', {\n        component:      'AddCustomBadgeDialog',\n        componentProps: {\n          isCreate:        this.mode === _CREATE,\n          mode:            this.mode,\n          clusterName:     this.name,\n          clusterExplorer: this.clusterPreview\n        },\n      });\n    },\n  },\n};\n</script>\n\n<template>\n  <div class=\"cluster-appearance\">\n    <label for=\"name\">\n      {{ t('clusterBadge.setClusterAppearance') }}\n    </label>\n    <div class=\"cluster-appearance-preview\">\n      <span>\n        <ClusterIconMenu :cluster=\"clusterPreview\" />\n      </span>\n      <button\n        :disabled=\"disable\"\n        @click=\"customBadgeDialog\"\n      >\n        <i class=\"icon icon-brush-icon\" />\n        <span>\n          {{ t('clusterBadge.customize') }}\n        </span>\n      </button>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .cluster-appearance {\n    display: flex;\n    flex-direction: column;\n    margin: 3px 35px 0px 0px;\n\n    label {\n      margin: 6px 0 0;\n    }\n\n    &-preview {\n      display: flex;\n      justify-content: center;\n      align-self: start;\n      gap: 10px;\n      justify-content: space-between;\n\n      span {\n        display: flex;\n        align-self: center;\n        height: auto;\n      }\n\n      button {\n        display: flex;\n        align-self: center;\n        height: auto;\n        margin: 0;\n        padding: 0;\n        top: 0;\n        color: var(--link);\n        background: transparent;\n\n        i {\n          margin-right: 2px;\n        }\n\n        &:disabled {\n          color: var(--disabled-text);\n          cursor: not-allowed;\n        }\n      }\n    }\n  }\n</style>\n"]}]}