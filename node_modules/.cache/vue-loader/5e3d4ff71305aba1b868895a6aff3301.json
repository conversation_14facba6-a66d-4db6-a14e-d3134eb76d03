{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Bar.vue?vue&type=style&index=0&id=1c9b98df&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Bar.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouYmFyIHsNCiAgICAkaGVpZ2h0OiAxNXB4Ow0KDQogICAgd2lkdGg6IDEwMCU7DQogICAgaGVpZ2h0OiAkaGVpZ2h0Ow0KICAgIGJvcmRlci1yYWRpdXM6IG1hdGguZGl2KCRoZWlnaHQsIDIpOw0KICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgcG9zaXRpb246IHJlbGF0aXZlOw0KDQogICAgLmluZGljYXRvciB7DQogICAgICAgIGhlaWdodDogMTAwJTsNCiAgICB9DQoNCiAgICAuc2xpY2Ugew0KICAgICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgICAgdG9wOiAwOw0KICAgICAgYm90dG9tOiAwOw0KICAgICAgd2lkdGg6IDFweDsNCiAgICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWJvZHktYmcpOw0KICAgIH0NCn0NCg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Bar.vue"], "names": [], "mappings": ";AA2DA,CAAC,CAAC,CAAC,EAAE;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,EAAE,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC;AACJ", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Bar.vue", "sourceRoot": "", "sourcesContent": ["<script>\r\nexport default {\r\n  props: {\r\n    percentage: {\r\n      type:     Number,\r\n      required: true\r\n    },\r\n    primaryColor: {\r\n      type:    String,\r\n      default: '--primary'\r\n    },\r\n    secondaryColor: {\r\n      type:    String,\r\n      default: '--border'\r\n    },\r\n    slices: {\r\n      type:    Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  computed: {\r\n    indicatorStyle() {\r\n      return {\r\n        width:           `${ this.percentage }%`,\r\n        backgroundColor: `var(${ this.primaryColor })`\r\n      };\r\n    },\r\n    barStyle() {\r\n      return { backgroundColor: `var(${ this.secondaryColor })` };\r\n    },\r\n    sliceStyles() {\r\n      return this.slices.map((slice) => ({\r\n        left:       `${ slice }%`,\r\n        visibility: slice < this.percentage ? 'visible' : 'hidden'\r\n      }));\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<template>\r\n  <div\n    class=\"bar\"\n    :style=\"barStyle\"\n  >\r\n    <div\n      class=\"indicator\"\n      :style=\"indicatorStyle\"\n    />\r\n    <div\n      v-for=\"(sliceStyle, i) in sliceStyles\"\n      :key=\"i\"\n      class=\"slice\"\n      :style=\"sliceStyle\"\n    />\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.bar {\r\n    $height: 15px;\r\n\r\n    width: 100%;\r\n    height: $height;\r\n    border-radius: math.div($height, 2);\r\n    overflow: hidden;\r\n    position: relative;\r\n\r\n    .indicator {\r\n        height: 100%;\r\n    }\r\n\r\n    .slice {\r\n      position: absolute;\r\n      top: 0;\r\n      bottom: 0;\r\n      width: 1px;\r\n      background-color: var(--body-bg);\r\n    }\r\n}\r\n</style>\r\n"]}]}