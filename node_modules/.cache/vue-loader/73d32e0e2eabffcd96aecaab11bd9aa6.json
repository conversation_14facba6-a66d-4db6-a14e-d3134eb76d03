{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayListGrouped.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayListGrouped.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCBBcnJheUxpc3QgZnJvbSAnQHNoZWxsL2NvbXBvbmVudHMvZm9ybS9BcnJheUxpc3QnOwppbXBvcnQgSW5mb0JveCBmcm9tICdAc2hlbGwvY29tcG9uZW50cy9JbmZvQm94JzsKaW1wb3J0IHsgX0VESVQsIF9WSUVXIH0gZnJvbSAnQHNoZWxsL2NvbmZpZy9xdWVyeS1wYXJhbXMnOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICAgICAgICdBcnJheUxpc3RHcm91cGVkJywKICBjb21wb25lbnRzOiB7IEFycmF5TGlzdCwgSW5mb0JveCB9LAogIHByb3BzOiAgICAgIHsKICAgIC8qKgogICAgICogQWxsb3cgdG8gcmVtb3ZlIGl0ZW1zIGJ5IHZhbHVlIG9yIGNvbXB1dGF0aW9uCiAgICAgKi8KICAgIGNhblJlbW92ZTogewogICAgICB0eXBlOiAgICBbQm9vbGVhbiwgRnVuY3Rpb25dLAogICAgICBkZWZhdWx0OiB0cnVlLAogICAgfSwKCiAgICAvKioKICAgICAqIEFsbG93IHRvIGV4dGVuZCBsaXN0CiAgICAgKi8KICAgIGNhbkFkZDogewogICAgICB0eXBlOiAgICBCb29sZWFuLAogICAgICBkZWZhdWx0OiB0cnVlLAogICAgfSwKICAgIC8qKgogICAgICogU3RhcnQgd2l0aCBlbXB0eSByb3cKICAgICAqLwogICAgaW5pdGlhbEVtcHR5Um93OiB7CiAgICAgIHR5cGU6ICAgIEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlLAogICAgfSwKCiAgICAvKioKICAgICAqIEZvcm0gbW9kZSBmb3IgdGhlIGNvbXBvbmVudAogICAgICovCiAgICBtb2RlOiB7CiAgICAgIHR5cGU6ICAgIFN0cmluZywKICAgICAgZGVmYXVsdDogX0VESVQsCiAgICB9LAoKICAgIHZhbHVlOiB7CiAgICAgIHR5cGU6ICAgIE9iamVjdCwKICAgICAgZGVmYXVsdDogKCkgPT4gewogICAgICAgIHJldHVybiB7fTsKICAgICAgfSwKICAgIH0sCiAgfSwKCiAgZW1pdHM6IFsndXBkYXRlOnZhbHVlJywgJ2FkZCcsICdyZW1vdmUnXSwKCiAgY29tcHV0ZWQ6IHsKICAgIGlzVmlldygpIHsKICAgICAgcmV0dXJuIHRoaXMubW9kZSA9PT0gX1ZJRVc7CiAgICB9CiAgfSwKCiAgbWV0aG9kczogewogICAgLyoqCiAgICAgKiBWZXJpZnkgaWYgcm93IGNhbiBiZSByZW1vdmVkIGJ5IG1vZGUsIGZ1bmN0aW9uIGFuZCBkZWNsYXJhdGlvbgogICAgICovCiAgICBjYW5SZW1vdmVSb3cocm93LCBpZHgpIHsKICAgICAgaWYgKCB0aGlzLmlzVmlldyApIHsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KCiAgICAgIGlmICggdHlwZW9mIHRoaXMuY2FuUmVtb3ZlID09PSAnZnVuY3Rpb24nICkgewogICAgICAgIHJldHVybiB0aGlzLmNhblJlbW92ZShyb3csIGlkeCk7CiAgICAgIH0KCiAgICAgIHJldHVybiB0aGlzLmNhblJlbW92ZTsKICAgIH0sCiAgfQp9Owo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayListGrouped.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEzD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAClC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;IACV,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC9C,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KACrB,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;KACrB,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC5B,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX,CAAC;IACH,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAExC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC/D,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd;;MAEA,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjC;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC;EACH;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayListGrouped.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport ArrayList from '@shell/components/form/ArrayList';\nimport InfoBox from '@shell/components/InfoBox';\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\n\nexport default {\n  name:       'ArrayListGrouped',\n  components: { ArrayList, InfoBox },\n  props:      {\n    /**\n     * Allow to remove items by value or computation\n     */\n    canRemove: {\n      type:    [Boolean, Function],\n      default: true,\n    },\n\n    /**\n     * Allow to extend list\n     */\n    canAdd: {\n      type:    Boolean,\n      default: true,\n    },\n    /**\n     * Start with empty row\n     */\n    initialEmptyRow: {\n      type:    Boolean,\n      default: false,\n    },\n\n    /**\n     * Form mode for the component\n     */\n    mode: {\n      type:    String,\n      default: _EDIT,\n    },\n\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      },\n    },\n  },\n\n  emits: ['update:value', 'add', 'remove'],\n\n  computed: {\n    isView() {\n      return this.mode === _VIEW;\n    }\n  },\n\n  methods: {\n    /**\n     * Verify if row can be removed by mode, function and declaration\n     */\n    canRemoveRow(row, idx) {\n      if ( this.isView ) {\n        return false;\n      }\n\n      if ( typeof this.canRemove === 'function' ) {\n        return this.canRemove(row, idx);\n      }\n\n      return this.canRemove;\n    },\n  }\n};\n</script>\n\n<template>\n  <ArrayList\n    class=\"array-list-grouped\"\n    :value=\"value\"\n    v-bind=\"$attrs\"\n    :add-allowed=\"canAdd && !isView\"\n    :mode=\"mode\"\n    :initial-empty-row=\"initialEmptyRow\"\n    @update:value=\"$emit('update:value', $event)\"\n    @add=\"$emit('add')\"\n    @remove=\"$emit('remove', $event)\"\n  >\n    <template v-slot:columns=\"scope\">\n      <InfoBox>\n        <slot v-bind=\"scope\" />\n      </InfoBox>\n    </template>\n    <template v-slot:remove-button=\"scope\">\n      <button\n        v-if=\"canRemoveRow(scope.row, scope.i)\"\n        type=\"button\"\n        class=\"btn role-link close btn-sm\"\n        :data-testid=\"`remove-item-${scope.i}`\"\n        @click=\"scope.remove\"\n      >\n        <i class=\"icon icon-x\" />\n      </button>\n      <span v-else />\n    </template>\n    <!-- Pass down templates provided by the caller -->\n    <template\n      v-for=\"(_, slot) of $slots\"\n      #[slot]=\"scope\"\n      :key=\"slot\"\n    >\n      <template v-if=\"typeof $slots[slot] === 'function'\">\n        <slot\n          :name=\"slot\"\n          v-bind=\"scope\"\n        />\n      </template>\n    </template>\n  </ArrayList>\n</template>\n\n<style lang=\"scss\">\n.array-list-grouped {\n    & > .box {\n      position: relative;\n      display: block;\n\n      & > .remove {\n        position: absolute;\n\n        top: 0;\n        right: 0;\n      }\n\n      & > .info-box {\n        margin-bottom: 0;\n        padding-right: 25px;\n      }\n    }\n}\n\n</style>\n"]}]}