{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/SimpleNodeGroup.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/SimpleNodeGroup.vue", "mtime": 1755002461158}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/SimpleNodeGroup.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE/C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACpB;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACtE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC7B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;eACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5D,CAAC,CAAC;IACJ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC7B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACzD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnB,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;MAClG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAElE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC3E,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEhC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;QACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC;MACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;QACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC;MACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;UAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC;QACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;UAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC;MACF;IACF;EACF;AACF,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/SimpleNodeGroup.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent, PropType } from 'vue';\nimport LabeledInput from '@components/Form/LabeledInput/LabeledInput.vue';\nimport LabeledSelect from '@shell/components/form/LabeledSelect.vue';\nimport UnitInput from '@shell/components/form/UnitInput.vue';\nimport Banner from '@components/Banner/Banner.vue';\nimport { EKSNodeGroup, AWS } from '../../types';\n\nexport default defineComponent({\n  name: 'SimpleNodeGroup',\n\n  components: {\n    LabeledInput,\n    LabeledSelect,\n    UnitInput,\n    Banner\n  },\n\n  props: {\n    modelValue: {\n      type:     Object as PropType<EKSNodeGroup>,\n      required: true\n    },\n    instanceTypeOptions: {\n      type:    Array as PropType<AWS.InstanceTypeOption[]>,\n      default: () => []\n    },\n    rules: {\n      type:    Object,\n      default: () => ({})\n    }\n  },\n\n  emits: ['update:modelValue'],\n\n  computed: {\n    nodeGroup: {\n      get(): EKSNodeGroup {\n        return this.modelValue;\n      },\n      set(value: EKSNodeGroup) {\n        this.$emit('update:modelValue', value);\n      }\n    },\n\n    recommendedInstanceTypes(): AWS.InstanceTypeOption[] {\n      // Filter to show only recommended instance types for PLG users\n      return this.instanceTypeOptions.filter((opt: AWS.InstanceTypeOption) => {\n        if (opt.kind === 'group') return false;\n        const value = opt.value || '';\n        // Recommend t3/t4g series for cost-effectiveness\n        return value.startsWith('t3.') || value.startsWith('t4g.') || \n               value.startsWith('m5.') || value.startsWith('m6i.');\n      });\n    },\n\n    estimatedMonthlyCost(): string {\n      // Simple cost estimation based on instance type and count\n      const costMap: Record<string, number> = {\n        't3.small':   15,\n        't3.medium':  30,\n        't3.large':   60,\n        't3.xlarge':  120,\n        't4g.small':  12,\n        't4g.medium': 24,\n        't4g.large':  48,\n        'm5.large':   70,\n        'm5.xlarge':  140,\n        'm6i.large':  70,\n        'm6i.xlarge': 140,\n      };\n\n      const instanceCost = this.nodeGroup.instanceType ? costMap[this.nodeGroup.instanceType] || 50 : 50;\n      const totalCost = instanceCost * (this.nodeGroup.desiredSize || 2);\n      \n      return `~$${totalCost}/month`;\n    }\n  },\n\n  methods: {\n    updateInstanceType(value: string) {\n      this.nodeGroup.instanceType = value;\n    },\n\n    updateNodeCount(field: 'minSize' | 'maxSize' | 'desiredSize', value: string) {\n      const numValue = parseInt(value, 10) || 0;\n      this.nodeGroup[field] = numValue;\n      \n      // Ensure logical constraints\n      if (field === 'minSize' && numValue > (this.nodeGroup.maxSize || 0)) {\n        this.nodeGroup.maxSize = numValue;\n      }\n      if (field === 'maxSize' && numValue < (this.nodeGroup.minSize || 0)) {\n        this.nodeGroup.minSize = numValue;\n      }\n      if (field === 'desiredSize') {\n        if (numValue < (this.nodeGroup.minSize || 0)) {\n          this.nodeGroup.minSize = numValue;\n        }\n        if (numValue > (this.nodeGroup.maxSize || 0)) {\n          this.nodeGroup.maxSize = numValue;\n        }\n      }\n    }\n  }\n});\n</script>\n\n<template>\n  <div class=\"simple-node-group\">\n    <div class=\"node-config-section\">\n      <h4>Instance Type</h4>\n      <p class=\"text-muted mb-10\">\n        Select the computing power for your worker nodes\n      </p>\n      \n      <LabeledSelect\n        v-model:value=\"nodeGroup.instanceType\"\n        label=\"Instance Type\"\n        :options=\"recommendedInstanceTypes\"\n        :rules=\"rules.instanceType\"\n        :searchable=\"true\"\n        placeholder=\"Select an instance type\"\n      >\n        <template #option=\"{ option }\">\n          <div class=\"instance-option\">\n            <span>{{ option.label }}</span>\n            <span\n              v-if=\"option.value?.startsWith('t3.') || option.value?.startsWith('t4g.')\"\n              class=\"badge-recommended\"\n            >\n              Recommended\n            </span>\n          </div>\n        </template>\n      </LabeledSelect>\n\n      <div\n        v-if=\"nodeGroup.instanceType\"\n        class=\"cost-estimate mt-10\"\n      >\n        <i class=\"icon icon-dollar\" />\n        <span>Estimated cost: <strong>{{ estimatedMonthlyCost }}</strong></span>\n      </div>\n    </div>\n\n    <div class=\"node-config-section mt-20\">\n      <h4>Cluster Size</h4>\n      <p class=\"text-muted mb-10\">\n        Configure auto-scaling for your cluster\n      </p>\n\n      <div class=\"row\">\n        <div class=\"col span-4\">\n          <UnitInput\n            v-model:value=\"nodeGroup.minSize\"\n            label=\"Minimum Nodes\"\n            suffix=\"nodes\"\n            :rules=\"rules.minSize\"\n            @update:value=\"updateNodeCount('minSize', $event)\"\n          />\n        </div>\n        <div class=\"col span-4\">\n          <UnitInput\n            v-model:value=\"nodeGroup.desiredSize\"\n            label=\"Desired Nodes\"\n            suffix=\"nodes\"\n            :rules=\"rules.desiredSize\"\n            @update:value=\"updateNodeCount('desiredSize', $event)\"\n          />\n        </div>\n        <div class=\"col span-4\">\n          <UnitInput\n            v-model:value=\"nodeGroup.maxSize\"\n            label=\"Maximum Nodes\"\n            suffix=\"nodes\"\n            :rules=\"rules.maxSize\"\n            @update:value=\"updateNodeCount('maxSize', $event)\"\n          />\n        </div>\n      </div>\n\n      <Banner\n        color=\"info\"\n        class=\"mt-10\"\n      >\n        <p>\n          Your cluster will automatically scale between \n          <strong>{{ nodeGroup.minSize }}</strong> and \n          <strong>{{ nodeGroup.maxSize }}</strong> nodes based on workload.\n        </p>\n      </Banner>\n    </div>\n\n    <div class=\"node-config-section mt-20\">\n      <h4>Storage</h4>\n      <p class=\"text-muted mb-10\">\n        Disk space for each node\n      </p>\n      \n      <div class=\"row\">\n        <div class=\"col span-6\">\n          <UnitInput\n            v-model:value=\"nodeGroup.diskSize\"\n            label=\"Disk Size\"\n            suffix=\"GB\"\n            :rules=\"rules.diskSize\"\n          />\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.simple-node-group {\n  .node-config-section {\n    padding: 15px;\n    background: var(--body-bg);\n    border: 1px solid var(--border);\n    border-radius: var(--border-radius);\n\n    h4 {\n      margin: 0 0 5px 0;\n      font-size: 16px;\n      font-weight: 600;\n    }\n\n    .text-muted {\n      color: var(--text-muted);\n      font-size: 14px;\n    }\n  }\n\n  .instance-option {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    width: 100%;\n\n    .badge-recommended {\n      background: var(--success);\n      color: var(--success-text);\n      padding: 2px 8px;\n      border-radius: 3px;\n      font-size: 11px;\n      font-weight: 600;\n      text-transform: uppercase;\n    }\n  }\n\n  .cost-estimate {\n    padding: 10px;\n    background: var(--info-banner-bg);\n    border: 1px solid var(--info);\n    border-radius: var(--border-radius);\n    display: flex;\n    align-items: center;\n    gap: 10px;\n    color: var(--text-default);\n\n    i {\n      color: var(--info);\n    }\n\n    strong {\n      color: var(--text-default);\n      font-weight: 600;\n    }\n  }\n}\n</style>\n\n"]}]}