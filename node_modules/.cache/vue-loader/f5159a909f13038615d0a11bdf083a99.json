{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ExplorerMembers.vue?vue&type=style&index=0&id=7242ddb2&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ExplorerMembers.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Cgoucm9sZSB7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMCwgMCwgMCwgMC4wNSk7CiAgICBib3JkZXI6IDFweCBzb2xpZCB2YXIoLS1oZWFkZXItYm9yZGVyKTsKICAgIGJvcmRlci1yYWRpdXM6IDVweDsKICAgIGNvbG9yOiB2YXIoLS10YWctdGV4dCk7CiAgICBsaW5lLWhlaWdodDogMjBweDsKICAgIHBhZGRpbmc6IDJweCA1cHg7CiAgICB3aGl0ZS1zcGFjZTogbm93cmFwOwogICAgZGlzcGxheTogaW5saW5lLWZsZXg7CiAgICBtYXJnaW4tcmlnaHQ6IDNweDsKfQoKLnJvbGUtdmFsdWUgewogICYudGV4dC1saW5rLWVuYWJsZWQgewogICAgY3Vyc29yOiBwb2ludGVyOwogICAgJjpob3ZlciB7CiAgICAgIGNvbG9yOiB2YXIoLS1wcmltYXJ5KTsKICAgIH0KICB9CiAgKyAuaWNvbi1jbG9zZSB7CiAgICBtYXJnaW4tbGVmdDogM3B4OwogICAgY3Vyc29yOiBwb2ludGVyOwogICAgJjpob3ZlciB7CiAgICAgIGNvbG9yOiB2YXIoLS1wcmltYXJ5KTsKICAgIH0KICB9Cn0KCi5wcm9qZWN0LW1lbWJlcnMgewogICYgOmRlZXAoKSAuZ3JvdXAtYmFyewogICAgZGlzcGxheTogZmxleDsKICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICB9Cn0KLmNsdXN0ZXItYWRkIHsKICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kOwp9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ExplorerMembers.vue"], "names": [], "mappings": ";;AA8ZA,CAAC,CAAC,CAAC,CAAC,EAAE;EACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACrB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB;EACF;EACA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB;EACF;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACf,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC;AACF;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ExplorerMembers.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { MANAGEMENT, NORMAN, VIRTUAL_TYPES } from '@shell/config/types';\nimport ResourceTable from '@shell/components/ResourceTable';\nimport Ma<PERSON>head from '@shell/components/ResourceList/Masthead';\nimport { AGE, ROLE, STATE, PRINCIPAL } from '@shell/config/table-headers';\nimport { canViewClusterPermissionsEditor } from '@shell/components/form/Members/ClusterPermissionsEditor.vue';\nimport Banner from '@components/Banner/Banner.vue';\nimport Tabbed from '@shell/components/Tabbed/index.vue';\nimport Tab from '@shell/components/Tabbed/Tab.vue';\nimport SortableTable from '@shell/components/SortableTable';\nimport { mapGetters } from 'vuex';\nimport { canViewProjectMembershipEditor } from '@shell/components/form/Members/ProjectMembershipEditor.vue';\nimport { allHash } from '@shell/utils/promise';\nimport { HARVESTER_NAME as HARVESTER } from '@shell/config/features';\n\n/**\n * Explorer members page.\n * Route: /c/local/explorer/members\n */\nexport default {\n  name: 'ExplorerMembers',\n\n  components: {\n    <PERSON>,\n    Masthead,\n    ResourceTable,\n    Tabbed,\n    Tab,\n    SortableTable\n  },\n\n  props: {\n    // Cluster tole template binding create route - defaults to the explorer route\n    createLocationOverride: {\n      type:    Object,\n      default: () => {\n        return {\n          name:   'c-cluster-product-resource-create',\n          params: { resource: MANAGEMENT.CLUSTER_ROLE_TEMPLATE_BINDING }\n        };\n      }\n    }\n  },\n\n  async fetch() {\n    const clusterRoleTemplateBindingSchema = this.$store.getters[\n      `rancher/schemaFor`\n    ](NORMAN.CLUSTER_ROLE_TEMPLATE_BINDING);\n\n    const projectRoleTemplateBindingSchema = this.$store.getters['rancher/schemaFor'](NORMAN.PROJECT_ROLE_TEMPLATE_BINDING);\n\n    this['normanClusterRTBSchema'] = clusterRoleTemplateBindingSchema;\n    this['normanProjectRTBSchema'] = projectRoleTemplateBindingSchema;\n\n    if (clusterRoleTemplateBindingSchema) {\n      Promise.all([\n        this.$store.dispatch(`rancher/findAll`, { type: NORMAN.CLUSTER_ROLE_TEMPLATE_BINDING }, { root: true }),\n        this.$store.dispatch(`management/findAll`, { type: MANAGEMENT.CLUSTER_ROLE_TEMPLATE_BINDING })\n      ]).then(([normanBindings]) => {\n        this['normanClusterRoleTemplateBindings'] = normanBindings;\n        this.loadingClusterBindings = false;\n      });\n    }\n\n    if (projectRoleTemplateBindingSchema) {\n      this.$store.dispatch('rancher/findAll', { type: NORMAN.PROJECT_ROLE_TEMPLATE_BINDING }, { root: true })\n        .then((bindings) => {\n          this['projectRoleTemplateBindings'] = bindings;\n          this.loadingProjectBindings = false;\n        });\n    }\n\n    this.$store.dispatch('management/findAll', { type: MANAGEMENT.PROJECT })\n      .then((projects) => (this['projects'] = projects));\n\n    const hydration = {\n      normanPrincipals:  this.$store.dispatch('rancher/findAll', { type: NORMAN.PRINCIPAL }),\n      mgmt:              this.$store.dispatch(`management/findAll`, { type: MANAGEMENT.USER }),\n      mgmtRoleTemplates: this.$store.dispatch(`management/findAll`, { type: MANAGEMENT.ROLE_TEMPLATE }),\n    };\n\n    await allHash(hydration);\n  },\n\n  data() {\n    return {\n      schema: this.$store.getters[`management/schemaFor`](\n        MANAGEMENT.CLUSTER_ROLE_TEMPLATE_BINDING\n      ),\n      headers:        [STATE, PRINCIPAL, ROLE, AGE],\n      createLocation: {\n        ...this.createLocationOverride,\n        params: {\n          ...this.createLocationOverride.params,\n          cluster: this.$store.getters['currentCluster'].id\n        }\n      },\n      resource:                          MANAGEMENT.CLUSTER_ROLE_TEMPLATE_BINDING,\n      normanClusterRTBSchema:            null,\n      normanProjectRTBSchema:            null,\n      normanClusterRoleTemplateBindings: [],\n      projectRoleTemplateBindings:       [],\n      projects:                          [],\n      VIRTUAL_TYPES,\n      projectRoleTemplateColumns:        [\n        STATE,\n        {\n          name:      'member',\n          labeKey:   'generic.name',\n          value:     'principalId',\n          formatter: 'Principal'\n        },\n        {\n          name:     'role',\n          labelKey: 'tableHeaders.role',\n          value:    'roleTemplate.nameDisplay'\n        },\n      ],\n      loadingProjectBindings: true,\n      loadingClusterBindings: true\n    };\n  },\n\n  computed: {\n    ...mapGetters(['currentCluster']),\n    clusterRoleTemplateBindings() {\n      return this.normanClusterRoleTemplateBindings.map((b) => b.clusterroletemplatebinding) ;\n    },\n    filteredClusterRoleTemplateBindings() {\n      return this.clusterRoleTemplateBindings.filter(\n        (b) => b?.clusterName === this.$store.getters['currentCluster'].id\n      );\n    },\n    filteredProjects() {\n      return this.projects.reduce((all, p) => {\n        if (p?.spec?.clusterName === this.currentCluster.id) {\n          all[p.id] = p;\n        }\n\n        return all;\n      }, {});\n    },\n    filteredProjectRoleTemplateBindings() {\n      const out = this.projectRoleTemplateBindings.filter((rb) => {\n        const projectId = rb.projectId.replace(':', '/');\n\n        return !!this.filteredProjects[projectId];\n      });\n\n      return out;\n    },\n    projectsWithoutRoles() {\n      const inUse = this.filteredProjectRoleTemplateBindings.reduce((projects, binding) => {\n        const thisProjectId = (binding.projectId || '').replace(':', '/');\n\n        if (!projects.includes(thisProjectId)) {\n          projects.push(thisProjectId);\n        }\n\n        return projects;\n      }, []);\n\n      return Object.keys(this.filteredProjects).reduce((all, projectId) => {\n        const project = this.filteredProjects[projectId];\n\n        if ( !inUse.includes(projectId)) {\n          all.push(project);\n        }\n\n        return all;\n      }, []);\n    },\n\n    // We're using this because we need to show projects as groups even if the project doesn't have any role bindings\n    rowsWithFakeProjects() {\n      const fakeRows = this.projectsWithoutRoles.map((project) => {\n        return {\n          groupByLabel:     `${ ('resourceTable.groupLabel.notInAProject') }-${ project.id }`,\n          isFake:           true,\n          mainRowKey:       project.id,\n          nameDisplay:      project.spec?.displayName, // Enable filtering by the project name\n          project,\n          availableActions: [],\n          projectId:        project.id\n        };\n      });\n\n      // We need to group each of the TemplateRoleBindings by the user + project\n      const userRoles = [...fakeRows, ...this.filteredProjectRoleTemplateBindings].reduce((rows, curr) => {\n        const {\n          userId, groupPrincipalId, roleTemplate, projectId\n        } = curr;\n\n        const userOrGroup = userId || groupPrincipalId;\n\n        if (!userOrGroup) {\n          return rows;\n        }\n\n        const userOrGroupKey = userOrGroup + projectId;\n\n        if (!rows[userOrGroupKey] ) {\n          rows[userOrGroupKey] = curr;\n          rows[userOrGroupKey].allRoles = [];\n        }\n\n        if (roleTemplate) {\n          rows[userOrGroupKey].allRoles.push(curr.roleTemplate);\n        }\n\n        return rows;\n      }, {});\n\n      return Object.values(userRoles);\n    },\n    canManageMembers() {\n      return canViewClusterPermissionsEditor(this.$store);\n    },\n    canManageProjectMembers() {\n      return canViewProjectMembershipEditor(this.$store);\n    },\n    isLocal() {\n      return this.$store.getters['currentCluster'].isLocal;\n    },\n    canEditProjectMembers() {\n      return this.normanProjectRTBSchema?.collectionMethods.find((x) => x.toLowerCase() === 'post');\n    },\n    canEditClusterMembers() {\n      return this.normanClusterRTBSchema?.collectionMethods.find((x) => x.toLowerCase() === 'post');\n    },\n    isHarvester() {\n      return this.$store.getters['currentProduct'].inStore === HARVESTER;\n    },\n  },\n  methods: {\n    getMgmtProjectId(group) {\n      return group.group.key.replace(':', '/');\n    },\n    getMgmtProject(group) {\n      return this.$store.getters['management/byId'](MANAGEMENT.PROJECT, this.getMgmtProjectId(group));\n    },\n    getProjectLabel(group) {\n      return this.getMgmtProject(group)?.spec?.displayName;\n    },\n    addProjectMember(group) {\n      this.$store.dispatch('cluster/promptModal', {\n        component:      'AddProjectMemberDialog',\n        componentProps: {\n          projectId:   group.group.key.replace('/', ':'),\n          saveInModal: true\n        },\n        modalSticky: true\n      });\n    },\n\n    getProjectRoleBinding(row, role) {\n      // Each row is a combination of project, role and user/group\n      // So find the specfic roleBindingTemplate corresponding to the specific project, role + user/group\n      const userOrGroupKey = row.userId ? 'userId' : 'groupPrincipalId';\n\n      return this.projectRoleTemplateBindings.find((r) => {\n        return r.projectId === row.projectId && r.roleTemplateId === role.id && r[userOrGroupKey] === row[userOrGroupKey];\n      });\n    },\n\n    async removeRole(row, role, event) {\n      const resource = this.getProjectRoleBinding(row, role);\n\n      await resource.promptRemove();\n    },\n\n    viewRoleInAPI(row, role) {\n      const resource = this.getProjectRoleBinding(row, role);\n\n      if (resource?.canViewInApi) {\n        resource.viewInApi();\n      }\n    },\n    slotName(project) {\n      return `main-row:${ project.id }`;\n    },\n  }\n};\n</script>\n\n<template>\n  <div class=\"project-members\">\n    <Masthead\n      :schema=\"schema\"\n      :resource=\"resource\"\n      :favorite-resource=\"VIRTUAL_TYPES.CLUSTER_MEMBERS\"\n      :create-location=\"createLocation\"\n      :create-button-label=\"t('members.createActionLabel')\"\n      :is-creatable=\"false\"\n      :type-display=\"t('members.clusterAndProject')\"\n    />\n    <Banner\n      v-if=\"isLocal\"\n      color=\"error\"\n      :label=\"t('members.localClusterWarning')\"\n    />\n    <Tabbed>\n      <Tab\n        name=\"cluster-membership\"\n        :label=\"t('members.clusterMembership')\"\n      >\n        <div\n          v-if=\"canEditClusterMembers\"\n          class=\"row mb-10 cluster-add\"\n        >\n          <router-link\n            :to=\"createLocation\"\n            class=\"btn role-primary pull-right\"\n          >\n            {{ t('members.createActionLabel') }}\n          </router-link>\n        </div>\n        <ResourceTable\n          :schema=\"schema\"\n          :headers=\"headers\"\n          :rows=\"filteredClusterRoleTemplateBindings\"\n          :groupable=\"true\"\n          :show-grouping=\"true\"\n          :namespaced=\"false\"\n          :loading=\"$fetchState.pending || !currentCluster || loadingClusterBindings\"\n          sub-search=\"subSearch\"\n          :sub-fields=\"['nameDisplay']\"\n        />\n      </Tab>\n      <Tab\n        v-if=\"canManageProjectMembers && !isHarvester\"\n        name=\"project-membership\"\n        :label=\"t('members.projectMembership')\"\n      >\n        <SortableTable\n          group-by=\"projectId\"\n          :loading=\"$fetchState.pending || !currentCluster || loadingProjectBindings\"\n          :rows=\"rowsWithFakeProjects\"\n          :headers=\"projectRoleTemplateColumns\"\n          :table-actions=\"false\"\n          :row-actions=\"false\"\n        >\n          <template #group-by=\"group\">\n            <div class=\"group-bar\">\n              <div\n                v-trim-whitespace\n                class=\"group-tab\"\n              >\n                <div\n                  v-clean-html=\"getProjectLabel(group)\"\n                  class=\"project-name\"\n                />\n              </div>\n              <div class=\"right\">\n                <button\n                  v-if=\"canEditProjectMembers\"\n                  type=\"button\"\n                  class=\"btn btn-sm role-secondary mr-10 right\"\n                  :data-testid=\"`add-project-member-${getProjectLabel(group).replace(' ', '').toLowerCase()}`\"\n                  @click=\"addProjectMember(group)\"\n                >\n                  {{ t('members.createActionLabel') }}\n                </button>\n              </div>\n            </div>\n          </template>\n          <template\n            #cell:role=\"{row}\"\n          >\n            <span\n              v-for=\"(role, j) in row.allRoles\"\n              :key=\"j\"\n              ref=\"value\"\n              :data-testid=\"`role-value-${j}`\"\n              class=\"role\"\n            >\n              <span\n                class=\"role-value\"\n                :class=\"{'text-link-enabled' : row.canViewInApi}\"\n                @click=\"viewRoleInAPI(row, role)\"\n              >\n                {{ role.nameDisplay }}\n              </span>\n              <i\n                class=\"icon icon-close\"\n                :data-testid=\"`role-values-close-${j}`\"\n                @click=\"removeRole(row, role, $event)\"\n              />\n            </span>\n          </template>\n          <template\n            v-for=\"(project, i) in projectsWithoutRoles\"\n            :key=\"i\"\n            v-slot:[slotName(project)]\n          >\n            <tr\n              class=\"main-row\"\n            >\n              <td\n                class=\"empty text-center\"\n                colspan=\"100%\"\n              >\n                {{ t('members.noRolesAssigned') }}\n              </td>\n            </tr>\n          </template>\n        </SortableTable>\n      </Tab>\n    </Tabbed>\n  </div>\n</template>\n\n<style lang='scss' scoped>\n\n.role {\n  align-items: center;\n    background-color: rgba(0, 0, 0, 0.05);\n    border: 1px solid var(--header-border);\n    border-radius: 5px;\n    color: var(--tag-text);\n    line-height: 20px;\n    padding: 2px 5px;\n    white-space: nowrap;\n    display: inline-flex;\n    margin-right: 3px;\n}\n\n.role-value {\n  &.text-link-enabled {\n    cursor: pointer;\n    &:hover {\n      color: var(--primary);\n    }\n  }\n  + .icon-close {\n    margin-left: 3px;\n    cursor: pointer;\n    &:hover {\n      color: var(--primary);\n    }\n  }\n}\n\n.project-members {\n  & :deep() .group-bar{\n    display: flex;\n    justify-content: space-between;\n  }\n}\n.cluster-add {\n  justify-content: flex-end;\n}\n</style>\n"]}]}