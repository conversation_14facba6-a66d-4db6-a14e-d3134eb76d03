{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetIntro.vue?vue&type=template&id=d24df9bc&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetIntro.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdiBjbGFzcz0iaW50cm8tYm94Ij4KICAgIDxpIGNsYXNzPSJpY29uIGljb24tcmVwb3NpdG9yeSIgLz4KICAgIDxkaXYgY2xhc3M9InRpdGxlIj4KICAgICAge3sgdCgnZmxlZXQuZ2l0UmVwby5yZXBvLm5vUmVwb3MnKSB9fQogICAgPC9kaXY+CiAgICA8ZGl2CiAgICAgIHYtaWY9ImNhbkNyZWF0ZVJlcG9zIgogICAgICBjbGFzcz0iYWN0aW9ucyIKICAgID4KICAgICAgPHJvdXRlci1saW5rCiAgICAgICAgOnRvPSJnaXRSZXBvUm91dGUiCiAgICAgICAgY2xhc3M9ImJ0biByb2xlLXNlY29uZGFyeSIKICAgICAgPgogICAgICAgIHt7IHQoJ2ZsZWV0LmdpdFJlcG8ucmVwby5hZGRSZXBvJykgfX0KICAgICAgPC9yb3V0ZXItbGluaz4KICAgIDwvZGl2PgogIDwvZGl2Pgo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetIntro.vue"], "names": [], "mappings": ";EA4BE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACtC,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB;MACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B;QACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetIntro.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { FLEET } from '@shell/config/types';\nimport { NAME } from '@shell/config/product/fleet';\n\nexport default {\n\n  name: 'FleetIntro',\n\n  data() {\n    const gitRepoRoute = {\n      name:   'c-cluster-product-resource-create',\n      params: {\n        product:  NAME,\n        resource: FLEET.GIT_REPO\n      },\n    };\n\n    const gitRepoSchema = this.$store.getters['management/schemaFor'](FLEET.GIT_REPO);\n    const canCreateRepos = gitRepoSchema && gitRepoSchema.resourceMethods.includes('PUT');\n\n    return {\n      gitRepoRoute,\n      canCreateRepos\n    };\n  },\n};\n</script>\n<template>\n  <div class=\"intro-box\">\n    <i class=\"icon icon-repository\" />\n    <div class=\"title\">\n      {{ t('fleet.gitRepo.repo.noRepos') }}\n    </div>\n    <div\n      v-if=\"canCreateRepos\"\n      class=\"actions\"\n    >\n      <router-link\n        :to=\"gitRepoRoute\"\n        class=\"btn role-secondary\"\n      >\n        {{ t('fleet.gitRepo.repo.addRepo') }}\n      </router-link>\n    </div>\n  </div>\n</template>\n<style lang=\"scss\" scoped>\n.intro-box {\n  flex: 0 0 100%;\n  height: calc(100vh - 246px); // 2(48 content header + 20 padding + 55 pageheader)\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-direction: column;\n}\n\n.title {\n  margin-bottom: 15px;\n  font-size: $font-size-h2;\n}\n.icon-repository {\n  font-size: 96px;\n  margin-bottom: 32px;\n}\n</style>\n"]}]}