{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Accordion/Accordion.vue?vue&type=template&id=42964d6c&scoped=true&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Accordion/Accordion.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdiBjbGFzcz0iYWNjb3JkaW9uLWNvbnRhaW5lciI+CiAgICA8ZGl2CiAgICAgIGNsYXNzPSJhY2NvcmRpb24taGVhZGVyIgogICAgICBkYXRhLXRlc3RpZD0iYWNjb3JkaW9uLWhlYWRlciIKICAgICAgQGNsaWNrPSJ0b2dnbGUiCiAgICA+CiAgICAgIDxpCiAgICAgICAgY2xhc3M9Imljb24gdGV4dC1wcmltYXJ5IgogICAgICAgIDpjbGFzcz0ieydpY29uLWNoZXZyb24tZG93bic6aXNPcGVuLCAnaWNvbi1jaGV2cm9uLXVwJzohaXNPcGVufSIKICAgICAgICBkYXRhLXRlc3RpZD0iYWNjb3JkaW9uLWNoZXZyb24iCiAgICAgIC8+CiAgICAgIDxzbG90IG5hbWU9ImhlYWRlciI+CiAgICAgICAgPGgyCiAgICAgICAgICBkYXRhLXRlc3RpZD0iYWNjb3JkaW9uLXRpdGxlLXNsb3QtY29udGVudCIKICAgICAgICAgIGNsYXNzPSJtYi0wIgogICAgICAgID4KICAgICAgICAgIHt7IHRpdGxlS2V5ID8gdCh0aXRsZUtleSkgOiB0aXRsZSB9fQogICAgICAgIDwvaDI+CiAgICAgIDwvc2xvdD4KICAgIDwvZGl2PgogICAgPGRpdgogICAgICB2LXNob3c9ImlzT3BlbiIKICAgICAgY2xhc3M9ImFjY29yZGlvbi1ib2R5IgogICAgICBkYXRhLXRlc3RpZD0iYWNjb3JkaW9uLWJvZHkiCiAgICA+CiAgICAgIDxzbG90IC8+CiAgICA8L2Rpdj4KICA8L2Rpdj4K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Accordion/Accordion.vue"], "names": [], "mappings": ";EAqCE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,<PERSON><PERSON>;IAChB;MACE,CAAC;QACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC;UACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb;UACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B;MACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Accordion/Accordion.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent } from 'vue';\nimport { mapGetters } from 'vuex';\n\nexport default defineComponent({\n  props: {\n    title: {\n      type:    String,\n      default: ''\n    },\n\n    titleKey: {\n      type:    String,\n      default: null\n    },\n\n    openInitially: {\n      type:    Boolean,\n      default: false\n    }\n  },\n\n  data() {\n    return { isOpen: this.openInitially };\n  },\n\n  computed: { ...mapGetters({ t: 'i18n/t' }) },\n\n  methods: {\n    toggle() {\n      this.isOpen = !this.isOpen;\n    }\n  }\n});\n</script>\n\n<template>\n  <div class=\"accordion-container\">\n    <div\n      class=\"accordion-header\"\n      data-testid=\"accordion-header\"\n      @click=\"toggle\"\n    >\n      <i\n        class=\"icon text-primary\"\n        :class=\"{'icon-chevron-down':isOpen, 'icon-chevron-up':!isOpen}\"\n        data-testid=\"accordion-chevron\"\n      />\n      <slot name=\"header\">\n        <h2\n          data-testid=\"accordion-title-slot-content\"\n          class=\"mb-0\"\n        >\n          {{ titleKey ? t(titleKey) : title }}\n        </h2>\n      </slot>\n    </div>\n    <div\n      v-show=\"isOpen\"\n      class=\"accordion-body\"\n      data-testid=\"accordion-body\"\n    >\n      <slot />\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.accordion-container {\n  border: 1px solid var(--border)\n}\n.accordion-header {\n  padding: 16px 16px 16px 11px;\n  display: flex;\n  align-items: center;\n  &>*{\n    padding: 5px 0px 5px 0px;\n  }\n  I {\n    margin: 0px 10px 0px 10px;\n  }\n}\n.accordion-body {\n  padding: 0px 16px 16px;\n}\n</style>\n"]}]}