{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/ResourcesSummary.vue?vue&type=style&index=0&id=7a9736b6&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/ResourcesSummary.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLmZsZXh3cmFwIHsKICAgIGZsZXgtd3JhcDogd3JhcDsKICB9CiAgLmNvdW50Ym94IHsKICAgIG1pbi13aWR0aDogMTUwcHg7CiAgICB3aWR0aDogMTIuNSU7CiAgICBtYXJnaW4tYm90dG9tOiAxMHB4OwogIH0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/ResourcesSummary.vue"], "names": [], "mappings": ";EAoFE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/ResourcesSummary.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport capitalize from 'lodash/capitalize';\nimport CountBox from '@shell/components/CountBox';\nimport { STATES } from '@shell/plugins/dashboard-store/resource-class';\n\nexport default {\n\n  name: 'ResourcesSummary',\n\n  components: { CountBox },\n\n  props: {\n    value: {\n      type:     Object,\n      required: true,\n    },\n\n    stateKey: {\n      type:    String,\n      default: 'fleet.fleetSummary.state'\n    },\n\n    requiredStates: {\n      type:    Array,\n      default: () => []\n    }\n  },\n\n  computed: {\n    counts() {\n      const out = this.requiredStates.reduce((obj, state) => {\n        obj[state] = {\n          count: 0,\n          color: state,\n          label: this.$store.getters['i18n/withFallback'](`${ this.stateKey }.${ state }`, null, state)\n        };\n\n        return obj;\n      }, {});\n\n      for (const k in this.value) {\n        if (k.startsWith('desired')) {\n          continue;\n        }\n\n        const mapped = STATES[k] || STATES['other'];\n\n        if (out[k]) {\n          out[k].count += this.value[k] || 0;\n          out[k].color = mapped.color;\n        } else {\n          out[k] = {\n            count: this.value[k] || 0,\n            color: mapped.color,\n            label: this.$store.getters['i18n/withFallback'](`${ this.stateKey }.${ k }`, null, capitalize(k))\n          };\n        }\n      }\n\n      return out;\n    },\n  },\n\n  methods: { capitalize },\n};\n</script>\n\n<template>\n  <div class=\"row flexwrap\">\n    <div\n      v-for=\"(v, k) in counts\"\n      :key=\"k\"\n      class=\"col countbox\"\n    >\n      <CountBox\n        :compact=\"true\"\n        :count=\"v['count']\"\n        :name=\"v.label\"\n        :primary-color-var=\"'--sizzle-' + v.color\"\n      />\n    </div>\n  </div>\n</template>\n<style lang=\"scss\" scoped>\n  .flexwrap {\n    flex-wrap: wrap;\n  }\n  .countbox {\n    min-width: 150px;\n    width: 12.5%;\n    margin-bottom: 10px;\n  }\n</style>\n"]}]}