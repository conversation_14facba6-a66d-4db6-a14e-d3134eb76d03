{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Pinned.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Pinned.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci8vIEFsbG93IHRoZSB1c2VyIHRvIHBpbiBhIGNsdXN0ZXIgYnkgY2xpY2tpbmcgaXQuCmV4cG9ydCBkZWZhdWx0IHsKICBwcm9wczogewogICAgY2x1c3RlcjogewogICAgICB0eXBlOiAgICAgT2JqZWN0LAogICAgICByZXF1aXJlZDogdHJ1ZSwKICAgIH0sCiAgICB0YWJPcmRlcjogewogICAgICB0eXBlOiAgICBOdW1iZXIsCiAgICAgIGRlZmF1bHQ6IG51bGwsCiAgICB9CiAgfSwKCiAgY29tcHV0ZWQ6IHsKICAgIHBpbm5lZCgpIHsKICAgICAgcmV0dXJuIHRoaXMuY2x1c3Rlci5waW5uZWQ7CiAgICB9CiAgfSwKCiAgbWV0aG9kczogewogICAgdG9nZ2xlKCkgewogICAgICBpZiAoIHRoaXMucGlubmVkICkgewogICAgICAgIHRoaXMuY2x1c3Rlci51bnBpbigpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuY2x1c3Rlci5waW4oKTsKICAgICAgfQogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Pinned.vue"], "names": [], "mappings": ";AACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AACjD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB;IACF;EACF;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Pinned.vue", "sourceRoot": "", "sourcesContent": ["<script>\n// Allow the user to pin a cluster by clicking it.\nexport default {\n  props: {\n    cluster: {\n      type:     Object,\n      required: true,\n    },\n    tabOrder: {\n      type:    Number,\n      default: null,\n    }\n  },\n\n  computed: {\n    pinned() {\n      return this.cluster.pinned;\n    }\n  },\n\n  methods: {\n    toggle() {\n      if ( this.pinned ) {\n        this.cluster.unpin();\n      } else {\n        this.cluster.pin();\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <i\n    :tabindex=\"tabOrder\"\n    :aria-checked=\"!!pinned\"\n    class=\"pin icon\"\n    :class=\"{'icon-pin-outlined': !pinned, 'icon-pin': pinned}\"\n    role=\"button\"\n    :aria-label=\"t('nav.ariaLabel.pinCluster', { cluster: cluster.label })\"\n    @click.stop.prevent=\"toggle\"\n    @keydown.enter.prevent=\"toggle\"\n    @keydown.space.prevent=\"toggle\"\n  />\n</template>\n\n<style lang=\"scss\" scoped>\n  .icon {\n    font-size: 14px;\n    transform: scaleX(-1);\n  }\n</style>\n"]}]}