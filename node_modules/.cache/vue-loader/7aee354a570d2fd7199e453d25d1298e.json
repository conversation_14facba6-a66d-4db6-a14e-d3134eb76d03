{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/KnownHostsEditDialog.vue?vue&type=style&index=0&id=73593971&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/KnownHostsEditDialog.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLnNzaC1rbm93bi1ob3N0cy1kaWFsb2cgewogICAgcGFkZGluZzogMTVweDsKCiAgICBoNCB7CiAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgICBtYXJnaW4tYm90dG9tOiAyMHB4OwogICAgfQoKICAgIC5kaWFsb2ctcGFuZWwgewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogICAgICBtaW4taGVpZ2h0OiAxMDBweDsKCiAgICAgIDpkZWVwKCkgLmNvZGUtbWlycm9yIHsKICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICAgICAgcmVzaXplOiBub25lOwoKICAgICAgICAuY29kZW1pcnJvci1jb250YWluZXIgewogICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgdmFyKC0tYm9yZGVyKTsKICAgICAgICB9CgogICAgICAgIC5Db2RlTWlycm9yLAogICAgICAgIC5Db2RlTWlycm9yLWd1dHRlcnMgewogICAgICAgICAgbWluLWhlaWdodDogNDAwcHg7CiAgICAgICAgICBtYXgtaGVpZ2h0OiA0MDBweDsKICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLXlhbWwtZWRpdG9yLWJnKTsKICAgICAgICB9CgogICAgICAgIC5Db2RlTWlycm9yLWd1dHRlcnMgewogICAgICAgICAgd2lkdGg6IDI1cHg7CiAgICAgICAgfQoKICAgICAgICAuQ29kZU1pcnJvci1saW5lbnVtYmVyIHsKICAgICAgICAgIHBhZGRpbmctbGVmdDogMDsKICAgICAgICB9CiAgICAgIH0KICAgIH0KCiAgICAuZGlhbG9nLWFjdGlvbnMgewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CgogICAgICAuYWN0aW9uLXBhbm5lbCB7CiAgICAgICAgbWFyZ2luLXRvcDogMTBweDsKICAgICAgfQoKICAgICAgLmZvcm0tYWN0aW9ucyB7CiAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kOwoKICAgICAgICA+ICo6bm90KDpsYXN0LWNoaWxkKSB7CiAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDEwcHg7CiAgICAgICAgfQogICAgICB9CiAgICB9CiAgfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/KnownHostsEditDialog.vue"], "names": [], "mappings": ";EAuIE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEb,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACjB;MACF;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAClB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEzB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACpB;MACF;IACF;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/KnownHostsEditDialog.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\nimport CodeMirror from '@shell/components/CodeMirror';\nimport FileSelector from '@shell/components/form/FileSelector.vue';\nimport AppModal from '@shell/components/AppModal.vue';\n\nexport default {\n  emits: ['closed'],\n\n  components: {\n    FileSelector,\n    AppModal,\n    CodeMirror,\n  },\n\n  props: {\n    value: {\n      type:     String,\n      required: true\n    },\n\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n  },\n\n  data() {\n    const codeMirrorOptions = {\n      readOnly:        this.isView,\n      gutters:         ['CodeMirror-foldgutter'],\n      mode:            'text/x-properties',\n      lint:            false,\n      lineNumbers:     !this.isView,\n      styleActiveLine: false,\n      tabSize:         2,\n      indentWithTabs:  false,\n      cursorBlinkRate: 530,\n    };\n\n    return {\n      codeMirrorOptions,\n      text:      this.value,\n      showModal: false,\n    };\n  },\n\n  computed: {\n    isView() {\n      return this.mode === _VIEW;\n    }\n  },\n\n  methods: {\n    onTextChange(value) {\n      this.text = value?.trim();\n    },\n\n    showDialog() {\n      this.showModal = true;\n    },\n\n    closeDialog(result) {\n      if (!result) {\n        this.text = this.value;\n      }\n\n      this.showModal = false;\n\n      this.$emit('closed', {\n        success: result,\n        value:   this.text,\n      });\n    },\n  }\n};\n</script>\n\n<template>\n  <app-modal\n    v-if=\"showModal\"\n    ref=\"sshKnownHostsDialog\"\n    data-testid=\"sshKnownHostsDialog\"\n    height=\"auto\"\n    :scrollable=\"true\"\n    @close=\"closeDialog(false)\"\n  >\n    <div\n      class=\"ssh-known-hosts-dialog\"\n    >\n      <h4 class=\"mt-10\">\n        {{ t('secret.ssh.editKnownHosts.title') }}\n      </h4>\n      <div class=\"custom mt-10\">\n        <div class=\"dialog-panel\">\n          <CodeMirror\n            :value=\"text\"\n            data-testid=\"ssh-known-hosts-dialog_code-mirror\"\n            :options=\"codeMirrorOptions\"\n            :showKeyMapBox=\"true\"\n            @onInput=\"onTextChange\"\n          />\n        </div>\n        <div class=\"dialog-actions\">\n          <div class=\"action-pannel file-selector\">\n            <FileSelector\n              class=\"btn role-secondary\"\n              data-testid=\"ssh-known-hosts-dialog_file-selector\"\n              :label=\"t('generic.readFromFile')\"\n              @selected=\"onTextChange\"\n            />\n          </div>\n          <div class=\"action-pannel form-actions\">\n            <button\n              class=\"btn role-secondary\"\n              data-testid=\"ssh-known-hosts-dialog_cancel-btn\"\n              @click=\"closeDialog(false)\"\n            >\n              {{ t('generic.cancel') }}\n            </button>\n            <button\n              class=\"btn role-primary\"\n              data-testid=\"ssh-known-hosts-dialog_save-btn\"\n              @click=\"closeDialog(true)\"\n            >\n              {{ t('generic.save') }}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </app-modal>\n</template>\n\n<style lang=\"scss\" scoped>\n  .ssh-known-hosts-dialog {\n    padding: 15px;\n\n    h4 {\n      font-weight: bold;\n      margin-bottom: 20px;\n    }\n\n    .dialog-panel {\n      display: flex;\n      flex-direction: column;\n      min-height: 100px;\n\n      :deep() .code-mirror {\n        display: flex;\n        flex-direction: column;\n        resize: none;\n\n        .codemirror-container {\n          border: 1px solid var(--border);\n        }\n\n        .CodeMirror,\n        .CodeMirror-gutters {\n          min-height: 400px;\n          max-height: 400px;\n          background-color: var(--yaml-editor-bg);\n        }\n\n        .CodeMirror-gutters {\n          width: 25px;\n        }\n\n        .CodeMirror-linenumber {\n          padding-left: 0;\n        }\n      }\n    }\n\n    .dialog-actions {\n      display: flex;\n      justify-content: space-between;\n\n      .action-pannel {\n        margin-top: 10px;\n      }\n\n      .form-actions {\n        display: flex;\n        justify-content: flex-end;\n\n        > *:not(:last-child) {\n          margin-right: 10px;\n        }\n      }\n    }\n  }\n</style>\n"]}]}