{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ActionDropdown.vue?vue&type=template&id=da1fb5ac", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ActionDropdown.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdiBjbGFzcz0iZHJvcGRvd24tYnV0dG9uLWdyb3VwIj4KICAgIDxkaXYKICAgICAgY2xhc3M9ImRyb3Bkb3duLWJ1dHRvbiBiZy1wcmltYXJ5IgogICAgICA6Y2xhc3M9Insnb25lLWFjdGlvbic6IWR1YWxBY3Rpb24sIFtidXR0b25TaXplXTp0cnVlLCAnZGlzYWJsZWQnOiBkaXNhYmxlQnV0dG9ufSIKICAgID4KICAgICAgPHYtZHJvcGRvd24KICAgICAgICBwbGFjZW1lbnQ9ImJvdHRvbSIKICAgICAgICA6Y29udGFpbmVyPSJmYWxzZSIKICAgICAgICA6ZGlzYWJsZWQ9ImRpc2FibGVCdXR0b24iCiAgICAgICAgOmZsaXA9ImZhbHNlIgogICAgICA+CiAgICAgICAgPHNsb3QKICAgICAgICAgIG5hbWU9ImJ1dHRvbi1jb250ZW50IgogICAgICAgICAgOmJ1dHRvblNpemU9ImJ1dHRvblNpemUiCiAgICAgICAgPgogICAgICAgICAgPGJ1dHRvbgogICAgICAgICAgICByZWY9InBvcG92ZXJCdXR0b24iCiAgICAgICAgICAgIGNsYXNzPSJpY29uLWNvbnRhaW5lciBiZy1wcmltYXJ5IG5vLWxlZnQtYm9yZGVyLXJhZGl1cyIKICAgICAgICAgICAgOmNsYXNzPSJidXR0b25TaXplIgogICAgICAgICAgICA6ZGlzYWJsZWQ9ImRpc2FibGVCdXR0b24iCiAgICAgICAgICAgIHR5cGU9ImJ1dHRvbiIKICAgICAgICAgID4KICAgICAgICAgICAgQnV0dG9uIDxpIGNsYXNzPSJpY29uIGljb24tY2hldnJvbi1kb3duIiAvPgogICAgICAgICAgPC9idXR0b24+CiAgICAgICAgPC9zbG90PgogICAgICAgIDx0ZW1wbGF0ZSAjcG9wcGVyPgogICAgICAgICAgPHNsb3QgbmFtZT0icG9wb3Zlci1jb250ZW50IiAvPgogICAgICAgIDwvdGVtcGxhdGU+CiAgICAgIDwvdi1kcm9wZG93bj4KICAgIDwvZGl2PgogIDwvZGl2Pgo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ActionDropdown.vue"], "names": [], "mappings": ";EA2DE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClF;MACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd;QACE,CAAC,CAAC,CAAC,CAAC;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ActionDropdown.vue", "sourceRoot": "", "sourcesContent": ["<script>\nexport default {\n  name: 'ActionDropdown',\n\n  props: {\n    size: {\n      type:    String,\n      default: '' // possible values are xs, sm, lg. empty is default .btn\n    },\n    // whether this is a button and dropdown (default) or dropdown that looks like a button/dropdown\n    dualAction: {\n      type:    Boolean,\n      default: true\n    },\n\n    disableButton: {\n      type:    Boolean,\n      default: false\n    }\n  },\n\n  computed: {\n    buttonSize() {\n      const { size } = this;\n      let out;\n\n      switch (size) {\n      case '':\n        out = 'btn';\n        break;\n      case 'xs':\n        out = 'btn btn-xs';\n        break;\n      case 'sm':\n        out = 'btn btn-sm';\n        break;\n      case 'lg':\n        out = 'btn btn-lg';\n        break;\n      default:\n      }\n\n      return out;\n    }\n  },\n\n  methods: {\n    hasSlot(name = 'default') {\n      return !!this.$slots[name] || !!this.$slots.name();\n    },\n\n    // allows parent components to programmatically open the dropdown\n    togglePopover() {\n      // this.$refs.popoverButton.click();\n    },\n  }\n};\n</script>\n<template>\n  <div class=\"dropdown-button-group\">\n    <div\n      class=\"dropdown-button bg-primary\"\n      :class=\"{'one-action':!dualAction, [buttonSize]:true, 'disabled': disableButton}\"\n    >\n      <v-dropdown\n        placement=\"bottom\"\n        :container=\"false\"\n        :disabled=\"disableButton\"\n        :flip=\"false\"\n      >\n        <slot\n          name=\"button-content\"\n          :buttonSize=\"buttonSize\"\n        >\n          <button\n            ref=\"popoverButton\"\n            class=\"icon-container bg-primary no-left-border-radius\"\n            :class=\"buttonSize\"\n            :disabled=\"disableButton\"\n            type=\"button\"\n          >\n            Button <i class=\"icon icon-chevron-down\" />\n          </button>\n        </slot>\n        <template #popper>\n          <slot name=\"popover-content\" />\n        </template>\n      </v-dropdown>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\">\n// load here instead of component so SSR render isn't all wonky\n.dropdown-button-group {\n  $xs-padding: 2px 3px;\n\n  .no-left-border-radius {\n    border-top-left-radius: 0px;\n    border-bottom-left-radius: 0px;\n  }\n\n  .no-right-border-radius {\n    border-top-right-radius: 0px;\n    border-bottom-right-radius: 0px;\n  }\n\n  .btn {\n    line-height: normal;\n    border: 0px;\n  }\n\n  .btn-xs,\n  .btn-group-xs > .btn,\n  .btn-xs .btn-label {\n      padding: $xs-padding;\n      font-size: 13px;\n  }\n\n  // this matches the top/bottom padding of the default button\n  $trigger-padding: 15px 10px 15px 10px;\n  $xs-trigger-padding: 2px 4px 4px 4px;\n  $sm-trigger-padding: 10px 10px 10px 10px;\n  $lg-trigger-padding: 18px 10px 10px 10px;\n\n  .v-popper {\n    .text-right {\n      margin-top: 5px;\n    }\n    .trigger {\n      height: 100%;\n      .icon-container {\n        height: 100%;\n        padding: 10px 10px 10px 10px;\n        i {\n          transform: scale(1);\n        }\n        &.btn-xs {\n          padding: $xs-trigger-padding;\n        }\n        &.btn-sm {\n          padding: $sm-trigger-padding;\n        }\n        &.btn-lg {\n          padding: $lg-trigger-padding;\n        }\n        &:focus {\n          outline-style: none;\n          box-shadow: none;\n          border-color: transparent;\n        }\n      }\n    }\n  }\n\n  .dropdown-button {\n    background: var(--tooltip-bg);\n    color: var(--link-text);\n    padding: 0;\n    display: inline-flex;\n\n    .wrapper-content {\n      button {\n        border-right: 0px;\n      }\n    }\n\n    &>*, .icon-chevron-down {\n      color: var(--primary);\n      background-color: rgba(0,0,0,0);\n    }\n\n    &.bg-primary:hover {\n      background: var(--accent-btn-hover);\n    }\n\n    &.one-action {\n      position: relative;\n      &>.btn {\n        padding: 15px 35px 15px 15px;\n      }\n      .v-popper{\n        .trigger{\n          position: absolute;\n          top: 0px;\n          right: 0px;\n          left: 0px;\n          bottom: 0px;\n          BUTTON {\n            position: absolute;\n            right: 0px;\n          }\n        }\n      }\n    }\n  }\n  .v-popper__popper {\n    border: none;\n  }\n  .v-popper__popper {\n    margin-top: 0px;\n\n    &[data-popper-placement^=\"bottom\"] {\n      .v-popper__arrow-container {\n        display: none;\n      }\n    }\n\n    .v-popper__inner {\n      color: var(--dropdown-text);\n      background-color: var(--dropdown-bg);\n      border: 1px solid var(--dropdown-border);\n      padding: 0px;\n      text-align: left;\n\n      LI {\n        padding: 10px;\n\n        &.divider {\n          padding-top: 0px;\n          padding-bottom: 0px;\n\n          > .divider-inner {\n            padding: 0;\n            border-bottom: 1px solid var(--dropdown-divider);\n            width: 125%;\n            margin: 0 auto;\n          }\n        }\n\n        &:not(.divider):hover {\n          background-color: var(--dropdown-hover-bg);\n          color: var(--dropdown-hover-text);\n          cursor: pointer;\n        }\n      }\n\n    }\n  }\n\n  //header\n  .user-info {\n    border-bottom: 1px solid var(--border);\n    display: block;\n  }\n}\n\n</style>\n"]}]}