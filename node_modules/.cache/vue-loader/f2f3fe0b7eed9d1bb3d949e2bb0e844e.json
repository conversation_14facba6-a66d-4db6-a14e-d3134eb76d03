{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/ResourcesSummary.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/ResourcesSummary.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCBjYXBpdGFsaXplIGZyb20gJ2xvZGFzaC9jYXBpdGFsaXplJzsKaW1wb3J0IENvdW50Qm94IGZyb20gJ0BzaGVsbC9jb21wb25lbnRzL0NvdW50Qm94JzsKaW1wb3J0IHsgU1RBVEVTIH0gZnJvbSAnQHNoZWxsL3BsdWdpbnMvZGFzaGJvYXJkLXN0b3JlL3Jlc291cmNlLWNsYXNzJzsKCmV4cG9ydCBkZWZhdWx0IHsKCiAgbmFtZTogJ1Jlc291cmNlc1N1bW1hcnknLAoKICBjb21wb25lbnRzOiB7IENvdW50Qm94IH0sCgogIHByb3BzOiB7CiAgICB2YWx1ZTogewogICAgICB0eXBlOiAgICAgT2JqZWN0LAogICAgICByZXF1aXJlZDogdHJ1ZSwKICAgIH0sCgogICAgc3RhdGVLZXk6IHsKICAgICAgdHlwZTogICAgU3RyaW5nLAogICAgICBkZWZhdWx0OiAnZmxlZXQuZmxlZXRTdW1tYXJ5LnN0YXRlJwogICAgfSwKCiAgICByZXF1aXJlZFN0YXRlczogewogICAgICB0eXBlOiAgICBBcnJheSwKICAgICAgZGVmYXVsdDogKCkgPT4gW10KICAgIH0KICB9LAoKICBjb21wdXRlZDogewogICAgY291bnRzKCkgewogICAgICBjb25zdCBvdXQgPSB0aGlzLnJlcXVpcmVkU3RhdGVzLnJlZHVjZSgob2JqLCBzdGF0ZSkgPT4gewogICAgICAgIG9ialtzdGF0ZV0gPSB7CiAgICAgICAgICBjb3VudDogMCwKICAgICAgICAgIGNvbG9yOiBzdGF0ZSwKICAgICAgICAgIGxhYmVsOiB0aGlzLiRzdG9yZS5nZXR0ZXJzWydpMThuL3dpdGhGYWxsYmFjayddKGAkeyB0aGlzLnN0YXRlS2V5IH0uJHsgc3RhdGUgfWAsIG51bGwsIHN0YXRlKQogICAgICAgIH07CgogICAgICAgIHJldHVybiBvYmo7CiAgICAgIH0sIHt9KTsKCiAgICAgIGZvciAoY29uc3QgayBpbiB0aGlzLnZhbHVlKSB7CiAgICAgICAgaWYgKGsuc3RhcnRzV2l0aCgnZGVzaXJlZCcpKSB7CiAgICAgICAgICBjb250aW51ZTsKICAgICAgICB9CgogICAgICAgIGNvbnN0IG1hcHBlZCA9IFNUQVRFU1trXSB8fCBTVEFURVNbJ290aGVyJ107CgogICAgICAgIGlmIChvdXRba10pIHsKICAgICAgICAgIG91dFtrXS5jb3VudCArPSB0aGlzLnZhbHVlW2tdIHx8IDA7CiAgICAgICAgICBvdXRba10uY29sb3IgPSBtYXBwZWQuY29sb3I7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIG91dFtrXSA9IHsKICAgICAgICAgICAgY291bnQ6IHRoaXMudmFsdWVba10gfHwgMCwKICAgICAgICAgICAgY29sb3I6IG1hcHBlZC5jb2xvciwKICAgICAgICAgICAgbGFiZWw6IHRoaXMuJHN0b3JlLmdldHRlcnNbJ2kxOG4vd2l0aEZhbGxiYWNrJ10oYCR7IHRoaXMuc3RhdGVLZXkgfS4keyBrIH1gLCBudWxsLCBjYXBpdGFsaXplKGspKQogICAgICAgICAgfTsKICAgICAgICB9CiAgICAgIH0KCiAgICAgIHJldHVybiBvdXQ7CiAgICB9LAogIH0sCgogIG1ldGhvZHM6IHsgY2FwaXRhbGl6ZSB9LAp9Owo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/ResourcesSummary.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEtE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;EAEb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAExB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;EAExB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9F,CAAC;;QAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACZ,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEN,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV;;QAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE3C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClG,CAAC;QACH;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACZ,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACzB,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/ResourcesSummary.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport capitalize from 'lodash/capitalize';\nimport CountBox from '@shell/components/CountBox';\nimport { STATES } from '@shell/plugins/dashboard-store/resource-class';\n\nexport default {\n\n  name: 'ResourcesSummary',\n\n  components: { CountBox },\n\n  props: {\n    value: {\n      type:     Object,\n      required: true,\n    },\n\n    stateKey: {\n      type:    String,\n      default: 'fleet.fleetSummary.state'\n    },\n\n    requiredStates: {\n      type:    Array,\n      default: () => []\n    }\n  },\n\n  computed: {\n    counts() {\n      const out = this.requiredStates.reduce((obj, state) => {\n        obj[state] = {\n          count: 0,\n          color: state,\n          label: this.$store.getters['i18n/withFallback'](`${ this.stateKey }.${ state }`, null, state)\n        };\n\n        return obj;\n      }, {});\n\n      for (const k in this.value) {\n        if (k.startsWith('desired')) {\n          continue;\n        }\n\n        const mapped = STATES[k] || STATES['other'];\n\n        if (out[k]) {\n          out[k].count += this.value[k] || 0;\n          out[k].color = mapped.color;\n        } else {\n          out[k] = {\n            count: this.value[k] || 0,\n            color: mapped.color,\n            label: this.$store.getters['i18n/withFallback'](`${ this.stateKey }.${ k }`, null, capitalize(k))\n          };\n        }\n      }\n\n      return out;\n    },\n  },\n\n  methods: { capitalize },\n};\n</script>\n\n<template>\n  <div class=\"row flexwrap\">\n    <div\n      v-for=\"(v, k) in counts\"\n      :key=\"k\"\n      class=\"col countbox\"\n    >\n      <CountBox\n        :compact=\"true\"\n        :count=\"v['count']\"\n        :name=\"v.label\"\n        :primary-color-var=\"'--sizzle-' + v.color\"\n      />\n    </div>\n  </div>\n</template>\n<style lang=\"scss\" scoped>\n  .flexwrap {\n    flex-wrap: wrap;\n  }\n  .countbox {\n    min-width: 150px;\n    width: 12.5%;\n    margin-bottom: 10px;\n  }\n</style>\n"]}]}