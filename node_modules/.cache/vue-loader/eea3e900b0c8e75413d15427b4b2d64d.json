{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/MatchExpressions.vue?vue&type=style&index=0&id=36b2320e&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/MatchExpressions.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgJHNlcGFyYXRvcjogMjA7CiAgJHJlbW92ZTogNzU7CiAgJHNwYWNpbmc6IDEwcHg7CgogIC5vcGVyYXRvciB7CiAgICAmIC52c19fZHJvcGRvd24tb3B0aW9uewogICAgICBwYWRkaW5nOiAzcHggNnB4IDNweCA2cHggIWltcG9ydGFudAogICAgfQogIH0KCiAgLnJlbW92ZS1leHByZXNzaW9uIHsKICAgIHBhZGRpbmc6ICA4cHg7CiAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICBtYXJnaW4tYm90dG9tOjEwcHg7CiAgICByaWdodDogMHB4OwogICAgdG9wOiAwcHg7CiAgICB6LWluZGV4OiB6LWluZGV4KCdvdmVyQ29udGVudCcpOwoKICAgIGkgewogICAgICBmb250LXNpemU6MmVtOwogICAgfQogIH0KCiAgLnJlbW92ZS1jb250YWluZXIgewogICAgZGlzcGxheTogZmxleDsKICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOwogIH0KCiAgLm1hdGNoLWV4cHJlc3Npb24tcm93LCAubWF0Y2gtZXhwcmVzc2lvbi1oZWFkZXIgewogICAgZGlzcGxheTogZ3JpZDsKICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyIDFmciAxZnI7CiAgICBtYXJnaW46IDVweCAwOwogICAgZ3JpZC1nYXA6ICRjb2x1bW4tZ3V0dGVyOwoKICAgICYgPiBMQUJFTCB7CiAgICAgIG1hcmdpbjogMDsKICAgIH0KCiAgICAmOm5vdCgudmlldyl7CiAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDMsIDFmcikgNTBweDsKICAgIH0KICB9CgogIC5tYXRjaC1leHByZXNzaW9uLXJvdyA+IGRpdiA+IGlucHV0IHsKICAgIG1pbi1oZWlnaHQ6IDQwcHggIWltcG9ydGFudDsKICB9CiAgLm1hdGNoLWV4cHJlc3Npb24tcm93LW1hdGNoaW5nLCAubWF0Y2gtZXhwcmVzc2lvbi1oZWFkZXItbWF0Y2hpbmcgewogICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnIgMWZyIDFmciAxZnI7CgogICAgJjpub3QoLnZpZXcpewogICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmciAxZnIgMWZyIDFmciAxMDBweDsKICAgIH0KICB9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/MatchExpressions.vue"], "names": [], "mappings": ";EA4YE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE/B,EAAE;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAExB,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACX;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC5C;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;IAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9C;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/MatchExpressions.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { NODE, POD } from '@shell/config/types';\nimport Select from '@shell/components/form/Select';\nimport { mapGetters } from 'vuex';\nimport { isArray, removeObject } from '@shell/utils/array';\nimport { clone } from '@shell/utils/object';\nimport { convert, simplify } from '@shell/utils/selector';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\n\nexport default {\n  emits: ['update:value', 'remove'],\n\n  components: { Select, LabeledSelect },\n  props:      {\n    // Array of actual match expressions\n    // or k8s selector Object of {matchExpressions, matchLabels}\n    value: {\n      type:    [Array, Object],\n      default: () => []\n    },\n\n    // CRU mode\n    mode: {\n      type:    String,\n      default: 'edit'\n    },\n\n    // pod/node affinity types have different operator options\n    type: {\n      type:    String,\n      default: NODE\n    },\n\n    // has select for matching fields or expressions (used for node affinity)\n    // https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.25/#nodeselectorterm-v1-core\n    matchingSelectorDisplay: {\n      type:    <PERSON><PERSON>an,\n      default: false,\n    },\n\n    // whether or not to show an initial empty row of inputs when value is empty in editing modes\n    initialEmptyRow: {\n      type:    <PERSON>olean,\n      default: false,\n    },\n\n    // whether or not to show add rule button at bottom\n    showAddButton: {\n      type:    Boolean,\n      default: true\n    },\n\n    // whether or not to show remove rule button right side of the rule\n    showRemoveButton: {\n      type:    Boolean,\n      default: true\n    },\n\n    // whether or not to show remove button in upper right\n    showRemove: {\n      type:    Boolean,\n      default: true\n    },\n\n    // if options are passed for keys, then the key's input will become a select\n    keysSelectOptions: {\n      type:    Array,\n      default: () => []\n    }\n  },\n\n  data() {\n    const t = this.$store.getters['i18n/t'];\n\n    const podOptions = [\n      { label: t('workload.scheduling.affinity.matchExpressions.in'), value: 'In' },\n      { label: t('workload.scheduling.affinity.matchExpressions.notIn'), value: 'NotIn' },\n      { label: t('workload.scheduling.affinity.matchExpressions.exists'), value: 'Exists' },\n      { label: t('workload.scheduling.affinity.matchExpressions.doesNotExist'), value: 'DoesNotExist' },\n    ];\n\n    const nodeOptions = [\n      { label: t('workload.scheduling.affinity.matchExpressions.in'), value: 'In' },\n      { label: t('workload.scheduling.affinity.matchExpressions.notIn'), value: 'NotIn' },\n      { label: t('workload.scheduling.affinity.matchExpressions.exists'), value: 'Exists' },\n      { label: t('workload.scheduling.affinity.matchExpressions.doesNotExist'), value: 'DoesNotExist' },\n      { label: t('workload.scheduling.affinity.matchExpressions.lessThan'), value: 'Lt' },\n      { label: t('workload.scheduling.affinity.matchExpressions.greaterThan'), value: 'Gt' },\n    ];\n\n    const ops = this.type === NODE ? nodeOptions : podOptions;\n\n    let rules;\n\n    // special case for matchFields and matchExpressions\n    // https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.25/#nodeselectorterm-v1-core\n    if ( this.matchingSelectorDisplay) {\n      const rulesByType = {\n        matchFields:      [],\n        matchExpressions: []\n      };\n\n      ['matchFields', 'matchExpressions'].forEach((type) => {\n        rulesByType[type] = this.parseRules(this.value[type], type);\n      });\n\n      rules = [...rulesByType.matchFields, ...rulesByType.matchExpressions];\n    } else if ( isArray(this.value) ) {\n      rules = [...this.value];\n      rules = this.parseRules(rules);\n    } else {\n      rules = convert(this.value.matchLabels, this.value.matchExpressions);\n      rules = this.parseRules(rules);\n    }\n\n    if (!rules.length && this.initialEmptyRow && !this.isView) {\n      const newRule = {\n        key:      '',\n        operator: 'In',\n        values:   ''\n      };\n\n      if (this.matchingSelectorDisplay) {\n        newRule.matching = 'matchExpressions';\n      }\n\n      rules.push(newRule);\n    }\n\n    return {\n      ops,\n      rules,\n      custom: []\n    };\n  },\n\n  computed: {\n    isView() {\n      return this.mode === 'view';\n    },\n\n    node() {\n      return NODE;\n    },\n\n    pod() {\n      return POD;\n    },\n\n    hasKeySelectOptions() {\n      return !!this.keysSelectOptions?.length;\n    },\n\n    matchingSelectOptions() {\n      return [\n        {\n          label: this.t('workload.scheduling.affinity.matchExpressions.label'),\n          value: 'matchExpressions',\n        },\n        {\n          label: this.t('workload.scheduling.affinity.matchFields.label'),\n          value: 'matchFields',\n        },\n      ];\n    },\n\n    ...mapGetters({ t: 'i18n/t' })\n  },\n\n  methods: {\n    parseRules(rules, matching) {\n      if (rules?.length) {\n        return rules.map((rule) => {\n          const newRule = clone(rule);\n\n          if (newRule.values && typeof newRule.values !== 'string') {\n            newRule.values = newRule.values.join(', ');\n          }\n\n          if (matching) {\n            newRule.matching = matching;\n          }\n\n          return newRule;\n        });\n      }\n\n      return [];\n    },\n\n    removeRule(row) {\n      removeObject(this.rules, row);\n      this.update();\n    },\n\n    addRule() {\n      const newRule = {\n        key:      '',\n        operator: 'In',\n        values:   ''\n      };\n\n      if (this.matchingSelectorDisplay) {\n        newRule.matching = 'matchExpressions';\n      }\n\n      this.rules.push(newRule);\n    },\n\n    update() {\n      this.$nextTick(() => {\n        const out = this.rules.map((rule) => {\n          const expression = { key: rule.key, operator: rule.operator };\n\n          if (this.matchingSelectorDisplay) {\n            expression.matching = rule.matching;\n          }\n\n          let val = (rule.values || '').trim();\n\n          if ( rule.operator === 'Exists' || rule.operator === 'DoesNotExist') {\n            val = null;\n          }\n\n          if ( val !== null ) {\n            expression.values = val.split(/\\s*,\\s*/);\n          }\n\n          return expression;\n        }).filter((x) => !!x);\n\n        if ( isArray(this.value) || this.matchingSelectorDisplay ) {\n          this.$emit('update:value', out);\n        } else {\n          this.$emit('update:value', simplify(out));\n        }\n      });\n    }\n  }\n};\n</script>\n\n<template>\n  <div>\n    <slot\n      v-if=\"rules.length\"\n      name=\"header\"\n    />\n    <button\n      v-if=\"showRemove && !isView\"\n      type=\"button\"\n      class=\"btn role-link remove-expression\"\n      @click=\"$emit('remove')\"\n    >\n      <i class=\"icon icon-x\" />\n    </button>\n\n    <div\n      v-if=\"rules.length\"\n      class=\"match-expression-header\"\n      :class=\"{ 'view':isView, 'match-expression-header-matching': matchingSelectorDisplay }\"\n    >\n      <label v-if=\"matchingSelectorDisplay\">\n        {{ t('workload.scheduling.affinity.matchExpressions.matchType') }}\n      </label>\n      <label>\n        {{ t('workload.scheduling.affinity.matchExpressions.key') }}\n      </label>\n      <label>\n        {{ t('workload.scheduling.affinity.matchExpressions.operator') }}\n      </label>\n      <label>\n        {{ t('workload.scheduling.affinity.matchExpressions.value') }}\n      </label>\n      <span />\n    </div>\n    <div\n      v-for=\"(row, index) in rules\"\n      :key=\"index\"\n      class=\"match-expression-row\"\n      :class=\"{'view':isView, 'mb-10': index !== rules.length - 1, 'match-expression-row-matching': matchingSelectorDisplay}\"\n    >\n      <!-- Select for matchFields and matchExpressions -->\n      <div\n        v-if=\"matchingSelectorDisplay\"\n        :data-testid=\"`input-match-type-field-${index}`\"\n      >\n        <div v-if=\"isView\">\n          {{ row.matching }}\n        </div>\n        <LabeledSelect\n          v-else\n          v-model:value=\"row.matching\"\n          :mode=\"mode\"\n          :options=\"matchingSelectOptions\"\n          :data-testid=\"`input-match-type-field-control-${index}`\"\n          @selecting=\"update\"\n        />\n      </div>\n      <div\n        :data-testid=\"`input-match-expression-key-${index}`\"\n      >\n        <div v-if=\"isView\">\n          {{ row.key }}\n        </div>\n        <input\n          v-else-if=\"!hasKeySelectOptions\"\n          v-model=\"row.key\"\n          :mode=\"mode\"\n          :data-testid=\"`input-match-expression-key-control-${index}`\"\n          @input=\"update\"\n        >\n        <LabeledSelect\n          v-else\n          v-model:value=\"row.key\"\n          :mode=\"mode\"\n          :options=\"keysSelectOptions\"\n          :data-testid=\"`input-match-expression-key-control-select-${index}`\"\n        />\n      </div>\n      <div\n        :data-testid=\"`input-match-expression-operator-${index}`\"\n      >\n        <div v-if=\"isView\">\n          {{ row.operator }}\n        </div>\n        <Select\n          v-else\n          v-model:value=\"row.operator\"\n          class=\"operator single\"\n          :options=\"ops\"\n          :clearable=\"false\"\n          :reduce=\"opt=>opt.value\"\n          :mode=\"mode\"\n          :data-testid=\"`input-match-expression-operator-control-${index}`\"\n          @update:value=\"update\"\n        />\n      </div>\n\n      <div\n        v-if=\"row.operator==='Exists' || row.operator==='DoesNotExist'\"\n        class=\"no-value\"\n      >\n        <label class=\"text-muted\">&hellip;</label>\n      </div>\n      <div\n        v-else\n        :data-testid=\"`input-match-expression-values-${index}`\"\n      >\n        <div v-if=\"isView\">\n          {{ row.values }}\n        </div>\n        <input\n          v-else\n          v-model=\"row.values\"\n          :mode=\"mode\"\n          :disabled=\"row.operator==='Exists' || row.operator==='DoesNotExist'\"\n          :data-testid=\"`input-match-expression-values-control-${index}`\"\n          @input=\"update\"\n        >\n      </div>\n      <div\n        v-if=\"showRemoveButton\"\n        class=\"remove-container\"\n      >\n        <button\n          v-if=\"!isView\"\n          type=\"button\"\n          class=\"btn role-link\"\n          :style=\"{padding:'0px'}\"\n\n          :disabled=\"mode==='view'\"\n          :data-testid=\"`input-match-expression-remove-control-${index}`\"\n          @click=\"removeRule(row)\"\n        >\n          <t k=\"generic.remove\" />\n        </button>\n      </div>\n    </div>\n    <div\n      v-if=\"!isView && showAddButton\"\n      class=\"mt-20\"\n    >\n      <button\n        type=\"button\"\n        class=\"btn role-tertiary add\"\n        :data-testid=\"`input-match-expression-add-rule`\"\n        @click=\"addRule\"\n      >\n        <t k=\"workload.scheduling.affinity.matchExpressions.addRule\" />\n      </button>\n    </div>\n  </div>\n</template>\n\n<style lang='scss' scoped>\n  $separator: 20;\n  $remove: 75;\n  $spacing: 10px;\n\n  .operator {\n    & .vs__dropdown-option{\n      padding: 3px 6px 3px 6px !important\n    }\n  }\n\n  .remove-expression {\n    padding:  8px;\n    position: absolute;\n    margin-bottom:10px;\n    right: 0px;\n    top: 0px;\n    z-index: z-index('overContent');\n\n    i {\n      font-size:2em;\n    }\n  }\n\n  .remove-container {\n    display: flex;\n    justify-content: center;\n  }\n\n  .match-expression-row, .match-expression-header {\n    display: grid;\n    grid-template-columns: 1fr 1fr 1fr;\n    margin: 5px 0;\n    grid-gap: $column-gutter;\n\n    & > LABEL {\n      margin: 0;\n    }\n\n    &:not(.view){\n      grid-template-columns: repeat(3, 1fr) 50px;\n    }\n  }\n\n  .match-expression-row > div > input {\n    min-height: 40px !important;\n  }\n  .match-expression-row-matching, .match-expression-header-matching {\n    grid-template-columns: 1fr 1fr 1fr 1fr;\n\n    &:not(.view){\n      grid-template-columns: 1fr 1fr 1fr 1fr 100px;\n    }\n  }\n</style>\n"]}]}