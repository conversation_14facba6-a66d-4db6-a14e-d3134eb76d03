{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayListSelect.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayListSelect.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayListSelect.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAElD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACjC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC3E,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACjE,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/G;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE/D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/C;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B;EACF;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayListSelect.vue", "sourceRoot": "", "sourcesContent": ["<script>\r\nimport ArrayList from '@shell/components/form/ArrayList';\r\nimport Select from '@shell/components/form/Select';\r\n\r\nexport default {\r\n  emits: ['update:value'],\r\n\r\n  components: { ArrayList, Select },\r\n  props:      {\r\n    value: {\r\n      type:     Array,\r\n      required: true\r\n    },\r\n    options: {\r\n      default: null,\r\n      type:    Array\r\n    },\r\n    selectProps: {\r\n      type:    Object,\r\n      default: null,\r\n    },\r\n    arrayListProps: {\r\n      type:    Object,\r\n      default: null\r\n    },\r\n    enableDefaultAddValue: {\r\n      type:    Boolean,\r\n      default: true\r\n    },\r\n    loading: {\r\n      type:    Boolean,\r\n      default: false\r\n    },\r\n    disabled: {\r\n      type:    Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  computed: {\r\n    filteredOptions() {\r\n      return this.options\r\n        .filter((option) => !this.value.includes(option.value));\r\n    },\r\n\r\n    addAllowed() {\r\n      return this.arrayListProps?.addAllowed || this.filteredOptions.length > 0;\r\n    },\r\n\r\n    defaultAddValue() {\r\n      return this.enableDefaultAddValue ? this.options[0]?.value : '';\r\n    },\r\n\r\n    getOptionLabel() {\r\n      return this.selectProps?.getOptionLabel ? (opt) => (this.selectProps?.getOptionLabel(opt) || opt) : undefined;\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    updateRow(index, value) {\r\n      this.value.splice(index, 1, value);\r\n      this.$emit('update:value', this.value);\r\n    },\r\n    calculateOptions(value) {\r\n      const valueOption = this.options.find((o) => o.value === value);\r\n\r\n      if (valueOption) {\r\n        return [valueOption, ...this.filteredOptions];\r\n      }\r\n\r\n      return this.filteredOptions;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<template>\r\n  <ArrayList\r\n    v-bind=\"arrayListProps\"\r\n    :value=\"value\"\r\n    class=\"array-list-select\"\r\n    :add-allowed=\"addAllowed || loading\"\r\n    :loading=\"loading\"\r\n    :defaultAddValue=\"defaultAddValue\"\r\n    :disabled=\"disabled\"\r\n    @update:value=\"$emit('update:value', $event)\"\r\n  >\r\n    <template v-slot:columns=\"scope\">\r\n      <Select\r\n        :value=\"scope.row.value\"\r\n        v-bind=\"selectProps\"\r\n        :options=\"calculateOptions(scope.row.value)\"\r\n        :get-option-label=\"getOptionLabel\"\r\n        @update:value=\"updateRow(scope.i, $event)\"\r\n      />\r\n    </template>\r\n  </ArrayList>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n:deep() .unlabeled-select {\r\n  height: 61px;\r\n  }\r\n</style>\r\n"]}]}