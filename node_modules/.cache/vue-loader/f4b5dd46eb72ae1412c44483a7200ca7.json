{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CountGauge.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CountGauge.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCBHcmFwaENpcmNsZSBmcm9tICdAc2hlbGwvY29tcG9uZW50cy9ncmFwaC9DaXJjbGUnOwppbXBvcnQgR3JhZGllbnRCb3ggZnJvbSAnQHNoZWxsL2NvbXBvbmVudHMvR3JhZGllbnRCb3gnOwoKZXhwb3J0IGRlZmF1bHQgewogIGNvbXBvbmVudHM6IHsgR3JhZGllbnRCb3gsIEdyYXBoQ2lyY2xlIH0sCiAgcHJvcHM6ICAgICAgewogICAgbmFtZTogewogICAgICB0eXBlOiAgICAgU3RyaW5nLAogICAgICByZXF1aXJlZDogdHJ1ZQogICAgfSwKICAgIHRvdGFsOiB7CiAgICAgIHR5cGU6ICAgICBOdW1iZXIsCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9LAogICAgdXNlZnVsOiB7CiAgICAgIHR5cGU6ICAgICBOdW1iZXIsCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9LAogICAgcHJpbWFyeUNvbG9yVmFyOiB7CiAgICAgIHR5cGU6ICAgIFN0cmluZywKICAgICAgZGVmYXVsdDogbnVsbCwKICAgIH0sCiAgICB3YXJuaW5nQ291bnQ6IHsKICAgICAgdHlwZTogICAgTnVtYmVyLAogICAgICBkZWZhdWx0OiAwCiAgICB9LAogICAgZXJyb3JDb3VudDogewogICAgICB0eXBlOiAgICBOdW1iZXIsCiAgICAgIGRlZmF1bHQ6IDAKICAgIH0sCiAgICBsb2NhdGlvbjogewogICAgICB0eXBlOiAgICBPYmplY3QsCiAgICAgIGRlZmF1bHQ6IG51bGwKICAgIH0sCiAgICBwbGFpbjogewogICAgICB0eXBlOiAgICBCb29sZWFuLAogICAgICBkZWZhdWx0OiBmYWxzZQogICAgfSwKICAgIGdyYXBoaWNhbDogewogICAgICB0eXBlOiAgICBCb29sZWFuLAogICAgICBkZWZhdWx0OiB0cnVlCiAgICB9CiAgfSwKCiAgY29tcHV0ZWQ6IHsKICAgIHBlcmNlbnRhZ2UoKSB7CiAgICAgIGlmICh0aGlzLnRvdGFsID09PSAwKSB7CiAgICAgICAgcmV0dXJuIDA7CiAgICAgIH0KCiAgICAgIHJldHVybiB0aGlzLnVzZWZ1bCAvIHRoaXMudG90YWw7CiAgICB9LAogICAgY2xpY2thYmxlKCkgewogICAgICByZXR1cm4gISF0aGlzLmxvY2F0aW9uOwogICAgfSwKICAgIHNob3dBbGVydHMoKSB7CiAgICAgIGNvbnN0IHRvdGFsID0gdGhpcy53YXJuaW5nQ291bnQgKyB0aGlzLmVycm9yQ291bnQ7CgogICAgICByZXR1cm4gdG90YWwgPiAwOwogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgdmlzaXRMb2NhdGlvbigpIHsKICAgICAgaWYgKCF0aGlzLmNsaWNrYWJsZSkgewogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgdGhpcy4kcm91dGVyLnB1c2godGhpcy5sb2NhdGlvbik7CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CountGauge.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEvD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACxC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;IACV,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACV;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEjD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAClB;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC;EACF;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CountGauge.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport GraphCircle from '@shell/components/graph/Circle';\nimport GradientBox from '@shell/components/GradientBox';\n\nexport default {\n  components: { GradientBox, GraphCircle },\n  props:      {\n    name: {\n      type:     String,\n      required: true\n    },\n    total: {\n      type:     Number,\n      required: true\n    },\n    useful: {\n      type:     Number,\n      required: true\n    },\n    primaryColorVar: {\n      type:    String,\n      default: null,\n    },\n    warningCount: {\n      type:    Number,\n      default: 0\n    },\n    errorCount: {\n      type:    Number,\n      default: 0\n    },\n    location: {\n      type:    Object,\n      default: null\n    },\n    plain: {\n      type:    Boolean,\n      default: false\n    },\n    graphical: {\n      type:    Boolean,\n      default: true\n    }\n  },\n\n  computed: {\n    percentage() {\n      if (this.total === 0) {\n        return 0;\n      }\n\n      return this.useful / this.total;\n    },\n    clickable() {\n      return !!this.location;\n    },\n    showAlerts() {\n      const total = this.warningCount + this.errorCount;\n\n      return total > 0;\n    }\n  },\n  methods: {\n    visitLocation() {\n      if (!this.clickable) {\n        return;\n      }\n\n      this.$router.push(this.location);\n    }\n  }\n};\n</script>\n\n<template>\n  <GradientBox\n    class=\"count-gauge\"\n    :class=\"{clickable}\"\n    :primary-color-var=\"primaryColorVar\"\n    :plain=\"plain\"\n    @click=\"visitLocation()\"\n  >\n    <div\n      v-if=\"graphical\"\n      class=\"graphical\"\n    >\n      <GraphCircle\n        v-if=\"percentage > 0\"\n        :primary-stroke-color=\"`rgba(var(${primaryColorVar}))`\"\n        secondary-stroke-color=\"rgb(var(--resource-gauge-back-circle))\"\n        :percentage=\"percentage\"\n      />\n      <GraphCircle\n        v-if=\"percentage === 0\"\n        :primary-stroke-color=\"`rgba(var(${primaryColorVar}))`\"\n        secondary-stroke-color=\"rgb(var(--resource-gauge-back-circle))\"\n        class=\"zero\"\n        :percentage=\"100\"\n      />\n    </div>\n    <div class=\"data\">\n      <h1>{{ useful }}</h1>\n      <label>{{ name }}</label>\n      <div\n        v-if=\"showAlerts\"\n        class=\"alerts\"\n      >\n        <span class=\"text-warning\">\n          <i class=\"icon icon-warning\" /><span class=\"count\">{{ warningCount }}</span>\n        </span>\n        <span class=\"text-error\">\n          <i class=\"icon icon-error\" /><span class=\"count\">{{ errorCount }}</span>\n        </span>\n      </div>\n    </div>\n  </GradientBox>\n</template>\n\n<style lang=\"scss\">\n    .zero {\n      circle {\n        stroke: var(--gauge-zero);\n      }\n    }\n    .count-gauge {\n        $padding: 10px;\n\n        padding: $padding;\n        position: relative;\n        display: flex;\n        flex-direction: row;\n        align-items: center;\n\n        &.clickable {\n          cursor: pointer;\n        }\n\n        .data {\n          display: flex;\n          flex-direction: column;\n          flex: 1;\n\n          label {\n            opacity: 0.7;\n          }\n        }\n\n        .graphical {\n          $size: 40px;\n          width: $size;\n          height: $size;\n          margin-right: $padding;\n        }\n\n        h1 {\n          font-size: 40px;\n          line-height: 36px;\n          padding-bottom: math.div($padding, 2);\n          margin-bottom: 0;\n        }\n\n        @media only screen and (min-width: map-get($breakpoints, '--viewport-7')) {\n          h1 {\n            font-size: 40px;\n            line-height: 36px;\n          }\n        }\n\n        .alerts {\n            position: absolute;\n            right: $padding;\n            top: math.div($padding, 2);\n            font-size: 15px;\n\n            .text-error {\n              margin-left: 5px;\n            }\n        }\n    }\n</style>\n"]}]}