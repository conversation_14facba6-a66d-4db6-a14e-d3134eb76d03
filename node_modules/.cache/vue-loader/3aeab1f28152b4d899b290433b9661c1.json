{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/CatalogList/index.vue?vue&type=style&index=0&id=6793ae20&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/CatalogList/index.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5zdWItcm93IHsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgdmFyKC0tc29ydGFibGUtdGFibGUtdG9wLWRpdmlkZXIpOwogIHBhZGRpbmctbGVmdDogMXJlbTsKICBwYWRkaW5nLXJpZ2h0OiAxcmVtOwp9CgouY29sIHsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CgogIHNlY3Rpb24gewogICAgbWFyZ2luLWJvdHRvbTogMS41cmVtOwogIH0KCiAgLnRpdGxlIHsKICAgIG1hcmdpbi1ib3R0b206IDAuNXJlbTsKICB9Cn0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/CatalogList/index.vue"], "names": [], "mappings": ";AAmHA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACrB;;AAEA,CAAC,CAAC,CAAC,EAAE;EACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/CatalogList/index.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapGetters } from 'vuex';\nimport isEmpty from 'lodash/isEmpty';\n\nimport ResourceManager from '@shell/mixins/resource-manager';\nimport { SERVICE, WORKLOAD_TYPES } from '@shell/config/types';\nimport { UI_PLUGIN_LABELS, UI_PLUGIN_NAMESPACE } from '@shell/config/uiplugins';\nimport { UI_PLUGIN_CATALOG } from '@shell/config/table-headers';\n\nimport ResourceTable from '@shell/components/ResourceTable';\n\nexport default {\n  emits: ['showCatalogUninstallDialog', 'showCatalogLoadDialog'],\n\n  name: 'CatalogList',\n\n  components: { ResourceTable },\n\n  mixins: [ResourceManager],\n\n  data() {\n    const actions = [\n      {\n        action:  'showCatalogUninstallDialog',\n        label:   this.t('plugins.uninstall.label'),\n        icon:    'icon icon-trash',\n        enabled: true,\n      }\n    ];\n\n    return {\n      actions,\n      catalogHeaders: UI_PLUGIN_CATALOG,\n    };\n  },\n\n  computed: {\n    ...mapGetters({ allRepos: 'catalog/repos' }),\n\n    namespacedDeployments() {\n      return this.$store.getters['management/all'](WORKLOAD_TYPES.DEPLOYMENT).filter((dep) => dep.metadata.namespace === UI_PLUGIN_NAMESPACE);\n    },\n\n    namespacedServices() {\n      return this.$store.getters['management/all'](SERVICE).filter((svc) => svc.metadata.namespace === UI_PLUGIN_NAMESPACE);\n    },\n\n    catalogRows() {\n      const rows = [];\n      const actions = this.actions;\n\n      if ( !isEmpty(this.namespacedDeployments) ) {\n        this.namespacedDeployments.forEach((deploy) => {\n          const resources = [this.namespacedServices, this.allRepos];\n          const deployName = deploy.metadata?.labels?.[UI_PLUGIN_LABELS.CATALOG_IMAGE];\n\n          if ( deployName ) {\n            const out = {\n              name:                       deployName,\n              state:                      deploy.metadata?.state?.name,\n              image:                      deploy.spec?.template?.spec?.containers[0]?.image,\n              service:                    null,\n              repo:                       null,\n              availableActions:           actions,\n              showCatalogUninstallDialog: () => this.$emit('showCatalogUninstallDialog', out)\n            };\n            const keys = ['service', 'repo'];\n\n            resources.forEach((resource, i) => {\n              out[keys[i]] = resource?.filter((item) => item.metadata?.labels?.[UI_PLUGIN_LABELS.CATALOG_IMAGE] === deployName)[0];\n            });\n\n            rows.push(out);\n          }\n        });\n      }\n\n      return rows;\n    }\n  },\n};\n</script>\n\n<template>\n  <div class=\"row mt-20\">\n    <div class=\"col span-12\">\n      <ResourceTable\n        :headers=\"catalogHeaders\"\n        :rows=\"catalogRows\"\n        :paging=\"true\"\n        :rows-per-page=\"10\"\n        :table-actions=\"false\"\n        key-field=\"name\"\n      >\n        <template #header-left>\n          <div>\n            <button\n              class=\"btn role-primary mr-10\"\n              type=\"button\"\n              aria-haspopup=\"dialog\"\n              data-testid=\"extensions-catalog-load-dialog\"\n              role=\"button\"\n              :aria-label=\"t('plugins.manageCatalog.imageLoad.load')\"\n              @click=\"$emit('showCatalogLoadDialog')\"\n            >\n              {{ t('plugins.manageCatalog.imageLoad.load') }}\n            </button>\n          </div>\n        </template>\n      </ResourceTable>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.sub-row {\n  border-bottom: 1px solid var(--sortable-table-top-divider);\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\n\n.col {\n  display: flex;\n  flex-direction: column;\n\n  section {\n    margin-bottom: 1.5rem;\n  }\n\n  .title {\n    margin-bottom: 0.5rem;\n  }\n}\n</style>\n"]}]}