{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/Project.vue?vue&type=style&index=0&id=0752acec&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/Project.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouaGVhZGVycyB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBmbGV4LWRpcmVjdGlvbjogcm93Ow0KICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtZXZlbmx5Ow0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkIHZhcigtLWJvcmRlcik7DQogICAgaGVpZ2h0OiAzMHB4Ow0KICAgIHdpZHRoOiBjYWxjKDEwMCUgLSA3NXB4KTsNCg0KICAgIGRpdiB7DQogICAgICAgIHdpZHRoOiAxMDAlOw0KICAgIH0NCn0NCg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/Project.vue"], "names": [], "mappings": ";AAyFA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;IAExB,CAAC,CAAC,EAAE;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;AACJ", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/Project.vue", "sourceRoot": "", "sourcesContent": ["<script>\r\nimport ArrayList from '@shell/components/form/ArrayList';\r\nimport Row from './ProjectRow';\r\nimport { QUOTA_COMPUTED } from './shared';\r\n\r\nexport default {\r\n  emits: ['remove', 'input'],\r\n\r\n  components: { ArrayList, Row },\r\n\r\n  props: {\r\n    mode: {\r\n      type:     String,\r\n      required: true,\r\n    },\r\n    value: {\r\n      type:    Object,\r\n      default: () => {\r\n        return {};\r\n      }\r\n    },\r\n    types: {\r\n      type:    Array,\r\n      default: () => {\r\n        return [];\r\n      }\r\n    }\r\n  },\r\n\r\n  data() {\r\n    this.value['spec'] = this.value.spec || {};\r\n    this.value.spec['namespaceDefaultResourceQuota'] = this.value.spec.namespaceDefaultResourceQuota || { limit: {} };\r\n    this.value.spec['resourceQuota'] = this.value.spec.resourceQuota || { limit: {} };\r\n\r\n    return { typeValues: Object.keys(this.value.spec.resourceQuota.limit) };\r\n  },\r\n\r\n  computed: { ...QUOTA_COMPUTED },\r\n\r\n  methods: {\r\n    updateType(i, type) {\r\n      this.typeValues[i] = type;\r\n    },\r\n    remainingTypes(currentType) {\r\n      return this.mappedTypes\r\n        .filter((mappedType) => !this.typeValues.includes(mappedType.value) || mappedType.value === currentType);\r\n    },\r\n    emitRemove(data) {\r\n      this.$emit('remove', data.row?.value);\r\n    }\r\n  },\r\n};\r\n</script>\r\n<template>\r\n  <div>\r\n    <div class=\"headers mb-10\">\r\n      <div class=\"mr-10\">\r\n        <label>{{ t('resourceQuota.headers.resourceType') }}</label>\r\n      </div>\r\n      <div class=\"mr-20\">\r\n        <label>{{ t('resourceQuota.headers.projectLimit') }}</label>\r\n      </div>\r\n      <div class=\"mr-10\">\r\n        <label>{{ t('resourceQuota.headers.namespaceDefaultLimit') }}</label>\r\n      </div>\r\n    </div>\r\n    <ArrayList\r\n      v-model:value=\"typeValues\"\r\n      label=\"Resources\"\r\n      :add-label=\"t('resourceQuota.add.label')\"\r\n      :default-add-value=\"remainingTypes()[0] ? remainingTypes()[0].value : ''\"\r\n      :add-allowed=\"remainingTypes().length > 0\"\r\n      :mode=\"mode\"\r\n      @remove=\"emitRemove\"\r\n    >\r\n      <template #columns=\"props\">\r\n        <Row\r\n          :value=\"value\"\r\n          :mode=\"mode\"\r\n          :types=\"remainingTypes(typeValues[props.i])\"\r\n          :type=\"typeValues[props.i]\"\r\n          @input=\"$emit('input', $event)\"\r\n          @type-change=\"updateType(props.i, $event)\"\r\n        />\r\n      </template>\r\n    </ArrayList>\r\n  </div>\r\n</template>\r\n<style lang=\"scss\" scoped>\r\n.headers {\r\n    display: flex;\r\n    flex-direction: row;\r\n    justify-content: space-evenly;\r\n    align-items: center;\r\n    border-bottom: 1px solid var(--border);\r\n    height: 30px;\r\n    width: calc(100% - 75px);\r\n\r\n    div {\r\n        width: 100%;\r\n    }\r\n}\r\n</style>\r\n"]}]}