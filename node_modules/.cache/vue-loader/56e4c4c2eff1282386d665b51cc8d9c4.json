{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AllowedPrincipals.vue?vue&type=template&id=b07a81ae", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AllowedPrincipals.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdj4KICAgIDxoMz57eyB0KCdhdXRoQ29uZmlnLmFjY2Vzc01vZGUubGFiZWwnLCB7cHJvdmlkZXI6IGF1dGhDb25maWcubmFtZURpc3BsYXl9KSB9fTwvaDM+CgogICAgPGRpdiBjbGFzcz0icm93Ij4KICAgICAgPGRpdiBjbGFzcz0iY29sIHNwYW4tNiI+CiAgICAgICAgPFJhZGlvR3JvdXAKICAgICAgICAgIHYtbW9kZWw6dmFsdWU9ImF1dGhDb25maWcuYWNjZXNzTW9kZSIKICAgICAgICAgIG5hbWU9ImFjY2Vzc01vZGUiCiAgICAgICAgICA6bW9kZT0ibW9kZSIKICAgICAgICAgIDpvcHRpb25zPSJhY2Nlc3NNb2RlT3B0aW9ucyIKICAgICAgICAvPgogICAgICA8L2Rpdj4KICAgICAgPGRpdiBjbGFzcz0iY29sIHNwYW4tNiI+CiAgICAgICAgPGg0IHYtaWY9ImFjY2Vzc01vZGUhPT0ndW5yZXN0cmljdGVkJyI+CiAgICAgICAgICA8dAogICAgICAgICAgICBrPSJhdXRoQ29uZmlnLmFsbG93ZWRQcmluY2lwYWxJZHMudGl0bGUiCiAgICAgICAgICAgIDpyYXc9InRydWUiCiAgICAgICAgICAvPgogICAgICAgIDwvaDQ+CiAgICAgICAgPEFycmF5TGlzdAogICAgICAgICAgdi1pZj0iYWNjZXNzTW9kZSE9PSd1bnJlc3RyaWN0ZWQnIgogICAgICAgICAga2V5PSJhbGxvd2VkUHJpbmNpcGFsSWRzIgogICAgICAgICAgdi1tb2RlbDp2YWx1ZT0iYXV0aENvbmZpZy5hbGxvd2VkUHJpbmNpcGFsSWRzIgogICAgICAgICAgdGl0bGUta2V5PSJhdXRoQ29uZmlnLmFsbG93ZWRQcmluY2lwYWxJZHMubGFiZWwiCiAgICAgICAgICA6bW9kZT0ibW9kZSIKICAgICAgICAgIDpwcm90aXA9ImZhbHNlIgogICAgICAgID4KICAgICAgICAgIDx0ZW1wbGF0ZSAjdmFsdWU9Intyb3d9Ij4KICAgICAgICAgICAgPFByaW5jaXBhbAogICAgICAgICAgICAgIDp2YWx1ZT0icm93LnZhbHVlIgogICAgICAgICAgICAvPgogICAgICAgICAgPC90ZW1wbGF0ZT4KCiAgICAgICAgICA8dGVtcGxhdGUKICAgICAgICAgICAgdi1pZj0iYXV0aENvbmZpZy5hbGxvd2VkUHJpbmNpcGFsSWRzLmxlbmd0aCA8PSAxIgogICAgICAgICAgICAjcmVtb3ZlLWJ1dHRvbgogICAgICAgICAgPgogICAgICAgICAgICA8YnV0dG9uCiAgICAgICAgICAgICAgdHlwZT0iYnV0dG9uIgogICAgICAgICAgICAgIGRpc2FibGVkCiAgICAgICAgICAgICAgY2xhc3M9ImJ0biByb2xlLWxpbmsgYmctdHJhbnNwYXJlbnQiCiAgICAgICAgICAgID4KICAgICAgICAgICAgICB7eyB0KCdnZW5lcmljLnJlbW92ZScpIH19CiAgICAgICAgICAgIDwvYnV0dG9uPgogICAgICAgICAgPC90ZW1wbGF0ZT4KCiAgICAgICAgICA8dGVtcGxhdGUgI2FkZD4KICAgICAgICAgICAgPFNlbGVjdFByaW5jaXBhbAogICAgICAgICAgICAgIDptb2RlPSJtb2RlIgogICAgICAgICAgICAgIEBhZGQ9ImFkZFByaW5jaXBhbCIKICAgICAgICAgICAgLz4KICAgICAgICAgIDwvdGVtcGxhdGU+CiAgICAgICAgPC9BcnJheUxpc3Q+CiAgICAgIDwvZGl2PgogICAgPC9kaXY+CiAgPC9kaXY+Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AllowedPrincipals.vue"], "names": [], "mappings": ";EAiEE,CAAC,CAAC,CAAC,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAElF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpC,CAAC;YACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC;QACH,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;YAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC;cACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AllowedPrincipals.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { RadioGroup } from '@components/Form/Radio';\nimport ArrayList from '@shell/components/form/ArrayList';\nimport Principal from '@shell/components/auth/Principal';\nimport SelectPrincipal from '@shell/components/auth/SelectPrincipal';\nimport { _EDIT } from '@shell/config/query-params';\nimport uniq from 'lodash/uniq';\n\nexport default {\n  components: {\n    SelectPrincipal,\n    ArrayList,\n    RadioGroup,\n    Principal,\n  },\n\n  props: {\n    provider: {\n      type:     String,\n      required: true,\n    },\n\n    authConfig: {\n      type:     Object,\n      required: true,\n    },\n\n    mode: {\n      type:    String,\n      default: _EDIT,\n    },\n  },\n\n  computed: {\n    accessModeOptions() {\n      return ['unrestricted', 'restricted', 'required'].map((k) => {\n        return {\n          label: this.t(`authConfig.accessMode.${ k }`, { provider: this.authConfig.nameDisplay }),\n          value: k,\n        };\n      });\n    },\n\n    accessMode() {\n      return this.authConfig?.accessMode;\n    }\n  },\n\n  created() {\n    if ( !this.authConfig.accessMode ) {\n      this.authConfig['accessMode'] = 'restricted';\n    } if (!this.authConfig.allowedPrincipalIds) {\n      this.authConfig['allowedPrincipalIds'] = [];\n    }\n  },\n\n  methods: {\n    addPrincipal(id) {\n      this.authConfig.allowedPrincipalIds = uniq([...this.authConfig.allowedPrincipalIds, id]);\n    },\n  }\n};\n</script>\n\n<template>\n  <div>\n    <h3>{{ t('authConfig.accessMode.label', {provider: authConfig.nameDisplay}) }}</h3>\n\n    <div class=\"row\">\n      <div class=\"col span-6\">\n        <RadioGroup\n          v-model:value=\"authConfig.accessMode\"\n          name=\"accessMode\"\n          :mode=\"mode\"\n          :options=\"accessModeOptions\"\n        />\n      </div>\n      <div class=\"col span-6\">\n        <h4 v-if=\"accessMode!=='unrestricted'\">\n          <t\n            k=\"authConfig.allowedPrincipalIds.title\"\n            :raw=\"true\"\n          />\n        </h4>\n        <ArrayList\n          v-if=\"accessMode!=='unrestricted'\"\n          key=\"allowedPrincipalIds\"\n          v-model:value=\"authConfig.allowedPrincipalIds\"\n          title-key=\"authConfig.allowedPrincipalIds.label\"\n          :mode=\"mode\"\n          :protip=\"false\"\n        >\n          <template #value=\"{row}\">\n            <Principal\n              :value=\"row.value\"\n            />\n          </template>\n\n          <template\n            v-if=\"authConfig.allowedPrincipalIds.length <= 1\"\n            #remove-button\n          >\n            <button\n              type=\"button\"\n              disabled\n              class=\"btn role-link bg-transparent\"\n            >\n              {{ t('generic.remove') }}\n            </button>\n          </template>\n\n          <template #add>\n            <SelectPrincipal\n              :mode=\"mode\"\n              @add=\"addPrincipal\"\n            />\n          </template>\n        </ArrayList>\n      </div>\n    </div>\n  </div>\n</template>\n"]}]}