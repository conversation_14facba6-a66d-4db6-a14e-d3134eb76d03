{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/RoleBindings.vue?vue&type=style&index=0&id=61d2e3b2&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/RoleBindings.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5yb2xlLXJvd3sKICBkaXNwbGF5OiBncmlkOwogIGdyaWQtdGVtcGxhdGUtY29sdW1uczogNDUlIDQ1JSAxMCU7CgogIGdyaWQtY29sdW1uLWdhcDogJGNvbHVtbi1ndXR0ZXI7CiAgbWFyZ2luLWJvdHRvbTogMTBweDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogICYgLnBvcnR7CiAgICBkaXNwbGF5OiBmbGV4OwogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIH0KCiAgJi5zaG93LWhvc3R7CiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDMwJSAxNiUgMTAlIDE1JSAxNSUgNSU7CiAgfQoKfQoKLmFkZC1ob3N0IHsKICBqdXN0aWZ5LXNlbGY6IGNlbnRlcjsKfQoKLnByb3RvY29sIHsKICBoZWlnaHQ6IDEwMCU7Cn0KCi5wb3J0cy1oZWFkZXJzIHsKICBjb2xvcjogdmFyKC0taW5wdXQtbGFiZWwpOwp9CgoudG9nZ2xlLWhvc3QtcG9ydHMgewogIGNvbG9yOiB2YXIoLS1wcmltYXJ5KTsKfQoKLnJlbW92ZSBCVVRUT04gewogIHBhZGRpbmc6IDBweDsKfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/RoleBindings.vue"], "names": [], "mappings": ";AAgbA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;EAElC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EAC/C;;AAEF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACd;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACd", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/RoleBindings.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { RBAC as RBAC_LABELS } from '@shell/config/labels-annotations';\nimport { allHash } from '@shell/utils/promise';\nimport { RBAC, MANAGEMENT } from '@shell/config/types';\nimport { _CONFIG, _DETAIL, _EDIT, _VIEW } from '@shell/config/query-params';\nimport { findBy, removeAt, removeObject } from '@shell/utils/array';\nimport Loading from '@shell/components/Loading';\nimport SortableTable from '@shell/components/SortableTable';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\n\nexport const SCOPE_NAMESPACE = 'Role';\nexport const SCOPE_CLUSTER = 'ClusterRole';\n// export const SCOPE_GLOBAL = 'GlobalRole';\n\nexport default {\n  components: {\n    Loading, LabeledSelect, SortableTable\n  },\n\n  props: {\n    registerAfterHook: {\n      type:    Function,\n      default: null,\n    },\n\n    mode: {\n      type:    String,\n      default: _EDIT,\n    },\n\n    as: {\n      type:    String,\n      default: _CONFIG,\n    },\n\n    inStore: {\n      type:    String,\n      default: 'cluster',\n    },\n\n    roleScope: {\n      type:     String,\n      required: true,\n      validator(val) {\n        return [SCOPE_NAMESPACE, SCOPE_CLUSTER].includes(val);\n      }\n    },\n\n    bindingScope: {\n      type:     String,\n      required: true,\n      validator(val) {\n        return [SCOPE_NAMESPACE, SCOPE_CLUSTER].includes(val);\n      }\n    },\n\n    namespace: {\n      type:    String,\n      default: null,\n    },\n\n    filterRoleKey: {\n      type:    String,\n      default: RBAC_LABELS.PRODUCT\n    },\n\n    filterRoleValue: {\n      type:    String,\n      default: null,\n    },\n  },\n\n  async fetch() {\n    const hash = { allUsers: this.$store.dispatch('management/findAll', { type: MANAGEMENT.USER }) };\n    const inStore = this.inStore;\n\n    if ( this.roleScope === SCOPE_NAMESPACE ) {\n      hash.allRoles = this.$store.dispatch(`${ inStore }/findAll`, { type: RBAC.ROLE });\n    } else if ( this.roleScope === SCOPE_CLUSTER ) {\n      hash.allRoles = this.$store.dispatch(`${ inStore }/findAll`, { type: RBAC.CLUSTER_ROLE });\n    // } else if ( this.roleScope === SCOPE_GLOBAL ) {\n    // hash.allRoles = this.$store.dispatch('management/findAll', { type: MANAGEMENT.GLOBAL_ROLE });\n    } else {\n      throw new Error('Unknown roleScope');\n    }\n\n    if ( this.bindingScope === SCOPE_NAMESPACE ) {\n      hash.allBindings = this.$store.dispatch(`${ inStore }/findAll`, { type: RBAC.ROLE_BINDING });\n    } else if ( this.bindingScope === SCOPE_CLUSTER ) {\n      hash.allBindings = this.$store.dispatch(`${ inStore }/findAll`, { type: RBAC.CLUSTER_ROLE_BINDING });\n    // } else if ( this.bindingScope === SCOPE_GLOBAL ) {\n    // hash.allBindings = this.$store.dispatch('management/findAll', { type: MANAGEMENT.GLOBAL_ROLE_BINDING });\n    } else {\n      throw new Error('Unknown scope');\n    }\n\n    const out = await allHash(hash);\n\n    for ( const key in out ) {\n      this[key] = out[key];\n    }\n\n    this.readExistingBindings();\n  },\n\n  data() {\n    return {\n      allRoles:    null,\n      allBindings: null,\n      allUsers:    null,\n      rows:        null,\n    };\n  },\n\n  computed: {\n    isView() {\n      return this.mode === _VIEW;\n    },\n\n    showDetail() {\n      return this.as === _DETAIL;\n    },\n\n    detailHeaders() {\n      return [\n        {\n          name:     'type',\n          labelKey: 'tableHeaders.type',\n          value:    'subjectKind',\n          sort:     'subjectKind',\n          search:   'subjectKind',\n        },\n        {\n          name:     'subject',\n          labelKey: 'tableHeaders.subject',\n          value:    'userObj.labelForSelect',\n          sort:     'userObj.labelForSelect',\n          search:   'userObj.labelForSelect',\n        },\n        {\n          name:     'role',\n          labelKey: 'tableHeaders.role',\n          value:    'roleObj.nameWithinProduct',\n          sort:     'roleObj.nameWithinProduct',\n          search:   'roleObj.nameWithinProduct',\n        },\n      ];\n    },\n\n    userOptions() {\n      return this.allUsers.filter((x) => {\n        return !x.isSystem;\n      }).map((x) => {\n        return {\n          label: x.labelForSelect,\n          value: x.id,\n        };\n      });\n    },\n\n    roleOptions() {\n      return this.roles.map((x) => {\n        return { label: x.nameWithinProduct, value: x.name };\n      });\n    },\n\n    roles() {\n      const all = this.allRoles;\n\n      if ( !this.filterRoleKey || !this.filterRoleValue ) {\n        return all;\n      }\n\n      const out = all.filter((role) => {\n        return role.metadata?.labels?.[this.filterRoleKey] === this.filterRoleValue;\n      });\n\n      return out;\n    },\n\n    existingBindings() {\n      const roles = this.roles.map((x) => x.name);\n\n      const out = this.allBindings.filter((binding) => {\n        if ( binding.roleRef.kind !== this.roleScope || !binding.roleRef?.name) {\n          return false;\n        }\n\n        if ( this.bindingScope === SCOPE_NAMESPACE && binding.metadata?.namespace !== this.namespace ) {\n          return false;\n        }\n\n        return roles.includes(binding.roleRef.name);\n      });\n\n      return out;\n    },\n\n    unremovedRows() {\n      return this.rows.filter((x) => x.remove !== true);\n    },\n  },\n\n  created() {\n    if ( this.mode !== _VIEW ) {\n      this.registerAfterHook(this.saveRoleBindings, 'saveRoleBindings');\n    }\n  },\n\n  methods: {\n    readExistingBindings() {\n      this.rows = this.existingBindings.map((binding) => {\n        for ( let i = 0 ; i < binding.subjects.length ; i++ ) {\n          const subject = binding.subjects[i];\n\n          // We have no way to do groups right now...\n          if ( subject.kind !== 'User' ) {\n            continue;\n          }\n\n          return {\n            subjectKind: subject.kind,\n            subject:     subject.name,\n            userObj:     findBy(this.allUsers, 'id', subject.name),\n            roleKind:    binding.roleRef.kind,\n            role:        binding.roleRef.name,\n            roleObj:     findBy(this.allRoles, 'id', binding.roleRef.name),\n            existing:    binding,\n            existingIdx: i,\n            remove:      false,\n          };\n        }\n      });\n    },\n\n    async saveRoleBindings() {\n      /* eslint-disable no-console */\n\n      const promises = [];\n\n      for ( const row of this.rows ) {\n        if ( row.remove ) {\n          // Remove an existing entry\n\n          if ( row.existing.subjects.length === 1 ) {\n            // There's only one subject, remove the whole binding\n            console.debug('Remove', row.existing.id, 'only one subject');\n            promises.push(row.existing.remove());\n          } else {\n          // There's multiple subjects, remove just this one\n            removeAt(row.existing.subjects, row.existingIdx);\n            console.debug('Remove', row.existing.id, 'subject', row.existingIdx);\n          }\n        } else if ( row.existing ) {\n          // Maybe update existing, which might be a PUT, or a PUT + POST, or a DELETE + POST\n          // because the role of an existing binding can't be edited in k8s (because reasons, presumably).\n          const obj = row.existing;\n          const subj = obj.subjects[row.existingIdx];\n\n          if ( obj.roleRef.name !== row.role || obj.roleRef.kind !== row.roleKind ) {\n            // The role changed\n            const neu = await this.createFrom(row);\n\n            // Make a new binding\n            promises.push(neu.save());\n            console.debug('Create binding from', row.existing.id, 'because role changed');\n\n            if ( obj.subjects.length === 1 ) {\n              // There's only one subject, remove the whole binding\n              promises.push(obj.remove());\n              console.debug('Remove', row.existing.id, 'because role changed, only one subject');\n            } else {\n              // There's multiple subject, remove this one just this one\n              removeAt(obj.subjects, row.existingIdx);\n              promises.push(obj.save());\n              console.debug('Update', row.existing.id, 'remove subject', row.existingIdx, 'because role changed');\n            }\n          } else if ( subj.name !== row.subject || subj.kind !== row.subjectKind ) {\n            // This subject changed\n            subj.kind = row.subjectKind;\n            subj.name = row.subject;\n\n            promises.push(obj.save());\n            console.debug('Changed', row.existing.id, 'subject', row.existingIdx);\n          } else {\n            // Nothing changed\n            console.debug('Unchanged', row.existing.id, 'subject', row.existingIdx);\n          }\n        } else {\n          // Create new\n          const obj = await this.createFrom(row);\n\n          promises.push(obj.save());\n          console.debug('Create binding');\n        }\n      }\n\n      try {\n        await Promise.all(promises);\n      } catch (e) {\n        // If something goes wrong, forget everything and reload to get the current state.\n        this.readExistingBindings();\n        throw e;\n      }\n\n      /* eslint-enable no-console */\n    },\n\n    createFrom(row) {\n      let type, apiGroup;\n      const inStore = this.inStore;\n\n      if ( this.bindingScope === SCOPE_NAMESPACE ) {\n        type = RBAC.ROLE_BINDING;\n        apiGroup = 'rbac.authorization.k8s.io';\n      } else if ( this.bindingScope === SCOPE_CLUSTER ) {\n        type = RBAC.CLUSTER_ROLE_BINDING;\n        apiGroup = 'rbac.authorization.k8s.io';\n      // } else if ( this.bindingScope === SCOPE_GLOBAL ) {\n      //   type = MANAGEMENT.GLOBAL_ROLE_BINDING;\n      //   inStore = 'management'\n      //   apiGroup = 'management.cattle.io' ?\n      } else {\n        throw new Error('Unknown binding scope');\n      }\n\n      const obj = this.$store.dispatch(`${ inStore }/create`, {\n        type,\n        metadata: {\n          generateName: `ui-${ this.filterRoleValue ? `${ this.filterRoleValue }-` : '' }`,\n          namespace:    this.namespace,\n        },\n        roleRef: {\n          apiGroup,\n          kind: row.roleKind,\n          name: row.role,\n        },\n        subjects: [\n          {\n            apiGroup,\n            kind: row.subjectKind,\n            name: row.subject,\n          },\n        ]\n      });\n\n      return obj;\n    },\n\n    remove(row) {\n      if ( row.existing ) {\n        row['remove'] = true;\n      } else {\n        removeObject(this.rows, row);\n      }\n    },\n\n    add() {\n      this.rows.push({\n        subjectKind: 'User',\n        subject:     '',\n        roleKind:    this.roleScope,\n        role:        '',\n      });\n    }\n  },\n};\n</script>\n\n<template>\n  <Loading v-if=\"$fetchState.pending\" />\n  <div v-else-if=\"showDetail\">\n    <SortableTable\n      :rows=\"unremovedRows\"\n      :headers=\"detailHeaders\"\n      :table-actions=\"false\"\n      :row-actions=\"false\"\n      key-field=\"existing.id\"\n      default-sort-by=\"subject\"\n      :paged=\"true\"\n    />\n  </div>\n  <div v-else>\n    <div\n      v-for=\"(row, idx) in unremovedRows\"\n      :key=\"idx\"\n      class=\"role-row\"\n      :class=\"{[mode]: true}\"\n    >\n      <div class=\"subject\">\n        <LabeledSelect\n          v-model:value=\"row.subject\"\n          label-key=\"rbac.roleBinding.user.label\"\n          :mode=\"mode\"\n          :searchable=\"true\"\n          :taggable=\"true\"\n          :options=\"userOptions\"\n        />\n      </div>\n      <div class=\"binding\">\n        <LabeledSelect\n          v-model:value=\"row.role\"\n          label-key=\"rbac.roleBinding.role.label\"\n          :mode=\"mode\"\n          :searchable=\"true\"\n          :taggable=\"true\"\n          :options=\"roleOptions\"\n        />\n      </div>\n      <div class=\"remove\">\n        <button\n          v-t=\"'generic.remove'\"\n          :disabled=\"isView\"\n          type=\"button\"\n          class=\"btn role-link\"\n          @click=\"remove(row)\"\n        />\n      </div>\n    </div>\n    <div>\n      <button\n        v-t=\"'rbac.roleBinding.add'\"\n        :disabled=\"isView\"\n        type=\"button\"\n        class=\"btn role-tertiary add\"\n        @click=\"add()\"\n      />\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.role-row{\n  display: grid;\n  grid-template-columns: 45% 45% 10%;\n\n  grid-column-gap: $column-gutter;\n  margin-bottom: 10px;\n  align-items: center;\n  & .port{\n    display: flex;\n    justify-content: space-between;\n  }\n\n  &.show-host{\n    grid-template-columns: 30% 16% 10% 15% 15% 5%;\n  }\n\n}\n\n.add-host {\n  justify-self: center;\n}\n\n.protocol {\n  height: 100%;\n}\n\n.ports-headers {\n  color: var(--input-label);\n}\n\n.toggle-host-ports {\n  color: var(--primary);\n}\n\n.remove BUTTON {\n  padding: 0px;\n}\n</style>\n"]}]}