{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcButton/RcButton.vue?vue&type=script&setup=true&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcButton/RcButton.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcButton/RcButton.vue"], "sourcesContent": ["<script setup lang=\"ts\">\n/**\n * A button element used for performing actions, such as submitting forms or\n * opening dialogs.\n *\n * Example:\n *\n * <rc-button primary @click=\"doAction\">Perform an Action</rc-button>\n */\nimport { computed, ref, defineExpose } from 'vue';\nimport { ButtonRoleProps, ButtonSizeProps } from './types';\n\nconst buttonRoles: { role: keyof ButtonRoleProps, className: string }[] = [\n  { role: 'primary', className: 'role-primary' },\n  { role: 'secondary', className: 'role-secondary' },\n  { role: 'tertiary', className: 'role-tertiary' },\n  { role: 'link', className: 'role-link' },\n  { role: 'ghost', className: 'role-ghost' },\n];\n\nconst buttonSizes: { size: keyof ButtonSizeProps, className: string }[] = [\n  { size: 'small', className: 'btn-sm' },\n];\n\nconst props = defineProps<ButtonRoleProps & ButtonSizeProps>();\n\nconst buttonClass = computed(() => {\n  const activeRole = buttonRoles.find(({ role }) => props[role]);\n  const isButtonSmall = buttonSizes.some(({ size }) => props[size]);\n\n  return {\n    btn: true,\n\n    [activeRole?.className || 'role-primary']: true,\n\n    'btn-sm': isButtonSmall,\n  };\n});\n\nconst RcFocusTarget = ref<HTMLElement | null>(null);\n\nconst focus = () => {\n  RcFocusTarget?.value?.focus();\n};\n\ndefineExpose({ focus });\n</script>\n\n<template>\n  <button\n    ref=\"RcFocusTarget\"\n    role=\"button\"\n    :class=\"{ ...buttonClass, ...($attrs.class || { }) }\"\n  >\n    <slot name=\"before\">\n      <!-- Empty Content -->\n    </slot>\n    <slot>\n      <!-- Empty Content -->\n    </slot>\n    <slot name=\"after\">\n      <!-- Empty Content -->\n    </slot>\n  </button>\n</template>\n\n<style lang=\"scss\" scoped>\nbutton {\n  &.role-link {\n    &:focus, &.focused {\n      @include focus-outline;\n      outline-offset: -2px;\n    }\n\n    &:hover {\n      background-color: var(--accent-btn);\n      box-shadow: none;\n    }\n  }\n\n  &.role-ghost {\n    padding: 0;\n    background-color: transparent;\n\n    &:focus, &.focused {\n      @include focus-outline;\n      outline-offset: 0;\n    }\n\n    &:focus-visible {\n      @include focus-outline;\n      outline-offset: 0;\n    }\n  }\n}</style>\n"], "names": [], "mappings": ";AASA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;;;;;;;;;;AAT1D,CAAC,CAAC;AACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC;AACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACV,CAAC;AACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpE,CAAC,CAAC;AAIF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5C,CAAC;;AAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxC,CAAC;;AAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAgD;;AAE9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B,CAAC,CAAC,CAAC;AACH,CAAC,CAAC;;AAEF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/B,CAAC;;AAED,QAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;;"}]}