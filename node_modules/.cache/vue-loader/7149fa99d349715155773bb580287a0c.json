{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceSelector.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceSelector.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceSelector.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAExB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACb,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7H,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;IAE1G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAErC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACZ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpD,CAAC;IACH,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrD,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAClC,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACvB,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnD;IACF,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5I,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1E,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACjC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C,CAAC;IACH,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EAC5B;;AAEF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceSelector.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { Banner } from '@components/Banner';\nimport MatchExpressions from '@shell/components/form/MatchExpressions';\nimport ResourceTable from '@shell/components/ResourceTable';\nimport { allHash } from '@shell/utils/promise';\nimport { _EDIT } from '@shell/config/query-params';\nimport { convert, matching, simplify } from '@shell/utils/selector';\nimport throttle from 'lodash/throttle';\n\nexport default {\n  name: 'ResourceSelector',\n\n  components: {\n    Banner,\n    MatchExpressions,\n    ResourceTable,\n  },\n\n  props: {\n    mode: {\n      type:    String,\n      default: _EDIT,\n    },\n    type: {\n      type:     String,\n      required: true,\n    },\n    value: {\n      type:     Object,\n      required: true,\n    },\n    namespace: {\n      type:    String,\n      default: '',\n    },\n  },\n\n  async fetch() {\n    // Used in conjunction with `matches/match/label selectors`. Requires https://github.com/rancher/dashboard/issues/10417 to fix\n    const hash = await allHash({ allResources: this.$store.dispatch('cluster/findAll', { type: this.type }) });\n\n    this.allResources = hash.allResources;\n\n    this.updateMatchingResources();\n  },\n\n  data() {\n    const matchingResources = {\n      matched: 0,\n      matches: [],\n      none:    true,\n      sample:  null,\n      total:   0,\n    };\n\n    return {\n      matchingResources,\n      allResources:        [],\n      allResourcesInScope: [],\n      tableHeaders:        this.$store.getters['type-map/headersFor'](\n        this.$store.getters['cluster/schemaFor'](this.type)\n      ),\n    };\n  },\n\n  watch: {\n    namespace:                'updateMatchingResources',\n    'value.matchLabels':      'updateMatchingResources',\n    'value.matchExpressions': 'updateMatchingResources',\n  },\n\n  computed: {\n    schema() {\n      return this.$store.getters['cluster/schemaFor'](this.type);\n    },\n    selectorExpressions: {\n      get() {\n        return convert(\n          this.value.matchLabels || {},\n          this.value.matchExpressions || []\n        );\n      },\n      set(selectorExpressions) {\n        const { matchLabels, matchExpressions } = simplify(selectorExpressions);\n\n        this.value['matchLabels'] = matchLabels;\n        this.value['matchExpressions'] = matchExpressions;\n      }\n    },\n  },\n\n  methods: {\n    updateMatchingResources: throttle(function() {\n      this.allResourcesInScope = this.namespace ? this.allResources.filter((res) => res.metadata.namespace === this.namespace) : this.allResources;\n      const match = matching(this.allResourcesInScope, this.selectorExpressions);\n      const matched = match.length || 0;\n      const sample = match[0]?.nameDisplay;\n\n      this.matchingResources = {\n        matched,\n        matches: match,\n        none:    matched === 0,\n        sample,\n        total:   this.allResourcesInScope.length,\n      };\n    }, 250, { leading: true }),\n  }\n\n};\n</script>\n\n<template>\n  <div>\n    <div class=\"row\">\n      <div class=\"col span-12\">\n        <MatchExpressions\n          v-model:value=\"selectorExpressions\"\n          :mode=\"mode\"\n          :show-remove=\"false\"\n          :type=\"type\"\n          :target-resources=\"allResourcesInScope\"\n        />\n      </div>\n    </div>\n    <div class=\"row\">\n      <div class=\"col span-12\">\n        <Banner :color=\"(matchingResources.none ? 'warning' : 'success')\">\n          <span v-clean-html=\"t('generic.selectors.matchingResources.matchesSome', matchingResources)\" />\n        </Banner>\n      </div>\n    </div>\n    <div class=\"row\">\n      <div class=\"col span-12\">\n        <ResourceTable\n          :rows=\"matchingResources.matches\"\n          :headers=\"tableHeaders\"\n          key-field=\"id\"\n          :table-actions=\"false\"\n          :schema=\"schema\"\n          :groupable=\"false\"\n          :search=\"false\"\n        />\n      </div>\n    </div>\n  </div>\n</template>\n"]}]}