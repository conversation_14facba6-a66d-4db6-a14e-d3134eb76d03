{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceList/index.vue?vue&type=style&index=0&id=8ed02bdc&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceList/index.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgICAuaGVhZGVyIHsKICAgICAgcG9zaXRpb246IHJlbGF0aXZlOwogICAgfQogICAgSDIgewogICAgICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgICAgIG1hcmdpbjogMCAwIDIwcHggMDsKICAgIH0KICAgIC5maWx0ZXJ7CiAgICAgIGxpbmUtaGVpZ2h0OiA0NXB4OwogICAgfQogICAgLnJpZ2h0LWFjdGlvbiB7CiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgICAgdG9wOiAxMHB4OwogICAgICByaWdodDogMTBweDsKICAgIH0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceList/index.vue"], "names": [], "mappings": ";IAkSI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB;IACA,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACpB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceList/index.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport ResourceTable from '@shell/components/ResourceTable';\nimport Loading from '@shell/components/Loading';\nimport Ma<PERSON><PERSON> from './Mast<PERSON>';\nimport ResourceLoadingIndicator from './ResourceLoadingIndicator';\nimport ResourceFetch from '@shell/mixins/resource-fetch';\nimport IconMessage from '@shell/components/IconMessage.vue';\nimport { ResourceListComponentName } from './resource-list.config';\nimport { PanelLocation, ExtensionPoint } from '@shell/core/types';\nimport ExtensionPanel from '@shell/components/ExtensionPanel';\nimport { sameContents } from '@shell/utils/array';\n\nexport default {\n  name: ResourceListComponentName,\n\n  components: {\n    Loading,\n    ResourceTable,\n    Masthead,\n    ResourceLoadingIndicator,\n    IconMessage,\n    ExtensionPanel\n  },\n  mixins: [ResourceFetch],\n\n  props: {\n    hasAdvancedFiltering: {\n      type:    Boolean,\n      default: false\n    },\n    advFilterHideLabelsAsCols: {\n      type:    Boolean,\n      default: false\n    },\n    advFilterPreventFilteringLabels: {\n      type:    <PERSON>ole<PERSON>,\n      default: false\n    },\n  },\n\n  async fetch() {\n    const store = this.$store;\n    const resource = this.resource;\n\n    const schema = this.schema;\n\n    if ( this.hasListComponent ) {\n      // If you provide your own list then call its fetch\n      const importer = this.listComponent;\n\n      const component = await importer.__asyncLoader();\n\n      if ( component?.typeDisplay ) {\n        this.customTypeDisplay = component.typeDisplay.apply(this);\n      }\n\n      // Is the custom component responsible fetching the resources?\n      // - Component has a fetch method - legacy method. fetch will handle the requests\n      // - Component contains the PaginatedResourceTable component - go forward method. PaginatedResourceTable owns fetching the resources\n      if ( component?.fetch || component?.components?.['PaginatedResourceTable']) {\n        this.componentWillFetch = true;\n      }\n\n      // If the custom component supports it, ask it what resources it loads, so we can\n      // use the incremental loading indicator when enabled\n      if (component?.$loadingResources) {\n        const { loadResources, loadIndeterminate } = component?.$loadingResources(this.$route, this.$store);\n\n        this.loadResources = loadResources || [resource];\n        this.loadIndeterminate = loadIndeterminate || false;\n      }\n    }\n\n    if ( !this.componentWillFetch ) {\n      if ( !schema ) {\n        store.dispatch('loadingError', new Error(this.t('nav.failWhale.resourceListNotFound', { resource }, true)));\n\n        return;\n      }\n\n      // See comment for `namespaceFilter` and `pagination` watchers, skip fetch if we're not ready yet... and something is going to call fetch later on\n      if (!this.namespaceFilterRequired && (!this.canPaginate || this.refreshFlag)) {\n        await this.$fetchType(resource);\n      }\n    }\n  },\n\n  data() {\n    const getters = this.$store.getters;\n    const params = { ...this.$route.params };\n    const resource = params.resource;\n\n    const hasListComponent = getters['type-map/hasCustomList'](resource);\n\n    const inStore = getters['currentStore'](resource);\n    const schema = getters[`${ inStore }/schemaFor`](resource);\n\n    const showMasthead = getters[`type-map/optionsFor`](resource).showListMasthead;\n\n    return {\n      schema,\n      hasListComponent,\n      showMasthead:                     showMasthead === undefined ? true : showMasthead,\n      resource,\n      extensionType:                    ExtensionPoint.PANEL,\n      extensionLocation:                PanelLocation.RESOURCE_LIST,\n      loadResources:                    [resource], // List of resources that will be loaded, this could be many (`Workloads`)\n      /**\n       * Will the custom component handle the fetch of resources....\n       * or will this instance fetch resources\n       */\n      componentWillFetch:               false,\n      // manual refresh\n      manualRefreshInit:                false,\n      watch:                            false,\n      force:                            false,\n      // Provided by fetch later\n      customTypeDisplay:                null,\n      // incremental loading\n      loadIndeterminate:                false,\n      // query param for simple filtering\n      useQueryParamsForSimpleFiltering: true,\n    };\n  },\n\n  computed: {\n    headers() {\n      if ( this.hasListComponent || !this.schema ) {\n        // Custom lists figure out their own headers\n        return [];\n      }\n\n      return this.$store.getters['type-map/headersFor'](this.schema, this.canPaginate);\n    },\n\n    groupBy() {\n      return this.$store.getters['type-map/groupByFor'](this.schema);\n    },\n\n    showIncrementalLoadingIndicator() {\n      return this.perfConfig?.incrementalLoading?.enabled;\n    },\n\n  },\n\n  watch: {\n\n    /**\n     * When a NS filter is required and the user selects a different one, kick off a new set of API requests\n     *\n     * ResourceList has two modes\n     * 1) ResourceList component handles API request to fetch resources\n     * 2) Custom list component handles API request to fetch resources\n     *\n     * This covers case 1\n     */\n    namespaceFilter(neu, old) {\n      if (neu && !this.componentWillFetch) {\n        if (sameContents(neu, old)) {\n          return;\n        }\n\n        this.$fetchType(this.resource);\n      }\n    },\n\n    /**\n     * When a pagination is required and the user changes page / sort / filter, kick off a new set of API requests\n     *\n     * ResourceList has two modes\n     * 1) ResourceList component handles API request to fetch resources\n     * 2) Custom list component handles API request to fetch resources\n     *\n     * This covers case 1\n     */\n    pagination(neu, old) {\n      if (neu && !this.componentWillFetch && !this.paginationEqual(neu, old)) {\n        this.$fetchType(this.resource);\n      }\n    },\n\n    /**\n     * Monitor the rows to ensure deleting the last entry in a server-side paginated page doesn't\n     * result in an empty page\n     */\n    rows(neu) {\n      if (!this.pagination) {\n        return;\n      }\n\n      if (this.pagination.page > 1 && neu.length === 0) {\n        this.setPagination({\n          ...this.pagination,\n          page: this.pagination.page - 1\n        });\n      }\n    },\n  },\n\n  created() {\n    let listComponent = false;\n\n    const resource = this.$route.params.resource;\n    const hasListComponent = this.$store.getters['type-map/hasCustomList'](resource);\n\n    if ( hasListComponent ) {\n      listComponent = this.$store.getters['type-map/importList'](resource);\n    }\n\n    this.listComponent = listComponent;\n  },\n};\n</script>\n\n<template>\n  <IconMessage\n    v-if=\"namespaceFilterRequired\"\n    :vertical=\"true\"\n    :subtle=\"false\"\n    icon=\"icon-filter_alt\"\n  >\n    <template #message>\n      {{ t('resourceList.nsFiltering') }}\n    </template>\n  </IconMessage>\n  <IconMessage\n    v-else-if=\"paginationNsFilterRequired\"\n    :vertical=\"true\"\n    :subtle=\"false\"\n    icon=\"icon-filter_alt\"\n  >\n    <template #message>\n      {{ t('resourceList.nsFilteringGeneric') }}\n    </template>\n  </IconMessage>\n  <div\n    v-else\n    class=\"outlet\"\n  >\n    <Masthead\n      v-if=\"showMasthead\"\n      :type-display=\"customTypeDisplay\"\n      :schema=\"schema\"\n      :resource=\"resource\"\n      :show-incremental-loading-indicator=\"showIncrementalLoadingIndicator\"\n      :load-resources=\"loadResources\"\n      :load-indeterminate=\"loadIndeterminate\"\n    >\n      <template #extraActions>\n        <slot name=\"extraActions\" />\n      </template>\n    </Masthead>\n    <!-- Extensions area -->\n    <ExtensionPanel\n      :resource=\"{}\"\n      :type=\"extensionType\"\n      :location=\"extensionLocation\"\n    />\n\n    <div\n      v-if=\"hasListComponent\"\n    >\n      <component\n        :is=\"listComponent\"\n        :incremental-loading-indicator=\"showIncrementalLoadingIndicator\"\n        :rows=\"rows\"\n        v-bind=\"$data\"\n      />\n    </div>\n    <ResourceTable\n      v-else\n      :schema=\"schema\"\n      :rows=\"rows\"\n      :alt-loading=\"canPaginate && !isFirstLoad\"\n      :loading=\"loading\"\n      :headers=\"headers\"\n      :group-by=\"groupBy\"\n      :has-advanced-filtering=\"hasAdvancedFiltering\"\n      :adv-filter-hide-labels-as-cols=\"advFilterHideLabelsAsCols\"\n      :adv-filter-prevent-filtering-labels=\"advFilterPreventFilteringLabels\"\n      :use-query-params-for-simple-filtering=\"useQueryParamsForSimpleFiltering\"\n      :force-update-live-and-delayed=\"forceUpdateLiveAndDelayed\"\n      :external-pagination-enabled=\"canPaginate\"\n      :external-pagination-result=\"paginationResult\"\n      @pagination-changed=\"paginationChanged\"\n    />\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n    .header {\n      position: relative;\n    }\n    H2 {\n      position: relative;\n      margin: 0 0 20px 0;\n    }\n    .filter{\n      line-height: 45px;\n    }\n    .right-action {\n      position: absolute;\n      top: 10px;\n      right: 10px;\n    }\n</style>\n"]}]}