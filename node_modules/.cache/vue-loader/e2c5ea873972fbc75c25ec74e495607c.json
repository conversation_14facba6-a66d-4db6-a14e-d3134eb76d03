{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AuthBanner.vue?vue&type=style&index=0&id=0e8d2270&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AuthBanner.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5iYW5uZXIgewogIGRpc3BsYXk6IGZsZXg7CiAgICBhbGlnbi1pdGVtczogY2VudGVyOwogIC50ZXh0IHsKICAgIGZsZXg6IDE7CiAgfQp9CgoudmFsdWVzIHsKICB0ciB0ZDpub3QoOmZpcnN0LW9mLXR5cGUpIHsKICAgIHBhZGRpbmctbGVmdDogMTBweDsKICB9Cn0KCg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AuthBanner.vue"], "names": [], "mappings": ";AAyFA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACN,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACpB;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AuthBanner.vue", "sourceRoot": "", "sourcesContent": ["\n<script>\nimport { Banner } from '@components/Banner';\nimport DisableAuthProviderModal from '@shell/components/DisableAuthProviderModal';\n\nexport default {\n  components: {\n    Banner,\n    DisableAuthProviderModal\n  },\n\n  props: {\n    tArgs: {\n      type:     Object,\n      required: true,\n      default:  () => { },\n    },\n    disable: {\n      type:     Function,\n      required: true,\n      default:  () => { },\n    },\n    edit: {\n      type:     Function,\n      required: true,\n      default:  () => { },\n    }\n  },\n\n  computed: {\n    values() {\n      return Object.entries(this.table);\n    }\n  },\n\n  methods: {\n    showDisableModal() {\n      this.$refs.disableAuthProviderModal.show();\n    }\n  },\n};\n</script>\n\n<template>\n  <div>\n    <Banner\n      color=\"success clearfix\"\n      class=\"banner\"\n    >\n      <div class=\"text\">\n        {{ t('authConfig.stateBanner.enabled', tArgs) }}\n      </div>\n      <slot name=\"actions\" />\n      <button\n        type=\"button\"\n        class=\"btn-sm role-primary\"\n        @click=\"edit\"\n      >\n        {{ t('action.edit') }}\n      </button>\n      <button\n        type=\"button\"\n        class=\"ml-10 btn-sm role-primary bg-error\"\n        @click=\"showDisableModal\"\n      >\n        {{ t('generic.disable') }}\n      </button>\n    </Banner>\n\n    <table\n      v-if=\"!!$slots.rows\"\n      class=\"values\"\n    >\n      <slot name=\"rows\" />\n    </table>\n\n    <slot\n      v-if=\"$slots.footer\"\n      name=\"footer\"\n    />\n\n    <DisableAuthProviderModal\n      ref=\"disableAuthProviderModal\"\n      @disable=\"disable\"\n    />\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.banner {\n  display: flex;\n    align-items: center;\n  .text {\n    flex: 1;\n  }\n}\n\n.values {\n  tr td:not(:first-of-type) {\n    padding-left: 10px;\n  }\n}\n\n</style>\n"]}]}