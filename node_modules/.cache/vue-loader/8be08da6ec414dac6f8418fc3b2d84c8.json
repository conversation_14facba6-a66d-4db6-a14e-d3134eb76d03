{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SideNav.vue?vue&type=style&index=0&id=54a98cce&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SideNav.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SideNav.vue"], "names": [], "mappings": ";EAudE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,EAAE;MACH,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEhB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEjB,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACvB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAElB,EAAE;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB;;QAEA,EAAE,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACnB;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC/B,EAAE;UACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC;MACF;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1B;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnB,EAAE,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAClB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACb;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SideNav.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport debounce from 'lodash/debounce';\nimport isEqual from 'lodash/isEqual';\nimport { mapGetters, mapState } from 'vuex';\nimport {\n  mapPref,\n  FAVORITE_TYPES\n} from '@shell/store/prefs';\nimport { getVersionInfo } from '@shell/utils/version';\nimport {\n  addObjects, replaceWith, clear, addObject, sameContents\n} from '@shell/utils/array';\nimport { sortBy } from '@shell/utils/sort';\nimport { ucFirst } from '@shell/utils/string';\n\nimport { HCI, UI, SCHEMA } from '@shell/config/types';\nimport { HARVESTER_NAME as HARVESTER } from '@shell/config/features';\nimport { NAME as EXPLORER } from '@shell/config/product/explorer';\nimport { TYPE_MODES } from '@shell/store/type-map';\nimport { NAME as NAVLINKS } from '@shell/config/product/navlinks';\nimport Group from '@shell/components/nav/Group';\nimport LocaleSelector from '@shell/components/LocaleSelector';\n\nexport default {\n  name:       'SideNav',\n  components: { Group, LocaleSelector },\n  data() {\n    return {\n      groups:        [],\n      gettingGroups: false\n    };\n  },\n\n  created() {\n    // Ensure that changes to resource that change often don't resort to spamming redraw of the side nav\n    this.queueUpdate = debounce(this.getGroups, 500);\n\n    this.getGroups();\n  },\n\n  mounted() {\n    // Sync the navigation tree on fresh load\n    this.$nextTick(() => this.syncNav());\n  },\n\n  watch: {\n\n    /**\n     * Keep this simple, we're only interested in new / removed schemas\n     */\n    allSchemasIds(a, b) {\n      if ( !sameContents(a, b) ) {\n        this.queueUpdate();\n      }\n    },\n\n    allNavLinksIds(a, b) {\n      if ( !sameContents(a, b) ) {\n        this.queueUpdate();\n      }\n    },\n\n    /**\n     * Note - There's no watch on prefs, so this only catches in session changes\n     */\n    favoriteTypes(a, b) {\n      if ( !isEqual(a, b) ) {\n        this.queueUpdate();\n      }\n    },\n\n    locale(a, b) {\n      if ( !isEqual(a, b) ) {\n        this.getGroups();\n      }\n    },\n\n    // Queue namespaceMode and namespaces\n    // Changes to namespaceMode can also change namespaces, so keep this simple and execute both in a shortened queue\n\n    namespaceMode(a, b) {\n      if ( a !== b ) {\n        this.queueUpdate();\n      }\n    },\n\n    namespaces(a, b) {\n      if ( !isEqual(a, b) ) {\n        this.queueUpdate();\n      }\n    },\n\n    clusterReady(a, b) {\n      if ( !isEqual(a, b) ) {\n        // Immediately update because you'll see it come in later\n        this.getGroups();\n      }\n    },\n\n    rootProduct(a, b) {\n      if (a?.name !== b?.name) {\n        // Immediately update because you'll see it come in later\n        this.getGroups();\n      }\n    },\n\n    $route(a, b) {\n      this.$nextTick(() => this.syncNav());\n    },\n\n  },\n\n  computed: {\n    ...mapState(['managementReady', 'clusterReady']),\n    ...mapGetters(['isStandaloneHarvester', 'productId', 'clusterId', 'currentProduct', 'rootProduct', 'isSingleProduct', 'namespaceMode', 'isExplorer', 'isVirtualCluster']),\n    ...mapGetters({ locale: 'i18n/selectedLocaleLabel', hasMultipleLocales: 'i18n/hasMultipleLocales' }),\n    ...mapGetters('type-map', ['activeProducts']),\n\n    favoriteTypes: mapPref(FAVORITE_TYPES),\n\n    supportLink() {\n      const product = this.rootProduct;\n\n      if (product?.supportRoute) {\n        return { ...product.supportRoute, params: { ...product.supportRoute.params, cluster: this.clusterId } };\n      }\n\n      return { name: `c-cluster-${ product?.name }-support` };\n    },\n\n    displayVersion() {\n      if (this.isSingleProduct?.getVersionInfo) {\n        return this.isSingleProduct?.getVersionInfo(this.$store);\n      }\n      const { displayVersion } = getVersionInfo(this.$store);\n\n      return displayVersion;\n    },\n\n    singleProductAbout() {\n      return this.isSingleProduct?.aboutPage;\n    },\n\n    harvesterVersion() {\n      return this.$store.getters['cluster/byId'](HCI.SETTING, 'server-version')?.value || 'unknown';\n    },\n\n    showProductFooter() {\n      if (this.isVirtualProduct) {\n        return true;\n      } else {\n        return false;\n      }\n    },\n\n    isVirtualProduct() {\n      return this.rootProduct.name === HARVESTER;\n    },\n\n    allNavLinks() {\n      if ( !this.clusterId || !this.$store.getters['cluster/schemaFor'](UI.NAV_LINK) ) {\n        return [];\n      }\n\n      return this.$store.getters['cluster/all'](UI.NAV_LINK);\n    },\n\n    allSchemasIds() {\n      const managementReady = this.managementReady;\n      const product = this.currentProduct;\n\n      if ( !managementReady || !product ) {\n        return [];\n      }\n\n      // This does take some up-front time, however avoids an even more costly getGroups call\n      return this.$store.getters[`${ product.inStore }/all`](SCHEMA).map((s) => s.id).sort();\n    },\n\n    namespaces() {\n      return this.$store.getters['activeNamespaceCache'];\n    },\n\n    allNavLinksIds() {\n      return this.allNavLinks.map((a) => a.id);\n    },\n  },\n\n  methods: {\n    /**\n     * Fetch navigation by creating groups from product schemas\n     */\n    getGroups() {\n      if ( this.gettingGroups ) {\n        return;\n      }\n      this.gettingGroups = true;\n\n      if ( !this.clusterReady ) {\n        clear(this.groups);\n        this.gettingGroups = false;\n\n        return;\n      }\n\n      const currentProduct = this.$store.getters['productId'];\n\n      // Always show cluster-level types, regardless of the namespace filter\n      const namespaceMode = 'both';\n      const out = [];\n      const loadProducts = this.isExplorer ? [EXPLORER] : [];\n\n      const productMap = this.activeProducts.reduce((acc, p) => {\n        return { ...acc, [p.name]: p };\n      }, {});\n\n      if ( this.isExplorer ) {\n        for ( const product of this.activeProducts ) {\n          if ( product.inStore === 'cluster' ) {\n            addObject(loadProducts, product.name);\n          }\n        }\n      }\n\n      // This should already have come into the list from above, but in case it hasn't...\n      addObject(loadProducts, currentProduct);\n\n      this.getProductsGroups(out, loadProducts, namespaceMode, productMap);\n\n      this.getExplorerGroups(out);\n\n      replaceWith(this.groups, ...sortBy(out, ['weight:desc', 'label']));\n\n      this.gettingGroups = false;\n    },\n\n    getProductsGroups(out, loadProducts, namespaceMode, productMap) {\n      const clusterId = this.$store.getters['clusterId'];\n      const currentType = this.$route.params.resource || '';\n\n      for ( const productId of loadProducts ) {\n        const modes = [TYPE_MODES.BASIC];\n\n        if ( productId === NAVLINKS ) {\n          // Navlinks produce their own top-level nav items so don't need to show it as a product.\n          continue;\n        }\n\n        if ( productId === EXPLORER ) {\n          modes.push(TYPE_MODES.FAVORITE);\n          modes.push(TYPE_MODES.USED);\n        }\n\n        // Get all types for all modes\n        const typesByMode = this.$store.getters['type-map/allTypes'](productId, modes);\n\n        for ( const mode of modes ) {\n          const types = typesByMode[mode] || {};\n          const more = this.$store.getters['type-map/getTree'](productId, mode, types, clusterId, namespaceMode, currentType);\n\n          if ( productId === EXPLORER || !this.isExplorer ) {\n            addObjects(out, more);\n          } else {\n            const root = more.find((x) => x.name === 'root');\n            const other = more.filter((x) => x.name !== 'root');\n\n            const group = {\n              name:     productId,\n              label:    this.$store.getters['i18n/withFallback'](`product.${ productId }`, null, ucFirst(productId)),\n              children: [...(root?.children || []), ...other],\n              weight:   productMap[productId]?.weight || 0,\n            };\n\n            addObject(out, group);\n          }\n        }\n      }\n    },\n\n    getExplorerGroups(out) {\n      if ( this.isExplorer ) {\n        const allNavLinks = this.allNavLinks;\n        const toAdd = [];\n        const haveGroup = {};\n\n        for ( const obj of allNavLinks ) {\n          if ( !obj.link ) {\n            continue;\n          }\n\n          const groupLabel = obj.spec.group;\n          const groupSlug = obj.normalizedGroup;\n\n          const entry = {\n            name:        `link-${ obj._key }`,\n            link:        obj.link,\n            target:      obj.actualTarget,\n            label:       obj.labelDisplay,\n            sideLabel:   obj.spec.sideLabel,\n            iconSrc:     obj.spec.iconSrc,\n            description: obj.spec.description,\n          };\n\n          // If there's a spec.group (groupLabel), all entries with that name go under one nav group\n          if ( groupSlug ) {\n            if ( haveGroup[groupSlug] ) {\n              continue;\n            }\n\n            haveGroup[groupSlug] = true;\n\n            toAdd.push({\n              name:     `navlink-group-${ groupSlug }`,\n              label:    groupLabel,\n              isRoot:   true,\n              // This is the item that actually shows up in the nav, since this outer group will be invisible\n              children: [\n                {\n                  name:  `navlink-child-${ groupSlug }`,\n                  label: groupLabel,\n                  route: {\n                    name:   'c-cluster-navlinks-group',\n                    params: {\n                      cluster: this.clusterId,\n                      group:   groupSlug,\n                    }\n                  },\n                }\n              ],\n              weight: -100,\n            });\n          } else {\n            toAdd.push({\n              name:     `navlink-${ entry.name }`,\n              label:    entry.label,\n              isRoot:   true,\n              // This is the item that actually shows up in the nav, since this outer group will be invisible\n              children: [entry],\n              weight:   -100,\n            });\n          }\n        }\n\n        addObjects(out, toAdd);\n      }\n    },\n\n    groupSelected(selected) {\n      this.$refs.groups.forEach((grp) => {\n        if (grp.canCollapse) {\n          grp.isExpanded = (grp.group.name === selected.name);\n        }\n      });\n    },\n\n    collapseAll() {\n      this.$refs.groups.forEach((grp) => {\n        grp.isExpanded = false;\n      });\n    },\n\n    syncNav() {\n      const refs = this.$refs.groups;\n\n      if (refs) {\n        // Only expand one group - so after the first has been expanded, no more will\n        // This prevents the 'More Resources' group being expanded in addition to the normal group\n        let canExpand = true;\n        const expanded = refs.filter((grp) => grp.isExpanded)[0];\n\n        if (expanded && expanded.hasActiveRoute()) {\n          this.$nextTick(() => expanded.syncNav());\n\n          return;\n        }\n        refs.forEach((grp) => {\n          if (!grp.group.isRoot) {\n            grp.isExpanded = false;\n            if (canExpand) {\n              const isActive = grp.hasActiveRoute();\n\n              if (isActive) {\n                grp.isExpanded = true;\n                canExpand = false;\n                this.$nextTick(() => grp.syncNav());\n              }\n            }\n          }\n        });\n      }\n    },\n  },\n};\n</script>\n\n<template>\n  <nav class=\"side-nav\">\n    <!-- Actual nav -->\n    <div class=\"nav\">\n      <template\n        v-for=\"(g) in groups\"\n        :key=\"g.name\"\n      >\n        <Group\n          ref=\"groups\"\n          id-prefix=\"\"\n          class=\"package\"\n          :group=\"g\"\n          :can-collapse=\"!g.isRoot\"\n          :show-header=\"!g.isRoot\"\n          @selected=\"groupSelected($event)\"\n          @expand=\"groupSelected($event)\"\n        />\n      </template>\n    </div>\n    <!-- SideNav footer area (seems to be tied to harvester) -->\n    <div\n      v-if=\"showProductFooter\"\n      class=\"footer\"\n    >\n      <!-- support link -->\n      <router-link\n        :to=\"supportLink\"\n        class=\"pull-right\"\n        role=\"link\"\n        :aria-label=\"t('nav.support', {hasSupport: true})\"\n      >\n        {{ t('nav.support', {hasSupport: true}) }}\n      </router-link>\n      <!-- version number -->\n      <span\n        v-clean-tooltip=\"{content: displayVersion, placement: 'top'}\"\n        class=\"clip version text-muted\"\n      >\n        {{ displayVersion }}\n      </span>\n\n      <!-- locale selector -->\n      <LocaleSelector\n        v-if=\"isSingleProduct && hasMultipleLocales && !isStandaloneHarvester\"\n        mode=\"login\"\n        :show-icon=\"false\"\n      />\n    </div>\n    <!-- SideNav footer alternative -->\n    <div\n      v-else\n      class=\"version text-muted flex\"\n    >\n      <router-link\n        v-if=\"singleProductAbout\"\n        :to=\"singleProductAbout\"\n        role=\"link\"\n        :aria-label=\"t('nav.ariaLabel.productAboutPage')\"\n      >\n        {{ displayVersion }}\n      </router-link>\n      <template v-else>\n        <span\n          v-if=\"isVirtualCluster && isExplorer\"\n          v-clean-tooltip=\"{content: harvesterVersion, placement: 'top'}\"\n          class=\"clip text-muted ml-5\"\n        >\n          (Harvester-{{ harvesterVersion }})\n        </span>\n      </template>\n    </div>\n  </nav>\n</template>\n\n<style lang=\"scss\" scoped>\n  .side-nav {\n    display: flex;\n    flex-direction: column;\n    .nav {\n      flex: 1;\n      overflow-y: auto;\n    }\n\n    position: relative;\n    background-color: var(--nav-bg);\n    border-right: var(--nav-border-size) solid var(--nav-border);\n    overflow-y: auto;\n\n    // h6 is used in Group element\n    :deep() h6 {\n      margin: 0;\n      letter-spacing: normal;\n      line-height: 15px;\n\n      A { padding-left: 0; }\n    }\n\n    .tools {\n      display: flex;\n      margin: 10px;\n      text-align: center;\n\n      A {\n        align-items: center;\n        border: 1px solid var(--border);\n        border-radius: 5px;\n        color: var(--body-text);\n        display: flex;\n        justify-content: center;\n        outline: 0;\n        flex: 1;\n        padding: 10px;\n\n        &:hover {\n          background: var(--nav-hover);\n          text-decoration: none;\n        }\n\n        > I {\n          margin-right: 4px;\n        }\n      }\n\n      &.router-link-active:not(:hover) {\n        A {\n          background-color: var(--nav-active);\n        }\n      }\n    }\n\n    .version {\n      cursor: default;\n      margin: 0 10px 10px 10px;\n    }\n\n    .footer {\n      margin: 20px;\n\n      display: flex;\n      flex: 0;\n      flex-direction: row;\n      > * {\n        flex: 1;\n        color: var(--link);\n\n        &:last-child {\n          text-align: right;\n        }\n\n        &:first-child {\n          text-align: left;\n        }\n\n        text-align: center;\n      }\n\n      .version {\n        cursor: default;\n        margin: 0px;\n      }\n\n      .locale-chooser {\n        cursor: pointer;\n      }\n    }\n  }\n\n  .flex {\n    display: flex;\n  }\n\n</style>\n"]}]}