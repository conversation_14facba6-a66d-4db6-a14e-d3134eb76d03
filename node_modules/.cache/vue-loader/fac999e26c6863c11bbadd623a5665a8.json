{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetRepos.vue?vue&type=style&index=0&id=4dd73d77&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetRepos.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5jbHVzdGVyLWNvdW50LWluZm8gewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKCiAgaSB7CiAgICBtYXJnaW4tbGVmdDogNXB4OwogICAgZm9udC1zaXplOiAyMnB4OwogICAgY29sb3I6IHZhcigtLXdhcm5pbmcpOwogIH0KfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetRepos.vue"], "names": [], "mappings": ";AA6KA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEnB,EAAE;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetRepos.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport ResourceTable from '@shell/components/ResourceTable';\nimport Link from '@shell/components/formatter/Link';\nimport Shortened from '@shell/components/formatter/Shortened';\nimport FleetIntro from '@shell/components/fleet/FleetIntro';\n\nimport {\n  AGE,\n  FLEET_REPO,\n  FLEET_REPO_CLUSTER_SUMMARY,\n  FLEET_REPO_CLUSTERS_READY,\n  FLEET_REPO_PER_CLUSTER_STATE,\n  FLEET_REPO_TARGET,\n  FLEET_SUMMARY,\n  NAME,\n  STATE,\n} from '@shell/config/table-headers';\n\n// i18n-ignore repoDisplay\nexport default {\n\n  name: 'FleetRepos',\n\n  components: {\n    ResourceTable, Link, Shortened, FleetIntro\n  },\n  props: {\n    clusterId: {\n      type:     String,\n      required: false,\n      default:  null,\n    },\n    rows: {\n      type:     Array,\n      required: true,\n    },\n\n    schema: {\n      type:     Object,\n      required: true,\n    },\n\n    loading: {\n      type:     Boolean,\n      required: false,\n    },\n\n    useQueryParamsForSimpleFiltering: {\n      type:    <PERSON>olean,\n      default: false\n    }\n  },\n\n  computed: {\n    filteredRows() {\n      if (!this.rows) {\n        return [];\n      }\n\n      // Returns boolean { [namespace]: true }\n      const selectedWorkspace = this.$store.getters['namespaces']();\n\n      return this.rows.filter((row) => {\n        return !!selectedWorkspace[row.metadata.namespace];\n      });\n    },\n\n    isClusterView() {\n      return !!this.clusterId;\n    },\n\n    noRows() {\n      return !this.filteredRows.length;\n    },\n\n    headers() {\n      // Cluster summary is only shown in the cluster view\n      const summary = this.isClusterView ? [{\n        ...FLEET_REPO_CLUSTER_SUMMARY,\n        formatterOpts: { clusterId: this.clusterId },\n      }] : [FLEET_REPO_CLUSTERS_READY, FLEET_SUMMARY];\n\n      // if hasPerClusterState then use the repo state\n      const state = this.isClusterView ? {\n        ...FLEET_REPO_PER_CLUSTER_STATE,\n        value: (repo) => repo.clusterState(this.clusterId),\n      } : STATE;\n\n      return [\n        state,\n        NAME,\n        FLEET_REPO,\n        FLEET_REPO_TARGET,\n        ...summary,\n        AGE\n      ];\n    },\n  },\n  methods: {\n    parseTargetMode(row) {\n      return row.targetInfo?.mode === 'clusterGroup' ? this.t('fleet.gitRepo.warningTooltip.clusterGroup') : this.t('fleet.gitRepo.warningTooltip.cluster');\n    },\n  },\n};\n</script>\n\n<template>\n  <div>\n    <FleetIntro v-if=\"noRows && !loading\" />\n    <ResourceTable\n      v-if=\"!noRows\"\n      v-bind=\"$attrs\"\n      :schema=\"schema\"\n      :headers=\"headers\"\n      :rows=\"rows\"\n      :loading=\"loading\"\n      :use-query-params-for-simple-filtering=\"useQueryParamsForSimpleFiltering\"\n      key-field=\"_key\"\n    >\n      <template #cell:repo=\"{ row }\">\n        <Link\n          :row=\"row\"\n          :value=\"row.spec.repo || ''\"\n          label-key=\"repoDisplay\"\n          before-icon-key=\"repoIcon\"\n          url-key=\"spec.repo\"\n        />\n        {{ row.cluster }}\n        <template v-if=\"row.commitDisplay\">\n          <div class=\"text-muted\">\n            <Shortened\n              long-value-key=\"status.commit\"\n              :row=\"row\"\n              :value=\"row.commitDisplay\"\n            />\n          </div>\n        </template>\n      </template>\n\n      <template\n        v-if=\"!isClusterView\"\n        #cell:clustersReady=\"{ row }\"\n      >\n        <span\n          v-if=\"!row.clusterInfo\"\n          class=\"text-muted\"\n        >&mdash;</span>\n        <span\n          v-else-if=\"row.clusterInfo.unready\"\n          class=\"text-warning\"\n        >{{ row.clusterInfo.ready }}/{{\n          row.clusterInfo.total }}</span>\n        <span\n          v-else\n          class=\"cluster-count-info\"\n        >\n          {{ row.clusterInfo.ready }}/{{ row.clusterInfo.total }}\n          <i\n            v-if=\"!row.clusterInfo.total\"\n            v-clean-tooltip.bottom=\"parseTargetMode(row)\"\n            class=\"icon icon-warning\"\n          />\n        </span>\n      </template>\n\n      <template #cell:target=\"{ row }\">\n        {{ row.targetInfo.modeDisplay }}\n      </template>\n    </ResourceTable>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.cluster-count-info {\n  display: flex;\n  align-items: center;\n\n  i {\n    margin-left: 5px;\n    font-size: 22px;\n    color: var(--warning);\n  }\n}\n</style>\n"]}]}