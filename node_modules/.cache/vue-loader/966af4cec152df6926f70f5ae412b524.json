{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Probe.vue?vue&type=style&index=0&id=e721c4b0&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Probe.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgogIC50aXRsZSB7CiAgICBtYXJnaW4tYm90dG9tOiAxMHB4OwogIH0KICA6ZGVlcCgpIC5sYWJlbGVkLXNlbGVjdCB7CiAgICBoZWlnaHQ6IGF1dG87CiAgfQoK"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Probe.vue"], "names": [], "mappings": ";;EA8WE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACd", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Probe.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\nimport { clone } from '@shell/utils/object';\nimport UnitInput from '@shell/components/form/UnitInput';\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport ShellInput from '@shell/components/form/ShellInput';\nimport KeyValue from '@shell/components/form/KeyValue';\n\nconst KINDS = [\n  'none',\n  'HTTP',\n  'HTTPS',\n  'tcp',\n  'exec',\n];\n\nexport default {\n  emits: ['update:value'],\n\n  components: {\n    LabeledInput, LabeledSelect, UnitInput, ShellInput, KeyValue,\n  },\n\n  props: {\n    value: {\n      type:    [Object, null],\n      default: null,\n    },\n    mode: {\n      type:    String,\n      default: _EDIT,\n    },\n\n    label: {\n      type:    String,\n      default: 'Probe',\n    },\n\n    description: {\n      type:    String,\n      default: '',\n    },\n  },\n\n  data() {\n    let kind = 'none';\n    let probe = null;\n    let exec = null;\n    let httpGet = null;\n    let tcpSocket = null;\n\n    if ( this.value ) {\n      probe = clone(this.value);\n\n      if ( probe.exec ) {\n        kind = 'exec';\n      } else if ( probe.httpGet ) {\n        if ( (probe.httpGet.scheme || '').toLowerCase() === 'https' ) {\n          kind = 'HTTPS';\n        } else {\n          kind = 'HTTP';\n        }\n      } else if ( probe.tcpSocket ) {\n        kind = 'tcp';\n      }\n    } else {\n      probe = {\n        failureThreshold:    3,\n        successThreshold:    1,\n        initialDelaySeconds: 0,\n        timeoutSeconds:      1,\n        periodSeconds:       10,\n        exec:                null,\n        httpGet:             null,\n        tcpSocket:           null,\n      };\n    }\n\n    exec = probe.exec || {};\n    httpGet = probe.httpGet || {};\n    tcpSocket = probe.tcpSocket || {};\n\n    return {\n      probe, kind, exec, httpGet, tcpSocket\n    };\n  },\n\n  computed: {\n    isView() {\n      return this.mode === _VIEW;\n    },\n\n    isNone() {\n      return this.kind === 'none';\n    },\n\n    kindOptions() {\n      return KINDS.map((k) => {\n        return { label: this.t(`workload.container.healthCheck.kind.${ k }`), value: k };\n      });\n    }\n  },\n\n  watch: {\n    kind() {\n      this.update();\n    }\n  },\n\n  methods: {\n    update() {\n      const probe = this.probe;\n\n      if ( this.isNone ) {\n        this.$emit('update:value', null);\n\n        return;\n      }\n\n      switch ( this.kind ) {\n      case 'HTTP':\n      case 'HTTPS':\n        this.httpGet.scheme = this.kind;\n        probe.httpGet = this.httpGet;\n        probe.tcpSocket = null;\n        probe.exec = null;\n        break;\n      case 'tcp':\n        probe.httpGet = null;\n        probe.tcpSocket = this.tcpSocket;\n        probe.exec = null;\n        break;\n      case 'exec':\n        probe.httpGet = null;\n        probe.tcpSocket = null;\n        probe.exec = this.exec;\n        break;\n      }\n\n      this.$emit('update:value', probe);\n    }\n  },\n};\n</script>\n\n<template>\n  <div>\n    <div class=\"title clearfix\">\n      <h3>\n        {{ label }}\n        <i\n          v-if=\"description\"\n          v-clean-tooltip=\"description\"\n          class=\"icon icon-info\"\n        />\n      </h3>\n    </div>\n    <div class=\"row\">\n      <div\n        data-testid=\"input-probe-kind\"\n        class=\"col span-11-of-23\"\n      >\n        <LabeledSelect\n          v-model:value=\"kind\"\n          :mode=\"mode\"\n          :label=\"t('probe.type.label')\"\n          :options=\"kindOptions\"\n          :placeholder=\"t('probe.type.placeholder')\"\n          @update:value=\"update\"\n        />\n\n        <div\n          v-if=\"kind && kind!=='none'\"\n          class=\"spacer-small\"\n        />\n\n        <!-- HTTP/HTTPS -->\n        <div\n          v-if=\"kind === 'HTTP' || kind === 'HTTPS'\"\n          data-testid=\"input-probe-port\"\n        >\n          <LabeledInput\n            v-model:value.number=\"httpGet.port\"\n            type=\"number\"\n            min=\"1\"\n            max=\"65535\"\n            :mode=\"mode\"\n            :label=\"t('probe.httpGet.port.label')\"\n            :placeholder=\"t('probe.httpGet.port.placeholder')\"\n            @update:value=\"update\"\n          />\n\n          <div class=\"spacer-small\" />\n\n          <div data-testid=\"input-probe-path\">\n            <LabeledInput\n              v-model:value=\"httpGet.path\"\n              :mode=\"mode\"\n              :label=\"t('probe.httpGet.path.label')\"\n              :placeholder=\"t('probe.httpGet.path.placeholder')\"\n              @update:value=\"update\"\n            />\n          </div>\n        </div>\n\n        <!-- TCP -->\n        <div\n          v-if=\"kind === 'tcp'\"\n          data-testid=\"input-probe-socket\"\n        >\n          <LabeledInput\n            v-model:value.number=\"tcpSocket.port\"\n            type=\"number\"\n            min=\"1\"\n            max=\"65535\"\n            :mode=\"mode\"\n            :label=\"t('probe.httpGet.port.label')\"\n            :placeholder=\"t('probe.httpGet.port.placeholderDeux')\"\n            @update:value=\"update\"\n          />\n          <div class=\"spacer-small\" />\n        </div>\n\n        <!-- Exec -->\n        <div\n          v-if=\"kind === 'exec'\"\n          data-testid=\"input-probe-command\"\n        >\n          <div class=\"col span-12\">\n            <ShellInput\n              v-model:value=\"exec.command\"\n              :label=\"t('probe.command.label')\"\n              :placeholder=\"t('probe.command.placeholder')\"\n              @update:value=\"update\"\n            />\n          </div>\n          <div class=\"spacer-small\" />\n        </div>\n      </div>\n\n      <div class=\"col span-1-of-13\">\n        <hr\n          v-if=\"kind && kind!=='none'\"\n          :style=\"{'position':'relative', 'margin':'0px'}\"\n          class=\"vertical\"\n        >\n      </div>\n\n      <!-- none -->\n      <div\n        v-if=\"!isNone\"\n        class=\"col span-11-of-23\"\n      >\n        <div class=\"row\">\n          <div\n            data-testid=\"input-probe-periodSeconds\"\n            class=\"col span-4\"\n          >\n            <UnitInput\n              v-model:value=\"probe.periodSeconds\"\n              :mode=\"mode\"\n              :label=\"t('probe.checkInterval.label')\"\n              min=\"1\"\n              :suffix=\"t('suffix.sec')\"\n              :placeholder=\"t('probe.checkInterval.placeholder')\"\n              @update:value=\"update\"\n            />\n          </div>\n          <div\n            data-testid=\"input-probe-initialDelaySeconds\"\n            class=\"col span-4\"\n          >\n            <UnitInput\n              v-model:value=\"probe.initialDelaySeconds\"\n              :mode=\"mode\"\n              :suffix=\"t('suffix.sec')\"\n              :label=\"t('probe.initialDelay.label')\"\n              min=\"0\"\n              :placeholder=\"t('probe.initialDelay.placeholder')\"\n              @update:value=\"update\"\n            />\n          </div>\n          <div\n            data-testid=\"input-probe-timeoutSeconds\"\n            class=\"col span-4\"\n          >\n            <UnitInput\n              v-model:value=\"probe.timeoutSeconds\"\n              min=\"0\"\n              :mode=\"mode\"\n              :suffix=\"t('suffix.sec')\"\n              :label=\"t('probe.timeout.label')\"\n              :placeholder=\"t('probe.timeout.placeholder')\"\n              @update:value=\"update\"\n            />\n          </div>\n        </div>\n\n        <div class=\"spacer-small\" />\n\n        <div class=\"row\">\n          <div\n            data-testid=\"input-probe-successThreshold\"\n            class=\"col span-6\"\n          >\n            <LabeledInput\n              v-model:value.number=\"probe.successThreshold\"\n              type=\"number\"\n              min=\"1\"\n              :mode=\"mode\"\n              :label=\"t('probe.successThreshold.label')\"\n              :placeholder=\"t('probe.successThreshold.placeholder')\"\n              @update:value=\"update\"\n            />\n          </div>\n          <div\n            data-testid=\"input-probe-failureThreshold\"\n            class=\"col span-6\"\n          >\n            <LabeledInput\n              v-model:value.number=\"probe.failureThreshold\"\n              type=\"number\"\n              min=\"1\"\n              :mode=\"mode\"\n              :label=\"t('probe.failureThreshold.label')\"\n              :placeholder=\"t('probe.failureThreshold.placeholder')\"\n              @update:value=\"update\"\n            />\n          </div>\n        </div>\n\n        <template v-if=\"kind === 'HTTP' || kind === 'HTTPS'\">\n          <div class=\"spacer-small\" />\n\n          <div class=\"row\">\n            <div class=\"col span-12\">\n              <KeyValue\n                v-model:value=\"httpGet.httpHeaders\"\n                data-testid=\"input-probe-http-headers\"\n                key-name=\"name\"\n                :mode=\"mode\"\n                :as-map=\"false\"\n                :read-allowed=\"false\"\n                :title=\"t('probe.httpGet.headers.label')\"\n                :key-label=\"t('generic.name')\"\n                :value-label=\"t('generic.value')\"\n                :add-label=\"t('generic.add')\"\n                @update:value=\"update\"\n              >\n                <template #title>\n                  <h3>\n                    {{ t('workload.container.healthCheck.httpGet.headers') }}\n                  </h3>\n                </template>\n              </KeyValue>\n            </div>\n          </div>\n        </template>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n\n  .title {\n    margin-bottom: 10px;\n  }\n  :deep() .labeled-select {\n    height: auto;\n  }\n\n</style>\n"]}]}