{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ProjectMemberEditor.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ProjectMemberEditor.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgQ3JlYXRlRWRpdFZpZXcgZnJvbSAnQHNoZWxsL21peGlucy9jcmVhdGUtZWRpdC12aWV3JzsNCmltcG9ydCBTZWxlY3RQcmluY2lwYWwgZnJvbSAnQHNoZWxsL2NvbXBvbmVudHMvYXV0aC9TZWxlY3RQcmluY2lwYWwnOw0KaW1wb3J0IHsgTUFOQUdFTUVOVCB9IGZyb20gJ0BzaGVsbC9jb25maWcvdHlwZXMnOw0KaW1wb3J0IExvYWRpbmcgZnJvbSAnQHNoZWxsL2NvbXBvbmVudHMvTG9hZGluZyc7DQppbXBvcnQgeyBDYXJkIH0gZnJvbSAnQGNvbXBvbmVudHMvQ2FyZCc7DQppbXBvcnQgeyBSYWRpb0dyb3VwIH0gZnJvbSAnQGNvbXBvbmVudHMvRm9ybS9SYWRpbyc7DQppbXBvcnQgeyBDaGVja2JveCB9IGZyb20gJ0Bjb21wb25lbnRzL0Zvcm0vQ2hlY2tib3gnOw0KaW1wb3J0IHsgREVTQ1JJUFRJT04gfSBmcm9tICdAc2hlbGwvY29uZmlnL2xhYmVscy1hbm5vdGF0aW9ucyc7DQppbXBvcnQgRE9NUHVyaWZ5IGZyb20gJ2RvbXB1cmlmeSc7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgY29tcG9uZW50czogew0KICAgIENhcmQsDQogICAgQ2hlY2tib3gsDQogICAgTG9hZGluZywNCiAgICBSYWRpb0dyb3VwLA0KICAgIFNlbGVjdFByaW5jaXBhbA0KICB9LA0KDQogIG1peGluczogW0NyZWF0ZUVkaXRWaWV3XSwNCg0KICBwcm9wczogew0KICAgIHZhbHVlOiB7DQogICAgICB0eXBlOiAgICAgT2JqZWN0LA0KICAgICAgcmVxdWlyZWQ6IHRydWUNCiAgICB9LA0KDQogICAgdXNlVHdvQ29sdW1uc0ZvckN1c3RvbTogew0KICAgICAgdHlwZTogICAgQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfQ0KICB9LA0KICBhc3luYyBmZXRjaCgpIHsNCiAgICBjb25zdCBbLCByb2xlVGVtcGxhdGVzLCBwcm9qZWN0c10gPSBhd2FpdCBQcm9taXNlLmFsbChbDQogICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnbWFuYWdlbWVudC9maW5kQWxsJywgeyB0eXBlOiBNQU5BR0VNRU5ULlVTRVIgfSksDQogICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnbWFuYWdlbWVudC9maW5kQWxsJywgeyB0eXBlOiBNQU5BR0VNRU5ULlJPTEVfVEVNUExBVEUgfSksDQogICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnbWFuYWdlbWVudC9maW5kQWxsJywgeyB0eXBlOiBNQU5BR0VNRU5ULlBST0pFQ1QgfSkNCiAgICBdKTsNCg0KICAgIHRoaXMucm9sZVRlbXBsYXRlcyA9IHJvbGVUZW1wbGF0ZXM7DQogICAgdGhpcy5wcm9qZWN0cyA9IHByb2plY3RzOw0KICB9LA0KICBkYXRhKCkgew0KICAgIHRoaXMuc2V0Um9sZVRlbXBsYXRlSWRzKHRoaXMudmFsdWUucGVybWlzc2lvbkdyb3VwKTsNCg0KICAgIHJldHVybiB7DQogICAgICBjdXN0b21QZXJtaXNzaW9uczogWw0KICAgICAgICB7DQogICAgICAgICAga2V5OiAgICdjcmVhdGUtbnMnLA0KICAgICAgICAgIGxhYmVsOiB0aGlzLnQoJ3Byb2plY3RNZW1iZXJzLnByb2plY3RQZXJtaXNzaW9ucy5jcmVhdGVOcycpLA0KICAgICAgICAgIHZhbHVlOiBmYWxzZSwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGtleTogICAnY29uZmlnbWFwcy1tYW5hZ2UnLA0KICAgICAgICAgIGxhYmVsOiB0aGlzLnQoJ3Byb2plY3RNZW1iZXJzLnByb2plY3RQZXJtaXNzaW9ucy5jb25maWdtYXBzTWFuYWdlJyksDQogICAgICAgICAgdmFsdWU6IGZhbHNlLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAga2V5OiAgICdpbmdyZXNzLW1hbmFnZScsDQogICAgICAgICAgbGFiZWw6IHRoaXMudCgncHJvamVjdE1lbWJlcnMucHJvamVjdFBlcm1pc3Npb25zLmluZ3Jlc3NNYW5hZ2UnKSwNCiAgICAgICAgICB2YWx1ZTogZmFsc2UsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBrZXk6ICAgJ3Byb2plY3RjYXRhbG9ncy1tYW5hZ2UnLA0KICAgICAgICAgIGxhYmVsOiB0aGlzLnQoJ3Byb2plY3RNZW1iZXJzLnByb2plY3RQZXJtaXNzaW9ucy5wcm9qZWN0Y2F0YWxvZ3NNYW5hZ2UnKSwNCiAgICAgICAgICB2YWx1ZTogZmFsc2UsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBrZXk6ICAgJ3Byb2plY3Ryb2xldGVtcGxhdGViaW5kaW5ncy1tYW5hZ2UnLA0KICAgICAgICAgIGxhYmVsOiB0aGlzLnQoJ3Byb2plY3RNZW1iZXJzLnByb2plY3RQZXJtaXNzaW9ucy5wcm9qZWN0cm9sZXRlbXBsYXRlYmluZGluZ3NNYW5hZ2UnKSwNCiAgICAgICAgICB2YWx1ZTogZmFsc2UsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBrZXk6ICAgJ3NlY3JldHMtbWFuYWdlJywNCiAgICAgICAgICBsYWJlbDogdGhpcy50KCdwcm9qZWN0TWVtYmVycy5wcm9qZWN0UGVybWlzc2lvbnMuc2VjcmV0c01hbmFnZScpLA0KICAgICAgICAgIHZhbHVlOiBmYWxzZSwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGtleTogICAnc2VydmljZWFjY291bnRzLW1hbmFnZScsDQogICAgICAgICAgbGFiZWw6IHRoaXMudCgncHJvamVjdE1lbWJlcnMucHJvamVjdFBlcm1pc3Npb25zLnNlcnZpY2VhY2NvdW50c01hbmFnZScpLA0KICAgICAgICAgIHZhbHVlOiBmYWxzZSwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGtleTogICAnc2VydmljZXMtbWFuYWdlJywNCiAgICAgICAgICBsYWJlbDogdGhpcy50KCdwcm9qZWN0TWVtYmVycy5wcm9qZWN0UGVybWlzc2lvbnMuc2VydmljZXNNYW5hZ2UnKSwNCiAgICAgICAgICB2YWx1ZTogZmFsc2UsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBrZXk6ICAgJ3BlcnNpc3RlbnR2b2x1bWVjbGFpbXMtbWFuYWdlJywNCiAgICAgICAgICBsYWJlbDogdGhpcy50KCdwcm9qZWN0TWVtYmVycy5wcm9qZWN0UGVybWlzc2lvbnMucGVyc2lzdGVudHZvbHVtZWNsYWltc01hbmFnZScpLA0KICAgICAgICAgIHZhbHVlOiBmYWxzZSwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGtleTogICAnd29ya2xvYWRzLW1hbmFnZScsDQogICAgICAgICAgbGFiZWw6IHRoaXMudCgncHJvamVjdE1lbWJlcnMucHJvamVjdFBlcm1pc3Npb25zLndvcmtsb2Fkc01hbmFnZScpLA0KICAgICAgICAgIHZhbHVlOiBmYWxzZSwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGtleTogICAnY29uZmlnbWFwcy12aWV3JywNCiAgICAgICAgICBsYWJlbDogdGhpcy50KCdwcm9qZWN0TWVtYmVycy5wcm9qZWN0UGVybWlzc2lvbnMuY29uZmlnbWFwc1ZpZXcnKSwNCiAgICAgICAgICB2YWx1ZTogZmFsc2UsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBrZXk6ICAgJ2luZ3Jlc3MtdmlldycsDQogICAgICAgICAgbGFiZWw6IHRoaXMudCgncHJvamVjdE1lbWJlcnMucHJvamVjdFBlcm1pc3Npb25zLmluZ3Jlc3NWaWV3JyksDQogICAgICAgICAgdmFsdWU6IGZhbHNlLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAga2V5OiAgICdtb25pdG9yaW5nLXVpLXZpZXcnLA0KICAgICAgICAgIGxhYmVsOiB0aGlzLnQoJ3Byb2plY3RNZW1iZXJzLnByb2plY3RQZXJtaXNzaW9ucy5tb25pdG9yaW5nVWlWaWV3JyksDQogICAgICAgICAgdmFsdWU6IGZhbHNlLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAga2V5OiAgICdwcm9qZWN0Y2F0YWxvZ3MtdmlldycsDQogICAgICAgICAgbGFiZWw6IHRoaXMudCgncHJvamVjdE1lbWJlcnMucHJvamVjdFBlcm1pc3Npb25zLnByb2plY3RjYXRhbG9nc1ZpZXcnKSwNCiAgICAgICAgICB2YWx1ZTogZmFsc2UsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBrZXk6ICAgJ3Byb2plY3Ryb2xldGVtcGxhdGViaW5kaW5ncy12aWV3JywNCiAgICAgICAgICBsYWJlbDogdGhpcy50KCdwcm9qZWN0TWVtYmVycy5wcm9qZWN0UGVybWlzc2lvbnMucHJvamVjdHJvbGV0ZW1wbGF0ZWJpbmRpbmdzVmlldycpLA0KICAgICAgICAgIHZhbHVlOiBmYWxzZSwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGtleTogICAnc2VjcmV0cy12aWV3JywNCiAgICAgICAgICBsYWJlbDogdGhpcy50KCdwcm9qZWN0TWVtYmVycy5wcm9qZWN0UGVybWlzc2lvbnMuc2VjcmV0c1ZpZXcnKSwNCiAgICAgICAgICB2YWx1ZTogZmFsc2UsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBrZXk6ICAgJ3NlcnZpY2VhY2NvdW50cy12aWV3JywNCiAgICAgICAgICBsYWJlbDogdGhpcy50KCdwcm9qZWN0TWVtYmVycy5wcm9qZWN0UGVybWlzc2lvbnMuc2VydmljZWFjY291bnRzVmlldycpLA0KICAgICAgICAgIHZhbHVlOiBmYWxzZSwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGtleTogICAnc2VydmljZXMtdmlldycsDQogICAgICAgICAgbGFiZWw6IHRoaXMudCgncHJvamVjdE1lbWJlcnMucHJvamVjdFBlcm1pc3Npb25zLnNlcnZpY2VzVmlldycpLA0KICAgICAgICAgIHZhbHVlOiBmYWxzZSwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGtleTogICAncGVyc2lzdGVudHZvbHVtZWNsYWltcy12aWV3JywNCiAgICAgICAgICBsYWJlbDogdGhpcy50KCdwcm9qZWN0TWVtYmVycy5wcm9qZWN0UGVybWlzc2lvbnMucGVyc2lzdGVudHZvbHVtZWNsYWltc1ZpZXcnKSwNCiAgICAgICAgICB2YWx1ZTogZmFsc2UsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBrZXk6ICAgJ3dvcmtsb2Fkcy12aWV3JywNCiAgICAgICAgICBsYWJlbDogdGhpcy50KCdwcm9qZWN0TWVtYmVycy5wcm9qZWN0UGVybWlzc2lvbnMud29ya2xvYWRzVmlldycpLA0KICAgICAgICAgIHZhbHVlOiBmYWxzZSwNCiAgICAgICAgfSwNCiAgICAgIF0sDQogICAgICBwcm9qZWN0czogICAgICBbXSwNCiAgICAgIHJvbGVUZW1wbGF0ZXM6IFtdLA0KICAgIH07DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgY3VzdG9tUm9sZXMoKSB7DQogICAgICByZXR1cm4gdGhpcy5yb2xlVGVtcGxhdGVzDQogICAgICAgIC5maWx0ZXIoKHJvbGUpID0+IHsNCiAgICAgICAgICByZXR1cm4gIXJvbGUuYnVpbHRpbiAmJiAhcm9sZS5leHRlcm5hbCAmJiAhcm9sZS5oaWRkZW4gJiYgcm9sZS5jb250ZXh0ID09PSAncHJvamVjdCc7DQogICAgICAgIH0pOw0KICAgIH0sDQoNCiAgICBvcHRpb25zKCkgew0KICAgICAgY29uc3QgY3VzdG9tUm9sZXMgPSB0aGlzLmN1c3RvbVJvbGVzLm1hcCgocm9sZSkgPT4gKHsNCiAgICAgICAgbGFiZWw6ICAgICAgIHRoaXMucHVyaWZ5T3B0aW9uKHJvbGUubmFtZURpc3BsYXkpLA0KICAgICAgICBkZXNjcmlwdGlvbjogdGhpcy5wdXJpZnlPcHRpb24ocm9sZS5kZXNjcmlwdGlvbiB8fCByb2xlLm1ldGFkYXRhPy5hbm5vdGF0aW9ucz8uW0RFU0NSSVBUSU9OXSB8fCB0aGlzLnQoJ3Byb2plY3RNZW1iZXJzLnByb2plY3RQZXJtaXNzaW9ucy5ub0Rlc2NyaXB0aW9uJykpLA0KICAgICAgICB2YWx1ZTogICAgICAgdGhpcy5wdXJpZnlPcHRpb24ocm9sZS5pZCksDQogICAgICB9KSk7DQoNCiAgICAgIHJldHVybiBbDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDogICAgICAgdGhpcy50KCdwcm9qZWN0TWVtYmVycy5wcm9qZWN0UGVybWlzc2lvbnMub3duZXIubGFiZWwnKSwNCiAgICAgICAgICBkZXNjcmlwdGlvbjogdGhpcy50KCdwcm9qZWN0TWVtYmVycy5wcm9qZWN0UGVybWlzc2lvbnMub3duZXIuZGVzY3JpcHRpb24nKSwNCiAgICAgICAgICB2YWx1ZTogICAgICAgJ293bmVyJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICAgICAgIHRoaXMudCgncHJvamVjdE1lbWJlcnMucHJvamVjdFBlcm1pc3Npb25zLm1lbWJlci5sYWJlbCcpLA0KICAgICAgICAgIGRlc2NyaXB0aW9uOiB0aGlzLnQoJ3Byb2plY3RNZW1iZXJzLnByb2plY3RQZXJtaXNzaW9ucy5tZW1iZXIuZGVzY3JpcHRpb24nKSwNCiAgICAgICAgICB2YWx1ZTogICAgICAgJ21lbWJlcicNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiAgICAgICB0aGlzLnQoJ3Byb2plY3RNZW1iZXJzLnByb2plY3RQZXJtaXNzaW9ucy5yZWFkT25seS5sYWJlbCcpLA0KICAgICAgICAgIGRlc2NyaXB0aW9uOiB0aGlzLnQoJ3Byb2plY3RNZW1iZXJzLnByb2plY3RQZXJtaXNzaW9ucy5yZWFkT25seS5kZXNjcmlwdGlvbicpLA0KICAgICAgICAgIHZhbHVlOiAgICAgICAncmVhZC1vbmx5Jw0KICAgICAgICB9LA0KICAgICAgICAuLi5jdXN0b21Sb2xlcywNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiAgICAgICB0aGlzLnQoJ3Byb2plY3RNZW1iZXJzLnByb2plY3RQZXJtaXNzaW9ucy5jdXN0b20ubGFiZWwnKSwNCiAgICAgICAgICBkZXNjcmlwdGlvbjogdGhpcy50KCdwcm9qZWN0TWVtYmVycy5wcm9qZWN0UGVybWlzc2lvbnMuY3VzdG9tLmRlc2NyaXB0aW9uJyksDQogICAgICAgICAgdmFsdWU6ICAgICAgICdjdXN0b20nDQogICAgICAgIH0NCiAgICAgIF07DQogICAgfSwNCiAgICBjdXN0b21QZXJtaXNzaW9uc1VwZGF0ZSgpIHsNCiAgICAgIHJldHVybiB0aGlzLmN1c3RvbVBlcm1pc3Npb25zLnJlZHVjZSgoYWNjLCBjdXN0b21QZXJtaXNzaW9uc0l0ZW0pID0+IHsNCiAgICAgICAgY29uc3QgbG9ja2VkRXhpc3QgPSB0aGlzLnJvbGVUZW1wbGF0ZXMuZmluZCgocm9sZVRlbXBsYXRlSXRlbSkgPT4gcm9sZVRlbXBsYXRlSXRlbS5pZCA9PT0gY3VzdG9tUGVybWlzc2lvbnNJdGVtLmtleSk7DQoNCiAgICAgICAgaWYgKGxvY2tlZEV4aXN0ICYmIGxvY2tlZEV4aXN0LmxvY2tlZCkgew0KICAgICAgICAgIGN1c3RvbVBlcm1pc3Npb25zSXRlbVsnbG9ja2VkJ10gPSB0cnVlOw0KICAgICAgICAgIGN1c3RvbVBlcm1pc3Npb25zSXRlbVsndG9vbHRpcCddID0gdGhpcy50KCdtZW1iZXJzLmNsdXN0ZXJQZXJtaXNzaW9ucy5jdXN0b20ubG9ja2VkUm9sZScpOw0KICAgICAgICB9DQoNCiAgICAgICAgcmV0dXJuIFsuLi5hY2MsIGN1c3RvbVBlcm1pc3Npb25zSXRlbV07DQogICAgICB9LCBbXSk7DQogICAgfQ0KICB9LA0KICB3YXRjaDogew0KICAgICd2YWx1ZS5wZXJtaXNzaW9uR3JvdXAnKG5ld1Blcm1pc3Npb25Hcm91cCkgew0KICAgICAgdGhpcy5zZXRSb2xlVGVtcGxhdGVJZHMobmV3UGVybWlzc2lvbkdyb3VwKTsNCiAgICB9LA0KDQogICAgY3VzdG9tUGVybWlzc2lvbnM6IHsNCiAgICAgIGRlZXA6IHRydWUsDQogICAgICBoYW5kbGVyKCkgew0KICAgICAgICB0aGlzLnNldFJvbGVUZW1wbGF0ZUlkcyh0aGlzLnZhbHVlLnBlcm1pc3Npb25Hcm91cCk7DQogICAgICB9DQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgb25BZGQocHJpbmNpcGFsSWQpIHsNCiAgICAgIHRoaXMudmFsdWVbJ3ByaW5jaXBhbElkJ10gPSBwcmluY2lwYWxJZDsNCiAgICB9LA0KDQogICAgc2V0Um9sZVRlbXBsYXRlSWRzKHBlcm1pc3Npb25Hcm91cCkgew0KICAgICAgY29uc3Qgcm9sZVRlbXBsYXRlSWRzID0gdGhpcy5nZXRSb2xlVGVtcGxhdGVJZHMocGVybWlzc2lvbkdyb3VwKTsNCg0KICAgICAgdGhpcy52YWx1ZVsncm9sZVRlbXBsYXRlSWRzJ10gPSByb2xlVGVtcGxhdGVJZHM7DQogICAgfSwNCg0KICAgIGdldFJvbGVUZW1wbGF0ZUlkcyhwZXJtaXNzaW9uR3JvdXApIHsNCiAgICAgIGlmIChwZXJtaXNzaW9uR3JvdXAgPT09ICdvd25lcicpIHsNCiAgICAgICAgcmV0dXJuIFsncHJvamVjdC1vd25lciddOw0KICAgICAgfQ0KDQogICAgICBpZiAocGVybWlzc2lvbkdyb3VwID09PSAnbWVtYmVyJykgew0KICAgICAgICByZXR1cm4gWydwcm9qZWN0LW1lbWJlciddOw0KICAgICAgfQ0KDQogICAgICBpZiAocGVybWlzc2lvbkdyb3VwID09PSAncmVhZC1vbmx5Jykgew0KICAgICAgICByZXR1cm4gWydyZWFkLW9ubHknXTsNCiAgICAgIH0NCg0KICAgICAgaWYgKHBlcm1pc3Npb25Hcm91cCA9PT0gJ2N1c3RvbScpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuY3VzdG9tUGVybWlzc2lvbnMNCiAgICAgICAgICAuZmlsdGVyKChwZXJtaXNzaW9uKSA9PiBwZXJtaXNzaW9uLnZhbHVlKQ0KICAgICAgICAgIC5tYXAoKHBlcm1pc3Npb24pID0+IHBlcm1pc3Npb24ua2V5KTsNCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIFtwZXJtaXNzaW9uR3JvdXBdOw0KICAgIH0sDQogICAgcHVyaWZ5T3B0aW9uKG9wdGlvbikgew0KICAgICAgcmV0dXJuIERPTVB1cmlmeS5zYW5pdGl6ZShvcHRpb24sIHsgQUxMT1dFRF9UQUdTOiBbJ3NwYW4nXSB9KTsNCiAgICB9DQogIH0NCn07DQoNCg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ProjectMemberEditor.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAExB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAC9E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACzE,CAAC,CAAC;;IAEF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1B,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB;UACE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/E,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7E,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACnB,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtF,CAAC,CAAC;IACN,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAClD,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1J,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC,CAAC;;MAEH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1E,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7E,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB;MACF,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACnE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEpH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3F;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,EAAE,CAAC,CAAC,CAAC;IACR;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD;IACF;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEhE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjD,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC/D;EACF;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ProjectMemberEditor.vue", "sourceRoot": "", "sourcesContent": ["<script>\r\nimport CreateEditView from '@shell/mixins/create-edit-view';\r\nimport SelectPrincipal from '@shell/components/auth/SelectPrincipal';\r\nimport { MANAGEMENT } from '@shell/config/types';\r\nimport Loading from '@shell/components/Loading';\r\nimport { Card } from '@components/Card';\r\nimport { RadioGroup } from '@components/Form/Radio';\r\nimport { Checkbox } from '@components/Form/Checkbox';\r\nimport { DESCRIPTION } from '@shell/config/labels-annotations';\r\nimport DOMPurify from 'dompurify';\r\n\r\nexport default {\r\n  components: {\r\n    Card,\r\n    Checkbox,\r\n    Loading,\r\n    RadioGroup,\r\n    SelectPrincipal\r\n  },\r\n\r\n  mixins: [CreateEditView],\r\n\r\n  props: {\r\n    value: {\r\n      type:     Object,\r\n      required: true\r\n    },\r\n\r\n    useTwoColumnsForCustom: {\r\n      type:    Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  async fetch() {\r\n    const [, roleTemplates, projects] = await Promise.all([\r\n      this.$store.dispatch('management/findAll', { type: MANAGEMENT.USER }),\r\n      this.$store.dispatch('management/findAll', { type: MANAGEMENT.ROLE_TEMPLATE }),\r\n      this.$store.dispatch('management/findAll', { type: MANAGEMENT.PROJECT })\r\n    ]);\r\n\r\n    this.roleTemplates = roleTemplates;\r\n    this.projects = projects;\r\n  },\r\n  data() {\r\n    this.setRoleTemplateIds(this.value.permissionGroup);\r\n\r\n    return {\r\n      customPermissions: [\r\n        {\r\n          key:   'create-ns',\r\n          label: this.t('projectMembers.projectPermissions.createNs'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'configmaps-manage',\r\n          label: this.t('projectMembers.projectPermissions.configmapsManage'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'ingress-manage',\r\n          label: this.t('projectMembers.projectPermissions.ingressManage'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'projectcatalogs-manage',\r\n          label: this.t('projectMembers.projectPermissions.projectcatalogsManage'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'projectroletemplatebindings-manage',\r\n          label: this.t('projectMembers.projectPermissions.projectroletemplatebindingsManage'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'secrets-manage',\r\n          label: this.t('projectMembers.projectPermissions.secretsManage'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'serviceaccounts-manage',\r\n          label: this.t('projectMembers.projectPermissions.serviceaccountsManage'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'services-manage',\r\n          label: this.t('projectMembers.projectPermissions.servicesManage'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'persistentvolumeclaims-manage',\r\n          label: this.t('projectMembers.projectPermissions.persistentvolumeclaimsManage'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'workloads-manage',\r\n          label: this.t('projectMembers.projectPermissions.workloadsManage'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'configmaps-view',\r\n          label: this.t('projectMembers.projectPermissions.configmapsView'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'ingress-view',\r\n          label: this.t('projectMembers.projectPermissions.ingressView'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'monitoring-ui-view',\r\n          label: this.t('projectMembers.projectPermissions.monitoringUiView'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'projectcatalogs-view',\r\n          label: this.t('projectMembers.projectPermissions.projectcatalogsView'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'projectroletemplatebindings-view',\r\n          label: this.t('projectMembers.projectPermissions.projectroletemplatebindingsView'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'secrets-view',\r\n          label: this.t('projectMembers.projectPermissions.secretsView'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'serviceaccounts-view',\r\n          label: this.t('projectMembers.projectPermissions.serviceaccountsView'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'services-view',\r\n          label: this.t('projectMembers.projectPermissions.servicesView'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'persistentvolumeclaims-view',\r\n          label: this.t('projectMembers.projectPermissions.persistentvolumeclaimsView'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'workloads-view',\r\n          label: this.t('projectMembers.projectPermissions.workloadsView'),\r\n          value: false,\r\n        },\r\n      ],\r\n      projects:      [],\r\n      roleTemplates: [],\r\n    };\r\n  },\r\n  computed: {\r\n    customRoles() {\r\n      return this.roleTemplates\r\n        .filter((role) => {\r\n          return !role.builtin && !role.external && !role.hidden && role.context === 'project';\r\n        });\r\n    },\r\n\r\n    options() {\r\n      const customRoles = this.customRoles.map((role) => ({\r\n        label:       this.purifyOption(role.nameDisplay),\r\n        description: this.purifyOption(role.description || role.metadata?.annotations?.[DESCRIPTION] || this.t('projectMembers.projectPermissions.noDescription')),\r\n        value:       this.purifyOption(role.id),\r\n      }));\r\n\r\n      return [\r\n        {\r\n          label:       this.t('projectMembers.projectPermissions.owner.label'),\r\n          description: this.t('projectMembers.projectPermissions.owner.description'),\r\n          value:       'owner'\r\n        },\r\n        {\r\n          label:       this.t('projectMembers.projectPermissions.member.label'),\r\n          description: this.t('projectMembers.projectPermissions.member.description'),\r\n          value:       'member'\r\n        },\r\n        {\r\n          label:       this.t('projectMembers.projectPermissions.readOnly.label'),\r\n          description: this.t('projectMembers.projectPermissions.readOnly.description'),\r\n          value:       'read-only'\r\n        },\r\n        ...customRoles,\r\n        {\r\n          label:       this.t('projectMembers.projectPermissions.custom.label'),\r\n          description: this.t('projectMembers.projectPermissions.custom.description'),\r\n          value:       'custom'\r\n        }\r\n      ];\r\n    },\r\n    customPermissionsUpdate() {\r\n      return this.customPermissions.reduce((acc, customPermissionsItem) => {\r\n        const lockedExist = this.roleTemplates.find((roleTemplateItem) => roleTemplateItem.id === customPermissionsItem.key);\r\n\r\n        if (lockedExist && lockedExist.locked) {\r\n          customPermissionsItem['locked'] = true;\r\n          customPermissionsItem['tooltip'] = this.t('members.clusterPermissions.custom.lockedRole');\r\n        }\r\n\r\n        return [...acc, customPermissionsItem];\r\n      }, []);\r\n    }\r\n  },\r\n  watch: {\r\n    'value.permissionGroup'(newPermissionGroup) {\r\n      this.setRoleTemplateIds(newPermissionGroup);\r\n    },\r\n\r\n    customPermissions: {\r\n      deep: true,\r\n      handler() {\r\n        this.setRoleTemplateIds(this.value.permissionGroup);\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    onAdd(principalId) {\r\n      this.value['principalId'] = principalId;\r\n    },\r\n\r\n    setRoleTemplateIds(permissionGroup) {\r\n      const roleTemplateIds = this.getRoleTemplateIds(permissionGroup);\r\n\r\n      this.value['roleTemplateIds'] = roleTemplateIds;\r\n    },\r\n\r\n    getRoleTemplateIds(permissionGroup) {\r\n      if (permissionGroup === 'owner') {\r\n        return ['project-owner'];\r\n      }\r\n\r\n      if (permissionGroup === 'member') {\r\n        return ['project-member'];\r\n      }\r\n\r\n      if (permissionGroup === 'read-only') {\r\n        return ['read-only'];\r\n      }\r\n\r\n      if (permissionGroup === 'custom') {\r\n        return this.customPermissions\r\n          .filter((permission) => permission.value)\r\n          .map((permission) => permission.key);\r\n      }\r\n\r\n      return [permissionGroup];\r\n    },\r\n    purifyOption(option) {\r\n      return DOMPurify.sanitize(option, { ALLOWED_TAGS: ['span'] });\r\n    }\r\n  }\r\n};\r\n\r\n</script>\r\n\r\n<template>\r\n  <Loading v-if=\"$fetchState.pending\" />\r\n  <div v-else>\r\n    <div class=\"row mt-10\">\r\n      <div class=\"col span-12\">\r\n        <SelectPrincipal\r\n          data-testid=\"cluster-member-select\"\r\n          project\r\n          class=\"mb-20\"\r\n          :mode=\"mode\"\r\n          :retain-selection=\"true\"\r\n          @add=\"onAdd\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <Card\r\n      class=\"m-0\"\r\n      :show-highlight-border=\"false\"\r\n      :show-actions=\"false\"\r\n    >\r\n      <template v-slot:title>\r\n        <div class=\"type-title\">\r\n          <h3>{{ t('projectMembers.projectPermissions.label') }}</h3>\r\n          <div class=\"type-description\">\r\n            {{ t('projectMembers.projectPermissions.description') }}\r\n          </div>\r\n        </div>\r\n      </template>\r\n      <template v-slot:body>\r\n        <RadioGroup\r\n          v-model:value=\"value.permissionGroup\"\r\n          data-testid=\"permission-options\"\r\n          :options=\"options\"\r\n          name=\"permission-group\"\r\n        />\r\n        <div\r\n          v-if=\"value.permissionGroup === 'custom'\"\r\n          class=\"custom-permissions ml-20 mt-10\"\r\n          :class=\"{'two-column': useTwoColumnsForCustom}\"\r\n        >\r\n          <div\r\n            v-for=\"(permission, i) in customPermissionsUpdate\"\r\n            :key=\"i\"\r\n          >\r\n            <Checkbox\r\n              v-model:value=\"permission.value\"\r\n              :data-testid=\"`custom-permission-${i}`\"\r\n              :disabled=\"permission.locked\"\r\n              class=\"mb-5\"\r\n              :label=\"permission.label\"\r\n            />\r\n            <i\r\n              v-if=\"permission.locked\"\r\n              v-clean-tooltip=\"permission.tooltip\"\r\n              class=\"icon icon-lock icon-fw\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </template>\r\n    </Card>\r\n  </div>\r\n</template>\r\n<style lang=\"scss\" scoped>\r\n$detailSize: 11px;\r\n\r\n:deep() .type-description {\r\n    font-size: $detailSize;\r\n}\r\n\r\nlabel.radio {\r\n  font-size: 16px;\r\n}\r\n\r\n.custom-permissions {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr 1fr;\r\n  &.two-column {\r\n    grid-template-columns: 1fr 1fr;\r\n  }\r\n\r\n  :deep() .checkbox-label {\r\n    margin-right: 0;\r\n  }\r\n}\r\n</style>\r\n"]}]}