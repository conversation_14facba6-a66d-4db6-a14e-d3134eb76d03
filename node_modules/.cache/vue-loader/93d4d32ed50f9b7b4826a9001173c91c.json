{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SimpleBox.vue?vue&type=style&index=0&id=fabb7f74&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SimpleBox.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci50b3AgewogIGRpc3BsYXk6IGZsZXg7CiAgcG9zaXRpb246IHJlbGF0aXZlOwogID4gaDIgewogICAgZmxleDogMTsKICB9Cn0KLmNsb3NlLWJ1dHRvbiB7CiAgY3Vyc29yOiBwb2ludGVyOwogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICBwYWRkaW5nOiAycHg7CiAgPiBpIHsKICAgIGZvbnQtc2l6ZTogMTRweDsKICAgIG9wYWNpdHk6IDAuNTsKICB9CiAgJjpob3ZlciB7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS13bS1jbG9zZXItaG92ZXItYmcpOwogIH0KfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SimpleBox.vue"], "names": [], "mappings": ";AAgEA,CAAC,CAAC,CAAC,EAAE;EACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,EAAE,CAAC,EAAE;IACH,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT;AACF;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACZ,EAAE,EAAE;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACd;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7C;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SimpleBox.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport Closeable from '@shell/mixins/closeable';\n\nexport default {\n  name: 'SimpleBox',\n\n  emits: ['close'],\n\n  mixins: [Closeable],\n\n  props: {\n    title: {\n      type:    String,\n      default: null,\n    },\n\n    canClose: {\n      type:    Boolean,\n      default: false\n    }\n  },\n\n  methods: {\n    closeBox(event) {\n      this.hide();\n      this.$emit('close', event);\n    }\n  }\n};\n</script>\n\n<template>\n  <div\n    v-if=\"shown\"\n    class=\"simple-box\"\n    data-testid=\"simple-box-container\"\n  >\n    <div\n      v-if=\"title || canClose || $slots.title\"\n      class=\"top\"\n    >\n      <slot name=\"title\">\n        <h2\n          v-if=\"title\"\n          data-testid=\"simple-box-title\"\n        >\n          {{ title }}\n        </h2>\n      </slot>\n      <div\n        v-if=\"canClose || pref\"\n        class=\"close-button\"\n        data-testid=\"simple-box-close\"\n        @click=\"closeBox($event)\"\n      >\n        <i class=\"icon icon-close\" />\n      </div>\n    </div>\n    <div class=\"content\">\n      <slot />\n    </div>\n  </div>\n</template>\n<style lang=\"scss\" scoped>\n.top {\n  display: flex;\n  position: relative;\n  > h2 {\n    flex: 1;\n  }\n}\n.close-button {\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 2px;\n  > i {\n    font-size: 14px;\n    opacity: 0.5;\n  }\n  &:hover {\n    background-color: var(--wm-closer-hover-bg);\n  }\n}\n</style>\n<style lang=\"scss\">\n.simple-box {\n  $padding: 15px;\n\n  background: var(--simple-box-bg) 0% 0% no-repeat padding-box;\n  box-shadow: 0px 0px 10px var(--simple-box-shadow);\n  border: 1px solid var(--simple-box-border);\n  padding: $padding;\n\n  .top {\n    line-height: 24px;\n    font-size: 18px;\n    border-bottom: 1px solid var(--simple-box-divider);\n    padding-bottom: $padding;\n    margin: 0 -15px 10px -15px;\n    padding: 0 15px 15px 15px;\n    align-items: center;\n    display: flex\n\n    & BUTTON {\n      padding: 0;\n      height: fit-content;\n      align-self: flex-start;\n    }\n\n    & H2{\n      margin-bottom: 0;\n    }\n  }\n\n  .content {\n    padding: $padding;\n  }\n}\n</style>\n"]}]}