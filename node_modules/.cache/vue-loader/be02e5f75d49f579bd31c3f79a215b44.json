{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Taints.vue?vue&type=style&index=0&id=22d70909&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Taints.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLmNvbXBhY3Qtc2VsZWN0IHsKICAgIGhlaWdodDogNDBweDsKICB9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Taints.vue"], "names": [], "mappings": ";EA8FE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACd", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Taints.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport KeyValue from '@shell/components/form/KeyValue';\nimport { _VIEW } from '@shell/config/query-params';\nimport Select from '@shell/components/form/Select';\n\nconst DEFAULT_EFFECT_VALUES = {\n  NoSchedule:       'NoSchedule',\n  PreferNoSchedule: 'PreferNoSchedule',\n  NoExecute:        'NoExecute',\n};\n\nexport default {\n  emits: ['update:value', 'input'],\n\n  components: { KeyValue, Select },\n\n  props: {\n    value: {\n      type:    Array,\n      default: null\n    },\n    mode: {\n      type:    String,\n      default: _VIEW\n    },\n    disabled: {\n      default: false,\n      type:    Boolean\n    },\n    effectValues: {\n      type:    Object,\n      default: () => DEFAULT_EFFECT_VALUES\n    }\n  },\n\n  data() {\n    return { effectOptions: Object.keys(this.effectValues).map((k) => ({ label: this.effectValues[k], value: k })) };\n  },\n\n  computed: {\n    localValue: {\n      get() {\n        return this.value;\n      },\n\n      set(localValue) {\n        this.$emit('update:value', localValue);\n      }\n    },\n\n    defaultAddData() {\n      return { effect: this.effectOptions[0].value };\n    }\n  }\n};\n</script>\n\n<template>\n  <div class=\"taints\">\n    <KeyValue\n      :value=\"value\"\n      data-testid=\"taints-keyvalue\"\n      :title=\"t('tableHeaders.taints')\"\n      :mode=\"mode\"\n      :as-map=\"false\"\n      :read-allowed=\"false\"\n      :protip=\"false\"\n      :show-header=\"true\"\n      :default-add-data=\"defaultAddData\"\n      :extra-columns=\"['effect']\"\n      :preserve-keys=\"['effect']\"\n      :add-label=\"t('labels.addTaint')\"\n      :disabled=\"disabled\"\n      @update:value=\"(e) => $emit('update:value', e)\"\n    >\n      <template #label:effect>\n        {{ t('tableHeaders.effect') }}\n      </template>\n\n      <template #col:effect=\"{row, queueUpdate, i}\">\n        <Select\n          v-model:value=\"row.effect\"\n          :data-testid=\"`taints-effect-row-${i}`\"\n          :options=\"effectOptions\"\n          :disabled=\"disabled\"\n          class=\"compact-select\"\n          @update:value=\"queueUpdate\"\n        />\n      </template>\n    </KeyValue>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .compact-select {\n    height: 40px;\n  }\n</style>\n"]}]}