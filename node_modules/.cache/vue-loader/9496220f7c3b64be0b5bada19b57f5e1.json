{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Type.vue?vue&type=template&id=4c933e29&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Type.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Type.vue"], "names": [], "mappings": ";EAoGE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACxC,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB;IACE,CAAC,CAAC;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7H,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B;MACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB;QACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC;QACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B;QACE,CAAC,CAAC,CAAC,CAAC;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC;UACF,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC;UACD,CAAC;YACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC;UACD,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB;IACE,CAAC;MACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB;MACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvF,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,CAAC,CAAC;EACJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Type.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport Favorite from '@shell/components/nav/Favorite';\nimport { TYPE_MODES } from '@shell/store/type-map';\n\nimport TabTitle from '@shell/components/TabTitle';\n\nconst showFavoritesFor = [TYPE_MODES.FAVORITE, TYPE_MODES.USED];\n\nexport default {\n\n  components: { Favorite, TabTitle },\n\n  emits: ['selected'],\n\n  props: {\n    type: {\n      type:     Object,\n      required: true\n    },\n\n    isRoot: {\n      type:    Boolean,\n      default: false,\n    },\n\n    depth: {\n      type:    Number,\n      default: 0,\n    },\n  },\n\n  data() {\n    return { near: false };\n  },\n\n  computed: {\n    showFavorite() {\n      return ( this.type.mode && this.near && showFavoritesFor.includes(this.type.mode) );\n    },\n\n    showCount() {\n      return this.count !== undefined && this.count !== null;\n    },\n\n    namespaceIcon() {\n      return this.type.namespaced;\n    },\n\n    count() {\n      if (this.type.count !== undefined) {\n        return this.type.count;\n      }\n\n      const inStore = this.$store.getters['currentStore'](this.type.name);\n\n      return this.$store.getters[`${ inStore }/count`]({ name: this.type.name });\n    },\n\n    isActive() {\n      const typeFullPath = this.$router.resolve(this.type.route)?.fullPath.toLowerCase();\n      const pageFullPath = this.$route.fullPath?.toLowerCase();\n\n      if ( !this.type.exact) {\n        const typeSplit = typeFullPath.split('/');\n        const pageSplit = pageFullPath.split('/');\n\n        for (let index = 0; index < typeSplit.length; ++index) {\n          if ( index >= pageSplit.length || typeSplit[index] !== pageSplit[index] ) {\n            return false;\n          }\n        }\n\n        return true;\n      }\n\n      return typeFullPath === pageFullPath;\n    }\n\n  },\n\n  methods: {\n    setNear(val) {\n      this.near = val;\n    },\n\n    selectType() {\n      // Prevent issues if custom NavLink is used #5047\n      if (this.type?.route) {\n        const typePath = this.$router.resolve(this.type.route)?.fullPath;\n\n        if (typePath !== this.$route.fullPath) {\n          this.$emit('selected');\n        }\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <router-link\n    v-if=\"type.route\"\n    :key=\"type.name\"\n    v-slot=\"{ href, navigate,isExactActive }\"\n    custom\n    :to=\"type.route\"\n  >\n    <li\n      class=\"child nav-type\"\n      :class=\"{'root': isRoot, [`depth-${depth}`]: true, 'router-link-active': isActive, 'router-link-exact-active': isExactActive}\"\n      @click=\"navigate\"\n      @keypress.enter=\"navigate\"\n    >\n      <TabTitle\n        v-if=\"isExactActive\"\n        :show-child=\"false\"\n      >\n        {{ type.labelKey ? t(type.labelKey) : (type.labelDisplay || type.label) }}\n      </TabTitle>\n      <a\n        role=\"link\"\n        :aria-label=\"type.labelKey ? t(type.labelKey) : (type.labelDisplay || type.label)\"\n        :href=\"href\"\n        class=\"type-link\"\n        :aria-current=\"isActive ? 'page' : undefined\"\n        @click=\"selectType(); navigate($event);\"\n        @mouseenter=\"setNear(true)\"\n        @mouseleave=\"setNear(false)\"\n      >\n        <span\n          v-if=\"type.labelKey\"\n          class=\"label\"\n        ><t :k=\"type.labelKey\" /></span>\n        <span\n          v-else\n          v-clean-html=\"type.labelDisplay || type.label\"\n          class=\"label\"\n          :class=\"{'no-icon': !type.icon}\"\n        />\n        <span\n          v-if=\"showFavorite || namespaceIcon || showCount\"\n          class=\"count\"\n        >\n          <Favorite\n            v-if=\"showFavorite\"\n            :resource=\"type.name\"\n          />\n          <i\n            v-if=\"namespaceIcon\"\n            class=\"icon icon-namespace\"\n            :class=\"{'ns-and-icon': showCount}\"\n            data-testid=\"type-namespaced\"\n          />\n          <span\n            v-if=\"showCount\"\n            data-testid=\"type-count\"\n          >{{ count }}</span>\n        </span>\n      </a>\n    </li>\n  </router-link>\n  <li\n    v-else-if=\"type.link\"\n    class=\"child nav-type nav-link\"\n    data-testid=\"link-type\"\n  >\n    <a\n      role=\"link\"\n      :href=\"type.link\"\n      :target=\"type.target\"\n      rel=\"noopener noreferrer nofollow\"\n      :aria-label=\"type.label\"\n    >\n      <span class=\"label\">{{ type.label }}&nbsp;<i class=\"icon icon-external-link\" /></span>\n    </a>\n  </li>\n  <li v-else>\n    {{ type }}?\n  </li>\n</template>\n\n<style lang=\"scss\" scoped>\n  .ns-and-icon {\n    margin-right: 4px;\n  }\n\n  .type-link:focus-visible span.label {\n    @include focus-outline;\n    outline-offset: 2px;\n  }\n\n  .nav-link a:focus-visible .label {\n    @include focus-outline;\n    outline-offset: 2px;\n  }\n\n  .child {\n    margin: 0 var(--outline) 0 0;\n\n    .label {\n      align-items: center;\n      grid-area: label;\n      overflow: hidden;\n      text-overflow: ellipsis;\n\n      &:not(.nav-type) &.no-icon {\n        padding-left: 3px;\n      }\n\n      :deep() .highlight {\n        background: var(--diff-ins-bg);\n        color: var(--body-text);\n        padding: 2px;\n      }\n\n      :deep() .icon {\n        position: relative;\n        color: var(--muted);\n      }\n    }\n\n    A {\n      display: grid;\n      grid-template-areas: \"label count\";\n      grid-template-columns: auto auto;\n      grid-column-gap: 5px;\n      font-size: 14px;\n      line-height: 24px;\n      padding: 7.5px 7px 7.5px 10px;\n      margin: 0 0 0 -3px;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n      color: var(--body-text);\n      height: 33px;\n\n      &:hover {\n        background: var(--nav-hover);\n        text-decoration: none;\n\n        :deep() .icon {\n          color: var(--body-text);\n        }\n      }\n    }\n\n    .favorite {\n      grid-area: favorite;\n      font-size: 12px;\n      position: relative;\n      vertical-align: middle;\n      margin-right: 4px;\n    }\n\n    .count {\n      font-size: 12px;\n      justify-items: center;\n      padding-right: 4px;\n      display: flex;\n      align-items: center;\n    }\n\n    &.nav-type.nav-link {\n      a .label {\n        display: flex;\n      }\n    }\n\n    &.nav-type:not(.depth-0) {\n      A {\n        padding-left: 16px;\n      }\n\n      :deep() .label I {\n        padding-right: 2px;\n      }\n    }\n\n    &.nav-type:is(.depth-1) {\n      A {\n        font-size: 13px;\n        padding-left: 23px;\n      }\n    }\n\n    &.nav-type:not(.depth-0):not(.depth-1) {\n      A {\n        padding-left: 14px;\n      }\n    }\n  }\n\n</style>\n"]}]}