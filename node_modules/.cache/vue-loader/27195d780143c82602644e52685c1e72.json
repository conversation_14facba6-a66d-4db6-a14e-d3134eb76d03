{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Jump.vue?vue&type=template&id=65ab091d&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Jump.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdj4KICAgIDxwCiAgICAgIGlkPSJkZXNjcmliZS1maWx0ZXItcmVzb3VyY2Utc2VhcmNoIgogICAgICBoaWRkZW4KICAgID4KICAgICAge3sgdCgnbmF2LnJlc291cmNlU2VhcmNoLmZpbHRlcmluZ0Rlc2NyaXB0aW9uJykgfX0KICAgIDwvcD4KICAgIDxpbnB1dAogICAgICByZWY9ImlucHV0IgogICAgICB2LW1vZGVsPSJ2YWx1ZSIKICAgICAgOnBsYWNlaG9sZGVyPSJ0KCduYXYucmVzb3VyY2VTZWFyY2gucGxhY2Vob2xkZXInKSIKICAgICAgY2xhc3M9InNlYXJjaCIKICAgICAgcm9sZT0idGV4dGJveCIKICAgICAgOmFyaWEtbGFiZWw9InQoJ25hdi5yZXNvdXJjZVNlYXJjaC5sYWJlbCcpIgogICAgICBhcmlhLWRlc2NyaWJlZGJ5PSJkZXNjcmliZS1maWx0ZXItcmVzb3VyY2Utc2VhcmNoIgogICAgICBAa2V5dXAuZXNjPSIkZW1pdCgnY2xvc2VTZWFyY2gnKSIKICAgID4KICAgIDxkaXYgY2xhc3M9InJlc3VsdHMiPgogICAgICA8ZGl2CiAgICAgICAgdi1mb3I9ImcgaW4gZ3JvdXBzIgogICAgICAgIDprZXk9ImcubmFtZSIKICAgICAgICBjbGFzcz0icGFja2FnZSIKICAgICAgPgogICAgICAgIDxHcm91cAogICAgICAgICAgdi1pZj0iIWcuaGlkZGVuIgogICAgICAgICAgOmtleT0iZy5uYW1lIgogICAgICAgICAgaWQtcHJlZml4PSIiCiAgICAgICAgICA6Z3JvdXA9ImciCiAgICAgICAgICA6Y2FuLWNvbGxhcHNlPSJmYWxzZSIKICAgICAgICAgIDpmaXhlZC1vcGVuPSJ0cnVlIgogICAgICAgICAgQGNsb3NlPSIkZW1pdCgnY2xvc2VTZWFyY2gnKSIKICAgICAgICA+CiAgICAgICAgICA8dGVtcGxhdGUgI2FjY29yZGlvbj4KICAgICAgICAgICAgPGg2Pnt7IGcubGFiZWwgfX08L2g2PgogICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICA8L0dyb3VwPgogICAgICA8L2Rpdj4KICAgIDwvZGl2PgogIDwvZGl2Pgo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Jump.vue"], "names": [], "mappings": ";EAwEE,CAAC,CAAC,CAAC,CAAC;IACF,CAAC;MACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP;MACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACnD,CAAC,CAAC,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC;IACA,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB;QACE,CAAC,CAAC,CAAC,CAAC,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Jump.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport debounce from 'lodash/debounce';\nimport Group from '@shell/components/nav/Group';\nimport { isMac } from '@shell/utils/platform';\nimport { BOTH, TYPE_MODES } from '@shell/store/type-map';\nimport { COUNT } from '@shell/config/types';\n\nexport default {\n  emits: ['closeSearch'],\n\n  components: { Group },\n\n  data() {\n    return {\n      isMac,\n      value:  '',\n      groups: null,\n    };\n  },\n\n  watch: {\n    value() {\n      this.queueUpdate();\n    },\n  },\n\n  mounted() {\n    this.updateMatches();\n    this.queueUpdate = debounce(this.updateMatches, 250);\n\n    this.$refs.input.focus();\n  },\n\n  methods: {\n    updateMatches() {\n      const clusterId = this.$store.getters['clusterId'];\n      const productId = this.$store.getters['productId'];\n      const product = this.$store.getters['currentProduct'];\n\n      const allTypesByMode = this.$store.getters['type-map/allTypes'](productId, [TYPE_MODES.ALL]) || {};\n      const allTypes = allTypesByMode[TYPE_MODES.ALL];\n      const out = this.$store.getters['type-map/getTree'](productId, TYPE_MODES.ALL, allTypes, clusterId, BOTH, null, this.value);\n\n      // Suplement the output with count info. Usualy the `Type` component would handle this individualy... but scales real bad so give it\n      // some help\n      const counts = this.$store.getters[`${ product.inStore }/all`](COUNT)?.[0]?.counts || {};\n\n      out.forEach((o) => {\n        o.children?.forEach((t) => {\n          const count = counts[t.name];\n\n          t.count = count ? count.summary.count || 0 : null;\n          t.byNamespace = count ? count.namespaces : {};\n          t.revision = count ? count.revision : null;\n        });\n      });\n\n      this.groups = out;\n\n      // Hide top-level groups with no children (or one child that is an overview)\n      this.groups.forEach((g) => {\n        const isRoot = g.isRoot || g.name === 'Root';\n        const hidden = isRoot || g.children?.length === 0 || (g.children?.length === 1 && g.children[0].overview);\n\n        g.hidden = !!hidden;\n      });\n    }\n  },\n};\n</script>\n\n<template>\n  <div>\n    <p\n      id=\"describe-filter-resource-search\"\n      hidden\n    >\n      {{ t('nav.resourceSearch.filteringDescription') }}\n    </p>\n    <input\n      ref=\"input\"\n      v-model=\"value\"\n      :placeholder=\"t('nav.resourceSearch.placeholder')\"\n      class=\"search\"\n      role=\"textbox\"\n      :aria-label=\"t('nav.resourceSearch.label')\"\n      aria-describedby=\"describe-filter-resource-search\"\n      @keyup.esc=\"$emit('closeSearch')\"\n    >\n    <div class=\"results\">\n      <div\n        v-for=\"g in groups\"\n        :key=\"g.name\"\n        class=\"package\"\n      >\n        <Group\n          v-if=\"!g.hidden\"\n          :key=\"g.name\"\n          id-prefix=\"\"\n          :group=\"g\"\n          :can-collapse=\"false\"\n          :fixed-open=\"true\"\n          @close=\"$emit('closeSearch')\"\n        >\n          <template #accordion>\n            <h6>{{ g.label }}</h6>\n          </template>\n        </Group>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .search, .search:hover {\n    position: relative;\n    background-color: var(--dropdown-bg);\n    border-radius: 0;\n    box-shadow: none;\n  }\n\n  .search:focus-visible {\n    outline-offset: -2px;\n  }\n\n  .results {\n    margin-top: -1px;\n    overflow-y: auto;\n    padding: 10px;\n    color: var(--dropdown-text);\n    background-color: var(--dropdown-bg);\n    border: 1px solid var(--dropdown-border);\n    height: 75vh;\n  }\n</style>\n"]}]}