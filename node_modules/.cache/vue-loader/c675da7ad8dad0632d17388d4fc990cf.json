{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GlobalRoleBindings.vue?vue&type=style&index=1&id=69abf3aa&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GlobalRoleBindings.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgJGRldGFpbFNpemU6IDExcHg7CgogIC5kZXByZWNhdGlvbi1ub3RpY2UgewogICAgbWFyZ2luOiA4cHggMCA4cHggMjBweDsKICB9CiAgLnJvbGUtZ3JvdXAgewogICAgLnR5cGUtdGl0bGUgewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogICAgICAudHlwZS1kZXNjcmlwdGlvbiB7CiAgICAgICAgZm9udC1zaXplOiAkZGV0YWlsU2l6ZTsKICAgICAgfQogICAgfQoKICAgIC5jaGVja2JveC1zZWN0aW9uIHsKICAgICAgZGlzcGxheTogZ3JpZDsKCiAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDMsIDFmcik7CgogICAgICAmLS1nbG9iYWwgewogICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMTAwJTsKICAgICAgfQoKICAgICAgLmNoZWNrYm94LWxhYmVsIHsKICAgICAgICAmLXNsb3QgewogICAgICAgICAgZGlzcGxheTogaW5saW5lLWZsZXg7CiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICAgIH0KICAgICAgICBjb2xvcjogdmFyKC0tYm9keS10ZXh0KTsKICAgICAgICBtYXJnaW46IDA7CiAgICAgIH0KICAgIH0KICB9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GlobalRoleBindings.vue"], "names": [], "mappings": ";EAuYE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACxB;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAErC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC7B;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACX;IACF;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GlobalRoleBindings.vue", "sourceRoot": "", "sourcesContent": ["\n<script>\nimport { mapGetters } from 'vuex';\nimport { MANAGEMENT } from '@shell/config/types';\nimport { Checkbox } from '@components/Form/Checkbox';\nimport { _CREATE, _VIEW } from '@shell/config/query-params';\nimport Loading from '@shell/components/Loading';\nimport { addObjects, isArray } from '@shell/utils/array';\nimport { Card } from '@components/Card';\n\n// i18n-uses rbac.globalRoles.types.*.label\n// i18n-uses rbac.globalRoles.types.*.description\n\n/**\n * Display checkboxes for each global role, checked for given user or principal (group). Can save changes.\n */\nexport default {\n  emits: ['hasChanges', 'canLogIn', 'changed'],\n\n  components: {\n    Checkbox,\n    Loading,\n    Card\n  },\n  props: {\n    mode: {\n      type:    String,\n      default: _VIEW,\n    },\n    realMode: {\n      type:    String,\n      default: _VIEW,\n    },\n    assignOnly: {\n      type:    Boolean,\n      default: false,\n    },\n    type: {\n      type:    String,\n      default: 'group',\n      validator(val) {\n        return val === 'group' || val === 'user';\n      }\n    },\n    groupPrincipalId: {\n      type:    String,\n      default: ''\n    },\n    userId: {\n      type:    String,\n      default: ''\n    }\n  },\n  async fetch() {\n    try {\n      this.allRoles = await this.$store.dispatch('management/findAll', { type: MANAGEMENT.GLOBAL_ROLE });\n      if (!this.sortedRoles) {\n        this.sortedRoles = {\n          global:  [],\n          builtin: [],\n          custom:  []\n        };\n\n        this.allRoles.forEach((role) => {\n          const roleType = this.getRoleType(role);\n\n          if (roleType) {\n            this.sortedRoles[roleType].push(role);\n          }\n        });\n\n        const sort = (a, b) => a.nameDisplay.localeCompare(b.nameDisplay);\n\n        // global roles are not sorted\n        this.sortedRoles.builtin = this.sortedRoles.builtin.sort(sort);\n        this.sortedRoles.custom = this.sortedRoles.custom.sort(sort);\n\n        if (!this.isCreate) {\n          this.globalRoleBindings = await this.$store.dispatch('management/findAll', { type: MANAGEMENT.GLOBAL_ROLE_BINDING, opt: { force: true } });\n        }\n\n        // Sort the global roles - use the order defined in 'globalPermissions' and then add the remaining roles after\n        const globalRoles = [];\n        const globalRolesAdded = {};\n\n        this.globalPermissions.forEach((id) => {\n          const role = this.sortedRoles.global.find((r) => r.id === id);\n\n          if (role) {\n            globalRoles.push(role);\n            globalRolesAdded[id] = true;\n          }\n        });\n\n        // Remaining global roles\n        const remainingGlobalRoles = this.sortedRoles.global.filter((r) => !globalRolesAdded[r.id]);\n\n        this.sortedRoles.global = globalRoles;\n        this.sortedRoles.global.push(...remainingGlobalRoles);\n        // End sort of global roles\n\n        this.update();\n      }\n    } catch (e) { }\n  },\n  data() {\n    return {\n      // This not only identifies global roles but the order here is the order we want to display them in the UI\n      globalPermissions: [\n        'admin',\n        'user',\n        'user-base',\n      ],\n      globalRoleBindings:    null,\n      sortedRoles:           null,\n      selectedRoles:         [],\n      startingSelectedRoles: [],\n      assignOnlyRoles:       {},\n      roleChanges:           {}\n    };\n  },\n  computed: {\n    ...mapGetters({ t: 'i18n/t' }),\n\n    isCreate() {\n      return this.realMode === _CREATE;\n    },\n\n    isUser() {\n      return this.type === 'user';\n    }\n  },\n  watch: {\n    groupPrincipalId(groupPrincipalId, oldGroupPrincipalId) {\n      if (groupPrincipalId === oldGroupPrincipalId) {\n        return;\n      }\n      this.update();\n    },\n    userId(userId, oldUserId) {\n      if (userId === oldUserId) {\n        return;\n      }\n      this.update();\n    }\n  },\n  methods: {\n    getRoleType(role) {\n      if (this.globalPermissions.find((p) => p === role.id)) {\n        return 'global';\n      } else if (role.builtin) {\n        return 'builtin';\n      } else {\n        return 'custom';\n      }\n    },\n    getUnique(...ids) {\n      return `${ this.groupPrincipalId || this.userId }-${ ids.join('-') }`;\n    },\n    selectDefaults() {\n      Object.values(this.sortedRoles).forEach((roles) => {\n        roles.forEach((mappedRole) => {\n          if (mappedRole.newUserDefault) {\n            this.selectedRoles.push(mappedRole.id);\n          }\n        });\n      });\n    },\n    update() {\n      this.selectedRoles = [];\n      this.startingSelectedRoles = [];\n      this.assignOnlyRoles = {};\n      if (this.isCreate) {\n        // Start with the new user default for each role\n        this.selectDefaults();\n      } else {\n        // Start with the principal/user's roles\n        if (!this.groupPrincipalId && !this.userId) {\n          return;\n        }\n\n        const boundRoles = this.globalRoleBindings.filter((grb) => {\n          return this.groupPrincipalId ? grb.groupPrincipalName === this.groupPrincipalId : grb.userName === this.userId;\n        });\n\n        Object.values(this.sortedRoles).forEach((roles) => {\n          roles.forEach((mappedRole) => {\n            const boundRole = boundRoles.find((boundRole) => boundRole.globalRoleName === mappedRole.id);\n\n            if (!!boundRole) {\n              this.selectedRoles.push(mappedRole.id);\n              this.startingSelectedRoles.push({\n                roleId:    mappedRole.id,\n                bindingId: boundRole.id\n              });\n              // Checkboxes should be disabled, besides normal 'mode' ways, if we're only assigning and not removing existing roles\n              this.assignOnlyRoles[mappedRole.id] = this.assignOnly;\n            }\n          });\n        });\n\n        if (this.assignOnly && !this.selectedRoles.length) {\n          // If we're assigning roles to a group that has no existing roles start with the default roles selected\n          this.selectDefaults();\n        }\n      }\n\n      // Force an update to pump out the initial state\n      this.checkboxChanged();\n    },\n    checkboxChanged() {\n      const addRoles = this.selectedRoles\n        .filter((selected) => !this.startingSelectedRoles.find((startingRole) => startingRole.roleId === selected));\n      const removeBindings = this.startingSelectedRoles\n        .filter((startingRole) => !this.selectedRoles.find((selected) => selected === startingRole.roleId))\n        .map((startingRole) => startingRole.bindingId);\n\n      this.roleChanges = {\n        initialRoles: this.startingSelectedRoles,\n        addRoles,\n        removeBindings\n      };\n\n      this.$emit('hasChanges', !!this.roleChanges.addRoles.length || !!this.roleChanges.removeBindings.length);\n      this.$emit('canLogIn', this.confirmUserCanLogIn());\n      this.$emit('changed', this.roleChanges);\n    },\n    async saveAddedRoles(userId) {\n      const requestOptions = {\n        type:     MANAGEMENT.GLOBAL_ROLE_BINDING,\n        metadata: { generateName: `grb-` },\n      };\n\n      if (this.groupPrincipalId) {\n        requestOptions.groupPrincipalName = this.groupPrincipalId;\n      } else {\n        requestOptions.userName = userId || this.userId;\n      }\n      const newBindings = await Promise.all(this.roleChanges.addRoles.map((role) => this.$store.dispatch(`management/create`, {\n        ...requestOptions,\n        globalRoleName: role,\n      })));\n\n      // Save all changes (and ensure user isn't logged out if they don't have permissions to make a change)\n      await Promise.all(newBindings.map((newBinding) => newBinding.save({ redirectUnauthorized: false })));\n    },\n    async saveRemovedRoles() {\n      const existingBindings = await Promise.all(this.roleChanges.removeBindings.map((bindingId) => this.$store.dispatch('management/find', {\n        type: MANAGEMENT.GLOBAL_ROLE_BINDING,\n        id:   bindingId\n      })));\n\n      // Save all changes (and ensure user isn't logged out if they don't have permissions to make a change)\n      await Promise.all(existingBindings.map((existingBinding) => existingBinding.remove({ redirectUnauthorized: false })));\n    },\n    /**\n     * userId is optional, used when a user has just been created\n     */\n    async save(userId) {\n      // Ensure roles are added before removed (in case by removing one user is unable to add another)\n      await this.saveAddedRoles(userId);\n      await this.saveRemovedRoles();\n      await this.$store.dispatch('management/findAll', {\n        type: MANAGEMENT.GLOBAL_ROLE_BINDING,\n        opt:  { force: true }\n      }, { force: true });\n    },\n    confirmUserCanLogIn() {\n      const allRolesRules = [];\n\n      Object.values(this.sortedRoles).forEach((roles) => {\n        roles.forEach((mappedRole) => {\n          if (this.selectedRoles.includes(mappedRole.id)) {\n            addObjects(allRolesRules, mappedRole.rules || []);\n          }\n        });\n      });\n\n      return allRolesRules.some((rule) => this.isRuleValid(rule));\n    },\n    isRuleValid(rule) {\n      // Brought over from Ember\n\n      if (( rule.resources || [] ).some(resourceValidator) && ( rule.apiGroups || [] ).some(apiGroupValidator) && verbsValidator(( rule.verbs || [] ))) {\n        return true;\n      }\n\n      return false;\n\n      function resourceValidator(resource) {\n        const resourcesRequiredForLogin = ['*', 'preferences', 'settings', 'features'];\n\n        // console.log(`resourceValidator status: `, resourcesRequiredForLogin.includes(resource), resource);\n        return resourcesRequiredForLogin.includes(resource);\n      }\n\n      function apiGroupValidator(apiGroup) {\n        const apiGroupsRequiredForLogin = ['*', 'management.cattle.io'];\n\n        // console.log(`apiGroupsRequiredForLogin status: `, apiGroupsRequiredForLogin.includes(apiGroup), apiGroup);\n        return apiGroupsRequiredForLogin.includes(apiGroup);\n      }\n\n      function verbsValidator(verbs) {\n        const restrictedTarget = ['get', 'list', 'watch'];\n        const verbsRequiredForLogin = ['*', ...restrictedTarget];\n\n        if (isArray(verbs) && verbs.length > 1) {\n          // console.log(`verbsRequiredForLogin status 1: `, restrictedTarget.every(rt => verbs.includes(rt)), verbs);\n          return restrictedTarget.every((rt) => verbs.includes(rt));\n        }\n\n        // console.log(`verbsRequiredForLogin status 2: `, verbsRequiredForLogin.includes(verbs[0]), verbsRequiredForLogin, verbs);\n        return verbsRequiredForLogin.includes(verbs[0]);\n      }\n    },\n\n  }\n};\n</script>\n\n<template>\n  <Loading v-if=\"$fetchState.pending\" />\n\n  <div v-else>\n    <form v-if=\"selectedRoles\">\n      <div\n        v-for=\"(sortedRole, roleType) in sortedRoles\"\n        :key=\"roleType\"\n        class=\"role-group mb-10\"\n      >\n        <Card\n          v-if=\"Object.keys(sortedRole).length\"\n          :show-highlight-border=\"false\"\n          :show-actions=\"false\"\n        >\n          <template v-slot:title>\n            <div class=\"type-title\">\n              <h3>{{ t(`rbac.globalRoles.types.${roleType}.label`) }}</h3>\n              <div class=\"type-description\">\n                {{ t(`rbac.globalRoles.types.${roleType}.description`, { isUser }) }}\n              </div>\n            </div>\n          </template>\n          <template v-slot:body>\n            <div\n              class=\"checkbox-section\"\n              :class=\"'checkbox-section--' + roleType\"\n            >\n              <div\n                v-for=\"(role, i) in sortedRoles[roleType]\"\n                :key=\"i\"\n                class=\"checkbox mb-10 mr-10\"\n              >\n                <Checkbox\n                  v-model:value=\"selectedRoles\"\n                  :value-when-true=\"role.id\"\n                  :disabled=\"!!assignOnlyRoles[role.id]\"\n                  :label=\"role.nameDisplay\"\n                  :description=\"role.descriptionDisplay\"\n                  :mode=\"mode\"\n                  :data-testId=\"'grb-checkbox-' + role.id\"\n                  @update:value=\"checkboxChanged\"\n                >\n                  <template #label>\n                    <div class=\"checkbox-label-slot\">\n                      <span class=\"checkbox-label\">{{ role.nameDisplay }}</span>\n                      <i\n                        v-if=\"!!assignOnlyRoles[role.id]\"\n                        v-clean-tooltip=\"t('rbac.globalRoles.assignOnlyRole')\"\n                        class=\"checkbox-info icon icon-info icon-lg\"\n                      />\n                    </div>\n                  </template>\n                </Checkbox>\n              </div>\n            </div>\n          </template>\n        </Card>\n      </div>\n    </form>\n  </div>\n</template>\n<style lang='scss'>\n.role-group {\n  .card-container {\n    margin: 0;\n  }\n}\n</style>\n<style lang='scss' scoped>\n  $detailSize: 11px;\n\n  .deprecation-notice {\n    margin: 8px 0 8px 20px;\n  }\n  .role-group {\n    .type-title {\n      display: flex;\n      flex-direction: column;\n      .type-description {\n        font-size: $detailSize;\n      }\n    }\n\n    .checkbox-section {\n      display: grid;\n\n      grid-template-columns: repeat(3, 1fr);\n\n      &--global {\n        grid-template-columns: 100%;\n      }\n\n      .checkbox-label {\n        &-slot {\n          display: inline-flex;\n          align-items: center;\n        }\n        color: var(--body-text);\n        margin: 0;\n      }\n    }\n  }\n</style>\n"]}]}