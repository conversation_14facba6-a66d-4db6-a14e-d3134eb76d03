{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CruResourceFooter.vue?vue&type=template&id=22ef885c", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CruResourceFooter.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdiBjbGFzcz0iY3J1LXJlc291cmNlLWZvb3RlciI+CiAgICA8c2xvdCBuYW1lPSJmb290ZXItcHJlZml4IiAvPgogICAgPHNsb3QgbmFtZT0iY2FuY2VsIj4KICAgICAgPGJ1dHRvbgogICAgICAgIHYtaWY9IiFpc1ZpZXcgJiYgc2hvd0NhbmNlbCIKICAgICAgICBpZD0iY3J1LWNhbmNlbCIKICAgICAgICA6ZGF0YS10ZXN0aWQ9ImNvbXBvbmVudFRlc3RpZCArICctY2FuY2VsJyIKICAgICAgICB0eXBlPSJidXR0b24iCiAgICAgICAgY2xhc3M9ImJ0biByb2xlLXNlY29uZGFyeSIKICAgICAgICBAY2xpY2s9ImNvbmZpcm1DYW5jZWxSZXF1aXJlZCA/IGNoZWNrQ2FuY2VsKHRydWUpIDogJGVtaXQoJ2NhbmNlbC1jb25maXJtZWQnLCB0cnVlKSIKICAgICAgPgogICAgICAgIDx0IGs9ImdlbmVyaWMuY2FuY2VsIiAvPgogICAgICA8L2J1dHRvbj4KICAgIDwvc2xvdD4KICAgIDxzbG90IDpjaGVja0NhbmNlbD0iY2hlY2tDYW5jZWwiPgogICAgICA8QXN5bmNCdXR0b24KICAgICAgICB2LWlmPSIhaXNWaWV3IgogICAgICAgIDpkYXRhLXRlc3RpZD0iY29tcG9uZW50VGVzdGlkICsgJy1jcmVhdGUnIgogICAgICAgIDptb2RlPSJmaW5pc2hCdXR0b25Nb2RlIHx8IG1vZGUiCiAgICAgICAgQGNsaWNrPSIkZW1pdCgnZmluaXNoJywgJGV2ZW50KSIKICAgICAgLz4KICAgIDwvc2xvdD4KICAgIDxSZXNvdXJjZUNhbmNlbE1vZGFsCiAgICAgIHJlZj0iY2FuY2VsTW9kYWwiCiAgICAgIDppcy1jYW5jZWwtbW9kYWw9ImlzQ2FuY2VsTW9kYWwiCiAgICAgIDppcy1mb3JtPSJpc0Zvcm0iCiAgICAgIEBjb25maXJtLWNhbmNlbD0iY29uZmlybUNhbmNlbCgkZXZlbnQpIgogICAgLz4KICA8L2Rpdj4K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CruResourceFooter.vue"], "names": [], "mappings": ";EAmFE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC5B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACrF;QACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC;EACH,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CruResourceFooter.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapGetters } from 'vuex';\n\nimport AsyncButton from '@shell/components/AsyncButton';\nimport ResourceCancelModal from '@shell/components/ResourceCancelModal';\nimport { _VIEW } from '@shell/config/query-params';\n\nexport default {\n  emits: ['cancel-confirmed', 'finish'],\n\n  components: { AsyncButton, ResourceCancelModal },\n  props:      {\n    mode: {\n      type:    String,\n      default: 'create',\n    },\n\n    isForm: {\n      type:    Boolean,\n      default: true,\n    },\n\n    // Override the set of labels shown on the button from the default save/create.\n    finishButtonMode: {\n      type:    String,\n      default: null,\n    },\n\n    confirmCancelRequired: {\n      type:    Boolean,\n      default: false,\n    },\n\n    confirmBackRequired: {\n      type:    Boolean,\n      default: true,\n    },\n\n    showCancel: {\n      type:    Boolean,\n      default: true\n    },\n\n    /**\n     * Inherited global identifier prefix for tests\n     * Define a term based on the parent component to avoid conflicts on multiple components\n     */\n    componentTestid: {\n      type:    String,\n      default: 'form-footer'\n    }\n  },\n\n  data() {\n    return { isCancelModal: false };\n  },\n\n  computed: {\n    ...mapGetters({ t: 'i18n/t' }),\n\n    isView() {\n      return this.mode === _VIEW;\n    },\n  },\n\n  methods: {\n    checkCancel(isCancel) {\n      if (isCancel) {\n        this.isCancelModal = true;\n      } else {\n        this.isCancelModal = false;\n      }\n      this.$refs.cancelModal.show();\n    },\n\n    confirmCancel(isCancel) {\n      this.$emit('cancel-confirmed', isCancel);\n    },\n  },\n};\n</script>\n\n<template>\n  <div class=\"cru-resource-footer\">\n    <slot name=\"footer-prefix\" />\n    <slot name=\"cancel\">\n      <button\n        v-if=\"!isView && showCancel\"\n        id=\"cru-cancel\"\n        :data-testid=\"componentTestid + '-cancel'\"\n        type=\"button\"\n        class=\"btn role-secondary\"\n        @click=\"confirmCancelRequired ? checkCancel(true) : $emit('cancel-confirmed', true)\"\n      >\n        <t k=\"generic.cancel\" />\n      </button>\n    </slot>\n    <slot :checkCancel=\"checkCancel\">\n      <AsyncButton\n        v-if=\"!isView\"\n        :data-testid=\"componentTestid + '-create'\"\n        :mode=\"finishButtonMode || mode\"\n        @click=\"$emit('finish', $event)\"\n      />\n    </slot>\n    <ResourceCancelModal\n      ref=\"cancelModal\"\n      :is-cancel-modal=\"isCancelModal\"\n      :is-form=\"isForm\"\n      @confirm-cancel=\"confirmCancel($event)\"\n    />\n  </div>\n</template>\n\n<style lang=\"scss\">\n.cru-resource-footer {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 20px;\n  z-index: z-index('cruFooter');\n\n  .btn {\n    margin-left: 20px;\n  }\n}\n\n</style>\n"]}]}