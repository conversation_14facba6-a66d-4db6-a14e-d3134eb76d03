{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/BannerSettings.vue?vue&type=style&index=0&id=66e8e727&scoped=true&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/BannerSettings.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5iYW5uZXItaW5wdXQtZm9vdG5vdGUgewogIGZvbnQtc2l6ZTogMTJweDsKICBvcGFjaXR5OiAwLjg7Cn0KCi5iYW5uZXItZGVjb3JhdGlvbi1jaGVja2JveCB7CiAgcG9zaXRpb246IHJlbGF0aXZlOwogIGRpc3BsYXk6IGlubGluZS1mbGV4OwogIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0OwogIG1hcmdpbjogMDsKICBjdXJzb3I6IHBvaW50ZXI7CiAgdXNlci1zZWxlY3Q6IG5vbmU7CiAgYm9yZGVyLXJhZGl1czogdmFyKC0tYm9yZGVyLXJhZGl1cyk7CiAgcGFkZGluZy1ib3R0b206IDVweDsKICBoZWlnaHQ6IDI0cHg7Cn0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/BannerSettings.vue"], "names": [], "mappings": ";AA2MA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACd;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACd", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/BannerSettings.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { Checkbox } from '@components/Form/Checkbox';\nimport ColorInput from '@shell/components/form/ColorInput';\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport { RadioGroup } from '@components/Form/Radio';\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\n\nexport default ({\n  name: 'BannerSettings',\n\n  props: {\n    value: {\n      type:    Object,\n      default: () => {}\n    },\n    mode: {\n      type: String,\n      validator(value) {\n        return [_EDIT, _VIEW].includes(value);\n      },\n      default: _EDIT,\n    },\n    bannerType: {\n      type:     String,\n      required: true\n    }\n  },\n\n  components: {\n    Checkbox, ColorInput, LabeledInput, LabeledSelect, RadioGroup\n  },\n\n  data() {\n    const showAsDialog = !!this.value[this.bannerType]?.button || false;\n    const buttonText = this.value[this.bannerType]?.button || this.t('banner.showAsDialog.defaultButtonText');\n\n    return {\n      showAsDialog,\n      buttonText,\n      uiBannerFontSizeOptions: ['10px', '12px', '14px', '16px', '18px', '20px'],\n      themeVars:               {\n        bannerBgColor:   getComputedStyle(document.body).getPropertyValue('--default'),\n        bannerTextColor: getComputedStyle(document.body).getPropertyValue('--banner-text-color')\n      }\n    };\n  },\n\n  watch: {\n    showAsDialog(neu, old) {\n      if (neu !== old && !neu) {\n        this.value[this.bannerType].button = '';\n      } else {\n        this.value[this.bannerType].button = this.buttonText;\n      }\n    },\n\n    buttonText(neu, old) {\n      if (neu !== old) {\n        this.value[this.bannerType].button = neu;\n      }\n    }\n  },\n\n  computed: {\n    radioOptions() {\n      const options = ['left', 'center', 'right'];\n      const labels = [\n        this.t('banner.bannerAlignment.leftOption'),\n        this.t('banner.bannerAlignment.centerOption'),\n        this.t('banner.bannerAlignment.rightOption'),\n      ];\n\n      return { options, labels };\n    },\n\n    isUiDisabled() {\n      return this.mode === _VIEW;\n    },\n\n    textDecorationOptions() {\n      const options = [\n        {\n          style: 'fontWeight',\n          label: this.t('banner.bannerDecoration.bannerBold')\n        },\n        {\n          style: 'fontStyle',\n          label: this.t('banner.bannerDecoration.bannerItalic')\n        },\n        {\n          style: 'textDecoration',\n          label: this.t('banner.bannerDecoration.bannerUnderline')\n        }\n      ];\n\n      return options;\n    },\n    isConsentBanner() {\n      return this.bannerType === 'bannerConsent';\n    }\n  }\n});\n</script>\n\n<template>\n  <div class=\"row mb-20\">\n    <div class=\"col span-12\">\n      <div class=\"row\">\n        <div class=\"col span-6\">\n          <LabeledInput\n            v-model:value=\"value[bannerType].text\"\n            :disabled=\"isUiDisabled\"\n            :label=\"t('banner.text')\"\n            type=\"multiline\"\n          />\n          <p\n            v-if=\"isConsentBanner\"\n            class=\"banner-input-footnote mt-5 mb-20\"\n          >\n            {{ t('banner.consentFootnote') }}\n          </p>\n          <div\n            v-if=\"isConsentBanner\"\n            class=\"mt-10\"\n          >\n            <Checkbox\n              v-model:value=\"showAsDialog\"\n              name=\"bannerDecoration\"\n              class=\"banner-decoration-checkbox\"\n              :mode=\"mode\"\n              :label=\"t('banner.showAsDialog.label')\"\n              :tooltip=\"t('banner.showAsDialog.tooltip')\"\n            />\n            <LabeledInput\n              v-model:value=\"buttonText\"\n              :disabled=\"!showAsDialog || isUiDisabled\"\n              :label=\"t('banner.buttonText')\"\n            />\n          </div>\n        </div>\n        <div class=\"col span-2\">\n          <RadioGroup\n            v-model:value=\"value[bannerType].textAlignment\"\n            name=\"bannerAlignment\"\n            :data-testid=\"`banner_alignment_radio_options${bannerType}`\"\n            :label=\"t('banner.bannerAlignment.label')\"\n            :options=\"radioOptions.options\"\n            :labels=\"radioOptions.labels\"\n            :mode=\"mode\"\n          />\n        </div>\n        <div class=\"col span-2\">\n          <h3>\n            {{ t('banner.bannerDecoration.label') }}\n          </h3>\n          <div\n            v-for=\"(o, i) in textDecorationOptions\"\n            :key=\"i\"\n          >\n            <Checkbox\n              v-model:value=\"value[bannerType][o.style]\"\n              name=\"bannerDecoration\"\n              :data-testid=\"`banner_decoration_checkbox${bannerType}${o.label}`\"\n              class=\"banner-decoration-checkbox\"\n              :mode=\"mode\"\n              :label=\"o.label\"\n            />\n          </div>\n        </div>\n        <div class=\"col span-2\">\n          <LabeledSelect\n            v-model:value=\"value[bannerType].fontSize\"\n            :data-testid=\"`banner_font_size_options${bannerType}`\"\n            :disabled=\"isUiDisabled\"\n            :label=\"t('banner.bannerFontSize.label')\"\n            :options=\"uiBannerFontSizeOptions\"\n          />\n        </div>\n      </div>\n      <div class=\"row mt-10\">\n        <div class=\"col span-6\">\n          <ColorInput\n            v-model:value=\"value[bannerType].color\"\n            :default-value=\"themeVars.bannerTextColor\"\n            :label=\"t('banner.textColor')\"\n            :mode=\"mode\"\n          />\n        </div>\n        <div class=\"col span-6\">\n          <ColorInput\n            v-model:value=\"value[bannerType].background\"\n            :default-value=\"themeVars.bannerBgColor\"\n            :label=\"t('banner.background')\"\n            :mode=\"mode\"\n          />\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style scoped lang='scss'>\n.banner-input-footnote {\n  font-size: 12px;\n  opacity: 0.8;\n}\n\n.banner-decoration-checkbox {\n  position: relative;\n  display: inline-flex;\n  align-items: flex-start;\n  margin: 0;\n  cursor: pointer;\n  user-select: none;\n  border-radius: var(--border-radius);\n  padding-bottom: 5px;\n  height: 24px;\n}\n</style>\n"]}]}