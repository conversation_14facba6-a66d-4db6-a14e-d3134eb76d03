{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ClusterIconMenu.vue?vue&type=template&id=3bd59edf&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ClusterIconMenu.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdgogICAgdi1pZj0iY2x1c3RlciIKICAgIGNsYXNzPSJjbHVzdGVyLWljb24tbWVudSIKICA+CiAgICA8ZGl2CiAgICAgIGNsYXNzPSJjbHVzdGVyLWJhZGdlLWxvZ28iCiAgICAgIDpjbGFzcz0ieyAnZGlzYWJsZWQnOiAhaXNFbmFibGVkIH0iCiAgICA+CiAgICAgIDxzcGFuCiAgICAgICAgdi1pZj0iIXNob3dMb2NhbEljb24iCiAgICAgICAgY2xhc3M9ImNsdXN0ZXItYmFkZ2UtbG9nby10ZXh0IgogICAgICA+CiAgICAgICAge3sgc21hbGxJZGVudGlmaWVyKGNsdXN0ZXIubGFiZWwpIH19CiAgICAgIDwvc3Bhbj4KICAgICAgPHNwYW4KICAgICAgICBjbGFzcz0iY3VzdG9tLWNvbG9yLWRlY29yYXRpb24iCiAgICAgICAgOnN0eWxlPSJ7J2JhY2tncm91bmQnOiBjdXN0b21Db2xvcn0iCiAgICAgIC8+CiAgICAgIDxzdmcKICAgICAgICB2LWlmPSJzaG93TG9jYWxJY29uIgogICAgICAgIGNsYXNzPSJjbHVzdGVyLWxvY2FsLWxvZ28iCiAgICAgICAgdmVyc2lvbj0iMS4xIgogICAgICAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgICAgICB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIKICAgICAgICB4PSIwcHgiCiAgICAgICAgeT0iMHB4IgogICAgICAgIHZpZXdCb3g9IjAgMCAxMDAgMTAwIgogICAgICAgIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDEwMCAxMDA7IgogICAgICAgIHhtbDpzcGFjZT0icHJlc2VydmUiCiAgICAgID4KICAgICAgICA8dGl0bGU+e3sgdCgnbmF2LmFyaWFMYWJlbC5sb2NhbENsdXN0ZXJJY29uJykgfX08L3RpdGxlPgogICAgICAgIDxnPgogICAgICAgICAgPGc+CiAgICAgICAgICAgIDxwYXRoCiAgICAgICAgICAgICAgY2xhc3M9InJhbmNoZXItaWNvbi1maWxsIgogICAgICAgICAgICAgIGQ9Ik0yNi4wODYyMDI2LDQ0LjQ5NTM5MThIOC42MTY1MTQyYy01LjU4MTgxNTcsMC05LjM5NzkxMzktNC42MjUyNzA4LTguNDgwMjYzNy0xMC4xMzExMDM1bDIuODU4MzkxLTE3LjIxMDcwMQogICAgICAgICAgICBDMy45MTIyOTIsMTEuNjQ3NzU1Niw2LjEzODI2NDcsNy4xMTI4MTI1LDcuODQxOTcwOSw3LjExMjgxMjVzMy4xNzg4NjExLDQuNTM2ODc1MiwzLjE3ODg2MTEsMTAuMTE4NjIxOHY0LjQ4Mzc3NDIKICAgICAgICAgICAgYzAsNS41ODE3NDcxLDQuNDA0NDQ5NSw5LjU0MDkxNjQsOS45ODYyNjUyLDkuNTQwOTE2NGg1LjA3OTEwNTRWNDQuNDk1MzkxOHoiCiAgICAgICAgICAgIC8+CiAgICAgICAgICA8L2c+CiAgICAgICAgICA8cGF0aAogICAgICAgICAgICBjbGFzcz0icmFuY2hlci1pY29uLWZpbGwiCiAgICAgICAgICAgIGQ9Ik02My4wMjE0NzI5LDkyLjg4NzE4NDFIMzcuMDg2MjA0NWMtNi4wNzUxMzQzLDAtMTEuMDAwMDAxOS00LjkyNDg2NTctMTEuMDAwMDAxOS0xMVYzMC4zODY0Mzg0CiAgICAgICAgICBjMC02LjA3NTEzMjQsNC45MjQ4Njc2LTExLDExLjAwMDAwMTktMTFoMjUuOTM1MjY4NGM2LjA3NTEzMDUsMCwxMS4wMDAwMDM4LDQuOTI0ODY3NiwxMS4wMDAwMDM4LDExdjUxLjUwMDc0NzcKICAgICAgICAgIEM3NC4wMjE0NzY3LDg3Ljk2MjMxODQsNjkuMDk2NjAzNCw5Mi44ODcxODQxLDYzLjAyMTQ3MjksOTIuODg3MTg0MXoiCiAgICAgICAgICAvPgogICAgICAgICAgPGc+CiAgICAgICAgICAgIDxwYXRoCiAgICAgICAgICAgICAgY2xhc3M9InJhbmNoZXItaWNvbi1maWxsIgogICAgICAgICAgICAgIGQ9Ik03My45MTM3OTU1LDQ0LjQ5NTM5MThoMTcuNDY5Njg4NGM1LjU4MTgxNzYsMCw5LjM5NzkxODctNC42MjUyNzA4LDguNDgwMjYyOC0xMC4xMzExMDM1CiAgICAgICAgICAgIGwtMi44NTgzOTA4LTE3LjIxMDcwMWMtMC45MTc2NDgzLTUuNTA1ODMxNy0zLjE0MzYyMzQtMTAuMDQwNzc1My00Ljg0NzMyODItMTAuMDQwNzc1MwogICAgICAgICAgICBzLTMuMTc4ODYzNSw0LjUzNjg3NTItMy4xNzg4NjM1LDEwLjExODYyMTh2NC40ODM3NzQyYzAsNS41ODE3NDcxLTQuNDA0NDQxOCw5LjU0MDkxNjQtOS45ODYyNTk1LDkuNTQwOTE2NGgtNS4wNzkxMDkyCiAgICAgICAgICAgIFY0NC40OTUzOTE4eiIKICAgICAgICAgICAgLz4KICAgICAgICAgIDwvZz4KICAgICAgICA8L2c+CiAgICAgIDwvc3ZnPgogICAgPC9kaXY+CiAgICA8aQogICAgICB2LWlmPSIhcm91dGVDb21ibyAmJiBjbHVzdGVyLnBpbm5lZCIKICAgICAgY2xhc3M9Imljb24gaWNvbi1waW4gY2x1c3Rlci1waW4taWNvbiIKICAgICAgOmFsdD0idCgnbmF2LmFyaWFMYWJlbC5waW5DbHVzdGVyJywgeyBjbHVzdGVyOiBjbHVzdGVyLm5hbWVEaXNwbGF5IH0pIgogICAgLz4KICAgIDxpCiAgICAgIHYtZWxzZS1pZj0icm91dGVDb21ibyIKICAgICAgY2xhc3M9Imljb24gaWNvbi1rZXlib2FyZF90YWIga2V5LWNvbWJvLWljb24iCiAgICAgIDphbHQ9InQoJ25hdi5hcmlhTGFiZWwuY2x1c3Rlckljb25LZXlDb21ibycpIgogICAgLz4KICA8L2Rpdj4K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ClusterIconMenu.vue"], "names": [], "mappings": ";EAgDE,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1B;IACE,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,<PERSON>AC,EAAE,CAAC;IACpC;MACE,CAAC,CAAC,CAAC,CAAC;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC;QACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC;MACD,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB;QACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD,CAAC,CAAC;UACA,CAAC,CAAC;YACA,CAAC,CAAC,CAAC,CAAC;cACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1E,CAAC;UACH,CAAC,CAAC,CAAC;UACH,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE,CAAC;UACD,CAAC,CAAC;YACA,CAAC,CAAC,CAAC,CAAC;cACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC;UACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC;MACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACvE,CAAC;IACD,CAAC;MACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9C,CAAC;EACH,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ClusterIconMenu.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { abbreviateClusterName } from '@shell/utils/cluster';\n\nexport default {\n  props: {\n    cluster: {\n      type:     Object,\n      required: true,\n    },\n    routeCombo: {\n      type:    Boolean,\n      default: false\n    },\n  },\n  computed: {\n    isEnabled() {\n      return !!this.cluster?.ready;\n    },\n    showLocalIcon() {\n      if (this.cluster.isLocal && this.cluster.removePreviewColor) {\n        return true;\n      }\n\n      return this.cluster.isLocal && !this.cluster.isHarvester && !this.cluster.badge?.iconText;\n    },\n\n    customColor() {\n      return this.cluster.iconColor || '';\n    },\n  },\n\n  methods: {\n    smallIdentifier(input) {\n      if (this.cluster.badge?.iconText) {\n        return this.cluster.badge?.iconText;\n      }\n\n      if (this.cluster.isLocal && !this.cluster.badge?.iconText) {\n        return undefined;\n      }\n\n      return abbreviateClusterName(input);\n    }\n  }\n};\n</script>\n\n<template>\n  <div\n    v-if=\"cluster\"\n    class=\"cluster-icon-menu\"\n  >\n    <div\n      class=\"cluster-badge-logo\"\n      :class=\"{ 'disabled': !isEnabled }\"\n    >\n      <span\n        v-if=\"!showLocalIcon\"\n        class=\"cluster-badge-logo-text\"\n      >\n        {{ smallIdentifier(cluster.label) }}\n      </span>\n      <span\n        class=\"custom-color-decoration\"\n        :style=\"{'background': customColor}\"\n      />\n      <svg\n        v-if=\"showLocalIcon\"\n        class=\"cluster-local-logo\"\n        version=\"1.1\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        xmlns:xlink=\"http://www.w3.org/1999/xlink\"\n        x=\"0px\"\n        y=\"0px\"\n        viewBox=\"0 0 100 100\"\n        style=\"enable-background:new 0 0 100 100;\"\n        xml:space=\"preserve\"\n      >\n        <title>{{ t('nav.ariaLabel.localClusterIcon') }}</title>\n        <g>\n          <g>\n            <path\n              class=\"rancher-icon-fill\"\n              d=\"M26.0862026,44.4953918H8.6165142c-5.5818157,0-9.3979139-4.6252708-8.4802637-10.1311035l2.858391-17.210701\n            C3.912292,11.6477556,6.1382647,7.1128125,7.8419709,7.1128125s3.1788611,4.5368752,3.1788611,10.1186218v4.4837742\n            c0,5.5817471,4.4044495,9.5409164,9.9862652,9.5409164h5.0791054V44.4953918z\"\n            />\n          </g>\n          <path\n            class=\"rancher-icon-fill\"\n            d=\"M63.0214729,92.8871841H37.0862045c-6.0751343,0-11.0000019-4.9248657-11.0000019-11V30.3864384\n          c0-6.0751324,4.9248676-11,11.0000019-11h25.9352684c6.0751305,0,11.0000038,4.9248676,11.0000038,11v51.5007477\n          C74.0214767,87.9623184,69.0966034,92.8871841,63.0214729,92.8871841z\"\n          />\n          <g>\n            <path\n              class=\"rancher-icon-fill\"\n              d=\"M73.9137955,44.4953918h17.4696884c5.5818176,0,9.3979187-4.6252708,8.4802628-10.1311035\n            l-2.8583908-17.210701c-0.9176483-5.5058317-3.1436234-10.0407753-4.8473282-10.0407753\n            s-3.1788635,4.5368752-3.1788635,10.1186218v4.4837742c0,5.5817471-4.4044418,9.5409164-9.9862595,9.5409164h-5.0791092\n            V44.4953918z\"\n            />\n          </g>\n        </g>\n      </svg>\n    </div>\n    <i\n      v-if=\"!routeCombo && cluster.pinned\"\n      class=\"icon icon-pin cluster-pin-icon\"\n      :alt=\"t('nav.ariaLabel.pinCluster', { cluster: cluster.nameDisplay })\"\n    />\n    <i\n      v-else-if=\"routeCombo\"\n      class=\"icon icon-keyboard_tab key-combo-icon\"\n      :alt=\"t('nav.ariaLabel.clusterIconKeyCombo')\"\n    />\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .rancher-icon-fill {\n    fill: var(--primary);\n  }\n\n  .cluster-icon-menu {\n    position: relative;\n    align-items: center;\n    display: flex;\n    height: 32px;\n    justify-content: center;\n    width: 42px;\n  }\n  .cluster-pin-icon {\n    position: absolute;\n    top: -6px;\n    right: -7px;\n    font-size: 14px;\n    transform: scaleX(-1);\n    color: var(--body-text);\n  }\n  .key-combo-icon {\n    position: absolute;\n    top: -7px;\n    right: -8px;\n    font-size: 14px;\n    color: var(--body-text);\n    background-color: #dddee6;\n    padding: 2px;\n    border-radius: 2px;\n  }\n\n  .cluster-local-logo {\n    width: 20px;\n  }\n\n  .cluster-badge-logo {\n    width: 42px;\n    height: 32px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    color: var(--default-active-text);\n    font-weight: bold;\n    background: var(--nav-icon-badge-bg);\n    border: 1px solid var(--border);\n    border-radius: 5px;\n    font-size: 12px;\n    text-transform: uppercase;\n\n    .custom-color-decoration {\n      height: 4px;\n      width: 100%;\n      margin: 0 auto;\n      position: absolute;\n      bottom: 0px;\n      border-radius: 0px 0px 5px 5px;\n    }\n\n    &.disabled {\n      color: var(--muted);\n    }\n  }\n</style>\n\n<style lang=\"scss\">\n  .theme-dark .key-combo-icon  {\n    color: var(--body-bg);\n  }\n</style>\n"]}]}