{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Config.vue?vue&type=template&id=40a6707b&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Config.vue", "mtime": 1754992537319}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Config.vue"], "names": [], "mappings": ";EAuRE,CAAC,CAAC,CAAC,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC;IACD,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;eAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB;MACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7D,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB;MACE,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB;QACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/C,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7C;QACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Config.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent, PropType } from 'vue';\nimport { EKSConfig, AWS } from '@/types';\nimport { _EDIT, _VIEW, _CREATE } from '@shell/config/query-params';\nimport semver from 'semver';\nimport { Store, mapGetters } from 'vuex';\nimport { sortable } from '@shell/utils/version';\nimport { sortBy } from '@shell/utils/sort';\n\nimport { MANAGEMENT } from '@shell/config/types';\nimport { SETTING } from '@shell/config/settings';\nimport RadioGroup from '@components/Form/Radio/RadioGroup.vue';\nimport Banner from '@components/Banner/Banner.vue';\n\nimport LabeledSelect from '@shell/components/form/LabeledSelect.vue';\nimport KeyValue from '@shell/components/form/KeyValue.vue';\nimport Checkbox from '@components/Form/Checkbox/Checkbox.vue';\nimport LabeledInput from '@components/Form/LabeledInput/LabeledInput.vue';\nimport eksVersions from '../../assets/data/eks-versions';\n\nexport default defineComponent({\n  name: 'EKSConfig',\n\n  emits: ['update:kmsKey', 'update:serviceRole', 'update:kubernetesVersion', 'update:enableNetworkPolicy', 'update:ebsCSIDriver', 'update:serviceRole', 'update:secretsEncryption', 'update:kmsKey', 'update:tags'],\n\n  components: {\n    LabeledSelect,\n    RadioGroup,\n    KeyValue,\n    Checkbox,\n    LabeledInput,\n    Banner\n  },\n\n  props: {\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n\n    isNewOrUnprovisioned: {\n      type:    Boolean,\n      default: true\n    },\n\n    loadingIam: {\n      type:    Boolean,\n      default: false\n    },\n\n    eksRoles: {\n      type:    Array,\n      default: () => []\n    },\n\n    tags: {\n      type:    Object,\n      default: () => {}\n    },\n\n    kmsKey: {\n      type:    String,\n      default: ''\n    },\n\n    secretsEncryption: {\n      type:    Boolean,\n      default: false\n    },\n\n    serviceRole: {\n      type:    String,\n      default: ''\n    },\n\n    kubernetesVersion: {\n      type:    String,\n      default: ''\n    },\n\n    enableNetworkPolicy: {\n      type:    Boolean,\n      default: false\n    },\n\n    ebsCSIDriver: {\n      type:    Boolean,\n      default: false\n    },\n\n    config: {\n      type:     Object as PropType<EKSConfig>,\n      required: true\n    },\n\n    originalVersion: {\n      type:    String,\n      default: ''\n    }\n  },\n\n  data() {\n    const store = this.$store as Store<any>;\n    // This setting is used by RKE1 AKS GKE and EKS - rke2/k3s have a different mechanism for fetching supported versions\n    const supportedVersionRange = store.getters['management/byId'](MANAGEMENT.SETTING, SETTING.UI_SUPPORTED_K8S_VERSIONS)?.value;\n    const t = store.getters['i18n/t'];\n\n    return {\n      kmsKeys:               [] as AWS.KmsKey[],\n      canReadKms:            false,\n      supportedVersionRange,\n      customServiceRole:     !!this.serviceRole && !!this.serviceRole.length,\n      loadingVersions:       false,\n      loadingKms:            false,\n      allKubernetesVersions: eksVersions as string[],\n      serviceRoleOptions:    [{ value: false, label: t('eks.serviceRole.options.standard') }, { value: true, label: t('eks.serviceRole.options.custom') }],\n\n    };\n  },\n\n  watch: {\n    'config.region': {\n      handler() {\n        if (this.mode !== _VIEW) {\n          this.fetchKubernetesVersions();\n          this.fetchKMSKeys();\n        }\n      },\n      immediate: true\n    },\n\n    'config.amazonCredentialSecret': {\n      handler() {\n        if (this.mode !== _VIEW) {\n          this.fetchKubernetesVersions();\n          this.fetchKMSKeys();\n        }\n      },\n      immediate: true\n    },\n\n    'secretsEncryption'(neu) {\n      if (!neu) {\n        this.$emit('update:kmsKey', '');\n      }\n    },\n\n    'customServiceRole'(neu) {\n      if (!neu) {\n        this.$emit('update:serviceRole', '');\n      }\n    },\n\n    versionOptions: {\n      handler(neu) {\n        if (neu && neu.length && !this.kubernetesVersion) {\n          this.$emit('update:kubernetesVersion', neu[0].value);\n        }\n      },\n      immediate: true\n    },\n\n  },\n\n  computed: {\n    ...mapGetters({ t: 'i18n/t' }),\n\n    // the control plane k8s version can't be more than one minor version ahead of any node pools\n    // verify that all nodepools are on the same version as the control plane before showing upgrade optiopns\n    canUpgrade(): boolean {\n      if (this.mode === _CREATE) {\n        return true;\n      }\n      const nodeGroups = this.config?.nodeGroups || [];\n\n      const needsUpgrade = nodeGroups.filter((group) => semver.gt(semver.coerce(this.originalVersion), semver.coerce(group.version)) || group._isUpgrading);\n\n      return !needsUpgrade.length;\n    },\n\n    hasUpgradesAvailable() {\n      return this.versionOptions.filter((opt) => !opt.disabled).length > 1;\n    },\n\n    versionOptions(): {value: string, label: string, sort?: string, disabled?:boolean}[] {\n      return this.allKubernetesVersions.reduce((versions, v: string) => {\n        const coerced = semver.coerce(v);\n\n        if (this.supportedVersionRange && !semver.satisfies(coerced, this.supportedVersionRange)) {\n          return versions;\n        }\n        if (!this.originalVersion) {\n          versions.push({ value: v, label: v });\n        } else if (semver.lte(semver.coerce(this.originalVersion), coerced)) {\n          const withinOneMinor = semver.inc(semver.coerce(this.originalVersion), 'minor');\n\n          if (semver.gt(coerced, withinOneMinor)) {\n            versions.push({\n              value: v, label: `${ v } ${ this.t('eks.version.upgradeWarning') }`, disabled: true\n            });\n          } else {\n            versions.push({ value: v, label: v });\n          }\n        }\n\n        // Generate sort field for each version\n        versions.forEach((v) => {\n          v.sort = sortable(v.value);\n        });\n\n        return sortBy(versions, 'sort', true);\n      }, [] as {value: string, label: string, sort?: string, disabled?:boolean}[]);\n    },\n\n    kmsOptions(): string[] {\n      return (this.kmsKeys || []).map((k) => k.KeyArn);\n    }\n  },\n\n  methods: {\n    // there is no api for fetching eks versions\n    // fetch addons and look at which versions they support\n    // this assumes that all k8s versions are compatible with at least one addon\n    async fetchKubernetesVersions() {\n      if (!this.config.region || !this.config.amazonCredentialSecret) {\n        return;\n      }\n      this.loadingVersions = true;\n      try {\n        const eksClient = await this.$store.dispatch('aws/eks', { region: this.config.region, cloudCredentialId: this.config.amazonCredentialSecret });\n        const addons = await this.$store.dispatch('aws/depaginateList', { client: eksClient, cmd: 'describeAddonVersions' });\n\n        if (!addons) {\n          return;\n        }\n        this.allKubernetesVersions = addons.reduce((versions: string[], addon: AWS.EKSAddon) => {\n          (addon?.addonVersions || []).forEach((addonVersion) => {\n            (addonVersion?.compatibilities || []).forEach((c) => {\n              if (!versions.includes(c.clusterVersion)) {\n                versions.push(c.clusterVersion);\n              }\n            });\n          });\n\n          return versions;\n        }, []);\n      } catch (err) {\n        // if the user doesn't have permission to describe addon versions swallow the error and use a fallback list of eks versions\n      }\n\n      this.loadingVersions = false;\n    },\n\n    async fetchKMSKeys() {\n      const { region, amazonCredentialSecret } = this.config;\n\n      if (!region || !amazonCredentialSecret) {\n        return;\n      }\n      this.loadingKms = true;\n      const store = this.$store as Store<any>;\n      const kmsClient = await store.dispatch('aws/kms', { region, cloudCredentialId: amazonCredentialSecret });\n\n      try {\n        this.kmsKeys = await this.$store.dispatch('aws/depaginateList', { client: kmsClient, cmd: 'listKeys' });\n\n        this.canReadKms = true;\n      } catch (e) {\n        this.canReadKms = false;\n      }\n      this.loadingKms = false;\n    },\n\n  }\n});\n\n</script>\n\n<template>\n  <div>\n    <Banner\n      v-if=\"!canUpgrade && hasUpgradesAvailable\"\n      color=\"info\"\n      label-key=\"eks.version.upgradeDisallowed\"\n      data-testid=\"eks-version-upgrade-disallowed-banner\"\n    />\n    <div\n      :style=\"{'display':'flex',\n               'align-items':'center'}\"\n      class=\"row mb-10\"\n    >\n      <div class=\"col span-6\">\n        <LabeledSelect\n          :value=\"kubernetesVersion\"\n          :options=\"versionOptions\"\n          label-key=\"eks.version.label\"\n          :mode=\"mode\"\n          :loading=\"loadingVersions\"\n          :taggable=\"true\"\n          :searchable=\"true\"\n          data-testid=\"eks-version-dropdown\"\n          :disabled=\"!canUpgrade && hasUpgradesAvailable\"\n          @update:value=\"$emit('update:kubernetesVersion', $event)\"\n        />\n      </div>\n      <div class=\"col span-3\">\n        <Checkbox\n          :mode=\"mode\"\n          label-key=\"eks.enableNetworkPolicy.label\"\n          :value=\"enableNetworkPolicy\"\n          :disabled=\"!isNewOrUnprovisioned\"\n          @update:value=\"$emit('update:enableNetworkPolicy', $event)\"\n        />\n      </div>\n      <div class=\"col span-3\">\n        <Checkbox\n          :mode=\"mode\"\n          label-key=\"eks.ebsCSIDriver.label\"\n          :value=\"ebsCSIDriver\"\n          :disabled=\"!isNewOrUnprovisioned\"\n          @update:value=\"$emit('update:ebsCSIDriver', $event)\"\n        />\n      </div>\n    </div>\n    <div class=\"row mb-10\">\n      <div class=\"col span-6\">\n        <RadioGroup\n          v-model:value=\"customServiceRole\"\n          :mode=\"mode\"\n          :options=\"serviceRoleOptions\"\n          name=\"serviceRoleMode\"\n          data-testid=\"eks-service-role-radio\"\n          :disabled=\"mode!=='create'\"\n        />\n      </div>\n      <div class=\"col span-6\">\n        <LabeledSelect\n          v-if=\"customServiceRole\"\n          :value=\"serviceRole\"\n          :mode=\"mode\"\n          :disabled=\"mode!=='create'\"\n          :options=\"eksRoles\"\n          option-label=\"RoleName\"\n          option-key=\"RoleId\"\n          label-key=\"eks.serviceRole.label\"\n          :loading=\"loadingIam\"\n          data-testid=\"eks-service-role-dropdown\"\n          @update:value=\"$emit('update:serviceRole', $event.RoleName)\"\n        />\n      </div>\n    </div>\n\n    <div class=\"row mb-10\">\n      <div class=\"col span-6\">\n        <Checkbox\n          :value=\"secretsEncryption\"\n          :disabled=\"mode!=='create'\"\n          :mode=\"mode\"\n          label-key=\"eks.encryptSecrets.label\"\n          data-testid=\"eks-secrets-encryption-checkbox\"\n          @update:value=\"$emit('update:secretsEncryption', $event)\"\n        />\n      </div>\n    </div>\n    <div\n      v-if=\"secretsEncryption\"\n      class=\"row mb-10\"\n    >\n      <div\n        class=\"col span-6\"\n      >\n        <LabeledSelect\n          v-if=\"canReadKms\"\n          :value=\"kmsKey\"\n          :mode=\"mode\"\n          :options=\"kmsOptions\"\n          :loading=\"loadingKms\"\n          :label=\"t('cluster.machineConfig.amazonEc2.kmsKey.label')\"\n          data-testid=\"eks-kms-dropdown\"\n          :disabled=\"mode!=='create'\"\n          @update:value=\"$emit('update:kmsKey', $event)\"\n        />\n        <template v-else>\n          <LabeledInput\n            :value=\"kmsKey\"\n            :mode=\"mode\"\n            :label=\"t('cluster.machineConfig.amazonEc2.kmsKey.label')\"\n            :tooltip=\"t('cluster.machineConfig.amazonEc2.kmsKey.text')\"\n            data-testid=\"eks-kms-input\"\n            :disabled=\"mode!=='create'\"\n            @update:value=\"$emit('update:kmsKey', $event)\"\n          />\n        </template>\n      </div>\n    </div>\n\n    <div class=\"col span-6 mt-20\">\n      <KeyValue\n        :value=\"tags\"\n        :mode=\"mode\"\n        :as-map=\"true\"\n        :read-allowed=\"false\"\n        @update:value=\"$emit('update:tags', $event)\"\n      >\n        <template #title>\n          <h3 v-t=\"'eks.tags.label'\" />\n        </template>\n      </KeyValue>\n    </div>\n  </div>\n</template>\n"]}]}