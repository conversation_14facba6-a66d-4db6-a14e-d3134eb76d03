{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/MoveModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/MoveModal.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/MoveModal.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE1D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpD,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EAChG,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7E,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7G,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAClD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC5G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC;QACJ;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,EAAE,CAAC,CAAC,CAAC;IACR;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACvB,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB;IACF;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;MAEtE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC;;MAEF,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf;IACF;EACF;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/MoveModal.vue", "sourceRoot": "", "sourcesContent": ["<script>\r\nimport { mapState, mapGetters } from 'vuex';\r\nimport { Card } from '@components/Card';\r\nimport AsyncButton from '@shell/components/AsyncButton';\r\nimport AppModal from '@shell/components/AppModal.vue';\r\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\r\nimport { MANAGEMENT } from '@shell/config/types';\r\nimport Loading from '@shell/components/Loading';\r\nimport { PROJECT } from '@shell/config/labels-annotations';\r\n\r\nexport default {\r\n  emits: ['moving'],\r\n\r\n  components: {\r\n    AsyncButton, Card, LabeledSelect, Loading, AppModal\r\n  },\r\n\r\n  async fetch() {\r\n    this.projects = await this.$store.dispatch('management/findAll', { type: MANAGEMENT.PROJECT });\r\n  },\r\n\r\n  data() {\r\n    return {\r\n      modalName: 'move-modal', projects: [], targetProject: null, showModal: false\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    ...mapState('action-menu', ['showPromptMove', 'toMove']),\r\n    ...mapGetters(['currentCluster']),\r\n\r\n    excludedProjects() {\r\n      return this.toMove.filter((namespace) => !!namespace.project).map((namespace) => namespace.project.shortId);\r\n    },\r\n\r\n    projectOptions() {\r\n      return this.projects.reduce((inCluster, project) => {\r\n        if (!this.excludedProjects.includes(project.shortId) && project.spec?.clusterName === this.currentCluster.id) {\r\n          inCluster.push({\r\n            value: project.shortId,\r\n            label: project.nameDisplay\r\n          });\r\n        }\r\n\r\n        return inCluster;\r\n      }, []);\r\n    }\r\n  },\r\n\r\n  watch: {\r\n    showPromptMove(show) {\r\n      if (show) {\r\n        this.showModal = true;\r\n      } else {\r\n        this.showModal = false;\r\n      }\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    close() {\r\n      this.$store.commit('action-menu/togglePromptMove');\r\n    },\r\n\r\n    async move(finish) {\r\n      const cluster = this.$store.getters['currentCluster'];\r\n      const clusterWithProjectId = `${ cluster.id }:${ this.targetProject }`;\r\n\r\n      const promises = this.toMove.map((namespace) => {\r\n        namespace.setLabel(PROJECT, this.targetProject);\r\n        namespace.setAnnotation(PROJECT, clusterWithProjectId);\r\n\r\n        return namespace.save();\r\n      });\r\n\r\n      try {\r\n        this.$emit('moving');\r\n        await Promise.all(promises);\r\n        finish(true);\r\n        this.targetProject = null;\r\n        this.close();\r\n      } catch (ex) {\r\n        finish(false);\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n<template>\r\n  <app-modal\r\n    v-if=\"showModal\"\r\n    class=\"move-modal\"\r\n    :name=\"modalName\"\r\n    :width=\"440\"\r\n    height=\"auto\"\r\n    @close=\"close\"\r\n  >\r\n    <Loading v-if=\"$fetchState.pending\" />\r\n    <Card\r\n      v-else\r\n      class=\"move-modal-card\"\r\n      :show-highlight-border=\"false\"\r\n    >\r\n      <template #title>\r\n        <h4 class=\"text-default-text\">\r\n          {{ t('moveModal.title') }}\r\n        </h4>\r\n      </template>\r\n      <template #body>\r\n        <div>\r\n          {{ t('moveModal.description') }}\r\n          <ul class=\"namespaces\">\r\n            <li\r\n              v-for=\"(namespace, i) in toMove\"\r\n              :key=\"i\"\r\n            >\r\n              {{ namespace.nameDisplay }}\r\n            </li>\r\n          </ul>\r\n        </div>\r\n        <LabeledSelect\r\n          v-model:value=\"targetProject\"\r\n          :options=\"projectOptions\"\r\n          :label=\"t('moveModal.targetProject')\"\r\n        />\r\n      </template>\r\n      <template #actions>\r\n        <button\r\n          class=\"btn role-secondary\"\r\n          @click=\"close\"\r\n        >\r\n          {{ t('generic.cancel') }}\r\n        </button>\r\n        <AsyncButton\r\n          :action-label=\"t('moveModal.moveButtonLabel')\"\r\n          class=\"btn bg-primary ml-10\"\r\n          :disabled=\"!targetProject\"\r\n          @click=\"move\"\r\n        />\r\n      </template>\r\n    </Card>\r\n  </app-modal>\r\n</template>\r\n\r\n<style lang='scss'>\r\n  .move-modal {\r\n    .namespaces {\r\n      max-height: 200px;\r\n      overflow-y: scroll;\r\n    }\r\n\r\n    .move-modal-card {\r\n        box-shadow: none;\r\n\r\n        border-radius: var(--border-radius);\r\n    }\r\n\r\n    .actions {\r\n      text-align: right;\r\n    }\r\n    .card-actions {\r\n      display: flex;\r\n      justify-content: center;\r\n    }\r\n  }\r\n</style>\r\n"]}]}