{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/NotificationSettings.vue?vue&type=style&index=0&id=2db95b34&scoped=true&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/NotificationSettings.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5iYW5uZXItZGVjb3JhdGlvbi1jaGVja2JveCB7CiAgcG9zaXRpb246IHJlbGF0aXZlOwogIGRpc3BsYXk6IGlubGluZS1mbGV4OwogIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0OwogIG1hcmdpbjogMDsKICBjdXJzb3I6IHBvaW50ZXI7CiAgdXNlci1zZWxlY3Q6IG5vbmU7CiAgYm9yZGVyLXJhZGl1czogdmFyKC0tYm9yZGVyLXJhZGl1cyk7CiAgcGFkZGluZy1ib3R0b206IDVweDsKICBoZWlnaHQ6IDI0cHg7Cn0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/NotificationSettings.vue"], "names": [], "mappings": ";AA2DA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACd", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/NotificationSettings.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport { Checkbox } from '@components/Form/Checkbox';\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\n\nexport default ({\n\n  name: 'NotificationSettings',\n\n  components: { LabeledInput, Checkbox },\n\n  props: {\n    value: {\n      type:    Object,\n      default: () => {}\n    },\n\n    mode: {\n      type: String,\n      validator(value) {\n        return [_EDIT, _VIEW].includes(value);\n      },\n      default: _EDIT,\n    }\n  },\n\n});\n</script>\n\n<template>\n  <div>\n    <div class=\"row mb-20\">\n      <div class=\"col span-6\">\n        <Checkbox\n          :mode=\"mode\"\n          :value=\"value.showMessage === 'true'\"\n          :label=\"t('notifications.loginError.showCheckboxLabel')\"\n          @update:value=\"value.showMessage = $event.toString()\"\n        />\n      </div>\n    </div>\n    <div class=\"row mb-20\">\n      <div class=\"col span-6\">\n        <div class=\"row\">\n          <div class=\"col span-12\">\n            <LabeledInput\n              v-model:value=\"value.message\"\n              :mode=\"mode\"\n              :disabled=\"value.showMessage === 'false'\"\n              :label=\"t('notifications.loginError.messageLabel')\"\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style scoped lang='scss'>\n.banner-decoration-checkbox {\n  position: relative;\n  display: inline-flex;\n  align-items: flex-start;\n  margin: 0;\n  cursor: pointer;\n  user-select: none;\n  border-radius: var(--border-radius);\n  padding-bottom: 5px;\n  height: 24px;\n}\n</style>\n"]}]}