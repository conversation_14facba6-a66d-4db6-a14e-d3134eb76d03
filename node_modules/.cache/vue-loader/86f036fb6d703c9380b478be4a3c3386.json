{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Members/ProjectMembershipEditor.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Members/ProjectMembershipEditor.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgeyBOT1JNQU4gfSBmcm9tICdAc2hlbGwvY29uZmlnL3R5cGVzJzsNCmltcG9ydCB7IF9DUkVBVEUsIF9WSUVXIH0gZnJvbSAnQHNoZWxsL2NvbmZpZy9xdWVyeS1wYXJhbXMnOw0KaW1wb3J0IE1lbWJlcnNoaXBFZGl0b3IsIHsgY2FuVmlld01lbWJlcnNoaXBFZGl0b3IgfSBmcm9tICdAc2hlbGwvY29tcG9uZW50cy9mb3JtL01lbWJlcnMvTWVtYmVyc2hpcEVkaXRvcic7DQoNCmV4cG9ydCBmdW5jdGlvbiBjYW5WaWV3UHJvamVjdE1lbWJlcnNoaXBFZGl0b3Ioc3RvcmUpIHsNCiAgcmV0dXJuIGNhblZpZXdNZW1iZXJzaGlwRWRpdG9yKHN0b3JlLCB0cnVlKTsNCn0NCg0KZXhwb3J0IGRlZmF1bHQgew0KICBjb21wb25lbnRzOiB7IE1lbWJlcnNoaXBFZGl0b3IgfSwNCg0KICBwcm9wczogew0KICAgIHBhcmVudElkOiB7DQogICAgICB0eXBlOiAgICBTdHJpbmcsDQogICAgICBkZWZhdWx0OiBudWxsDQogICAgfSwNCg0KICAgIG1vZGU6IHsNCiAgICAgIHR5cGU6ICAgICBTdHJpbmcsDQogICAgICByZXF1aXJlZDogdHJ1ZQ0KICAgIH0NCiAgfSwNCg0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBOT1JNQU4sIGJpbmRpbmdzOiBbXSwgbGFzdFNhdmVkQmluZGluZ3M6IFtdDQogICAgfTsNCiAgfSwNCg0KICBjb21wdXRlZDogew0KICAgIGlzQ3JlYXRlKCkgew0KICAgICAgcmV0dXJuIHRoaXMubW9kZSA9PT0gX0NSRUFURTsNCiAgICB9LA0KDQogICAgaXNWaWV3KCkgew0KICAgICAgcmV0dXJuIHRoaXMubW9kZSA9PT0gX1ZJRVc7DQogICAgfQ0KICB9LA0KDQogIG1ldGhvZHM6IHsNCiAgICBkZWZhdWx0QmluZGluZ0hhbmRsZXIoKSB7DQogICAgICByZXR1cm4gdGhpcy4kc3RvcmUuZGlzcGF0Y2goYG1hbmFnZW1lbnQvY3JlYXRlYCwgew0KICAgICAgICB0eXBlOiAgICAgICAgICAgIE5PUk1BTi5QUk9KRUNUX1JPTEVfVEVNUExBVEVfQklORElORywNCiAgICAgICAgcm9sZVRlbXBsYXRlSWQ6ICAncHJvamVjdC1vd25lcicsDQogICAgICAgIHVzZXJQcmluY2lwYWxJZDogdGhpcy4kc3RvcmUuZ2V0dGVyc1snYXV0aC9wcmluY2lwYWxJZCddLA0KICAgICAgfSk7DQogICAgfQ0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Members/ProjectMembershipEditor.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE3G,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACpD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;EAEhC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC5C,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC/C,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1D,CAAC,CAAC;IACJ;EACF;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Members/ProjectMembershipEditor.vue", "sourceRoot": "", "sourcesContent": ["<script>\r\nimport { NORMAN } from '@shell/config/types';\r\nimport { _CREATE, _VIEW } from '@shell/config/query-params';\r\nimport MembershipEditor, { canViewMembershipEditor } from '@shell/components/form/Members/MembershipEditor';\r\n\r\nexport function canViewProjectMembershipEditor(store) {\r\n  return canViewMembershipEditor(store, true);\r\n}\r\n\r\nexport default {\r\n  components: { MembershipEditor },\r\n\r\n  props: {\r\n    parentId: {\r\n      type:    String,\r\n      default: null\r\n    },\r\n\r\n    mode: {\r\n      type:     String,\r\n      required: true\r\n    }\r\n  },\r\n\r\n  data() {\r\n    return {\r\n      NORMAN, bindings: [], lastSavedBindings: []\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    isCreate() {\r\n      return this.mode === _CREATE;\r\n    },\r\n\r\n    isView() {\r\n      return this.mode === _VIEW;\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    defaultBindingHandler() {\r\n      return this.$store.dispatch(`management/create`, {\r\n        type:            NORMAN.PROJECT_ROLE_TEMPLATE_BINDING,\r\n        roleTemplateId:  'project-owner',\r\n        userPrincipalId: this.$store.getters['auth/principalId'],\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n<template>\r\n  <MembershipEditor\r\n    ref=\"editor\"\r\n    add-member-dialog-name=\"AddProjectMemberDialog\"\r\n    :modal-sticky=\"true\"\r\n    :default-binding-handler=\"defaultBindingHandler\"\r\n    :type=\"NORMAN.PROJECT_ROLE_TEMPLATE_BINDING\"\r\n    :mode=\"mode\"\r\n    parent-key=\"projectId\"\r\n    :parent-id=\"parentId\"\n  />\r\n</template>\r\n"]}]}