{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CountGauge.vue?vue&type=style&index=0&id=587369ed&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CountGauge.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgICAuemVybyB7CiAgICAgIGNpcmNsZSB7CiAgICAgICAgc3Ryb2tlOiB2YXIoLS1nYXVnZS16ZXJvKTsKICAgICAgfQogICAgfQogICAgLmNvdW50LWdhdWdlIHsKICAgICAgICAkcGFkZGluZzogMTBweDsKCiAgICAgICAgcGFkZGluZzogJHBhZGRpbmc7CiAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlOwogICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgZmxleC1kaXJlY3Rpb246IHJvdzsKICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOwoKICAgICAgICAmLmNsaWNrYWJsZSB7CiAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICAgICAgfQoKICAgICAgICAuZGF0YSB7CiAgICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICAgICAgICAgIGZsZXg6IDE7CgogICAgICAgICAgbGFiZWwgewogICAgICAgICAgICBvcGFjaXR5OiAwLjc7CiAgICAgICAgICB9CiAgICAgICAgfQoKICAgICAgICAuZ3JhcGhpY2FsIHsKICAgICAgICAgICRzaXplOiA0MHB4OwogICAgICAgICAgd2lkdGg6ICRzaXplOwogICAgICAgICAgaGVpZ2h0OiAkc2l6ZTsKICAgICAgICAgIG1hcmdpbi1yaWdodDogJHBhZGRpbmc7CiAgICAgICAgfQoKICAgICAgICBoMSB7CiAgICAgICAgICBmb250LXNpemU6IDQwcHg7CiAgICAgICAgICBsaW5lLWhlaWdodDogMzZweDsKICAgICAgICAgIHBhZGRpbmctYm90dG9tOiBtYXRoLmRpdigkcGFkZGluZywgMik7CiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAwOwogICAgICAgIH0KCiAgICAgICAgQG1lZGlhIG9ubHkgc2NyZWVuIGFuZCAobWluLXdpZHRoOiBtYXAtZ2V0KCRicmVha3BvaW50cywgJy0tdmlld3BvcnQtNycpKSB7CiAgICAgICAgICBoMSB7CiAgICAgICAgICAgIGZvbnQtc2l6ZTogNDBweDsKICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDM2cHg7CiAgICAgICAgICB9CiAgICAgICAgfQoKICAgICAgICAuYWxlcnRzIHsKICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlOwogICAgICAgICAgICByaWdodDogJHBhZGRpbmc7CiAgICAgICAgICAgIHRvcDogbWF0aC5kaXYoJHBhZGRpbmcsIDIpOwogICAgICAgICAgICBmb250LXNpemU6IDE1cHg7CgogICAgICAgICAgICAudGV4dC1lcnJvciB7CiAgICAgICAgICAgICAgbWFyZ2luLWxlZnQ6IDVweDsKICAgICAgICAgICAgfQogICAgICAgIH0KICAgIH0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CountGauge.vue"], "names": [], "mappings": ";IAuHI,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B;IACF;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB;;QAEA,CAAC,CAAC,CAAC,CAAC,EAAE;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;UAEP,CAAC,CAAC,CAAC,CAAC,EAAE;YACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACd;QACF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB;;QAEA,CAAC,EAAE;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAClB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACxE,CAAC,EAAE;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACnB;QACF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;YAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAClB;QACJ;IACJ", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CountGauge.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport GraphCircle from '@shell/components/graph/Circle';\nimport GradientBox from '@shell/components/GradientBox';\n\nexport default {\n  components: { GradientBox, GraphCircle },\n  props:      {\n    name: {\n      type:     String,\n      required: true\n    },\n    total: {\n      type:     Number,\n      required: true\n    },\n    useful: {\n      type:     Number,\n      required: true\n    },\n    primaryColorVar: {\n      type:    String,\n      default: null,\n    },\n    warningCount: {\n      type:    Number,\n      default: 0\n    },\n    errorCount: {\n      type:    Number,\n      default: 0\n    },\n    location: {\n      type:    Object,\n      default: null\n    },\n    plain: {\n      type:    Boolean,\n      default: false\n    },\n    graphical: {\n      type:    Boolean,\n      default: true\n    }\n  },\n\n  computed: {\n    percentage() {\n      if (this.total === 0) {\n        return 0;\n      }\n\n      return this.useful / this.total;\n    },\n    clickable() {\n      return !!this.location;\n    },\n    showAlerts() {\n      const total = this.warningCount + this.errorCount;\n\n      return total > 0;\n    }\n  },\n  methods: {\n    visitLocation() {\n      if (!this.clickable) {\n        return;\n      }\n\n      this.$router.push(this.location);\n    }\n  }\n};\n</script>\n\n<template>\n  <GradientBox\n    class=\"count-gauge\"\n    :class=\"{clickable}\"\n    :primary-color-var=\"primaryColorVar\"\n    :plain=\"plain\"\n    @click=\"visitLocation()\"\n  >\n    <div\n      v-if=\"graphical\"\n      class=\"graphical\"\n    >\n      <GraphCircle\n        v-if=\"percentage > 0\"\n        :primary-stroke-color=\"`rgba(var(${primaryColorVar}))`\"\n        secondary-stroke-color=\"rgb(var(--resource-gauge-back-circle))\"\n        :percentage=\"percentage\"\n      />\n      <GraphCircle\n        v-if=\"percentage === 0\"\n        :primary-stroke-color=\"`rgba(var(${primaryColorVar}))`\"\n        secondary-stroke-color=\"rgb(var(--resource-gauge-back-circle))\"\n        class=\"zero\"\n        :percentage=\"100\"\n      />\n    </div>\n    <div class=\"data\">\n      <h1>{{ useful }}</h1>\n      <label>{{ name }}</label>\n      <div\n        v-if=\"showAlerts\"\n        class=\"alerts\"\n      >\n        <span class=\"text-warning\">\n          <i class=\"icon icon-warning\" /><span class=\"count\">{{ warningCount }}</span>\n        </span>\n        <span class=\"text-error\">\n          <i class=\"icon icon-error\" /><span class=\"count\">{{ errorCount }}</span>\n        </span>\n      </div>\n    </div>\n  </GradientBox>\n</template>\n\n<style lang=\"scss\">\n    .zero {\n      circle {\n        stroke: var(--gauge-zero);\n      }\n    }\n    .count-gauge {\n        $padding: 10px;\n\n        padding: $padding;\n        position: relative;\n        display: flex;\n        flex-direction: row;\n        align-items: center;\n\n        &.clickable {\n          cursor: pointer;\n        }\n\n        .data {\n          display: flex;\n          flex-direction: column;\n          flex: 1;\n\n          label {\n            opacity: 0.7;\n          }\n        }\n\n        .graphical {\n          $size: 40px;\n          width: $size;\n          height: $size;\n          margin-right: $padding;\n        }\n\n        h1 {\n          font-size: 40px;\n          line-height: 36px;\n          padding-bottom: math.div($padding, 2);\n          margin-bottom: 0;\n        }\n\n        @media only screen and (min-width: map-get($breakpoints, '--viewport-7')) {\n          h1 {\n            font-size: 40px;\n            line-height: 36px;\n          }\n        }\n\n        .alerts {\n            position: absolute;\n            right: $padding;\n            top: math.div($padding, 2);\n            font-size: 15px;\n\n            .text-error {\n              margin-left: 5px;\n            }\n        }\n    }\n</style>\n"]}]}