{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Members/MembershipEditor.vue?vue&type=template&id=192d741e&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Members/MembershipEditor.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Members/MembershipEditor.vue"], "names": [], "mappings": ";EAmKE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB;IACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACxB;UACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB;QACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB;QACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Members/MembershipEditor.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { MANAGEMENT, NORMAN } from '@shell/config/types';\nimport ArrayList from '@shell/components/form/ArrayList';\nimport Principal from '@shell/components/auth/Principal';\nimport Loading from '@shell/components/Loading';\nimport { _CREATE, _VIEW } from '@shell/config/query-params';\nimport { get, set } from '@shell/utils/object';\n\nfunction normalizeId(id) {\n  return id?.replace(':', '/') || id;\n}\n\nexport function canViewMembershipEditor(store, needsProject = false) {\n  return (!!store.getters['management/schemaFor'](MANAGEMENT.PROJECT_ROLE_TEMPLATE_BINDING) || !needsProject) &&\n    !!store.getters['management/schemaFor'](MANAGEMENT.ROLE_TEMPLATE) &&\n    !!store.getters['rancher/schemaFor'](NORMAN.PRINCIPAL);\n}\n\nexport default {\n  emits: ['membership-update'],\n\n  components: {\n    ArrayList, Loading, Principal\n  },\n\n  props: {\n    addMemberDialogName: {\n      type:     String,\n      required: true\n    },\n\n    parentKey: {\n      type:     String,\n      required: true\n    },\n\n    parentId: {\n      type:    String,\n      default: null\n    },\n\n    mode: {\n      type:     String,\n      required: true\n    },\n\n    type: {\n      type:     String,\n      required: true\n    },\n\n    defaultBindingHandler: {\n      type:    Function,\n      default: null,\n    },\n\n    modalSticky: {\n      type:    Boolean,\n      default: false,\n    }\n  },\n\n  async fetch() {\n    const roleBindingRequestParams = { type: this.type, opt: { force: true } };\n\n    if (this.type === NORMAN.PROJECT_ROLE_TEMPLATE_BINDING && this.parentId) {\n      Object.assign(roleBindingRequestParams, { opt: { filter: { projectId: this.parentId.split('/').join(':') }, force: true } });\n    }\n    const userHydration = [\n      this.schema ? this.$store.dispatch(`rancher/findAll`, roleBindingRequestParams) : [],\n      this.$store.dispatch('rancher/findAll', { type: NORMAN.PRINCIPAL }),\n      this.$store.dispatch(`management/findAll`, { type: MANAGEMENT.ROLE_TEMPLATE }),\n      this.$store.dispatch(`management/findAll`, { type: MANAGEMENT.USER })\n    ];\n    const [allBindings] = await Promise.all(userHydration);\n\n    const bindings = allBindings\n      .filter((b) => normalizeId(get(b, this.parentKey)) === normalizeId(this.parentId));\n\n    this['lastSavedBindings'] = [...bindings];\n\n    // Add the current user as the project owner. This will get created by default\n    if (this.mode === _CREATE && bindings.length === 0 && this.defaultBindingHandler) {\n      const defaultBinding = await this.defaultBindingHandler();\n\n      defaultBinding.isDefaultBinding = true;\n      bindings.push(defaultBinding);\n    }\n\n    this['bindings'] = bindings;\n  },\n\n  data() {\n    return {\n      schema:            this.$store.getters[`rancher/schemaFor`](this.type),\n      bindings:          [],\n      lastSavedBindings: [],\n    };\n  },\n\n  computed: {\n    newBindings() {\n      return this.bindings\n        .filter((binding) => !binding.id && !this.lastSavedBindings.includes(binding) && !binding.isDefaultBinding);\n    },\n    removedBindings() {\n      return this.lastSavedBindings\n        .filter((binding) => !this.bindings.includes(binding));\n    },\n    membershipUpdate() {\n      const newBindings = this.newBindings;\n      const removedBindings = this.removedBindings;\n\n      return {\n        newBindings:     this.newBindings,\n        removedBindings: this.removedBindings,\n        save:            (parentId) => {\n          const savedPromises = newBindings.map((binding) => {\n            set(binding, this.parentKey, parentId);\n\n            return binding.save();\n          });\n\n          const removedPromises = removedBindings.map((binding) => binding.remove());\n\n          return Promise.all([...savedPromises, ...removedPromises]);\n        }\n      };\n    },\n\n    isCreate() {\n      return this.mode === _CREATE;\n    },\n\n    isView() {\n      return this.mode === _VIEW;\n    },\n  },\n  watch: {\n    membershipUpdate: {\n      deep: true,\n      handler() {\n        this.$emit('membership-update', this.membershipUpdate);\n      }\n    }\n  },\n\n  methods: {\n    addMember() {\n      this.$store.dispatch('cluster/promptModal', {\n        component:      this.addMemberDialogName,\n        componentProps: { onAdd: this.onAddMember },\n        modalSticky:    this.modalSticky\n      });\n    },\n\n    onAddMember(bindings) {\n      this['bindings'] = [...this.bindings, ...bindings];\n    },\n  }\n};\n</script>\n<template>\n  <Loading v-if=\"$fetchState.pending\" />\n  <ArrayList\n    v-else\n    v-model:value=\"bindings\"\n    :mode=\"mode\"\n    :show-header=\"true\"\n  >\n    <template #column-headers>\n      <div class=\"box mb-0\">\n        <div class=\"column-headers row\">\n          <div class=\"col span-6\">\n            <label class=\"text-label\">{{ t('membershipEditor.user') }}</label>\n          </div>\n          <div class=\"col span-6\">\n            <label class=\"text-label\">{{ t('membershipEditor.role') }}</label>\n          </div>\n        </div>\n      </div>\n    </template>\n    <template #columns=\"{row, i}\">\n      <div class=\"columns row\">\n        <div class=\"col span-6\">\n          <Principal\n            :value=\"row.value.principalId\"\n          />\n        </div>\n        <div\n          :data-testid=\"`role-item-${i}`\"\n          class=\"col span-6 role\"\n        >\n          {{ row.value.roleDisplay }}\n        </div>\n      </div>\n    </template>\n    <template #add>\n      <button\n        type=\"button\"\n        class=\"btn role-primary mt-10\"\n        data-testid=\"add-item\"\n        @click=\"addMember\"\n      >\n        {{ t('generic.add') }}\n      </button>\n    </template>\n    <template #remove-button=\"{remove, i}\">\n      <span v-if=\"(isCreate && i === 0) || isView\" />\n      <button\n        v-else\n        type=\"button\"\n        :disabled=\"isView\"\n        class=\"btn role-link\"\n        :data-testid=\"`remove-item-${i}`\"\n        @click=\"remove\"\n      >\n        {{ t('generic.remove') }}\n      </button>\n    </template>\n  </ArrayList>\n</template>\n\n<style lang=\"scss\" scoped>\n.role {\n  display: flex;\n  align-items: center;\n  flex-direction: row;\n}\n</style>\n"]}]}