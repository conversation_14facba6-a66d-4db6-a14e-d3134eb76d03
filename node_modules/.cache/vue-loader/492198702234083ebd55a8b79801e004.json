{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/index.vue?vue&type=style&index=0&id=f9b48e2a&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/index.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLmlucHV0LWtub3duLXNzaC1ob3N0cyB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwoKICAgIC5ob3N0cy1pbnB1dCB7CiAgICAgIGN1cnNvcjogZGVmYXVsdDsKICAgICAgbGluZS1oZWlnaHQ6IGNhbGMoMThweCArIDFweCk7CiAgICAgIHBhZGRpbmc6IDE4cHggMCAwIDA7CiAgICB9CgogICAgLnNob3ctZGlhbG9nLWJ0biB7CiAgICAgIGRpc3BsYXk6IGNvbnRlbnRzOwogICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB0cmFuc3BhcmVudDsKICAgIH0KICB9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/index.vue"], "names": [], "mappings": ";EAqFE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC;IACrB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/index.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent } from 'vue';\nimport KnownHostsEditDialog from './KnownHostsEditDialog.vue';\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\n\nexport default defineComponent({\n  name: 'SSHKnownHosts',\n\n  emits: ['update:value'],\n\n  props: {\n    value: {\n      type:     String,\n      required: true\n    },\n\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n  },\n\n  components: { KnownHostsEditDialog },\n\n  computed: {\n    isViewMode() {\n      return this.mode === _VIEW;\n    },\n\n    // The number of entries - exclude empty lines and comments\n    entries() {\n      return this.value.split('\\n').filter((line: string) => !!line.trim().length && !line.startsWith('#')).length;\n    },\n\n    summary() {\n      return this.t('secret.ssh.editKnownHosts.entries', { entries: this.entries });\n    }\n  },\n\n  methods: {\n    openDialog() {\n      (this.$refs.button as HTMLInputElement)?.blur();\n      (this.$refs.editDialog as any).showDialog();\n    },\n\n    dialogClosed(result: any) {\n      if (result.success) {\n        this.$emit('update:value', result.value);\n      }\n    }\n  }\n});\n</script>\n<template>\n  <div\n    class=\"input-known-ssh-hosts labeled-input\"\n    data-testid=\"input-known-ssh-hosts\"\n  >\n    <label>{{ t('secret.ssh.knownHosts') }}</label>\n    <div\n      class=\"hosts-input\"\n      data-testid=\"input-known-ssh-hosts_summary\"\n    >\n      {{ summary }}\n    </div>\n    <template v-if=\"!isViewMode\">\n      <button\n        ref=\"button\"\n        data-testid=\"input-known-ssh-hosts_open-dialog\"\n        class=\"show-dialog-btn btn\"\n        @click=\"openDialog\"\n      >\n        <i class=\"icon icon-edit\" />\n      </button>\n\n      <KnownHostsEditDialog\n        ref=\"editDialog\"\n        :value=\"value\"\n        :mode=\"mode\"\n        @closed=\"dialogClosed\"\n      />\n    </template>\n  </div>\n</template>\n<style lang=\"scss\" scoped>\n  .input-known-ssh-hosts {\n    display: flex;\n    justify-content: space-between;\n\n    .hosts-input {\n      cursor: default;\n      line-height: calc(18px + 1px);\n      padding: 18px 0 0 0;\n    }\n\n    .show-dialog-btn {\n      display: contents;\n      background-color: transparent;\n    }\n  }\n</style>\n"]}]}