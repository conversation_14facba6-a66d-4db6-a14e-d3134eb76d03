{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceList/Masthead.vue?vue&type=style&index=0&id=ab874436&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceList/Masthead.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLnRpdGxlIHsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgaDEgewogICAgICBtYXJnaW46IDA7CiAgICB9CiAgfQoKICBoZWFkZXIgewogICAgbWFyZ2luLWJvdHRvbTogMjBweDsKICB9CgogIGhlYWRlci53aXRoLXN1YmhlYWRlciB7CiAgICBncmlkLXRlbXBsYXRlLWFyZWFzOgogICAgICAndHlwZS1iYW5uZXIgdHlwZS1iYW5uZXInCiAgICAgICd0aXRsZSBhY3Rpb25zJwogICAgICAnc3ViLWhlYWRlciBzdWItaGVhZGVyJwogICAgICAnc3RhdGUtYmFubmVyIHN0YXRlLWJhbm5lcic7CiAgfQoKICAuc3ViLWhlYWRlciB7CiAgICBncmlkLWFyZWE6IHN1Yi1oZWFkZXI7CgogICAgYSB7CiAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICAgIH0KICB9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceList/Masthead.vue"], "names": [], "mappings": ";EA0NE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACX;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/B;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAErB,EAAE;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceList/Masthead.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapGetters } from 'vuex';\nimport Favorite from '@shell/components/nav/Favorite';\nimport TypeDescription from '@shell/components/TypeDescription';\nimport { get } from '@shell/utils/object';\nimport { AS, _YAML } from '@shell/config/query-params';\nimport ResourceLoadingIndicator from './ResourceLoadingIndicator';\nimport TabTitle from '@shell/components/TabTitle';\n\n/**\n * Resource List Masthead component.\n */\nexport default {\n\n  name: 'MastheadResourceList',\n\n  components: {\n    Favorite,\n    TypeDescription,\n    ResourceLoadingIndicator,\n    TabTitle\n  },\n  props: {\n    resource: {\n      type:     String,\n      required: true,\n    },\n    favoriteResource: {\n      type:    String,\n      default: null\n    },\n    schema: {\n      type:    Object,\n      default: null,\n    },\n    typeDisplay: {\n      type:    String,\n      default: null,\n    },\n    isCreatable: {\n      type:    Boolean,\n      default: null,\n    },\n    isYamlCreatable: {\n      type:    Boolean,\n      default: null,\n    },\n    createLocation: {\n      type:    Object,\n      default: null,\n    },\n    yamlCreateLocation: {\n      type:    Object,\n      default: null,\n    },\n    createButtonLabel: {\n      type:    String,\n      default: null\n    },\n    loadResources: {\n      type:    Array,\n      default: () => []\n    },\n\n    loadIndeterminate: {\n      type:    Boolean,\n      default: false\n    },\n\n    showIncrementalLoadingIndicator: {\n      type:    Boolean,\n      default: false\n    },\n\n    /**\n     * Inherited global identifier prefix for tests\n     * Define a term based on the parent component to avoid conflicts on multiple components\n     */\n    componentTestid: {\n      type:    String,\n      default: 'masthead'\n    }\n  },\n\n  data() {\n    const params = { ...this.$route.params };\n\n    const formRoute = { name: `${ this.$route.name }-create`, params };\n\n    const hasEditComponent = this.$store.getters['type-map/hasCustomEdit'](this.resource);\n\n    const yamlRoute = {\n      name:  `${ this.$route.name }-create`,\n      params,\n      query: { [AS]: _YAML },\n    };\n\n    return {\n      formRoute,\n      yamlRoute,\n      hasEditComponent,\n    };\n  },\n\n  computed: {\n    get,\n    ...mapGetters(['isExplorer', 'currentCluster']),\n\n    resourceName() {\n      if (this.schema) {\n        return this.$store.getters['type-map/labelFor'](this.schema);\n      }\n\n      return this.resource;\n    },\n\n    _typeDisplay() {\n      if ( this.typeDisplay !== null) {\n        return this.typeDisplay;\n      }\n\n      if ( !this.schema ) {\n        return '?';\n      }\n\n      return this.$store.getters['type-map/labelFor'](this.schema, 99);\n    },\n\n    _isYamlCreatable() {\n      if ( this.isYamlCreatable !== null) {\n        return this.isYamlCreatable;\n      }\n\n      return this.schema && this._isCreatable && this.$store.getters['type-map/optionsFor'](this.resource).canYaml;\n    },\n\n    _isCreatable() {\n      // Does not take into account hasEditComponent, such that _isYamlCreatable works\n      if ( this.isCreatable !== null) {\n        return this.isCreatable;\n      }\n\n      // blocked-post means you can post through norman, but not through steve.\n      if ( this.schema && !this.schema?.collectionMethods.find((x) => ['blocked-post', 'post'].includes(x.toLowerCase())) ) {\n        return false;\n      }\n\n      return this.$store.getters['type-map/optionsFor'](this.resource).isCreatable;\n    },\n\n    _createLocation() {\n      return this.createLocation || this.formRoute;\n    },\n\n    _yamlCreateLocation() {\n      return this.yamlCreateLocation || this.yamlRoute;\n    },\n\n    _createButtonlabel() {\n      return this.createButtonLabel || this.t('resourceList.head.create');\n    },\n  }\n};\n</script>\n\n<template>\n  <header class=\"with-subheader\">\n    <slot name=\"typeDescription\">\n      <TypeDescription :resource=\"resource\" />\n    </slot>\n    <div class=\"title\">\n      <h1 class=\"m-0\">\n        <TabTitle>{{ _typeDisplay }}</TabTitle> <Favorite\n          v-if=\"isExplorer\"\n          :resource=\"favoriteResource || resource\"\n        />\n      </h1>\n      <ResourceLoadingIndicator\n        v-if=\"showIncrementalLoadingIndicator\"\n        :resources=\"loadResources\"\n        :indeterminate=\"loadIndeterminate\"\n      />\n    </div>\n    <div class=\"sub-header\">\n      <slot name=\"subHeader\">\n        <!--Slot content-->\n      </slot>\n    </div>\n    <div class=\"actions-container\">\n      <slot name=\"actions\">\n        <div class=\"actions\">\n          <slot name=\"extraActions\" />\n\n          <slot name=\"createButton\">\n            <router-link\n              v-if=\"hasEditComponent && _isCreatable\"\n              :to=\"_createLocation\"\n              class=\"btn role-primary\"\n              :data-testid=\"componentTestid+'-create'\"\n            >\n              {{ _createButtonlabel }}\n            </router-link>\n            <router-link\n              v-else-if=\"_isYamlCreatable\"\n              :to=\"_yamlCreateLocation\"\n              class=\"btn role-primary\"\n              :data-testid=\"componentTestid+'-create-yaml'\"\n            >\n              {{ t(\"resourceList.head.createFromYaml\") }}\n            </router-link>\n          </slot>\n        </div>\n      </slot>\n    </div>\n  </header>\n</template>\n\n<style lang=\"scss\" scoped>\n  .title {\n    align-items: center;\n    display: flex;\n    h1 {\n      margin: 0;\n    }\n  }\n\n  header {\n    margin-bottom: 20px;\n  }\n\n  header.with-subheader {\n    grid-template-areas:\n      'type-banner type-banner'\n      'title actions'\n      'sub-header sub-header'\n      'state-banner state-banner';\n  }\n\n  .sub-header {\n    grid-area: sub-header;\n\n    a {\n      display: inline-block;\n    }\n  }\n</style>\n"]}]}