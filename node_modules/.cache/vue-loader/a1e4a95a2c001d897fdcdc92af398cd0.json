{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ButtonDropdown.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ButtonDropdown.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ButtonDropdown.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEnE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAChB,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE;UACT,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YACnF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACd;QACF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAC3B,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAC7C,CAAC,CAAC;OACD,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;OACtD,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;OAClD,CAAC;MACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE1C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpD,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C;;MAEA,CAAC,CAAC;OACD,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OACxE;OACA,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;OACvE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;OACrC;OACA,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;OACpE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;OACvE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;OACP,CAAC;MACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT;YACE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;UAC9B,CAAC;UACD;YACE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5D,CAAC;UACH,CAAC;QACH,CAAC;MACH,CAAC,CAAC;;MAEF,CAAC,CAAC;OACD,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OACtD,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;OACpF,CAAC;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrE,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC;MACF,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEjD,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ;MACF,CAAC,CAAC;IACJ,CAAC;IACD,CAAC,CAAC,CAAC;EACL,CAAC;AACH,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ButtonDropdown.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { createPopper } from '@popperjs/core';\nimport { get } from '@shell/utils/object';\nimport isString from 'lodash/isString';\nimport VueSelectOverrides from '@shell/mixins/vue-select-overrides';\n\nexport default {\n  emits: ['dd-button-action', 'click-action'],\n\n  mixins: [VueSelectOverrides],\n  props:  {\n    buttonLabel: {\n      default: '',\n      type:    String,\n    },\n    closeOnSelect: {\n      default: true,\n      type:    Boolean\n    },\n    disabled: {\n      default: false,\n      type:    Boolean,\n    },\n    // array of option objects containing at least a label and link, but also icon and action are available\n    dropdownOptions: {\n      // required: true,\n      default: () => [],\n      type:    Array,\n    },\n    optionKey: {\n      default: null,\n      type:    String,\n    },\n    optionLabel: {\n      default: 'label',\n      type:    String,\n    },\n    // sm, null(med), lg - no xs...its so small\n    size: {\n      default: null,\n      type:    String,\n    },\n    value: {\n      default: null,\n      type:    String,\n    },\n    placement: {\n      default: 'bottom-start',\n      type:    String\n    },\n    selectable: {\n      default: (opt) => {\n        if ( opt ) {\n          if ( opt.disabled || opt.kind === 'group' || opt.kind === 'divider' || opt.loading ) {\n            return false;\n          }\n        }\n\n        return true;\n      },\n      type: Function\n    },\n  },\n  data() {\n    return { focused: false };\n  },\n\n  methods: {\n    withPopper(dropdownList, component, { width }) {\n      /**\n       * We need to explicitly define the dropdown width since\n       * it is usually inherited from the parent with CSS.\n       */\n      const componentWidth = component.$refs.search.clientWidth;\n      const dropWidth = dropdownList.clientWidth;\n\n      if (dropWidth < componentWidth) {\n        dropdownList.style.width = `${ componentWidth }px`;\n      } else {\n        dropdownList.style.width = 'min-content';\n      }\n\n      /**\n       * Here we position the dropdownList relative to the $refs.toggle Element.\n       *\n       * The 'offset' modifier aligns the dropdown so that the $refs.toggle and\n       * the dropdownList overlap by 1 pixel.\n       *\n       * The 'toggleClass' modifier adds a 'drop-up' class to the Vue Select\n       * wrapper so that we can set some styles for when the dropdown is placed\n       * above.\n       */\n      const popper = createPopper(component.$refs.toggle, dropdownList, {\n        placement: this.placement || 'bottom-start',\n        modifiers: [\n          {\n            name:    'offset',\n            options: { offset: [-2, 2] },\n          },\n          {\n            name:    'toggleClass',\n            enabled: true,\n            phase:   'write',\n            fn({ state }) {\n              component.$el.setAttribute('x-placement', state.placement);\n            },\n          },\n        ],\n      });\n\n      /**\n       * To prevent memory leaks Popper needs to be destroyed.\n       * If you return function, it will be called just before dropdown is removed from DOM.\n       */\n      return () => popper.destroy();\n    },\n    ddButtonAction(option) {\n      this.focusSearch();\n      this.$emit('dd-button-action', option);\n    },\n    getOptionLabel(option) {\n      if (isString(option)) {\n        return option;\n      }\n\n      if (this.$attrs['get-option-label']) {\n        return this.$attrs['get-option-label'](option);\n      }\n\n      if (get(option, this.optionLabel)) {\n        if (this.localizedLabel) {\n          return this.$store.getters['i18n/t'](get(option, this.optionLabel));\n        } else {\n          return get(option, this.optionLabel);\n        }\n      } else {\n        return option;\n      }\n    },\n\n    onFocus() {\n      return this.onFocusLabeled();\n    },\n\n    onFocusLabeled() {\n      this.focused = true;\n    },\n\n    onBlur() {\n      return this.onBlurLabeled();\n    },\n\n    onBlurLabeled() {\n      this.focused = false;\n    },\n\n    focusSearch() {\n      this.$nextTick(() => {\n        const el = this.$refs['button-dropdown'].searchEl;\n\n        if ( el ) {\n          el.focus();\n        }\n      });\n    },\n    get,\n  },\n};\n</script>\n\n<template>\n  <v-select\n    ref=\"button-dropdown\"\n    class=\"button-dropdown btn\"\n    :class=\"{\n      disabled,\n      focused,\n    }\"\n    v-bind=\"$attrs\"\n    :append-to-body=\"true\"\n    :calculate-position=\"withPopper\"\n    :searchable=\"false\"\n    :clearable=\"false\"\n    :close-on-select=\"closeOnSelect\"\n    :filterable=\"false\"\n    :modelValue=\"buttonLabel\"\n    :options=\"dropdownOptions\"\n    :map-keydown=\"mappedKeys\"\n    :get-option-key=\"\n      (opt) => (optionKey ? get(opt, optionKey) : getOptionLabel(opt))\n    \"\n    :get-option-label=\"(opt) => getOptionLabel(opt)\"\n    :selectable=\"selectable\"\n    @search:blur=\"onBlur\"\n    @search:focus=\"onFocus\"\n    @update:modelValue=\"$emit('click-action', $event)\"\n  >\n    <template #no-options>\n      <slot name=\"no-options\" />\n    </template>\n\n    <template #selected-option=\"option\">\n      <button\n        tabindex=\"-1\"\n        type=\"button\"\n        class=\"dropdown-button-two btn\"\n        data-testid=\"dropdown-button\"\n        @click=\"ddButtonAction(option)\"\n        @focus=\"focusSearch\"\n      >\n        {{ option.label }}\n      </button>\n    </template>\n    <!-- Pass down templates provided by the caller -->\n    <template\n      v-for=\"(_, slot) of $slots\"\n      #[slot]=\"scope\"\n      :key=\"slot\"\n    >\n      <template v-if=\"slot !== 'selected-option' && typeof $slots[slot] === 'function'\">\n        <slot\n          :name=\"slot\"\n          v-bind=\"scope\"\n        />\n      </template>\n    </template>\n  </v-select>\n</template>\n\n<style lang='scss' scoped>\n.button-dropdown.btn-sm {\n  :deep() > .vs__dropdown-toggle {\n    .vs__actions {\n      &:after {\n        font-size: 1.6rem;\n      }\n    }\n  }\n}\n.button-dropdown.btn-lg {\n  :deep() > .vs__dropdown-toggle {\n    .vs__actions {\n      &:after {\n        font-size: 2.6rem;\n      }\n    }\n  }\n}\n.button-dropdown {\n  background: var(--accent-btn);\n  border: solid 1px var(--link);\n  color: var(--link);\n  padding: 0;\n\n  &.vs--open :deep() {\n    outline: none;\n    box-shadow: none;\n  }\n\n  &:hover {\n    :deep() .vs__dropdown-toggle .vs__actions,\n    :deep() .vs__selected-options {\n      background: var(--accent-btn-hover);\n    }\n    :deep() .vs__selected-options .vs__selected button {\n      background-color: transparent;\n      color: var(--accent-btn-hover-text);\n    }\n    :deep() .vs__dropdown-toggle .vs__actions {\n      &:after {\n        color: var(--accent-btn-hover-text);\n      }\n    }\n  }\n\n  :deep() > .vs__dropdown-toggle {\n    width: 100%;\n    display: grid;\n    grid-template-columns: 75% 25%;\n    border: none;\n    background: transparent;\n\n    .vs__actions {\n\n      &:after {\n        color: var(--link);\n        line-height: 1;\n      }\n    }\n  }\n\n  :deep() .vs__selected-options {\n    .vs__selected {\n      margin: unset;\n      border: none;\n\n      button {\n        border: none;\n        background: transparent;\n        color: var(--link);\n      }\n    }\n    .vs__search {\n      // if you need to keep the dd open you can toggle these on and off\n      // display: none;\n      // visibility: hidden;\n      position: absolute;\n      opacity: 0;\n      padding: 0;\n    }\n  }\n\n  :deep() .vs__dropdown-menu {\n    min-width: unset;\n    width: fit-content;\n  }\n}\n</style>\n"]}]}