{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ModalWithCard.vue?vue&type=template&id=4c410e60&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ModalWithCard.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGFwcC1tb2RhbAogICAgOm5hbWU9Im5hbWUiCiAgICA6d2lkdGg9IndpZHRoIgogICAgOmNsaWNrLXRvLWNsb3NlPSJmYWxzZSIKICAgIDpoZWlnaHQ9ImhlaWdodCIKICAgIHYtYmluZD0iJGF0dHJzIgogICAgY2xhc3M9Im1vZGFsIgogICAgZGF0YS10ZXN0aWQ9Im12Y19fY2FyZCIKICAgIEBjbG9zZT0iJGVtaXQoJ2ZpbmlzaCcsICRldmVudCkiCiAgPgogICAgPENhcmQKICAgICAgY2xhc3M9Im1vZGFsIgogICAgICA6c2hvdy1oaWdobGlnaHQtYm9yZGVyPSJmYWxzZSIKICAgID4KICAgICAgPHRlbXBsYXRlICN0aXRsZT4KICAgICAgICA8aDQgY2xhc3M9InRleHQtZGVmYXVsdC10ZXh0Ij4KICAgICAgICAgIDxzbG90IG5hbWU9InRpdGxlIiAvPgogICAgICAgIDwvaDQ+CiAgICAgIDwvdGVtcGxhdGU+CgogICAgICA8dGVtcGxhdGUgI2JvZHk+CiAgICAgICAgPHNsb3QgbmFtZT0iY29udGVudCIgLz4KCiAgICAgICAgPGRpdgogICAgICAgICAgdi1mb3I9IihlcnIsaWR4KSBpbiBlcnJvcnMiCiAgICAgICAgICA6a2V5PSJpZHgiCiAgICAgICAgPgogICAgICAgICAgPEJhbm5lcgogICAgICAgICAgICBjbGFzcz0iYmFubmVyIgogICAgICAgICAgICBjb2xvcj0iZXJyb3IiCiAgICAgICAgICAgIDpsYWJlbD0iZXJyIgogICAgICAgICAgLz4KICAgICAgICA8L2Rpdj4KICAgICAgPC90ZW1wbGF0ZT4KCiAgICAgIDx0ZW1wbGF0ZSAjYWN0aW9ucz4KICAgICAgICA8c2xvdCBuYW1lPSJmb290ZXIiPgogICAgICAgICAgPGRpdiBjbGFzcz0iZm9vdGVyIj4KICAgICAgICAgICAgPGJ1dHRvbgogICAgICAgICAgICAgIGNsYXNzPSJidG4gcm9sZS1zZWNvbmRhcnkgbXItMjAiCiAgICAgICAgICAgICAgQGNsaWNrLnByZXZlbnQ9ImhpZGUiCiAgICAgICAgICAgID4KICAgICAgICAgICAgICB7eyBjbG9zZVRleHQgfX0KICAgICAgICAgICAgPC9idXR0b24+CgogICAgICAgICAgICA8QXN5bmNCdXR0b24KICAgICAgICAgICAgICA6bW9kZT0ic2F2ZVRleHQiCiAgICAgICAgICAgICAgQGNsaWNrPSIkZW1pdCgnZmluaXNoJywgJGV2ZW50KSIKICAgICAgICAgICAgLz4KICAgICAgICAgIDwvZGl2PgogICAgICAgIDwvc2xvdD4KICAgICAgPC90ZW1wbGF0ZT4KICAgIDwvQ2FyZD4KICA8L2FwcC1tb2RhbD4K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ModalWithCard.vue"], "names": [], "mappings": ";EA2DE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjC;IACE,CAAC,CAAC,CAAC,CAAC;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B;MACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;QAEtB,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB;cACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ModalWithCard.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { Card } from '@components/Card';\nimport { Banner } from '@components/Banner';\nimport AsyncButton from '@shell/components/AsyncButton';\nimport AppModal from '@shell/components/AppModal.vue';\n\nexport default {\n  name: 'ModalWithCard',\n\n  emits: ['close', 'finish'],\n\n  components: {\n    Card, Banner, AsyncButton, AppModal\n  },\n\n  props: {\n    name: {\n      type:     String,\n      required: true\n    },\n\n    closeText: {\n      type:    String,\n      default: 'Close'\n    },\n\n    saveText: {\n      type:    String,\n      default: 'create'\n    },\n\n    width: {\n      type:    [String, Number],\n      default: '50%'\n    },\n\n    height: {\n      type:    [String, Number],\n      default: 'auto'\n    },\n\n    errors: {\n      type:    Array,\n      default: () => {\n        return [];\n      }\n    }\n  },\n\n  methods: {\n    hide() {\n      this.$emit('close');\n    },\n  }\n};\n\n</script>\n\n<template>\n  <app-modal\n    :name=\"name\"\n    :width=\"width\"\n    :click-to-close=\"false\"\n    :height=\"height\"\n    v-bind=\"$attrs\"\n    class=\"modal\"\n    data-testid=\"mvc__card\"\n    @close=\"$emit('finish', $event)\"\n  >\n    <Card\n      class=\"modal\"\n      :show-highlight-border=\"false\"\n    >\n      <template #title>\n        <h4 class=\"text-default-text\">\n          <slot name=\"title\" />\n        </h4>\n      </template>\n\n      <template #body>\n        <slot name=\"content\" />\n\n        <div\n          v-for=\"(err,idx) in errors\"\n          :key=\"idx\"\n        >\n          <Banner\n            class=\"banner\"\n            color=\"error\"\n            :label=\"err\"\n          />\n        </div>\n      </template>\n\n      <template #actions>\n        <slot name=\"footer\">\n          <div class=\"footer\">\n            <button\n              class=\"btn role-secondary mr-20\"\n              @click.prevent=\"hide\"\n            >\n              {{ closeText }}\n            </button>\n\n            <AsyncButton\n              :mode=\"saveText\"\n              @click=\"$emit('finish', $event)\"\n            />\n          </div>\n        </slot>\n      </template>\n    </Card>\n  </app-modal>\n</template>\n\n<style lang=\"scss\" scoped>\n.footer {\n  width: 100%;\n  display: flex;\n  justify-content: center;\n}\n\n.banner {\n  margin-bottom: 0px;\n}\n</style>\n\n<style lang=\"scss\">\n.modal {\n  border-radius: var(--border-radius);\n  max-height: 100vh;\n\n  &.card-container {\n    box-shadow: none;\n  }\n}\n</style>\n"]}]}