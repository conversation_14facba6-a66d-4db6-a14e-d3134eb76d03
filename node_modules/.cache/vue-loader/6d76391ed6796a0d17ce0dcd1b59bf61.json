{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ShellInput.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ShellInput.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCB7IExhYmVsZWRJbnB1dCB9IGZyb20gJ0Bjb21wb25lbnRzL0Zvcm0vTGFiZWxlZElucHV0JzsKCmV4cG9ydCBkZWZhdWx0IHsKICBlbWl0czogWyd1cGRhdGU6dmFsdWUnXSwKCiAgY29tcG9uZW50czogeyBMYWJlbGVkSW5wdXQgfSwKCiAgcHJvcHM6IHsKICAgIHZhbHVlOiB7CiAgICAgIHR5cGU6ICAgIEFycmF5LAogICAgICBkZWZhdWx0OiBudWxsLAogICAgfQogIH0sCiAgLyoKICAgICAgdXNlclZhbHVlIGlzIGEgc3RyaW5nIHJlcHJlc2VudGF0aW9uIG9mIGFyZ3MgYXJyYXksIHdpdGggc3BhY2VzIGJldHdlZW4gZWFjaCBhcnJheSBpdGVtIGFuZCBzaW5nbGUgcXVvdGVzIGFyb3VuZCBhbnkgaXRlbXMgd2l0aCB3aGl0ZXNwYWNlCiAgICAgIHZhbHVlIGlucHV0IG9mIFsiLWMiLCAic2xlZXAgNjAwIl0KICAgICAgaXMgZGlzcGxheWVkIGFzOiAiLWMgJ3NsZWVwIDYwMCciCgogICAgICB1c2VyIGlucHV0IG9mICItYyAic2xlZXAgNjAwIiIgb3IgIi1jICdzbGVlcCA2MDAnIgogICAgICBjYXVzZXMgJGVtaXQgJ2lucHV0JyBvZiBbIi1jIiwgInNsZWVwIDYwMCJdCiAgKi8KICBkYXRhKCkgewogICAgbGV0IHVzZXJWYWx1ZSA9ICcnOwoKICAgIGlmICggdGhpcy52YWx1ZSApIHsKICAgICAgdXNlclZhbHVlID0gdGhpcy52YWx1ZS5yZWR1Y2UoKHN0ciwgZWFjaCkgPT4gewogICAgICAgIGlmIChlYWNoLmluY2x1ZGVzKCcgJykpIHsKICAgICAgICAgIHN0ciArPSBgJyR7IGVhY2ggfSdgOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBzdHIgKz0gZWFjaDsKICAgICAgICB9CiAgICAgICAgc3RyICs9ICcgJzsKCiAgICAgICAgcmV0dXJuIHN0cjsKICAgICAgfSwgJycpLnRyaW0oKTsKICAgIH0KCiAgICByZXR1cm4geyB1c2VyVmFsdWUgfTsKICB9LAoKICBtZXRob2RzOiB7CiAgICB1cGRhdGUodXNlclZhbHVlKSB7CiAgICAgIGxldCBvdXQgPSBudWxsOwoKICAgICAgaWYgKCB1c2VyVmFsdWUgKSB7CiAgICAgICAgb3V0ID0gdXNlclZhbHVlLm1hdGNoKC8oJ1teJ10rJyl8KCJbXiJdKyIpfFxTKy9nKS5tYXAoKHN0cmluZykgPT4gc3RyaW5nLnJlcGxhY2UoL14nfCckfF4ifCIkL2csICcnKSk7CiAgICAgIH0KICAgICAgdGhpcy4kZW1pdCgndXBkYXRlOnZhbHVlJywgb3V0KTsKICAgIH0sCiAgfQp9OwoKZXhwb3J0IGNvbnN0IE9QUyA9IFsnfHwnLCAnJiYnLCAnOzsnLCAnfCYnLCAnJicsICc7JywgJygnLCAnKScsICd8JywgJzwnLCAnPiddOwpleHBvcnQgZnVuY3Rpb24gcmVvcCh4cykgewogIHJldHVybiB4cy5tYXAoKHMpID0+IHsKICAgIGlmICggT1BTLmluY2x1ZGVzKHMpICkgewogICAgICByZXR1cm4geyBvcDogcyB9OwogICAgfSBlbHNlIHsKICAgICAgcmV0dXJuIHM7CiAgICB9CiAgfSk7Cn0KCmV4cG9ydCBmdW5jdGlvbiB1bnBhcnNlKHhzKSB7CiAgcmV0dXJuIHhzLm1hcCgocykgPT4gewogICAgaWYgKCBzICYmIHR5cGVvZiBzID09PSAnb2JqZWN0JyApIHsKICAgICAgaWYgKCBPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwocywgJ3BhdHRlcm4nKSApIHsKICAgICAgICByZXR1cm4gYCIkeyBzLnBhdHRlcm4gfSJgOwogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiBzLm9wOwogICAgICB9CiAgICB9IGVsc2UgaWYgKCAvWyJcc10vLnRlc3QocykgJiYgIS8nLy50ZXN0KHMpICkgewogICAgICByZXR1cm4gYCckeyBzLnJlcGxhY2UoLyhbJ1xcXSkvZywgJ1xcJDEnKSB9J2A7CiAgICB9IGVsc2UgaWYgKCAvWyInXHNdLy50ZXN0KHMpICkgewogICAgICByZXR1cm4gYCIkeyBzLnJlcGxhY2UoLyhbIlxcJGAhXSkvZywgJ1xcJDEnKSB9ImA7CiAgICB9IGVsc2UgewogICAgICByZXR1cm4gU3RyaW5nKHMpLnJlcGxhY2UoLyhbXFwkYCgpISMmKnxdKS9nLCAnXFwkMScpOwogICAgfQogIH0pLmpvaW4oJyAnKTsKfQoK"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ShellInput.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE5D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;EAE5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;EACF,CAAC;EACD,CAAC;MACG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEhC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC9C,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;IAElB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC3C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UACtB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACtB,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb;QACA,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;;QAEV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACtB,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEd,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACf,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACvG;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;EACH;AACF,CAAC;;AAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC9E,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACnB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IAClB,EAAE,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACV;EACF,CAAC,CAAC;AACJ;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACnB,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MAChC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACxD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC3B,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb;IACF,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/C,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClD,EAAE,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtD;EACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AACd", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ShellInput.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { LabeledInput } from '@components/Form/LabeledInput';\n\nexport default {\n  emits: ['update:value'],\n\n  components: { LabeledInput },\n\n  props: {\n    value: {\n      type:    Array,\n      default: null,\n    }\n  },\n  /*\n      userValue is a string representation of args array, with spaces between each array item and single quotes around any items with whitespace\n      value input of [\"-c\", \"sleep 600\"]\n      is displayed as: \"-c 'sleep 600'\"\n\n      user input of \"-c \"sleep 600\"\" or \"-c 'sleep 600'\"\n      causes $emit 'input' of [\"-c\", \"sleep 600\"]\n  */\n  data() {\n    let userValue = '';\n\n    if ( this.value ) {\n      userValue = this.value.reduce((str, each) => {\n        if (each.includes(' ')) {\n          str += `'${ each }'`;\n        } else {\n          str += each;\n        }\n        str += ' ';\n\n        return str;\n      }, '').trim();\n    }\n\n    return { userValue };\n  },\n\n  methods: {\n    update(userValue) {\n      let out = null;\n\n      if ( userValue ) {\n        out = userValue.match(/('[^']+')|(\"[^\"]+\")|\\S+/g).map((string) => string.replace(/^'|'$|^\"|\"$/g, ''));\n      }\n      this.$emit('update:value', out);\n    },\n  }\n};\n\nexport const OPS = ['||', '&&', ';;', '|&', '&', ';', '(', ')', '|', '<', '>'];\nexport function reop(xs) {\n  return xs.map((s) => {\n    if ( OPS.includes(s) ) {\n      return { op: s };\n    } else {\n      return s;\n    }\n  });\n}\n\nexport function unparse(xs) {\n  return xs.map((s) => {\n    if ( s && typeof s === 'object' ) {\n      if ( Object.prototype.hasOwnProperty.call(s, 'pattern') ) {\n        return `\"${ s.pattern }\"`;\n      } else {\n        return s.op;\n      }\n    } else if ( /[\"\\s]/.test(s) && !/'/.test(s) ) {\n      return `'${ s.replace(/(['\\\\])/g, '\\\\$1') }'`;\n    } else if ( /[\"'\\s]/.test(s) ) {\n      return `\"${ s.replace(/([\"\\\\$`!])/g, '\\\\$1') }\"`;\n    } else {\n      return String(s).replace(/([\\\\$`()!#&*|])/g, '\\\\$1');\n    }\n  }).join(' ');\n}\n\n</script>\n\n<template>\n  <LabeledInput\n    v-model:value=\"userValue\"\n    v-bind=\"$attrs\"\n    @update:value=\"update($event)\"\n  />\n</template>\n"]}]}