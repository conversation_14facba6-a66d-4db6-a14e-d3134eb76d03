{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CruResourceFooter.vue?vue&type=style&index=0&id=22ef885c&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CruResourceFooter.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5jcnUtcmVzb3VyY2UtZm9vdGVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7CiAgbWFyZ2luLXRvcDogMjBweDsKICB6LWluZGV4OiB6LWluZGV4KCdjcnVGb290ZXInKTsKCiAgLmJ0biB7CiAgICBtYXJnaW4tbGVmdDogMjBweDsKICB9Cn0KCg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CruResourceFooter.vue"], "names": [], "mappings": ";AAmHA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE7B,CAAC,CAAC,CAAC,EAAE;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CruResourceFooter.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapGetters } from 'vuex';\n\nimport AsyncButton from '@shell/components/AsyncButton';\nimport ResourceCancelModal from '@shell/components/ResourceCancelModal';\nimport { _VIEW } from '@shell/config/query-params';\n\nexport default {\n  emits: ['cancel-confirmed', 'finish'],\n\n  components: { AsyncButton, ResourceCancelModal },\n  props:      {\n    mode: {\n      type:    String,\n      default: 'create',\n    },\n\n    isForm: {\n      type:    Boolean,\n      default: true,\n    },\n\n    // Override the set of labels shown on the button from the default save/create.\n    finishButtonMode: {\n      type:    String,\n      default: null,\n    },\n\n    confirmCancelRequired: {\n      type:    Boolean,\n      default: false,\n    },\n\n    confirmBackRequired: {\n      type:    Boolean,\n      default: true,\n    },\n\n    showCancel: {\n      type:    Boolean,\n      default: true\n    },\n\n    /**\n     * Inherited global identifier prefix for tests\n     * Define a term based on the parent component to avoid conflicts on multiple components\n     */\n    componentTestid: {\n      type:    String,\n      default: 'form-footer'\n    }\n  },\n\n  data() {\n    return { isCancelModal: false };\n  },\n\n  computed: {\n    ...mapGetters({ t: 'i18n/t' }),\n\n    isView() {\n      return this.mode === _VIEW;\n    },\n  },\n\n  methods: {\n    checkCancel(isCancel) {\n      if (isCancel) {\n        this.isCancelModal = true;\n      } else {\n        this.isCancelModal = false;\n      }\n      this.$refs.cancelModal.show();\n    },\n\n    confirmCancel(isCancel) {\n      this.$emit('cancel-confirmed', isCancel);\n    },\n  },\n};\n</script>\n\n<template>\n  <div class=\"cru-resource-footer\">\n    <slot name=\"footer-prefix\" />\n    <slot name=\"cancel\">\n      <button\n        v-if=\"!isView && showCancel\"\n        id=\"cru-cancel\"\n        :data-testid=\"componentTestid + '-cancel'\"\n        type=\"button\"\n        class=\"btn role-secondary\"\n        @click=\"confirmCancelRequired ? checkCancel(true) : $emit('cancel-confirmed', true)\"\n      >\n        <t k=\"generic.cancel\" />\n      </button>\n    </slot>\n    <slot :checkCancel=\"checkCancel\">\n      <AsyncButton\n        v-if=\"!isView\"\n        :data-testid=\"componentTestid + '-create'\"\n        :mode=\"finishButtonMode || mode\"\n        @click=\"$emit('finish', $event)\"\n      />\n    </slot>\n    <ResourceCancelModal\n      ref=\"cancelModal\"\n      :is-cancel-modal=\"isCancelModal\"\n      :is-form=\"isForm\"\n      @confirm-cancel=\"confirmCancel($event)\"\n    />\n  </div>\n</template>\n\n<style lang=\"scss\">\n.cru-resource-footer {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 20px;\n  z-index: z-index('cruFooter');\n\n  .btn {\n    margin-left: 20px;\n  }\n}\n\n</style>\n"]}]}