{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GrafanaDashboard.vue?vue&type=style&index=0&id=0ee01af3&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GrafanaDashboard.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouZ3JhZmFuYS1ncmFwaCB7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgbWluLWhlaWdodDogMTAwJTsNCiAgbWluLXdpZHRoOiAxMDAlOw0KDQogICYgOmRlZXAoKSAuY29udGVudCB7DQogICAgcG9zaXRpb246IHJlbGF0aXZlOw0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIHdpZHRoOiAxMDAlOw0KICAgIGhlaWdodDogMTAwJTsNCiAgICBwYWRkaW5nOiAwOw0KICB9DQoNCiAgJiA6ZGVlcCgpIC5vdmVybGF5IHsNCiAgICBwb3NpdGlvbjogc3RhdGljOw0KICAgIGJhY2tncm91bmQtY29sb3I6IGluaXRpYWw7DQogIH0NCg0KICBpZnJhbWUgew0KICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICBsZWZ0OiAwOw0KICAgIHJpZ2h0OiAwOw0KICAgIHRvcDogMjBweDsNCiAgICBib3R0b206IDA7DQogICAgd2lkdGg6IDEwMCU7DQogICAgaGVpZ2h0OiAxMDAlOw0KICAgIG92ZXJmbG93OiBoaWRkZW47DQoNCiAgICAmLmxvYWRpbmcgew0KICAgICAgdmlzaWJpbGl0eTogaGlkZGVuOw0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GrafanaDashboard.vue"], "names": [], "mappings": ";AA4RA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEf,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACZ;;EAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACR,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB;EACF;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GrafanaDashboard.vue", "sourceRoot": "", "sourcesContent": ["<script>\r\nimport Loading from '@shell/components/Loading';\r\nimport { Banner } from '@components/Banner';\r\nimport { computeDashboardUrl } from '@shell/utils/grafana';\r\nimport { CATALOG } from '@shell/config/types';\r\n\r\nexport default {\r\n  components: { Banner, Loading },\r\n  props:      {\r\n    url: {\r\n      type:     String,\r\n      required: true,\r\n    },\r\n    vars: {\r\n      type:    Object,\r\n      default: () => ({})\r\n    },\r\n    range: {\r\n      type:    String,\r\n      default: null\r\n    },\r\n    refreshRate: {\r\n      type:    String,\r\n      default: null\r\n    },\r\n    // change the grafana url prefix for local clusters in certain monitoring versions\r\n    // project monitoring (projectHelmCharts) supply a grafana url that never needs to be modified in this way\r\n    modifyPrefix: {\r\n      type:    Boolean,\r\n      default: true\r\n    },\r\n    backgroundColor: {\r\n      type:    String,\r\n      default: '#1b1c21'\r\n    },\r\n    theme: {\r\n      type:    String,\r\n      default: 'dark'\r\n    }\r\n  },\r\n  async fetch() {\r\n    const inStore = this.$store.getters['currentProduct'].inStore;\r\n\r\n    if (this.$store.getters[`${ inStore }/canList`](CATALOG.APP)) {\r\n      try {\r\n        const res = await this.$store.dispatch(`${ inStore }/find`, { type: CATALOG.APP, id: 'cattle-monitoring-system/rancher-monitoring' });\r\n\r\n        this.monitoringVersion = res?.currentVersion;\r\n      } catch (err) {}\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false, error: false, interval: null, errorTimer: null, monitoringVersion: ''\r\n    };\r\n  },\r\n  computed: {\r\n    currentUrl() {\r\n      return this.computeUrl();\r\n    },\r\n    grafanaUrl() {\r\n      return this.currentUrl.replace('&kiosk', '');\r\n    },\r\n    graphWindow() {\r\n      return this.$refs.frame?.contentWindow;\r\n    },\r\n    graphHistory() {\r\n      return this.graphWindow?.history;\r\n    },\r\n    graphDocument() {\r\n      return this.graphWindow?.document;\r\n    }\r\n  },\r\n  watch: {\r\n    currentUrl(neu) {\r\n      // Should consider changing `this.graphWindow?.angular` to something like `!loaded && !error`\r\n      // https://github.com/rancher/dashboard/pull/5802\r\n      if (this.graphHistory && this.graphWindow?.angular) {\r\n        this.graphWindow.location.replace(neu);\r\n      }\r\n    },\r\n\r\n    error(neu) {\r\n      if (neu) {\r\n        this.errorTimer = setInterval(() => {\r\n          this.reload();\r\n        }, 45000);\r\n      } else {\r\n        clearInterval(this.errorTimer);\r\n        this.errorTimer = null;\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$refs.frame.onload = this.inject;\r\n    this.poll();\r\n  },\r\n  beforeUnmount() {\r\n    if (this.interval) {\r\n      clearInterval(this.interval);\r\n    }\r\n\r\n    if (this.errorTimer) {\r\n      clearInterval(this.errorTimer);\r\n    }\r\n  },\r\n  methods: {\r\n    poll() {\r\n      if (this.interval) {\r\n        clearInterval(this.interval);\r\n        this.interval = null;\r\n      }\r\n\r\n      this.interval = setInterval(() => {\r\n        try {\r\n          const graphWindow = this.$refs.frame?.contentWindow;\r\n\r\n          // Note. getElementsByClassName won't work, following a grafana bump class names are now unique - for example css-2qng6u-panel-container\r\n          const errorElements = graphWindow.document.querySelectorAll('[class$=\"alert-error');\r\n          const errorCornerElements = graphWindow.document.querySelectorAll('[class$=\"panel-info-corner--error');\r\n          const panelInFullScreenElements = graphWindow.document.querySelectorAll('[class$=\"panel-in-fullscreen');\r\n          const panelContainerElements = graphWindow.document.querySelectorAll('[class$=\"panel-container');\r\n          const error = errorElements.length > 0 || errorCornerElements.length > 0;\r\n          const loaded = panelInFullScreenElements.length > 0 || panelContainerElements.length > 0;\r\n          const errorMessageElms = graphWindow.document.getElementsByTagName('pre');\r\n          const errorMessage = errorMessageElms.length > 0 ? errorMessageElms[0].innerText : '';\r\n          const isFailure = errorMessage.includes('\"status\": \"Failure\"');\r\n\r\n          if (error) {\r\n            throw new Error('An error was detected in the iframe');\r\n          }\r\n\r\n          this['loading'] = !loaded;\r\n          this['error'] = isFailure;\r\n        } catch (ex) {\r\n          this['error'] = true;\r\n          this['loading'] = false;\r\n          clearInterval(this.interval);\r\n          this.interval = null;\r\n        }\r\n      }, 100);\r\n    },\r\n    computeFromTo() {\r\n      return {\r\n        from: `now-${ this.range }`,\r\n        to:   `now`\r\n      };\r\n    },\r\n    computeUrl() {\r\n      const embedUrl = this.url;\r\n      const clusterId = this.$store.getters['currentCluster'].id;\r\n      const params = this.computeParams();\r\n\r\n      return computeDashboardUrl(this.monitoringVersion, embedUrl, clusterId, params, this.modifyPrefix);\r\n    },\r\n    computeParams() {\r\n      const params = {};\r\n      const fromTo = this.computeFromTo();\r\n\r\n      if (fromTo.from) {\r\n        params.from = fromTo.from;\r\n      }\r\n\r\n      if (fromTo.to) {\r\n        params.to = fromTo.to;\r\n      }\r\n\r\n      if (this.refreshRate) {\r\n        params.refresh = this.refreshRate;\r\n      }\r\n\r\n      if (Object.keys(this.vars).length > 0) {\r\n        Object.entries(this.vars).forEach((entry) => {\r\n          const paramName = `var-${ entry[0] }`;\r\n\r\n          params[paramName] = entry[1];\r\n        });\r\n      }\r\n\r\n      params.theme = this.theme;\r\n\r\n      return params;\r\n    },\r\n    reload(ev) {\r\n      ev && ev.preventDefault();\r\n      this.$refs.frame.contentWindow.location.reload();\r\n      this.poll();\r\n    },\r\n    injectCss() {\r\n      const style = document.createElement('style');\r\n\r\n      style.innerHTML = `\r\n        body .grafana-app .dashboard-content {\r\n          background: ${ this.backgroundColor };\r\n          padding: 0;\r\n        }\r\n\r\n        body .grafana-app .layout {\r\n          background: ${ this.backgroundColor };\r\n        }\r\n\r\n\r\n        body .grafana-app .dashboard-content .panel-container {\r\n          background-color: initial;\r\n          border: none;\r\n        }\r\n\r\n        body .grafana-app .dashboard-content .panel-wrapper {\r\n          height: 100%;\r\n        }\r\n\r\n        body .grafana-app .panel-menu-container {\r\n          display: none;\r\n        }\r\n\r\n        body .grafana-app .panel-title {\r\n          cursor: default;\r\n        }\r\n\r\n        body .grafana-app .panel-title .panel-title-text div {\r\n          display: none;\r\n        }\r\n      `;\r\n\r\n      const graphWindow = this.$refs.frame?.contentWindow;\r\n      const graphDocument = graphWindow?.document;\r\n\r\n      if (graphDocument.head) {\r\n        graphDocument.head.appendChild(style);\r\n      }\r\n    },\r\n\r\n    inject() {\r\n      this.injectCss();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<template>\r\n  <div class=\"grafana-graph\">\r\n    <Banner\r\n      v-if=\"error\"\r\n      color=\"error\"\r\n      style=\"z-index: 1000\"\r\n    >\r\n      <div class=\"text-center\">\r\n        {{ t('grafanaDashboard.failedToLoad') }} <a\r\n          href=\"#\"\r\n          @click=\"reload\"\r\n        >{{ t('grafanaDashboard.reload') }}</a>\r\n      </div>\r\n    </Banner>\r\n    <iframe\r\n      v-show=\"!error\"\r\n      ref=\"frame\"\r\n      :class=\"{loading, frame: true}\"\r\n      :src=\"currentUrl\"\r\n      frameborder=\"0\"\r\n      scrolling=\"no\"\r\n    />\r\n    <div v-if=\"loading\">\r\n      <Loading />\r\n    </div>\r\n    <div\r\n      v-if=\"!loading && !error\"\r\n      class=\"external-link\"\r\n    >\r\n      <!-- https://github.com/harvester/harvester-installer/pull/512/files -->\r\n      <!-- It is necessary to include the parameter referer when accessing the Grafana page. -->\r\n      <!-- This parameter is required by the backend to identify the origin of the request from which cluster -->\r\n      <!-- The matching mechanism as follows: -->\r\n      <!-- ~.*/k8s/clusters/(c-m-.+)/.* -->\r\n      <!-- ~.*/dashboard/harvester/c/(c-m-.+)/.* -->\r\n      <a\r\n        :href=\"grafanaUrl\"\r\n        target=\"_blank\"\r\n        rel=\"noopener nofollow\"\r\n      >{{ t('grafanaDashboard.grafana') }} <i class=\"icon icon-external-link\" /></a>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<style lang='scss' scoped>\r\n.grafana-graph {\r\n  position: relative;\r\n  min-height: 100%;\r\n  min-width: 100%;\r\n\r\n  & :deep() .content {\r\n    position: relative;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-items: center;\r\n    width: 100%;\r\n    height: 100%;\r\n    padding: 0;\r\n  }\r\n\r\n  & :deep() .overlay {\r\n    position: static;\r\n    background-color: initial;\r\n  }\r\n\r\n  iframe {\r\n    position: absolute;\r\n    left: 0;\r\n    right: 0;\r\n    top: 20px;\r\n    bottom: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    overflow: hidden;\r\n\r\n    &.loading {\r\n      visibility: hidden;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}