{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ButtonDropdown.vue?vue&type=style&index=0&id=403d97b4&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ButtonDropdown.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ButtonDropdown.vue"], "names": [], "mappings": ";AAsOA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB;IACF;EACF;AACF;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB;IACF;EACF;AACF;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;EAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;MAEX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAChB;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB;IACF;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MACjE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ButtonDropdown.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { createPopper } from '@popperjs/core';\nimport { get } from '@shell/utils/object';\nimport isString from 'lodash/isString';\nimport VueSelectOverrides from '@shell/mixins/vue-select-overrides';\n\nexport default {\n  emits: ['dd-button-action', 'click-action'],\n\n  mixins: [VueSelectOverrides],\n  props:  {\n    buttonLabel: {\n      default: '',\n      type:    String,\n    },\n    closeOnSelect: {\n      default: true,\n      type:    Boolean\n    },\n    disabled: {\n      default: false,\n      type:    Boolean,\n    },\n    // array of option objects containing at least a label and link, but also icon and action are available\n    dropdownOptions: {\n      // required: true,\n      default: () => [],\n      type:    Array,\n    },\n    optionKey: {\n      default: null,\n      type:    String,\n    },\n    optionLabel: {\n      default: 'label',\n      type:    String,\n    },\n    // sm, null(med), lg - no xs...its so small\n    size: {\n      default: null,\n      type:    String,\n    },\n    value: {\n      default: null,\n      type:    String,\n    },\n    placement: {\n      default: 'bottom-start',\n      type:    String\n    },\n    selectable: {\n      default: (opt) => {\n        if ( opt ) {\n          if ( opt.disabled || opt.kind === 'group' || opt.kind === 'divider' || opt.loading ) {\n            return false;\n          }\n        }\n\n        return true;\n      },\n      type: Function\n    },\n  },\n  data() {\n    return { focused: false };\n  },\n\n  methods: {\n    withPopper(dropdownList, component, { width }) {\n      /**\n       * We need to explicitly define the dropdown width since\n       * it is usually inherited from the parent with CSS.\n       */\n      const componentWidth = component.$refs.search.clientWidth;\n      const dropWidth = dropdownList.clientWidth;\n\n      if (dropWidth < componentWidth) {\n        dropdownList.style.width = `${ componentWidth }px`;\n      } else {\n        dropdownList.style.width = 'min-content';\n      }\n\n      /**\n       * Here we position the dropdownList relative to the $refs.toggle Element.\n       *\n       * The 'offset' modifier aligns the dropdown so that the $refs.toggle and\n       * the dropdownList overlap by 1 pixel.\n       *\n       * The 'toggleClass' modifier adds a 'drop-up' class to the Vue Select\n       * wrapper so that we can set some styles for when the dropdown is placed\n       * above.\n       */\n      const popper = createPopper(component.$refs.toggle, dropdownList, {\n        placement: this.placement || 'bottom-start',\n        modifiers: [\n          {\n            name:    'offset',\n            options: { offset: [-2, 2] },\n          },\n          {\n            name:    'toggleClass',\n            enabled: true,\n            phase:   'write',\n            fn({ state }) {\n              component.$el.setAttribute('x-placement', state.placement);\n            },\n          },\n        ],\n      });\n\n      /**\n       * To prevent memory leaks Popper needs to be destroyed.\n       * If you return function, it will be called just before dropdown is removed from DOM.\n       */\n      return () => popper.destroy();\n    },\n    ddButtonAction(option) {\n      this.focusSearch();\n      this.$emit('dd-button-action', option);\n    },\n    getOptionLabel(option) {\n      if (isString(option)) {\n        return option;\n      }\n\n      if (this.$attrs['get-option-label']) {\n        return this.$attrs['get-option-label'](option);\n      }\n\n      if (get(option, this.optionLabel)) {\n        if (this.localizedLabel) {\n          return this.$store.getters['i18n/t'](get(option, this.optionLabel));\n        } else {\n          return get(option, this.optionLabel);\n        }\n      } else {\n        return option;\n      }\n    },\n\n    onFocus() {\n      return this.onFocusLabeled();\n    },\n\n    onFocusLabeled() {\n      this.focused = true;\n    },\n\n    onBlur() {\n      return this.onBlurLabeled();\n    },\n\n    onBlurLabeled() {\n      this.focused = false;\n    },\n\n    focusSearch() {\n      this.$nextTick(() => {\n        const el = this.$refs['button-dropdown'].searchEl;\n\n        if ( el ) {\n          el.focus();\n        }\n      });\n    },\n    get,\n  },\n};\n</script>\n\n<template>\n  <v-select\n    ref=\"button-dropdown\"\n    class=\"button-dropdown btn\"\n    :class=\"{\n      disabled,\n      focused,\n    }\"\n    v-bind=\"$attrs\"\n    :append-to-body=\"true\"\n    :calculate-position=\"withPopper\"\n    :searchable=\"false\"\n    :clearable=\"false\"\n    :close-on-select=\"closeOnSelect\"\n    :filterable=\"false\"\n    :modelValue=\"buttonLabel\"\n    :options=\"dropdownOptions\"\n    :map-keydown=\"mappedKeys\"\n    :get-option-key=\"\n      (opt) => (optionKey ? get(opt, optionKey) : getOptionLabel(opt))\n    \"\n    :get-option-label=\"(opt) => getOptionLabel(opt)\"\n    :selectable=\"selectable\"\n    @search:blur=\"onBlur\"\n    @search:focus=\"onFocus\"\n    @update:modelValue=\"$emit('click-action', $event)\"\n  >\n    <template #no-options>\n      <slot name=\"no-options\" />\n    </template>\n\n    <template #selected-option=\"option\">\n      <button\n        tabindex=\"-1\"\n        type=\"button\"\n        class=\"dropdown-button-two btn\"\n        data-testid=\"dropdown-button\"\n        @click=\"ddButtonAction(option)\"\n        @focus=\"focusSearch\"\n      >\n        {{ option.label }}\n      </button>\n    </template>\n    <!-- Pass down templates provided by the caller -->\n    <template\n      v-for=\"(_, slot) of $slots\"\n      #[slot]=\"scope\"\n      :key=\"slot\"\n    >\n      <template v-if=\"slot !== 'selected-option' && typeof $slots[slot] === 'function'\">\n        <slot\n          :name=\"slot\"\n          v-bind=\"scope\"\n        />\n      </template>\n    </template>\n  </v-select>\n</template>\n\n<style lang='scss' scoped>\n.button-dropdown.btn-sm {\n  :deep() > .vs__dropdown-toggle {\n    .vs__actions {\n      &:after {\n        font-size: 1.6rem;\n      }\n    }\n  }\n}\n.button-dropdown.btn-lg {\n  :deep() > .vs__dropdown-toggle {\n    .vs__actions {\n      &:after {\n        font-size: 2.6rem;\n      }\n    }\n  }\n}\n.button-dropdown {\n  background: var(--accent-btn);\n  border: solid 1px var(--link);\n  color: var(--link);\n  padding: 0;\n\n  &.vs--open :deep() {\n    outline: none;\n    box-shadow: none;\n  }\n\n  &:hover {\n    :deep() .vs__dropdown-toggle .vs__actions,\n    :deep() .vs__selected-options {\n      background: var(--accent-btn-hover);\n    }\n    :deep() .vs__selected-options .vs__selected button {\n      background-color: transparent;\n      color: var(--accent-btn-hover-text);\n    }\n    :deep() .vs__dropdown-toggle .vs__actions {\n      &:after {\n        color: var(--accent-btn-hover-text);\n      }\n    }\n  }\n\n  :deep() > .vs__dropdown-toggle {\n    width: 100%;\n    display: grid;\n    grid-template-columns: 75% 25%;\n    border: none;\n    background: transparent;\n\n    .vs__actions {\n\n      &:after {\n        color: var(--link);\n        line-height: 1;\n      }\n    }\n  }\n\n  :deep() .vs__selected-options {\n    .vs__selected {\n      margin: unset;\n      border: none;\n\n      button {\n        border: none;\n        background: transparent;\n        color: var(--link);\n      }\n    }\n    .vs__search {\n      // if you need to keep the dd open you can toggle these on and off\n      // display: none;\n      // visibility: hidden;\n      position: absolute;\n      opacity: 0;\n      padding: 0;\n    }\n  }\n\n  :deep() .vs__dropdown-menu {\n    min-width: unset;\n    width: fit-content;\n  }\n}\n</style>\n"]}]}