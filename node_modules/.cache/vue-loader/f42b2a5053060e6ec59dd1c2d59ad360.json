{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdown.vue?vue&type=script&setup=true&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdown.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdown.vue"], "sourcesContent": ["<script setup lang=\"ts\">\n/**\n * Offers a list of choices to the user, such as a set of actions or functions.\n * Opened by activating RcDropdownTrigger.\n *\n * Example:\n *\n *  <rc-dropdown :aria-label=\"t('nav.actionMenu.label')\">\n *    <rc-dropdown-trigger tertiary>\n *      <i class=\"icon icon-actions\" />\n *    </rc-dropdown-trigger>\n *    <template #dropdownCollection>\n *      <rc-dropdown-item @click=\"performAction()\">\n *        Action 1\n *      </rc-dropdown-item>\n *      <rc-dropdown-separator />\n *      <rc-dropdown-item @click=\"performAction()\">\n *        Action 2\n *      </rc-dropdown-item>\n *    </template>\n *  </rc-dropdown>\n */\nimport { ref } from 'vue';\nimport { useClickOutside } from '@shell/composables/useClickOutside';\nimport { useDropdownContext } from '@components/RcDropdown/useDropdownContext';\n\ndefineProps<{\n  ariaLabel?: string\n}>();\n\nconst emit = defineEmits(['update:open']);\n\nconst {\n  isMenuOpen,\n  showMenu,\n  returnFocus,\n  setFocus,\n  provideDropdownContext,\n  registerDropdownCollection,\n  handleKeydown,\n} = useDropdownContext(emit);\n\nprovideDropdownContext();\n\nconst popperContainer = ref(null);\nconst dropdownTarget = ref(null);\n\nuseClickOutside(dropdownTarget, () => showMenu(false));\n\nconst applyShow = () => {\n  registerDropdownCollection(dropdownTarget.value);\n  setFocus();\n};\n\n</script>\n\n<template>\n  <v-dropdown\n    no-auto-focus\n    :triggers=\"[]\"\n    :shown=\"isMenuOpen\"\n    :auto-hide=\"false\"\n    :container=\"popperContainer\"\n    :placement=\"'bottom-end'\"\n    @apply-show=\"applyShow\"\n  >\n    <slot name=\"default\">\n      <!--Empty slot content Trigger-->\n    </slot>\n\n    <template #popper>\n      <div\n        ref=\"dropdownTarget\"\n        class=\"dropdownTarget\"\n        tabindex=\"-1\"\n        role=\"menu\"\n        aria-orientation=\"vertical\"\n        dropdown-menu-collection\n        :aria-label=\"ariaLabel || 'Dropdown Menu'\"\n        @keydown=\"handleKeydown\"\n        @keydown.down=\"setFocus()\"\n      >\n        <slot name=\"dropdownCollection\">\n          <!--Empty slot content-->\n        </slot>\n      </div>\n    </template>\n  </v-dropdown>\n  <div\n    ref=\"popperContainer\"\n    class=\"popperContainer\"\n    @keydown.tab=\"showMenu(false)\"\n    @keydown.escape=\"returnFocus\"\n  >\n    <!--Empty container for mounting popper content-->\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .popperContainer {\n    display: contents;\n    &:deep(.v-popper__popper) {\n\n      .v-popper__wrapper {\n        box-shadow: 0px 6px 18px 0px rgba(0, 0, 0, 0.25), 0px 4px 10px 0px rgba(0, 0, 0, 0.15);\n        border-radius: var(--border-radius-lg);\n\n        .v-popper__arrow-container {\n          display: none;\n        }\n\n        .v-popper__inner {\n          padding: 10px 0 10px 0;\n        }\n      }\n    }\n  }\n\n  .dropdownTarget {\n    &:focus-visible, &:focus {\n      outline: none;\n    }\n  }\n</style>\n"], "names": [], "mappings": ";AAsBA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;;;;;;;AAvB9E,CAAC,CAAC;AACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC,CAAC;AACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACV,CAAC;AACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjB,CAAC,CAAC;;;AASF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAA4B;;AAEzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAExB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEhC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACZ,CAAC;;;;;;;;"}]}