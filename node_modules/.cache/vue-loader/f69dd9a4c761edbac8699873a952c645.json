{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ActionDropdown.vue", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ActionDropdown.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVuZGVyIH0gZnJvbSAiLi9BY3Rpb25Ecm9wZG93bi52dWU/dnVlJnR5cGU9dGVtcGxhdGUmaWQ9ZGExZmI1YWMiCmltcG9ydCBzY3JpcHQgZnJvbSAiLi9BY3Rpb25Ecm9wZG93bi52dWU/dnVlJnR5cGU9c2NyaXB0Jmxhbmc9anMiCmV4cG9ydCAqIGZyb20gIi4vQWN0aW9uRHJvcGRvd24udnVlP3Z1ZSZ0eXBlPXNjcmlwdCZsYW5nPWpzIgoKaW1wb3J0ICIuL0FjdGlvbkRyb3Bkb3duLnZ1ZT92dWUmdHlwZT1zdHlsZSZpbmRleD0wJmlkPWRhMWZiNWFjJmxhbmc9c2NzcyIKCmltcG9ydCBleHBvcnRDb21wb25lbnQgZnJvbSAiLi4vLi4vLi4vdnVlLWxvYWRlci9kaXN0L2V4cG9ydEhlbHBlci5qcyIKY29uc3QgX19leHBvcnRzX18gPSAvKiNfX1BVUkVfXyovZXhwb3J0Q29tcG9uZW50KHNjcmlwdCwgW1sncmVuZGVyJyxyZW5kZXJdLFsnX19maWxlJywibm9kZV9tb2R1bGVzL0ByYW5jaGVyL3NoZWxsL2NvbXBvbmVudHMvQWN0aW9uRHJvcGRvd24udnVlIl1dKQovKiBob3QgcmVsb2FkICovCmlmIChtb2R1bGUuaG90KSB7CiAgX19leHBvcnRzX18uX19obXJJZCA9ICJkYTFmYjVhYyIKICBjb25zdCBhcGkgPSBfX1ZVRV9ITVJfUlVOVElNRV9fCiAgbW9kdWxlLmhvdC5hY2NlcHQoKQogIGlmICghYXBpLmNyZWF0ZVJlY29yZCgnZGExZmI1YWMnLCBfX2V4cG9ydHNfXykpIHsKICAgIGFwaS5yZWxvYWQoJ2RhMWZiNWFjJywgX19leHBvcnRzX18pCiAgfQogIAogIG1vZHVsZS5ob3QuYWNjZXB0KCIuL0FjdGlvbkRyb3Bkb3duLnZ1ZT92dWUmdHlwZT10ZW1wbGF0ZSZpZD1kYTFmYjVhYyIsICgpID0+IHsKICAgIGFwaS5yZXJlbmRlcignZGExZmI1YWMnLCByZW5kZXIpCiAgfSkKCn0KCgpleHBvcnQgZGVmYXVsdCBfX2V4cG9ydHNfXw=="}]}