{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Pinned.vue?vue&type=style&index=0&id=6f3b6bf2&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Pinned.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLmljb24gewogICAgZm9udC1zaXplOiAxNHB4OwogICAgdHJhbnNmb3JtOiBzY2FsZVgoLTEpOwogIH0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Pinned.vue"], "names": [], "mappings": ";EA+CE,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Pinned.vue", "sourceRoot": "", "sourcesContent": ["<script>\n// Allow the user to pin a cluster by clicking it.\nexport default {\n  props: {\n    cluster: {\n      type:     Object,\n      required: true,\n    },\n    tabOrder: {\n      type:    Number,\n      default: null,\n    }\n  },\n\n  computed: {\n    pinned() {\n      return this.cluster.pinned;\n    }\n  },\n\n  methods: {\n    toggle() {\n      if ( this.pinned ) {\n        this.cluster.unpin();\n      } else {\n        this.cluster.pin();\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <i\n    :tabindex=\"tabOrder\"\n    :aria-checked=\"!!pinned\"\n    class=\"pin icon\"\n    :class=\"{'icon-pin-outlined': !pinned, 'icon-pin': pinned}\"\n    role=\"button\"\n    :aria-label=\"t('nav.ariaLabel.pinCluster', { cluster: cluster.label })\"\n    @click.stop.prevent=\"toggle\"\n    @keydown.enter.prevent=\"toggle\"\n    @keydown.space.prevent=\"toggle\"\n  />\n</template>\n\n<style lang=\"scss\" scoped>\n  .icon {\n    font-size: 14px;\n    transform: scaleX(-1);\n  }\n</style>\n"]}]}