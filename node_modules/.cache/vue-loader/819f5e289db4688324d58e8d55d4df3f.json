{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetClusters.vue?vue&type=template&id=4338021a&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetClusters.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetClusters.vue"], "names": [], "mappings": ";EAyFE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB;IACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzG,CAAC,CAAC,CAAC,CAAC;QACF,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,CAAC;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC,CAAC;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5D,CAAC,CAAC,CAAC,CAAC;QACF,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/C,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvE,CAAC,CAAC;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B;QACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC,CAAC;cACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cAC/B,CAAC,CAAC,CAAC,CAAC;gBACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB;gBACE,CAAC,CAAC,CAAC;kBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;kBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnB;kBACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;kBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnB;kBACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACN,CAAC;gBACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;gBACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzC;gBACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACjF,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC;UACA,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB;AACR,CAAC,CAAC,CAAC,CAAC,CAAC;QACG,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetClusters.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport ResourceTable from '@shell/components/ResourceTable';\nimport Tag from '@shell/components/Tag.vue';\nimport { STATE, NAME, AGE, FLEET_SUMMARY } from '@shell/config/table-headers';\nimport { FLEET, MANAGEMENT } from '@shell/config/types';\n\nexport default {\n  components: { ResourceTable, Tag },\n\n  props: {\n    rows: {\n      type:     Array,\n      required: true,\n    },\n\n    schema: {\n      type:    Object,\n      default: null,\n    },\n\n    loading: {\n      type:    Boolean,\n      default: false,\n    },\n    useQueryParamsForSimpleFiltering: {\n      type:    <PERSON>olean,\n      default: false\n    }\n  },\n\n  computed: {\n    MANAGEMENT_CLUSTER() {\n      return MANAGEMENT.CLUSTER;\n    },\n\n    headers() {\n      const out = [\n        STATE,\n        NAME,\n        {\n          name:     'bundlesReady',\n          labelKey: 'tableHeaders.bundlesReady',\n          value:    'status.display.readyBundles',\n          sort:     'status.summary.ready',\n          search:   false,\n        },\n        {\n          name:     'reposReady',\n          labelKey: 'tableHeaders.reposReady',\n          value:    'status.readyGitRepos',\n          sort:     'status.summary.ready',\n          search:   false,\n        },\n        FLEET_SUMMARY,\n        {\n          name:          'lastSeen',\n          labelKey:      'tableHeaders.lastSeen',\n          value:         'status.agent.lastSeen',\n          sort:          'status.agent.lastSeen',\n          search:        false,\n          formatter:     'LiveDate',\n          formatterOpts: { addSuffix: true },\n          width:         120,\n        },\n        AGE,\n      ];\n\n      return out;\n    },\n\n    pagingParams() {\n      const schema = this.$store.getters[`management/schemaFor`](FLEET.CLUSTER);\n\n      return {\n        singularLabel: this.$store.getters['type-map/labelFor'](schema),\n        pluralLabel:   this.$store.getters['type-map/labelFor'](schema, 99),\n      };\n    },\n  },\n\n  methods: {\n    toggleCustomLabels(row) {\n      row['displayCustomLabels'] = !row.displayCustomLabels;\n    }\n  }\n};\n</script>\n\n<template>\n  <ResourceTable\n    v-bind=\"$attrs\"\n    :schema=\"schema\"\n    :headers=\"headers\"\n    :rows=\"rows\"\n    :sub-rows=\"true\"\n    :loading=\"loading\"\n    :use-query-params-for-simple-filtering=\"useQueryParamsForSimpleFiltering\"\n    key-field=\"_key\"\n  >\n    <template #cell:workspace=\"{row}\">\n      <span v-if=\"row.type !== MANAGEMENT_CLUSTER && row.metadata.namespace\">{{ row.metadata.namespace }}</span>\n      <span\n        v-else\n        class=\"text-muted\"\n      >&mdash;</span>\n    </template>\n\n    <template #cell:reposReady=\"{row}\">\n      <span\n        v-if=\"!row.repoInfo\"\n        class=\"text-muted\"\n      >&mdash;</span>\n      <span\n        v-else-if=\"row.repoInfo.unready\"\n        class=\"text-warning\"\n      >{{ row.repoInfo.ready }}/{{ row.repoInfo.total }}</span>\n      <span v-else>{{ row.repoInfo.total }}</span>\n    </template>\n\n    <template #cell:bundlesReady=\"{row}\">\n      <span\n        v-if=\"row.bundleInfo.noValidData\"\n        class=\"text-muted\"\n      >&mdash;</span>\n      <span\n        v-else-if=\"row.bundleInfo.ready !== row.bundleInfo.total\"\n        class=\"text-warning\"\n      >{{ row.bundleInfo.ready }}/{{ row.bundleInfo.total }}</span>\n      <span\n        v-else\n        :class=\"{'text-error': !row.bundleInfo.total}\"\n      >{{ row.bundleInfo.total }}</span>\n    </template>\n\n    <template #sub-row=\"{fullColspan, row, onRowMouseEnter, onRowMouseLeave}\">\n      <tr\n        class=\"labels-row sub-row\"\n        @mouseenter=\"onRowMouseEnter\"\n        @mouseleave=\"onRowMouseLeave\"\n      >\n        <template v-if=\"row.customLabels.length\">\n          <td>&nbsp;</td>\n          <td>&nbsp;</td>\n          <td :colspan=\"fullColspan-2\">\n            <span\n              v-if=\"row.customLabels.length\"\n              class=\"mt-5\"\n            > {{ t('fleet.cluster.labels') }}:\n              <span\n                v-for=\"(label, i) in row.customLabels\"\n                :key=\"i\"\n                class=\"mt-5 labels\"\n              >\n                <Tag\n                  v-if=\"i < 7\"\n                  class=\"mr-5 label\"\n                >\n                  {{ label }}\n                </Tag>\n                <Tag\n                  v-else-if=\"i > 6 && row.displayCustomLabels\"\n                  class=\"mr-5 label\"\n                >\n                  {{ label }}\n                </Tag>\n              </span>\n              <a\n                v-if=\"row.customLabels.length > 7\"\n                href=\"#\"\n                @click.prevent=\"toggleCustomLabels(row)\"\n              >\n                {{ t(`fleet.cluster.${row.displayCustomLabels? 'hideLabels' : 'showLabels'}`) }}\n              </a>\n            </span>\n          </td>\n        </template>\n        <td\n          v-else\n          :colspan=\"fullColspan\"\n        >\n&nbsp;\n        </td>\n      </tr>\n    </template>\n  </ResourceTable>\n</template>\n\n<style lang='scss' scoped>\n  .labels-row {\n    td {\n      padding-top:0;\n      .tag {\n        margin-right: 5px;\n        display: inline-block;\n        margin-top: 2px;\n      }\n    }\n  }\n  .labels {\n    display: inline;\n    flex-wrap: wrap;\n\n    .label {\n      display: inline-block;\n      margin-top: 2px;\n    }\n  }\n</style>\n"]}]}