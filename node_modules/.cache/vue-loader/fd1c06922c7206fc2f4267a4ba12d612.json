{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/index.vue?vue&type=style&index=0&id=ae661846&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/index.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLnF1ZXN0aW9uIHsKICAgIG1hcmdpbi10b3A6IDEwcHg7CgogICAgJjpmaXJzdC1jaGlsZCB7CiAgICAgIG1hcmdpbi10b3A6IDA7CiAgICB9CiAgfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/index.vue"], "names": [], "mappings": ";EAkgBE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACf;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/index.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport Jexl from 'jexl';\nimport Tab from '@shell/components/Tabbed/Tab';\nimport { get, set } from '@shell/utils/object';\nimport { sortBy, camelCase } from 'lodash';\nimport { _EDIT } from '@shell/config/query-params';\nimport StringType from './String';\nimport BooleanType from './Boolean';\nimport EnumType from './Enum';\nimport IntType from './Int';\nimport FloatType from './Float';\nimport ArrayType from './Array';\nimport MapType from './QuestionMap';\nimport ReferenceType from './Reference';\nimport CloudCredentialType from './CloudCredential';\nimport RadioType from './Radio';\nimport YamlType from './Yaml';\nimport Loading from '@shell/components/Loading';\n\nexport const knownTypes = {\n  string:          StringType,\n  hostname:        StringType,\n  multiline:       StringType,\n  password:        StringType,\n  ipaddr:          StringType,\n  cidr:            StringType,\n  cron:            StringType,\n  boolean:         BooleanType,\n  enum:            EnumType,\n  int:             IntType,\n  float:           FloatType,\n  questionMap:     MapType,\n  reference:       ReferenceType,\n  configmap:       ReferenceType,\n  secret:          ReferenceType,\n  storageclass:    ReferenceType,\n  pvc:             ReferenceType,\n  cloudcredential: CloudCredentialType,\n  radio:           RadioType,\n  yaml:            YamlType,\n};\n\nexport function componentForQuestion(q) {\n  const type = (q.type || '').toLowerCase();\n\n  if ( knownTypes[type] ) {\n    return type;\n  } else if ( type.startsWith('array') ) { // This only really works for array[string|multiline], but close enough for now.\n    return ArrayType;\n  } else if ( type.startsWith('map') ) { // Same, only works with map[string|multiline]\n    return MapType;\n  } else if ( type.startsWith('reference[') ) { // Same, only works with map[string|multiline]\n    return ReferenceType;\n  }\n\n  return 'string';\n}\n\nexport function schemaToQuestions(fields) {\n  const keys = Object.keys(fields);\n  const out = [];\n\n  for ( const k of keys ) {\n    out.push({\n      variable: k,\n      label:    k,\n      ...fields[k],\n    });\n  }\n\n  return out;\n}\n\nfunction migrate(expr) {\n  let out;\n\n  if ( expr.includes('||') ) {\n    out = expr.split('||').map((x) => migrate(x)).join(' || ');\n  } else if ( expr.includes('&&') ) {\n    out = expr.split('&&').map((x) => migrate(x)).join(' && ');\n  } else {\n    const parts = expr.match(/^(.*)(!?=)(.*)$/);\n\n    if ( parts ) {\n      const key = parts[1].trim();\n      const op = parts[2].trim() === '!=' ? '!=' : '==';\n      const val = parts[3].trim();\n\n      if ( val === 'true' || val === 'false' || val === 'null' ) {\n        out = `${ key } ${ op } ${ val }`;\n      } else if ( val === '' ) {\n        // Existing charts expect `foo=` with `{foo: null}` to be true.\n        if ( op === '!=' ) {\n          out = `!!${ key }`;\n        } else {\n          out = `!${ key }`;\n        }\n        // out = `${ op === '!' ? '!' : '' }(${ key } == \"\" || ${ key } == null)`;\n      } else {\n        out = `${ key } ${ op } \"${ val }\"`;\n      }\n    } else {\n      try {\n        Jexl.compile(expr);\n\n        out = expr;\n      } catch (e) {\n        console.error('Error migrating expression:', expr); // eslint-disable-line no-console\n\n        out = 'true';\n      }\n    }\n  }\n\n  return out;\n}\n\nexport default {\n  emits: ['updated'],\n\n  components: {\n    ...knownTypes,\n    Tab,\n    Loading,\n  },\n\n  props: {\n    mode: {\n      type:    String,\n      default: _EDIT,\n    },\n\n    value: {\n      type:     Object,\n      required: true,\n    },\n\n    tabbed: {\n      type:    [Boolean, String],\n      default: true,\n    },\n\n    // Can be a chartVersion, resource Schema, or an Array of question objects\n    source: {\n      type:     [Object, Array],\n      required: true,\n    },\n\n    targetNamespace: {\n      type:     String,\n      required: true\n    },\n\n    ignoreVariables: {\n      type:    Array,\n      default: () => [],\n    },\n\n    disabled: {\n      type:    Boolean,\n      default: false,\n    },\n\n    inStore: {\n      type:    String,\n      default: 'cluster'\n    },\n\n    emit: {\n      type:    Boolean,\n      default: false,\n    }\n  },\n\n  async fetch() {\n    // If this source is a schema, ensure the schema's `resourceFields` is populated\n    if (this.source.type === 'schema' && this.source.requiresResourceFields) {\n      await this.source.fetchResourceFields();\n    }\n  },\n\n  data() {\n    return { valueGeneration: 0 };\n  },\n\n  computed: {\n    allQuestions() {\n      if ( this.source.questions?.questions ) {\n        return this.source.questions.questions;\n      } else if ( this.source.type === 'schema' && this.source.resourceFields ) {\n        return schemaToQuestions(this.source.resourceFields);\n      } else if ( typeof this.source === 'object' ) {\n        return schemaToQuestions(this.source);\n      } else {\n        return [];\n      }\n    },\n\n    shownQuestions() {\n      const values = this.value;\n      const vm = this;\n\n      if ( this.valueGeneration < 0 ) {\n        // Pointless condition to get this to depend on generation and recompute\n        return;\n      }\n\n      const out = [];\n\n      for ( const q of this.allQuestions ) {\n        if ( this.ignoreVariables.includes(q.variable) ) {\n          continue;\n        }\n\n        addQuestion(q);\n      }\n\n      return out;\n\n      function addQuestion(q, depth = 1, parentGroup) {\n        if ( !vm.shouldShow(q, values) ) {\n          return;\n        }\n\n        q.depth = depth;\n        q.group = q.group || parentGroup;\n\n        out.push(q);\n\n        if ( q.subquestions?.length && vm.shouldShowSub(q, values) ) {\n          for ( const sub of q.subquestions ) {\n            addQuestion(sub, depth + 1, q.group);\n          }\n        }\n      }\n    },\n\n    chartName() {\n      return this.source.chart?.name;\n    },\n\n    groups() {\n      const map = {};\n      const defaultGroup = 'Questions';\n      let weight = this.shownQuestions.length;\n\n      for ( const q of this.shownQuestions ) {\n        const group = q.group || defaultGroup;\n\n        const normalized = group.trim().toLowerCase();\n        const name = this.$store.getters['i18n/withFallback'](`charts.${ this.chartName }.group.${ camelCase(group) }`, null, group);\n\n        if ( !map[normalized] ) {\n          map[normalized] = {\n            name,\n            questions: [],\n            weight:    weight--,\n          };\n        }\n\n        map[normalized].questions.push(q);\n      }\n\n      const out = Object.values(map);\n\n      return sortBy(out, 'weight:desc');\n    },\n\n    asTabs() {\n      if ( this.tabbed === false || this.tabbed === 'never' ) {\n        return false;\n      }\n\n      if ( this.tabbed === 'multiple' ) {\n        return !!this.groups.length;\n      }\n\n      return true;\n    },\n  },\n\n  watch: {\n    value: {\n      deep: true,\n\n      handler() {\n        this.valueGeneration++;\n      },\n    }\n  },\n\n  methods: {\n    get,\n    set,\n    componentForQuestion,\n\n    update(variable, $event) {\n      set(this.value, variable, $event);\n      if (this.emit) {\n        this.$emit('updated');\n      }\n    },\n    evalExpr(expr, values, question, allQuestions) {\n      try {\n        const out = Jexl.evalSync(expr, values);\n\n        // console.log('Eval', expr, '=> ', out);\n\n        // If the variable contains a hyphen, check if it evaluates to true\n        // according to the evaluation logic used in the old UI.\n        // This helps users avoid manual work to migrate from legacy apps.\n        if (!out && expr.includes('-')) {\n          const res = this.evaluate(question, allQuestions);\n\n          return res;\n        }\n\n        return out;\n      } catch (err) {\n        console.error('Error evaluating expression:', expr, values); // eslint-disable-line no-console\n\n        return true;\n      }\n    },\n    evaluate(question, allQuestions) {\n      if ( !question.show_if ) {\n        return true;\n      }\n      const and = question.show_if.split('&&');\n      const or = question.show_if.split('||');\n\n      let result;\n\n      if ( get(or, 'length') > 1 ) {\n        result = or.some((showIf) => this.calExpression(showIf, allQuestions));\n      } else {\n        result = and.every((showIf) => this.calExpression(showIf, allQuestions));\n      }\n\n      return result;\n    },\n    calExpression(showIf, allQuestions) {\n      if ( showIf.includes('!=')) {\n        return this.isNotEqual(showIf, allQuestions);\n      } else {\n        return this.isEqual(showIf, allQuestions);\n      }\n    },\n    isEqual(showIf, allQuestions) {\n      showIf = showIf.trim();\n      const variables = this.getVariables(showIf, '=');\n\n      if ( variables ) {\n        const left = this.stringifyAnswer(this.getAnswer(variables.left, allQuestions));\n        const right = this.stringifyAnswer(variables.right);\n\n        return left === right;\n      }\n\n      return false;\n    },\n    isNotEqual(showIf, allQuestions) {\n      showIf = showIf.trim();\n      const variables = this.getVariables(showIf, '!=');\n\n      if ( variables ) {\n        const left = this.stringifyAnswer(this.getAnswer(variables.left, allQuestions));\n        const right = this.stringifyAnswer(variables.right);\n\n        return left !== right;\n      }\n\n      return false;\n    },\n    getVariables(showIf, operator) {\n      if ( showIf.includes(operator)) {\n        const array = showIf.split(operator);\n\n        if ( array.length === 2 ) {\n          return {\n            left:  array[0],\n            right: array[1]\n          };\n        } else {\n          return null;\n        }\n      }\n\n      return null;\n    },\n    getAnswer(variable, questions) {\n      const found = questions.find((q) => q.variable === variable);\n\n      if ( found ) {\n        // Equivalent to finding question.answer in Ember\n        return get(this.value, found.variable);\n      } else {\n        return variable;\n      }\n    },\n    stringifyAnswer(answer) {\n      if ( answer === undefined || answer === null ) {\n        return '';\n      } else if ( typeof answer === 'string' ) {\n        return answer;\n      } else {\n        return `${ answer }`;\n      }\n    },\n    shouldShow(q, values) {\n      let expr = q.if;\n\n      if ( expr === undefined && q.show_if !== undefined ) {\n        expr = migrate(q.show_if);\n      }\n\n      if ( expr ) {\n        const shown = !!this.evalExpr(expr, values, q, this.allQuestions);\n\n        return shown;\n      }\n\n      return true;\n    },\n    shouldShowSub(q, values) {\n      // Sigh, both singular and plural are used in the wild...\n      let expr = ( q.subquestions_if === undefined ? q.subquestion_if : q.subquestions_if);\n      const old = ( q.show_subquestions_if === undefined ? q.show_subquestion_if : q.show_subquestions_if);\n\n      if ( !expr && old !== undefined ) {\n        if ( old === false || old === 'false' ) {\n          expr = `!${ q.variable }`;\n        } else if ( old === true || old === 'true' ) {\n          expr = `!!${ q.variable }`;\n        } else {\n          expr = `${ q.variable } == \"${ old }\"`;\n        }\n      }\n\n      if ( expr ) {\n        return this.evalExpr(expr, values, q, this.allQuestions);\n      }\n\n      return true;\n    }\n  },\n};\n</script>\n\n<template>\n  <Loading\n    v-if=\"$fetchState.pending\"\n    mode=\"relative\"\n  />\n  <form v-else-if=\"asTabs\">\n    <Tab\n      v-for=\"(g, i) in groups\"\n      :key=\"i\"\n      :name=\"g.name\"\n      :label=\"g.name\"\n      :weight=\"g.weight\"\n    >\n      <div\n        v-for=\"(q, j) in g.questions\"\n        :key=\"`${i}-${j}`\"\n        class=\"row question\"\n      >\n        <div class=\"col span-12\">\n          <component\n            :is=\"componentForQuestion(q)\"\n            :in-store=\"inStore\"\n            :question=\"q\"\n            :target-namespace=\"targetNamespace\"\n            :value=\"get(value, q.variable)\"\n            :disabled=\"disabled\"\n            :chart-name=\"chartName\"\n            @update:value=\"update(q.variable, $event)\"\n          />\n        </div>\n      </div>\n    </Tab>\n  </form>\n  <form v-else>\n    <div\n      v-for=\"(g, i) in groups\"\n      :key=\"i\"\n    >\n      <h3 v-if=\"groups.length > 1\">\n        {{ g.label }}\n      </h3>\n      <div\n        v-for=\"(q, j) in g.questions\"\n        :key=\"`${i}-${j}`\"\n        class=\"row question\"\n      >\n        <div class=\"col span-12\">\n          <component\n            :is=\"componentForQuestion(q)\"\n            :in-store=\"inStore\"\n            :question=\"q\"\n            :target-namespace=\"targetNamespace\"\n            :mode=\"mode\"\n            :value=\"get(value, q.variable)\"\n            :disabled=\"disabled\"\n            :chart-name=\"chartName\"\n            @update:value=\"update(q.variable, $event)\"\n          />\n        </div>\n      </div>\n    </div>\n  </form>\n</template>\n\n<style lang=\"scss\" scoped>\n  .question {\n    margin-top: 10px;\n\n    &:first-child {\n      margin-top: 0;\n    }\n  }\n</style>\n"]}]}