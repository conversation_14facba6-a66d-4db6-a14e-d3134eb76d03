{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SingleClusterInfo.vue?vue&type=style&index=0&id=6eba356d&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SingleClusterInfo.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLnNpbmdsZS1jbHVzdGVyLWhlYWRlciB7CiAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgZGlzcGxheTogZmxleDsKCiAgICAucmFuY2hlci1pY29uIHsKICAgICAgbWFyZ2luLXJpZ2h0OiAxMHB4OwogICAgfQoKICAgIGgxIHsKICAgICAgZm9udC1zaXplOiAyMHB4OwogICAgICBtYXJnaW46IDA7CiAgICB9CiAgfQogIC5zaW5nbGUtY2x1c3Rlci1pbmZvIHsKICAgIG1hcmdpbi10b3A6IDIwcHg7CgogICAgLnNlY3Rpb24gewogICAgICBtYXJnaW46IDE1cHggMCA1cHggMDsKICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICB9CgogICAgLmNsdXN0ZXItY291bnRzIHsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAgbWFyZ2luOiAxMHB4IDA7CiAgICAgID4gKiB7CiAgICAgICAgZmxleDogMTsKICAgICAgICAmOm5vdCg6bGFzdC1jaGlsZCkgewogICAgICAgICAgbWFyZ2luLXJpZ2h0OiAyMHB4OwogICAgICAgIH0KICAgICAgfQogICAgfQoKICAgIC5nbGFuY2UtaXRlbSB7CiAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgcGFkZGluZzogNXB4IDA7CgogICAgICAuY2x1c3Rlci1saW5rIHsKICAgICAgICBmb250LXNpemU6IDE0cHg7CiAgICAgIH0KICAgIH0KICB9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SingleClusterInfo.vue"], "names": [], "mappings": ";EA6JE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACpB;;IAEA,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACX;EACF;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;MACd,EAAE,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACpB;MACF;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;;MAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB;IACF;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SingleClusterInfo.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport ClusterProviderIcon from '@shell/components/ClusterProviderIcon';\nimport ResourceSummary, { resourceCounts } from '@shell/components/ResourceSummary';\nimport {\n  NAMESPACE, MANAGEMENT, NODE, COUNT, CATALOG\n} from '@shell/config/types';\nimport { RESOURCES } from '@shell/pages/c/_cluster/explorer/index';\n\nexport default {\n  components: {\n    ClusterProviderIcon,\n    ResourceSummary\n  },\n\n  async fetch() {\n    this.clusters = await this.$store.dispatch('management/findAll', { type: MANAGEMENT.CLUSTER });\n  },\n\n  data() {\n    return {\n      clusters:      [],\n      clusterDetail: null,\n      clusterCounts: {}\n    };\n  },\n\n  computed: {\n    exploreLink() {\n      return { name: 'c-cluster', params: { cluster: this.clusterDetail.id } };\n    },\n\n    clusterToolsLink() {\n      return { name: 'c-cluster-explorer-tools', params: { cluster: this.clusterDetail.id } };\n    },\n\n    accessibleResources() {\n      return RESOURCES.filter((resource) => this.$store.getters['cluster/schemaFor'](resource));\n    },\n\n    totalCountGaugeInput() {\n      const totalInput = {\n        name:         this.t('clusterIndexPage.resourceGauge.totalResources'),\n        total:        0,\n        useful:       0,\n        warningCount: 0,\n        errorCount:   0\n      };\n\n      this.accessibleResources.forEach((resource) => {\n        const counts = resourceCounts(this.$store, resource);\n\n        Object.entries(counts).forEach((entry) => {\n          totalInput[entry[0]] += entry[1];\n        });\n      });\n\n      return totalInput;\n    },\n\n    canAccessNodes() {\n      return !!this.clusterCounts?.[0]?.counts?.[NODE];\n    },\n\n    canAccessNamespaces() {\n      return !!this.clusterCounts?.[0]?.counts?.[NAMESPACE];\n    },\n\n    showClusterTools() {\n      return this.$store.getters['cluster/canList'](CATALOG.CLUSTER_REPO) &&\n             this.$store.getters['cluster/canList'](CATALOG.APP);\n    }\n  },\n\n  watch: {\n    async clusters(neu) {\n      this.clusterDetail = neu[0];\n      await this.$store.dispatch('loadCluster', { id: this.clusterDetail.id });\n      this.clusterCounts = this.$store.getters[`cluster/all`](COUNT);\n    }\n  }\n};\n</script>\n\n<template>\n  <div v-if=\"clusterDetail\">\n    <div class=\"single-cluster-header\">\n      <ClusterProviderIcon\n        :cluster=\"clusterDetail\"\n        class=\"rancher-icon\"\n        width=\"32\"\n      />\n      <h1>{{ t('glance.clusterInfo') }}</h1>\n    </div>\n\n    <div class=\"single-cluster-info\">\n      <div class=\"cluster-counts\">\n        <ResourceSummary :spoofed-counts=\"totalCountGaugeInput\" />\n        <ResourceSummary\n          v-if=\"canAccessNodes\"\n          :cluster=\"clusterDetail.id\"\n          resource=\"node\"\n        />\n        <ResourceSummary\n          v-if=\"canAccessNamespaces\"\n          :cluster=\"clusterDetail.id\"\n          resource=\"namespace\"\n        />\n      </div>\n      <div class=\"glance-item\">\n        <label>{{ t('glance.provider') }}: </label>\n        <span>{{ t(`cluster.provider.${ clusterDetail.status.provider || 'other' }`) }}</span>\n      </div>\n      <div\n        v-if=\"clusterDetail.kubernetesVersionRaw\"\n        class=\"glance-item\"\n      >\n        <label>{{ t('glance.version') }}: </label>\n        <span>{{ clusterDetail.kubernetesVersionBase }}</span>\n        <span\n          v-if=\"clusterDetail.kubernetesVersionExtension\"\n          style=\"font-size: 0.75em\"\n        >{{ clusterDetail.kubernetesVersionExtension }}</span>\n      </div>\n      <div class=\"glance-item\">\n        <label>{{ t('glance.created') }}: </label>\n        <span><LiveDate\n          :value=\"clusterDetail.metadata.creationTimestamp\"\n          :add-suffix=\"true\"\n          :show-tooltip=\"true\"\n        /></span>\n      </div>\n      <div class=\"section\">\n        {{ t('generic.links') }}\n      </div>\n      <div class=\"glance-item\">\n        <router-link\n          :to=\"exploreLink\"\n          class=\"cluster-link\"\n        >\n          {{ t('nav.categories.explore') }}\n        </router-link>\n      </div>\n      <div\n        v-if=\"showClusterTools\"\n        class=\"glance-item\"\n      >\n        <router-link\n          :to=\"clusterToolsLink\"\n          class=\"cluster-link\"\n        >\n          {{ t('nav.clusterTools') }}\n        </router-link>\n      </div>\n    </div>\n  </div>\n</template>\n<style lang=\"scss\" scoped>\n  .single-cluster-header {\n    align-items: center;\n    display: flex;\n\n    .rancher-icon {\n      margin-right: 10px;\n    }\n\n    h1 {\n      font-size: 20px;\n      margin: 0;\n    }\n  }\n  .single-cluster-info {\n    margin-top: 20px;\n\n    .section {\n      margin: 15px 0 5px 0;\n      font-weight: bold;\n    }\n\n    .cluster-counts {\n      display: flex;\n      margin: 10px 0;\n      > * {\n        flex: 1;\n        &:not(:last-child) {\n          margin-right: 20px;\n        }\n      }\n    }\n\n    .glance-item {\n      font-size: 14px;\n      padding: 5px 0;\n\n      .cluster-link {\n        font-size: 14px;\n      }\n    }\n  }\n</style>\n"]}]}