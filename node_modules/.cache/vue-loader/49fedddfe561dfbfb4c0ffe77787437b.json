{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Import.vue?vue&type=template&id=24491468&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Import.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Import.vue"], "names": [], "mappings": ";EA0HE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACrC,CAAC,CAAC,CAAC,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B;IACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAChD,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAC1B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3B,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxD,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpB;QACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf;UACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpB;QACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf;UACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Import.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapGetters } from 'vuex';\nimport { Card } from '@components/Card';\nimport { Banner } from '@components/Banner';\nimport Loading from '@shell/components/Loading';\nimport YamlEditor from '@shell/components/YamlEditor';\nimport FileSelector from '@shell/components/form/FileSelector';\nimport AsyncButton from '@shell/components/AsyncButton';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport SortableTable from '@shell/components/SortableTable';\nimport { sortBy } from '@shell/utils/sort';\nimport { exceptionToErrorsArray } from '@shell/utils/error';\nimport { NAMESPACE } from '@shell/config/types';\nimport { NAME as NAME_COL, TYPE, NAMESPACE as NAMESPACE_COL, AGE } from '@shell/config/table-headers';\n\nexport default {\n  emits: ['close', 'onReadyYamlEditor'],\n\n  components: {\n    AsyncButton,\n    Banner,\n    Card,\n    Loading,\n    YamlEditor,\n    FileSelector,\n    LabeledSelect,\n    SortableTable\n  },\n\n  props: {\n    defaultNamespace: {\n      type:    String,\n      default: 'default'\n    },\n  },\n\n  async fetch() {\n    this.allNamespaces = await this.$store.dispatch('cluster/findAll', { type: NAMESPACE, opt: { url: 'namespaces' } });\n  },\n\n  data() {\n    return {\n      currentYaml:   '',\n      allNamespaces: [],\n      errors:        null,\n      rows:          null,\n      done:          false,\n    };\n  },\n\n  computed: {\n    ...mapGetters(['currentCluster']),\n\n    namespaceOptions() {\n      const out = this.allNamespaces.map((obj) => {\n        return {\n          label: obj.name,\n          value: obj.name,\n        };\n      });\n\n      return sortBy(out, 'label');\n    },\n\n    headers() {\n      return [\n        TYPE,\n        NAME_COL,\n        NAMESPACE_COL,\n        AGE\n      ];\n    },\n  },\n\n  methods: {\n    close() {\n      this.$emit('close');\n    },\n\n    onFileSelected(value) {\n      const component = this.$refs.yamleditor;\n\n      if (component) {\n        this.errors = null;\n        component.updateValue(value);\n      }\n    },\n\n    async importYaml(btnCb) {\n      try {\n        this.errors = [];\n\n        const res = await this.currentCluster.doAction('apply', {\n          yaml:             this.currentYaml,\n          defaultNamespace: this.defaultNamespace,\n        });\n\n        btnCb(true);\n\n        this.rows = res;\n        this.done = true;\n      } catch (err) {\n        this.errors = exceptionToErrorsArray(err);\n        this.done = false;\n        btnCb(false);\n      }\n    },\n\n    rowClick(e) {\n      if ( e.target.tagName === 'A' ) {\n        this.close();\n      }\n    },\n\n    onReadyYamlEditor(arg) {\n      this.$emit('onReadyYamlEditor', arg);\n    }\n  },\n};\n</script>\n\n<template>\n  <Loading v-if=\"$fetchState.pending\" />\n  <Card\n    v-else\n    :show-highlight-border=\"false\"\n    data-testid=\"import-yaml\"\n    :trigger-focus-trap=\"true\"\n  >\n    <template #title>\n      <div style=\"display: block; width: 100%;\">\n        <template v-if=\"done\">\n          <h4 data-testid=\"import-yaml-success\">\n            {{ t('import.success', {count: rows.length}) }}\n          </h4>\n        </template>\n        <template v-else>\n          <h4 v-t=\"'import.title'\" />\n          <div class=\"row\">\n            <div class=\"col span-6\">\n              <FileSelector\n                role=\"button\"\n                :aria-label=\"t('generic.readFromFileArea', { area: t('import.title') })\"\n                class=\"btn role-secondary pull-left\"\n                :label=\"t('generic.readFromFile')\"\n                @selected=\"onFileSelected\"\n              />\n            </div>\n            <div class=\"col span-6\">\n              <LabeledSelect\n                :value=\"defaultNamespace\"\n                :options=\"namespaceOptions\"\n                label-key=\"import.defaultNamespace.label\"\n                mode=\"edit\"\n                @update:value=\"newValue => defaultNamespace = newValue\"\n              />\n            </div>\n          </div>\n        </template>\n      </div>\n    </template>\n    <template #body>\n      <template v-if=\"done\">\n        <div class=\"results\">\n          <SortableTable\n            :rows=\"rows\"\n            :headers=\"headers\"\n            mode=\"view\"\n            key-field=\"_key\"\n            :search=\"false\"\n            :paging=\"true\"\n            :row-actions=\"false\"\n            :table-actions=\"false\"\n            :sub-rows-description=\"false\"\n            @rowClick=\"rowClick\"\n          />\n        </div>\n      </template>\n      <YamlEditor\n        v-else\n        ref=\"yamleditor\"\n        v-model:value=\"currentYaml\"\n        class=\"yaml-editor\"\n        @onReady=\"onReadyYamlEditor\"\n      />\n      <Banner\n        v-for=\"(err, i) in errors\"\n        :key=\"i\"\n        color=\"error\"\n        :label=\"err\"\n      />\n    </template>\n    <template #actions>\n      <div\n        v-if=\"done\"\n        class=\"text-center\"\n        style=\"width: 100%\"\n      >\n        <button\n          :aria-label=\"t('generic.close')\"\n          role=\"button\"\n          type=\"button\"\n          class=\"btn role-primary\"\n          data-testid=\"import-yaml-close\"\n          @click=\"close\"\n        >\n          {{ t('generic.close') }}\n        </button>\n      </div>\n      <div\n        v-else\n        class=\"text-center\"\n        style=\"width: 100%\"\n      >\n        <button\n          :aria-label=\"t('generic.cancel')\"\n          role=\"button\"\n          type=\"button\"\n          class=\"btn role-secondary mr-10\"\n          data-testid=\"import-yaml-cancel\"\n          @click=\"close\"\n        >\n          {{ t('generic.cancel') }}\n        </button>\n        <AsyncButton\n          v-if=\"!done\"\n          mode=\"import\"\n          :disabled=\"!currentYaml.length\"\n          data-testid=\"import-yaml-import-action\"\n          :aria-label=\"t('import.title')\"\n          @click=\"importYaml\"\n        />\n      </div>\n    </template>\n  </Card>\n</template>\n\n<style lang='scss' scoped>\n  $min: 50vh;\n  $max: 50vh;\n\n  .yaml-editor {\n    flex: 1;\n    min-height: $min;\n    max-height: $max;\n\n    :deep() .code-mirror {\n      .CodeMirror {\n        position: initial;\n      }\n\n      .CodeMirror,\n      .CodeMirror-scroll,\n      .CodeMirror-gutters {\n        min-height: $min;\n        max-height: $max;\n      }\n    }\n  }\n</style>\n"]}]}