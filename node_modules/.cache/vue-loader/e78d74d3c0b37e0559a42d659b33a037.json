{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AzureWarning.vue?vue&type=style&index=0&id=0124e661&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AzureWarning.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiNhenVyZS13YXJuIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS13YXJuaW5nKTsKICBjb2xvcjogdmFyKC0td2FybmluZy10ZXh0KTsKICBsaW5lLWhlaWdodDogMmVtOwogIHdpZHRoOiAxMDAlOwoKICA+IHAgewogICAgdGV4dC1hbGlnbjogY2VudGVyOwogIH0KfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AzureWarning.vue"], "names": [], "mappings": ";AAkEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEX,EAAE,EAAE;IACF,<PERSON>AC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AzureWarning.vue", "sourceRoot": "", "sourcesContent": ["<script>\n// This component will become redundant in 2023, see https://docs.microsoft.com/en-us/graph/migrate-azure-ad-graph-overview\nimport { NORMAN, MANAGEMENT } from '@shell/config/types';\nimport { get } from '@shell/utils/object';\nimport { AZURE_MIGRATED } from '@shell/config/labels-annotations';\nimport { BLANK_CLUSTER } from '@shell/store/store-types.js';\n\nexport default {\n  async fetch() {\n    // Check for access to steve authConfigs because everyone can load the norman auth config schema\n    if (\n      this.$store.getters['isRancher'] &&\n      this.$store.getters['management/schemaFor'](MANAGEMENT.AUTH_CONFIG)\n    ) {\n      this.authConfig = await this.$store.dispatch('rancher/find', {\n        type: NORMAN.AUTH_CONFIG,\n        id:   'azuread',\n        opt:  { url: `/v3/${ NORMAN.AUTH_CONFIG }/azuread` }\n      });\n    }\n  },\n\n  data() {\n    return {\n      authConfig:      null,\n      authConfigRoute: {\n        name:   'c-cluster-auth-config-id',\n        params: {\n          cluster: this.$route.params.cluster || BLANK_CLUSTER,\n          id:      'azuread'\n        }\n      }\n    };\n  },\n\n  computed: {\n    showWarning() {\n      if (!this.authConfig) {\n        return false;\n      }\n\n      return (\n        get(this.authConfig, `annotations.\"${ AZURE_MIGRATED }\"`) !== 'true' &&\n        this.authConfig.enabled\n      );\n    }\n  }\n};\n</script>\n\n<template>\n  <div\n    v-if=\"showWarning\"\n    id=\"azure-warn\"\n    class=\"banner\"\n  >\n    <p>\n      {{ t('authConfig.azuread.updateEndpoint.banner.message') }}\n      <router-link :to=\"authConfigRoute\">\n        {{ t('authConfig.azuread.updateEndpoint.banner.linkText') }}\n      </router-link>\n    </p>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n#azure-warn {\n  background-color: var(--warning);\n  color: var(--warning-text);\n  line-height: 2em;\n  width: 100%;\n\n  > p {\n    text-align: center;\n  }\n}\n</style>\n"]}]}