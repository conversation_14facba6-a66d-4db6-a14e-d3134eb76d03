{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/EtcdInfoBanner.vue?vue&type=style&index=0&id=1effd22d&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/EtcdInfoBanner.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogICAgLmJhbm5lciB7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtZXZlbmx5Ow0KICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KDQogICAgICAgIC5kYXR1bSB7DQogICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDVweDsNCiAgICAgICAgfQ0KDQogICAgICAgICYgOmRlZXAoKSBsYWJlbCB7DQogICAgICAgICAgICBjb2xvcjogdmFyKC0taW5mbyk7DQogICAgICAgIH0NCiAgICB9DQoNCg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/EtcdInfoBanner.vue"], "names": [], "mappings": ";IA4DI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrB;;QAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB;IACJ", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/EtcdInfoBanner.vue", "sourceRoot": "", "sourcesContent": ["<script>\r\nimport { Banner } from '@components/Banner';\r\nimport Loading from '@shell/components/Loading';\r\nimport { mapGetters } from 'vuex';\r\nimport { hasLeader, leaderChanges, failedProposals } from '@shell/utils/grafana';\r\nimport { CATALOG } from '@shell/config/types';\r\n\r\nexport default {\r\n  components: { Banner, Loading },\r\n  async fetch() {\r\n    const inStore = this.$store.getters['currentProduct'].inStore;\r\n    let monitoringVersion = '';\r\n\r\n    if (this.$store.getters[`${ inStore }/canList`](CATALOG.APP)) {\r\n      try {\r\n        const res = await this.$store.dispatch(`${ inStore }/find`, { type: CATALOG.APP, id: 'cattle-monitoring-system/rancher-monitoring' });\r\n\r\n        monitoringVersion = res?.currentVersion;\r\n      } catch (err) {\r\n\r\n      }\r\n    }\r\n\r\n    const leader = await hasLeader(monitoringVersion, this.$store.dispatch, this.currentCluster.id);\r\n\r\n    this.hasLeader = leader ? 'Yes' : 'No';\r\n    this.leaderChanges = await leaderChanges(monitoringVersion, this.$store.dispatch, this.currentCluster.id);\r\n    this.failedProposals = await failedProposals(monitoringVersion, this.$store.dispatch, this.currentCluster.id);\r\n  },\r\n  data() {\r\n    return {\r\n      hasLeader:       'No',\r\n      leaderChanges:   0,\r\n      failedProposals: 0\r\n    };\r\n  },\r\n  computed: { ...mapGetters(['currentCluster']) }\r\n};\r\n</script>\r\n\r\n<template>\r\n  <Loading v-if=\"$fetchState.pending\" />\r\n  <Banner\r\n    v-else\r\n    class=\"banner\"\r\n    color=\"info\"\r\n  >\r\n    <div class=\"datum\">\r\n      <label>{{ t('etcdInfoBanner.hasLeader') }}</label> {{ hasLeader }},\r\n    </div>\r\n    <div class=\"datum\">\r\n      <label>{{ t('etcdInfoBanner.leaderChanges') }}</label> {{ leaderChanges }},\r\n    </div>\r\n    <div class=\"datum\">\r\n      <label>{{ t('etcdInfoBanner.failedProposals') }}</label> {{ failedProposals }}\r\n    </div>\r\n  </Banner>\r\n</template>\r\n\r\n<style lang='scss' scoped>\r\n    .banner {\r\n        display: flex;\r\n        justify-content: space-evenly;\r\n        align-items: center;\r\n\r\n        .datum {\r\n            text-align: center;\r\n            margin-right: 5px;\r\n        }\r\n\r\n        & :deep() label {\r\n            color: var(--info);\r\n        }\r\n    }\r\n\r\n</style>\r\n"]}]}