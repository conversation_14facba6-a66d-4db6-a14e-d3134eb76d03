{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcButton/RcButton.vue?vue&type=style&index=0&id=b795a3d0&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcButton/RcButton.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmJ1dHRvbiB7CiAgJi5yb2xlLWxpbmsgewogICAgJjpmb2N1cywgJi5mb2N1c2VkIHsKICAgICAgQGluY2x1ZGUgZm9jdXMtb3V0bGluZTsKICAgICAgb3V0bGluZS1vZmZzZXQ6IC0ycHg7CiAgICB9CgogICAgJjpob3ZlciB7CiAgICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWFjY2VudC1idG4pOwogICAgICBib3gtc2hhZG93OiBub25lOwogICAgfQogIH0KCiAgJi5yb2xlLWdob3N0IHsKICAgIHBhZGRpbmc6IDA7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiB0cmFuc3BhcmVudDsKCiAgICAmOmZvY3VzLCAmLmZvY3VzZWQgewogICAgICBAaW5jbHVkZSBmb2N1cy1vdXRsaW5lOwogICAgICBvdXRsaW5lLW9mZnNldDogMDsKICAgIH0KCiAgICAmOmZvY3VzLXZpc2libGUgewogICAgICBAaW5jbHVkZSBmb2N1cy1vdXRsaW5lOwogICAgICBvdXRsaW5lLW9mZnNldDogMDsKICAgIH0KICB9Cn0="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcButton/RcButton.vue"], "names": [], "mappings": ";AAmEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACtB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACnB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACnB;EACF;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcButton/RcButton.vue", "sourceRoot": "", "sourcesContent": ["<script setup lang=\"ts\">\n/**\n * A button element used for performing actions, such as submitting forms or\n * opening dialogs.\n *\n * Example:\n *\n * <rc-button primary @click=\"doAction\">Perform an Action</rc-button>\n */\nimport { computed, ref, defineExpose } from 'vue';\nimport { ButtonRoleProps, ButtonSizeProps } from './types';\n\nconst buttonRoles: { role: keyof ButtonRoleProps, className: string }[] = [\n  { role: 'primary', className: 'role-primary' },\n  { role: 'secondary', className: 'role-secondary' },\n  { role: 'tertiary', className: 'role-tertiary' },\n  { role: 'link', className: 'role-link' },\n  { role: 'ghost', className: 'role-ghost' },\n];\n\nconst buttonSizes: { size: keyof ButtonSizeProps, className: string }[] = [\n  { size: 'small', className: 'btn-sm' },\n];\n\nconst props = defineProps<ButtonRoleProps & ButtonSizeProps>();\n\nconst buttonClass = computed(() => {\n  const activeRole = buttonRoles.find(({ role }) => props[role]);\n  const isButtonSmall = buttonSizes.some(({ size }) => props[size]);\n\n  return {\n    btn: true,\n\n    [activeRole?.className || 'role-primary']: true,\n\n    'btn-sm': isButtonSmall,\n  };\n});\n\nconst RcFocusTarget = ref<HTMLElement | null>(null);\n\nconst focus = () => {\n  RcFocusTarget?.value?.focus();\n};\n\ndefineExpose({ focus });\n</script>\n\n<template>\n  <button\n    ref=\"RcFocusTarget\"\n    role=\"button\"\n    :class=\"{ ...buttonClass, ...($attrs.class || { }) }\"\n  >\n    <slot name=\"before\">\n      <!-- Empty Content -->\n    </slot>\n    <slot>\n      <!-- Empty Content -->\n    </slot>\n    <slot name=\"after\">\n      <!-- Empty Content -->\n    </slot>\n  </button>\n</template>\n\n<style lang=\"scss\" scoped>\nbutton {\n  &.role-link {\n    &:focus, &.focused {\n      @include focus-outline;\n      outline-offset: -2px;\n    }\n\n    &:hover {\n      background-color: var(--accent-btn);\n      box-shadow: none;\n    }\n  }\n\n  &.role-ghost {\n    padding: 0;\n    background-color: transparent;\n\n    &:focus, &.focused {\n      @include focus-outline;\n      outline-offset: 0;\n    }\n\n    &:focus-visible {\n      @include focus-outline;\n      outline-offset: 0;\n    }\n  }\n}</style>\n"]}]}