{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ExtensionPanel.vue?vue&type=template&id=0f963a70", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ExtensionPanel.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdgogICAgdi1pZj0iZXh0ZW5zaW9uRGF0YS5sZW5ndGgiCiAgPgogICAgPGRpdgogICAgICB2LWZvcj0iaXRlbSwgaSBpbiBleHRlbnNpb25EYXRhIgogICAgICA6a2V5PSJgZXh0ZW5zaW9uRGF0YSR7bG9jYXRpb259JHtpfWAiCiAgICA+CiAgICAgIDxjb21wb25lbnQKICAgICAgICA6aXM9Iml0ZW0uY29tcG9uZW50IgogICAgICAgIDpyZXNvdXJjZT0icmVzb3VyY2UiCiAgICAgIC8+CiAgICA8L2Rpdj4KICA8L2Rpdj4K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ExtensionPanel.vue"], "names": [], "mappings": ";EA4BE,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B;IACE,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC;MACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ExtensionPanel.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { getApplicableExtensionEnhancements } from '@shell/core/plugin-helpers';\n\nexport default {\n  name:  'ExtensionPanel',\n  props: {\n    resource: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    type: {\n      type:    String,\n      default: ''\n    },\n    location: {\n      type:    String,\n      default: ''\n    },\n  },\n  data() {\n    return { extensionData: getApplicableExtensionEnhancements(this, this.type, this.location, this.$route) };\n  },\n};\n</script>\n\n<template>\n  <div\n    v-if=\"extensionData.length\"\n  >\n    <div\n      v-for=\"item, i in extensionData\"\n      :key=\"`extensionData${location}${i}`\"\n    >\n      <component\n        :is=\"item.component\"\n        :resource=\"resource\"\n      />\n    </div>\n  </div>\n</template>\n"]}]}