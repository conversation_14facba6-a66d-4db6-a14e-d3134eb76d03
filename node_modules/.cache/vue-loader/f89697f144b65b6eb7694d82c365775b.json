{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SecretSelector.vue?vue&type=template&id=584eeb32", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SecretSelector.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdgogICAgY2xhc3M9InNlY3JldC1zZWxlY3RvciIKICAgIDpjbGFzcz0ieydzaG93LWtleS1zZWxlY3Rvcic6IHNob3dLZXlTZWxlY3Rvcn0iCiAgPgogICAgPGRpdiBjbGFzcz0iaW5wdXQtY29udGFpbmVyIj4KICAgICAgPCEtLSBrZXkgYnkgbmFtZXNwYWNlIHRvIGVuc3VyZSBsYWJlbCBzZWxlY3QgY3VycmVudCBwYWdlIGlzIHJlY3JlYXRlZCBvbiBucyBjaGFuZ2UgLS0+CiAgICAgIDxSZXNvdXJjZUxhYmVsZWRTZWxlY3QKICAgICAgICB2LW1vZGVsOnZhbHVlPSJuYW1lIgogICAgICAgIDpkaXNhYmxlZD0iIWlzVmlldyAmJiBkaXNhYmxlZCIKICAgICAgICA6bGFiZWw9InNlY3JldE5hbWVMYWJlbCIKICAgICAgICA6bW9kZT0ibW9kZSIKICAgICAgICA6cmVzb3VyY2UtdHlwZT0iU0VDUkVUIgogICAgICAgIDppbi1zdG9yZT0iaW5TdG9yZSIKICAgICAgICA6cGFnaW5hdGVkLXJlc291cmNlLXNldHRpbmdzPSJwYWdpbmF0ZVNlY3JldHNTZXR0aW5nIgogICAgICAgIDphbGwtcmVzb3VyY2VzLXNldHRpbmdzPSJhbGxTZWNyZXRzU2V0dGluZ3MiCiAgICAgIC8+CiAgICAgIDxMYWJlbGVkU2VsZWN0CiAgICAgICAgdi1pZj0ic2hvd0tleVNlbGVjdG9yIgogICAgICAgIHYtbW9kZWw6dmFsdWU9ImtleSIKICAgICAgICBjbGFzcz0iY29sIHNwYW4tNiIKICAgICAgICA6ZGlzYWJsZWQ9ImlzS2V5RGlzYWJsZWQiCiAgICAgICAgOm9wdGlvbnM9ImtleXMiCiAgICAgICAgOmxhYmVsPSJrZXlOYW1lTGFiZWwiCiAgICAgICAgOm1vZGU9Im1vZGUiCiAgICAgIC8+CiAgICA8L2Rpdj4KICA8L2Rpdj4K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SecretSelector.vue"], "names": [], "mappings": ";EAoME,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChD;IACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7C,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SecretSelector.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport ResourceLabeledSelect from '@shell/components/form/ResourceLabeledSelect';\nimport { SECRET } from '@shell/config/types';\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\nimport { SECRET_TYPES as TYPES } from '@shell/config/secret';\nimport { PaginationParamFilter } from '@shell/types/store/pagination.types';\nimport { LABEL_SELECT_KINDS } from '@shell/types/components/labeledSelect';\n\nconst NONE = '__[[NONE]]__';\n\nexport default {\n  emits:      ['update:value'],\n  components: { LabeledSelect, ResourceLabeledSelect },\n\n  props: {\n    value: {\n      type:     [String, Object],\n      required: false,\n      default:  undefined\n    },\n    namespace: {\n      type:     String,\n      required: true\n    },\n    types: {\n      type:    Array,\n      default: () => Object.values(TYPES)\n    },\n    disabled: {\n      type:    Boolean,\n      default: false\n    },\n    mountKey: {\n      type:    String,\n      default: 'valueFrom'\n    },\n    nameKey: {\n      type:    String,\n      default: 'name'\n    },\n    keyKey: {\n      type:    String,\n      default: 'key'\n    },\n    showKeySelector: {\n      type:    Boolean,\n      default: false\n    },\n    secretNameLabel: {\n      type:    String,\n      default: 'Secret Name'\n    },\n    keyNameLabel: {\n      type:    String,\n      default: 'Key'\n    },\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n    inStore: {\n      type:    String,\n      default: 'cluster',\n    }\n  },\n\n  data() {\n    return {\n      secrets:            null,\n      SECRET,\n      allSecretsSettings: {\n        updateResources: (secrets) => {\n          const allSecretsInNamespace = secrets.filter((secret) => this.types.includes(secret._type) && secret.namespace === this.namespace);\n          const mappedSecrets = this.mapSecrets(allSecretsInNamespace.sort((a, b) => a.name.localeCompare(b.name)));\n\n          this.secrets = allSecretsInNamespace; // We need the key from the selected secret\n\n          return mappedSecrets;\n        }\n      },\n      paginateSecretsSetting: {\n        requestSettings: this.paginatePageOptions,\n        updateResources: (secrets) => {\n          const mappedSecrets = this.mapSecrets(secrets);\n\n          this.secrets = secrets; // We need the key from the selected secret. When paginating we won't touch the store, so just pass back here\n\n          return mappedSecrets;\n        }\n      }\n    };\n  },\n\n  computed: {\n    name: {\n      get() {\n        const name = this.showKeySelector ? this.value?.[this.mountKey]?.secretKeyRef?.[this.nameKey] : this.value;\n\n        return name || NONE;\n      },\n      set(name) {\n        const isNone = name === NONE;\n        const correctedName = isNone ? undefined : name;\n\n        if (this.showKeySelector) {\n          this.$emit('update:value', { [this.mountKey]: { secretKeyRef: { [this.nameKey]: correctedName, [this.keyKey]: '' } } });\n        } else {\n          this.$emit('update:value', correctedName);\n        }\n      }\n    },\n\n    key: {\n      get() {\n        return this.value?.[this.mountKey]?.secretKeyRef?.[this.keyKey] || '';\n      },\n      set(key) {\n        this.$emit('update:value', { [this.mountKey]: { secretKeyRef: { [this.nameKey]: this.name, [this.keyKey]: key } } });\n      }\n    },\n\n    keys() {\n      const secret = (this.secrets || []).find((secret) => secret.name === this.name) || {};\n\n      return Object.keys(secret.data || {}).map((key) => ({\n        label: key,\n        value: key\n      }));\n    },\n\n    isView() {\n      return this.mode === _VIEW;\n    },\n\n    isKeyDisabled() {\n      return !this.isView && (!this.name || this.name === NONE || this.disabled);\n    }\n  },\n\n  methods: {\n    /**\n     * Provide a set of options for the LabelSelect ([none, ...{label, value}])\n     */\n    mapSecrets(secrets) {\n      const mappedSecrets = secrets\n        .reduce((res, s) => {\n          if (s.kind === LABEL_SELECT_KINDS.NONE) {\n            return res;\n          }\n\n          if (s.id) {\n            res.push({ label: s.name, value: s.name });\n          } else {\n            res.push(s);\n          }\n\n          return res;\n        }, []);\n\n      return [\n        {\n          label: 'None', value: NONE, kind: LABEL_SELECT_KINDS.NONE\n        },\n        ...mappedSecrets\n      ];\n    },\n\n    /**\n     * @param [LabelSelectPaginationFunctionOptions] opts\n     * @returns LabelSelectPaginationFunctionOptions\n     */\n    paginatePageOptions(opts) {\n      const { opts: { filter } } = opts;\n\n      const filters = !!filter ? [PaginationParamFilter.createSingleField({ field: 'metadata.name', value: filter })] : [];\n\n      filters.push(\n        PaginationParamFilter.createSingleField({ field: 'metadata.namespace', value: this.namespace }),\n        PaginationParamFilter.createSingleField({ field: 'metadata.fields.1', value: this.types.join(',') })\n      );\n\n      return {\n        ...opts,\n        filters,\n        groupByNamespace: false,\n        classify:         true,\n        sort:             [{ asc: true, field: 'metadata.name' }],\n      };\n    },\n  }\n\n};\n</script>\n\n<template>\n  <div\n    class=\"secret-selector\"\n    :class=\"{'show-key-selector': showKeySelector}\"\n  >\n    <div class=\"input-container\">\n      <!-- key by namespace to ensure label select current page is recreated on ns change -->\n      <ResourceLabeledSelect\n        v-model:value=\"name\"\n        :disabled=\"!isView && disabled\"\n        :label=\"secretNameLabel\"\n        :mode=\"mode\"\n        :resource-type=\"SECRET\"\n        :in-store=\"inStore\"\n        :paginated-resource-settings=\"paginateSecretsSetting\"\n        :all-resources-settings=\"allSecretsSettings\"\n      />\n      <LabeledSelect\n        v-if=\"showKeySelector\"\n        v-model:value=\"key\"\n        class=\"col span-6\"\n        :disabled=\"isKeyDisabled\"\n        :options=\"keys\"\n        :label=\"keyNameLabel\"\n        :mode=\"mode\"\n      />\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\">\n.secret-selector {\n  width: 100%;\n  label {\n    display: block;\n  }\n\n  & .labeled-select {\n    min-height: $input-height;\n  }\n\n  & .vs__selected-options {\n    padding: 8px 0 7px 0;\n  }\n\n  & label {\n    display: inline-block;\n  }\n\n  &.show-key-selector {\n    .input-container > * {\n      display: inline-block;\n      width: 50%;\n\n      &.labeled-select.focused {\n        z-index: 10;\n      }\n\n      &:first-child {\n        border-top-right-radius: 0;\n        border-bottom-right-radius: 0;\n        margin-right: 0;\n      }\n\n      &:last-child {\n        border-top-left-radius: 0;\n        border-bottom-left-radius: 0;\n        border-left: none;\n        float: right;\n      }\n    }\n  }\n}\n</style>\n"]}]}