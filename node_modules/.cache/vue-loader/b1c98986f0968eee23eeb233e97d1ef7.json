{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/EnvVars.vue?vue&type=style&index=0&id=74c7a39e&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/EnvVars.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci52YWx1ZS1mcm9tIDpkZWVwKCkgewogIC52LXNlbGVjdCB7CiAgICBoZWlnaHQ6IDUwcHg7CiAgfQoKICBJTlBVVDpub3QoLnZzX19zZWFyY2gpIHsKICAgIGhlaWdodDogNTBweDsKICB9Cn0KLnZhbHVlLWZyb20sIC52YWx1ZS1mcm9tLWhlYWRlcnMgewogIGRpc3BsYXk6IGdyaWQ7CiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAyMCUgMjAlIDIwJSA1JSAyMCUgYXV0bzsKICBncmlkLWdhcDogJGNvbHVtbi1ndXR0ZXI7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBtYXJnaW4tYm90dG9tOiAxMHB4Owp9CiAgLnZhbHVlLWZyb20taGVhZGVycyB7CiAgICBtYXJnaW46IDEwcHggMHB4IDEwcHggMHB4OwogICAgY29sb3I6IHZhcigtLWlucHV0LWxhYmVsKTsKICAgIH0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/EnvVars.vue"], "names": [], "mappings": ";AAqIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACd;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACd;AACF;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACrB;EACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/EnvVars.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport ValueFromResource from '@shell/components/form/ValueFromResource';\nimport debounce from 'lodash/debounce';\nimport { randomStr } from '@shell/utils/string';\nimport { _VIEW } from '@shell/config/query-params';\n\nexport default {\n  components: { ValueFromResource },\n\n  props: {\n    /**\n     * Form mode for the component\n     */\n    mode: {\n      type:     String,\n      required: true,\n    },\n    configMaps: {\n      type:     Array,\n      required: true\n    },\n    secrets: {\n      type:     Array,\n      required: true\n    },\n    loading: {\n      default: false,\n      type:    Boolean\n    },\n    /**\n     * Container spec\n     */\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  data() {\n    const { env = [], envFrom = [] } = this.value;\n\n    const allEnv = [...env, ...envFrom].map((row) => {\n      return { value: row, id: randomStr(4) };\n    });\n\n    return {\n      env, envFrom, allEnv\n    };\n  },\n\n  computed: {\n    isView() {\n      return this.mode === _VIEW;\n    }\n  },\n\n  watch: {\n    'value.tty'(neu) {\n      if (neu) {\n        this.value['stdin'] = true;\n      }\n    }\n  },\n  created() {\n    this.queueUpdate = debounce(this.update, 500);\n  },\n\n  methods: {\n    update() {\n      delete this.value.env;\n      delete this.value.envFrom;\n      const envVarSource = [];\n      const envVar = [];\n\n      this.allEnv.forEach((row) => {\n        if (!row.value) {\n          return;\n        }\n        if (!!row.value.configMapRef || !!row.value.secretRef) {\n          envVarSource.push(row.value);\n        } else {\n          envVar.push(row.value);\n        }\n      });\n      this.value['env'] = envVar;\n      this.value['envFrom'] = envVarSource;\n    },\n\n    updateRow() {\n      this.queueUpdate();\n    },\n\n    removeRow(idx) {\n      this.allEnv.splice(idx, 1);\n      this.queueUpdate();\n    },\n\n    addFromReference() {\n      this.allEnv.push({ value: { name: '', valueFrom: {} }, id: randomStr(4) });\n    },\n  },\n};\n</script>\n<template>\n  <div :style=\"{'width':'100%'}\">\n    <div\n      v-for=\"(row, i) in allEnv\"\n      :key=\"i\"\n    >\n      <ValueFromResource\n        v-model:value=\"row.value\"\n        :all-secrets=\"secrets\"\n        :all-config-maps=\"configMaps\"\n        :mode=\"mode\"\n        :loading=\"loading\"\n        @remove=\"removeRow(i)\"\n        @update:value=\"updateRow\"\n      />\n    </div>\n    <button\n      v-if=\"!isView\"\n      v-t=\"'workload.container.command.addEnvVar'\"\n      type=\"button\"\n      class=\"btn role-tertiary add\"\n      data-testid=\"add-env-var\"\n      @click=\"addFromReference\"\n    />\n  </div>\n</template>\n\n<style lang='scss' scoped>\n.value-from :deep() {\n  .v-select {\n    height: 50px;\n  }\n\n  INPUT:not(.vs__search) {\n    height: 50px;\n  }\n}\n.value-from, .value-from-headers {\n  display: grid;\n  grid-template-columns: 20% 20% 20% 5% 20% auto;\n  grid-gap: $column-gutter;\n  align-items: center;\n  margin-bottom: 10px;\n}\n  .value-from-headers {\n    margin: 10px 0px 10px 0px;\n    color: var(--input-label);\n    }\n</style>\n"]}]}