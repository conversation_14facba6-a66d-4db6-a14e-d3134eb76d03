{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Enum.vue?vue&type=template&id=2b9a1134", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Enum.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdiBjbGFzcz0icm93Ij4KICAgIDxkaXYgY2xhc3M9ImNvbCBzcGFuLTYiPgogICAgICA8TGFiZWxlZFNlbGVjdAogICAgICAgIDptb2RlPSJtb2RlIgogICAgICAgIDpsYWJlbD0iZGlzcGxheUxhYmVsIgogICAgICAgIDpvcHRpb25zPSJvcHRpb25zIgogICAgICAgIDpwbGFjZWhvbGRlcj0icXVlc3Rpb24uZGVzY3JpcHRpb24iCiAgICAgICAgOnJlcXVpcmVkPSJxdWVzdGlvbi5yZXF1aXJlZCIKICAgICAgICA6bXVsdGlwbGU9InF1ZXN0aW9uLm11bHRpcGxlIgogICAgICAgIDp2YWx1ZT0idmFsdWUiCiAgICAgICAgOmRpc2FibGVkPSJkaXNhYmxlZCIKICAgICAgICA6dG9vbHRpcD0iZGlzcGxheVRvb2x0aXAiCiAgICAgICAgOnNlYXJjaGFibGU9InF1ZXN0aW9uLnNlYXJjaGFibGUiCiAgICAgICAgQHVwZGF0ZTp2YWx1ZT0iJGVtaXQoJ3VwZGF0ZTp2YWx1ZScsICRldmVudCkiCiAgICAgIC8+CiAgICA8L2Rpdj4KICAgIDxkaXYKICAgICAgdi1pZj0ic2hvd0Rlc2NyaXB0aW9uIgogICAgICBjbGFzcz0iY29sIHNwYW4tNiBtdC0xMCIKICAgID4KICAgICAge3sgZGlzcGxheURlc2NyaXB0aW9uIH19CiAgICA8L2Rpdj4KICA8L2Rpdj4K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Enum.vue"], "names": [], "mappings": ";EA6BE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB;MACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Enum.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport Question from './Question';\n\nexport default {\n  emits: ['update:value'],\n\n  components: { LabeledSelect },\n  mixins:     [Question],\n  computed:   {\n    options() {\n      const options = this.question.options;\n\n      if (Array.isArray(options)) {\n        return options;\n      }\n\n      return Object.entries(options).map(([key, value]) => {\n        return {\n          value: key,\n          label: value,\n        };\n      });\n    }\n  }\n};\n</script>\n\n<template>\n  <div class=\"row\">\n    <div class=\"col span-6\">\n      <LabeledSelect\n        :mode=\"mode\"\n        :label=\"displayLabel\"\n        :options=\"options\"\n        :placeholder=\"question.description\"\n        :required=\"question.required\"\n        :multiple=\"question.multiple\"\n        :value=\"value\"\n        :disabled=\"disabled\"\n        :tooltip=\"displayTooltip\"\n        :searchable=\"question.searchable\"\n        @update:value=\"$emit('update:value', $event)\"\n      />\n    </div>\n    <div\n      v-if=\"showDescription\"\n      class=\"col span-6 mt-10\"\n    >\n      {{ displayDescription }}\n    </div>\n  </div>\n</template>\n"]}]}