{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/FixedBanner.vue?vue&type=style&index=0&id=ecabaee6&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/FixedBanner.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLmJhbm5lciB7CiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgICBsaW5lLWhlaWdodDogMmVtOwogICAgd2lkdGg6IDEwMCU7CiAgICBwYWRkaW5nOiAwIDIwcHg7CgogICAgJi5iYW5uZXItY29uc2VudCB7CiAgICAgIGhlaWdodDogdW5zZXQ7CiAgICAgIG1pbi1oZWlnaHQ6IDJlbTsKICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsKICAgIH0KICB9CiAgLmJhbm5lci1kaWFsb2csIC5iYW5uZXItZGlhbG9nLWdsYXNzIHsKICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgIHRvcDogMHB4OwogICAgbGVmdDogMHB4OwogICAgd2lkdGg6IDEwMHZ3OwogICAgaGVpZ2h0OiAxMDB2aDsKICB9CiAgLmJhbm5lci1kaWFsb2ctZ2xhc3MgewogICAgei1pbmRleDogNTAwMDsKICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWRlZmF1bHQpOwogICAgb3BhY2l0eTogMC43NTsKICB9CiAgLmJhbm5lci1kaWFsb2cgewogICAgei1pbmRleDogNTAwMTsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CgogICAgLmJhbm5lci1kaWFsb2ctZnJhbWUgewogICAgICBib3JkZXI6IDJweCBzb2xpZCB2YXIoLS1ib3JkZXIpOwogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogICAgICBwYWRkaW5nOiAyMHB4OwogICAgICBoZWlnaHQ6IGZpdC1jb250ZW50OwogICAgICB3aWR0aDogZml0LWNvbnRlbnQ7CiAgICAgIG1pbi13aWR0aDogNTAlOwogICAgICBtYXgtd2lkdGg6IDgwJTsKICAgICAgbWF4LWhlaWdodDogOTAlOwoKICAgICAgLmJhbm5lciB7CiAgICAgICAgaGVpZ2h0OiBpbml0aWFsOwogICAgICAgIG92ZXJmbG93LXk6IGF1dG87CiAgICAgIH0KCiAgICAgIGJ1dHRvbiB7CiAgICAgICAgbWFyZ2luLXRvcDogMTBweDsKICAgICAgICBtYXgtd2lkdGg6IDUwJTsKICAgICAgICBvdmVyZmxvdzogaGlkZGVuOwogICAgICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOwogICAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7CiAgICAgICAgd2lkdGg6IGZpdC1jb250ZW50OwogICAgICB9CiAgICB9CiAgfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/FixedBanner.vue"], "names": [], "mappings": ";EAkOE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB;EACF;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACf;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAClB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB;IACF;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/FixedBanner.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { MANAGEMENT } from '@shell/config/types';\nimport { SETTING } from '@shell/config/settings';\nimport isEmpty from 'lodash/isEmpty';\nimport { getIndividualBanners, overlayIndividualBanners } from '@shell/utils/banners';\n\nexport default {\n  props: {\n    header: {\n      type:    Boolean,\n      default: false\n    },\n    consent: {\n      type:    Boolean,\n      default: false\n    },\n    footer: {\n      type:    Boolean,\n      default: false\n    },\n  },\n\n  data() {\n    return {\n      showDialog:  true,\n      showHeader:  false,\n      showFooter:  false,\n      showConsent: false,\n      banner:      {},\n    };\n  },\n\n  methods: {\n    hideDialog() {\n      this.showDialog = false;\n    },\n    handleLineBreaksConsentText(banner) {\n      if (banner.text?.length) {\n        // split text by newline char\n        const textArray = banner.text.split('\\n').filter((element) => element);\n\n        if (textArray.length > 1) {\n          textArray.forEach((str, i) => {\n            textArray[i] = str.trim();\n          });\n          banner.text = textArray;\n        }\n      }\n\n      return banner;\n    }\n  },\n\n  computed: {\n    uiBannerIndividual() {\n      return getIndividualBanners(this.$store);\n    },\n\n    bannerSetting() {\n      return this.$store.getters['management/all'](MANAGEMENT.SETTING).find((s) => s.id === SETTING.BANNERS);\n    },\n\n    bannerStyle() {\n      return {\n        color:              this.banner.color,\n        'background-color': this.banner.background,\n        'text-align':       this.banner.textAlignment,\n        'font-weight':      this.banner.fontWeight ? 'bold' : '',\n        'font-style':       this.banner.fontStyle ? 'italic' : '',\n        'font-size':        this.banner.fontSize,\n        'text-decoration':  this.banner.textDecoration ? 'underline' : ''\n      };\n    },\n    dialogStyle() {\n      return {\n        color:              this.banner.color,\n        'background-color': this.banner.background\n      };\n    },\n    showBanner() {\n      if (!this.banner?.text && !this.banner?.background) {\n        return false;\n      }\n\n      if (this.header) {\n        return this.showHeader;\n      } else if (this.consent) {\n        return this.showConsent;\n      } else if (this.footer) {\n        return this.showFooter;\n      }\n\n      return null;\n    },\n    isTextAnArray() {\n      return Array.isArray(this.banner?.text);\n    },\n    showAsDialog() {\n      return this.consent && !!this.banner.button;\n    },\n\n    // ID to place on the Banner DIV\n    id() {\n      if (this.header) {\n        return 'banner-header';\n      } else if (this.consent) {\n        return 'banner-consent';\n      } else if (this.footer) {\n        return 'banner-footer';\n      }\n\n      return 'banner';\n    }\n  },\n\n  watch: {\n    bannerSetting: {\n      deep: true,\n      handler(neu) {\n        if (neu?.value && neu.value !== '') {\n          try {\n            const parsed = JSON.parse(neu.value);\n\n            overlayIndividualBanners(parsed, this.uiBannerIndividual);\n\n            const {\n              bannerHeader, bannerFooter, bannerConsent, banner, showHeader, showFooter, showConsent\n            } = parsed;\n            let bannerContent = parsed?.banner || {};\n\n            if (isEmpty(banner)) {\n              if (showHeader && this.header) {\n                bannerContent = this.handleLineBreaksConsentText(bannerHeader) || {};\n              } else if (showConsent && this.consent) {\n                bannerContent = this.handleLineBreaksConsentText(bannerConsent) || {};\n              } else if (showFooter && this.footer) {\n                bannerContent = this.handleLineBreaksConsentText(bannerFooter) || {};\n              } else {\n                bannerContent = {};\n              }\n            }\n\n            this.showHeader = showHeader === 'true';\n            this.showFooter = showFooter === 'true';\n            this.showConsent = showConsent === 'true';\n            this.banner = bannerContent;\n          } catch {}\n        }\n      },\n      immediate: true\n    },\n\n  }\n};\n</script>\n\n<template>\n  <div\n    v-if=\"showBanner\"\n    :id=\"id\"\n  >\n    <div\n      v-if=\"!showAsDialog\"\n      class=\"banner\"\n      data-testid=\"fixed__banner\"\n      :style=\"bannerStyle\"\n      :class=\"{'banner-consent': consent}\"\n    >\n      <!-- text as array to support line breaks programmatically rather than just exposing HTML -->\n      <div v-if=\"isTextAnArray\">\n        <div\n          v-for=\"(text, index) in banner.text\"\n          :key=\"index\"\n          class=\"array-row\"\n        >\n          {{ text }}\n        </div>\n      </div>\n      <div\n        v-else\n        class=\"single-row\"\n      >\n        {{ banner.text }}\n      </div>\n    </div>\n    <div v-else-if=\"showDialog\">\n      <div class=\"banner-dialog-glass\" />\n      <div class=\"banner-dialog\">\n        <div\n          class=\"banner-dialog-frame\"\n          :style=\"dialogStyle\"\n        >\n          <div\n            class=\"banner\"\n            :style=\"bannerStyle\"\n          >\n            <!-- text as array to support line breaks programmatically rather than just exposing HTML -->\n            <div v-if=\"isTextAnArray\">\n              <div\n                v-for=\"(text, index) in banner.text\"\n                :key=\"index\"\n                class=\"array-row\"\n              >\n                {{ text }}\n              </div>\n            </div>\n            <div\n              v-else\n              class=\"single-row\"\n            >\n              {{ banner.text }}\n            </div>\n          </div>\n          <button\n            class=\"btn role-primary\"\n            @click=\"hideDialog()\"\n          >\n            {{ banner.button }}\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .banner {\n    text-align: center;\n    line-height: 2em;\n    width: 100%;\n    padding: 0 20px;\n\n    &.banner-consent {\n      height: unset;\n      min-height: 2em;\n      overflow: hidden;\n    }\n  }\n  .banner-dialog, .banner-dialog-glass {\n    position: absolute;\n    top: 0px;\n    left: 0px;\n    width: 100vw;\n    height: 100vh;\n  }\n  .banner-dialog-glass {\n    z-index: 5000;\n    background-color: var(--default);\n    opacity: 0.75;\n  }\n  .banner-dialog {\n    z-index: 5001;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    .banner-dialog-frame {\n      border: 2px solid var(--border);\n      display: flex;\n      align-items: center;\n      flex-direction: column;\n      padding: 20px;\n      height: fit-content;\n      width: fit-content;\n      min-width: 50%;\n      max-width: 80%;\n      max-height: 90%;\n\n      .banner {\n        height: initial;\n        overflow-y: auto;\n      }\n\n      button {\n        margin-top: 10px;\n        max-width: 50%;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        white-space: nowrap;\n        width: fit-content;\n      }\n    }\n  }\n</style>\n"]}]}