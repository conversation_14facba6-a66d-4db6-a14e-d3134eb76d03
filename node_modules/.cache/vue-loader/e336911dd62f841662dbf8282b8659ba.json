{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ChangePassword.vue?vue&type=template&id=d6824fba&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ChangePassword.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ChangePassword.vue"], "names": [], "mappings": ";EA2SE,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClE;IACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC;;QAED,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC,CAAC,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B;QACA,CAAC,CAAC,CAAC,CAAC,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC;QACD,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B;UACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvD,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B;UACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B;MACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ChangePassword.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapGetters } from 'vuex';\nimport { Banner } from '@components/Banner';\nimport { Checkbox } from '@components/Form/Checkbox';\nimport Password from '@shell/components/form/Password';\nimport { NORMAN } from '@shell/config/types';\nimport { _CREATE, _EDIT } from '@shell/config/query-params';\n\n// Component handles three use cases\n// 1) isChange - Current user is changing their own password\n// 2) isCreate - New password is for a new user\n// 3) isEdit - New password is for an existing user\nexport default {\n  emits: ['valid', 'update:value'],\n\n  components: {\n    Checkbox, Banner, Password\n  },\n  props: {\n    mode: {\n      type:    String,\n      default: null\n    },\n    mustChangePassword: {\n      type:    Boolean,\n      default: false\n    }\n  },\n  async fetch() {\n    if (this.isChange) {\n      // Fetch the username for hidden input fields. The value itself is not needed if create or changing another user's password\n      const users = await this.$store.dispatch('rancher/findAll', {\n        type: NORMAN.USER,\n        opt:  { url: '/v3/users', filter: { me: true } }\n      });\n      const user = users?.[0];\n\n      this.username = user?.username;\n    }\n    this.userChangeOnLogin = this.mustChangePassword;\n  },\n  data(ctx) {\n    return {\n      username:                   '',\n      errorMessages:              [],\n      pCanShowMismatchedPassword: false,\n      pIsRandomGenerated:         false,\n      form:                       {\n        deleteKeys:        false,\n        currentP:          '',\n        newP:              '',\n        genP:              '',\n        confirmP:          '',\n        userChangeOnLogin: false,\n      },\n    };\n  },\n  computed: {\n    ...mapGetters({ t: 'i18n/t' }),\n\n    isRandomGenerated: {\n      get() {\n        return this.pIsRandomGenerated;\n      },\n\n      set(isRandomGenerated) {\n        this.pIsRandomGenerated = isRandomGenerated;\n        this.errorMessages = [];\n        this.validate();\n      }\n    },\n\n    passwordGen: {\n      get() {\n        return this.form.genP;\n      },\n\n      set(p) {\n        this.form.genP = p;\n        this.validate();\n      }\n    },\n\n    passwordCurrent: {\n      get() {\n        return this.form.currentP;\n      },\n\n      set(p) {\n        this.form.currentP = p;\n        this.validate();\n      }\n    },\n\n    passwordNew: {\n      get() {\n        return this.form.newP;\n      },\n\n      set(p) {\n        this.form.newP = p;\n        this.validate();\n      }\n    },\n\n    passwordConfirm: {\n      get() {\n        return this.form.confirmP;\n      },\n\n      set(p) {\n        this.form.confirmP = p;\n        this.validate();\n      }\n    },\n\n    userChangeOnLogin: {\n      get() {\n        return this.form.userChangeOnLogin;\n      },\n\n      set(p) {\n        this.form.userChangeOnLogin = p;\n        this.validate();\n      }\n    },\n\n    passwordConfirmBlurred: {\n      get() {\n        return this.pCanShowMismatchedPassword;\n      },\n\n      set(p) {\n        this.pCanShowMismatchedPassword = p;\n        this.validate();\n      }\n    },\n\n    password() {\n      return this.isRandomGenerated ? this.passwordGen : this.passwordNew;\n    },\n\n    isChange() {\n      // Change password prompt\n      return !this.mode;\n    },\n\n    isCreateEdit() {\n      return this.isCreate || this.isEdit;\n    },\n\n    isCreate() {\n      return this.mode === _CREATE;\n    },\n\n    isEdit() {\n      // Edit user prompt\n      return this.mode === _EDIT;\n    },\n\n    userGeneratedPasswordsRequired() {\n      if (this.isChange) {\n        return true;\n      }\n      if (this.isCreate) {\n        return !this.isRandomGenerated;\n      }\n      if (this.isEdit) {\n        return !!this.passwordNew || !!this.passwordConfirm;\n      }\n\n      return false;\n    }\n  },\n  created() {\n    // Catch the 'create' case and there's no content\n    this.validate();\n  },\n\n  methods: {\n    passwordsMatch() {\n      const match = this.passwordNew === this.passwordConfirm;\n\n      this.errorMessages = this.passwordConfirmBlurred && !match ? [this.t('changePassword.errors.mismatchedPassword')] : [];\n\n      return match;\n    },\n\n    baseIsUserGenPasswordValid() {\n      return this.passwordsMatch() && !!this.passwordNew;\n    },\n\n    isValid() {\n      if (this.isChange) {\n        return !!this.passwordCurrent && (this.isRandomGenerated ? true : this.baseIsUserGenPasswordValid());\n      }\n\n      if (this.isRandomGenerated) {\n        // If we're not changing current user... and password is randomly generated... there'll be no new/confirm mismatch\n        return true;\n      }\n\n      if (this.isCreate) {\n        return this.baseIsUserGenPasswordValid();\n      }\n\n      if (this.isEdit) {\n        // If the user generated password is required... ensure it's valid\n        return this.userGeneratedPasswordsRequired ? this.baseIsUserGenPasswordValid() : true;\n      }\n\n      return false;\n    },\n\n    validate() {\n      const isValid = this.isValid();\n\n      if (isValid) {\n        // Covers the case where we don't re-evaluate the error messages (don't need to at the time)\n        this.errorMessages = [];\n      }\n\n      this.$emit('valid', isValid);\n      this.$emit('update:value', {\n        password:          this.password,\n        userChangeOnLogin: this.userChangeOnLogin\n      });\n    },\n\n    async save(user) {\n      if (this.isChange) {\n        await this.changePassword();\n        if (this.form.deleteKeys) {\n          await this.deleteKeys();\n        }\n      } else if (this.isEdit) {\n        return this.setPassword(user);\n      }\n    },\n\n    async setPassword(user) {\n      // Error handling is catered for by caller\n      await this.$store.dispatch('rancher/resourceAction', {\n        type:       NORMAN.USER,\n        actionName: 'setpassword',\n        resource:   user,\n        body:       { newPassword: this.isRandomGenerated ? this.form.genP : this.form.newP },\n      });\n    },\n\n    async changePassword() {\n      try {\n        await this.$store.dispatch('rancher/collectionAction', {\n          type:       NORMAN.USER,\n          actionName: 'changepassword',\n          body:       {\n            currentPassword: this.form.currentP,\n            newPassword:     this.isRandomGenerated ? this.form.genP : this.form.newP\n          },\n        });\n      } catch (err) {\n        this.errorMessages = [err.message || this.t('changePassword.errors.failedToChange')];\n        throw err;\n      }\n    },\n\n    async deleteKeys() {\n      try {\n        const tokens = await this.$store.dispatch('rancher/findAll', {\n          type: NORMAN.TOKEN,\n          opt:  {\n            // Ensure we have any new tokens since last fetched... and that we don't attempt to delete previously deleted tokens\n            force: true\n          }\n        });\n\n        await Promise.all(tokens.reduce((res, token) => {\n          if (!token.current) {\n            res.push(token.remove());\n          }\n\n          return res;\n        }, []));\n      } catch (err) {\n        if (err.message) {\n          this.errorMessages = [err.message];\n        } else if (err.length > 1) {\n          this.errorMessages = [this.t('changePassword.errors.failedDeleteKeys')];\n        } else {\n          this.errorMessages = [this.t('changePassword.errors.failedDeleteKey')];\n        }\n        throw err;\n      }\n    },\n  },\n};\n</script>\n\n<template>\n  <div\n    class=\"change-password\"\n    :class=\"{'change': isChange, 'create': isCreate, 'edit': isEdit}\"\n  >\n    <div class=\"form\">\n      <div class=\"fields\">\n        <Checkbox\n          v-if=\"isChange\"\n          v-model:value=\"form.deleteKeys\"\n          label-key=\"changePassword.deleteKeys.label\"\n          class=\"mt-10\"\n        />\n        <Checkbox\n          v-if=\"isCreateEdit\"\n          v-model:value=\"userChangeOnLogin\"\n          label-key=\"changePassword.changeOnLogin.label\"\n          class=\"mt-10 type\"\n        />\n        <Checkbox\n          v-if=\"isCreateEdit\"\n          v-model:value=\"isRandomGenerated\"\n          label-key=\"changePassword.generatePassword.label\"\n          class=\"mt-10 type\"\n        />\n\n        <!-- Create two 'invisible fields' for password managers -->\n        <input\n          id=\"username\"\n          type=\"text\"\n          name=\"username\"\n          autocomplete=\"username\"\n          :value=\"username\"\n          tabindex=\"-1\"\n          :data-lpignore=\"!isChange\"\n        >\n        <input\n          id=\"password\"\n          type=\"password\"\n          name=\"password\"\n          autocomplete=\"password\"\n          :value=\"password\"\n          tabindex=\"-1\"\n          :data-lpignore=\"!isChange\"\n        >\n        <Password\n          v-if=\"isChange\"\n          v-model:value=\"passwordCurrent\"\n          data-testid=\"account__current_password\"\n          class=\"mt-10\"\n          :required=\"true\"\n          :label=\"t('changePassword.currentPassword.label')\"\n        />\n        <div\n          v-if=\"isRandomGenerated\"\n          :class=\"{'row': isCreateEdit}\"\n        >\n          <div :class=\"{'col': isCreateEdit, 'span-8': isCreateEdit}\">\n            <Password\n              v-model:value=\"passwordGen\"\n              class=\"mt-10\"\n              :is-random=\"true\"\n              :required=\"false\"\n              :label=\"t('changePassword.randomGen.generated.label')\"\n            />\n          </div>\n        </div>\n        <div\n          v-else\n          class=\"userGen\"\n          :class=\"{'row': isCreateEdit}\"\n        >\n          <div :class=\"{'col': isCreateEdit, 'span-4': isCreateEdit}\">\n            <Password\n              v-model:value=\"passwordNew\"\n              data-testid=\"account__new_password\"\n              class=\"mt-10\"\n              :label=\"t('changePassword.userGen.newPassword.label')\"\n              :required=\"userGeneratedPasswordsRequired\"\n              :ignore-password-managers=\"!isChange\"\n            />\n          </div>\n          <div :class=\"{'col': isCreateEdit, 'span-4': isCreateEdit}\">\n            <Password\n              v-model:value=\"passwordConfirm\"\n              data-testid=\"account__confirm_password\"\n              class=\"mt-10\"\n              :label=\"t('changePassword.userGen.confirmPassword.label')\"\n              :required=\"userGeneratedPasswordsRequired\"\n              :ignore-password-managers=\"!isChange\"\n              @blur=\"passwordConfirmBlurred = true\"\n            />\n          </div>\n        </div>\n      </div>\n      <Checkbox\n        v-if=\"isChange\"\n        v-model:value=\"isRandomGenerated\"\n        label-key=\"changePassword.generatePassword.label\"\n        class=\"mt-10 type\"\n      />\n    </div>\n    <div\n      v-if=\"errorMessages && errorMessages.length\"\n      class=\"text-error\"\n      :class=\"{'row': isCreateEdit}\"\n    >\n      <div :class=\"{'col': isCreateEdit, 'span-8': isCreateEdit}\">\n        <Banner\n          v-for=\"(err, i) in errorMessages\"\n          :key=\"i\"\n          role=\"alert\"\n          :aria-label=\"err\"\n          color=\"error\"\n          :label=\"err\"\n          class=\"mb-0\"\n        />\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .change-password {\n    display: flex;\n    flex-direction: column;\n\n    &.change {\n      .form .fields {\n        height: 240px;\n      }\n    }\n\n    &.create, &.edit {\n      height: 185px;\n      .form {\n        .fields {\n          display: flex;\n          flex-direction: column;\n        }\n      }\n    }\n\n    .form {\n      display: flex;\n      flex-direction: column;\n      .fields{\n        #username, #password {\n          height: 0;\n          width: 0;\n          background-size: 0;\n          padding: 0;\n          border: none;\n        }\n      }\n    }\n\n    .text-error {\n      height: 53px;\n    }\n  }\n\n</style>\n"]}]}