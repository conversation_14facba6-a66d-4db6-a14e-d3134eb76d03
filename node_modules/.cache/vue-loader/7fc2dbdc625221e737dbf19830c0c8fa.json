{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ButtonMultiAction.vue?vue&type=template&id=439898ca&scoped=true&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ButtonMultiAction.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGJ1dHRvbgogICAgdHlwZT0iYnV0dG9uIgogICAgY2xhc3M9ImJ0biBidG4tc20gcm9sZS1tdWx0aS1hY3Rpb24gYWN0aW9ucyIKICAgIHJvbGU9ImJ1dHRvbiIKICAgIDpjbGFzcz0iYnV0dG9uQ2xhc3MiCiAgICBAY2xpY2s9IihlOiBFdmVudCkgPT4gJGVtaXQoJ2NsaWNrJywgZSkiCiAgPgogICAgPGkKICAgICAgY2xhc3M9Imljb24gaWNvbi1hY3Rpb25zIgogICAgICA6YWx0PSJ0KCdzb3J0YWJsZVRhYmxlLnRhYmxlQWN0aW9uc0ltZ0FsdCcpIgogICAgLz4KICA8L2J1dHRvbj4K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ButtonMultiAction.vue"], "names": [], "mappings": ";EAqBE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACzC;IACE,CAAC;MACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC;EACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ButtonMultiAction.vue", "sourceRoot": "", "sourcesContent": ["<script setup lang=\"ts\">\nimport { computed } from 'vue';\n\ndefineEmits(['click']);\n\ntype Props = {\n  borderless?: boolean;\n  invisible?: boolean;\n}\n\nconst props = defineProps<Props>();\n\nconst buttonClass = computed(() => {\n  return {\n    borderless: props?.borderless,\n    invisible:  props?.invisible,\n  };\n});\n</script>\n\n<template>\n  <button\n    type=\"button\"\n    class=\"btn btn-sm role-multi-action actions\"\n    role=\"button\"\n    :class=\"buttonClass\"\n    @click=\"(e: Event) => $emit('click', e)\"\n  >\n    <i\n      class=\"icon icon-actions\"\n      :alt=\"t('sortableTable.tableActionsImgAlt')\"\n    />\n  </button>\n</template>\n\n<style lang=\"scss\" scoped>\n.borderless {\n  background-color: transparent;\n  border: none;\n\n  &:focus-visible {\n    @include focus-outline;\n    outline-offset: -2px;\n  }\n\n  &:hover, &:focus {\n    background-color: var(--accent-btn);\n    box-shadow: none;\n  }\n}\n</style>\n"]}]}