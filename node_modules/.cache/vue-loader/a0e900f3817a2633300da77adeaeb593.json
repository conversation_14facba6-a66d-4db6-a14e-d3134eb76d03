{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/AccountAccess.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/AccountAccess.vue", "mtime": *************}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": *************}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": *************}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": *************}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": *************}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": *************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/AccountAccess.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACrC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9F,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;AAG5C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAExB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7C;IACF;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACpE,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC9B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UACrF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtD;UACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC5E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7C,EAAE,CAAC,CAAC,CAAC,EAAE;cACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrD;UACF;QACF;MACF,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;MAE1E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC5B,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;UAElG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9D,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpE;MACF;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9E,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC1C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC;IACJ,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;IAE9B,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjE,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC;;EAEH,CAAC;AACH,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/AccountAccess.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent } from 'vue';\nimport { _CREATE, _VIEW } from '@shell/config/query-params';\nimport LabeledSelect from '@shell/components/form/LabeledSelect.vue';\nimport SelectCredential from '@shell/edit/provisioning.cattle.io.cluster/SelectCredential.vue';\nimport { DEFAULT_REGION } from './CruEKS.vue';\nimport { mapGetters } from 'vuex';\nimport { AWS } from '../../types';\nimport { NORMAN } from '@shell/config/types';\n\n\nexport default defineComponent({\n  name: 'EKSAccountAccess',\n\n  emits: ['update-region', 'error', 'cancel-credential', 'update-credential'],\n\n  components: {\n    LabeledSelect,\n    SelectCredential\n  },\n\n  props: {\n    mode: {\n      type:    String,\n      default: _CREATE\n    },\n\n    credential: {\n      type:    String,\n      default: null\n    },\n\n    region: {\n      type:    String,\n      default: ''\n    }\n  },\n\n  async fetch() {\n    if (this.mode !== _VIEW) {\n      this.defaultRegions = await this.$store.dispatch('aws/defaultRegions');\n      if (this.defaultRegions.length && !this.region) {\n        this.$emit('update-region', DEFAULT_REGION);\n      }\n    }\n  },\n\n  data() {\n    return { regions: [] as string[], defaultRegions: [] as string[] };\n  },\n\n  watch: {\n    isAuthenticated: {\n      async handler(neu) {\n        if (neu && this.mode !== _VIEW) {\n          // Auto-default to credential's region only once on initialization if no region is set\n          const credentialDefaultRegion = await this.getCredentialDefaultRegion();\n          if (credentialDefaultRegion) {\n            this.$emit('update-region', credentialDefaultRegion);\n          }\n          await this.fetchRegions();\n        } else {\n          if (this.defaultRegions.length && !this.defaultRegions.includes(this.region)) {\n            if (this.defaultRegions.includes(DEFAULT_REGION)) {\n              this.$emit('update-region', DEFAULT_REGION);\n            } else {\n              this.$emit('update-region', this.defaultRegions[0]);\n            }\n          }\n        }\n      },\n      immediate: true\n    }\n  },\n\n  methods: {\n    async fetchRegions() {\n      const { region, credential }: { region: string, credential: string} = this;\n\n      if (!!region && !!credential) {\n        try {\n          const ec2Client = await this.$store.dispatch('aws/ec2', { region, cloudCredentialId: credential });\n\n          const res: {Regions: AWS.EC2Region[]} = await ec2Client.describeRegions({});\n\n          this.regions = (res?.Regions || []).map((r) => r.RegionName);\n        } catch (err) {\n          this.$emit('error', this.t('eks.errors.fetchingRegions', { err }));\n        }\n      }\n    },\n    \n    async getCredentialDefaultRegion(): Promise<string|null> {\n      if (!this.credential) return null;\n            // Use the correct store and schema type consistent with rest of codebase\n      return this.$store.dispatch('rancher/find', {\n        type: NORMAN.CLOUD_CREDENTIAL,\n        id: this.credential\n      }).then(credentialResource => {\n        return credentialResource?.decodedData?.defaultRegion || null;\n      }).catch(err => {\n        console.warn('Failed to fetch credential default region:', err);\n        return null;\n      });\n    },\n  },\n\n  computed: {\n    ...mapGetters({ t: 'i18n/t' }),\n\n    // once the credential is validated we can fetch a list of available regions\n    isAuthenticated(): boolean {\n      return !!this.credential;\n    },\n\n    regionOptions(): string[] {\n      return this.regions.length ? this.regions : this.defaultRegions;\n    },\n\n    CREATE(): string {\n      return _CREATE;\n    },\n\n    VIEW(): string {\n      return _VIEW;\n    },\n\n  },\n});\n</script>\n\n<template>\n  <div\n    :class=\"{'showing-form': !credential}\"\n    class=\"credential-region\"\n  >\n    <div class=\"region mb-10\">\n      <LabeledSelect\n        :disabled=\"mode!=='create'\"\n        :value=\"region\"\n        data-testid=\"eks_region\"\n        label-key=\"eks.region.label\"\n        :options=\"regionOptions\"\n        @update:value=\"$emit('update-region', $event)\"\n      />\n    </div>\n    <div\n      class=\"select-credential-container\"\n    >\n      <SelectCredential\n        :value=\"credential\"\n        data-testid=\"crueks-select-credential\"\n        :mode=\"mode === VIEW ? VIEW : CREATE\"\n        provider=\"aws\"\n        :default-on-cancel=\"true\"\n        :showing-form=\"!credential\"\n        class=\"select-credential\"\n        :cancel=\"()=>$emit('cancel-credential')\"\n        @update:value=\"$emit('update-credential', $event)\"\n      />\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\">\n  .credential-region {\n    display: flex;\n\n    .region {\n      flex-basis: 50%;\n      flex-grow: 0;\n      margin: 0 1.75% 0 0;\n    }\n\n    &.showing-form {\n      flex-direction: column;\n      flex-grow: 1;\n\n      &>.region {\n        margin: 0;\n      }\n\n      &>.select-credential-container{\n      display:flex;\n      flex-direction: column;\n      flex-grow: 1;\n      }\n    }\n\n    &>.select-credential-container{\n      flex-basis: 50%;\n\n      &>.select-credential{\n        flex-grow: 1;\n      }\n\n    }\n  }\n\n</style>\n"]}]}