{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/RuleSelector.vue?vue&type=style&index=0&id=c5ef2e9a&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/RuleSelector.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5ydWxlLXNlbGVjdG9yIHsKICAmOm5vdCgudmlldykgdGFibGUgewogICAgdGFibGUtbGF5b3V0OiBpbml0aWFsOwogIH0KCiAgIDpkZWVwKCkgLmJveCB7CiAgICBkaXNwbGF5OiBncmlkOwogICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAyNSUgMjUlIDI1JSAxNSU7CiAgICBjb2x1bW4tZ2FwOiAxLjc1JTsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBtYXJnaW4tYm90dG9tOiAxMHB4OwoKICAgIC5rZXksCiAgICAudmFsdWUsCiAgICAub3BlcmF0b3IgewogICAgICBoZWlnaHQ6IDEwMCU7CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/RuleSelector.vue"], "names": [], "mappings": ";AAoKA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB;;GAEC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEnB,CAAC,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACd;EACF;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/RuleSelector.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { _VIEW } from '@shell/config/query-params';\nimport ArrayList from '@shell/components/form/ArrayList';\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport Select from '@shell/components/form/Select';\n\nconst OPERATOR_VALUES = {\n  IS_SET:      'Exists',\n  IS_NOT_SET:  'DoesNotExist',\n  IN_LIST:     'In',\n  NOT_IN_LIST: 'NotIn'\n};\n\nexport default {\n  emits: ['update:value', 'input'],\n\n  components: {\n    ArrayList,\n    LabeledInput,\n    Select\n  },\n\n  props: {\n    value: {\n      type:    Array,\n      default: null\n    },\n    mode: {\n      type:    String,\n      default: _VIEW\n    },\n    addLabel: {\n      type:     String,\n      required: true\n    }\n  },\n\n  data() {\n    return {\n      operatorOptions: [\n        {\n          label: 'is set',\n          value: OPERATOR_VALUES.IS_SET,\n        },\n        {\n          label: 'is not set',\n          value: OPERATOR_VALUES.IS_NOT_SET,\n        },\n        {\n          label: 'in list',\n          value: OPERATOR_VALUES.IN_LIST,\n        },\n        {\n          label: 'not in list',\n          value: OPERATOR_VALUES.NOT_IN_LIST,\n        }\n      ],\n      optionsWithValueDisabled: [\n        OPERATOR_VALUES.IS_SET,\n        OPERATOR_VALUES.IS_NOT_SET\n      ],\n      defaultAddValue: {\n        key:      '',\n        operator: OPERATOR_VALUES.IS_SET,\n      }\n    };\n  },\n\n  computed: {\n    localValue: {\n      get() {\n        return this.value;\n      },\n      set(localValue) {\n        this.$emit('update:value', localValue);\n      }\n    }\n  },\n\n  methods: {\n    onOperatorInput(scope, operator) {\n      scope.row.value.operator = operator;\n      if (this.optionsWithValueDisabled.includes(operator)) {\n        if (scope.row.value.values) {\n          delete scope.row.value.values;\n        }\n      } else {\n        scope.row.value.values = scope.row.value.values || [];\n      }\n      scope.queueUpdate();\n    },\n\n    isValueDisabled(scope) {\n      return this.optionsWithValueDisabled.includes(scope.row.value.operator);\n    },\n    getValue(scope) {\n      return scope.row.value.values?.join(',') || '';\n    },\n    onValueInput(scope, rawValue) {\n      scope.row.value.values = rawValue.split(',')\n        .map((entry) => entry.trim());\n      scope.queueUpdate();\n    }\n  },\n};\n</script>\n\n<template>\n  <div\n    class=\"rule-selector\"\n    :class=\"{[mode]: true}\"\n  >\n    <ArrayList\n      :value=\"value\"\n      :protip=\"false\"\n      :show-header=\"true\"\n      :add-label=\"addLabel\"\n      :default-add-value=\"defaultAddValue\"\n      :mode=\"mode\"\n      @update:value=\"$emit('input', $event)\"\n    >\n      <template v-slot:column-headers>\n        <div class=\"box\">\n          <div class=\"key\">\n            Key\n          </div>\n          <div class=\"operator\">\n            Operator\n          </div>\n          <div class=\"value\">\n            Value\n          </div>\n          <div />\n        </div>\n      </template>\n      <template v-slot:columns=\"scope\">\n        <div class=\"key\">\n          <LabeledInput\n            v-model:value=\"scope.row.value.key\"\n            :mode=\"mode\"\n          />\n        </div>\n        <div class=\"operator\">\n          <Select\n            :mode=\"mode\"\n            :value=\"scope.row.value.operator\"\n            :options=\"operatorOptions\"\n            @update:value=\"onOperatorInput(scope, $event)\"\n          />\n        </div>\n        <div class=\"value\">\n          <LabeledInput\n            :disabled=\"isValueDisabled(scope)\"\n            :value=\"getValue(scope)\"\n            :mode=\"mode\"\n            @update:value=\"onValueInput(scope, $event)\"\n          />\n        </div>\n      </template>\n    </ArrayList>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.rule-selector {\n  &:not(.view) table {\n    table-layout: initial;\n  }\n\n   :deep() .box {\n    display: grid;\n    grid-template-columns: 25% 25% 25% 15%;\n    column-gap: 1.75%;\n    align-items: center;\n    margin-bottom: 10px;\n\n    .key,\n    .value,\n    .operator {\n      height: 100%;\n    }\n  }\n}\n</style>\n"]}]}