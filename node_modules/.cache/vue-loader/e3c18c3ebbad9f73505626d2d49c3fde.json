{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetBundleResources.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetBundleResources.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetBundleResources.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAA<PERSON>,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE/C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;EAE7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACnB;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC9B,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEnC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL;UACE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QAChB,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC;MACH,CAAC;IACH,CAAC;EACH;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetBundleResources.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { colorForState, stateDisplay, stateSort } from '@shell/plugins/dashboard-store/resource-class';\nimport SortableTable from '@shell/components/SortableTable';\nimport { randomStr } from '@shell/utils/string';\n\nexport default {\n  name: 'FleetBundleResources',\n\n  components: { SortableTable },\n\n  props: {\n    value: {\n      type:    Array,\n      default: () => [],\n    }\n  },\n\n  computed: {\n    computedResources() {\n      return this.value.map((item) => {\n        const { state } = item;\n        const color = colorForState(state).replace('text-', 'bg-');\n        const display = stateDisplay(state);\n\n        return {\n          ...item,\n          tableKey:        randomStr(),\n          stateBackground: color,\n          stateDisplay:    display,\n          stateSort:       stateSort(color, display),\n        };\n      });\n    },\n\n    resourceHeaders() {\n      return [\n        {\n          name:      'state',\n          value:     'state',\n          label:     'State',\n          sort:      'stateSort',\n          formatter: 'BadgeStateFormatter',\n          width:     100,\n        },\n        {\n          name:  'apiVersion',\n          value: 'apiVersion',\n          sort:  'apiVersion',\n          label: 'API Version',\n        },\n        {\n          name:  'kind',\n          value: 'kind',\n          sort:  'kind',\n          label: 'Kind',\n        },\n        {\n          name:      'name',\n          value:     'name',\n          sort:      'name',\n          label:     'Name',\n          formatter: 'LinkDetail',\n        },\n        {\n          name:  'namespace',\n          value: 'namespace',\n          sort:  'namespace',\n          label: 'Namespace',\n        },\n      ];\n    },\n  }\n};\n</script>\n\n<template>\n  <SortableTable\n    :rows=\"computedResources\"\n    :headers=\"resourceHeaders\"\n    :table-actions=\"false\"\n    :row-actions=\"false\"\n    key-field=\"tableKey\"\n    default-sort-by=\"state\"\n    :paged=\"true\"\n  />\n</template>\n"]}]}