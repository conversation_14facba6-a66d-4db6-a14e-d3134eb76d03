{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Setting.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Setting.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCBBY3Rpb25NZW51IGZyb20gJ0BzaGVsbC9jb21wb25lbnRzL0FjdGlvbk1lbnVTaGVsbC52dWUnOwppbXBvcnQgeyBtYXBHZXR0ZXJzIH0gZnJvbSAndnVleCc7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAgICAgICAnU2V0dGluZycsCiAgY29tcG9uZW50czogeyBBY3Rpb25NZW51IH0sCiAgcHJvcHM6ICAgICAgewogICAgdmFsdWU6IHsKICAgICAgdHlwZTogICAgIE9iamVjdCwKICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICB9LAogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIC4uLm1hcEdldHRlcnMoeyB0OiAnaTE4bi90JyB9KSwKICAgIC4uLm1hcEdldHRlcnMoeyBvcHRpb25zOiAnYWN0aW9uLW1lbnUvb3B0aW9uc0FycmF5JyB9KSwKICB9LAp9Owo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Setting.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACxD,CAAC;AACH,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Setting.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport ActionMenu from '@shell/components/ActionMenuShell.vue';\nimport { mapGetters } from 'vuex';\nexport default {\n  name:       'Setting',\n  components: { ActionMenu },\n  props:      {\n    value: {\n      type:     Object,\n      required: true,\n    },\n  },\n  computed: {\n    ...mapGetters({ t: 'i18n/t' }),\n    ...mapGetters({ options: 'action-menu/optionsArray' }),\n  },\n};\n</script>\n\n<template>\n  <div\n    class=\"advanced-setting mb-20\"\n    :data-testid=\"`advanced-setting__option-${value.id}`\"\n  >\n    <div class=\"header\">\n      <div class=\"title\">\n        <h1>\n          {{ value.id }}\n          <span\n            v-if=\"value.fromEnv\"\n            class=\"modified\"\n          >{{ t('advancedSettings.setEnv') }}</span>\n          <span\n            v-else-if=\"value.customized\"\n            class=\"modified\"\n          >{{ t('advancedSettings.modified') }}</span>\n        </h1>\n        <h2>{{ t(`advancedSettings.descriptions.${value.id}`) }}</h2>\n      </div>\n      <div\n        v-if=\"value.hasActions\"\n        class=\"action\"\n      >\n        <action-menu\n          :resource=\"value.data\"\n          :button-aria-label=\"t('advancedSettings.edit.label')\"\n          data-testid=\"action-button\"\n          button-role=\"tertiary\"\n        />\n      </div>\n    </div>\n    <div value>\n      <div v-if=\"value.canHide\">\n        <button\n          class=\"btn btn-sm role-primary\"\n          role=\"button\"\n          :aria-label=\"t('advancedSettings.hideShow')\"\n          @click=\"value.hide = !value.hide\"\n        >\n          {{ value.hide ? t('advancedSettings.show') : t('advancedSettings.hide') }} {{ value.id }}\n        </button>\n      </div>\n      <div\n        v-show=\"!value.canHide || (value.canHide && !value.hide)\"\n        class=\"settings-value\"\n      >\n        <pre v-if=\"value.kind === 'json'\">{{ value.json }}</pre>\n        <pre v-else-if=\"value.kind === 'multiline'\">{{ value.data.value || value.data.default }}</pre>\n        <pre v-else-if=\"value.kind === 'enum'\">{{ t(value.enum) }}</pre>\n        <pre v-else-if=\"value.data.value || value.data.default\">{{ value.data.value || value.data.default }}</pre>\n        <pre\n          v-else\n          class=\"text-muted\"\n        >&lt;{{ t('advancedSettings.none') }}&gt;</pre>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang='scss' scoped>\n.settings-value pre {\n  margin: 0;\n}\n.advanced-setting {\n  border: 1px solid var(--border);\n  padding: 20px;\n  border-radius: var(--border-radius);\n\n  h1 {\n    font-size: 14px;\n  }\n  h2 {\n    font-size: 12px;\n    margin-bottom: 0;\n    opacity: 0.8;\n  }\n}\n\n.header {\n  display: flex;\n  margin-bottom: 20px;\n}\n\n.title {\n  flex: 1;\n}\n\n.modified {\n  margin-left: 10px;\n  border: 1px solid var(--primary);\n  border-radius: 5px;\n  padding: 2px 10px;\n  font-size: 12px;\n}\n</style>\n"]}]}