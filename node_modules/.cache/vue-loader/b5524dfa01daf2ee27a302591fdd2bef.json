{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Header.vue?vue&type=style&index=0&id=7e5cb63c&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Header.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Header.vue"], "names": [], "mappings": ";EAovBE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAClH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEnC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE9B,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACT;;IAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C;MACF;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC;MACF;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEhB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACd;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACnB;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB;IACF;;IAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEb,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;QAEV,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACd;MACF;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACjB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB;;IAEA,EAAE,EAAE;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrE;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;MAEV,EAAE,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MAChB;;MAEA,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;QAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;QAEf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACpB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAClB;;QAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B;;UAEA,CAAC,CAAC,EAAE;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb;QACF;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEhB,EAAE;UACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACnH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACd;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEb,EAAE,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB;QACF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACZ;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACpB;;MAEA,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACf;UACF;QACF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cAClB;cACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE;gBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB;YACF;UACF;QACF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC;;MAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACrB;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,EAAE;MACD,EAAE;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf;IACF;EACF;;EAEA,CAAC,CAAC,EAAE;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,EAAE;MACD,EAAE;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEnB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACf;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB;MACF;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACxB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACvB;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACnF,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnB;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;;MAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACb;IACF;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Header.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapGetters } from 'vuex';\nimport debounce from 'lodash/debounce';\nimport { MANAGEMENT, NORMAN, STEVE } from '@shell/config/types';\nimport { HARVESTER_NAME as HARVESTER } from '@shell/config/features';\nimport { ucFirst } from '@shell/utils/string';\nimport { isAlternate, isMac } from '@shell/utils/platform';\nimport Import from '@shell/components/Import';\nimport BrandImage from '@shell/components/BrandImage';\nimport { getProduct, getVendor } from '@shell/config/private-label';\nimport ClusterProviderIcon from '@shell/components/ClusterProviderIcon';\nimport ClusterBadge from '@shell/components/ClusterBadge';\nimport AppModal from '@shell/components/AppModal';\nimport { LOGGED_OUT, IS_SSO } from '@shell/config/query-params';\nimport NamespaceFilter from './NamespaceFilter';\nimport WorkspaceSwitcher from './WorkspaceSwitcher';\nimport TopLevelMenu from './TopLevelMenu';\n\nimport Jump from './Jump';\nimport { allHash } from '@shell/utils/promise';\nimport { ActionLocation, ExtensionPoint } from '@shell/core/types';\nimport { getApplicableExtensionEnhancements } from '@shell/core/plugin-helpers';\nimport IconOrSvg from '@shell/components/IconOrSvg';\nimport { wait } from '@shell/utils/async';\nimport { configType } from '@shell/models/management.cattle.io.authconfig';\nimport HeaderPageActionMenu from './HeaderPageActionMenu.vue';\nimport {\n  RcDropdown,\n  RcDropdownItem,\n  RcDropdownSeparator,\n  RcDropdownTrigger\n} from '@components/RcDropdown';\n\nexport default {\n\n  components: {\n    NamespaceFilter,\n    WorkspaceSwitcher,\n    Import,\n    TopLevelMenu,\n    Jump,\n    BrandImage,\n    ClusterBadge,\n    ClusterProviderIcon,\n    IconOrSvg,\n    AppModal,\n    HeaderPageActionMenu,\n    RcDropdown,\n    RcDropdownItem,\n    RcDropdownSeparator,\n    RcDropdownTrigger,\n  },\n\n  props: {\n    simple: {\n      type:    Boolean,\n      default: false\n    }\n  },\n\n  fetch() {\n    // fetch needed data to check if any auth provider is enabled\n    this.$store.dispatch('auth/getAuthProviders');\n  },\n\n  data() {\n    const searchShortcut = isMac ? '(\\u2318-K)' : '(Ctrl+K)';\n    const shellShortcut = '(Ctrl+`)';\n\n    return {\n      authInfo:               {},\n      show:                   false,\n      showTooltip:            false,\n      isUserMenuOpen:         false,\n      isPageActionMenuOpen:   false,\n      kubeConfigCopying:      false,\n      searchShortcut,\n      shellShortcut,\n      LOGGED_OUT,\n      navHeaderRight:         null,\n      extensionHeaderActions: getApplicableExtensionEnhancements(this, ExtensionPoint.ACTION, ActionLocation.HEADER, this.$route),\n      ctx:                    this,\n      showImportModal:        false,\n      showSearchModal:        false\n    };\n  },\n\n  computed: {\n    ...mapGetters([\n      'clusterReady',\n      'isExplorer',\n      'isRancher',\n      'currentCluster',\n      'currentProduct',\n      'rootProduct',\n      'backToRancherLink',\n      'backToRancherGlobalLink',\n      'pageActions',\n      'isSingleProduct',\n      'isRancherInHarvester',\n      'showTopLevelMenu',\n      'isMultiCluster'\n    ]),\n\n    samlAuthProviderEnabled() {\n      const publicAuthProviders = this.$store.getters['rancher/all']('authProvider');\n\n      return publicAuthProviders.find((authProvider) => configType[authProvider.id] === 'saml') || {};\n    },\n\n    shouldShowSloLogoutModal() {\n      if (this.isAuthLocalProvider) {\n        // If the user logged in as a local user... they cannot log out as if they were an auth config user\n        return false;\n      }\n\n      const { logoutAllSupported, logoutAllEnabled, logoutAllForced } = this.samlAuthProviderEnabled;\n\n      return logoutAllSupported && logoutAllEnabled && !logoutAllForced;\n    },\n\n    appName() {\n      return getProduct();\n    },\n\n    vendor() {\n      this.$store.getters['management/all'](MANAGEMENT.SETTING)?.find((setting) => setting.id === 'ui-pl');\n\n      return getVendor();\n    },\n\n    authEnabled() {\n      return this.$store.getters['auth/enabled'];\n    },\n\n    isAuthLocalProvider() {\n      return this.principal && this.principal.provider === 'local';\n    },\n\n    generateLogoutRoute() {\n      return this.isAuthLocalProvider ? { name: 'auth-logout', query: { [LOGGED_OUT]: true } } : { name: 'auth-logout', query: { [LOGGED_OUT]: true, [IS_SSO]: true } };\n    },\n\n    principal() {\n      return this.$store.getters['rancher/byId'](NORMAN.PRINCIPAL, this.$store.getters['auth/principalId']) || {};\n    },\n\n    kubeConfigEnabled() {\n      return true;\n    },\n\n    shellEnabled() {\n      return !!this.currentCluster?.links?.shell;\n    },\n\n    showKubeShell() {\n      return !this.rootProduct?.hideKubeShell;\n    },\n\n    showKubeConfig() {\n      return !this.rootProduct?.hideKubeConfig;\n    },\n\n    showCopyConfig() {\n      return !this.rootProduct?.hideCopyConfig;\n    },\n\n    showPreferencesLink() {\n      return (this.$store.getters['management/schemaFor'](STEVE.PREFERENCE, false, false)?.resourceMethods || []).includes('PUT');\n    },\n\n    showAccountAndApiKeyLink() {\n      // Keep this simple for the moment and only check if the user can see tokens... plus the usual isRancher/isSingleProduct\n      const canSeeTokens = this.$store.getters['rancher/schemaFor'](NORMAN.TOKEN, false, false);\n\n      return canSeeTokens && (this.isRancher || this.isSingleProduct);\n    },\n\n    showPageActions() {\n      return !this.featureRancherDesktop && this.pageActions && this.pageActions.length;\n    },\n\n    showUserMenu() {\n      return !this.featureRancherDesktop;\n    },\n\n    showFilter() {\n      // Some products won't have a current cluster\n      const validClusterOrProduct = this.currentCluster ||\n                 (this.currentProduct && this.currentProduct.customNamespaceFilter) ||\n                 (this.currentProduct && this.currentProduct.showWorkspaceSwitcher);\n      // Don't show if the header is in 'simple' mode\n      const notSimple = !this.simple;\n      // One of these must be enabled, otherwise t here's no component to show\n      const validFilterSettings = this.currentProduct?.showNamespaceFilter || this.currentProduct?.showWorkspaceSwitcher;\n\n      return validClusterOrProduct && notSimple && validFilterSettings;\n    },\n\n    featureRancherDesktop() {\n      return this.$config.rancherEnv === 'desktop';\n    },\n\n    importEnabled() {\n      return !!this.currentCluster?.actions?.apply;\n    },\n\n    prod() {\n      const name = this.rootProduct.name;\n\n      return this.$store.getters['i18n/withFallback'](`product.\"${ name }\"`, null, ucFirst(name));\n    },\n\n    showSearch() {\n      return this.rootProduct?.inStore === 'cluster';\n    },\n\n    showImportYaml() {\n      return this.rootProduct?.inStore !== 'harvester';\n    },\n\n    nameTooltip() {\n      return !this.showTooltip ? {} : {\n        content: this.currentCluster?.nameDisplay,\n        delay:   400,\n      };\n    },\n\n    singleProductLogoRoute() {\n      const cluster = this.$store.getters.defaultClusterId;\n\n      return {\n        ...this.isSingleProduct.logoRoute,\n        params: {\n          cluster,\n          ...this.isSingleProduct.logoRoute.params,\n        }\n      };\n    },\n\n    isHarvester() {\n      return this.$store.getters['currentProduct'].inStore === HARVESTER;\n    },\n  },\n\n  watch: {\n    currentCluster(nue, old) {\n      if (nue && old && nue.id !== old.id) {\n        this.checkClusterName();\n      }\n    },\n    // since the Header is a \"persistent component\" we need to update it at every route change...\n    $route: {\n      handler(nue) {\n        if (nue) {\n          this.extensionHeaderActions = getApplicableExtensionEnhancements(this, ExtensionPoint.ACTION, ActionLocation.HEADER, nue);\n\n          this.navHeaderRight = this.$plugin?.getDynamic('component', 'NavHeaderRight');\n        }\n      },\n      immediate: true,\n      deep:      true,\n    }\n  },\n\n  mounted() {\n    this.checkClusterName();\n    this.debouncedLayoutHeader = debounce(this.layoutHeader, 400);\n    window.addEventListener('resize', this.debouncedLayoutHeader);\n\n    this.$nextTick(() => this.layoutHeader(null, true));\n  },\n\n  beforeUnmount() {\n    window.removeEventListener('resize', this.debouncedLayoutHeader);\n  },\n\n  methods: {\n    showSloModal() {\n      this.$store.dispatch('management/promptModal', {\n        component:      'SloDialog',\n        componentProps: { authProvider: this.samlAuthProviderEnabled },\n        modalWidth:     '500px'\n      });\n    },\n    // Sizes the product area of the header such that it shrinks to ensure the whole header bar can be shown\n    // where possible - we use a minimum width of 32px which is enough to just show the product icon\n    layoutHeader() {\n      const header = this.$refs.header;\n      const product = this.$refs.product;\n\n      if (!header || !product) {\n        return;\n      }\n\n      // If the product element has an exact size, remove it and then recalculate\n      if (product.style.width) {\n        product.style.width = '';\n\n        this.$nextTick(() => this.layoutHeader());\n\n        return;\n      }\n\n      const overflow = header.scrollWidth - window.innerWidth;\n\n      if (overflow > 0) {\n        const w = Math.max(32, product.offsetWidth - overflow);\n\n        // Set exact width on the product div so that the content in it fits that available space\n        product.style.width = `${ w }px`;\n      }\n    },\n    showMenu(show) {\n      this.isUserMenuOpen = show;\n    },\n\n    openImport() {\n      this.showImportModal = true;\n    },\n\n    closeImport() {\n      this.showImportModal = false;\n    },\n\n    openSearch() {\n      this.showSearchModal = true;\n    },\n\n    hideSearch() {\n      this.showSearchModal = false;\n    },\n\n    checkClusterName() {\n      this.$nextTick(() => {\n        const el = this.$refs.clusterName;\n\n        this.showTooltip = el && (el.clientWidth < el.scrollWidth);\n      });\n    },\n\n    copyKubeConfig(event) {\n      const button = event.target?.parentElement;\n\n      if (this.kubeConfigCopying) {\n        return;\n      }\n\n      this.kubeConfigCopying = true;\n\n      if (button) {\n        button.classList.add('header-btn-active');\n      }\n\n      // Make sure we wait at least 1 second so that the user can see the visual indication that the config has been copied\n      allHash({\n        copy:     this.currentCluster.copyKubeConfig(),\n        minDelay: wait(1000),\n      }).finally(() => {\n        this.kubeConfigCopying = false;\n\n        if (button) {\n          button.classList.remove('header-btn-active');\n        }\n      });\n    },\n\n    handleExtensionAction(action, event) {\n      const fn = action.invoke;\n      const opts = {\n        event,\n        action,\n        isAlt:   isAlternate(event),\n        product: this.currentProduct.name,\n        cluster: this.currentCluster,\n      };\n      const enabled = action.enabled ? action.enabled.apply(this, [this.ctx]) : true;\n\n      if (fn && enabled) {\n        fn.apply(this, [opts, [], { $route: this.$route }]);\n      }\n    },\n\n    handleExtensionTooltip(action) {\n      if (action.tooltipKey || action.tooltip) {\n        const tooltip = action.tooltipKey ? this.t(action.tooltipKey) : action.tooltip;\n        const shortcut = action.shortcutLabel ? action.shortcutLabel() : '';\n\n        return `${ tooltip } ${ shortcut }`;\n      }\n\n      return null;\n    }\n  }\n};\n</script>\n\n<template>\n  <header\n    ref=\"header\"\n    data-testid=\"header\"\n  >\n    <div>\n      <TopLevelMenu v-if=\"isRancherInHarvester || isMultiCluster || !isSingleProduct\" />\n    </div>\n\n    <div\n      class=\"menu-spacer\"\n      :class=\"{'isSingleProduct': isSingleProduct }\"\n    >\n      <router-link\n        v-if=\"isSingleProduct && !isRancherInHarvester\"\n        :to=\"singleProductLogoRoute\"\n        role=\"link\"\n        :alt=\"t('branding.logos.home')\"\n      >\n        <BrandImage\n          v-if=\"isSingleProduct.supportCustomLogo && isHarvester\"\n          class=\"side-menu-logo\"\n          file-name=\"harvester.svg\"\n          :support-custom-logo=\"true\"\n          :alt=\"t('branding.logos.label')\"\n        />\n        <img\n          v-else\n          class=\"side-menu-logo\"\n          :src=\"isSingleProduct.logo\"\n          :alt=\"t('branding.logos.label')\"\n        >\n      </router-link>\n    </div>\n\n    <div\n      v-if=\"!simple\"\n      ref=\"product\"\n      class=\"product\"\n    >\n      <div\n        v-if=\"currentProduct && currentProduct.showClusterSwitcher\"\n        v-clean-tooltip=\"nameTooltip\"\n        class=\"cluster cluster-clipped\"\n      >\n        <div\n          v-if=\"isSingleProduct && !isRancherInHarvester\"\n          class=\"product-name\"\n        >\n          <template v-if=\"isSingleProduct.supportCustomLogo\">\n            {{ vendor }}\n          </template>\n          <template v-else>\n            {{ t(isSingleProduct.productNameKey) }}\n          </template>\n        </div>\n        <template v-else>\n          <ClusterProviderIcon\n            v-if=\"currentCluster\"\n            :cluster=\"currentCluster\"\n            class=\"mr-10\"\n            :alt=\"t('branding.logos.label')\"\n          />\n          <div\n            v-if=\"currentCluster\"\n            ref=\"clusterName\"\n            class=\"cluster-name\"\n          >\n            {{ currentCluster.spec.displayName }}\n          </div>\n          <ClusterBadge\n            v-if=\"currentCluster\"\n            :cluster=\"currentCluster\"\n            class=\"ml-10\"\n            :alt=\"t('branding.logos.label')\"\n          />\n          <div\n            v-if=\"!currentCluster\"\n            class=\"simple-title\"\n          >\n            <BrandImage\n              class=\"side-menu-logo-img\"\n              file-name=\"rancher-logo.svg\"\n              :alt=\"t('branding.logos.label')\"\n            />\n          </div>\n        </template>\n      </div>\n      <div\n        v-if=\"currentProduct && !currentProduct.showClusterSwitcher\"\n        class=\"cluster\"\n      >\n        <img\n          v-if=\"currentProduct.iconHeader\"\n          v-bind=\"$attrs\"\n          :src=\"currentProduct.iconHeader\"\n          class=\"cluster-os-logo mr-10\"\n          style=\"width: 44px; height: 36px;\"\n          :alt=\"t('branding.logos.label')\"\n        >\n        <div class=\"product-name\">\n          {{ prod }}\n        </div>\n      </div>\n    </div>\n\n    <div\n      v-else\n      class=\"simple-title\"\n    >\n      <div\n        v-if=\"isSingleProduct\"\n        class=\"product-name\"\n      >\n        {{ t(isSingleProduct.productNameKey) }}\n      </div>\n\n      <div\n        v-else\n        class=\"side-menu-logo\"\n      >\n        <BrandImage\n          class=\"side-menu-logo-img\"\n          data-testid=\"header__brand-img\"\n          file-name=\"rancher-logo.svg\"\n          :alt=\"t('branding.logos.label')\"\n        />\n      </div>\n    </div>\n\n    <div class=\"spacer\" />\n\n    <div class=\"rd-header-right\">\n      <component :is=\"navHeaderRight\" />\n      <div\n        v-if=\"showFilter\"\n        class=\"top\"\n      >\n        <NamespaceFilter v-if=\"clusterReady && currentProduct && (currentProduct.showNamespaceFilter || isExplorer)\" />\n        <WorkspaceSwitcher v-else-if=\"clusterReady && currentProduct && currentProduct.showWorkspaceSwitcher\" />\n      </div>\n      <div\n        v-if=\"currentCluster && !simple\"\n        class=\"header-buttons\"\n      >\n        <template v-if=\"currentProduct && currentProduct.showClusterSwitcher\">\n          <button\n            v-if=\"showImportYaml\"\n            v-clean-tooltip=\"t('nav.import')\"\n            :disabled=\"!importEnabled\"\n            type=\"button\"\n            class=\"btn header-btn role-tertiary\"\n            data-testid=\"header-action-import-yaml\"\n            role=\"button\"\n            tabindex=\"0\"\n            :aria-label=\"t('nav.import')\"\n            @click=\"openImport()\"\n          >\n            <i class=\"icon icon-upload icon-lg\" />\n          </button>\n          <app-modal\n            v-if=\"showImportModal\"\n            class=\"import-modal\"\n            name=\"importModal\"\n            width=\"75%\"\n            height=\"auto\"\n            styles=\"max-height: 90vh;\"\n            @close=\"closeImport\"\n          >\n            <Import\n              :cluster=\"currentCluster\"\n              @close=\"closeImport\"\n            />\n          </app-modal>\n\n          <button\n            v-if=\"showKubeShell\"\n            id=\"btn-kubectl\"\n            v-clean-tooltip=\"t('nav.shellShortcut', {key: shellShortcut})\"\n            v-shortkey=\"{windows: ['ctrl', '`'], mac: ['meta', '`']}\"\n            :disabled=\"!shellEnabled\"\n            type=\"button\"\n            class=\"btn header-btn role-tertiary\"\n            role=\"button\"\n            tabindex=\"0\"\n            :aria-label=\"t('nav.shellShortcut', {key:''})\"\n            @shortkey=\"currentCluster.openShell()\"\n            @click=\"currentCluster.openShell()\"\n          >\n            <i class=\"icon icon-terminal icon-lg\" />\n          </button>\n\n          <button\n            v-if=\"showKubeConfig\"\n            v-clean-tooltip=\"t('nav.kubeconfig.download')\"\n            :disabled=\"!kubeConfigEnabled\"\n            type=\"button\"\n            class=\"btn header-btn role-tertiary\"\n            data-testid=\"btn-download-kubeconfig\"\n            role=\"button\"\n            tabindex=\"0\"\n            :aria-label=\"t('nav.kubeconfig.download')\"\n            @click=\"currentCluster.downloadKubeConfig()\"\n          >\n            <i class=\"icon icon-file icon-lg\" />\n          </button>\n\n          <button\n            v-if=\"showCopyConfig\"\n            v-clean-tooltip=\"t('nav.kubeconfig.copy')\"\n            :disabled=\"!kubeConfigEnabled\"\n            type=\"button\"\n            class=\"btn header-btn role-tertiary\"\n            data-testid=\"btn-copy-kubeconfig\"\n            role=\"button\"\n            tabindex=\"0\"\n            :aria-label=\"t('nav.kubeconfig.copy')\"\n            @click=\"copyKubeConfig($event)\"\n          >\n            <i\n              v-if=\"kubeConfigCopying\"\n              class=\"icon icon-checkmark icon-lg\"\n            />\n            <i\n              v-else\n              class=\"icon icon-copy icon-lg\"\n            />\n          </button>\n        </template>\n\n        <button\n          v-if=\"showSearch\"\n          id=\"header-btn-search\"\n          v-clean-tooltip=\"t('nav.resourceSearch.toolTip', {key: searchShortcut})\"\n          v-shortkey=\"{windows: ['ctrl', 'k'], mac: ['meta', 'k']}\"\n          type=\"button\"\n          class=\"btn header-btn role-tertiary\"\n          data-testid=\"header-resource-search\"\n          role=\"button\"\n          tabindex=\"0\"\n          :aria-label=\"t('nav.resourceSearch.toolTip', {key: ''})\"\n          @shortkey=\"openSearch()\"\n          @click=\"openSearch()\"\n        >\n          <i class=\"icon icon-search icon-lg\" />\n        </button>\n        <app-modal\n          v-if=\"showSearch && showSearchModal\"\n          class=\"search-modal\"\n          name=\"searchModal\"\n          width=\"50%\"\n          height=\"auto\"\n          :trigger-focus-trap=\"true\"\n          return-focus-selector=\"#header-btn-search\"\n          @close=\"hideSearch()\"\n        >\n          <Jump @closeSearch=\"hideSearch()\" />\n        </app-modal>\n      </div>\n\n      <!-- Extension header actions -->\n      <div\n        v-if=\"extensionHeaderActions.length\"\n        class=\"header-buttons\"\n      >\n        <button\n          v-for=\"action, i in extensionHeaderActions\"\n          :key=\"`${action.label}${i}`\"\n          v-clean-tooltip=\"handleExtensionTooltip(action)\"\n          v-shortkey=\"action.shortcutKey\"\n          :disabled=\"action.enabled ? !action.enabled(ctx) : false\"\n          type=\"button\"\n          class=\"btn header-btn role-tertiary\"\n          :data-testid=\"`extension-header-action-${ action.labelKey || action.label }`\"\n          role=\"button\"\n          tabindex=\"0\"\n          :aria-label=\"action.label\"\n          @shortkey=\"handleExtensionAction(action, $event)\"\n          @click=\"handleExtensionAction(action, $event)\"\n        >\n          <IconOrSvg\n            class=\"icon icon-lg\"\n            :icon=\"action.icon\"\n            :src=\"action.svg\"\n            color=\"header\"\n          />\n        </button>\n      </div>\n\n      <div class=\"center-self\">\n        <header-page-action-menu v-if=\"showPageActions\" />\n        <rc-dropdown\n          v-if=\"showUserMenu\"\n          :aria-label=\"t('nav.userMenu.label')\"\n        >\n          <rc-dropdown-trigger\n            ghost\n            small\n            data-testid=\"nav_header_showUserMenu\"\n            :aria-label=\"t('nav.userMenu.button.label')\"\n          >\n            <img\n              v-if=\"principal && principal.avatarSrc\"\n              :src=\"principal.avatarSrc\"\n              :class=\"{'avatar-round': principal.roundAvatar}\"\n              width=\"36\"\n              height=\"36\"\n            >\n            <i\n              v-else\n              class=\"icon icon-user icon-3x avatar\"\n            />\n          </rc-dropdown-trigger>\n          <template #dropdownCollection>\n            <template v-if=\"authEnabled\">\n              <div class=\"user-info\">\n                <div class=\"user-name\">\n                  <i class=\"icon icon-lg icon-user\" /> {{ principal.loginName }}\n                </div>\n                <div class=\"text-small\">\n                  <template v-if=\"principal.loginName !== principal.name\">\n                    {{ principal.name }}\n                  </template>\n                </div>\n              </div>\n              <rc-dropdown-separator />\n            </template>\n            <rc-dropdown-item\n              v-if=\"showPreferencesLink\"\n              @click=\"$router.push({ name: 'prefs'})\"\n            >\n              {{ t('nav.userMenu.preferences') }}\n            </rc-dropdown-item>\n            <rc-dropdown-item\n              v-if=\"showAccountAndApiKeyLink\"\n              @click=\"$router.push({ name: 'account'})\"\n            >\n              {{ t('nav.userMenu.accountAndKeys', {}, true) }}\n            </rc-dropdown-item>\n            <rc-dropdown-item\n              v-if=\"authEnabled && shouldShowSloLogoutModal\"\n              @click=\"showSloModal\"\n            >\n              {{ t('nav.userMenu.logOut') }}\n            </rc-dropdown-item>\n            <rc-dropdown-item\n              v-else-if=\"authEnabled\"\n              @click=\"$router.push(generateLogoutRoute)\"\n            >\n              {{ t('nav.userMenu.logOut') }}\n            </rc-dropdown-item>\n          </template>\n        </rc-dropdown>\n      </div>\n    </div>\n  </header>\n</template>\n\n<style lang=\"scss\" scoped>\n  // It would be nice to grab this from `Group.vue`, but there's margin, padding and border, which is overkill to var\n  $side-menu-group-padding-left: 16px;\n\n  HEADER {\n    display: flex;\n    z-index: z-index('mainHeader');\n\n    > .spacer {\n      flex: 1;\n    }\n\n    > .menu-spacer {\n      flex: 0 0 15px;\n\n      &.isSingleProduct  {\n        display: flex;\n        justify-content: center;\n\n        // Align the icon with the side nav menu items ($side-menu-group-padding-left)\n        .side-menu-logo {\n          margin-left: $side-menu-group-padding-left;\n        }\n      }\n    }\n\n    .title {\n      border-left: 1px solid var(--header-border);\n      padding-left: 10px;\n      opacity: 0.7;\n      text-transform: uppercase;\n    }\n\n    .filter {\n      :deep() .labeled-select,\n      :deep() .unlabeled-select {\n        .vs__search::placeholder {\n          color: var(--body-text) !important;\n        }\n\n        .vs__dropdown-toggle .vs__actions:after {\n          color: var(--body-text) !important;\n        }\n\n        .vs__dropdown-toggle {\n          background: transparent;\n          border: 1px solid var(--header-border);\n        }\n      }\n    }\n\n    .back {\n      padding-top: 6px;\n\n      > *:first-child {\n        height: 40px;\n      }\n    }\n\n    .simple-title {\n      align-items: center;\n      display: flex;\n\n      .title {\n        height: 24px;\n        line-height: 24px;\n      }\n    }\n\n    .cluster {\n      align-items: center;\n      display: flex;\n      height: 32px;\n      white-space: nowrap;\n      .cluster-name {\n        font-size: 16px;\n        text-overflow: ellipsis;\n        overflow: hidden;\n      }\n      &.cluster-clipped {\n        overflow: hidden;\n      }\n    }\n\n    > .product {\n      align-items: center;\n      position: relative;\n      display: flex;\n\n      .logo {\n        height: 30px;\n        position: absolute;\n        top: 9px;\n        left: 0;\n        z-index: 2;\n\n        img {\n          height: 30px;\n        }\n      }\n    }\n\n    .product-name {\n      font-size: 16px;\n    }\n\n    .side-menu-logo {\n      align-items: center;\n      display: flex;\n      margin-right: 8px;\n      height: 55px;\n      margin-left: 5px;\n      max-width: 200px;\n      padding: 12px 0;\n    }\n\n    .side-menu-logo-img {\n      object-fit: contain;\n      height: 21px;\n      max-width: 200px;\n    }\n\n    > * {\n      background-color: var(--header-bg);\n      border-bottom: var(--header-border-size) solid var(--header-border);\n    }\n\n    .rd-header-right {\n      display: flex;\n      flex-direction: row;\n      padding: 0;\n\n      > * {\n        padding: 0 5px;\n      }\n\n      > .top {\n        padding-top: 6px;\n\n        INPUT[type='search']::placeholder,\n        .vs__open-indicator,\n        .vs__selected {\n          color: var(--header-btn-bg) !important;\n          background: var(--header-btn-bg);\n          border-radius: var(--border-radius);\n          border: none;\n          margin: 0 35px 0 25px!important;\n        }\n\n        .vs__selected {\n          background: rgba(255, 255, 255, 0.15);\n          border-color: rgba(255, 255, 255, 0.25);\n        }\n\n        .vs__deselect {\n          fill: var(--header-btn-bg);\n        }\n\n        .filter .vs__dropdown-toggle {\n          background: var(--header-btn-bg);\n          border-radius: var(--border-radius);\n          border: none;\n          margin: 0 35px 0 25px!important;\n        }\n      }\n\n      .header-buttons {\n        align-items: center;\n        display: flex;\n        margin-top: 1px;\n\n        // Spacing between header buttons\n        .btn:not(:last-of-type) {\n          margin-right: 10px;\n        }\n\n        .btn:focus {\n          box-shadow: none;\n        }\n\n        > .header-btn {\n          &.header-btn-active, &.header-btn-active:hover {\n            background-color: var(--success);\n            color: var(--success-text);\n          }\n\n          img {\n            height: 20px;\n            width: 20px;\n          }\n        }\n      }\n\n      .header-btn {\n        width: 40px;\n      }\n\n      :deep() div .btn.role-tertiary {\n        border: 1px solid var(--header-btn-bg);\n        border: none;\n        background: var(--header-btn-bg);\n        color: var(--header-btn-text);\n        padding: 0 10px;\n        line-height: 32px;\n        min-height: 32px;\n\n        i {\n          // Ideally same height as the parent button, but this means tooltip needs adjusting (which is it's own can of worms)\n          line-height: 20px;\n        }\n\n        &:hover {\n          background: var(--primary);\n          color: #fff;\n        }\n\n        &[disabled=disabled] {\n          background-color: rgba(0,0,0,0.25) !important;\n          color: var(--header-btn-text) !important;\n          opacity: 0.7;\n        }\n      }\n\n      :deep(.actions) {\n        align-items: center;\n        cursor: pointer;\n        display: flex;\n\n        > I {\n          font-size: 18px;\n          padding: 6px;\n          &:hover {\n            color: var(--link);\n          }\n        }\n\n        :deep(.v-popper:focus) {\n          outline: 0;\n        }\n\n        .dropdown {\n          margin: 0 -10px;\n        }\n      }\n\n      .header-spacer {\n        background-color: var(--header-bg);\n        position: relative;\n      }\n\n      .avatar-round {\n        border: 0;\n        border-radius: 50%;\n      }\n\n      > .user {\n        outline: none;\n        width: var(--header-height);\n\n        .v-popper {\n          display: flex;\n          :deep() .trigger{\n          .user-image {\n              display: flex;\n            }\n          }\n        }\n\n        .user-image {\n          display: flex;\n          align-items: center;\n        }\n\n        &:focus {\n          .v-popper {\n            :deep() .trigger {\n              line-height: 0;\n              .user-image {\n                max-height: 40px;\n              }\n              .user-image > * {\n                @include form-focus\n              }\n            }\n          }\n        }\n\n        background-color: var(--header-bg);\n      }\n\n      > .center-self {\n        align-self: center;\n        display: flex;\n        gap: 1rem;\n        align-items: center;\n        padding-right: 1rem;\n      }\n    }\n  }\n\n  .list-unstyled {\n    li {\n      a {\n        display: flex;\n        justify-content: space-between;\n        padding: 10px;\n      }\n    }\n  }\n\n  div {\n    &.user-info {\n      padding: 0 8px;\n      margin: 0 9px;\n      min-width: 200px;\n      display: flex;\n      gap: 5px;\n      flex-direction: column;\n    }\n  }\n\n  .config-actions {\n    li {\n      a {\n        justify-content: start;\n        align-items: center;\n\n        & .icon {\n          margin: 0 4px;\n        }\n\n        &:hover {\n          cursor: pointer;\n        }\n      }\n    }\n  }\n\n  .v-popper__popper .v-popper__inner {\n    padding: 0;\n    border-radius: 0;\n  }\n\n  .user-name {\n    display: flex;\n    align-items: center;\n    color: var(--secondary);\n  }\n\n  .user-menu {\n    :deep(.v-popper__arrow-container) {\n      display: none;\n    }\n\n    :deep(.v-popper__inner) {\n      padding: 10px 0 10px 0;\n    }\n\n    :deep(.v-popper) {\n      display: flex;\n    }\n  }\n\n  .user-menu-item {\n    a, &.no-link > span {\n      cursor: pointer;\n      padding: 0px 10px;\n\n      &:hover {\n        background-color: var(--dropdown-hover-bg);\n        color: var(--dropdown-hover-text);\n        text-decoration: none;\n      }\n\n      // When the menu item is focused, pop the margin and compensate the padding, so that\n      // the focus border appears within the menu\n      &:focus {\n        margin: 0 2px;\n        padding: 10px 8px;\n      }\n    }\n\n    &.no-link > span {\n      display: flex;\n      justify-content: space-between;\n      padding: 10px;\n      color: var(--popover-text);\n    }\n\n    div.menu-separator {\n      cursor: default;\n      padding: 4px 0;\n\n      .menu-separator-line {\n        background-color: var(--border);\n        height: 1px;\n      }\n    }\n  }\n\n</style>\n"]}]}