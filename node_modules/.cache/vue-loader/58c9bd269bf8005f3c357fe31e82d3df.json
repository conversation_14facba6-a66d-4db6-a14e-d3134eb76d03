{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/RoleDetailEdit.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/RoleDetailEdit.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/RoleDetailEdit.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9E,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEzE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE/C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE/C,CAAC,CAAC;CACD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;CACrC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAClC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACpC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACjC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACxC;CACA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;CAClE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACV,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACpB;CACA,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC3E;CACA,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;CAC5C;CACA,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC3D,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC1E,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;CACpD,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC7C,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;CAC3C,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC/D,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC5C,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACvD,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC1D,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAChD,CAAC;AACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAExC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACrE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC3F,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACrF,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpF,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxF,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACxE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACvF,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEnE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC3G,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC;MACH,CAAC,CAAC;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjE;IACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;MACpB,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ;QACpB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC7C,CAAC;IACH,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACtC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE7B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC;;IAEA,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEhF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C,CAAC,CAAC,CAAC,CAAC,CAAC;IACP;;IAEA,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC5B,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClF,CAAC,CAAC;IACJ;;IAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtD;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC,CAAC;EACJ,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3E,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C;MACF,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;MAElB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEhD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACzF,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACjD,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR;QACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACnH,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACtD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR;;QAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEpD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEnD,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAClC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/D,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE5B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACpC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACnD,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAClD,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAElD,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAC1C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAE1H,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;UACp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lB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC;;UAEF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE3E,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM;gBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACd;YACF,CAAC,CAAC;UACJ,CAAC,CAAC;;UAEF,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC7D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/F,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEvF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;cACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM;kBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACd;cACF,CAAC,CAAC;YACJ,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;;MAEF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE9D,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7D,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACvD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACzD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE/D,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACxD,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC;;MAEF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxE,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD;MACF,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3E,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5G,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC3D,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACvC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC;;MAEH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd;UACE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB;MACF,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzD;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAEP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACxB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC7C,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEtB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACzD,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAChD,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;MAET,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEV,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACpB;QACA,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACvB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACrD,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACtC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACvB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAC7C,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAC9D,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACjD,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACxB;QACA,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC9B;QACA,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC;MACP;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC5F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAClC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YAC7C,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACrE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cAC3B,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACZ;;YAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;;MAEF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACnB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAC7D,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACxD;IACF,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAChC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACzC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC5C,CAAC;;QAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7E,CAAC,CAAC;;QAEF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC;IACJ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;MACxD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACf,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC;UACF,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC;;MAEJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACZ,CAAC;EACH;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/RoleDetailEdit.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { MANAGEMENT, RBAC } from '@shell/config/types';\nimport CruResource from '@shell/components/CruResource';\nimport CreateEditView from '@shell/mixins/create-edit-view';\nimport FormValidation from '@shell/mixins/form-validation';\nimport Error from '@shell/components/form/Error';\nimport { RadioGroup } from '@components/Form/Radio';\nimport Select from '@shell/components/form/Select';\nimport ArrayList from '@shell/components/form/ArrayList';\nimport NameNsDescription from '@shell/components/form/NameNsDescription';\nimport Tab from '@shell/components/Tabbed/Tab';\nimport Tabbed from '@shell/components/Tabbed';\nimport { ucFirst } from '@shell/utils/string';\nimport SortableTable from '@shell/components/SortableTable';\nimport { _CLONE, _DETAIL } from '@shell/config/query-params';\nimport { SCOPED_RESOURCES, SCOPED_RESOURCE_GROUPS } from '@shell/config/roles';\nimport LabeledInput from '@components/Form/LabeledInput/LabeledInput.vue';\n\nimport { SUBTYPE_MAPPING, VERBS } from '@shell/models/management.cattle.io.roletemplate';\nimport Loading from '@shell/components/Loading';\n\nconst GLOBAL = SUBTYPE_MAPPING.GLOBAL.key;\nconst CLUSTER = SUBTYPE_MAPPING.CLUSTER.key;\nconst NAMESPACE = SUBTYPE_MAPPING.NAMESPACE.key;\nconst RBAC_ROLE = SUBTYPE_MAPPING.RBAC_ROLE.key;\n\n/**\n * Handles the View, Create and Edit of\n * - management.cattle.io.globalrole\n * - management.cattle.io.roletemplate\n * - rbac.authorization.k8s.io.role\n * - rbac.authorization.k8s.io.clusterrole\n *\n * management.cattle.io.roletemplate is further split into two types\n * - Cluster\n * - Project/Namespace\n *\n * The above means there are 4 types ==> 5 subtypes handled by this component\n *\n * This component is used in these five forms:\n *\n * 1. Cluster Explorer > More Resources > RBAC > ClusterRoles\n *   - Should show list of cluster scoped resources and namespaced resources\n * 2. Cluster Explorer > More Resources > RBAC > Roles\n *   - Should show list of namespaced resources\n * 3. Users & Authentication > Roles > Global\n *   - Should show global, cluster and namespace scoped resources\n * 4. Users & Authentication > Roles > Cluster\n *   - Should show cluster and namespace scoped resources\n * 5. Users & Authentication > Roles > Projects & Namespaces\n *   - Should show only namespace scoped resources\n */\nexport default {\n  emits: ['set-subtype', 'input'],\n\n  components: {\n    ArrayList,\n    CruResource,\n    RadioGroup,\n    Select,\n    NameNsDescription,\n    Tab,\n    Tabbed,\n    SortableTable,\n    Loading,\n    Error,\n    LabeledInput\n  },\n\n  mixins: [CreateEditView, FormValidation],\n\n  async fetch() {\n    // We don't want to get all schemas from the cluster because there are\n    // two problems with that:\n    // - In the local cluster, that yields over 500-1,000 schemas, most of which aren't meant to\n    //   be edited by humans.\n    // - Populating the list directly from the schemas wouldn't include resources that may\n    //   be in downstream clusters but not the local cluster. For example, if the logging\n    //   application isn't installed in the local cluster, you wouldn't see logging resources\n    //   such as Flows in the resource list, which you might want in order to\n    //   create a role that is intended to be used by someone with access to a cluster where\n    //   logging is installed.\n    // Therefore we use a hardcoded list that is essentially intended\n    // to be in-app documentation for convenience only, while allowing\n    // users to freely type in resources that are not shown in the list.\n\n    if (this.value.subtype === CLUSTER || this.value.subtype === NAMESPACE) {\n      (await this.$store.dispatch(`management/findAll`, { type: MANAGEMENT.ROLE_TEMPLATE })).forEach((template) => {\n        // Ensure we have quick access to a specific template. This allows unselected drop downs to show the correct value\n        this.keyedTemplateOptions[template.id] = {\n          label: template.nameDisplay,\n          value: template.id\n        };\n      });\n      this.templateOptions = Object.values(this.keyedTemplateOptions);\n    }\n    if (this.realMode === _CLONE) {\n      this.value.displayName = '';\n      this.value.builtin = false;\n    }\n  },\n\n  data() {\n    return {\n      defaultRule: {\n        apiGroups:       [''],\n        nonResourceURLs: [],\n        resourceNames:   [],\n        resources:       [],\n        verbs:           []\n      },\n      verbOptions:          VERBS,\n      templateOptions:      [],\n      keyedTemplateOptions: {},\n      resources:            this.value.resources,\n      scopedResources:      SCOPED_RESOURCES,\n      defaultValue:         false,\n      selectFocused:        null,\n      fvFormRuleSets:       [\n        { path: 'displayName', rules: ['required'] }\n      ],\n    };\n  },\n\n  created() {\n    this.value['rules'] = this.value.rules || [];\n    const query = { ...this.$route.query };\n    const { roleContext } = query;\n\n    if (roleContext && this.value.updateSubtype) {\n      this.value.updateSubtype(roleContext);\n    }\n\n    // Set the default value for the mapped subtype\n    this.defaultValue = !!this.value[SUBTYPE_MAPPING[this.value.subtype].defaultKey];\n\n    switch (this.value.subtype) {\n    case CLUSTER:\n    case NAMESPACE:\n      this.value['roleTemplateNames'] = this.value.roleTemplateNames || [];\n      this.value['locked'] = !!this.value.locked;\n      break;\n    }\n\n    // On save hook request\n    if (this.registerBeforeHook) {\n      this.registerBeforeHook(() => {\n        // Map default value back to its own key for given subtype\n        this.value[SUBTYPE_MAPPING[this.value.subtype].defaultKey] = !!this.defaultValue;\n      });\n    }\n\n    if (this.value?.metadata?.name && !this.value.displayName) {\n      this.value['displayName'] = this.value.metadata.name;\n    }\n\n    this.$nextTick(() => {\n      this.$emit('set-subtype', this.label);\n    });\n  },\n\n  computed: {\n    label() {\n      return this.t(`rbac.roletemplate.subtypes.${ this.value.subtype }.label`);\n    },\n    defaultLabel() {\n      return this.t(`rbac.roletemplate.subtypes.${ this.value.subtype }.defaultLabel`);\n    },\n    lockedOptions() {\n      return [\n        {\n          value: true,\n          label: this.t('rbac.roletemplate.locked.yes')\n        },\n        {\n          value: false,\n          label: this.t('rbac.roletemplate.locked.no')\n        }\n      ];\n    },\n    resourceOptions() {\n      const options = [];\n\n      const scopes = Object.keys(this.scopedResources);\n\n      scopes.forEach((scope) => {\n        if (scope === SCOPED_RESOURCE_GROUPS.GLOBAL && this.value.type !== MANAGEMENT.GLOBAL_ROLE) {\n          // If we are not in the global role creation form,\n          // skip adding the global-scoped resources.\n          return;\n        }\n        if (scope === SCOPED_RESOURCE_GROUPS.CLUSTER && (this.value.type === RBAC.ROLE || this.value.subtype === NAMESPACE)) {\n          // If we are in a project/namespace role creation form,\n          // additionally skip adding the cluster-scoped resources.\n          return;\n        }\n\n        const apiGroupsInScope = this.scopedResources[scope];\n\n        const apiGroupNames = Object.keys(apiGroupsInScope);\n\n        // Put each API group as a header and put its resources under it.\n        apiGroupNames.forEach((apiGroup) => {\n          // Add API group as the header for a group of related resources.\n          let apiGroupLabel = apiGroup;\n          let apiGroupValue = apiGroup;\n\n          if (apiGroup === 'coreKubernetesApi') {\n            // If a resource belongs to the core Kubernetes API,\n            // the API group is technically an empty string but\n            // we will label it \"Core K8s API, Cluster Scoped.\"\n\n            // Some core Kubernetes resources are namespaced,\n            // in which case they go under a different heading\n            // \"Core K8s API, Namespaced.\" This lets us\n            // separate them by scope.\n            const labelForNoApiGroup = this.t('rbac.roletemplate.tabs.grantResources.noApiGroupClusterScope');\n            const labelForNamespacedResourcesWithNoApiGroup = this.t('rbac.roletemplate.tabs.grantResources.noApiGroupNamespaceScope');\n\n            apiGroupLabel = scope.includes('cluster') ? labelForNoApiGroup : labelForNamespacedResourcesWithNoApiGroup;\n            apiGroupValue = '';\n          }\n\n          if (apiGroup === 'neuvectorApi') {\n            // Some NeuVector resources are namespaced, in which case they go under a different heading\n            const labelForClusterScoped = this.t('rbac.roletemplate.tabs.grantResources.neuvector.labelClusterScoped');\n            const labelForNamespaceScoped = this.t('rbac.roletemplate.tabs.grantResources.neuvector.labelNamespaceScoped');\n\n            apiGroupLabel = scope.includes('cluster') ? labelForClusterScoped : labelForNamespaceScoped;\n            apiGroupValue = 'permission.neuvector.com';\n          }\n\n          options.push({\n            kind:      'group',\n            optionKey: apiGroupLabel,\n            label:     apiGroupLabel,\n            value:     apiGroupValue,\n            disabled:  true,\n          });\n\n          const resourcesInApiGroup = this.scopedResources[scope][apiGroup].resources;\n\n          // Add non-deprecated resources to the resource options list\n          resourcesInApiGroup.forEach((resourceName) => {\n            options.push({\n              label:     resourceName,\n              // Use unique key for resource list in the Select dropdown\n              optionKey: apiGroupValue.concat(resourceName),\n              value:     {\n                resourceName: resourceName.toLowerCase(),\n                apiGroupValue\n              }\n            });\n          });\n\n          // If the API group has any deprecated options,\n          // list them as \"Resource Name (deprecated)\"\n          if (this.scopedResources[scope][apiGroup].deprecatedResources) {\n            const deprecatedResourcesInApiGroup = this.scopedResources[scope][apiGroup].deprecatedResources;\n            const deprecatedLabel = this.t('rbac.roletemplate.tabs.grantResources.deprecatedLabel');\n\n            deprecatedResourcesInApiGroup.forEach((resourceName) => {\n              options.push({\n                label:     `${ resourceName } ${ deprecatedLabel }`,\n                optionKey: apiGroupValue.concat(resourceName),\n                value:     {\n                  resourceName: resourceName.toLowerCase(),\n                  apiGroupValue\n                }\n              });\n            });\n          }\n        });\n      });\n\n      options.push({\n        // This hidden option is to work around a bug in the Select\n        // component where an option marked as disabled\n        // is still selected by default if is value is an empty string.\n\n        // In the Role or Project Role form, an API group will be the\n        // default choice for the namespace value because there's only\n        // one option with the value as an empty string and that's the default\n        // value for the namespace. It's not good to have an API\n        // group as the default value for a resource.\n        // Adding this option means there are least two values with an\n        // empty string in the options, preventing the Select from\n        // selecting a disabled header group as the resource by default.\n\n        // This bug is such an edge case that I can't imagine anyone\n        // else hitting it, so I figured the workaround is better\n        // than fixing the select.\n        kind:      'group',\n        optionKey: 'hiddenOption',\n        label:     '_',\n        value:     '',\n        disabled:  true,\n      });\n\n      return options;\n    },\n\n    newUserDefaultOptions() {\n      return [\n        {\n          value: true,\n          label: this.t(`rbac.roletemplate.subtypes.${ this.value.subtype }.yes`)\n        },\n        {\n          value: false,\n          label: this.t('rbac.roletemplate.newUserDefault.no')\n        }\n      ];\n    },\n    isRancherRoleTemplate() {\n      return this.value.subtype === CLUSTER || this.value.subtype === NAMESPACE;\n    },\n    isNamespaced() {\n      return this.value.subtype === RBAC_ROLE;\n    },\n    isRancherType() {\n      return this.value.subtype === GLOBAL || this.value.subtype === CLUSTER || this.value.subtype === NAMESPACE;\n    },\n    isDetail() {\n      return this.as === _DETAIL;\n    },\n    isBuiltin() {\n      return this.value.builtin;\n    },\n    doneLocationOverride() {\n      return this.value.listLocation;\n    },\n    ruleClass() {\n      return `col ${ this.isNamespaced ? 'span-4' : 'span-3' }`;\n    },\n    // Detail View\n    rules() {\n      return this.createRules(this.value);\n    },\n    ruleHeaders() {\n      const verbHeaders = VERBS.map((verb) => ({\n        name:      verb,\n        key:       ucFirst(verb),\n        value:     this.verbKey(verb),\n        formatter: 'Checked',\n        align:     'center'\n      }));\n\n      return [\n        ...verbHeaders,\n        {\n          name:      'custom',\n          labelKey:  'tableHeaders.customVerbs',\n          key:       ucFirst('custom'),\n          value:     'hasCustomVerbs',\n          formatter: 'Checked',\n          align:     'center'\n        },\n        {\n          name:      'resources',\n          labelKey:  'tableHeaders.resources',\n          value:     'resources',\n          formatter: 'list',\n        },\n        {\n          name:      'url',\n          labelKey:  'tableHeaders.url',\n          value:     'nonResourceURLs',\n          formatter: 'list',\n        },\n        {\n          name:      'apiGroups',\n          labelKey:  'tableHeaders.apiGroup',\n          value:     'apiGroups',\n          formatter: 'list',\n        }\n      ];\n    },\n    inheritedRules() {\n      return this.createInheritedRules(this.value, [], false);\n    }\n  },\n\n  methods: {\n\n    setRule(key, rule, event) {\n      // The key is the aspect of a permissions rule\n      // that is being set, for example, \"verbs,\" \"resources\",\n      // \"apiGroups\" or \"nonResourceUrls.\"\n\n      // The event/value contains name of a resource,\n      // for example, \"Apps.\"\n\n      // The 'rule' contains the the contents of each row of the\n      // role creation form under Grant Resources. Each\n      // rule contains these fields:\n      // - apiGroups\n      // - nonResourceURLs\n      // - resourceNames\n      // - resources\n      // - verbs\n\n      switch (key) {\n      case 'apiGroups':\n\n        if (event || (event === '')) {\n          rule['apiGroups'] = [event];\n        }\n\n        break;\n\n      case 'verbs':\n\n        if (event) {\n          rule['verbs'] = [event];\n        } else {\n          rule['verbs'] = [];\n        }\n        break;\n\n      case 'resources':\n        if (event?.resourceName) {\n          // If we are updating the resources defined in a rule,\n          // the event will be an object with the\n          // properties apiGroupValue and resourceName.\n          rule['resources'] = [event.resourceName];\n          // Automatically fill in the API group of the\n          // selected resource.\n          rule['apiGroups'] = [event.apiGroupValue];\n        } else if (event?.label) {\n          // When the user creates a new resource name in the resource\n          // field instead of selecting an existing one,\n          // we have to treat that differently because the incoming event\n          // is shaped like {\"label\":\"something\"} instead of\n          // the same format as the other options:\n          // { resourceName: \"something\", apiGroupValue: \"\" }\n          rule['resources'] = [event.label];\n        } else {\n          rule['resources'] = [];\n          rule['apiGroups'] = [];\n        }\n        break;\n\n      case 'nonResourceURLs':\n        if (event) {\n          rule['nonResourceURLs'] = [event];\n        } else {\n          rule['nonResourceURLs'] = [];\n        }\n        break;\n\n      default:\n        break;\n      }\n    },\n    getRule(key, rule) {\n      return rule[key]?.[0] || null;\n    },\n    updateSelectValue(row, key, event) {\n      const value = event.label ? event.value : event;\n\n      row[key] = value;\n    },\n    cancel() {\n      this.done();\n    },\n    async actuallySave(url) {\n      // Go through all of the grules and replace double quote apiGroups\n      // k8S documentation shows using empty rules as \"\" - we change this to empty string when used\n      this.value.rules?.forEach((rule) => {\n        if (rule.apiGroups) {\n          rule.apiGroups = rule.apiGroups.map((group) => {\n            // If the group is two double quotes (\"\") replace if with empty string\n            if (group.trim() === '\\\"\\\"') {\n              group = '';\n            }\n\n            return group;\n          });\n        }\n      });\n\n      if ( this.isCreate ) {\n        url = url || this.schema.linkFor('collection');\n        await this.value.save({ url, redirectUnauthorized: false });\n      } else {\n        await this.value.save({ redirectUnauthorized: false });\n      }\n    },\n    // Detail View\n    verbKey(verb) {\n      return `has${ ucFirst(verb) }`;\n    },\n    createRules(role) {\n      return (role.rules || []).map((rule, i) => {\n        const tableRule = {\n          index:           i,\n          apiGroups:       rule.apiGroups || [''],\n          resources:       rule.resources || [],\n          nonResourceURLs: rule.nonResourceURLs || []\n        };\n\n        VERBS.forEach((verb) => {\n          const key = this.verbKey(verb);\n\n          tableRule[key] = rule.verbs[0] === '*' || rule.verbs.includes(verb);\n          tableRule.hasCustomVerbs = rule.verbs.some((verb) => !VERBS.includes(verb));\n        });\n\n        return tableRule;\n      });\n    },\n    createInheritedRules(parent, res = [], showParent = true) {\n      if (!parent.roleTemplateNames) {\n        return [];\n      }\n\n      parent.roleTemplateNames\n        .map((rtn) => this.$store.getters[`management/byId`](MANAGEMENT.ROLE_TEMPLATE, rtn))\n        .forEach((rt) => {\n          // Add Self\n          res.push({\n            showParent,\n            parent,\n            template: rt,\n            rules:    this.createRules(rt)\n          });\n          // Add inherited\n          this.createInheritedRules(rt, res);\n        });\n\n      return res;\n    },\n  }\n};\n</script>\n\n<template>\n  <Loading v-if=\"$fetchState.pending\" />\n  <CruResource\n    v-else\n    class=\"receiver\"\n    :can-yaml=\"!isCreate\"\n    :mode=\"mode\"\n    :resource=\"value\"\n    :errors=\"fvUnreportedValidationErrors\"\n    :validation-passed=\"fvFormIsValid\"\n    :cancel-event=\"true\"\n    @error=\"e=>errors = e\"\n    @finish=\"save\"\n    @cancel=\"cancel\"\n  >\n    <template v-if=\"isDetail\">\n      <SortableTable\n        key-field=\"index\"\n        :rows=\"rules\"\n        :headers=\"ruleHeaders\"\n        :table-actions=\"false\"\n        :row-actions=\"false\"\n        :search=\"false\"\n      />\n      <div\n        v-for=\"(inherited, index) of inheritedRules\"\n        :key=\"index\"\n      >\n        <div class=\"spacer\" />\n        <h3>\n          Inherited from {{ inherited.template.nameDisplay }}\n          <template v-if=\"inherited.showParent\">\n            {{ inherited.parent ? '(' + inherited.parent.nameDisplay + ')' : '' }}\n          </template>\n        </h3>\n        <SortableTable\n          key-field=\"index\"\n          :rows=\"inherited.rules\"\n          :headers=\"ruleHeaders\"\n          :table-actions=\"false\"\n          :row-actions=\"false\"\n          :search=\"false\"\n        />\n      </div>\n    </template>\n    <template v-else>\n      <NameNsDescription\n        :value=\"value\"\n        :namespaced=\"isNamespaced\"\n        :mode=\"mode\"\n        name-key=\"displayName\"\n        description-key=\"description\"\n        label=\"Name\"\n        :rules=\"{ name: fvGetAndReportPathRules('displayName') }\"\n        @update:value=\"$emit('input', $event)\"\n      />\n      <div\n        v-if=\"isRancherType\"\n        class=\"row\"\n      >\n        <div class=\"col span-6\">\n          <RadioGroup\n            v-model:value=\"defaultValue\"\n            name=\"storageSource\"\n            :label=\"defaultLabel\"\n            class=\"mb-10\"\n            data-testid=\"roletemplate-creator-default-options\"\n            :options=\"newUserDefaultOptions\"\n            :mode=\"mode\"\n          />\n        </div>\n        <div\n          v-if=\"isRancherRoleTemplate\"\n          class=\"col span-6\"\n        >\n          <RadioGroup\n            v-model:value=\"value.locked\"\n            name=\"storageSource\"\n            :label=\"t('rbac.roletemplate.locked.label')\"\n            class=\"mb-10\"\n            data-testid=\"roletemplate-locked-options\"\n            :options=\"lockedOptions\"\n            :mode=\"mode\"\n          />\n        </div>\n      </div>\n      <div class=\"spacer\" />\n      <Tabbed :side-tabs=\"true\">\n        <Tab\n          name=\"grant-resources\"\n          :label=\"t('rbac.roletemplate.tabs.grantResources.label')\"\n          :weight=\"1\"\n        >\n          <Error\n            :value=\"value.rules\"\n            :rules=\"fvGetAndReportPathRules('rules')\"\n            as-banner\n          />\n          <ArrayList\n            v-model:value=\"value.rules\"\n            label=\"Resources\"\n            :disabled=\"isBuiltin\"\n            :remove-allowed=\"!isBuiltin\"\n            :add-allowed=\"!isBuiltin\"\n            :default-add-value=\"defaultRule\"\n            :initial-empty-row=\"true\"\n            :show-header=\"true\"\n            add-label=\"Add Resource\"\n            :mode=\"mode\"\n          >\n            <template #column-headers>\n              <div class=\"column-headers row\">\n                <div :class=\"ruleClass\">\n                  <span class=\"text-label\">{{ t('rbac.roletemplate.tabs.grantResources.tableHeaders.verbs') }}\n                    <span class=\"required\">*</span>\n                  </span>\n                </div>\n                <div :class=\"ruleClass\">\n                  <span class=\"text-label\">\n                    {{ t('rbac.roletemplate.tabs.grantResources.tableHeaders.resources') }}\n                    <i\n                      v-clean-tooltip=\"t('rbac.roletemplate.tabs.grantResources.resourceOptionInfo')\"\n                      class=\"icon icon-info\"\n                    />\n                    <span\n                      v-if=\"isNamespaced\"\n                      class=\"required\"\n                    >*</span>\n                  </span>\n                </div>\n                <div :class=\"ruleClass\">\n                  <span class=\"text-label\">{{ t('rbac.roletemplate.tabs.grantResources.tableHeaders.apiGroups') }}</span>\n                </div>\n                <div\n                  v-if=\"!isNamespaced\"\n                  :class=\"ruleClass\"\n                >\n                  <span class=\"text-label\">{{ t('rbac.roletemplate.tabs.grantResources.tableHeaders.nonResourceUrls') }}</span>\n                </div>\n              </div>\n            </template>\n            <template #columns=\"props\">\n              <div class=\"columns row mr-20\">\n                <div :class=\"ruleClass\">\n                  <!-- Select verbs -->\n                  <Select\n                    :value=\"props.row.value.verbs\"\n                    class=\"lg\"\n                    :disabled=\"isBuiltin\"\n                    :taggable=\"true\"\n                    :searchable=\"true\"\n                    :options=\"verbOptions\"\n                    :multiple=\"true\"\n                    :mode=\"mode\"\n                    :compact=\"true\"\n                    :data-testid=\"`grant-resources-verbs${props.i}`\"\n                    @update:value=\"updateSelectValue(props.row.value, 'verbs', $event)\"\n                  />\n                </div>\n                <div :class=\"ruleClass\">\n                  <Select\n                    :value=\"getRule('resources', props.row.value)\"\n                    :disabled=\"isBuiltin\"\n                    :options=\"resourceOptions\"\n                    option-key=\"optionKey\"\n                    :searchable=\"true\"\n                    :taggable=\"true\"\n                    :mode=\"mode\"\n                    :compact=\"true\"\n                    :data-testid=\"`grant-resources-resources${props.i}`\"\n                    @update:value=\"setRule('resources', props.row.value, $event)\"\n                    @createdListItem=\"setRule('resources', props.row.value, $event)\"\n                  />\n                </div>\n                <div :class=\"ruleClass\">\n                  <LabeledInput\n                    :value=\"getRule('apiGroups', props.row.value)\"\n                    :disabled=\"isBuiltin\"\n                    :mode=\"mode\"\n                    :data-testid=\"`grant-resources-api-groups${props.i}`\"\n                    @input=\"setRule('apiGroups', props.row.value, $event.target.value)\"\n                  />\n                </div>\n                <div\n                  v-if=\"!isNamespaced\"\n                  :class=\"ruleClass\"\n                >\n                  <LabeledInput\n                    :value=\"getRule('nonResourceURLs', props.row.value)\"\n                    :disabled=\"isBuiltin\"\n                    :mode=\"mode\"\n                    :data-testid=\"`grant-resources-non-resource-urls${props.i}`\"\n                    @input=\"setRule('nonResourceURLs', props.row.value, $event.target.value)\"\n                  />\n                </div>\n              </div>\n            </template>\n          </ArrayList>\n        </Tab>\n        <Tab\n          v-if=\"isRancherRoleTemplate\"\n          name=\"inherit-from\"\n          label=\"Inherit From\"\n          :weight=\"0\"\n        >\n          <ArrayList\n            v-model:value=\"value.roleTemplateNames\"\n            :disabled=\"isBuiltin\"\n            :remove-allowed=\"!isBuiltin\"\n            :add-allowed=\"!isBuiltin\"\n            label=\"Resources\"\n            add-label=\"Add Resource\"\n            :mode=\"mode\"\n          >\n            <template #columns=\"props\">\n              <div class=\"columns row mr-20\">\n                <div class=\"col span-12\">\n                  <Select\n                    v-model:value=\"props.row.value\"\n                    class=\"lg\"\n                    :taggable=\"false\"\n                    :disabled=\"isBuiltin\"\n                    :searchable=\"true\"\n                    :options=\"selectFocused === props.i ? templateOptions : [keyedTemplateOptions[props.row.value]]\"\n                    option-key=\"value\"\n                    option-label=\"label\"\n                    :mode=\"mode\"\n                    :compact=\"true\"\n                    @on-focus=\"selectFocused = props.i\"\n                    @on-blur=\"selectFocused = null\"\n                  />\n                </div>\n              </div>\n            </template>\n          </ArrayList>\n        </Tab>\n      </Tabbed>\n    </template>\n  </CruResource>\n</template>\n\n<style lang=\"scss\" scoped>\n  .required {\n    color: var(--error);\n  }\n\n  :deep() {\n    .column-headers {\n      margin-right: 75px;\n      margin-bottom: 5px;\n    }\n\n    .box {\n      align-items: initial;\n\n      .remove {\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        align-items: flex-end;\n      }\n    }\n\n    .columns {\n      .col > .unlabeled-select:not(.taggable) {\n        // override the odd padding-top from shell/assets/styles/global/_select.scss\n        padding: $unlabaled-select-padding\n      }\n    }\n  }\n</style>\n"]}]}