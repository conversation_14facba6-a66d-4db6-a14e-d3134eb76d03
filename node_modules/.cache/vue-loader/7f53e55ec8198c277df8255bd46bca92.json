{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/ResourcesSummary.vue?vue&type=template&id=7a9736b6&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/ResourcesSummary.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdiBjbGFzcz0icm93IGZsZXh3cmFwIj4KICAgIDxkaXYKICAgICAgdi1mb3I9Iih2LCBrKSBpbiBjb3VudHMiCiAgICAgIDprZXk9ImsiCiAgICAgIGNsYXNzPSJjb2wgY291bnRib3giCiAgICA+CiAgICAgIDxDb3VudEJveAogICAgICAgIDpjb21wYWN0PSJ0cnVlIgogICAgICAgIDpjb3VudD0idlsnY291bnQnXSIKICAgICAgICA6bmFtZT0idi5sYWJlbCIKICAgICAgICA6cHJpbWFyeS1jb2xvci12YXI9IictLXNpenpsZS0nICsgdi5jb2xvciIKICAgICAgLz4KICAgIDwvZGl2PgogIDwvZGl2Pgo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/ResourcesSummary.vue"], "names": [], "mappings": ";EAoEE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB;MACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3C,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/ResourcesSummary.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport capitalize from 'lodash/capitalize';\nimport CountBox from '@shell/components/CountBox';\nimport { STATES } from '@shell/plugins/dashboard-store/resource-class';\n\nexport default {\n\n  name: 'ResourcesSummary',\n\n  components: { CountBox },\n\n  props: {\n    value: {\n      type:     Object,\n      required: true,\n    },\n\n    stateKey: {\n      type:    String,\n      default: 'fleet.fleetSummary.state'\n    },\n\n    requiredStates: {\n      type:    Array,\n      default: () => []\n    }\n  },\n\n  computed: {\n    counts() {\n      const out = this.requiredStates.reduce((obj, state) => {\n        obj[state] = {\n          count: 0,\n          color: state,\n          label: this.$store.getters['i18n/withFallback'](`${ this.stateKey }.${ state }`, null, state)\n        };\n\n        return obj;\n      }, {});\n\n      for (const k in this.value) {\n        if (k.startsWith('desired')) {\n          continue;\n        }\n\n        const mapped = STATES[k] || STATES['other'];\n\n        if (out[k]) {\n          out[k].count += this.value[k] || 0;\n          out[k].color = mapped.color;\n        } else {\n          out[k] = {\n            count: this.value[k] || 0,\n            color: mapped.color,\n            label: this.$store.getters['i18n/withFallback'](`${ this.stateKey }.${ k }`, null, capitalize(k))\n          };\n        }\n      }\n\n      return out;\n    },\n  },\n\n  methods: { capitalize },\n};\n</script>\n\n<template>\n  <div class=\"row flexwrap\">\n    <div\n      v-for=\"(v, k) in counts\"\n      :key=\"k\"\n      class=\"col countbox\"\n    >\n      <CountBox\n        :compact=\"true\"\n        :count=\"v['count']\"\n        :name=\"v.label\"\n        :primary-color-var=\"'--sizzle-' + v.color\"\n      />\n    </div>\n  </div>\n</template>\n<style lang=\"scss\" scoped>\n  .flexwrap {\n    flex-wrap: wrap;\n  }\n  .countbox {\n    min-width: 150px;\n    width: 12.5%;\n    margin-bottom: 10px;\n  }\n</style>\n"]}]}