{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/PromptRestore.vue?vue&type=style&index=0&id=1d30f1d2&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/PromptRestore.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLnByb21wdHJlc3RvcmUtbW9kYWwgewogICAgYm9yZGVyLXJhZGl1czogdmFyKC0tYm9yZGVyLXJhZGl1cyk7CiAgICBvdmVyZmxvdzogc2Nyb2xsOwogICAgbWF4LWhlaWdodDogMTAwdmg7CiAgICAmIDo6LXdlYmtpdC1zY3JvbGxiYXItY29ybmVyIHsKICAgICAgYmFja2dyb3VuZDogcmdiYSgwLDAsMCwwKTsKICAgIH0KCiAgICAucHJvbXB0LXJlc3RvcmUgZm9ybSBwIHsKICAgICAgbWluLWhlaWdodDogMTZweDsKICAgIH0KCiAgICAvLyBQb3NpdGlvbiBkaWFsb2cgYnV0dG9ucyBvbiB0aGUgcmlnaHQtaGFuZCBzaWRlIG9mIHRoZSBkaWFsb2cKICAgIC5kaWFsb2ctYWN0aW9ucyB7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7CiAgICB9CiAgfQoKICAucHJvbXB0LXJlc3RvcmUgOmRlZXAoKSAuY2FyZC13cmFwIC5jYXJkLWFjdGlvbnMgewogICAgZGlzcGxheTogYmxvY2s7CgogICAgYnV0dG9uOm5vdCg6bGFzdC1jaGlsZCkgewogICAgICBtYXJnaW4tcmlnaHQ6IDEwcHg7CiAgICB9CgogICAgLmJhbm5lciB7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICB9CiAgfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/PromptRestore.vue"], "names": [], "mappings": ";EAwSE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB;;IAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACpB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/PromptRestore.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapState, mapGetters } from 'vuex';\nimport AsyncButton from '@shell/components/AsyncButton';\nimport { Card } from '@components/Card';\nimport { Banner } from '@components/Banner';\nimport Date from '@shell/components/formatter/Date.vue';\nimport RadioGroup from '@components/Form/Radio/RadioGroup.vue';\nimport LabeledSelect from '@shell/components/form/LabeledSelect.vue';\nimport { exceptionToErrorsArray } from '@shell/utils/error';\nimport { CAPI, NORMAN, SNAPSHOT } from '@shell/config/types';\nimport { set } from '@shell/utils/object';\nimport ChildHook, { BEFORE_SAVE_HOOKS } from '@shell/mixins/child-hook';\nimport { DATE_FORMAT, TIME_FORMAT } from '@shell/store/prefs';\nimport { escapeHtml } from '@shell/utils/string';\nimport day from 'dayjs';\nimport { sortBy } from '@shell/utils/sort';\nimport { STATES_ENUM } from '@shell/plugins/dashboard-store/resource-class';\nimport AppModal from '@shell/components/AppModal.vue';\n\nexport default {\n  components: {\n    Card,\n    AsyncButton,\n    Banner,\n    Date,\n    LabeledSelect,\n    RadioGroup,\n    AppModal,\n  },\n\n  name: 'PromptRestore',\n\n  mixins: [\n    ChildHook,\n  ],\n\n  data() {\n    return {\n      errors:           [],\n      labels:           {},\n      restoreMode:      'all',\n      loaded:           false,\n      allSnapshots:     {},\n      sortedSnapshots:  [],\n      selectedSnapshot: null,\n    };\n  },\n\n  computed: {\n    // toRestore can be a provisioning.cattle.io.cluster or a rke.cattle.io.etcdsnapshot or an etcdBackup resource\n    ...mapState('action-menu', ['showPromptRestore', 'toRestore']),\n    ...mapGetters({ t: 'i18n/t' }),\n\n    // Was the dialog opened to restore a specific snapshot, or opened on a cluster to choose\n    isCluster() {\n      const isSnapshot = this.toRestore[0]?.type.toLowerCase() === NORMAN.ETCD_BACKUP ||\n      this.toRestore[0]?.type.toLowerCase() === SNAPSHOT;\n\n      return !isSnapshot;\n    },\n\n    snapshot() {\n      return !this.isCluster ? this.toRestore[0] : this.allSnapshots[this.selectedSnapshot];\n    },\n\n    hasSnapshot() {\n      return !!this.snapshot;\n    },\n\n    isRke2() {\n      return !!this.snapshot?.rke2;\n    },\n\n    clusterSnapshots() {\n      if (this.sortedSnapshots) {\n        return this.sortedSnapshots.map((snapshot) => ({ label: this.snapshotLabel(snapshot), value: snapshot.name }));\n      } else {\n        return [];\n      }\n    },\n    restoreModeOptions() {\n      const etcdOption = this.isRke2 ? 'none' : 'etcd';\n\n      return [etcdOption, 'kubernetesVersion', 'all'];\n    }\n  },\n\n  watch: {\n    async showPromptRestore(show) {\n      if (show) {\n        this.loaded = true;\n        await this.fetchSnapshots();\n        this.selectDefaultSnapshot();\n      } else {\n        this.loaded = false;\n      }\n    }\n  },\n\n  methods: {\n    close() {\n      this.errors = [];\n      this.labels = {};\n      this.$store.commit('action-menu/togglePromptRestore');\n      this.selectedSnapshot = null;\n    },\n\n    // If the user needs to choose a snapshot, fetch all snapshots for the cluster\n    async fetchSnapshots() {\n      if (!this.isCluster) {\n        return;\n      }\n\n      const cluster = this.toRestore?.[0];\n      let promise;\n\n      if (!cluster?.isRke2) {\n        promise = this.$store.dispatch('rancher/findAll', { type: NORMAN.ETCD_BACKUP }).then((snapshots) => {\n          return snapshots.filter((s) => s.state === STATES_ENUM.ACTIVE && s.clusterId === cluster.metadata.name);\n        });\n      } else {\n        promise = this.$store.dispatch('management/findAll', { type: SNAPSHOT }).then((snapshots) => {\n          const toRestoreClusterName = cluster?.clusterName || cluster?.metadata?.name;\n\n          return snapshots.filter((s) => s?.snapshotFile?.status === STATES_ENUM.SUCCESSFUL && s.clusterName === toRestoreClusterName\n          );\n        });\n      }\n\n      // Map of snapshots by name\n      const allSnapshots = await promise.then((snapshots) => {\n        return snapshots.reduce((v, s) => {\n          v[s.name] = s;\n\n          return v;\n        }, {});\n      }).catch((err) => {\n        this.errors = exceptionToErrorsArray(err);\n      });\n\n      this.allSnapshots = allSnapshots;\n      this.sortedSnapshots = sortBy(Object.values(this.allSnapshots), ['snapshotFile.createdAt', 'created', 'metadata.creationTimestamp'], true);\n    },\n\n    selectDefaultSnapshot() {\n      if (this.selectedSnapshot) {\n        return;\n      }\n\n      const defaultSnapshot = this.toRestore[0]?.type === SNAPSHOT ? this.toRestore[0].name : this.clusterSnapshots[0]?.value;\n\n      this['selectedSnapshot'] = defaultSnapshot;\n    },\n\n    async apply(buttonDone) {\n      try {\n        if ( this.isRke2 ) {\n          const cluster = this.$store.getters['management/byId'](CAPI.RANCHER_CLUSTER, this.snapshot.clusterId);\n\n          await this.applyHooks(BEFORE_SAVE_HOOKS);\n\n          const now = cluster.spec?.rkeConfig?.etcdSnapshotRestore?.generation || 0;\n\n          set(cluster, 'spec.rkeConfig.etcdSnapshotRestore', {\n            generation:       now + 1,\n            name:             this.snapshot.name,\n            restoreRKEConfig: this.restoreMode,\n          });\n\n          await cluster.save();\n        } else {\n          await this.$store.dispatch('rancher/request', {\n            url:    `/v3/clusters/${ escape(this.snapshot.clusterId) }?action=restoreFromEtcdBackup`,\n            method: 'post',\n            data:   {\n              etcdBackupId:     this.snapshot.id,\n              restoreRkeConfig: this.restoreMode,\n            },\n          });\n        }\n\n        this.$store.dispatch('growl/success', {\n          title:   this.t('promptRestore.notification.title'),\n          message: this.t('promptRestore.notification.message', { selectedSnapshot: this.selectedSnapshot })\n        }, { root: true });\n\n        buttonDone(true);\n        this.close();\n      } catch (err) {\n        this.errors = exceptionToErrorsArray(err);\n        buttonDone(false);\n      }\n    },\n    snapshotLabel(snapshot) {\n      const dateFormat = escapeHtml(this.$store.getters['prefs/get'](DATE_FORMAT));\n      const timeFormat = escapeHtml( this.$store.getters['prefs/get'](TIME_FORMAT));\n\n      const created = snapshot.createdAt || snapshot.created || snapshot.metadata.creationTimestamp;\n      const d = day(created).format(dateFormat);\n      const t = day(created).format(timeFormat);\n\n      return `${ d } ${ t } : ${ snapshot.nameDisplay }`;\n    }\n  }\n};\n</script>\n\n<template>\n  <app-modal\n    v-if=\"loaded\"\n    custom-class=\"promptrestore-modal\"\n    name=\"promptRestore\"\n    styles=\"background-color: var(--nav-bg); border-radius: var(--border-radius); max-height: 100vh;\"\n    height=\"auto\"\n    :scrollable=\"true\"\n    @close=\"close\"\n  >\n    <Card\n      v-if=\"loaded\"\n      class=\"prompt-restore\"\n      :show-highlight-border=\"false\"\n    >\n      <template #title>\n        <h4\n          v-clean-html=\"t('promptRestore.title', null, true)\"\n          class=\"text-default-text\"\n        />\n      </template>\n\n      <template #body>\n        <div class=\"pl-10 pr-10\">\n          <form>\n            <h3 v-t=\"'promptRestore.name'\" />\n            <div v-if=\"!isCluster\">\n              {{ snapshot.nameDisplay }}\n            </div>\n\n            <LabeledSelect\n              v-if=\"isCluster\"\n              v-model:value=\"selectedSnapshot\"\n              :label=\"t('promptRestore.label')\"\n              :placeholder=\"t('promptRestore.placeholder')\"\n              :options=\"clusterSnapshots\"\n            />\n\n            <div class=\"spacer\" />\n\n            <h3 v-t=\"'promptRestore.date'\" />\n            <div>\n              <p>\n                <Date\n                  v-if=\"snapshot\"\n                  :value=\"snapshot.createdAt || snapshot.created || snapshot.metadata.creationTimestamp\"\n                />\n              </p>\n            </div>\n            <div class=\"spacer\" />\n            <RadioGroup\n              v-model:value=\"restoreMode\"\n              name=\"restoreMode\"\n              label=\"Restore Type\"\n              :labels=\"['Only etcd', 'Kubernetes version and etcd', 'Cluster config, Kubernetes version and etcd']\"\n              :options=\"restoreModeOptions\"\n            />\n          </form>\n        </div>\n      </template>\n\n      <template #actions>\n        <div class=\"dialog-actions\">\n          <button\n            class=\"btn role-secondary\"\n            @click=\"close\"\n          >\n            {{ t('generic.cancel') }}\n          </button>\n\n          <AsyncButton\n            mode=\"restore\"\n            :disabled=\"!hasSnapshot\"\n            @click=\"apply\"\n          />\n\n          <Banner\n            v-for=\"(err, i) in errors\"\n            :key=\"i\"\n            color=\"error\"\n            :label=\"err\"\n          />\n        </div>\n      </template>\n    </Card>\n  </app-modal>\n</template>\n\n<style lang='scss' scoped>\n  .promptrestore-modal {\n    border-radius: var(--border-radius);\n    overflow: scroll;\n    max-height: 100vh;\n    & ::-webkit-scrollbar-corner {\n      background: rgba(0,0,0,0);\n    }\n\n    .prompt-restore form p {\n      min-height: 16px;\n    }\n\n    // Position dialog buttons on the right-hand side of the dialog\n    .dialog-actions {\n      display: flex;\n      justify-content: flex-end;\n    }\n  }\n\n  .prompt-restore :deep() .card-wrap .card-actions {\n    display: block;\n\n    button:not(:last-child) {\n      margin-right: 10px;\n    }\n\n    .banner {\n      display: flex;\n    }\n  }\n</style>\n"]}]}