{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/EnvVars.vue?vue&type=template&id=74c7a39e&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/EnvVars.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdiA6c3R5bGU9Insnd2lkdGgnOicxMDAlJ30iPgogICAgPGRpdgogICAgICB2LWZvcj0iKHJvdywgaSkgaW4gYWxsRW52IgogICAgICA6a2V5PSJpIgogICAgPgogICAgICA8VmFsdWVGcm9tUmVzb3VyY2UKICAgICAgICB2LW1vZGVsOnZhbHVlPSJyb3cudmFsdWUiCiAgICAgICAgOmFsbC1zZWNyZXRzPSJzZWNyZXRzIgogICAgICAgIDphbGwtY29uZmlnLW1hcHM9ImNvbmZpZ01hcHMiCiAgICAgICAgOm1vZGU9Im1vZGUiCiAgICAgICAgOmxvYWRpbmc9ImxvYWRpbmciCiAgICAgICAgQHJlbW92ZT0icmVtb3ZlUm93KGkpIgogICAgICAgIEB1cGRhdGU6dmFsdWU9InVwZGF0ZVJvdyIKICAgICAgLz4KICAgIDwvZGl2PgogICAgPGJ1dHRvbgogICAgICB2LWlmPSIhaXNWaWV3IgogICAgICB2LXQ9Iid3b3JrbG9hZC5jb250YWluZXIuY29tbWFuZC5hZGRFbnZWYXInIgogICAgICB0eXBlPSJidXR0b24iCiAgICAgIGNsYXNzPSJidG4gcm9sZS10ZXJ0aWFyeSBhZGQiCiAgICAgIGRhdGEtdGVzdGlkPSJhZGQtZW52LXZhciIKICAgICAgQGNsaWNrPSJhZGRGcm9tUmVmZXJlbmNlIgogICAgLz4KICA8L2Rpdj4K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/EnvVars.vue"], "names": [], "mappings": ";EA0GE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT;MACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;EACH,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/EnvVars.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport ValueFromResource from '@shell/components/form/ValueFromResource';\nimport debounce from 'lodash/debounce';\nimport { randomStr } from '@shell/utils/string';\nimport { _VIEW } from '@shell/config/query-params';\n\nexport default {\n  components: { ValueFromResource },\n\n  props: {\n    /**\n     * Form mode for the component\n     */\n    mode: {\n      type:     String,\n      required: true,\n    },\n    configMaps: {\n      type:     Array,\n      required: true\n    },\n    secrets: {\n      type:     Array,\n      required: true\n    },\n    loading: {\n      default: false,\n      type:    Boolean\n    },\n    /**\n     * Container spec\n     */\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  data() {\n    const { env = [], envFrom = [] } = this.value;\n\n    const allEnv = [...env, ...envFrom].map((row) => {\n      return { value: row, id: randomStr(4) };\n    });\n\n    return {\n      env, envFrom, allEnv\n    };\n  },\n\n  computed: {\n    isView() {\n      return this.mode === _VIEW;\n    }\n  },\n\n  watch: {\n    'value.tty'(neu) {\n      if (neu) {\n        this.value['stdin'] = true;\n      }\n    }\n  },\n  created() {\n    this.queueUpdate = debounce(this.update, 500);\n  },\n\n  methods: {\n    update() {\n      delete this.value.env;\n      delete this.value.envFrom;\n      const envVarSource = [];\n      const envVar = [];\n\n      this.allEnv.forEach((row) => {\n        if (!row.value) {\n          return;\n        }\n        if (!!row.value.configMapRef || !!row.value.secretRef) {\n          envVarSource.push(row.value);\n        } else {\n          envVar.push(row.value);\n        }\n      });\n      this.value['env'] = envVar;\n      this.value['envFrom'] = envVarSource;\n    },\n\n    updateRow() {\n      this.queueUpdate();\n    },\n\n    removeRow(idx) {\n      this.allEnv.splice(idx, 1);\n      this.queueUpdate();\n    },\n\n    addFromReference() {\n      this.allEnv.push({ value: { name: '', valueFrom: {} }, id: randomStr(4) });\n    },\n  },\n};\n</script>\n<template>\n  <div :style=\"{'width':'100%'}\">\n    <div\n      v-for=\"(row, i) in allEnv\"\n      :key=\"i\"\n    >\n      <ValueFromResource\n        v-model:value=\"row.value\"\n        :all-secrets=\"secrets\"\n        :all-config-maps=\"configMaps\"\n        :mode=\"mode\"\n        :loading=\"loading\"\n        @remove=\"removeRow(i)\"\n        @update:value=\"updateRow\"\n      />\n    </div>\n    <button\n      v-if=\"!isView\"\n      v-t=\"'workload.container.command.addEnvVar'\"\n      type=\"button\"\n      class=\"btn role-tertiary add\"\n      data-testid=\"add-env-var\"\n      @click=\"addFromReference\"\n    />\n  </div>\n</template>\n\n<style lang='scss' scoped>\n.value-from :deep() {\n  .v-select {\n    height: 50px;\n  }\n\n  INPUT:not(.vs__search) {\n    height: 50px;\n  }\n}\n.value-from, .value-from-headers {\n  display: grid;\n  grid-template-columns: 20% 20% 20% 5% 20% auto;\n  grid-gap: $column-gutter;\n  align-items: center;\n  margin-bottom: 10px;\n}\n  .value-from-headers {\n    margin: 10px 0px 10px 0px;\n    color: var(--input-label);\n    }\n</style>\n"]}]}