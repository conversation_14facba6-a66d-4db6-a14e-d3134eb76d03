{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ClusterProviderIcon.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ClusterProviderIcon.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmV4cG9ydCBkZWZhdWx0IHsKICBwcm9wczogewogICAgY2x1c3RlcjogewogICAgICB0eXBlOiAgICAgT2JqZWN0LAogICAgICByZXF1aXJlZDogdHJ1ZSwKICAgIH0sCiAgICBzbWFsbDogewogICAgICB0eXBlOiAgICBCb29sZWFuLAogICAgICBkZWZhdWx0OiBmYWxzZSwKICAgIH0KICB9LAoKICBjb21wdXRlZDogewogICAgdXNlRm9ySWNvbigpIHsKICAgICAgcmV0dXJuICEhdGhpcy5jbHVzdGVyPy5iYWRnZT8uaWNvblRleHQ7CiAgICB9LAogICAgc2hvd0JvcmRlcnMoKSB7CiAgICAgIHJldHVybiB0aGlzLmNsdXN0ZXI/LmJhZGdlPy5jb2xvciA9PT0gJ3RyYW5zcGFyZW50JzsKICAgIH0sCiAgfQp9Owo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ClusterProviderIcon.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC;EACH;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ClusterProviderIcon.vue", "sourceRoot": "", "sourcesContent": ["<script>\nexport default {\n  props: {\n    cluster: {\n      type:     Object,\n      required: true,\n    },\n    small: {\n      type:    Boolean,\n      default: false,\n    }\n  },\n\n  computed: {\n    useForIcon() {\n      return !!this.cluster?.badge?.iconText;\n    },\n    showBorders() {\n      return this.cluster?.badge?.color === 'transparent';\n    },\n  }\n};\n</script>\n\n<template>\n  <div\n    v-if=\"cluster\"\n    class=\"cluster-icon\"\n    :class=\"{'cluster-icon-small': small}\"\n  >\n    <div\n      v-if=\"useForIcon\"\n      class=\"cluster-badge-logo\"\n      :class=\"{ 'cluster-icon-border': showBorders}\"\n      :style=\"{ backgroundColor: cluster.badge.color, color: cluster.badge.textColor }\"\n    >\n      {{ cluster.badge.iconText }}\n    </div>\n    <!-- eslint-disable -->\n    <svg \n      v-else-if=\"cluster.isLocal && !cluster.isHarvester\" \n      class=\"cluster-local-logo\" \n      version=\"1.1\" \n      id=\"Layer_1\" \n      xmlns=\"http://www.w3.org/2000/svg\" \n      xmlns:xlink=\"http://www.w3.org/1999/xlink\" \n      x=\"0px\" \n      y=\"0px\" \n      viewBox=\"0 0 100 100\" \n      style=\"enable-background:new 0 0 100 100;\" \n      xml:space=\"preserve\">\n      <title>{{ t('nav.ariaLabel.clusterProvIcon', { cluster: 'local' }) }}</title>\n      <g>\n        <g>\n          <path class=\"rancher-icon-fill\" d=\"M26.0862026,44.4953918H8.6165142c-5.5818157,0-9.3979139-4.6252708-8.4802637-10.1311035l2.858391-17.210701\n            C3.912292,11.6477556,6.1382647,7.1128125,7.8419709,7.1128125s3.1788611,4.5368752,3.1788611,10.1186218v4.4837742\n            c0,5.5817471,4.4044495,9.5409164,9.9862652,9.5409164h5.0791054V44.4953918z\"/>\n        </g>\n        <path class=\"rancher-icon-fill\" d=\"M63.0214729,92.8871841H37.0862045c-6.0751343,0-11.0000019-4.9248657-11.0000019-11V30.3864384\n          c0-6.0751324,4.9248676-11,11.0000019-11h25.9352684c6.0751305,0,11.0000038,4.9248676,11.0000038,11v51.5007477\n          C74.0214767,87.9623184,69.0966034,92.8871841,63.0214729,92.8871841z\"/>\n        <g>\n          <path class=\"rancher-icon-fill\" d=\"M73.9137955,44.4953918h17.4696884c5.5818176,0,9.3979187-4.6252708,8.4802628-10.1311035\n            l-2.8583908-17.210701c-0.9176483-5.5058317-3.1436234-10.0407753-4.8473282-10.0407753\n            s-3.1788635,4.5368752-3.1788635,10.1186218v4.4837742c0,5.5817471-4.4044418,9.5409164-9.9862595,9.5409164h-5.0791092\n            V44.4953918z\"/>\n        </g>\n      </g>\n    </svg>\n    <!-- eslint-enable -->\n    <img\n      v-else-if=\"cluster.providerNavLogo\"\n      class=\"cluster-os-logo\"\n      :src=\"cluster.providerNavLogo\"\n      :alt=\"t('nav.ariaLabel.clusterProvIcon', { cluster: cluster.nameDisplay })\"\n    >\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .rancher-icon-fill {\n    fill: var(--primary);\n  }\n  .cluster-icon {\n    align-items: center;\n    display: flex;\n    height: 32px;\n    justify-content: center;\n    width: 42px;\n\n    &-border {\n      border: 1px solid var(--border);\n      border-radius: 5px;\n      color: var(--body-text) !important; // !important is needed to override the color set by the badge when there's a transparent background.\n    }\n  }\n\n  .cluster-icon-small {\n    height: 25px;\n    width: 25px;\n\n    .cluster-os-logo {\n      width: 25px;\n      height: 25px;\n    }\n\n    .cluster-badge-logo {\n      width: 25px;\n      height: 25px;\n    }\n  }\n\n  .cluster-os-logo {\n    width: 32px;\n    height: 32px;\n  }\n  .cluster-local-logo {\n    display: flex;\n    width: 25px;\n  }\n  .cluster-badge-logo {\n    min-width: 42px;\n    height: 32px;\n    padding: 0px 5px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 5px;\n    font-weight: bold;\n  }\n</style>\n"]}]}