{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetResources.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetResources.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCBTb3J0YWJsZVRhYmxlIGZyb20gJ0BzaGVsbC9jb21wb25lbnRzL1NvcnRhYmxlVGFibGUnOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdGbGVldFJlc291cmNlcycsCgogIGNvbXBvbmVudHM6IHsgU29ydGFibGVUYWJsZSB9LAoKICBwcm9wczogewogICAgdmFsdWU6IHsKICAgICAgdHlwZTogICAgIE9iamVjdCwKICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICB9LAogICAgY2x1c3RlcklkOiB7CiAgICAgIHR5cGU6ICAgICBTdHJpbmcsCiAgICAgIHJlcXVpcmVkOiBmYWxzZSwKICAgICAgZGVmYXVsdDogIG51bGwsCiAgICB9LAogIH0sCgogIGNvbXB1dGVkOiB7CiAgICBjb21wdXRlZFJlc291cmNlcygpIHsKICAgICAgcmV0dXJuIHRoaXMudmFsdWUucmVzb3VyY2VzU3RhdHVzZXM7CiAgICB9LAoKICAgIHJlc291cmNlSGVhZGVycygpIHsKICAgICAgcmV0dXJuIFsKICAgICAgICB7CiAgICAgICAgICBuYW1lOiAgICAgICdzdGF0ZScsCiAgICAgICAgICB2YWx1ZTogICAgICdzdGF0ZScsCiAgICAgICAgICBsYWJlbDogICAgICdTdGF0ZScsCiAgICAgICAgICBzb3J0OiAgICAgICdzdGF0ZVNvcnQnLAogICAgICAgICAgZm9ybWF0dGVyOiAnQmFkZ2VTdGF0ZUZvcm1hdHRlcicsCiAgICAgICAgICB3aWR0aDogICAgIDEwMCwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIG5hbWU6ICAnY2x1c3RlcicsCiAgICAgICAgICB2YWx1ZTogJ2NsdXN0ZXJOYW1lJywKICAgICAgICAgIHNvcnQ6ICBbJ2NsdXN0ZXJOYW1lJywgJ3N0YXRlU29ydCddLAogICAgICAgICAgbGFiZWw6ICdDbHVzdGVyJywKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIG5hbWU6ICAnYXBpVmVyc2lvbicsCiAgICAgICAgICB2YWx1ZTogJ2FwaVZlcnNpb24nLAogICAgICAgICAgc29ydDogICdhcGlWZXJzaW9uJywKICAgICAgICAgIGxhYmVsOiAnQVBJIFZlcnNpb24nLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbmFtZTogICdraW5kJywKICAgICAgICAgIHZhbHVlOiAna2luZCcsCiAgICAgICAgICBzb3J0OiAgJ2tpbmQnLAogICAgICAgICAgbGFiZWw6ICdLaW5kJywKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIG5hbWU6ICAgICAgJ25hbWUnLAogICAgICAgICAgdmFsdWU6ICAgICAnbmFtZScsCiAgICAgICAgICBzb3J0OiAgICAgICduYW1lJywKICAgICAgICAgIGxhYmVsOiAgICAgJ05hbWUnLAogICAgICAgICAgZm9ybWF0dGVyOiAnTGlua0RldGFpbCcsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBuYW1lOiAgJ25hbWVzcGFjZScsCiAgICAgICAgICB2YWx1ZTogJ25hbWVzcGFjZScsCiAgICAgICAgICBzb3J0OiAgJ25hbWVzcGFjZScsCiAgICAgICAgICBsYWJlbDogJ05hbWVzcGFjZScsCiAgICAgICAgfSwKICAgICAgXTsKICAgIH0sCiAgfQp9Owo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetResources.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;EAE7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL;UACE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QAChB,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC;MACH,CAAC;IACH,CAAC;EACH;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetResources.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport SortableTable from '@shell/components/SortableTable';\n\nexport default {\n  name: 'FleetResources',\n\n  components: { SortableTable },\n\n  props: {\n    value: {\n      type:     Object,\n      required: true,\n    },\n    clusterId: {\n      type:     String,\n      required: false,\n      default:  null,\n    },\n  },\n\n  computed: {\n    computedResources() {\n      return this.value.resourcesStatuses;\n    },\n\n    resourceHeaders() {\n      return [\n        {\n          name:      'state',\n          value:     'state',\n          label:     'State',\n          sort:      'stateSort',\n          formatter: 'BadgeStateFormatter',\n          width:     100,\n        },\n        {\n          name:  'cluster',\n          value: 'clusterName',\n          sort:  ['clusterName', 'stateSort'],\n          label: 'Cluster',\n        },\n        {\n          name:  'apiVersion',\n          value: 'apiVersion',\n          sort:  'apiVersion',\n          label: 'API Version',\n        },\n        {\n          name:  'kind',\n          value: 'kind',\n          sort:  'kind',\n          label: 'Kind',\n        },\n        {\n          name:      'name',\n          value:     'name',\n          sort:      'name',\n          label:     'Name',\n          formatter: 'LinkDetail',\n        },\n        {\n          name:  'namespace',\n          value: 'namespace',\n          sort:  'namespace',\n          label: 'Namespace',\n        },\n      ];\n    },\n  }\n};\n</script>\n\n<template>\n  <SortableTable\n    :rows=\"computedResources\"\n    :headers=\"resourceHeaders\"\n    :table-actions=\"false\"\n    :row-actions=\"false\"\n    key-field=\"tableKey\"\n    default-sort-by=\"state\"\n    :paged=\"true\"\n  />\n</template>\n"]}]}