{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/HookOption.vue?vue&type=template&id=348dcaa0&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/HookOption.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/HookOption.vue"], "names": [], "mappings": ";EAsHE,CAAC,CAAC,CAAC,CAAC;IACF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC,CAAC;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrE,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC;QACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC;QACD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC;YACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,CAAC;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB;UACE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/HookOption.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport debounce from 'lodash/debounce';\nimport { RadioGroup } from '@components/Form/Radio';\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport ShellInput from '@shell/components/form/ShellInput';\nimport { _VIEW } from '@shell/config/query-params';\nimport { isEmpty } from '@shell/utils/object';\n\nexport default {\n  emits: ['update:value'],\n\n  props: {\n    mode: {\n      type:     String,\n      required: true,\n    },\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  components: {\n    RadioGroup, LabeledInput, LabeledSelect, ShellInput\n  },\n\n  data() {\n    const selectHook = null;\n\n    const defaultExec = { exec: { command: [] } };\n    const defaultHttpGet = {\n      httpGet: {\n        host:        '',\n        path:        '',\n        port:        null,\n        scheme:      '',\n        httpHeaders: null\n      }\n    };\n\n    return {\n      selectHook,\n      defaultExec,\n      defaultHttpGet,\n      schemeOptions: ['HTTP', 'HTTPS']\n    };\n  },\n\n  computed: {\n    isView() {\n      return this.mode === _VIEW;\n    },\n  },\n\n  created() {\n    if (this.value) {\n      this.selectHook = Object.keys(this.value)[0];\n    }\n\n    if (isEmpty(this.value)) {\n      this.selectHook = 'none';\n    }\n\n    this.queueUpdate = debounce(this.update, 500);\n  },\n\n  methods: {\n    addHeader() {\n      const header = { name: '', value: '' };\n\n      if (!this.value.httpGet.httpHeaders) {\n        this.value.httpGet['httpHeaders'] = [];\n      }\n\n      this.value.httpGet.httpHeaders.push(header);\n    },\n\n    removeHeader(index) {\n      this.value.httpGet.httpHeaders.splice(index, 1);\n    },\n\n    update() {\n      const { ...leftovers } = this.value;\n\n      switch (this.selectHook) {\n      case 'none':\n        this.deleteLeftovers(leftovers);\n        break;\n      case 'exec':\n        this.deleteLeftovers(leftovers);\n        Object.assign(this.value, this.defaultExec);\n        break;\n      case 'httpGet':\n        this.deleteLeftovers(leftovers);\n        Object.assign(this.value, this.defaultHttpGet);\n        break;\n      default:\n        break;\n      }\n\n      this.$emit('update:value', this.value);\n    },\n\n    deleteLeftovers(leftovers) {\n      if (leftovers) {\n        for (const obj in leftovers) {\n          delete this.value[obj];\n        }\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <div>\n    <div class=\"row mb-10\">\n      <RadioGroup\n        v-model:value=\"selectHook\"\n        name=\"selectHook\"\n        :options=\"['none', 'exec', 'httpGet']\"\n        :labels=\"[\n          t('generic.none'),\n          t('workload.container.lifecycleHook.exec.add'),\n          t('workload.container.lifecycleHook.httpGet.add'),\n        ]\"\n        :mode=\"mode\"\n        @update:value=\"update\"\n      />\n    </div>\n\n    <template v-if=\"selectHook === 'exec'\">\n      <div class=\"mb-20 single-value\">\n        <h4>{{ t('workload.container.lifecycleHook.exec.title') }}</h4>\n        <div>\n          <ShellInput\n            v-model:value=\"value.exec.command\"\n            :mode=\"mode\"\n            :label=\"t('workload.container.lifecycleHook.exec.command.label')\"\n            :placeholder=\"t('workload.container.lifecycleHook.exec.command.placeholder', null, true)\"\n            required\n          />\n        </div>\n      </div>\n    </template>\n\n    <template v-if=\"selectHook === 'httpGet'\">\n      <h4>{{ t('workload.container.lifecycleHook.httpGet.title') }}</h4>\n      <div class=\"var-row\">\n        <LabeledInput\n          v-model:value=\"value.httpGet.host\"\n          :label=\"t('workload.container.lifecycleHook.httpGet.host.label')\"\n          :placeholder=\"t('workload.container.lifecycleHook.httpGet.host.placeholder')\"\n          :mode=\"mode\"\n          @update:value=\"update\"\n        />\n        <LabeledInput\n          v-model:value=\"value.httpGet.path\"\n          :label=\"t('workload.container.lifecycleHook.httpGet.path.label')\"\n          :placeholder=\"t('workload.container.lifecycleHook.httpGet.path.placeholder')\"\n          :mode=\"mode\"\n          @update:value=\"update\"\n        />\n        <LabeledInput\n          v-model:value.number=\"value.httpGet.port\"\n          type=\"number\"\n          :label=\"t('workload.container.lifecycleHook.httpGet.port.label')\"\n          :placeholder=\"t('workload.container.lifecycleHook.httpGet.port.placeholder')\"\n          :mode=\"mode\"\n          required\n          @update:value=\"update\"\n        />\n        <LabeledSelect\n          v-model:value=\"value.httpGet.scheme\"\n          :label=\"t('workload.container.lifecycleHook.httpGet.scheme.label')\"\n          :placeholder=\"t('workload.container.lifecycleHook.httpGet.scheme.placeholder')\"\n          :options=\"schemeOptions\"\n          :mode=\"mode\"\n          @update:value=\"update\"\n        />\n      </div>\n\n      <h4>{{ t('workload.container.lifecycleHook.httpHeaders.title') }}</h4>\n      <div\n        v-for=\"(header, index) in value.httpGet.httpHeaders\"\n        :key=\"index\"\n        class=\"var-row\"\n        data-testid=\"hookoption-header-row\"\n      >\n        <LabeledInput\n          v-model:value=\"value.httpGet.httpHeaders[index].name\"\n          :label=\"t('workload.container.lifecycleHook.httpHeaders.name.label')\"\n          :placeholder=\"t('workload.container.lifecycleHook.httpHeaders.name.placeholder')\"\n          class=\"single-value\"\n          :mode=\"mode\"\n          required\n          @update:value=\"update\"\n        />\n        <LabeledInput\n          v-model:value=\"value.httpGet.httpHeaders[index].value\"\n          :label=\"t('workload.container.lifecycleHook.httpHeaders.value.label')\"\n          :placeholder=\"t('workload.container.lifecycleHook.httpHeaders.value.placeholder')\"\n          class=\"single-value\"\n          :mode=\"mode\"\n          @update:value=\"update\"\n        />\n        <div class=\"remove\">\n          <button\n            v-if=\"!isView\"\n            type=\"button\"\n            class=\"btn role-link ml0\"\n            :disabled=\"mode==='view'\"\n            @click.stop=\"removeHeader(index)\"\n          >\n            <t k=\"generic.remove\" />\n          </button>\n        </div>\n      </div>\n\n      <div>\n        <button\n          v-if=\"!isView\"\n          type=\"button\"\n          class=\"btn role-link mb-20\"\n          :disabled=\"mode === 'view'\"\n          data-testid=\"hookoption-add-header-button\"\n          @click.stop=\"addHeader\"\n        >\n          Add Header\n        </button>\n      </div>\n    </template>\n  </div>\n</template>\n\n<style lang='scss' scoped>\n.var-row{\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr 1fr 100px;\n  grid-column-gap: 20px;\n  margin-bottom: 20px;\n  align-items: center;\n\n  .single-value {\n    grid-column: span 2;\n  }\n\n  .labeled-select {\n    min-height: $input-height;\n  }\n  .remove BUTTON {\n    padding: 0px;\n  }\n}\n</style>\n"]}]}