{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Favorite.vue?vue&type=template&id=40c3f40b&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Favorite.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGkKICAgIDp0YWJpbmRleD0iMCIKICAgIDphcmlhLXByZXNzZWQ9IiEhaXNGYXZvcml0ZSIKICAgIGNsYXNzPSJmYXZvcml0ZSBpY29uIgogICAgOmNsYXNzPSJ7J2ljb24tc3Rhci1vcGVuJzogIWlzRmF2b3JpdGUsICdpY29uLXN0YXInOiBpc0Zhdm9yaXRlfSIKICAgIGFyaWEtcm9sZT0iYnV0dG9uIgogICAgOmFyaWEtbGFiZWw9ImFyaWFMYWJlbCIKICAgIEBjbGljay5zdG9wLnByZXZlbnQ9InRvZ2dsZSIKICAgIEBrZXlkb3duLmVudGVyLnByZXZlbnQ9InRvZ2dsZSIKICAgIEBrZXlkb3duLnNwYWNlLnByZXZlbnQ9InRvZ2dsZSIKICAvPgo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Favorite.vue"], "names": [], "mappings": ";EA+BE,CAAC;IACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Favorite.vue", "sourceRoot": "", "sourcesContent": ["<script>\nexport default {\n  props: {\n    resource: {\n      type:     String,\n      required: true,\n    }\n  },\n\n  computed: {\n    isFavorite() {\n      return this.$store.getters['type-map/isFavorite'](this.resource);\n    },\n    ariaLabel() {\n      return this.t(`resourceDetail.masthead.ariaLabel.${ this.isFavorite ? 'unfavoriteAction' : 'favoriteAction' }`, { resource: this.resource });\n    }\n  },\n\n  methods: {\n    toggle() {\n      if ( this.isFavorite ) {\n        this.$store.dispatch('type-map/removeFavorite', this.resource);\n      } else {\n        this.$store.dispatch('type-map/addFavorite', this.resource);\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <i\n    :tabindex=\"0\"\n    :aria-pressed=\"!!isFavorite\"\n    class=\"favorite icon\"\n    :class=\"{'icon-star-open': !isFavorite, 'icon-star': isFavorite}\"\n    aria-role=\"button\"\n    :aria-label=\"ariaLabel\"\n    @click.stop.prevent=\"toggle\"\n    @keydown.enter.prevent=\"toggle\"\n    @keydown.space.prevent=\"toggle\"\n  />\n</template>\n\n<style lang=\"scss\" scoped>\n  .favorite {\n    position: relative;\n    cursor: pointer;\n    font-size: 20px;\n    transform: ease-in-out-all 1s;\n\n    &.icon-star-open {\n      color: var(--muted);\n    }\n\n    &.icon-star-closed {\n      color: var(--body-text);\n    }\n  }\n</style>\n"]}]}