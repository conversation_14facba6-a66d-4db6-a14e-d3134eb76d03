{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Dialog.vue?vue&type=template&id=530e148b&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Dialog.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGFwcC1tb2RhbAogICAgdi1pZj0iIWNsb3NlZCIKICAgIDpuYW1lPSJuYW1lIgogICAgaGVpZ2h0PSJhdXRvIgogICAgOnNjcm9sbGFibGU9InRydWUiCiAgICA6dHJpZ2dlci1mb2N1cy10cmFwPSJ0cnVlIgogICAgOnJldHVybi1mb2N1cy1zZWxlY3Rvcj0icmV0dXJuRm9jdXNTZWxlY3RvciIKICAgIDpyZXR1cm4tZm9jdXMtZmlyc3QtaXRlcmFibGUtbm9kZS1zZWxlY3Rvcj0icmV0dXJuRm9jdXNGaXJzdEl0ZXJhYmxlTm9kZVNlbGVjdG9yIgogICAgQGNsb3NlPSJjbG9zZURpYWxvZyhmYWxzZSkiCiAgICBAYmVmb3JlLW9wZW49ImJlZm9yZU9wZW4iCiAgPgogICAgPGRpdiBjbGFzcz0ibW9kYWwtZGlhbG9nIj4KICAgICAgPGg0PgogICAgICAgIHt7IHRpdGxlIH19CiAgICAgIDwvaDQ+CiAgICAgIDxzbG90IC8+CiAgICAgIDxkaXYgY2xhc3M9ImRpYWxvZy1idXR0b25zIG10LTIwIj4KICAgICAgICA8c2xvdCBuYW1lPSJidXR0b25zIiAvPgogICAgICAgIDxkaXYgdi1pZj0iISRzbG90cy5idXR0b25zIj4KICAgICAgICAgIDxidXR0b24KICAgICAgICAgICAgY2xhc3M9ImJ0biByb2xlLXNlY29uZGFyeSIKICAgICAgICAgICAgQGNsaWNrPSJjbG9zZURpYWxvZyhmYWxzZSkiCiAgICAgICAgICA+CiAgICAgICAgICAgIHt7IHQoJ2dlbmVyaWMuY2FuY2VsJykgfX0KICAgICAgICAgIDwvYnV0dG9uPgogICAgICAgICAgPGJ1dHRvbgogICAgICAgICAgICB2LWlmPSIhbW9kZSIKICAgICAgICAgICAgY2xhc3M9ImJ0biByb2xlLXByaW1hcnkgbWwtMTAiCiAgICAgICAgICAgIEBjbGljaz0iY2xvc2VEaWFsb2codHJ1ZSkiCiAgICAgICAgICA+CiAgICAgICAgICAgIHt7IHQoJ2dlbmVyaWMub2snKSB9fQogICAgICAgICAgPC9idXR0b24+CiAgICAgICAgICA8QXN5bmNCdXR0b24KICAgICAgICAgICAgdi1lbHNlCiAgICAgICAgICAgIDptb2RlPSJtb2RlIgogICAgICAgICAgICBjbGFzcz0ibWwtMTAiCiAgICAgICAgICAgIEBjbGljaz0ib2siCiAgICAgICAgICAvPgogICAgICAgIDwvZGl2PgogICAgICA8L2Rpdj4KICAgIDwvZGl2PgogIDwvYXBwLW1vZGFsPgo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Dialog.vue"], "names": [], "mappings": ";EAyEE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1B;IACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC;QACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B;YACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B;YACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Dialog.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport AsyncButton from '@shell/components/AsyncButton';\nimport AppModal, { DEFAULT_ITERABLE_NODE_SELECTOR } from '@shell/components/AppModal.vue';\n\nexport default {\n  emits: ['okay', 'closed'],\n\n  components: { AsyncButton, AppModal },\n\n  props: {\n    name: {\n      type:     String,\n      required: true,\n    },\n\n    title: {\n      type:     String,\n      required: true,\n    },\n\n    mode: {\n      type:    String,\n      default: '',\n    },\n\n    /**\n     * forcefully set return focus element based on this selector\n     */\n    returnFocusSelector: {\n      type:    String,\n      default: '',\n    },\n\n    /**\n     * will return focus to the first iterable node of this container select\n     */\n    returnFocusFirstIterableNodeSelector: {\n      type:    String,\n      default: DEFAULT_ITERABLE_NODE_SELECTOR,\n    }\n  },\n\n  data() {\n    return { closed: false };\n  },\n\n  methods: {\n    beforeOpen() {\n      this.closed = false;\n    },\n\n    ok(btnCb) {\n      const callback = (ok) => {\n        btnCb(ok);\n        if (ok) {\n          this.closeDialog(true);\n        }\n      };\n\n      this.$emit('okay', callback);\n    },\n\n    closeDialog(result) {\n      if (!this.closed) {\n        this.$emit('closed', result);\n        this.closed = true;\n      }\n    },\n  }\n};\n</script>\n\n<template>\n  <app-modal\n    v-if=\"!closed\"\n    :name=\"name\"\n    height=\"auto\"\n    :scrollable=\"true\"\n    :trigger-focus-trap=\"true\"\n    :return-focus-selector=\"returnFocusSelector\"\n    :return-focus-first-iterable-node-selector=\"returnFocusFirstIterableNodeSelector\"\n    @close=\"closeDialog(false)\"\n    @before-open=\"beforeOpen\"\n  >\n    <div class=\"modal-dialog\">\n      <h4>\n        {{ title }}\n      </h4>\n      <slot />\n      <div class=\"dialog-buttons mt-20\">\n        <slot name=\"buttons\" />\n        <div v-if=\"!$slots.buttons\">\n          <button\n            class=\"btn role-secondary\"\n            @click=\"closeDialog(false)\"\n          >\n            {{ t('generic.cancel') }}\n          </button>\n          <button\n            v-if=\"!mode\"\n            class=\"btn role-primary ml-10\"\n            @click=\"closeDialog(true)\"\n          >\n            {{ t('generic.ok') }}\n          </button>\n          <AsyncButton\n            v-else\n            :mode=\"mode\"\n            class=\"ml-10\"\n            @click=\"ok\"\n          />\n        </div>\n      </div>\n    </div>\n  </app-modal>\n</template>\n\n<style lang=\"scss\" scoped>\n  .modal-dialog {\n    padding: 10px;\n\n    h4 {\n      font-weight: bold;\n    }\n\n    .dialog-buttons {\n      display: flex;\n      justify-content: flex-end;\n      margin-top: 10px;\n\n      > *:not(:last-child) {\n        margin-right: 10px;\n      }\n    }\n  }\n</style>\n"]}]}