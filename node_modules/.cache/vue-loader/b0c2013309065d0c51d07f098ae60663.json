{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/RelatedResources.vue?vue&type=template&id=0ff276bd", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/RelatedResources.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPFJlc291cmNlVGFibGUKICAgIDpzY2hlbWE9Im51bGwiCiAgICA6cm93cz0icm93cyIKICAgIDpoZWFkZXJzPSJoZWFkZXJzIgogICAgOnNlYXJjaD0iZmFsc2UiCiAgICA6dGFibGUtYWN0aW9ucz0iZmFsc2UiCiAgICA6bmFtZXNwYWNlZD0idHJ1ZSIKICAgIDptYW5nbGUtYWN0aW9uLXJlc291cmNlcz0iZ2V0UmVhbFJlc291cmNlcyIKICAgIHBhZ2luZy1sYWJlbD0ic29ydGFibGVUYWJsZS5wYWdpbmcuZ2VuZXJpYyIKICAgIDpncm91cGFibGU9ImZhbHNlIgogID4KICAgIDx0ZW1wbGF0ZSAjY2VsbDpzdGF0ZT0ie3Jvd30iPgogICAgICA8QmFkZ2VTdGF0ZQogICAgICAgIHYtaWY9InJvdy5yZWFsIgogICAgICAgIDp2YWx1ZT0icm93LnJlYWwiCiAgICAgIC8+CiAgICAgIDxCYWRnZVN0YXRlCiAgICAgICAgdi1lbHNlCiAgICAgICAgOnZhbHVlPSJyb3ciCiAgICAgIC8+CiAgICA8L3RlbXBsYXRlPgogIDwvUmVzb3VyY2VUYWJsZT4K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/RelatedResources.vue"], "names": [], "mappings": ";EAoKE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB;IACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/RelatedResources.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport ResourceTable from '@shell/components/ResourceTable';\nimport { colorForState, stateDisplay } from '@shell/plugins/dashboard-store/resource-class';\nimport { NAME, NAMESPACE, STATE, TYPE } from '@shell/config/table-headers';\nimport { sortableNumericSuffix } from '@shell/utils/sort';\nimport { NAME as EXPLORER } from '@shell/config/product/explorer';\nimport { BadgeState } from '@components/BadgeState';\n\nexport default {\n  components: { ResourceTable, BadgeState },\n\n  props: {\n    value: {\n      type:     Object,\n      required: true,\n    },\n\n    rel: {\n      type:    String,\n      default: null,\n    },\n\n    direction: {\n      type:    String,\n      default: 'to'\n    },\n\n    ignoreTypes: {\n      type:    Array,\n      default: () => []\n    }\n  },\n\n  data() {\n    return { loadedResources: 1 };\n  },\n\n  computed: {\n    filteredRelationships() {\n      let all = this.value?.metadata?.relationships || [];\n\n      // @TODO probably will need more flexible filtering here for\n      // related resources other than helm app resources...\n\n      all = all.filter((relationship) => {\n        const type = relationship[`${ this.direction }Type`];\n\n        if (!type || this.ignoreTypes.includes(type)) {\n          return false;\n        }\n\n        if (this.rel && relationship.rel !== this.rel) {\n          return false;\n        }\n\n        return true;\n      });\n\n      return all;\n    },\n\n    rows() {\n      if ( this.loadedResources < 1 ) {\n        // This does nothing except force recompute when loaded resources change below\n        return;\n      }\n\n      const cluster = this.$store.getters['clusterId'];\n      const inStore = this.$store.getters['currentStore']();\n      const out = [];\n\n      for ( const r of this.filteredRelationships) {\n        const state = r.state || 'active';\n        const stateColor = colorForState(state, r.error, r.transitioning);\n        const type = r[`${ this.direction }Type`];\n        const schema = this.$store.getters[`${ inStore }/schemaFor`](type);\n\n        let name = r[`${ this.direction }Id`];\n\n        // Skip things like toType/toNamespace+selector for now\n        if ( !name ) {\n          continue;\n        }\n\n        let namespace = null;\n        const idx = name.indexOf('/');\n        const key = `${ type }/${ namespace }/${ name }`;\n\n        if ( idx > 0 ) {\n          namespace = name.substr(0, idx);\n          name = name.substr(idx + 1);\n        }\n\n        const detailLocation = {\n          name:   `c-cluster-product-resource${ namespace ? '-namespace' : '' }-id`,\n          params: {\n            product:  EXPLORER,\n            cluster:  inStore === 'management' ? 'local' : cluster,\n            resource: type,\n            namespace,\n            id:       name,\n          }\n        };\n\n        out.push({\n          type,\n          real:     this.$store.getters[`${ inStore }/byId`](type, r[`${ this.direction }Id`]),\n          id:       r[`${ this.direction }Id`],\n          state,\n          metadata: { namespace, name },\n          _key:     key,\n\n          name,\n          namespace,\n          nameDisplay: name,\n          nameSort:    sortableNumericSuffix(name).toLowerCase(),\n\n          stateColor,\n          detailLocation,\n          typeDisplay:     this.$store.getters['type-map/labelFor'](schema),\n          stateDisplay:    stateDisplay(state),\n          stateBackground: stateColor.replace('text-', 'bg-'),\n          groupByLabel:    namespace,\n        });\n      }\n\n      return out;\n    },\n\n    headers() {\n      return [\n        STATE,\n        TYPE,\n        NAME,\n        NAMESPACE,\n      ];\n    },\n  },\n\n  methods: {\n    async getRealResources(rows) {\n      const inStore = this.$store.getters['currentStore']();\n\n      const res = await Promise.allSettled(rows.map((row) => {\n        return this.$store.dispatch(`${ inStore }/find`, { type: row.type, id: row.id });\n      }));\n\n      const out = [];\n\n      for ( let i = 0 ; i < res.length ; i++ ) {\n        if ( res[i].status === 'fulfilled' ) {\n          out.push(res[i].value);\n        }\n      }\n\n      this.loadedResources++;\n\n      return out;\n    }\n  },\n};\n</script>\n\n<template>\n  <ResourceTable\n    :schema=\"null\"\n    :rows=\"rows\"\n    :headers=\"headers\"\n    :search=\"false\"\n    :table-actions=\"false\"\n    :namespaced=\"true\"\n    :mangle-action-resources=\"getRealResources\"\n    paging-label=\"sortableTable.paging.generic\"\n    :groupable=\"false\"\n  >\n    <template #cell:state=\"{row}\">\n      <BadgeState\n        v-if=\"row.real\"\n        :value=\"row.real\"\n      />\n      <BadgeState\n        v-else\n        :value=\"row\"\n      />\n    </template>\n  </ResourceTable>\n</template>\n"]}]}