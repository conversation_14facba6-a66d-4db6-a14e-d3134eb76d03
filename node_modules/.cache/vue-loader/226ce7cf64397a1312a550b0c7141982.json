{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Import.vue?vue&type=template&id=e4791144", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Import.vue", "mtime": 1754992537319}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdiBjbGFzcz0icm93IG1iLTEwIGFsaWduLWNlbnRlciI+CiAgICA8ZGl2IGNsYXNzPSJjb2wgc3Bhbi02Ij4KICAgICAgPExhYmVsZWRTZWxlY3QKICAgICAgICB2LWlmPSJjbHVzdGVycy5sZW5ndGgiCiAgICAgICAgOmxvYWRpbmc9ImxvYWRpbmdDbHVzdGVycyIKICAgICAgICA6bW9kZT0ibW9kZSIKICAgICAgICA6dmFsdWU9ImNsdXN0ZXJOYW1lIgogICAgICAgIDpvcHRpb25zPSJjbHVzdGVycyIKICAgICAgICBsYWJlbC1rZXk9ImVrcy5pbXBvcnQubGFiZWwiCiAgICAgICAgOnJ1bGVzPSJydWxlcy5kaXNwbGF5TmFtZSIKICAgICAgICBAc2VsZWN0aW5nPSIkZW1pdCgndXBkYXRlOmNsdXN0ZXJOYW1lJywgJGV2ZW50KSIKICAgICAgLz4KICAgICAgPExhYmVsZWRJbnB1dAogICAgICAgIHYtZWxzZQogICAgICAgIGxhYmVsLWtleT0iZWtzLmltcG9ydC5sYWJlbCIKICAgICAgICA6dmFsdWU9ImNsdXN0ZXJOYW1lIgogICAgICAgIDpydWxlcz0icnVsZXMuZGlzcGxheU5hbWUiCiAgICAgICAgQHVwZGF0ZTp2YWx1ZT0iJGVtaXQoJ3VwZGF0ZTpjbHVzdGVyTmFtZScsICRldmVudCkiCiAgICAgIC8+CiAgICA8L2Rpdj4KICAgIDxkaXYgY2xhc3M9ImNvbCBzcGFuLTYgIj4KICAgICAgPENoZWNrYm94CiAgICAgICAgOnZhbHVlPSJlbmFibGVOZXR3b3JrUG9saWN5IgogICAgICAgIDptb2RlPSJtb2RlIgogICAgICAgIDpsYWJlbD0idCgnZWtzLmVuYWJsZU5ldHdvcmtQb2xpY3kubGFiZWwnKSIKICAgICAgICBAdXBkYXRlOnZhbHVlPSIkZW1pdCgndXBkYXRlOmVuYWJsZU5ldHdvcmtQb2xpY3knLCAkZXZlbnQpIgogICAgICAvPgogICAgPC9kaXY+CiAgPC9kaXY+Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Import.vue"], "names": [], "mappings": ";EAmGE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjD,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpD,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5D,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Import.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { _EDIT } from '@shell/config/query-params';\nimport debounce from 'lodash/debounce';\nimport LabeledSelect from '@shell/components/form/LabeledSelect.vue';\nimport LabeledInput from '@components/Form/LabeledInput/LabeledInput.vue';\nimport Checkbox from '@components/Form/Checkbox/Checkbox.vue';\n\nexport default {\n  name: 'ImportEKS',\n\n  emits: ['update:clusterName', 'error', 'update:enableNetworkPolicy'],\n\n  components: {\n    LabeledSelect, LabeledInput, Checkbox\n  },\n\n  props: {\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n\n    // name of cluster to be imported\n    // this wont necessarily align with normanCluster.name as it would w/ provisioning\n    clusterName: {\n      type:    String,\n      default: ''\n    },\n\n    credential: {\n      type:    String,\n      default: null\n    },\n\n    region: {\n      type:    String,\n      default: ''\n    },\n\n    enableNetworkPolicy: {\n      type:    Boolean,\n      default: false\n    },\n\n    rules: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  created() {\n    this.debouncedlistEKSClusters = debounce(this.listEKSClusters, 200);\n    this.debouncedlistEKSClusters();\n  },\n\n  data() {\n    return { loadingClusters: false, clusters: [] };\n  },\n\n  watch: {\n    region() {\n      this.debouncedlistEKSClusters();\n    },\n    cloudCredentialId() {\n      this.debouncedlistEKSClusters();\n    }\n  },\n\n  methods: {\n\n    async listEKSClusters() {\n      if (!this.region || !this.credential) {\n        return;\n      }\n      this.loadingClusters = true;\n      try {\n        const eksClient = await this.$store.dispatch('aws/eks', { region: this.region, cloudCredentialId: this.credential });\n\n        const res = await eksClient.listClusters({});\n        const clusters = res?.clusters;\n\n        if (!clusters) {\n          return;\n        }\n\n        this.clusters = res?.clusters;\n      } catch (err) {\n        this.$emit('error', err);\n      }\n\n      this.loadingClusters = false;\n    }\n  }\n};\n</script>\n\n<template>\n  <div class=\"row mb-10 align-center\">\n    <div class=\"col span-6\">\n      <LabeledSelect\n        v-if=\"clusters.length\"\n        :loading=\"loadingClusters\"\n        :mode=\"mode\"\n        :value=\"clusterName\"\n        :options=\"clusters\"\n        label-key=\"eks.import.label\"\n        :rules=\"rules.displayName\"\n        @selecting=\"$emit('update:clusterName', $event)\"\n      />\n      <LabeledInput\n        v-else\n        label-key=\"eks.import.label\"\n        :value=\"clusterName\"\n        :rules=\"rules.displayName\"\n        @update:value=\"$emit('update:clusterName', $event)\"\n      />\n    </div>\n    <div class=\"col span-6 \">\n      <Checkbox\n        :value=\"enableNetworkPolicy\"\n        :mode=\"mode\"\n        :label=\"t('eks.enableNetworkPolicy.label')\"\n        @update:value=\"$emit('update:enableNetworkPolicy', $event)\"\n      />\n    </div>\n  </div>\n</template>\n"]}]}