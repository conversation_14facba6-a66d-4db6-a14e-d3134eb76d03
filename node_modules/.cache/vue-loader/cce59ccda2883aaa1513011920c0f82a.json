{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Circle.vue?vue&type=style&index=0&id=d1de6198&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Circle.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CnN2Zy50ZXh0IHsKICBmaWxsOiByZWQKfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Circle.vue"], "names": [], "mappings": ";AAgKA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AACV", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Circle.vue", "sourceRoot": "", "sourcesContent": ["<script>\nlet id = 0;\n\nexport default {\n  props: {\n    percentage: {\n      type:    Number,\n      default: 0.75\n    },\n    strokeWidth: {\n      type:    Number,\n      default: 22\n    },\n    primaryStrokeColor: {\n      type:     String,\n      required: true\n    },\n    primaryStrokeGradientColor: {\n      type:    String,\n      default: null\n    },\n    secondaryStrokeColor: {\n      type:     String,\n      required: true\n    },\n    secondaryStrokeGradientColor: {\n      type:    String,\n      default: null\n    },\n    rotate: {\n      type:    Number,\n      default: 90\n    },\n    showText: {\n      type:    Boolean,\n      default: false\n    },\n  },\n  data() {\n    return { id: id++ };\n  },\n  computed: {\n    viewportSize() {\n      return 100;\n    },\n    radius() {\n      const outerRadius = this.viewportSize / 2;\n      const halfStrokeWidth = this.strokeWidth / 2;\n\n      return outerRadius - halfStrokeWidth;\n    },\n    center() {\n      return this.viewportSize / 2;\n    },\n    viewBox() {\n      return `0 0 ${ this.viewportSize } ${ this.viewportSize }`;\n    },\n    circumference() {\n      return 2 * Math.PI * this.radius;\n    },\n    transform() {\n      return `rotate(${ this.rotate }, ${ this.center }, ${ this.center })`;\n    },\n    strokeDasharray() {\n      // This needs to be the circumference of the circle in order to allow the path to be filled\n      return this.circumference;\n    },\n    strokeDashoffset() {\n      // This needs to be the percentage of the circumference that we won't show as it will hide that portion of the path\n      return this.circumference * (1 - this.percentage);\n    },\n    primaryStrokeColorId() {\n      return `primary-${ id }`;\n    },\n    secondaryStrokeColorId() {\n      return `secondary-${ id }`;\n    },\n    parsePercentage() {\n      return parseInt(this.percentage * 100) || 0;\n    },\n  }\n};\n\n</script>\n\n<template>\n  <svg\n    class=\"circle\"\n    width=\"100%\"\n    height=\"100%\"\n    :viewBox=\"viewBox\"\n  >\n    <g :transform=\"transform\">\n      <defs>\n        <linearGradient\n          :id=\"primaryStrokeColorId\"\n          x1=\"0%\"\n          y1=\"0%\"\n          x2=\"100%\"\n          y2=\"0%\"\n        >\n          <stop\n            offset=\"50%\"\n            :stop-color=\"primaryStrokeGradientColor || primaryStrokeColor\"\n          />\n          <stop\n            offset=\"100%\"\n            :stop-color=\"primaryStrokeColor\"\n          />\n        </linearGradient>\n        <linearGradient\n          :id=\"secondaryStrokeColorId\"\n          x1=\"0%\"\n          y1=\"0%\"\n          x2=\"100%\"\n          y2=\"0%\"\n        >\n          <stop\n            offset=\"50%\"\n            :stop-color=\"secondaryStrokeGradientColor || secondaryStrokeColor\"\n          />\n          <stop\n            offset=\"100%\"\n            :stop-color=\"secondaryStrokeColor\"\n          />\n        </linearGradient>\n      </defs>\n      <circle\n        :r=\"radius\"\n        :cy=\"center\"\n        :cx=\"center\"\n        :stroke-width=\"strokeWidth\"\n        :stroke=\"`url(#${secondaryStrokeColorId})`\"\n        fill=\"none\"\n      />\n      <circle\n        :r=\"radius\"\n        :cy=\"center\"\n        :cx=\"center\"\n        :stroke-width=\"strokeWidth\"\n        :stroke=\"`url(#${primaryStrokeColorId})`\"\n        :stroke-dasharray=\"circumference\"\n        :stroke-dashoffset=\"circumference * (1 - percentage)\"\n        fill=\"none\"\n      />\n    </g>\n\n    <text\n      v-if=\"showText\"\n      :x=\"center\"\n      :y=\"center\"\n      style=\"font-size: 25; dominant-baseline:  middle; text-anchor:middle;\"\n      :fill=\"`url(#${primaryStrokeColorId})`\"\n    >\n      {{ parsePercentage }}%\n    </text>\n  </svg>\n</template>\n\n<style lang=\"scss\" scoped>\nsvg.text {\n  fill: red\n}\n</style>\n"]}]}