{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Members/ClusterPermissionsEditor.vue?vue&type=style&index=0&id=d8a00d76&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Members/ClusterPermissionsEditor.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQokZGV0YWlsU2l6ZTogMTFweDsNCg0KOmRlZXAoKSAudHlwZS1kZXNjcmlwdGlvbiB7DQogICAgZm9udC1zaXplOiAkZGV0YWlsU2l6ZTsNCn0NCg0KbGFiZWwucmFkaW8gew0KICBmb250LXNpemU6IDE2cHg7DQp9DQoNCi5jdXN0b20tcGVybWlzc2lvbnMgew0KICBkaXNwbGF5OiBncmlkOw0KICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmciAxZnIgMWZyOw0KICAmLnR3by1jb2x1bW4gew0KICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyIDFmcjsNCiAgfQ0KICA6ZGVlcCgpIC5jaGVja2JveC1sYWJlbCB7DQogICAgbWFyZ2luLXJpZ2h0OiAwOw0KICB9DQp9DQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Members/ClusterPermissionsEditor.vue"], "names": [], "mappings": ";AAqSA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;AAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChC;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACjB;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Members/ClusterPermissionsEditor.vue", "sourceRoot": "", "sourcesContent": ["\r\n<script>\r\nimport CreateEditView from '@shell/mixins/create-edit-view';\r\nimport SelectPrincipal from '@shell/components/auth/SelectPrincipal';\r\nimport { MANAGEMENT, NORMAN } from '@shell/config/types';\r\nimport { RadioGroup } from '@components/Form/Radio';\r\nimport { Card } from '@components/Card';\r\nimport Loading from '@shell/components/Loading';\r\nimport { Checkbox } from '@components/Form/Checkbox';\r\nimport { DESCRIPTION } from '@shell/config/labels-annotations';\r\n\r\nexport function canViewClusterPermissionsEditor(store) {\r\n  return !!store.getters['management/schemaFor'](MANAGEMENT.CLUSTER_ROLE_TEMPLATE_BINDING) &&\r\n    !!store.getters['management/schemaFor'](MANAGEMENT.ROLE_TEMPLATE) &&\r\n    !!store.getters['management/schemaFor'](MANAGEMENT.USER);\r\n}\r\n\r\nexport default {\r\n  emits: ['update:value'],\r\n\r\n  components: {\r\n    Card,\r\n    Checkbox,\r\n    Loading,\r\n    RadioGroup,\r\n    SelectPrincipal\r\n  },\r\n\r\n  mixins: [CreateEditView],\r\n\r\n  props: {\r\n    value: {\r\n      type:     Array,\r\n      required: true\r\n    },\r\n\r\n    useTwoColumnsForCustom: {\r\n      type:    Boolean,\r\n      default: false\r\n    },\r\n\r\n    clusterName: {\r\n      type:    String,\r\n      default: null\r\n    }\r\n  },\r\n  async fetch() {\r\n    const [, roleTemplates] = await Promise.all([\r\n      this.$store.dispatch('management/findAll', { type: MANAGEMENT.USER }),\r\n      this.$store.dispatch('management/findAll', { type: MANAGEMENT.ROLE_TEMPLATE })\r\n    ]);\r\n\r\n    this.roleTemplates = roleTemplates;\r\n  },\r\n  data() {\r\n    return {\r\n      customPermissions: [\r\n        {\r\n          label: this.t('members.clusterPermissions.createProjects'),\r\n          key:   'projects-create',\r\n          value: false\r\n        },\r\n        {\r\n          label: this.t('members.clusterPermissions.manageClusterBackups'),\r\n          key:   'backups-manage',\r\n          value: false\r\n        },\r\n        {\r\n          label: this.t('members.clusterPermissions.manageClusterCatalogs'),\r\n          key:   'clustercatalogs-manage',\r\n          value: false\r\n        },\r\n        {\r\n          label: this.t('members.clusterPermissions.manageClusterMembers'),\r\n          key:   'clusterroletemplatebindings-manage',\r\n          value: false\r\n        },\r\n        {\r\n          label: this.t('members.clusterPermissions.manageNavlinks'),\r\n          key:   'navlinks-manage',\r\n          value: false\r\n        },\r\n        {\r\n          label: this.t('members.clusterPermissions.manageNodes'),\r\n          key:   'nodes-manage',\r\n          value: false\r\n        },\r\n        {\r\n          label: this.t('members.clusterPermissions.manageStorage'),\r\n          key:   'storage-manage',\r\n          value: false\r\n        },\r\n        {\r\n          label: this.t('members.clusterPermissions.viewAllProjects'),\r\n          key:   'projects-view',\r\n          value: false\r\n        },\r\n        {\r\n          label: this.t('members.clusterPermissions.viewClusterCatalogs'),\r\n          key:   'clustercatalogs-view',\r\n          value: false\r\n        },\r\n        {\r\n          label: this.t('members.clusterPermissions.viewClusterMembers'),\r\n          key:   'clusterroletemplatebindings-view',\r\n          value: false\r\n        },\r\n        {\r\n          label: this.t('members.clusterPermissions.viewNodes'),\r\n          key:   'nodes-view',\r\n          value: false\r\n        }\r\n      ],\r\n      permissionGroup: 'member',\r\n      custom:          {},\r\n      roleTemplates:   [],\r\n      principalId:     '',\r\n      bindings:        []\r\n    };\r\n  },\r\n  computed: {\r\n    customRoles() {\r\n      return this.roleTemplates\r\n        .filter((role) => {\r\n          return !role.builtin && !role.external && !role.hidden && role.context === 'cluster';\r\n        });\r\n    },\r\n    roleTemplateIds() {\r\n      if (this.permissionGroup === 'owner') {\r\n        return ['cluster-owner'];\r\n      }\r\n\r\n      if (this.permissionGroup === 'member') {\r\n        return ['cluster-member'];\r\n      }\r\n\r\n      if (this.permissionGroup === 'custom') {\r\n        return this.customPermissions\r\n          .filter((permission) => permission.value)\r\n          .map((permission) => permission.key);\r\n      }\r\n\r\n      return [this.permissionGroup];\r\n    },\r\n    options() {\r\n      const customRoles = this.customRoles.map((role) => ({\r\n        label:       role.nameDisplay,\r\n        description: role.description || role.metadata?.annotations?.[DESCRIPTION] || this.t('members.clusterPermissions.noDescription'),\r\n        value:       role.id\r\n      }));\r\n\r\n      return [\r\n        {\r\n          label:       this.t('members.clusterPermissions.owner.label'),\r\n          description: this.t('members.clusterPermissions.owner.description'),\r\n          value:       'owner'\r\n        },\r\n        {\r\n          label:       this.t('members.clusterPermissions.member.label'),\r\n          description: this.t('members.clusterPermissions.member.description'),\r\n          value:       'member'\r\n        },\r\n        ...customRoles,\r\n        {\r\n          label:       this.t('members.clusterPermissions.custom.label'),\r\n          description: this.t('members.clusterPermissions.custom.description'),\r\n          value:       'custom'\r\n        }\r\n      ];\r\n    },\r\n    principal() {\r\n      const principalId = this.principalId.replace(/\\//g, '%2F');\r\n\r\n      return this.$store.dispatch('rancher/find', {\r\n        type: NORMAN.PRINCIPAL,\r\n        id:   this.principalId,\r\n        opt:  { url: `/v3/principals/${ principalId }` }\r\n      }, { root: true });\r\n    },\r\n    customPermissionsUpdate() {\r\n      return this.customPermissions.reduce((acc, customPermissionsItem) => {\r\n        const lockedExist = this.roleTemplates.find((roleTemplateItem) => roleTemplateItem.id === customPermissionsItem.key);\r\n\r\n        if (lockedExist.locked) {\r\n          customPermissionsItem['locked'] = true;\r\n          customPermissionsItem['tooltip'] = this.t('members.clusterPermissions.custom.lockedRole');\r\n        }\r\n\r\n        return [...acc, customPermissionsItem];\r\n      }, []);\r\n    }\r\n  },\r\n\r\n  watch: {\r\n    roleTemplateIds() {\r\n      this.updateBindings();\r\n    }\r\n  },\r\n  methods: {\r\n    async principalProperty() {\r\n      const principal = await this.principal;\r\n\r\n      return principal?.principalType === 'group' ? 'groupPrincipalId' : 'userPrincipalId';\r\n    },\r\n\r\n    onAdd(principalId) {\r\n      this['principalId'] = principalId;\r\n      this.updateBindings();\r\n    },\r\n\r\n    async updateBindings() {\r\n      if (this.principalId) {\r\n        const principalProperty = await this.principalProperty();\r\n        const bindingPromises = this.roleTemplateIds.map((id) => this.$store.dispatch(`rancher/create`, {\r\n          type:                NORMAN.CLUSTER_ROLE_TEMPLATE_BINDING,\r\n          clusterId:           this.clusterName,\r\n          roleTemplateId:      id,\r\n          [principalProperty]: this.principalId\r\n        }));\r\n\r\n        const bindings = await Promise.all(bindingPromises);\r\n\r\n        this.$emit('update:value', bindings);\r\n      }\r\n    }\r\n  },\r\n};\r\n</script>\r\n<template>\r\n  <Loading v-if=\"$fetchState.pending\" />\r\n  <div\r\n    v-else\r\n    class=\"cluster-permissions-editor\"\r\n  >\r\n    <div class=\"row mt-10\">\r\n      <div class=\"col span-12\">\r\n        <SelectPrincipal\r\n          v-focus\r\n          class=\"mb-20\"\r\n          :mode=\"mode\"\r\n          :retain-selection=\"true\"\r\n          data-testid=\"cluster-member-select\"\r\n          @add=\"onAdd\"\r\n        />\r\n      </div>\r\n    </div>\r\n\r\n    <Card\r\n      class=\"m-0\"\r\n      :show-highlight-border=\"false\"\r\n      :show-actions=\"false\"\r\n    >\r\n      <template v-slot:title>\r\n        <div class=\"type-title\">\r\n          <h3>{{ t('members.clusterPermissions.label') }}</h3>\r\n          <div class=\"type-description\">\r\n            {{ t('members.clusterPermissions.description') }}\r\n          </div>\r\n        </div>\r\n      </template>\r\n      <template v-slot:body>\r\n        <RadioGroup\r\n          v-model:value=\"permissionGroup\"\r\n          :options=\"options\"\r\n          name=\"permission-group\"\r\n        />\r\n        <div\r\n          v-if=\"permissionGroup === 'custom'\"\r\n          class=\"custom-permissions ml-20 mt-10\"\r\n          :class=\"{'two-column': useTwoColumnsForCustom}\"\r\n        >\r\n          <div\r\n            v-for=\"(permission, i) in customPermissionsUpdate\"\r\n            :key=\"i\"\r\n          >\r\n            <Checkbox\r\n              v-model:value=\"permission.value\"\r\n              :disabled=\"permission.locked\"\r\n              class=\"mb-5\"\r\n              :label=\"permission.label\"\r\n            />\r\n            <i\r\n              v-if=\"permission.locked\"\r\n              v-clean-tooltip=\"permission.tooltip\"\r\n              class=\"icon icon-lock icon-fw\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </template>\r\n    </Card>\r\n  </div>\r\n</template>\r\n<style lang=\"scss\" scoped>\r\n$detailSize: 11px;\r\n\r\n:deep() .type-description {\r\n    font-size: $detailSize;\r\n}\r\n\r\nlabel.radio {\r\n  font-size: 16px;\r\n}\r\n\r\n.custom-permissions {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr 1fr;\r\n  &.two-column {\r\n    grid-template-columns: 1fr 1fr;\r\n  }\r\n  :deep() .checkbox-label {\r\n    margin-right: 0;\r\n  }\r\n}\r\n</style>\r\n"]}]}