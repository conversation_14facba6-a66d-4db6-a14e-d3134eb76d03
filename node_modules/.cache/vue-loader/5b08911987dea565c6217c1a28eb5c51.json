{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/explorer/ConfigBadge.vue?vue&type=style&index=0&id=579db7f8&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/explorer/ConfigBadge.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLmJhZGdlLWluc3RhbGw6aG92ZXIgewogICAgY3Vyc29yOiBwb2ludGVyOwogIH0KICAuYmFkZ2UtaW5zdGFsbCB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgbWFyZ2luLWxlZnQ6IDEwcHg7CgogICAgJjpob3ZlciB7CiAgICAgIGJvcmRlci1jb2xvcjogdmFyKC0tbGlnaHRlc3QpOwogICAgfQoKICAgID4gSSB7CiAgICAgIGxpbmUtaGVpZ2h0OiBpbmhlcml0OwogICAgfQoKICAgICY6Zm9jdXMgewogICAgICBvdXRsaW5lOiAwOwogICAgfQogIH0KCg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/explorer/ConfigBadge.vue"], "names": [], "mappings": ";EA2CE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B;;IAEA,EAAE,EAAE;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/explorer/ConfigBadge.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { _EDIT } from '@shell/config/query-params';\nexport default {\n  props: {\n    cluster: {\n      type:     Object,\n      required: true,\n    },\n  },\n\n  name: 'ConfigBadge',\n\n  computed: {\n    tooltip() {\n      return this.t('clusterBadge.customizeAppearance');\n    }\n  },\n  methods: {\n    customBadgeDialog() {\n      this.$store.dispatch('cluster/promptModal', { component: 'AddCustomBadgeDialog', componentProps: { mode: _EDIT } });\n    },\n  },\n};\n</script>\n\n<template>\n  <div class=\"config-badge\">\n    <div>\n      <button\n        v-clean-tooltip=\"{content: tooltip, triggers: ['hover', 'touch', 'focus'] }\"\n        v-stripped-aria-label=\"tooltip\"\n        class=\"badge-install btn btn-sm role-secondary\"\n        data-testid=\"add-custom-cluster-badge\"\n        role=\"button\"\n        @click=\"customBadgeDialog\"\n      >\n        <i class=\"icon icon-brush-icon\" />\n      </button>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .badge-install:hover {\n    cursor: pointer;\n  }\n  .badge-install {\n    display: flex;\n    margin-left: 10px;\n\n    &:hover {\n      border-color: var(--lightest);\n    }\n\n    > I {\n      line-height: inherit;\n    }\n\n    &:focus {\n      outline: 0;\n    }\n  }\n\n</style>\n"]}]}