{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/GetCredential.vue?vue&type=style&index=0&id=ca33f1b4&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/GetCredential.vue", "mtime": 1754995886426}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLnNlbGVjdC1jcmVkZW50aWFscyB7CiAgICBmbGV4LWdyb3c6IDE7IC8vIERvIGdyb3cgd2hlbiBvbiBvd24KICAgICZfX3Nob3dpbmdGb3JtIHsKICAgICAgZmxleC1ncm93OiAwOyAvLyBEb24ndCBncm93IHdoZW4gaW4gcmtlMiBmb3JtCiAgICB9CiAgfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/GetCredential.vue"], "names": [], "mappings": ";EAuPE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9C;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/GetCredential.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport Loading from '@shell/components/Loading';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport { NORMAN, DEFAULT_WORKSPACE } from '@shell/config/types';\nimport CreateEditView from '@shell/mixins/create-edit-view';\nimport CruResource from '@shell/components/CruResource';\nimport NameNsDescription from '@shell/components/form/NameNsDescription';\nimport { Banner } from '@components/Banner';\nimport { CAPI } from '@shell/config/labels-annotations';\nimport { clear } from '@shell/utils/array';\nimport cloneDeep from 'lodash/cloneDeep';\n\nconst _NEW = '_NEW';\nconst _NONE = '_NONE';\n\nexport default {\n  emits: ['update:value', 'credential-created'],\n\n  components: {\n    Loading, LabeledSelect, CruResource, NameNsDescription, Banner\n  },\n\n  mixins: [CreateEditView],\n\n  props: {\n    value: {\n      type:    String,\n      default: null,\n    },\n\n    provider: {\n      type:    String,\n      default: null,\n    },\n\n    cancel: {\n      type:    Function,\n      default: null\n    },\n\n    showingForm: {\n      type:     Boolean,\n      required: true,\n    }\n  },\n\n  async fetch() {\n    this.allCredentials = await this.$store.dispatch('rancher/findAll', { type: NORMAN.CLOUD_CREDENTIAL });\n\n    const field = this.$store.getters['plugins/credentialFieldForDriver'](this.driverName);\n\n    this.newCredential = await this.$store.dispatch('rancher/create', {\n      type:     NORMAN.CLOUD_CREDENTIAL,\n      metadata: {\n        namespace:   DEFAULT_WORKSPACE,\n        annotations: { [CAPI.CREDENTIAL_DRIVER]: this.driverName }\n      },\n      [`${ field }credentialConfig`]: {}\n    });\n\n    if ( this.value ) {\n      this.credentialId = this.value;\n    } else if ( this.filteredCredentials.length === 1 ) {\n      // Auto pick the first credential if there's only one\n      this.credentialId = this.filteredCredentials[0].id;\n    } else if ( !this.filteredCredentials.length ) {\n      this.credentialId = _NEW;\n    }\n  },\n\n  data() {\n    return {\n      allCredentials:                [],\n      nodeComponent:                 null,\n      credentialId:                  this.value || _NONE,\n      newCredential:                 null,\n      credCustomComponentValidation: false,\n      nameRequiredValidation:        false,\n      originalId:                    this.value\n    };\n  },\n\n  computed: {\n    hasCustomCloudCredentialComponent() {\n      const driverName = this.driverName;\n\n      return this.$store.getters['type-map/hasCustomCloudCredentialComponent'](driverName);\n    },\n\n    cloudCredentialComponent() {\n      const driverName = this.driverName;\n\n      return this.$store.getters['type-map/importCloudCredential'](driverName);\n    },\n\n    genericCloudCredentialComponent() {\n      return this.$store.getters['type-map/importCloudCredential']('generic');\n    },\n\n    cloudComponent() {\n      if (this.hasCustomCloudCredentialComponent) {\n        return this.cloudCredentialComponent;\n      }\n\n      return this.genericCloudCredentialComponent;\n    },\n\n    isNone() {\n      return this.credentialId === null || this.credentialId === _NONE;\n    },\n\n    isNew() {\n      return false;\n      // return this.credentialId === _NEW;\n    },\n\n    isPicked() {\n      return !!this.credentialId && !this.isNone && !this.isNew;\n    },\n\n    driverName() {\n      let driver = this.provider;\n\n      // Map providers that share a common credential to one driver\n      driver = this.$store.getters['plugins/credentialDriverFor'](driver);\n\n      return driver;\n    },\n\n    filteredCredentials() {\n      return this.allCredentials.filter((x) => x.provider === this.driverName);\n    },\n\n    options() {\n      const duplicates = {};\n\n      this.filteredCredentials.forEach((cred) => {\n        duplicates[cred.nameDisplay] = duplicates[cred.nameDisplay] === null ? true : null;\n      });\n\n      const out = this.filteredCredentials.map((obj) => ({\n        // if credential name is duplicated we add the id to the label\n        label: duplicates[obj.nameDisplay] ? `${ obj.nameDisplay } (${ obj.id })` : obj.nameDisplay,\n        value: obj.id,\n      }));\n\n      if ( this.originalId && !out.find((x) => x.value === this.originalId) ) {\n        out.unshift({\n          label: `${ this.originalId.replace(/^cattle-global-data:/, '') } (current)`,\n          value: this.originalId\n        });\n      }\n\n      // out.unshift({\n      //   label: this.t('cluster.credential.select.option.new'),\n      //   value: _NEW,\n      // });\n\n      out.unshift({\n        label:    this.t('cluster.credential.select.option.none'),\n        value:    _NONE,\n        disabled: true,\n      });\n\n      return out;\n    },\n\n    validationPassed() {\n      if ( this.credentialId === _NONE ) {\n        return false;\n      }\n\n      if ( this.credentialId === _NEW ) {\n        return this.credCustomComponentValidation && this.nameRequiredValidation;\n      }\n\n      return !!this.credentialId;\n    },\n  },\n\n  watch: {\n    credentialId(val) {\n      if ( val === _NEW || val === _NONE ) {\n        this.$emit('update:value', null);\n      } else {\n        this.$emit('update:value', val);\n      }\n    },\n    'newCredential.name'(newValue) {\n      this.nameRequiredValidation = newValue?.length > 0;\n    }\n  },\n\n  methods: {\n\n    createValidationChanged(passed) {\n      this.credCustomComponentValidation = passed;\n    },\n\n    backToExisting() {\n      this.credentialId = _NONE;\n    },\n    updateCredentialValue(key, value) {\n      this.newCredential.setData(key, value);\n    }\n  },\n};\n</script>\n\n<template>\n  <Loading v-if=\"$fetchState.pending\" />\n  <div v-else>\n    <div>\n      <Banner\n        v-if=\"!credentialId\"\n        label=\"First you need to pick or create the cloud credential that will be used to create the nodes for the cluster...\"\n        color=\"info\"\n      />\n\n      <LabeledSelect\n        v-model:value=\"credentialId\"\n        :label=\"t('cluster.credential.label')\"\n        :options=\"options\"\n        option-key=\"value\"\n        :mode=\"mode\"\n        :selectable=\"option => !option.disabled\"\n        data-testid=\"cluster-prov-select-credential\"\n      />\n    </div>\n\n    <!-- <template\n      v-if=\"isNew && options.length\"\n      #footer-prefix\n    >\n      <button\n        class=\"btn role-secondary\"\n        @click=\"backToExisting()\"\n      >\n        {{ t('cluster.credential.selectExisting.label') }}\n      </button>\n    </template> -->\n  </div>\n\n  <!-- </CruResource> -->\n</template>\n\n<style lang='scss' scoped>\n  .select-credentials {\n    flex-grow: 1; // Do grow when on own\n    &__showingForm {\n      flex-grow: 0; // Don't grow when in rke2 form\n    }\n  }\n</style>\n"]}]}