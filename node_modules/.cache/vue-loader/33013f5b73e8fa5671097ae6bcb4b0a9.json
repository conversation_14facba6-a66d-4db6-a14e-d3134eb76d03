{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/DeveloperInstallDialog.vue?vue&type=style&index=0&id=57c6aa83&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/DeveloperInstallDialog.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLnBsdWdpbi1pbnN0YWxsLWRpYWxvZyB7CiAgICBwYWRkaW5nOiAxMHB4OwoKICAgIGg0IHsKICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICB9CgogICAgLmRpYWxvZy1wYW5lbCB7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICAgIG1pbi1oZWlnaHQ6IDEwMHB4OwoKICAgICAgcCB7CiAgICAgICAgbWFyZ2luLWJvdHRvbTogNXB4OwogICAgICB9CgogICAgICAuZGlhbG9nLWluZm8gewogICAgICAgIGZsZXg6IDE7CiAgICAgIH0KCiAgICAgIC50b2dnbGUtYWR2YW5jZWQgewogICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICAgICAgbWFyZ2luOiAxMHB4IDA7CgogICAgICAgICY6aG92ZXIgewogICAgICAgICAgdGV4dC1kZWNvcmF0aW9uOiBub25lOwogICAgICAgICAgY29sb3I6IHZhcigtLWxpbmspOwogICAgICAgIH0KICAgICAgfQoKICAgICAgLnZlcnNpb24tc2VsZWN0b3IgewogICAgICAgIG1hcmdpbjogMCAxMHB4IDEwcHggMTBweDsKICAgICAgICB3aWR0aDogYXV0bzsKICAgICAgfQogICAgfQoKICAgIC5kaWFsb2ctYnV0dG9ucyB7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7CiAgICAgIG1hcmdpbi10b3A6IDEwcHg7CgogICAgICA+ICo6bm90KDpsYXN0LWNoaWxkKSB7CiAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMHB4OwogICAgICB9CiAgICB9CiAgfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/DeveloperInstallDialog.vue"], "names": [], "mappings": ";EAoNE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEb,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEjB,EAAE;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACpB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACT;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;QAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEhB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpB;IACF;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/DeveloperInstallDialog.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport AsyncButton from '@shell/components/AsyncButton';\nimport AppModal from '@shell/components/AppModal.vue';\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport Checkbox from '@components/Form/Checkbox/Checkbox.vue';\nimport { UI_PLUGIN } from '@shell/config/types';\nimport { UI_PLUGIN_CHART_ANNOTATIONS, UI_PLUGIN_NAMESPACE } from '@shell/config/uiplugins';\n\nexport default {\n  emits: ['closed'],\n\n  components: {\n    AsyncButton,\n    Checkbox,\n    LabeledInput,\n    AppModal,\n  },\n\n  data() {\n    return {\n      name:                '',\n      location:            '',\n      persist:             false,\n      canModifyName:       true,\n      canModifyLocation:   true,\n      showModal:           false,\n      returnFocusSelector: '[data-testid=\"extensions-page-menu\"]'\n    };\n  },\n\n  watch: {\n    name(neu) {\n      if (this.canModifyLocation) {\n        this.location = `/pkg/${ neu }/${ neu }.umd.min.js`;\n      }\n    },\n    location(neu, old) {\n      if (this.canModifyName) {\n        const names = neu.split('/');\n        let last = names[names.length - 1];\n        let index = last.indexOf('.umd.min.js');\n\n        if (index !== -1) {\n          last = last.substr(0, index);\n        } else {\n          index = last.indexOf('.umd.js');\n          if (index !== -1) {\n            last = last.substr(0, index);\n          }\n        }\n\n        this.name = last;\n      }\n    }\n  },\n\n  methods: {\n    showDialog() {\n      this.showModal = true;\n    },\n    closeDialog(result) {\n      this.showModal = false;\n      this.$emit('closed', result);\n    },\n\n    updateName(v) {\n      this.canModifyName = v.length === 0;\n    },\n\n    updateLocation(v) {\n      this.canModifyLocation = v.length === 0;\n    },\n\n    async loadPlugin(btnCb) {\n      let name = this.name;\n      const url = this.location;\n\n      if (!name) {\n        const parts = url.split('/');\n        const n = parts[parts.length - 1];\n\n        // Split on '.'\n        name = n.split('.')[0];\n      }\n\n      // Try and parse version number from the name\n      let version = '0.0.1';\n      let crdName = name;\n\n      const parts = name.split('-');\n\n      if (parts.length >= 2) {\n        version = parts.pop();\n        crdName = parts.join('-');\n      }\n\n      if (this.persist) {\n        const pluginCR = await this.$store.dispatch('management/create', {\n          type:     UI_PLUGIN,\n          metadata: {\n            name,\n            namespace: UI_PLUGIN_NAMESPACE\n          },\n          spec: {\n            plugin: {\n              name:     crdName,\n              version,\n              endpoint: url,\n              noCache:  true,\n              metadata: {\n                developer:                                        'true',\n                direct:                                           'true',\n                [UI_PLUGIN_CHART_ANNOTATIONS.EXTENSIONS_VERSION]: '>= 3',\n              },\n              noAuth: true\n            }\n          }\n        });\n\n        try {\n          await pluginCR.save({ url: `/v1/${ UI_PLUGIN }`, method: 'POST' });\n        } catch (e) {\n          console.error('Could not create CRD for plugin', e); // eslint-disable-line no-console\n          btnCb(false);\n        }\n      }\n\n      this.$plugin.loadAsync(name, url).then(() => {\n        this.closeDialog(true);\n        this.$store.dispatch('growl/success', {\n          title:   this.t('plugins.success.title', { name }),\n          message: this.t('plugins.success.message'),\n          timeout: 3000,\n        }, { root: true });\n        btnCb(true);\n      }).catch((error) => {\n        btnCb(false);\n        // this.closeDialog(false);\n        const message = typeof error === 'object' ? this.t('plugins.error.message') : error;\n\n        this.$store.dispatch('growl/error', {\n          title:   this.t('plugins.error.title'),\n          message,\n          timeout: 5000\n        }, { root: true });\n      });\n    }\n  }\n};\n</script>\n\n<template>\n  <app-modal\n    v-if=\"showModal\"\n    name=\"developerInstallPluginDialog\"\n    height=\"auto\"\n    :scrollable=\"true\"\n    :trigger-focus-trap=\"true\"\n    :return-focus-selector=\"returnFocusSelector\"\n    @close=\"closeDialog()\"\n  >\n    <div class=\"plugin-install-dialog\">\n      <h4>\n        {{ t('plugins.developer.title') }}\n      </h4>\n      <p>\n        {{ t('plugins.developer.prompt') }}\n      </p>\n      <div class=\"custom mt-10\">\n        <div class=\"fields\">\n          <LabeledInput\n            v-model:value=\"location\"\n            v-focus\n            label-key=\"plugins.developer.fields.url\"\n            @update:value=\"updateLocation\"\n          />\n        </div>\n      </div>\n      <div class=\"custom mt-10\">\n        <div class=\"fields\">\n          <LabeledInput\n            v-model:value=\"name\"\n            label-key=\"plugins.developer.fields.name\"\n            @update:value=\"updateName\"\n          />\n        </div>\n        <div class=\"fields mt-10\">\n          <Checkbox\n            v-model:value=\"persist\"\n            label-key=\"plugins.developer.fields.persist\"\n          />\n        </div>\n        <div class=\"dialog-buttons mt-20\">\n          <button\n            class=\"btn role-secondary\"\n            data-testid=\"dev-install-ext-modal-cancel-btn\"\n            @click=\"closeDialog()\"\n          >\n            {{ t('generic.cancel') }}\n          </button>\n          <AsyncButton\n            mode=\"load\"\n            data-testid=\"dev-install-ext-modal-install-btn\"\n            @click=\"loadPlugin\"\n          />\n        </div>\n      </div>\n    </div>\n  </app-modal>\n</template>\n\n<style lang=\"scss\" scoped>\n  .plugin-install-dialog {\n    padding: 10px;\n\n    h4 {\n      font-weight: bold;\n    }\n\n    .dialog-panel {\n      display: flex;\n      flex-direction: column;\n      min-height: 100px;\n\n      p {\n        margin-bottom: 5px;\n      }\n\n      .dialog-info {\n        flex: 1;\n      }\n\n      .toggle-advanced {\n        display: flex;\n        align-items: center;\n        cursor: pointer;\n        margin: 10px 0;\n\n        &:hover {\n          text-decoration: none;\n          color: var(--link);\n        }\n      }\n\n      .version-selector {\n        margin: 0 10px 10px 10px;\n        width: auto;\n      }\n    }\n\n    .dialog-buttons {\n      display: flex;\n      justify-content: flex-end;\n      margin-top: 10px;\n\n      > *:not(:last-child) {\n        margin-right: 10px;\n      }\n    }\n  }\n</style>\n"]}]}