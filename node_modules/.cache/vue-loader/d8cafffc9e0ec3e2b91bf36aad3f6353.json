{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Bar.vue?vue&type=template&id=1c9b98df&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Bar.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxkaXYKICAgIGNsYXNzPSJiYXIiCiAgICA6c3R5bGU9ImJhclN0eWxlIgogID4NCiAgICA8ZGl2CiAgICAgIGNsYXNzPSJpbmRpY2F0b3IiCiAgICAgIDpzdHlsZT0iaW5kaWNhdG9yU3R5bGUiCiAgICAvPg0KICAgIDxkaXYKICAgICAgdi1mb3I9IihzbGljZVN0eWxlLCBpKSBpbiBzbGljZVN0eWxlcyIKICAgICAgOmtleT0iaSIKICAgICAgY2xhc3M9InNsaWNlIgogICAgICA6c3R5bGU9InNsaWNlU3R5bGUiCiAgICAvPg0KICA8L2Rpdj4NCg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Bar.vue"], "names": [], "mappings": ";EAyCE,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB;IACE,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC;IACD,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;EACH,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Bar.vue", "sourceRoot": "", "sourcesContent": ["<script>\r\nexport default {\r\n  props: {\r\n    percentage: {\r\n      type:     Number,\r\n      required: true\r\n    },\r\n    primaryColor: {\r\n      type:    String,\r\n      default: '--primary'\r\n    },\r\n    secondaryColor: {\r\n      type:    String,\r\n      default: '--border'\r\n    },\r\n    slices: {\r\n      type:    Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  computed: {\r\n    indicatorStyle() {\r\n      return {\r\n        width:           `${ this.percentage }%`,\r\n        backgroundColor: `var(${ this.primaryColor })`\r\n      };\r\n    },\r\n    barStyle() {\r\n      return { backgroundColor: `var(${ this.secondaryColor })` };\r\n    },\r\n    sliceStyles() {\r\n      return this.slices.map((slice) => ({\r\n        left:       `${ slice }%`,\r\n        visibility: slice < this.percentage ? 'visible' : 'hidden'\r\n      }));\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<template>\r\n  <div\n    class=\"bar\"\n    :style=\"barStyle\"\n  >\r\n    <div\n      class=\"indicator\"\n      :style=\"indicatorStyle\"\n    />\r\n    <div\n      v-for=\"(sliceStyle, i) in sliceStyles\"\n      :key=\"i\"\n      class=\"slice\"\n      :style=\"sliceStyle\"\n    />\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.bar {\r\n    $height: 15px;\r\n\r\n    width: 100%;\r\n    height: $height;\r\n    border-radius: math.div($height, 2);\r\n    overflow: hidden;\r\n    position: relative;\r\n\r\n    .indicator {\r\n        height: 100%;\r\n    }\r\n\r\n    .slice {\r\n      position: absolute;\r\n      top: 0;\r\n      bottom: 0;\r\n      width: 1px;\r\n      background-color: var(--body-bg);\r\n    }\r\n}\r\n</style>\r\n"]}]}