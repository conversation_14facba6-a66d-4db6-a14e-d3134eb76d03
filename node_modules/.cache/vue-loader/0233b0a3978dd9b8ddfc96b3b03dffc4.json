{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SortableTable/THead.vue?vue&type=template&id=06369a08&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SortableTable/THead.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SortableTable/THead.vue"], "names": [], "mappings": ";EA4NE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrE,CAAC,CAAC;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB;QACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,CAAC;MACH,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC;MACD,CAAC,CAAC;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACvC;QACE,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACrE;UACE,CAAC,CAAC,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACpC,CAAC,CAAC,CAAC,CAAC;cACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB;cACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB;YACE,CAAC;cACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvC,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC/C,CAAC;gBACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9C,CAAC;cACD,CAAC;gBACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7C,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB;QACE,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B;YACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B;YACE,CAAC,CAAC,CAAC;cACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/B;cACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC;YACH,CAAC,CAAC,CAAC;cACD,CAAC,CAAC;gBACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC9C;gBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxD,CAAC;cACH,CAAC,CAAC,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC;IACH,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SortableTable/THead.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { Checkbox } from '@components/Form/Checkbox';\nimport { SOME, NONE } from './selection';\nimport { AUTO, CENTER, fitOnScreen } from '@shell/utils/position';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\n\nexport default {\n  emits: ['update-cols-options', 'on-toggle-all', 'group-value-change', 'on-sort-change', 'col-visibility-change'],\n\n  components: { Checkbox, LabeledSelect },\n  props:      {\n    columns: {\n      type:     Array,\n      required: true\n    },\n    sortBy: {\n      type:     String,\n      required: true\n    },\n    defaultSortBy: {\n      type:    String,\n      default: ''\n    },\n    group: {\n      type:    String,\n      default: ''\n    },\n    groupOptions: {\n      type:    Array,\n      default: () => []\n    },\n    descending: {\n      type:     Boolean,\n      required: true\n    },\n    hasAdvancedFiltering: {\n      type:     Boolean,\n      required: false\n    },\n    tableColsOptions: {\n      type:    Array,\n      default: () => [],\n    },\n    tableActions: {\n      type:     Boolean,\n      required: true,\n    },\n    rowActions: {\n      type:     Boolean,\n      required: true,\n    },\n    howMuchSelected: {\n      type:     String,\n      required: true,\n    },\n    checkWidth: {\n      type:    Number,\n      default: 30,\n    },\n    rowActionsWidth: {\n      type:     Number,\n      required: true\n    },\n    subExpandColumn: {\n      type:    Boolean,\n      default: false,\n    },\n    expandWidth: {\n      type:    Number,\n      default: 30,\n    },\n    labelFor: {\n      type:     Function,\n      required: true,\n    },\n    noRows: {\n      type:    Boolean,\n      default: true,\n    },\n    noResults: {\n      type:    Boolean,\n      default: true,\n    },\n    loading: {\n      type:     Boolean,\n      required: false,\n    },\n  },\n\n  data() {\n    return {\n      tableColsOptionsVisibility: false,\n      tableColsMenuPosition:      null\n    };\n  },\n\n  watch: {\n    advancedFilteringValues() {\n      // passing different dummy args to make sure update is triggered\n      this.watcherUpdateLiveAndDelayed(true, false);\n    },\n    tableColsOptionsVisibility(neu) {\n      if (neu) {\n        // check if user clicked outside the table cols options box\n        window.addEventListener('click', this.onClickOutside);\n\n        // update filtering options and toggable cols every time dropdown is open\n        this.$emit('update-cols-options');\n      } else {\n        // unregister click event\n        window.removeEventListener('click', this.onClickOutside);\n      }\n    }\n  },\n  computed: {\n    isAll: {\n      get() {\n        return this.howMuchSelected !== NONE;\n      },\n\n      set(value) {\n        this.$emit('on-toggle-all', value);\n      }\n    },\n    hasAdvGrouping() {\n      return this.group?.length && this.groupOptions?.length;\n    },\n    advGroup: {\n      get() {\n        return this.group || this.advGroup;\n      },\n\n      set(val) {\n        this.$emit('group-value-change', val);\n      }\n    },\n\n    isIndeterminate() {\n      return this.howMuchSelected === SOME;\n    },\n    hasColumnWithSubLabel() {\n      return this.columns.some((col) => col.subLabel);\n    }\n  },\n\n  methods: {\n    changeSort(e, col) {\n      if ( !col.sort ) {\n        return;\n      }\n\n      let desc = false;\n\n      if ( this.sortBy === col.name ) {\n        desc = !this.descending;\n      }\n\n      this.$emit('on-sort-change', col.name, desc);\n    },\n\n    isCurrent(col) {\n      return col.name === this.sortBy;\n    },\n\n    ariaSort(col) {\n      if (this.isCurrent(col)) {\n        return this.descending ? this.t('generic.descending') : this.t('generic.ascending');\n      }\n\n      return this.t('generic.none');\n    },\n\n    tableColsOptionsClick(ev) {\n      // set menu position\n      const menu = document.querySelector('.table-options-container');\n      const elem = document.querySelector('.table-options-btn');\n\n      this.tableColsMenuPosition = fitOnScreen(menu, ev || elem, {\n        overlapX:  true,\n        fudgeX:    326,\n        fudgeY:    -22,\n        positionX: CENTER,\n        positionY: AUTO,\n      });\n\n      // toggle visibility\n      this.tableColsOptionsVisibility = !this.tableColsOptionsVisibility;\n    },\n\n    onClickOutside(event) {\n      const tableOpts = this.$refs['table-options'];\n\n      if (!tableOpts || tableOpts.contains(event.target)) {\n        return;\n      }\n      this.tableColsOptionsVisibility = false;\n    },\n\n    tableOptionsCheckbox(value, label) {\n      this.$emit('col-visibility-change', {\n        label,\n        value\n      });\n    },\n\n    tooltip(col) {\n      if (!col.tooltip) {\n        return null;\n      }\n\n      const exists = this.$store.getters['i18n/exists'];\n\n      return exists(col.tooltip) ? this.t(col.tooltip) : col.tooltip;\n    },\n  }\n\n};\n</script>\n\n<template>\n  <thead>\n    <tr :class=\"{'loading': loading, 'top-aligned': hasColumnWithSubLabel}\">\n      <th\n        v-if=\"tableActions\"\n        :width=\"checkWidth\"\n      >\n        <Checkbox\n          v-model:value=\"isAll\"\n          class=\"check\"\n          data-testid=\"sortable-table_check_select_all\"\n          :indeterminate=\"isIndeterminate\"\n          :disabled=\"noRows || noResults\"\n          :alternate-label=\"t('sortableTable.genericGroupCheckbox')\"\n        />\n      </th>\n      <th\n        v-if=\"subExpandColumn\"\n        :width=\"expandWidth\"\n      />\n      <th\n        v-for=\"(col) in columns\"\n        v-show=\"!hasAdvancedFiltering || (hasAdvancedFiltering && col.isColVisible)\"\n        :key=\"col.name\"\n        :align=\"col.align || 'left'\"\n        :width=\"col.width\"\n        :class=\"{ sortable: col.sort, [col.breakpoint]: !!col.breakpoint}\"\n        :tabindex=\"col.sort ? 0 : -1\"\n        class=\"sortable-table-head-element\"\n        :aria-sort=\"ariaSort(col)\"\n        @click.prevent=\"changeSort($event, col)\"\n        @keyup.enter=\"changeSort($event, col)\"\n        @keyup.space=\"changeSort($event, col)\"\n      >\n        <div\n          class=\"table-header-container\"\n          :class=\"{ 'not-filterable': hasAdvancedFiltering && !col.isFilter }\"\n        >\n          <div\n            v-clean-tooltip=\"tooltip(col)\"\n            class=\"content\"\n          >\n            <span v-clean-html=\"labelFor(col)\" />\n            <span\n              v-if=\"col.subLabel\"\n              class=\"text-muted\"\n            >\n              {{ col.subLabel }}\n            </span>\n          </div>\n          <div\n            v-if=\"col.sort\"\n            class=\"sort\"\n            aria-hidden=\"true\"\n          >\n            <i\n              v-show=\"hasAdvancedFiltering && !col.isFilter\"\n              v-clean-tooltip=\"t('sortableTable.tableHeader.noFilter')\"\n              class=\"icon icon-info not-filter-icon\"\n            />\n            <span class=\"icon-stack\">\n              <i class=\"icon icon-sort icon-stack-1x faded\" />\n              <i\n                v-if=\"isCurrent(col) && !descending\"\n                class=\"icon icon-sort-down icon-stack-1x\"\n                :alt=\"t('sortableTable.alt.sortingIconDesc')\"\n              />\n              <i\n                v-if=\"isCurrent(col) && descending\"\n                class=\"icon icon-sort-up icon-stack-1x\"\n                :alt=\"t('sortableTable.alt.sortingIconAsc')\"\n              />\n            </span>\n          </div>\n        </div>\n      </th>\n      <th\n        v-if=\"rowActions && hasAdvancedFiltering && tableColsOptions.length\"\n        :width=\"rowActionsWidth\"\n      >\n        <div\n          ref=\"table-options\"\n          class=\"table-options-group\"\n        >\n          <button\n            aria-haspopup=\"true\"\n            aria-expanded=\"false\"\n            type=\"button\"\n            class=\"btn btn-sm role-multi-action table-options-btn\"\n            @click=\"tableColsOptionsClick\"\n          >\n            <i class=\"icon icon-actions\" />\n          </button>\n          <div\n            v-show=\"tableColsOptionsVisibility\"\n            class=\"table-options-container\"\n            :style=\"tableColsMenuPosition\"\n          >\n            <div\n              v-if=\"hasAdvGrouping\"\n              class=\"table-options-grouping\"\n            >\n              <span class=\"table-options-col-subtitle\">{{ t('sortableTable.tableHeader.groupBy') }}:</span>\n              <LabeledSelect\n                v-model:value=\"advGroup\"\n                class=\"table-options-grouping-select\"\n                :clearable=\"true\"\n                :options=\"groupOptions\"\n                :disabled=\"false\"\n                :searchable=\"false\"\n                mode=\"edit\"\n                :multiple=\"false\"\n                :taggable=\"false\"\n              />\n            </div>\n            <p class=\"table-options-col-subtitle mb-20\">\n              {{ t('sortableTable.tableHeader.show') }}:\n            </p>\n            <ul>\n              <li\n                v-for=\"(col, index) in tableColsOptions\"\n                v-show=\"col.isTableOption\"\n                :key=\"index\"\n                :class=\"{ 'visible': !col.preventColToggle }\"\n              >\n                <Checkbox\n                  v-show=\"!col.preventColToggle\"\n                  v-model:value=\"col.isColVisible\"\n                  class=\"table-options-checkbox\"\n                  :label=\"col.label\"\n                  @update:value=\"tableOptionsCheckbox($event, col.label)\"\n                />\n              </li>\n            </ul>\n          </div>\n        </div>\n      </th>\n      <th\n        v-else-if=\"rowActions\"\n        :width=\"rowActionsWidth\"\n      />\n    </tr>\n  </thead>\n</template>\n\n  <style lang=\"scss\" scoped>\n    .table-options-group {\n\n      .table-options-btn.role-multi-action {\n        background-color: transparent;\n        border: none;\n        font-size: 18px;\n        &:hover, &:focus {\n          background-color: var(--accent-btn);\n          box-shadow: none;\n        }\n      }\n      .table-options-container {\n        width: 350px;\n        border: 1px solid var(--primary);\n        background-color: var(--body-bg);\n        padding: 20px;\n        z-index: 1;\n\n        .table-options-grouping {\n          display: flex;\n          align-items: center;\n          margin-bottom: 20px;\n\n          span {\n            white-space: nowrap;\n            margin-right: 10px;\n          }\n        }\n\n        ul {\n          list-style: none;\n          margin: 0;\n          padding: 0;\n          max-height: 200px;\n          overflow-y: auto;\n\n          li {\n            margin: 0;\n            padding: 0;\n\n            &.visible {\n              margin: 0 0 10px 0;\n            }\n          }\n        }\n      }\n    }\n\n    .sortable > SPAN {\n      cursor: pointer;\n      user-select: none;\n      white-space: nowrap;\n      &:hover,\n      &:active {\n        text-decoration: underline;\n        color: var(--body-text);\n      }\n    }\n\n    .top-aligned th {\n      vertical-align: top;\n      padding-top: 10px;\n    }\n\n    thead {\n      tr {\n        background-color: var(--sortable-table-header-bg);\n        color: var(--body-text);\n        text-align: left;\n        border-bottom: 1px solid var(--sortable-table-top-divider);\n      }\n    }\n\n    th {\n      padding: 8px 5px;\n      font-weight: normal;\n      border: 0;\n      color: var(--body-text);\n\n      &.sortable-table-head-element:focus-visible {\n        @include focus-outline;\n        outline-offset: -4px;\n      }\n\n      .table-header-container {\n        display: inline-flex;\n\n        .content {\n          display: flex;\n          flex-direction: column;\n        }\n\n        &.not-filterable {\n          margin-top: -2px;\n\n          .icon-stack {\n            margin-top: -2px;\n          }\n        }\n\n        .not-filter-icon {\n          font-size: 16px;\n          color: var(--primary);\n          vertical-align: super;\n        }\n      }\n\n      &:first-child {\n        padding-left: 10px;\n      }\n\n      &:last-child {\n        padding-right: 10px;\n      }\n\n      &:not(.sortable) > SPAN {\n        display: block;\n        margin-bottom: 2px;\n      }\n\n      & A {\n        color: var(--body-text);\n      }\n\n      // Aligns with COLUMN_BREAKPOINTS\n      @media only screen and (max-width: map-get($breakpoints, '--viewport-4')) {\n        // HIDE column on sizes below 480px\n        &.tablet, &.laptop, &.desktop {\n          display: none;\n        }\n      }\n      @media only screen and (max-width: map-get($breakpoints, '--viewport-9')) {\n        // HIDE column on sizes below 992px\n        &.laptop, &.desktop {\n          display: none;\n        }\n      }\n      @media only screen and (max-width: map-get($breakpoints, '--viewport-12')) {\n        // HIDE column on sizes below 1281px\n        &.desktop {\n          display: none;\n        }\n      }\n    }\n\n    .icon-stack {\n      width: 12px;\n    }\n\n    .icon-sort {\n      &.faded {\n        opacity: .3;\n      }\n    }\n  </style>\n  <style lang=\"scss\">\n    .table-options-checkbox .checkbox-custom {\n      min-width: 14px;\n    }\n    .table-options-checkbox .checkbox-label {\n      color: var(--body-text);\n    }\n  </style>\n"]}]}