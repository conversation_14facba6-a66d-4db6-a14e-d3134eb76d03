{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/TopLevelMenu.vue?vue&type=style&index=1&id=4367c1c6&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/TopLevelMenu.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/TopLevelMenu.vue"], "names": [], "mappings": ";EA+7BE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,EAAE,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7F;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,EAAE,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAErC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC1E,CAAC,CAAC,CAAC,EAAE;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrB;MACF;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnB,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACd;IACF;IACA,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClB;IACF;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACvC;IACA,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClB;;QAEA,CAAC,CAAC,CAAC,EAAE;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB;QACF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEnB,EAAE,EAAE,EAAE;YACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;YAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;cAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB;UACF;QACF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;UAErB,CAAC,CAAC,CAAC,EAAE;YACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B;QACF;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC;;UAEA,CAAC,CAAC,CAAC,EAAE;YACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB;QACF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAClB;;QAEA,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb;QACF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB;UACF;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACtB;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEhC,CAAC,CAAC,EAAE;YACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC;;UAEA,EAAE;YACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC;;UAEA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB;QACF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB;QACF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,EAAE,CAAC,CAAC,EAAE;YACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEhC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB;UACF;UACA,CAAC,CAAC,EAAE;YACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC;UACA,CAAC,CAAC,EAAE;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC;UACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEnB,EAAE,CAAC,CAAC,CAAC,EAAE;cACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB;UACF;QACF;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjE;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACd;QACA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;UAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;YAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB;UACF;QACF;QACA,EAAE,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B;QACF;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf;;QAEA,CAAC,CAAC,CAAC,EAAE;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrB;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;SAEf,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;SACV;;QAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;UAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;YAEZ,CAAC,CAAC,CAAC,CAAC,EAAE;cACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACd;UACF;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;YAEjB,CAAC,CAAC,CAAC,EAAE;cACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACjB;;YAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACvB;YACF;;YAEA,EAAE;cACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACV;UACF;QACF;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACb;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,EAAE;cACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACjB;UACF;QACF;QACA,CAAC,CAAC,CAAC,EAAE;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;QAEP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEzB,CAAC,CAAC,CAAC,EAAE;YACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAClB;;UAEA,CAAC,EAAE;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC;QACF;;SAEC,EAAE;YACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;UACxC;MACJ;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;YAEpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAClB;UACF;QACF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;UAEpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAClB;QACF;MACF;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACZ;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,EAAE;YACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACZ;;UAEA,CAAC,EAAE;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb;QACF;MACF;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;;QAErC,CAAC,CAAC,CAAC,EAAE;UACH,EAAE;YACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf;QACF;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACN,CAAC,EAAE;cACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACb;UACF;QACF;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACjB;QACF;MACF;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnB,EAAE,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAClB;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACrB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrB;MACF;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,EAAE,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACZ;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,EAAE,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClC;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACjB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ;;IAEA,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACvB;IACF;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/TopLevelMenu.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport BrandImage from '@shell/components/BrandImage';\nimport ClusterIconMenu from '@shell/components/ClusterIconMenu';\nimport IconOrSvg from '../IconOrSvg';\nimport { BLANK_CLUSTER } from '@shell/store/store-types.js';\nimport { mapGetters } from 'vuex';\nimport { CAPI, COUNT, MANAGEMENT } from '@shell/config/types';\nimport { MENU_MAX_CLUSTERS, PINNED_CLUSTERS } from '@shell/store/prefs';\nimport { sortBy } from '@shell/utils/sort';\nimport { ucFirst } from '@shell/utils/string';\nimport { KEY } from '@shell/utils/platform';\nimport { getVersionInfo } from '@shell/utils/version';\nimport { SETTING } from '@shell/config/settings';\nimport { getProductFromRoute } from '@shell/utils/router';\nimport { isRancherPrime } from '@shell/config/version';\nimport Pinned from '@shell/components/nav/Pinned';\nimport { TopLevelMenuHelperPagination, TopLevelMenuHelperLegacy } from '@shell/components/nav/TopLevelMenu.helper';\nimport { debounce } from 'lodash';\nimport { sameContents } from '@shell/utils/array';\n\nexport default {\n  components: {\n    BrandImage,\n    ClusterIconMenu,\n    IconOrSvg,\n    Pinned\n  },\n\n  data() {\n    const { displayVersion, fullVersion } = getVersionInfo(this.$store);\n    const hasProvCluster = this.$store.getters[`management/schemaFor`](CAPI.RANCHER_CLUSTER);\n\n    const canPagination = this.$store.getters[`management/paginationEnabled`]({\n      id:      MANAGEMENT.CLUSTER,\n      context: 'side-bar',\n    }) && this.$store.getters[`management/paginationEnabled`]({\n      id:      CAPI.RANCHER_CLUSTER,\n      context: 'side-bar',\n    });\n    const helper = canPagination ? new TopLevelMenuHelperPagination({ $store: this.$store }) : new TopLevelMenuHelperLegacy({ $store: this.$store });\n    const provClusters = !canPagination && hasProvCluster ? this.$store.getters[`management/all`](CAPI.RANCHER_CLUSTER) : [];\n    const mgmtClusters = !canPagination ? this.$store.getters[`management/all`](MANAGEMENT.CLUSTER) : [];\n\n    if (!canPagination) {\n      // Reduce the impact of the initial load, but only if we're not making a request\n      const args = {\n        pinnedIds:   this.pinnedIds,\n        searchTerm:  this.search,\n        unPinnedMax: this.maxClustersToShow\n      };\n\n      helper.update(args);\n    }\n\n    return {\n      shown:             false,\n      displayVersion,\n      fullVersion,\n      clusterFilter:     '',\n      hasProvCluster,\n      maxClustersToShow: MENU_MAX_CLUSTERS,\n      emptyCluster:      BLANK_CLUSTER,\n      routeCombo:        false,\n\n      canPagination,\n      helper,\n      debouncedHelperUpdateSlow:   debounce((...args) => this.helper.update(...args), 1000),\n      debouncedHelperUpdateMedium: debounce((...args) => this.helper.update(...args), 750),\n      debouncedHelperUpdateQuick:  debounce((...args) => this.helper.update(...args), 200),\n      provClusters,\n      mgmtClusters,\n    };\n  },\n\n  computed: {\n    ...mapGetters(['clusterId']),\n    ...mapGetters(['clusterReady', 'isRancher', 'currentCluster', 'currentProduct', 'isRancherInHarvester']),\n    ...mapGetters({ features: 'features/get' }),\n\n    pinnedIds() {\n      return this.$store.getters['prefs/get'](PINNED_CLUSTERS);\n    },\n\n    showClusterSearch() {\n      return this.allClustersCount > this.maxClustersToShow;\n    },\n\n    allClustersCount() {\n      const counts = this.$store.getters[`management/all`](COUNT)?.[0]?.counts || {};\n      const count = counts[MANAGEMENT.CLUSTER] || {};\n\n      return count?.summary.count;\n    },\n\n    // New\n    search() {\n      return (this.clusterFilter || '').toLowerCase();\n    },\n\n    // New\n    showPinClusters() {\n      return !this.clusterFilter;\n    },\n\n    // New\n    searchActive() {\n      return !!this.search;\n    },\n\n    /**\n     * Only Clusters that are pinned\n     *\n     * (see description of helper.clustersPinned for more details)\n     */\n    pinFiltered() {\n      return this.hasProvCluster ? this.helper.clustersPinned : [];\n    },\n\n    /**\n     * Used to shown unpinned clusters OR results of text search\n     *\n     * (see description of helper.clustersOthers for more details)\n     */\n    clustersFiltered() {\n      return this.hasProvCluster ? this.helper.clustersOthers : [];\n    },\n\n    pinnedClustersHeight() {\n      const pinCount = this.pinFiltered.length;\n      const height = pinCount > 2 ? (pinCount * 43) : 90;\n\n      return `min-height: ${ height }px`;\n    },\n    clusterFilterCount() {\n      return this.clusterFilter ? this.clustersFiltered.length : this.allClustersCount;\n    },\n\n    multiClusterApps() {\n      const options = this.options;\n\n      return options.filter((opt) => {\n        const filterApps = (opt.inStore === 'management' || opt.isMultiClusterApp) && opt.category !== 'configuration' && opt.category !== 'legacy';\n\n        if (this.isRancherInHarvester) {\n          return filterApps && opt.category !== 'hci';\n        } else {\n          // We expect the location of Virtualization Management to remain the same when rancher-manage-support is not enabled\n          return filterApps;\n        }\n      });\n    },\n\n    configurationApps() {\n      const options = this.options;\n\n      return options.filter((opt) => opt.category === 'configuration');\n    },\n\n    hciApps() {\n      const options = this.options;\n\n      return options.filter((opt) => this.isRancherInHarvester && opt.category === 'hci');\n    },\n\n    options() {\n      const cluster = this.clusterId || this.$store.getters['defaultClusterId'];\n\n      // TODO plugin routes\n      const entries = this.$store.getters['type-map/activeProducts']?.map((p) => {\n        // Try product-specific index first\n        const to = p.to || {\n          name:   `c-cluster-${ p.name }`,\n          params: { cluster }\n        };\n\n        const matched = this.$router.getRoutes().filter((route) => route.name === to.name);\n\n        if ( !matched.length ) {\n          to.name = 'c-cluster-product';\n          to.params.product = p.name;\n        }\n\n        return {\n          label:             this.$store.getters['i18n/withFallback'](`product.\"${ p.name }\"`, null, ucFirst(p.name)),\n          icon:              `icon-${ p.icon || 'copy' }`,\n          svg:               p.svg,\n          value:             p.name,\n          removable:         p.removable !== false,\n          inStore:           p.inStore || 'cluster',\n          weight:            p.weight || 1,\n          category:          p.category || 'none',\n          to,\n          isMultiClusterApp: p.isMultiClusterApp,\n        };\n      });\n\n      return sortBy(entries, ['weight']);\n    },\n\n    canEditSettings() {\n      return (this.$store.getters['management/schemaFor'](MANAGEMENT.SETTING)?.resourceMethods || []).includes('PUT');\n    },\n\n    hasSupport() {\n      return isRancherPrime() || this.$store.getters['management/byId'](MANAGEMENT.SETTING, SETTING.SUPPORTED )?.value === 'true';\n    },\n\n    isCurrRouteClusterExplorer() {\n      return this.$route?.name?.startsWith('c-cluster');\n    },\n\n    productFromRoute() {\n      return getProductFromRoute(this.$route);\n    },\n\n    aboutText() {\n      // If a version number (starts with 'v') then use that\n      if (this.displayVersion.startsWith('v')) {\n        // Don't show the '.0' for a minor release (e.g. 2.8.0, 2.9.0 etc)\n        return !this.displayVersion.endsWith('.0') ? this.displayVersion : this.displayVersion.substr(0, this.displayVersion.length - 2);\n      }\n\n      // Default fallback to 'About'\n      return this.t('about.title');\n    },\n\n    largeAboutText() {\n      return this.aboutText.length > 6;\n    },\n\n    appBar() {\n      let activeFound = false;\n\n      // order is important for the object keys here\n      // since we want to check last pinFiltered and clustersFiltered\n      const appBar = {\n        hciApps:           this.hciApps,\n        multiClusterApps:  this.multiClusterApps,\n        configurationApps: this.configurationApps,\n        pinFiltered:       this.pinFiltered,\n        clustersFiltered:  this.clustersFiltered,\n      };\n\n      Object.keys(appBar).forEach((menuSection) => {\n        const menuSectionItems = appBar[menuSection];\n        const isClusterCheck = menuSection === 'pinFiltered' || menuSection === 'clustersFiltered';\n\n        // need to reset active state on other menu items\n        menuSectionItems.forEach((item) => {\n          item.isMenuActive = false;\n\n          if (!activeFound && this.checkActiveRoute(item, isClusterCheck)) {\n            activeFound = true;\n            item.isMenuActive = true;\n          }\n        });\n      });\n\n      return appBar;\n    }\n  },\n\n  // See https://github.com/rancher/dashboard/issues/12831 for outstanding performance related work\n  watch: {\n    $route() {\n      this.shown = false;\n    },\n\n    // Before SSP world all of these changes were kicked off given Vue change detection to properties in a computed method.\n    // Changes could come from two scenarios\n    // 1. Changes made by the user (pin / search). Could be tens per second\n    // 2. Changes made by rancher to clusters (state, label, etc change). Could be hundreds a second\n    // They can be restricted to help the churn caused from above\n    // 1. When SSP enabled reduce http spam\n    // 2. When SSP is disabled (legacy) reduce fn churn (this was a known performance customer issue)\n\n    pinnedIds: {\n      immediate: true,\n      handler(neu, old) {\n        if (sameContents(neu, old)) {\n          return;\n        }\n\n        // Low throughput (user click). Changes should be shown quickly\n        this.updateClusters(neu, 'quick');\n      }\n    },\n\n    search() {\n      // Medium throughput. Changes should be shown quickly, unless we want to reduce http spam in SSP world\n      this.updateClusters(this.pinnedIds, this.canPagination ? 'medium' : 'quick');\n    },\n\n    provClusters: {\n      handler(neu, old) {\n        // Potentially incredibly high throughput. Changes should be at least limited (slow if state change, quick if added/removed). Shouldn't get here if SSP\n        this.updateClusters(this.pinnedIds, neu?.length === old?.length ? 'slow' : 'quick');\n      },\n      deep:      true,\n      immediate: true,\n    },\n\n    mgmtClusters: {\n      handler(neu, old) {\n        // Potentially incredibly high throughput. Changes should be at least limited (slow if state change, quick if added/removed). Shouldn't get here if SSP\n        this.updateClusters(this.pinnedIds, neu?.length === old?.length ? 'slow' : 'quick');\n      },\n      deep:      true,\n      immediate: true,\n    },\n\n  },\n\n  mounted() {\n    document.addEventListener('keyup', this.handler);\n  },\n\n  beforeUnmount() {\n    document.removeEventListener('keyup', this.handler);\n  },\n\n  methods: {\n    checkActiveRoute(obj, isClusterRoute) {\n      // for Cluster links in main nav: check if route is a cluster explorer one + check if route cluster matches cluster obj id + check if curr product matches route product\n      if (isClusterRoute) {\n        return this.isCurrRouteClusterExplorer && this.$route?.params?.cluster === obj?.id && this.productFromRoute === this.currentProduct?.name;\n      }\n\n      // for remaining main nav items, check if curr product matches route product is enough\n      return this.productFromRoute === obj?.value;\n    },\n\n    handleKeyComboClick() {\n      this.routeCombo = !this.routeCombo;\n    },\n\n    clusterMenuClick(ev, cluster) {\n      if (this.routeCombo) {\n        ev.preventDefault();\n\n        if (this.isCurrRouteClusterExplorer && this.productFromRoute === this.currentProduct?.name) {\n          const clusterRoute = {\n            name:   this.$route.name,\n            params: { ...this.$route.params },\n            query:  { ...this.$route.query }\n          };\n\n          clusterRoute.params.cluster = cluster.id;\n\n          return this.$router.push(clusterRoute);\n        }\n      }\n\n      return this.$router.push(cluster.clusterRoute);\n    },\n\n    handler(e) {\n      if (e.keyCode === KEY.ESCAPE ) {\n        this.hide();\n      }\n    },\n\n    hide() {\n      this.shown = false;\n      if (this.clustersFiltered === 0) {\n        this.clusterFilter = '';\n      }\n    },\n\n    toggle() {\n      this.shown = !this.shown;\n    },\n\n    async goToHarvesterCluster() {\n      const localCluster = this.$store.getters['management/all'](CAPI.RANCHER_CLUSTER).find((C) => C.id === 'fleet-local/local');\n\n      try {\n        await localCluster.goToHarvesterCluster();\n      } catch {\n      }\n    },\n\n    getTooltipConfig(item, showWhenClosed = false) {\n      if (!item) {\n        return;\n      }\n\n      let contentText = '';\n      let content;\n      let popperClass = '';\n\n      // this is the normal tooltip scenario where we are just passing a string\n      if (typeof item === 'string') {\n        contentText = item;\n        content = this.shown ? null : contentText;\n\n      // if key combo is pressed, then we update the tooltip as well\n      } else if (this.routeCombo &&\n        typeof item === 'object' &&\n        !Array.isArray(item) &&\n        item !== null &&\n        item.ready) {\n        contentText = this.t('nav.keyComboTooltip');\n\n        if (showWhenClosed) {\n          content = !this.shown ? contentText : null;\n        } else {\n          content = this.shown ? contentText : null;\n        }\n\n      // this is scenario where we show a tooltip when we are on the expanded menu to show full description\n      } else {\n        contentText = item.label;\n        // this adds a class to the tooltip container so that we can control the max width\n        popperClass = 'menu-description-tooltip';\n\n        if (item.description) {\n          contentText += `<br><br>${ item.description }`;\n        }\n\n        if (showWhenClosed) {\n          content = !this.shown ? contentText : null;\n        } else {\n          content = this.shown ? contentText : null;\n\n          // this adds a class to adjust tooltip position so it doesn't overlap the cluster pinning action\n          popperClass += ' description-tooltip-pos-adjustment';\n        }\n      }\n\n      return {\n        content,\n        placement:     'right',\n        popperOptions: { modifiers: { preventOverflow: { enabled: false }, hide: { enabled: false } } },\n        popperClass\n      };\n    },\n\n    updateClusters(pinnedIds, speed = 'slow' | 'medium' | 'quick') {\n      const args = {\n        pinnedIds,\n        searchTerm:  this.search,\n        unPinnedMax: this.maxClustersToShow\n      };\n\n      switch (speed) {\n      case 'slow':\n        this.debouncedHelperUpdateSlow(args);\n        break;\n      case 'medium':\n        this.debouncedHelperUpdateMedium(args);\n        break;\n      case 'quick':\n        this.debouncedHelperUpdateQuick(args);\n        break;\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <div>\n    <!-- Overlay -->\n    <div\n      v-if=\"shown\"\n      class=\"side-menu-glass\"\n      @click=\"hide()\"\n    />\n    <transition name=\"fade\">\n      <!-- Side menu -->\n      <div\n        data-testid=\"side-menu\"\n        class=\"side-menu\"\n        :class=\"{'menu-open': shown, 'menu-close':!shown}\"\n        tabindex=\"-1\"\n        role=\"navigation\"\n        :aria-label=\"t('nav.ariaLabel.topLevelMenu')\"\n      >\n        <!-- Logo and name -->\n        <div class=\"title\">\n          <div\n            data-testid=\"top-level-menu\"\n            :aria-label=\"t('nav.expandCollapseAppBar')\"\n            role=\"button\"\n            tabindex=\"0\"\n            class=\"menu\"\n            @keyup.enter=\"toggle()\"\n            @keyup.space=\"toggle()\"\n            @click=\"toggle()\"\n          >\n            <svg\n              class=\"menu-icon\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n              height=\"24\"\n              viewBox=\"0 0 24 24\"\n              width=\"24\"\n              :alt=\"t('nav.alt.mainMenuIcon')\"\n            ><path\n              d=\"M0 0h24v24H0z\"\n              fill=\"none\"\n            /><path d=\"M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z\" /></svg>\n          </div>\n          <div class=\"side-menu-logo\">\n            <BrandImage\n              data-testid=\"side-menu__brand-img\"\n              :alt=\"t('nav.alt.mainMenuRancherLogo')\"\n              file-name=\"rancher-logo.svg\"\n            />\n          </div>\n        </div>\n\n        <!-- Menu body -->\n        <div class=\"body\">\n          <div>\n            <!-- Home button -->\n            <div @click=\"hide()\">\n              <router-link\n                class=\"option cluster selector home\"\n                :to=\"{ name: 'home' }\"\n                role=\"link\"\n                :aria-label=\"t('nav.ariaLabel.homePage')\"\n              >\n                <svg\n                  v-clean-tooltip=\"getTooltipConfig(t('nav.home'))\"\n                  class=\"top-menu-icon\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  width=\"24\"\n                ><path\n                  d=\"M0 0h24v24H0z\"\n                  fill=\"none\"\n                /><path d=\"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z\" /></svg>\n                <div class=\"home-text\">\n                  {{ t('nav.home') }}\n                </div>\n              </router-link>\n            </div>\n            <!-- Search bar -->\n            <div\n              v-if=\"showClusterSearch\"\n              class=\"clusters-search\"\n            >\n              <div class=\"clusters-search-count\">\n                <span>{{ clusterFilterCount }}</span>\n                {{ t('nav.search.clusters') }}\n                <i\n                  v-if=\"clusterFilter\"\n                  class=\"icon icon-filter_alt\"\n                />\n              </div>\n\n              <div\n                class=\"search\"\n              >\n                <input\n                  ref=\"clusterFilter\"\n                  v-model=\"clusterFilter\"\n                  :placeholder=\"t('nav.search.placeholder')\"\n                  :tabindex=\"!shown ? -1 : 0\"\n                  :aria-label=\"t('nav.search.ariaLabel')\"\n                >\n                <i\n                  class=\"magnifier icon icon-search\"\n                  :class=\"{ active: clusterFilter }\"\n                />\n                <i\n                  v-if=\"clusterFilter\"\n                  class=\"icon icon-close\"\n                  @click=\"clusterFilter=''\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <!-- Harvester extras -->\n          <template v-if=\"hciApps.length\">\n            <div class=\"category\" />\n            <div>\n              <a\n                v-if=\"isRancherInHarvester\"\n                class=\"option\"\n                tabindex=\"0\"\n                @click=\"goToHarvesterCluster()\"\n              >\n                <i\n                  class=\"icon icon-dashboard app-icon\"\n                />\n                <div>\n                  {{ t('nav.harvesterDashboard') }}\n                </div>\n              </a>\n            </div>\n            <div\n              v-for=\"(a, i) in appBar.hciApps\"\n              :key=\"i\"\n              @click=\"hide()\"\n            >\n              <router-link\n                class=\"option\"\n                :to=\"a.to\"\n                :class=\"{'active-menu-link': a.isMenuActive }\"\n                role=\"link\"\n                :aria-label=\"`${t('nav.ariaLabel.harvesterCluster')} ${ a.label }`\"\n              >\n                <IconOrSvg\n                  class=\"app-icon\"\n                  :icon=\"a.icon\"\n                  :src=\"a.svg\"\n                />\n                <div>{{ a.label }}</div>\n              </router-link>\n            </div>\n          </template>\n\n          <!-- Cluster menu -->\n          <template v-if=\"!!allClustersCount\">\n            <div\n              ref=\"clusterList\"\n              class=\"clusters\"\n              :style=\"pinnedClustersHeight\"\n            >\n              <!-- Pinned Clusters -->\n              <div\n                v-if=\"showPinClusters && pinFiltered.length\"\n                class=\"clustersPinned\"\n              >\n                <div\n                  v-for=\"(c, index) in appBar.pinFiltered\"\n                  :key=\"index\"\n                  :data-testid=\"`pinned-ready-cluster-${index}`\"\n                  @click=\"hide()\"\n                >\n                  <button\n                    v-if=\"c.ready\"\n                    v-shortkey.push=\"{windows: ['alt'], mac: ['option']}\"\n                    :data-testid=\"`pinned-menu-cluster-${ c.id }`\"\n                    class=\"cluster selector option\"\n                    :class=\"{'active-menu-link': c.isMenuActive }\"\n                    :to=\"c.clusterRoute\"\n                    role=\"button\"\n                    :aria-label=\"`${t('nav.ariaLabel.cluster')} ${ c.label }`\"\n                    @click.prevent=\"clusterMenuClick($event, c)\"\n                    @shortkey=\"handleKeyComboClick\"\n                  >\n                    <ClusterIconMenu\n                      v-clean-tooltip=\"getTooltipConfig(c, true)\"\n                      :cluster=\"c\"\n                      :route-combo=\"routeCombo\"\n                      class=\"rancher-provider-icon\"\n                    />\n                    <div\n                      v-clean-tooltip=\"getTooltipConfig(c)\"\n                      class=\"cluster-name\"\n                    >\n                      <p>{{ c.label }}</p>\n                      <p\n                        v-if=\"c.description\"\n                        class=\"description\"\n                      >\n                        {{ c.description }}\n                      </p>\n                    </div>\n                    <Pinned\n                      :cluster=\"c\"\n                      :tab-order=\"shown ? 0 : -1\"\n                    />\n                  </button>\n                  <span\n                    v-else\n                    class=\"option cluster selector disabled\"\n                    :data-testid=\"`pinned-menu-cluster-disabled-${ c.id }`\"\n                  >\n                    <ClusterIconMenu\n                      v-clean-tooltip=\"getTooltipConfig(c, true)\"\n                      :cluster=\"c\"\n                      class=\"rancher-provider-icon\"\n                    />\n                    <div\n                      v-clean-tooltip=\"getTooltipConfig(c)\"\n                      class=\"cluster-name\"\n                    >\n                      <p>{{ c.label }}</p>\n                      <p\n                        v-if=\"c.description\"\n                        class=\"description\"\n                      >\n                        {{ c.description }}\n                      </p>\n                    </div>\n                    <Pinned\n                      :cluster=\"c\"\n                      :tab-order=\"shown ? 0 : -1\"\n                    />\n                  </span>\n                </div>\n                <div\n                  v-if=\"clustersFiltered.length > 0\"\n                  class=\"category-title\"\n                >\n                  <hr>\n                </div>\n              </div>\n\n              <!-- Clusters Search result -->\n              <div class=\"clustersList\">\n                <div\n                  v-for=\"(c, index) in appBar.clustersFiltered\"\n                  :key=\"index\"\n                  :data-testid=\"`top-level-menu-cluster-${index}`\"\n                  @click=\"hide()\"\n                >\n                  <button\n                    v-if=\"c.ready\"\n                    v-shortkey.push=\"{windows: ['alt'], mac: ['option']}\"\n                    :data-testid=\"`menu-cluster-${ c.id }`\"\n                    class=\"cluster selector option\"\n                    :class=\"{'active-menu-link': c.isMenuActive }\"\n                    :to=\"c.clusterRoute\"\n                    role=\"button\"\n                    :aria-label=\"`${t('nav.ariaLabel.cluster')} ${ c.label }`\"\n                    @click=\"clusterMenuClick($event, c)\"\n                    @shortkey=\"handleKeyComboClick\"\n                  >\n                    <ClusterIconMenu\n                      v-clean-tooltip=\"getTooltipConfig(c, true)\"\n                      :cluster=\"c\"\n                      :route-combo=\"routeCombo\"\n                      class=\"rancher-provider-icon\"\n                    />\n                    <div\n                      v-clean-tooltip=\"getTooltipConfig(c)\"\n                      class=\"cluster-name\"\n                    >\n                      <p>{{ c.label }}</p>\n                      <p\n                        v-if=\"c.description\"\n                        class=\"description\"\n                      >\n                        {{ c.description }}\n                      </p>\n                    </div>\n                    <Pinned\n                      :class=\"{'showPin': c.pinned}\"\n                      :tab-order=\"shown ? 0 : -1\"\n                      :cluster=\"c\"\n                    />\n                  </button>\n                  <span\n                    v-else\n                    class=\"option cluster selector disabled\"\n                    :data-testid=\"`menu-cluster-disabled-${ c.id }`\"\n                  >\n                    <ClusterIconMenu\n                      v-clean-tooltip=\"getTooltipConfig(c, true)\"\n                      :cluster=\"c\"\n                      class=\"rancher-provider-icon\"\n                    />\n                    <div\n                      v-clean-tooltip=\"getTooltipConfig(c)\"\n                      class=\"cluster-name\"\n                    >\n                      <p>{{ c.label }}</p>\n                      <p\n                        v-if=\"c.description\"\n                        class=\"description\"\n                      >\n                        {{ c.description }}\n                      </p>\n                    </div>\n                    <Pinned\n                      :class=\"{'showPin': c.pinned}\"\n                      :tab-order=\"shown ? 0 : -1\"\n                      :cluster=\"c\"\n                    />\n                  </span>\n                </div>\n              </div>\n\n              <!-- No clusters message -->\n              <div\n                v-if=\"clustersFiltered.length === 0 && searchActive\"\n                data-testid=\"top-level-menu-no-results\"\n                class=\"none-matching\"\n              >\n                {{ t('nav.search.noResults') }}\n              </div>\n            </div>\n\n            <!-- See all clusters -->\n            <router-link\n              v-if=\"allClustersCount > maxClustersToShow\"\n              class=\"clusters-all\"\n              :to=\"{name: 'c-cluster-product-resource', params: {\n                cluster: emptyCluster,\n                product: 'manager',\n                resource: 'provisioning.cattle.io.cluster'\n              } }\"\n              role=\"link\"\n              :aria-label=\"t('nav.ariaLabel.seeAll')\"\n            >\n              <span>\n                {{ shown ? t('nav.seeAllClusters') : t('nav.seeAllClustersCollapsed') }}\n                <i class=\"icon icon-chevron-right\" />\n              </span>\n            </router-link>\n          </template>\n\n          <!-- MULTI CLUSTER APPS -->\n          <div class=\"category\">\n            <template v-if=\"multiClusterApps.length\">\n              <div\n                class=\"category-title\"\n              >\n                <hr>\n                <span>\n                  {{ t('nav.categories.multiCluster') }}\n                </span>\n              </div>\n              <div\n                v-for=\"(a, i) in appBar.multiClusterApps\"\n                :key=\"i\"\n                @click=\"hide()\"\n              >\n                <router-link\n                  class=\"option\"\n                  :class=\"{'active-menu-link': a.isMenuActive }\"\n                  :to=\"a.to\"\n                  role=\"link\"\n                  :aria-label=\"`${t('nav.ariaLabel.multiClusterApps')} ${ a.label }`\"\n                >\n                  <IconOrSvg\n                    v-clean-tooltip=\"getTooltipConfig(a.label)\"\n                    class=\"app-icon\"\n                    :icon=\"a.icon\"\n                    :src=\"a.svg\"\n                  />\n                  <span class=\"option-link\">{{ a.label }}</span>\n                </router-link>\n              </div>\n            </template>\n\n            <!-- Configuration apps menu -->\n            <template v-if=\"configurationApps.length\">\n              <div\n                class=\"category-title\"\n              >\n                <hr>\n                <span>\n                  {{ t('nav.categories.configuration') }}\n                </span>\n              </div>\n              <div\n                v-for=\"(a, i) in appBar.configurationApps\"\n                :key=\"i\"\n                @click=\"hide()\"\n              >\n                <router-link\n                  class=\"option\"\n                  :class=\"{'active-menu-link': a.isMenuActive }\"\n                  :to=\"a.to\"\n                  role=\"link\"\n                  :aria-label=\"`${t('nav.ariaLabel.configurationApps')} ${ a.label }`\"\n                >\n                  <IconOrSvg\n                    v-clean-tooltip=\"getTooltipConfig(a.label)\"\n                    class=\"app-icon\"\n                    :icon=\"a.icon\"\n                    :src=\"a.svg\"\n                  />\n                  <div>{{ a.label }}</div>\n                </router-link>\n              </div>\n            </template>\n          </div>\n        </div>\n\n        <!-- Footer -->\n        <div\n          class=\"footer\"\n        >\n          <div\n            v-if=\"canEditSettings\"\n            class=\"support\"\n            @click=\"hide()\"\n          >\n            <router-link\n              :to=\"{name: 'support'}\"\n              role=\"link\"\n              :aria-label=\"t('nav.ariaLabel.support')\"\n            >\n              {{ t('nav.support', {hasSupport}) }}\n            </router-link>\n          </div>\n          <div\n            class=\"version\"\n            :class=\"{'version-small': largeAboutText}\"\n            @click=\"hide()\"\n          >\n            <router-link\n              :to=\"{ name: 'about' }\"\n              role=\"link\"\n              :aria-label=\"t('nav.ariaLabel.about')\"\n            >\n              {{ aboutText }}\n            </router-link>\n          </div>\n        </div>\n      </div>\n    </transition>\n  </div>\n</template>\n\n<style lang=\"scss\">\n  .menu-description-tooltip {\n    max-width: 200px;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n  }\n\n  .description-tooltip-pos-adjustment {\n    // needs !important so that we can\n    // offset the tooltip a bit so it doesn't\n    // overlap the pin icon and cause bad UX\n    left: 48px !important;\n  }\n\n  .localeSelector, .footer-tooltip {\n    z-index: 1000;\n  }\n\n  .localeSelector {\n    .v-popper__inner {\n      padding: 10px 0;\n    }\n\n    .v-popper__arrow-container {\n      display: none;\n    }\n\n    .v-popper:focus {\n      outline: 0;\n    }\n  }\n\n  .theme-dark .cluster-name .description {\n    color: var(--input-label) !important;\n  }\n  .theme-dark .body .option  {\n    &:hover .cluster-name .description,\n    &.router-link-active .cluster-name .description,\n    &.active-menu-link .cluster-name .description {\n      color: var(--side-menu-desc) !important;\n  }\n  }\n</style>\n\n<style lang=\"scss\" scoped>\n  $clear-search-size: 20px;\n  $icon-size: 25px;\n  $option-padding: 9px;\n  $option-padding-left: 14px;\n  $option-height: $icon-size + $option-padding + $option-padding;\n\n  .side-menu {\n    .menu {\n      position: absolute;\n      width: $app-bar-collapsed-width;\n      height: 54px;\n      top: 0;\n      grid-area: menu;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n\n      &:focus-visible {\n        outline: none;\n\n        .menu-icon {\n          @include focus-outline;\n          outline-offset: 4px;  // Ensure there is space around the menu icon for the focus indication\n        }\n      }\n\n      .menu-icon {\n        width: 25px;\n        height: 25px;\n        fill: var(--header-btn-text);\n      }\n    }\n\n    position: absolute;\n    top: 0;\n    left: 0px;\n    bottom: 0;\n    width: $app-bar-collapsed-width;\n    background-color: var(--topmenu-bg);\n    z-index: 100;\n    border-right: 1px solid var(--topmost-border);\n    display: flex;\n    flex-direction: column;\n    padding: 0;\n    overflow: hidden;\n    transition: width 250ms;\n\n    &:focus, &:focus-visible {\n      outline: 0;\n    }\n\n    .option:focus-visible {\n      outline: 0;\n    }\n\n    &.menu-open {\n      width: 300px;\n      box-shadow: 3px 1px 3px var(--shadow);\n\n      // because of accessibility, we force pin action to be visible on menu open\n      .pin {\n        display: block !important;\n\n        &:focus-visible {\n          @include focus-outline;\n          outline-offset: 4px;\n        }\n      }\n    }\n\n    .title {\n      display: flex;\n      height: 55px;\n      flex: 0 0 55px;\n      width: 100%;\n      justify-content: flex-start;\n      align-items: center;\n\n      .menu {\n        display: flex;\n        justify-content: center;\n      }\n      .menu-icon {\n        width: 25px;\n        height: 25px;\n      }\n    }\n    .home {\n      svg {\n        width: 25px;\n        height: 25px;\n        margin-left: 9px;\n      }\n    }\n    .home-text {\n      margin-left: $option-padding-left - 7;\n    }\n    .body {\n      flex: 1;\n      display: flex;\n      flex-direction: column;\n      margin: 10px 0;\n      width: 300px;\n      overflow: auto;\n\n      .option {\n        align-items: center;\n        cursor: pointer;\n        display: flex;\n        color: var(--link);\n        font-size: 14px;\n        height: $option-height;\n        white-space: nowrap;\n        background-color: transparent;\n        width: 100%;\n        border-radius: 0;\n        border: none;\n\n        .cluster-badge-logo-text {\n          color: var(--default-active-text);\n          font-weight: 500;\n        }\n\n        .pin {\n          font-size: 16px;\n          margin-left: auto;\n          display: none;\n          color: var(--body-text);\n          &.showPin {\n            display: block;\n          }\n        }\n\n        .cluster-name {\n          line-height: normal;\n\n          & > p {\n            width: 182px;\n            white-space: nowrap;\n            overflow: hidden;\n            text-overflow: ellipsis;\n            text-align: left;\n\n            &.description {\n              font-size: 12px;\n              padding-right: 8px;\n              color: var(--darker);\n            }\n          }\n        }\n\n        &:hover {\n          text-decoration: none;\n\n          .pin {\n            display: block;\n            color: var(--darker-text);\n          }\n        }\n        &.disabled {\n          background: transparent;\n          cursor: not-allowed;\n\n          .rancher-provider-icon,\n          .cluster-name p {\n            filter: grayscale(1);\n            color: var(--muted) !important;\n          }\n\n          .pin {\n            cursor: pointer;\n          }\n        }\n\n        &:focus {\n          outline: 0;\n          box-shadow: none;\n        }\n\n        > i, > img {\n          display: block;\n          font-size: $icon-size;\n          margin-right: 14px;\n          &:not(.pin){\n            width: 42px;\n          }\n        }\n\n        .rancher-provider-icon,\n        svg {\n          margin-right: 16px;\n          fill: var(--link);\n        }\n\n        .top-menu-icon {\n          outline-offset: 4px;\n        }\n\n        &.router-link-active, &.active-menu-link {\n          &:focus-visible {\n            .top-menu-icon, .app-icon {\n              @include focus-outline;\n            }\n          }\n\n          &:focus-visible .rancher-provider-icon {\n            @include focus-outline;\n            outline-offset: -4px;\n          }\n\n          background: var(--primary-hover-bg);\n          color: var(--primary-hover-text);\n\n          svg {\n            fill: var(--primary-hover-text);\n          }\n\n          i {\n            color: var(--primary-hover-text);\n          }\n\n          div .description {\n            color: var(--default);\n          }\n        }\n\n        &:focus-visible {\n          .top-menu-icon, .rancher-provider-icon, .app-icon {\n            @include focus-outline;\n          }\n        }\n\n        &:hover {\n          color: var(--primary-hover-text);\n          background: var(--primary-hover-bg);\n          > div {\n            color: var(--primary-hover-text);\n\n            .description {\n              color: var(--default);\n            }\n          }\n          svg {\n            fill: var(--primary-hover-text);\n          }\n          div {\n            color: var(--primary-hover-text);\n          }\n          &.disabled {\n            background: transparent;\n            color: var(--muted);\n\n            > .pin {\n              color:var(--default-text);\n              display: block;\n            }\n          }\n        }\n      }\n\n      .option, .option-disabled {\n        padding: $option-padding 0 $option-padding $option-padding-left;\n      }\n\n      .search {\n        position: relative;\n        > input {\n          background-color: transparent;\n          padding-right: 35px;\n          padding-left: 25px;\n          height: 32px;\n        }\n        > .magnifier {\n          position: absolute;\n          top: 12px;\n          left: 8px;\n          width: 12px;\n          height: 12px;\n          font-size: 12px;\n          opacity: 0.4;\n\n          &.active {\n            opacity: 1;\n\n            &:hover {\n              color: var(--body-text);\n            }\n          }\n        }\n        > i {\n          position: absolute;\n          font-size: 12px;\n          top: 12px;\n          right: 8px;\n          opacity: 0.7;\n          cursor: pointer;\n          &:hover {\n            color: var(--disabled-bg);\n          }\n        }\n      }\n\n      .clusters-all {\n        display: flex;\n        flex-direction: row-reverse;\n        margin-right: 16px;\n        margin-top: 10px;\n\n        &:focus-visible {\n          outline: none;\n        }\n\n        span {\n          display: flex;\n          align-items: center;\n        }\n\n        &:focus-visible span {\n          @include focus-outline;\n          outline-offset: 4px;\n        }\n      }\n\n      .clusters {\n        overflow-y: auto;\n\n         a, span {\n          margin: 0;\n         }\n\n        &-search {\n          display: flex;\n          align-items: center;\n          gap: 14px;\n          margin: 16px 0;\n          height: 42px;\n\n          .search {\n            transition: all 0.25s ease-in-out;\n            transition-delay: 2s;\n            width: 72%;\n            height: 36px;\n\n            input {\n              height: 100%;\n            }\n          }\n\n          &-count {\n            position: relative;\n            display: flex;\n            flex-direction: column;\n            width: 42px;\n            height: 42px;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            color: var(--default-active-text);\n            margin-left: $option-padding-left;\n            border-radius: 5px;\n            font-size: 10px;\n            font-weight: bold;\n\n            span {\n              font-size: 14px;\n            }\n\n            .router-link-active {\n              &:hover {\n                text-decoration: none;\n              }\n            }\n\n            i {\n              font-size: 12px;\n              position: absolute;\n              right: -3px;\n              top: 2px;\n            }\n          }\n        }\n      }\n\n      .none-matching {\n        width: 100%;\n        text-align: center;\n        padding: 8px\n      }\n\n      .clustersPinned {\n        .category {\n          &-title {\n            margin: 8px 0;\n            margin-left: 16px;\n            hr {\n              margin: 0;\n              width: 94%;\n              transition: all 0.25s ease-in-out;\n              max-width: 100%;\n            }\n          }\n        }\n        .pin {\n          display: block;\n        }\n      }\n\n      .category {\n        display: flex;\n        flex-direction: column;\n        place-content: flex-end;\n        flex: 1;\n\n        &-title {\n          display: flex;\n          flex-direction: row;\n          align-items: flex-start;\n          align-items: center;\n          margin: 15px 0;\n          margin-left: 16px;\n          font-size: 14px;\n          text-transform: uppercase;\n\n          span {\n            transition: all 0.25s ease-in-out;\n            display: flex;\n            max-height: 16px;\n          }\n\n          hr {\n            margin: 0;\n            max-width: 50px;\n            width: 0;\n            transition: all 0.25s ease-in-out;\n          }\n        }\n\n         i {\n            padding-left: $option-padding-left - 5;\n          }\n      }\n    }\n\n    &.menu-open {\n      .option {\n        &.router-link-active, &.active-menu-link {\n          &:focus-visible {\n            @include focus-outline;\n            border-radius: 0;\n            outline-offset: -4px;\n\n            .top-menu-icon, .app-icon, .rancher-provider-icon {\n              outline: none;\n              border-radius: 0;\n            }\n          }\n        }\n\n        &:focus-visible {\n          @include focus-outline;\n          outline-offset: -4px;\n\n          .top-menu-icon, .app-icon, .rancher-provider-icon {\n            outline: none;\n            border-radius: 0;\n          }\n        }\n      }\n    }\n\n    &.menu-close {\n      .side-menu-logo  {\n        opacity: 0;\n      }\n      .category {\n        &-title {\n          span {\n            opacity: 0;\n          }\n\n          hr {\n            width: 40px;\n          }\n        }\n      }\n      .clusters-all {\n        flex-direction: row;\n        margin-left: $option-padding-left + 2;\n\n        span {\n          i {\n            display: none;\n          }\n        }\n      }\n\n      .clustersPinned {\n        .category {\n          &-title {\n            hr {\n              width: 40px;\n            }\n          }\n        }\n      }\n\n      .footer {\n        margin: 20px 10px;\n        width: 50px;\n\n        .support {\n          display: none;\n        }\n\n        .version{\n          text-align: center;\n\n          &.version-small {\n            font-size: 12px;\n          }\n        }\n      }\n    }\n\n    .footer {\n      margin: 20px;\n      width: 240px;\n      display: flex;\n      flex: 0;\n      flex-direction: row;\n      > * {\n        flex: 1;\n        color: var(--link);\n\n        &:first-child {\n          text-align: left;\n        }\n        &:last-child {\n          text-align: right;\n        }\n        text-align: center;\n      }\n\n      .support a:focus-visible {\n        @include focus-outline;\n        outline-offset: 4px;\n      }\n\n      .version {\n        cursor: pointer;\n\n        a:focus-visible {\n          @include focus-outline;\n          outline-offset: 4px;\n        }\n      }\n    }\n  }\n\n  .side-menu-glass {\n    position: absolute;\n    top: 0;\n    left: 0px;\n    bottom: 0;\n    width: 100vw;\n    z-index: 99;\n    opacity: 1;\n  }\n\n  .side-menu-logo {\n    align-items: center;\n    display: flex;\n    transform: translateX($app-bar-collapsed-width);\n    opacity: 1;\n    max-width: 200px;\n    width: 100%;\n    justify-content: center;\n    transition: all 0.5s;\n    overflow: hidden;\n    & IMG {\n      object-fit: contain;\n      height: 21px;\n      max-width: 200px;\n    }\n  }\n\n  .fade-enter-active, .fade-leave-active {\n    transition: all 0.25s;\n    transition-timing-function: ease;\n  }\n\n  .fade-leave-active {\n    transition: all 0.25s;\n  }\n\n  .fade-leave-to {\n    left: -300px;\n  }\n\n  .fade-enter {\n    left: -300px;\n  }\n\n  .locale-chooser {\n    cursor: pointer;\n  }\n\n  .localeSelector {\n    :deep() .v-popper__inner {\n      padding: 50px 0;\n    }\n\n    :deep() .v-popper__arrow-container {\n      display: none;\n    }\n\n    :deep() .v-popper:focus {\n      outline: 0;\n    }\n\n    li {\n      padding: 8px 20px;\n\n      &:hover {\n        background-color: var(--primary-hover-bg);\n        color: var(--primary-hover-text);\n        text-decoration: none;\n      }\n    }\n  }\n</style>\n"]}]}