{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayListSelect.vue?vue&type=style&index=0&id=64024a2a&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayListSelect.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQo6ZGVlcCgpIC51bmxhYmVsZWQtc2VsZWN0IHsNCiAgaGVpZ2h0OiA2MXB4Ow0KICB9DQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayListSelect.vue"], "names": [], "mappings": ";AAmGA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayListSelect.vue", "sourceRoot": "", "sourcesContent": ["<script>\r\nimport ArrayList from '@shell/components/form/ArrayList';\r\nimport Select from '@shell/components/form/Select';\r\n\r\nexport default {\r\n  emits: ['update:value'],\r\n\r\n  components: { ArrayList, Select },\r\n  props:      {\r\n    value: {\r\n      type:     Array,\r\n      required: true\r\n    },\r\n    options: {\r\n      default: null,\r\n      type:    Array\r\n    },\r\n    selectProps: {\r\n      type:    Object,\r\n      default: null,\r\n    },\r\n    arrayListProps: {\r\n      type:    Object,\r\n      default: null\r\n    },\r\n    enableDefaultAddValue: {\r\n      type:    Boolean,\r\n      default: true\r\n    },\r\n    loading: {\r\n      type:    Boolean,\r\n      default: false\r\n    },\r\n    disabled: {\r\n      type:    Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  computed: {\r\n    filteredOptions() {\r\n      return this.options\r\n        .filter((option) => !this.value.includes(option.value));\r\n    },\r\n\r\n    addAllowed() {\r\n      return this.arrayListProps?.addAllowed || this.filteredOptions.length > 0;\r\n    },\r\n\r\n    defaultAddValue() {\r\n      return this.enableDefaultAddValue ? this.options[0]?.value : '';\r\n    },\r\n\r\n    getOptionLabel() {\r\n      return this.selectProps?.getOptionLabel ? (opt) => (this.selectProps?.getOptionLabel(opt) || opt) : undefined;\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    updateRow(index, value) {\r\n      this.value.splice(index, 1, value);\r\n      this.$emit('update:value', this.value);\r\n    },\r\n    calculateOptions(value) {\r\n      const valueOption = this.options.find((o) => o.value === value);\r\n\r\n      if (valueOption) {\r\n        return [valueOption, ...this.filteredOptions];\r\n      }\r\n\r\n      return this.filteredOptions;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<template>\r\n  <ArrayList\r\n    v-bind=\"arrayListProps\"\r\n    :value=\"value\"\r\n    class=\"array-list-select\"\r\n    :add-allowed=\"addAllowed || loading\"\r\n    :loading=\"loading\"\r\n    :defaultAddValue=\"defaultAddValue\"\r\n    :disabled=\"disabled\"\r\n    @update:value=\"$emit('update:value', $event)\"\r\n  >\r\n    <template v-slot:columns=\"scope\">\r\n      <Select\r\n        :value=\"scope.row.value\"\r\n        v-bind=\"selectProps\"\r\n        :options=\"calculateOptions(scope.row.value)\"\r\n        :get-option-label=\"getOptionLabel\"\r\n        @update:value=\"updateRow(scope.i, $event)\"\r\n      />\r\n    </template>\r\n  </ArrayList>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n:deep() .unlabeled-select {\r\n  height: 61px;\r\n  }\r\n</style>\r\n"]}]}