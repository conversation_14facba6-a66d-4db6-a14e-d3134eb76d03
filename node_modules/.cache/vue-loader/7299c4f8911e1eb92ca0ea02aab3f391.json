{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetIntro.vue?vue&type=style&index=0&id=d24df9bc&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetIntro.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5pbnRyby1ib3ggewogIGZsZXg6IDAgMCAxMDAlOwogIGhlaWdodDogY2FsYygxMDB2aCAtIDI0NnB4KTsgLy8gMig0OCBjb250ZW50IGhlYWRlciArIDIwIHBhZGRpbmcgKyA1NSBwYWdlaGVhZGVyKQogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwp9CgoudGl0bGUgewogIG1hcmdpbi1ib3R0b206IDE1cHg7CiAgZm9udC1zaXplOiAkZm9udC1zaXplLWgyOwp9Ci5pY29uLXJlcG9zaXRvcnkgewogIGZvbnQtc2l6ZTogOTZweDsKICBtYXJnaW4tYm90dG9tOiAzMnB4Owp9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetIntro.vue"], "names": [], "mappings": ";AA+CA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACT,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACrB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetIntro.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { FLEET } from '@shell/config/types';\nimport { NAME } from '@shell/config/product/fleet';\n\nexport default {\n\n  name: 'FleetIntro',\n\n  data() {\n    const gitRepoRoute = {\n      name:   'c-cluster-product-resource-create',\n      params: {\n        product:  NAME,\n        resource: FLEET.GIT_REPO\n      },\n    };\n\n    const gitRepoSchema = this.$store.getters['management/schemaFor'](FLEET.GIT_REPO);\n    const canCreateRepos = gitRepoSchema && gitRepoSchema.resourceMethods.includes('PUT');\n\n    return {\n      gitRepoRoute,\n      canCreateRepos\n    };\n  },\n};\n</script>\n<template>\n  <div class=\"intro-box\">\n    <i class=\"icon icon-repository\" />\n    <div class=\"title\">\n      {{ t('fleet.gitRepo.repo.noRepos') }}\n    </div>\n    <div\n      v-if=\"canCreateRepos\"\n      class=\"actions\"\n    >\n      <router-link\n        :to=\"gitRepoRoute\"\n        class=\"btn role-secondary\"\n      >\n        {{ t('fleet.gitRepo.repo.addRepo') }}\n      </router-link>\n    </div>\n  </div>\n</template>\n<style lang=\"scss\" scoped>\n.intro-box {\n  flex: 0 0 100%;\n  height: calc(100vh - 246px); // 2(48 content header + 20 padding + 55 pageheader)\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-direction: column;\n}\n\n.title {\n  margin-bottom: 15px;\n  font-size: $font-size-h2;\n}\n.icon-repository {\n  font-size: 96px;\n  margin-bottom: 32px;\n}\n</style>\n"]}]}