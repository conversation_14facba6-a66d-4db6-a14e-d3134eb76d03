{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ClusterIconMenu.vue?vue&type=style&index=1&id=3bd59edf&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ClusterIconMenu.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLnRoZW1lLWRhcmsgLmtleS1jb21iby1pY29uICB7CiAgICBjb2xvcjogdmFyKC0tYm9keS1iZyk7CiAgfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ClusterIconMenu.vue"], "names": [], "mappings": ";EAyLE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ClusterIconMenu.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { abbreviateClusterName } from '@shell/utils/cluster';\n\nexport default {\n  props: {\n    cluster: {\n      type:     Object,\n      required: true,\n    },\n    routeCombo: {\n      type:    Boolean,\n      default: false\n    },\n  },\n  computed: {\n    isEnabled() {\n      return !!this.cluster?.ready;\n    },\n    showLocalIcon() {\n      if (this.cluster.isLocal && this.cluster.removePreviewColor) {\n        return true;\n      }\n\n      return this.cluster.isLocal && !this.cluster.isHarvester && !this.cluster.badge?.iconText;\n    },\n\n    customColor() {\n      return this.cluster.iconColor || '';\n    },\n  },\n\n  methods: {\n    smallIdentifier(input) {\n      if (this.cluster.badge?.iconText) {\n        return this.cluster.badge?.iconText;\n      }\n\n      if (this.cluster.isLocal && !this.cluster.badge?.iconText) {\n        return undefined;\n      }\n\n      return abbreviateClusterName(input);\n    }\n  }\n};\n</script>\n\n<template>\n  <div\n    v-if=\"cluster\"\n    class=\"cluster-icon-menu\"\n  >\n    <div\n      class=\"cluster-badge-logo\"\n      :class=\"{ 'disabled': !isEnabled }\"\n    >\n      <span\n        v-if=\"!showLocalIcon\"\n        class=\"cluster-badge-logo-text\"\n      >\n        {{ smallIdentifier(cluster.label) }}\n      </span>\n      <span\n        class=\"custom-color-decoration\"\n        :style=\"{'background': customColor}\"\n      />\n      <svg\n        v-if=\"showLocalIcon\"\n        class=\"cluster-local-logo\"\n        version=\"1.1\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        xmlns:xlink=\"http://www.w3.org/1999/xlink\"\n        x=\"0px\"\n        y=\"0px\"\n        viewBox=\"0 0 100 100\"\n        style=\"enable-background:new 0 0 100 100;\"\n        xml:space=\"preserve\"\n      >\n        <title>{{ t('nav.ariaLabel.localClusterIcon') }}</title>\n        <g>\n          <g>\n            <path\n              class=\"rancher-icon-fill\"\n              d=\"M26.0862026,44.4953918H8.6165142c-5.5818157,0-9.3979139-4.6252708-8.4802637-10.1311035l2.858391-17.210701\n            C3.912292,11.6477556,6.1382647,7.1128125,7.8419709,7.1128125s3.1788611,4.5368752,3.1788611,10.1186218v4.4837742\n            c0,5.5817471,4.4044495,9.5409164,9.9862652,9.5409164h5.0791054V44.4953918z\"\n            />\n          </g>\n          <path\n            class=\"rancher-icon-fill\"\n            d=\"M63.0214729,92.8871841H37.0862045c-6.0751343,0-11.0000019-4.9248657-11.0000019-11V30.3864384\n          c0-6.0751324,4.9248676-11,11.0000019-11h25.9352684c6.0751305,0,11.0000038,4.9248676,11.0000038,11v51.5007477\n          C74.0214767,87.9623184,69.0966034,92.8871841,63.0214729,92.8871841z\"\n          />\n          <g>\n            <path\n              class=\"rancher-icon-fill\"\n              d=\"M73.9137955,44.4953918h17.4696884c5.5818176,0,9.3979187-4.6252708,8.4802628-10.1311035\n            l-2.8583908-17.210701c-0.9176483-5.5058317-3.1436234-10.0407753-4.8473282-10.0407753\n            s-3.1788635,4.5368752-3.1788635,10.1186218v4.4837742c0,5.5817471-4.4044418,9.5409164-9.9862595,9.5409164h-5.0791092\n            V44.4953918z\"\n            />\n          </g>\n        </g>\n      </svg>\n    </div>\n    <i\n      v-if=\"!routeCombo && cluster.pinned\"\n      class=\"icon icon-pin cluster-pin-icon\"\n      :alt=\"t('nav.ariaLabel.pinCluster', { cluster: cluster.nameDisplay })\"\n    />\n    <i\n      v-else-if=\"routeCombo\"\n      class=\"icon icon-keyboard_tab key-combo-icon\"\n      :alt=\"t('nav.ariaLabel.clusterIconKeyCombo')\"\n    />\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .rancher-icon-fill {\n    fill: var(--primary);\n  }\n\n  .cluster-icon-menu {\n    position: relative;\n    align-items: center;\n    display: flex;\n    height: 32px;\n    justify-content: center;\n    width: 42px;\n  }\n  .cluster-pin-icon {\n    position: absolute;\n    top: -6px;\n    right: -7px;\n    font-size: 14px;\n    transform: scaleX(-1);\n    color: var(--body-text);\n  }\n  .key-combo-icon {\n    position: absolute;\n    top: -7px;\n    right: -8px;\n    font-size: 14px;\n    color: var(--body-text);\n    background-color: #dddee6;\n    padding: 2px;\n    border-radius: 2px;\n  }\n\n  .cluster-local-logo {\n    width: 20px;\n  }\n\n  .cluster-badge-logo {\n    width: 42px;\n    height: 32px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    color: var(--default-active-text);\n    font-weight: bold;\n    background: var(--nav-icon-badge-bg);\n    border: 1px solid var(--border);\n    border-radius: 5px;\n    font-size: 12px;\n    text-transform: uppercase;\n\n    .custom-color-decoration {\n      height: 4px;\n      width: 100%;\n      margin: 0 auto;\n      position: absolute;\n      bottom: 0px;\n      border-radius: 0px 0px 5px 5px;\n    }\n\n    &.disabled {\n      color: var(--muted);\n    }\n  }\n</style>\n\n<style lang=\"scss\">\n  .theme-dark .key-combo-icon  {\n    color: var(--body-bg);\n  }\n</style>\n"]}]}