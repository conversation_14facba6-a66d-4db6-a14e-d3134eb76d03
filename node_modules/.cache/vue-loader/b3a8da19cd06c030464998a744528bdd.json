{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SelectOrCreateAuthSecret.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SelectOrCreateAuthSecret.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SelectOrCreateAuthSecret.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE5C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEhC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACpE,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACb,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC5C,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KAC7D,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KAC/E,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACxE;KACA,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACtF,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;KAC1H,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KACjC;KACA,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAClC,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACvH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACtE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxD,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1C,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC;MACH;IACF;;IAEA,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACrF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC1E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClF,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACvG;IACF,EAAE,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACzB;;IAEA,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1D;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;MAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAErB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;MAE5G,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;MAEhB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B;;MAEA,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC;;MAEA,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KAC9D;KACA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACrE,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;MAExB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;UACrF;UACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YACb,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC,EAAE;cACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,EAAE;cACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACd;;YAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC;MACN,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC;;MAEA,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACnC,CAAC,CAAC,CAAC,CAAC,EAAE;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC1C,EAAE,EAAE,CAAC;;QAEL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;QAE3I,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACX,CAAC;MACH,CAAC,CAAC;;MAEF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;cACpD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACb,CAAC;UACH,CAAC,CAAC;;QAEJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB;;MAEA,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QAC5B,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UAChB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;UAElB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE;YACvC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;cAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;cAExB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;gBACf,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBAChF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cAChB,CAAC,CAAC;;cAEF,CAAC,CAAC,CAAC;YACL;UACF;QACF;MACF;;MAEA,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACf,CAAC,CAAC;MACJ;MACA,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC;MACJ;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC;MACJ;;MAEA,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC;MACJ;;MAEA,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC;MACJ;;MAEA,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC;MACJ;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC;MACJ;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACZ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX;;MAEA,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACnJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnG;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAExC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC;;MAEA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxD;IACF,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC3B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE1F,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrE;IACF,EAAE,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACxB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE1D,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QAChB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC7D,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAClD,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACzB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACnB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACV,CAAC;cACH;YACF,CAAC;UACH,CAAC;QACH,CAAC,CAAC;MACJ,CAAC;;MAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC9D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC;MACJ;;MAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;;MAElE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACxB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QAChG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACzB;;MAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC;;MAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACxC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3C,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEtC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC;;UAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjC;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QAC/H,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR;;MAEA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEV,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACpD,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC;QACH,CAAC,CAAC;MACJ,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC9D,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC;QACH,CAAC,CAAC;;QAEF,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEnC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;UAChF,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEnB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/C,CAAC;;UAED,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACxG,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACrF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;UAClE;QACF;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC;;MAEF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;EACH,CAAC;AACH,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SelectOrCreateAuthSecret.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { _EDIT } from '@shell/config/query-params';\nimport { Banner } from '@components/Banner';\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport SSHKnownHosts from '@shell/components/form/SSHKnownHosts';\nimport { AUTH_TYPE, NORMAN, SECRET } from '@shell/config/types';\nimport { SECRET_TYPES } from '@shell/config/secret';\nimport { base64Encode } from '@shell/utils/crypto';\nimport { addObjects, insertAt } from '@shell/utils/array';\nimport { sortBy } from '@shell/utils/sort';\nimport {\n  FilterArgs,\n  PaginationFilterField,\n  PaginationParamFilter,\n} from '@shell/types/store/pagination.types';\n\nexport default {\n  name: 'SelectOrCreateAuthSecret',\n\n  emits: ['inputauthval', 'update:value'],\n\n  components: {\n    Banner,\n    LabeledInput,\n    LabeledSelect,\n    SSHKnownHosts,\n  },\n\n  props: {\n    mode: {\n      type:    String,\n      default: _EDIT,\n    },\n\n    preSelect: {\n      type:    Object,\n      default: null,\n    },\n\n    value: {\n      type:    [String, Object],\n      default: null,\n    },\n\n    inStore: {\n      type:    String,\n      default: 'cluster',\n    },\n\n    labelKey: {\n      type:    String,\n      default: 'selectOrCreateAuthSecret.label',\n    },\n\n    namespace: {\n      type:     String,\n      required: true,\n    },\n\n    /**\n     * Limit the selection of an existing secret to the namespace provided\n     */\n    limitToNamespace: {\n      type:    Boolean,\n      default: true,\n    },\n\n    generateName: {\n      type:    String,\n      default: 'auth-',\n    },\n\n    allowNone: {\n      type:    Boolean,\n      default: true,\n    },\n\n    allowSsh: {\n      type:    Boolean,\n      default: true,\n    },\n\n    allowBasic: {\n      type:    Boolean,\n      default: true,\n    },\n\n    allowS3: {\n      type:    Boolean,\n      default: false,\n    },\n\n    allowRke: {\n      type:    Boolean,\n      default: false,\n    },\n\n    registerBeforeHook: {\n      type:     Function,\n      required: true,\n    },\n\n    hookName: {\n      type:    String,\n      default: 'registerAuthSecret'\n    },\n\n    appendUniqueIdToHook: {\n      type:    Boolean,\n      default: false\n    },\n\n    hookPriority: {\n      type:    Number,\n      default: 99,\n    },\n\n    vertical: {\n      type:    Boolean,\n      default: false,\n    },\n\n    /**\n     * This component is used in MultiStep Process\n     * So when user click through to final step and submit the form\n     * This component get recreated therefore register `doCreate` as a hook each time\n     * Also, the parent step component is not aware that credential is created\n     *\n     * This property is implement to prevent this issue and delegate it to parent component.\n     */\n    delegateCreateToParent: {\n      type:    Boolean,\n      default: false,\n    },\n\n    /**\n     * Set to false to make a fresh http request to secrets every time. This can be used when secrets have already been used for\n     * another purpose on the same page\n     *\n     * Set to true to cache the response\n     */\n    cacheSecrets: {\n      type:    Boolean,\n      default: false,\n    },\n\n    showSshKnownHosts: {\n      type:    Boolean,\n      default: false,\n    },\n  },\n\n  async fetch() {\n    if ( (this.allowSsh || this.allowBasic || this.allowRke) && this.$store.getters[`${ this.inStore }/schemaFor`](SECRET) ) {\n      if (this.$store.getters[`${ this.inStore }/paginationEnabled`](SECRET)) {\n        // Filter results via api (because we shouldn't be fetching them all...)\n        this.filteredSecrets = await this.filterSecretsByApi();\n      } else {\n        // Cannot yet filter via api, so fetch all and filter later on\n        this.allSecrets = await this.$store.dispatch(\n          `${ this.inStore }/findAll`,\n          { type: SECRET }\n        );\n      }\n    }\n\n    if ( this.allowS3 && this.$store.getters['rancher/canList'](NORMAN.CLOUD_CREDENTIAL) ) {\n      // Avoid an async call and loading screen if already loaded by someone else\n      if (this.$store.getters['rancher/haveAll'](NORMAN.CLOUD_CREDENTIAL)) {\n        this.allCloudCreds = this.$store.getters['rancher/all'](NORMAN.CLOUD_CREDENTIAL);\n      } else {\n        this.allCloudCreds = await this.$store.dispatch('rancher/findAll', { type: NORMAN.CLOUD_CREDENTIAL });\n      }\n    } else {\n      this.allCloudCreds = [];\n    }\n\n    if ( !this.value ) {\n      this.publicKey = this.preSelect?.publicKey || '';\n      this.privateKey = this.preSelect?.privateKey || '';\n      this.sshKnownHosts = this.preSelect?.sshKnownHosts || '';\n    }\n\n    this.updateSelectedFromValue();\n    this.update();\n  },\n\n  data() {\n    return {\n      allCloudCreds: [],\n\n      allSecrets:      null,\n      filteredSecrets: null,\n\n      selected: null,\n\n      filterByNamespace: this.namespace && this.limitToNamespace,\n\n      publicKey:     '',\n      privateKey:    '',\n      sshKnownHosts: '',\n      uniqueId:      new Date().getTime(), // Allows form state to be individually tracked if the form is in a list\n\n      SSH:   AUTH_TYPE._SSH,\n      BASIC: AUTH_TYPE._BASIC,\n      S3:    AUTH_TYPE._S3,\n      RKE:   AUTH_TYPE._RKE,\n    };\n  },\n\n  computed: {\n    secretTypes() {\n      const types = [];\n\n      if ( this.allowSsh ) {\n        types.push(SECRET_TYPES.SSH);\n      }\n\n      if ( this.allowBasic ) {\n        types.push(SECRET_TYPES.BASIC);\n      }\n\n      if ( this.allowRke ) {\n        types.push(SECRET_TYPES.RKE_AUTH_CONFIG);\n      }\n\n      return types;\n    },\n\n    /**\n     * Fitler secrets given their namespace and required secret type\n     *\n     * Convert secrets to list of options and suplement with custom entries\n     */\n    options() {\n      let filteredSecrets = [];\n\n      if (this.allSecrets) {\n        // Fitler secrets given their namespace and required secret type\n        filteredSecrets = this.allSecrets\n          .filter((x) => this.filterByNamespace ? x.metadata.namespace === this.namespace : true\n          )\n          .filter((x) => {\n            // Must match one of the required types\n            if (\n              this.secretTypes.length &&\n              !this.secretTypes.includes(x._type)\n            ) {\n              return false;\n            }\n\n            return true;\n          });\n      } else if (this.filteredSecrets) {\n        filteredSecrets = this.filteredSecrets;\n      }\n\n      let out = filteredSecrets.map((x) => {\n        const {\n          dataPreview, subTypeDisplay, metadata, id\n        } = x;\n\n        const label =\n          subTypeDisplay && dataPreview ? `${ metadata.name } (${ subTypeDisplay }: ${ dataPreview })` : `${ metadata.name } (${ subTypeDisplay })`;\n\n        return {\n          label,\n          group: metadata.namespace,\n          value: id,\n        };\n      });\n\n      if (this.allowS3) {\n        const more = this.allCloudCreds\n          .filter((x) => ['aws', 's3'].includes(x.provider))\n          .map((x) => {\n            return {\n              label: `${ x.nameDisplay } (${ x.providerDisplay })`,\n              group: 'Cloud Credentials',\n              value: x.id,\n            };\n          });\n\n        addObjects(out, more);\n      }\n\n      if ( !this.limitToNamespace ) {\n        out = sortBy(out, 'group');\n        if ( out.length ) {\n          let lastGroup = '';\n\n          for ( let i = 0 ; i < out.length ; i++ ) {\n            if ( out[i].group !== lastGroup ) {\n              lastGroup = out[i].group;\n\n              insertAt(out, i, {\n                kind:     'title',\n                label:    this.t('selectOrCreateAuthSecret.namespaceGroup', { name: lastGroup }),\n                disabled: true,\n              });\n\n              i++;\n            }\n          }\n        }\n      }\n\n      if ( out.length ) {\n        out.unshift({\n          kind:     'title',\n          label:    this.t('selectOrCreateAuthSecret.chooseExisting'),\n          disabled: true\n        });\n      }\n      if ( this.allowNone ) {\n        out.unshift({\n          label: this.t('generic.none'),\n          value: AUTH_TYPE._NONE,\n        });\n      }\n\n      if (this.allowSsh || this.allowS3 || this.allowBasic || this.allowRke) {\n        out.unshift({\n          label:    'divider',\n          disabled: true,\n          kind:     'divider'\n        });\n      }\n\n      if ( this.allowSsh ) {\n        out.unshift({\n          label: this.t('selectOrCreateAuthSecret.createSsh'),\n          value: AUTH_TYPE._SSH,\n          kind:  'highlighted'\n        });\n      }\n\n      if ( this.allowS3 ) {\n        out.unshift({\n          label: this.t('selectOrCreateAuthSecret.createS3'),\n          value: AUTH_TYPE._S3,\n          kind:  'highlighted'\n        });\n      }\n\n      if ( this.allowBasic ) {\n        out.unshift({\n          label: this.t('selectOrCreateAuthSecret.createBasic'),\n          value: AUTH_TYPE._BASIC,\n          kind:  'highlighted'\n        });\n      }\n\n      // Note here about order\n      if ( this.allowRke ) {\n        out.unshift({\n          label: this.t('selectOrCreateAuthSecret.createRKE'),\n          value: AUTH_TYPE._RKE,\n          kind:  'highlighted'\n        });\n      }\n\n      return out;\n    },\n\n    firstCol() {\n      if ( this.vertical ) {\n        return '';\n      }\n\n      if ( this.selected === AUTH_TYPE._SSH || this.selected === AUTH_TYPE._BASIC || this.selected === AUTH_TYPE._RKE || this.selected === AUTH_TYPE._S3 ) {\n        return 'col span-4';\n      }\n\n      return 'col span-6';\n    },\n\n    moreCols() {\n      if ( this.vertical ) {\n        return 'mt-20';\n      }\n\n      return (this.selected === AUTH_TYPE._SSH) && this.showSshKnownHosts ? 'col span-3' : 'col span-4';\n    }\n  },\n\n  watch: {\n    selected:      'update',\n    publicKey:     'updateKeyVal',\n    privateKey:    'updateKeyVal',\n    sshKnownHosts: 'updateKeyVal',\n    value:         'updateSelectedFromValue',\n\n    async namespace(ns) {\n      if (ns && !this.selected.startsWith(`${ ns }/`)) {\n        this.selected = AUTH_TYPE._NONE;\n      }\n\n      // if ns has changed and we're filtering by api... we need to re-fetch entries\n      if (this.filteredSecrets && this.filterByNamespace) {\n        this.filteredSecrets = await this.filterSecretsByApi();\n      }\n    },\n  },\n\n  created() {\n    if (this.registerBeforeHook) {\n      const hookName = this.appendUniqueIdToHook ? this.hookName + this.uniqueId : this.hookName;\n\n      if (!this.delegateCreateToParent) {\n        this.registerBeforeHook(this.doCreate, hookName, this.hookPriority);\n      }\n    } else {\n      throw new Error('Before Hook is missing');\n    }\n  },\n\n  methods: {\n    updateSelectedFromValue() {\n      let selected = this.preSelect?.selected || AUTH_TYPE._NONE;\n\n      if ( this.value ) {\n        if ( typeof this.value === 'object' ) {\n          selected = `${ this.value.namespace }/${ this.value.name }`;\n        } else if ( this.value.includes('/') || this.value.includes(':') ) {\n          selected = this.value;\n        } else if ( this.namespace ) {\n          selected = `${ this.namespace }/${ this.value }`;\n        } else {\n          selected = this.value;\n        }\n      }\n\n      this.selected = selected;\n    },\n    async filterSecretsByApi() {\n      const findPageArgs = {\n        // Of type ActionFindPageArgs\n        namespaced: this.filterByNamespace ? this.namespace : '',\n        pagination: new FilterArgs({\n          filters: [\n            PaginationParamFilter.createMultipleFields(\n              this.secretTypes.map(\n                (t) => new PaginationFilterField({\n                  field: 'metadata.fields.1',\n                  value: t,\n                })\n              )\n            ),\n          ],\n        }),\n      };\n\n      if (this.cacheSecrets) {\n        return await this.$store.dispatch(`${ this.inStore }/findPage`, {\n          type: SECRET,\n          opt:  findPageArgs,\n        });\n      }\n\n      const url = this.$store.getters[`${ this.inStore }/urlFor`](\n        SECRET,\n        null,\n        findPageArgs\n      );\n      const res = await this.$store.dispatch(`cluster/request`, { url });\n\n      return res?.data || [];\n    },\n\n    updateKeyVal() {\n      if ( ![AUTH_TYPE._SSH, AUTH_TYPE._BASIC, AUTH_TYPE._S3, AUTH_TYPE._RKE].includes(this.selected) ) {\n        this.privateKey = '';\n        this.publicKey = '';\n        this.sshKnownHosts = '';\n      }\n\n      const value = {\n        selected:   this.selected,\n        privateKey: this.privateKey,\n        publicKey:  this.publicKey,\n      };\n\n      if (this.sshKnownHosts) {\n        value.sshKnownHosts = this.sshKnownHosts;\n      }\n\n      this.$emit('inputauthval', value);\n    },\n\n    update() {\n      if ( (!this.selected || [AUTH_TYPE._SSH, AUTH_TYPE._BASIC, AUTH_TYPE._S3, AUTH_TYPE._RKE, AUTH_TYPE._NONE].includes(this.selected))) {\n        this.$emit('update:value', null);\n      } else if ( this.selected.includes(':') ) {\n        // Cloud creds\n        this.$emit('update:value', this.selected);\n      } else {\n        const split = this.selected.split('/');\n\n        if ( this.limitToNamespace ) {\n          this.$emit('update:value', split[1]);\n        } else {\n          const out = {\n            namespace: split[0],\n            name:      split[1]\n          };\n\n          this.$emit('update:value', out);\n        }\n      }\n\n      this.updateKeyVal();\n    },\n\n    async doCreate() {\n      if ( ![AUTH_TYPE._SSH, AUTH_TYPE._BASIC, AUTH_TYPE._S3, AUTH_TYPE._RKE].includes(this.selected) || this.delegateCreateToParent ) {\n        return;\n      }\n\n      let secret;\n\n      if ( this.selected === AUTH_TYPE._S3 ) {\n        secret = await this.$store.dispatch(`rancher/create`, {\n          type:               NORMAN.CLOUD_CREDENTIAL,\n          s3credentialConfig: {\n            accessKey: this.publicKey,\n            secretKey: this.privateKey,\n          },\n        });\n      } else {\n        secret = await this.$store.dispatch(`${ this.inStore }/create`, {\n          type:     SECRET,\n          metadata: {\n            namespace:    this.namespace,\n            generateName: this.generateName\n          },\n        });\n\n        let type, publicField, privateField;\n\n        switch ( this.selected ) {\n        case AUTH_TYPE._SSH:\n          type = SECRET_TYPES.SSH;\n          publicField = 'ssh-publickey';\n          privateField = 'ssh-privatekey';\n          break;\n        case AUTH_TYPE._BASIC:\n          type = SECRET_TYPES.BASIC;\n          publicField = 'username';\n          privateField = 'password';\n          break;\n        case AUTH_TYPE._RKE:\n          type = SECRET_TYPES.RKE_AUTH_CONFIG;\n          // Set the 'auth' key to be the base64 of the username and password concatenated with a ':' character\n          secret.data = { auth: base64Encode(`${ this.publicKey }:${ this.privateKey }`) };\n          break;\n        default:\n          throw new Error('Unknown type');\n        }\n\n        secret._type = type;\n\n        // Set the data if not set by one of the specific cases above\n        if (!secret.data) {\n          secret.data = {\n            [publicField]:  base64Encode(this.publicKey),\n            [privateField]: base64Encode(this.privateKey),\n          };\n\n          // Add ssh known hosts data key - we will add a key with an empty value if the inout field was left blank\n          // This ensures on edit of the secret, we allow the user to edit the known_hosts field\n          if ((this.selected === AUTH_TYPE._SSH) && this.showSshKnownHosts) {\n            secret.data.known_hosts = base64Encode(this.sshKnownHosts || '');\n          }\n        }\n      }\n\n      await secret.save();\n\n      await this.$nextTick(() => {\n        this.selected = secret.id;\n      });\n\n      return secret;\n    },\n  },\n};\n</script>\n\n<template>\n  <div\n    class=\"select-or-create-auth-secret\"\n  >\n    <div\n      class=\"mt-20\"\n      :class=\"{'row': !vertical}\"\n    >\n      <div :class=\"firstCol\">\n        <LabeledSelect\n          v-model:value=\"selected\"\n          data-testid=\"auth-secret-select\"\n          :mode=\"mode\"\n          :label-key=\"labelKey\"\n          :loading=\"$fetchState.pending\"\n          :options=\"options\"\n          :selectable=\"option => !option.disabled\"\n        />\n      </div>\n      <template v-if=\"selected === SSH\">\n        <div :class=\"moreCols\">\n          <LabeledInput\n            v-model:value=\"publicKey\"\n            data-testid=\"auth-secret-ssh-public-key\"\n            :mode=\"mode\"\n            type=\"multiline\"\n            label-key=\"selectOrCreateAuthSecret.ssh.publicKey\"\n          />\n        </div>\n        <div :class=\"moreCols\">\n          <LabeledInput\n            v-model:value=\"privateKey\"\n            data-testid=\"auth-secret-ssh-private-key\"\n            :mode=\"mode\"\n            type=\"multiline\"\n            label-key=\"selectOrCreateAuthSecret.ssh.privateKey\"\n          />\n        </div>\n        <div\n          v-if=\"showSshKnownHosts\"\n          class=\"col span-2\"\n        >\n          <SSHKnownHosts\n            v-model:value=\"sshKnownHosts\"\n            data-testid=\"auth-secret-known-ssh-hosts\"\n            :mode=\"mode\"\n          />\n        </div>\n      </template>\n      <template v-else-if=\"selected === BASIC || selected === RKE\">\n        <Banner\n          v-if=\"selected === RKE\"\n          color=\"info\"\n          :class=\"moreCols\"\n        >\n          {{ t('selectOrCreateAuthSecret.rke.info', {}, true) }}\n        </Banner>\n        <div :class=\"moreCols\">\n          <LabeledInput\n            v-model:value=\"publicKey\"\n            data-testid=\"auth-secret-basic-username\"\n            :mode=\"mode\"\n            label-key=\"selectOrCreateAuthSecret.basic.username\"\n          />\n        </div>\n        <div :class=\"moreCols\">\n          <LabeledInput\n            v-model:value=\"privateKey\"\n            data-testid=\"auth-secret-basic-password\"\n            :mode=\"mode\"\n            type=\"password\"\n            label-key=\"selectOrCreateAuthSecret.basic.password\"\n          />\n        </div>\n      </template>\n      <template v-else-if=\"selected === S3\">\n        <div :class=\"moreCols\">\n          <LabeledInput\n            v-model:value=\"publicKey\"\n            data-testid=\"auth-secret-s3-public-key\"\n            :mode=\"mode\"\n            label-key=\"selectOrCreateAuthSecret.s3.accessKey\"\n          />\n        </div>\n        <div :class=\"moreCols\">\n          <LabeledInput\n            v-model:value=\"privateKey\"\n            data-testid=\"auth-secret-s3-private-key\"\n            :mode=\"mode\"\n            type=\"password\"\n            label-key=\"selectOrCreateAuthSecret.s3.secretKey\"\n          />\n        </div>\n      </template>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\">\n.select-or-create-auth-secret div.labeled-select {\n  min-height: $input-height;\n}\n</style>\n"]}]}