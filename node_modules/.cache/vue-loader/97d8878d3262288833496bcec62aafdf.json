{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceDetail/Masthead.vue?vue&type=template&id=c0c5e95c&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceDetail/Masthead.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceDetail/Masthead.vue"], "names": [], "mappings": ";EAyaE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB;cACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACN,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB;cACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC;cACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjK,CAAC;cACC,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC;YACD,CAAC,CAAC,CAAC,CAAC;cACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB;cACE,CAAC;gBACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChC,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACN,CAAC;cACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC;cACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACtC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C;cACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C;YACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD;cACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC;cACF,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtD;cACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnJ,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B;cACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC;;YAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC;;YAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB;cACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAER,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrE,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceDetail/Masthead.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { KUBERNETES, PROJECT } from '@shell/config/labels-annotations';\nimport { FLEET, NAMESPACE, MANAGEMENT, HELM } from '@shell/config/types';\nimport ButtonGroup from '@shell/components/ButtonGroup';\nimport { BadgeState } from '@components/BadgeState';\nimport { Banner } from '@components/Banner';\nimport { get } from '@shell/utils/object';\nimport { NAME as FLEET_NAME } from '@shell/config/product/fleet';\nimport { HIDE_SENSITIVE } from '@shell/store/prefs';\nimport {\n  AS, _DETAIL, _CONFIG, _YAML, MODE, _CREATE, _EDIT, _VIEW, _UNFLAG, _GRAPH\n} from '@shell/config/query-params';\nimport { ExtensionPoint, PanelLocation } from '@shell/core/types';\nimport ExtensionPanel from '@shell/components/ExtensionPanel';\nimport TabTitle from '@shell/components/TabTitle';\n\n// i18n-uses resourceDetail.header.*\n\n/**\n * Resource Detail Masthead component.\n *\n * ToDo: this component seem to be picking up a lot of logic from special cases, could be simplified down to parameters and then customized per use-case via wrapper component\n */\nexport default {\n\n  name: 'MastheadResourceDetail',\n\n  components: {\n    BadgeState, Banner, ButtonGroup, ExtensionPanel, TabTitle\n  },\n  props: {\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n\n    mode: {\n      type:    String,\n      default: 'create'\n    },\n\n    realMode: {\n      type:    String,\n      default: 'create'\n    },\n\n    as: {\n      type:    String,\n      default: _YAML,\n    },\n\n    hasGraph: {\n      type:    Boolean,\n      default: false\n    },\n\n    hasDetail: {\n      type:    Boolean,\n      default: false\n    },\n\n    hasEdit: {\n      type:    Boolean,\n      default: false\n    },\n\n    storeOverride: {\n      type:    String,\n      default: null,\n    },\n\n    resource: {\n      type:    String,\n      default: null,\n    },\n\n    resourceSubtype: {\n      type:    String,\n      default: null,\n    },\n\n    parentRouteOverride: {\n      type:    String,\n      default: null,\n    },\n\n    canViewYaml: {\n      type:    Boolean,\n      default: false,\n    }\n  },\n\n  data() {\n    return {\n      DETAIL_VIEW:       _DETAIL,\n      extensionType:     ExtensionPoint.PANEL,\n      extensionLocation: PanelLocation.DETAILS_MASTHEAD,\n    };\n  },\n\n  computed: {\n    dev() {\n      return this.$store.getters['prefs/dev'];\n    },\n\n    schema() {\n      const inStore = this.storeOverride || this.$store.getters['currentStore'](this.resource);\n\n      return this.$store.getters[`${ inStore }/schemaFor`]( this.resource );\n    },\n\n    isView() {\n      return this.mode === _VIEW;\n    },\n\n    isEdit() {\n      return this.mode === _EDIT;\n    },\n\n    isCreate() {\n      return this.mode === _CREATE;\n    },\n\n    isNamespace() {\n      return this.schema?.id === NAMESPACE;\n    },\n\n    isProject() {\n      return this.schema?.id === MANAGEMENT.PROJECT;\n    },\n\n    isProjectHelmChart() {\n      return this.schema?.id === HELM.PROJECTHELMCHART;\n    },\n\n    hasMultipleNamespaces() {\n      return !!this.value.namespaces;\n    },\n\n    namespace() {\n      if (this.value?.metadata?.namespace) {\n        return this.value?.metadata?.namespace;\n      }\n\n      return null;\n    },\n\n    detailsAction() {\n      return this.value?.detailsAction;\n    },\n\n    shouldHifenize() {\n      return (this.mode === 'view' || this.mode === 'edit') && this.resourceSubtype?.length && this.value?.nameDisplay?.length;\n    },\n\n    namespaceLocation() {\n      if (!this.isNamespace) {\n        return this.value.namespaceLocation || {\n          name:   'c-cluster-product-resource-id',\n          params: {\n            cluster:  this.$route.params.cluster,\n            product:  this.$store.getters['productId'],\n            resource: NAMESPACE,\n            id:       this.$route.params.namespace\n          }\n        };\n      }\n\n      return null;\n    },\n\n    isWorkspace() {\n      return this.$store.getters['productId'] === FLEET_NAME && !!this.value?.metadata?.namespace;\n    },\n\n    workspaceLocation() {\n      return {\n        name:   'c-cluster-product-resource-id',\n        params: {\n          cluster:  this.$route.params.cluster,\n          product:  this.$store.getters['productId'],\n          resource: FLEET.WORKSPACE,\n          id:       this.$route.params.namespace\n        }\n      };\n    },\n\n    project() {\n      if (this.isNamespace) {\n        const cluster = this.$store.getters['currentCluster'];\n\n        if (cluster) {\n          const id = (this.value?.metadata?.labels || {})[PROJECT];\n\n          return this.$store.getters['management/byId'](MANAGEMENT.PROJECT, `${ cluster.id }/${ id }`);\n        }\n      }\n\n      return null;\n    },\n\n    banner() {\n      if (this.value?.stateObj?.error) {\n        const defaultErrorMessage = this.t('resourceDetail.masthead.defaultBannerMessage.error', undefined, true);\n\n        return {\n          color:   'error',\n          message: this.value.stateObj.message || defaultErrorMessage\n        };\n      }\n\n      if (this.value?.spec?.paused) {\n        return {\n          color:   'info',\n          message: this.t('asyncButton.pause.description')\n        };\n      }\n\n      if (this.value?.stateObj?.transitioning) {\n        const defaultTransitioningMessage = this.t('resourceDetail.masthead.defaultBannerMessage.transitioning', undefined, true);\n\n        return {\n          color:   'info',\n          message: this.value.stateObj.message || defaultTransitioningMessage\n        };\n      }\n\n      return null;\n    },\n\n    parent() {\n      const displayName = this.value?.parentNameOverride || this.$store.getters['type-map/labelFor'](this.schema);\n      const product = this.$store.getters['currentProduct'].name;\n\n      const defaultLocation = {\n        name:   'c-cluster-product-resource',\n        params: {\n          resource: this.resource,\n          product,\n        }\n      };\n\n      const location = this.value?.parentLocationOverride || defaultLocation;\n\n      if (this.parentRouteOverride) {\n        location.name = this.parentRouteOverride;\n      }\n\n      const typeOptions = this.$store.getters[`type-map/optionsFor`]( this.resource );\n      const out = {\n        displayName, location, ...typeOptions\n      };\n\n      return out;\n    },\n\n    hideSensitiveData() {\n      return this.$store.getters['prefs/get'](HIDE_SENSITIVE);\n    },\n\n    sensitiveOptions() {\n      return [\n        {\n          tooltipKey: 'resourceDetail.masthead.sensitive.hide',\n          icon:       'icon-hide',\n          value:      true,\n        },\n        {\n          tooltipKey: 'resourceDetail.masthead.sensitive.show',\n          icon:       'icon-show',\n          value:      false\n        }\n      ];\n    },\n\n    viewOptions() {\n      const out = [];\n\n      if ( this.hasDetail ) {\n        out.push({\n          labelKey: 'resourceDetail.masthead.detail',\n          value:    _DETAIL,\n        });\n      }\n\n      if ( this.hasEdit && this.parent?.showConfigView !== false) {\n        out.push({\n          labelKey: 'resourceDetail.masthead.config',\n          value:    _CONFIG,\n        });\n      }\n\n      if ( this.hasGraph ) {\n        out.push({\n          labelKey: 'resourceDetail.masthead.graph',\n          value:    _GRAPH,\n        });\n      }\n\n      if ( this.canViewYaml ) {\n        out.push({\n          labelKey: 'resourceDetail.masthead.yaml',\n          value:    _YAML,\n        });\n      }\n\n      if ( out.length < 2 ) {\n        return null;\n      }\n\n      return out;\n    },\n\n    currentView: {\n      get() {\n        return this.as;\n      },\n\n      set(val) {\n        switch ( val ) {\n        case _DETAIL:\n          this.$router.applyQuery({\n            [MODE]: _UNFLAG,\n            [AS]:   _UNFLAG,\n          });\n          break;\n        case _CONFIG:\n          this.$router.applyQuery({\n            [MODE]: _UNFLAG,\n            [AS]:   _CONFIG,\n          });\n          break;\n        case _GRAPH:\n          this.$router.applyQuery({\n            [MODE]: _UNFLAG,\n            [AS]:   _GRAPH,\n          });\n          break;\n        case _YAML:\n          this.$router.applyQuery({\n            [MODE]: _UNFLAG,\n            [AS]:   _YAML,\n          });\n          break;\n        }\n      },\n    },\n\n    showSensitiveToggle() {\n      return !!this.value.hasSensitiveData && this.mode === _VIEW && this.as !== _YAML;\n    },\n\n    managedWarning() {\n      const { value } = this;\n      const labels = value?.metadata?.labels || {};\n\n      const managedBy = labels[KUBERNETES.MANAGED_BY] || '';\n      const appName = labels[KUBERNETES.MANAGED_NAME] || labels[KUBERNETES.INSTANCE] || '';\n\n      return {\n        show:    this.mode === _EDIT && !!managedBy,\n        type:    value?.kind || '',\n        hasName: appName ? 'yes' : 'no',\n        appName,\n        managedBy,\n      };\n    },\n\n    displayName() {\n      let displayName = this.value.nameDisplay;\n\n      if (this.isProjectHelmChart) {\n        displayName = this.value.projectDisplayName;\n      }\n\n      return this.shouldHifenize ? ` - ${ displayName }` : displayName;\n    },\n\n    location() {\n      const { parent } = this;\n\n      return parent?.location;\n    },\n\n    hideNamespaceLocation() {\n      return this.$store.getters['currentProduct'].hideNamespaceLocation || this.value.namespaceLocation === null;\n    },\n\n    resourceExternalLink() {\n      return this.value.resourceExternalLink;\n    },\n  },\n\n  methods: {\n    get,\n\n    showActions() {\n      this.$store.commit('action-menu/show', {\n        resources: this.value,\n        elem:      this.$refs.actions,\n      });\n    },\n\n    toggleSensitiveData(e) {\n      this.$store.dispatch('prefs/set', { key: HIDE_SENSITIVE, value: !!e });\n    },\n\n    invokeDetailsAction() {\n      const action = this.detailsAction;\n\n      if (action) {\n        const fn = this.value[action.action];\n\n        if (fn) {\n          fn.apply(this.value, []);\n        }\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <div class=\"masthead\">\n    <header>\n      <div class=\"title\">\n        <div class=\"primaryheader\">\n          <h1>\n            <TabTitle\n              v-if=\"isCreate\"\n              :showChild=\"false\"\n            >\n              {{ parent.displayName }}\n            </TabTitle>\n            <TabTitle\n              v-else\n              :showChild=\"false\"\n            >\n              {{ displayName }}\n            </TabTitle>\n            <router-link\n              v-if=\"location\"\n              :to=\"location\"\n              role=\"link\"\n              class=\"masthead-resource-list-link\"\n              :aria-label=\"parent.displayName\"\n            >\n              {{ parent.displayName }}:\n            </router-link>\n            <span v-else>{{ parent.displayName }}:</span>\n            <span v-if=\"value?.detailPageHeaderActionOverride && value?.detailPageHeaderActionOverride(realMode)\">{{ value?.detailPageHeaderActionOverride(realMode) }}</span>\n            <t\n              v-else\n              class=\"masthead-resource-title\"\n              :k=\"'resourceDetail.header.' + realMode\"\n              :subtype=\"resourceSubtype\"\n              :name=\"displayName\"\n              :escapehtml=\"false\"\n            />\n            <BadgeState\n              v-if=\"!isCreate && parent.showState\"\n              class=\"masthead-state\"\n              :value=\"value\"\n            />\n            <span\n              v-if=\"!isCreate && value.injectionEnabled\"\n              class=\"masthead-istio\"\n            >\n              <i\n                v-clean-tooltip=\"t('projectNamespaces.isIstioInjectionEnabled')\"\n                class=\"icon icon-sm icon-istio\"\n              />\n            </span>\n            <a\n              v-if=\"dev && !!resourceExternalLink\"\n              v-clean-tooltip=\"t(resourceExternalLink.tipsKey || 'generic.resourceExternalLinkTips')\"\n              class=\"resource-external\"\n              rel=\"nofollow noopener noreferrer\"\n              target=\"_blank\"\n              :href=\"resourceExternalLink.url\"\n            >\n              <i class=\"icon icon-external-link\" />\n            </a>\n          </h1>\n        </div>\n        <div\n          v-if=\"!isCreate\"\n          class=\"subheader\"\n        >\n          <span v-if=\"isNamespace && project\">{{ t(\"resourceDetail.masthead.project\") }}: <router-link :to=\"project.detailLocation\">{{ project.nameDisplay }}</router-link></span>\n          <span v-else-if=\"isWorkspace\">{{ t(\"resourceDetail.masthead.workspace\") }}: <router-link :to=\"workspaceLocation\">{{ namespace }}</router-link></span>\n          <span v-else-if=\"namespace && !hasMultipleNamespaces\">\n            {{ t(\"resourceDetail.masthead.namespace\") }}:\n            <router-link\n              v-if=\"!hideNamespaceLocation\"\n              :to=\"namespaceLocation\"\n              data-testid=\"masthead-subheader-namespace\"\n            >\n              {{ namespace }}\n            </router-link>\n            <span v-else>\n              {{ namespace }}\n            </span>\n          </span>\n          <span v-if=\"parent.showAge\">\n            {{ t(\"resourceDetail.masthead.age\") }}:\n            <LiveDate\n              class=\"live-date\"\n              :value=\"value.creationTimestamp\"\n            />\n          </span>\n          <span\n            v-if=\"value.showCreatedBy\"\n            data-testid=\"masthead-subheader-createdBy\"\n          >\n            {{ t(\"resourceDetail.masthead.createdBy\") }}:\n            <router-link\n              v-if=\"value.createdBy.location\"\n              :to=\"value.createdBy.location\"\n              data-testid=\"masthead-subheader-createdBy-link\"\n            >\n              {{ value.createdBy.displayName }}\n            </router-link>\n            <span\n              v-else\n              data-testid=\"masthead-subheader-createdBy_plain-text\"\n            >\n              {{ value.createdBy.displayName }}\n            </span>\n          </span>\n          <span v-if=\"value.showPodRestarts\">{{ t(\"resourceDetail.masthead.restartCount\") }}:<span class=\"live-data\"> {{ value.restartCount }}</span></span>\n        </div>\n      </div>\n      <slot name=\"right\">\n        <div class=\"actions-container align-start\">\n          <div class=\"actions\">\n            <button\n              v-if=\"detailsAction && currentView === DETAIL_VIEW && isView\"\n              type=\"button\"\n              class=\"btn role-primary actions mr-10\"\n              :disabled=\"!detailsAction.enabled\"\n              @click=\"invokeDetailsAction\"\n            >\n              {{ detailsAction.label }}\n            </button>\n            <ButtonGroup\n              v-if=\"showSensitiveToggle\"\n              :value=\"!!hideSensitiveData\"\n              icon-size=\"lg\"\n              :options=\"sensitiveOptions\"\n              class=\"mr-10\"\n              @update:value=\"toggleSensitiveData\"\n            />\n\n            <ButtonGroup\n              v-if=\"viewOptions && isView\"\n              v-model:value=\"currentView\"\n              :options=\"viewOptions\"\n              class=\"mr-10\"\n            />\n\n            <button\n              v-if=\"isView\"\n              ref=\"actions\"\n              data-testid=\"masthead-action-menu\"\n              aria-haspopup=\"true\"\n              type=\"button\"\n              class=\"btn role-multi-action actions\"\n              @click=\"showActions\"\n            >\n              <i class=\"icon icon-actions\" />\n            </button>\n          </div>\n        </div>\n      </slot>\n    </header>\n\n    <!-- Extension area -->\n    <ExtensionPanel\n      :resource=\"value\"\n      :type=\"extensionType\"\n      :location=\"extensionLocation\"\n    />\n\n    <Banner\n      v-if=\"banner && isView && !parent.hideBanner\"\n      class=\"state-banner mb-10\"\n      :color=\"banner.color\"\n      :label=\"banner.message\"\n    />\n    <Banner\n      v-if=\"managedWarning.show\"\n      color=\"warning\"\n      class=\"mb-20\"\n      :label=\"t('resourceDetail.masthead.managedWarning', managedWarning)\"\n    />\n\n    <slot />\n  </div>\n</template>\n\n<style lang='scss' scoped>\n  .masthead {\n    padding-bottom: 10px;\n    border-bottom: 1px solid var(--border);\n    margin-bottom: 10px;\n  }\n\n  HEADER {\n    margin: 0;\n    grid-template-columns: minmax(0, 1fr) auto;\n  }\n\n  .primaryheader {\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n\n    h1 {\n      margin: 0 0 0 -5px;\n      overflow-x: hidden;\n      display: flex;\n      flex-direction: row;\n      align-items: center;\n\n      .masthead-resource-title {\n        text-overflow: ellipsis;\n        overflow: hidden;\n        white-space: nowrap;\n      }\n\n      .masthead-resource-list-link {\n        margin: 5px;\n      }\n    }\n  }\n\n  .subheader{\n    display: flex;\n    flex-direction: row;\n    color: var(--input-label);\n    & > * {\n      margin: 5px 20px 5px 0px;\n    }\n\n    .live-data {\n      color: var(--body-text);\n      margin-left: 3px;\n    }\n  }\n\n  .state-banner {\n    margin: 3px 0 0 0;\n  }\n\n  .masthead-state {\n    margin-left: 8px;\n    font-size: initial;\n  }\n\n  .masthead-istio {\n    .icon {\n      vertical-align: middle;\n      color: var(--primary);\n    }\n  }\n\n  .left-right-split {\n    display: grid;\n    align-items: center;\n\n    .left-half {\n      grid-column: 1;\n    }\n\n    .right-half {\n      grid-column: 2;\n    }\n  }\n\n  div.actions-container > div.actions {\n    display: flex;\n    flex-direction: row;\n    justify-content: flex-end;\n  }\n\n  .resource-external {\n    font-size: 18px;\n  }\n</style>\n"]}]}