{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SimpleSecretSelector.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SimpleSecretSelector.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SimpleSecretSelector.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC;CACD,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;CACpD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACpD;CACA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;GAE3B,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACpD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;GAE9B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;GAEtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;GACrG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;CAChD,CAAC;AACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE3E,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;EAEpD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;IAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEzG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEhF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB;MACF,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC5B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;UAEpI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB;MACF;IACF,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAErF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAClD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX,CAAC,CAAC,CAAC;IACL,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5E;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACzE,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;UAClB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACZ;;UAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAC5C,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACZ,CAAC,EAAE,CAAC,CAAC,CAAC;;MAER,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC;IACH,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KACnD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC9C,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACxB,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEjC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;MAEpH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC/F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACrG,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAC3D,CAAC;IACH,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACzB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACf;MACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC;IACF;EACF;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SimpleSecretSelector.vue", "sourceRoot": "", "sourcesContent": ["<script>\n/**\n * I created this component because the regular secret\n * selector assumes secrets need to be in this format:\n *\n *  valueFrom:\n      secretKeyRef:\n        name: example-secret-name\n        key: example-secret-key\n\n   But for secrets for receivers in AlertmanagerConfigs,\n   it needed to be in this format:\n\n   name: example-secret-name\n   key: example-secret-key\n\n   FIXME: The solution to above would have been to have a configurable path to set/get name and key from.\n   This would have avoided a lot of copy and paste\n */\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport ResourceLabeledSelect from '@shell/components/form/ResourceLabeledSelect';\nimport { SECRET } from '@shell/config/types';\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\nimport { TYPES } from '@shell/models/secret';\nimport { LABEL_SELECT_KINDS } from '@shell/types/components/labeledSelect';\nimport { PaginationParamFilter } from '@shell/types/store/pagination.types';\n\nconst NONE = '__[[NONE]]__';\n\nexport default {\n  emits: ['updateSecretName', 'updateSecretKey'],\n\n  components: { LabeledSelect, ResourceLabeledSelect },\n\n  props: {\n    test:        { type: String, default: '' },\n    initialName: {\n      type:     String,\n      required: true\n    },\n    initialKey: {\n      type:     String,\n      required: true\n    },\n    namespace: {\n      type:     String,\n      required: true\n    },\n    types: {\n      type:    Array,\n      default: () => Object.values(TYPES)\n    },\n    disabled: {\n      type:    Boolean,\n      default: false\n    },\n    secretNameLabel: {\n      type:    String,\n      default: 'Secret Name'\n    },\n    keyNameLabel: {\n      type:    String,\n      default: 'Key'\n    },\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n  },\n\n  data(props) {\n    return {\n      secrets:            [],\n      name:               props.initialName,\n      key:                props.initialKey,\n      none:               NONE,\n      SECRET,\n      allSecretsSettings: {\n        mapResult: (secrets) => {\n          const allSecretsInNamespace = secrets.filter((secret) => this.types.includes(secret._type) && secret.namespace === this.namespace);\n          const mappedSecrets = this.mapSecrets(allSecretsInNamespace.sort((a, b) => a.name.localeCompare(b.name)));\n\n          this.secrets = allSecretsInNamespace; // We need the key from the selected secret\n\n          return mappedSecrets;\n        }\n      },\n      paginateSecretsSetting: {\n        requestSettings: this.paginatePageOptions,\n        mapResult:       (secrets) => {\n          const mappedSecrets = this.mapSecrets(secrets);\n\n          this.secrets = secrets; // We need the key from the selected secret. When paginating we won't touch the store, so just pass back here\n\n          return mappedSecrets;\n        }\n      }\n    };\n  },\n\n  computed: {\n    keys() {\n      const secret = (this.secrets || []).find((secret) => secret.name === this.name) || {};\n\n      return Object.keys(secret.data || {}).map((key) => ({\n        label: key,\n        value: key\n      }));\n    },\n    isView() {\n      return this.mode === _VIEW;\n    },\n    isKeyDisabled() {\n      return !this.isView && (!this.name || this.name === NONE || this.disabled);\n    }\n  },\n\n  methods: {\n    /**\n     * Provide a set of options for the LabelSelect ([none, ...{label, value}])\n     */\n    mapSecrets(secrets) {\n      const mappedSecrets = secrets\n        .reduce((res, s) => {\n          if (s.kind === LABEL_SELECT_KINDS.NONE) {\n            return res;\n          }\n\n          if (s.id) {\n            res.push({ label: s.name, value: s.name });\n          } else {\n            res.push(s);\n          }\n\n          return res;\n        }, []);\n\n      return [\n        {\n          label: 'None', value: NONE, kind: LABEL_SELECT_KINDS.NONE\n        },\n        ...mappedSecrets\n      ];\n    },\n\n    /**\n     * @param [LabelSelectPaginationFunctionOptions] opts\n     * @returns LabelSelectPaginationFunctionOptions\n     */\n    paginatePageOptions(opts) {\n      const { opts: { filter } } = opts;\n\n      const filters = !!filter ? [PaginationParamFilter.createSingleField({ field: 'metadata.name', value: filter })] : [];\n\n      filters.push(\n        PaginationParamFilter.createSingleField({ field: 'metadata.namespace', value: this.namespace }),\n        PaginationParamFilter.createSingleField({ field: 'metadata.fields.1', value: this.types.join(',') })\n      );\n\n      return {\n        ...opts,\n        filters,\n        groupByNamespace: false,\n        classify:         true,\n        sort:             [{ asc: true, field: 'metadata.name' }],\n      };\n    },\n\n    updateSecretName(e) {\n      if (e.value === this.none) {\n        // The key should appear blank if the secret name is cleared\n        this.key = '';\n      }\n      if (e.value) {\n        this.$emit('updateSecretName', e.value);\n      }\n    },\n    updateSecretKey(e) {\n      if (e.value) {\n        this.$emit('updateSecretKey', e.value);\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <div class=\"secret-selector show-key-selector\">\n    <div class=\"input-container\">\n      <ResourceLabeledSelect\n        v-model:value=\"name\"\n        class=\"col span-6\"\n        :disabled=\"!isView && disabled\"\n        :loading=\"$fetchState.pending\"\n        :label=\"secretNameLabel\"\n        :mode=\"mode\"\n        :resource-type=\"SECRET\"\n        :paginated-resource-settings=\"paginateSecretsSetting\"\n        :all-resources-settings=\"allSecretsSettings\"\n        @selecting=\"updateSecretName\"\n      />\n      <LabeledSelect\n        v-model:value=\"key\"\n        class=\"col span-6\"\n        :disabled=\"isKeyDisabled\"\n        :options=\"keys\"\n        :label=\"keyNameLabel\"\n        :mode=\"mode\"\n        @selecting=\"updateSecretKey\"\n      />\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\">\n.secret-selector {\n  width: 100%;\n  label {\n    display: block;\n  }\n\n  & .labeled-select {\n    min-height: $input-height;\n  }\n\n  & .vs__selected-options {\n    padding: 8px 0 7px 0;\n  }\n\n  & label {\n    display: inline-block;\n  }\n\n  &.show-key-selector {\n    .input-container > * {\n      display: inline-block;\n      width: 50%;\n\n      &.labeled-select.focused {\n        z-index: 10;\n      }\n\n      &:first-child {\n        border-top-right-radius: 0;\n        border-bottom-right-radius: 0;\n        margin-right: 0;\n      }\n\n      &:last-child {\n        border-top-left-radius: 0;\n        border-bottom-left-radius: 0;\n        border-left: none;\n        float: right;\n      }\n    }\n  }\n}\n</style>\n"]}]}