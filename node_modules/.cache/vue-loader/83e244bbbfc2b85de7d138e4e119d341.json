{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetResources.vue?vue&type=template&id=63fc975b", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetResources.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPFNvcnRhYmxlVGFibGUKICAgIDpyb3dzPSJjb21wdXRlZFJlc291cmNlcyIKICAgIDpoZWFkZXJzPSJyZXNvdXJjZUhlYWRlcnMiCiAgICA6dGFibGUtYWN0aW9ucz0iZmFsc2UiCiAgICA6cm93LWFjdGlvbnM9ImZhbHNlIgogICAga2V5LWZpZWxkPSJ0YWJsZUtleSIKICAgIGRlZmF1bHQtc29ydC1ieT0ic3RhdGUiCiAgICA6cGFnZWQ9InRydWUiCiAgLz4K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetResources.vue"], "names": [], "mappings": ";EAyEE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetResources.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport SortableTable from '@shell/components/SortableTable';\n\nexport default {\n  name: 'FleetResources',\n\n  components: { SortableTable },\n\n  props: {\n    value: {\n      type:     Object,\n      required: true,\n    },\n    clusterId: {\n      type:     String,\n      required: false,\n      default:  null,\n    },\n  },\n\n  computed: {\n    computedResources() {\n      return this.value.resourcesStatuses;\n    },\n\n    resourceHeaders() {\n      return [\n        {\n          name:      'state',\n          value:     'state',\n          label:     'State',\n          sort:      'stateSort',\n          formatter: 'BadgeStateFormatter',\n          width:     100,\n        },\n        {\n          name:  'cluster',\n          value: 'clusterName',\n          sort:  ['clusterName', 'stateSort'],\n          label: 'Cluster',\n        },\n        {\n          name:  'apiVersion',\n          value: 'apiVersion',\n          sort:  'apiVersion',\n          label: 'API Version',\n        },\n        {\n          name:  'kind',\n          value: 'kind',\n          sort:  'kind',\n          label: 'Kind',\n        },\n        {\n          name:      'name',\n          value:     'name',\n          sort:      'name',\n          label:     'Name',\n          formatter: 'LinkDetail',\n        },\n        {\n          name:  'namespace',\n          value: 'namespace',\n          sort:  'namespace',\n          label: 'Namespace',\n        },\n      ];\n    },\n  }\n};\n</script>\n\n<template>\n  <SortableTable\n    :rows=\"computedResources\"\n    :headers=\"resourceHeaders\"\n    :table-actions=\"false\"\n    :row-actions=\"false\"\n    key-field=\"tableKey\"\n    default-sort-by=\"state\"\n    :paged=\"true\"\n  />\n</template>\n"]}]}