{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/EmberPage.vue?vue&type=style&index=1&id=02d8f5ba&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/EmberPage.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgJGJhbm5lci1oZWlnaHQ6IDJlbTsKCiAgLmVtYmVyLWlmcmFtZSB7CiAgICBib3JkZXI6IDA7CiAgICBsZWZ0OiBjYWxjKHZhcigtLW5hdi13aWR0aCkgKyAkYXBwLWJhci1jb2xsYXBzZWQtd2lkdGgpOwogICAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gdmFyKC0taGVhZGVyLWhlaWdodCkpOwogICAgcG9zaXRpb246IGFic29sdXRlOwogICAgdG9wOiB2YXIoLS1oZWFkZXItaGVpZ2h0KTsKICAgIHdpZHRoOiBjYWxjKDEwMHZ3IC0gdmFyKC0tbmF2LXdpZHRoKSAtICRhcHAtYmFyLWNvbGxhcHNlZC13aWR0aCk7CiAgICB2aXNpYmlsaXR5OiBzaG93OwogIH0KCiAgLmVtYmVyLWlmcmFtZS10b3AtYmFubmVyIHsKICAgIHRvcDogY2FsYygjeyRiYW5uZXItaGVpZ2h0fSArIHZhcigtLWhlYWRlci1oZWlnaHQpKTsKICB9CgogIC5lbWJlci1pZnJhbWUtb25lLWJhbm5lciB7CiAgICBoZWlnaHQ6IGNhbGMoMTAwdmggLSB2YXIoLS1oZWFkZXItaGVpZ2h0KSAtICN7JGJhbm5lci1oZWlnaHR9KTsKICB9CgogIC5lbWJlci1pZnJhbWUtdHdvLWJhbm5lcnMgewogICAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gdmFyKC0taGVhZGVyLWhlaWdodCkgLSAjeyRiYW5uZXItaGVpZ2h0fSAtICN7JGJhbm5lci1oZWlnaHR9KTsKICB9CgogIC5lbWJlci1pZnJhbWUtaW5saW5lIHsKICAgIGJvcmRlcjogMDsKICAgIG92ZXJmbG93OiBoaWRkZW47CiAgfQoKICAuZW1iZXItaWZyYW1lLWhpZGRlbiB7CiAgICB2aXNpYmlsaXR5OiBoaWRkZW47CiAgfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/EmberPage.vue"], "names": [], "mappings": ";EA6kBE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrD;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChE;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/EmberPage.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport Loading from '@shell/components/Loading';\nimport { mapGetters, mapState } from 'vuex';\nimport { NAME as MANAGER } from '@shell/config/product/manager';\nimport { CAPI, MANAGEMENT } from '@shell/config/types';\nimport { SETTING } from '@shell/config/settings';\nimport { findEmberPage, clearEmberInactiveTimer, startEmberInactiveTimer, EMBER_FRAME } from '@shell/utils/ember-page';\n\nconst EMBER_FRAME_HIDE_CLASS = 'ember-iframe-hidden';\nconst PAGE_CHECK_TIMEOUT = 30000;\nconst WINDOW_MANAGER = 'windowmanager';\n\n// Pages that we should intercept when loaded in the IFRAME and instead\n// navigate to a page in Cluster Dashboard\n// example if the Ember clusters page that is navigated to when the user presses cancel on some pages\n// we intercept this and go the the vue Clusters page instead\nconst INTERCEPTS = {\n  'global-admin.clusters.index': {\n    name:   'c-cluster-product-resource',\n    params: {\n      cluster:  '',\n      product:  MANAGER,\n      resource: CAPI.RANCHER_CLUSTER,\n    }\n  },\n  'authenticated.cluster.index': {\n    name:   'c-cluster-product-resource',\n    params: {\n      cluster:  '',\n      product:  MANAGER,\n      resource: CAPI.RANCHER_CLUSTER,\n    }\n  },\n  'authenticated.cluster.istio.cluster-setting': { name: 'c-cluster-explorer-tools' },\n};\n\nexport default {\n  emits: ['before-nav'],\n\n  components: { Loading },\n\n  props: {\n    src: {\n      type:     String,\n      required: true\n    },\n    pop: {\n      type:    Boolean,\n      default: false\n    },\n    inline: {\n      type:    String,\n      default: ''\n    },\n    // force inline pages to reuse cached Ember pages\n    forceInlineReuse: {\n      type:    Boolean,\n      default: false\n    },\n    // Do not reuse cached pages\n    forceNew: {\n      type:    Boolean,\n      default: false\n    }\n  },\n\n  data() {\n    return {\n      iframeEl:         null,\n      loaded:           true,\n      loadRequired:     false,\n      emberCheck:       null,\n      error:            false,\n      heightSync:       null,\n      frameHeight:      -1,\n      frameWidth:       -1,\n      wmHeight:         -1,\n      showHeaderBanner: false,\n      showFooterBanner: false,\n    };\n  },\n\n  computed: {\n    ...mapGetters({ theme: 'prefs/theme' }),\n    ...mapGetters(['clusterId', 'productId']),\n    ...mapState('wm', ['open']),\n    locale() {\n      return this.$store.getters['i18n/current']();\n    }\n  },\n\n  watch: {\n    theme(theme) {\n      this.notifyTheme(theme);\n    },\n\n    // Update when source property changes\n    src(nue, old) {\n      if (nue !== old) {\n        this.initFrame();\n      }\n    },\n\n    // Watch on the window manager opening/closing\n    open(nue, old) {\n      if (nue !== old) {\n        if (nue) {\n          this.syncSize();\n        } else {\n          clearTimeout(this.heightSync);\n          const iframeEl = findEmberPage();\n\n          // Reset the height when the window manager is closed\n          this.heightSync = null;\n          this.wmHeight = -1;\n\n          if (iframeEl) {\n            iframeEl.style.height = '';\n          }\n        }\n      }\n    },\n    locale() {\n      this.syncLocale();\n    }\n  },\n\n  mounted() {\n    // Embedded page visited, so cancel time to remove IFRAME when inactive\n    clearEmberInactiveTimer();\n    window.addEventListener('message', this.receiveMessage);\n    this.initFrame();\n  },\n\n  beforeUnmount() {\n    window.removeEventListener('message', this.receiveMessage);\n\n    if (this.heightSync) {\n      clearTimeout(this.heightSync);\n    }\n\n    if (this.inline) {\n      const iframeEl = findEmberPage();\n\n      // Remove the IFRAME - we can't reuse it one its been moved inline\n      if (iframeEl) {\n        iframeEl.remove();\n      }\n    }\n\n    // Hide the iframe\n    if (this.iframeEl) {\n      this.iframeEl.classList.add(EMBER_FRAME_HIDE_CLASS);\n    }\n\n    // Cancel any pending http request to check Ember UI availability\n    if (this.emberCheck) {\n      this.emberCheck.cancel('User left page');\n    }\n\n    // Set up a timer to remove the IFrame after a period of inactivity\n    startEmberInactiveTimer();\n  },\n\n  methods: {\n    addBannerClasses(elm, prefix) {\n      if (!elm) {\n        return;\n      }\n\n      elm.classList.remove(`${ prefix }-top-banner`);\n      elm.classList.remove(`${ prefix }-one-banner`);\n      elm.classList.remove(`${ prefix }-two-banners`);\n\n      if (this.showHeaderBanner) {\n        elm.classList.add(`${ prefix }-top-banner`);\n        if (this.showFooterBanner) {\n          elm.classList.add(`${ prefix }-two-banners`);\n        }\n      } else if (this.showFooterBanner) {\n        elm.classList.add(`${ prefix }-one-banner`);\n      }\n    },\n\n    async initFrame() {\n      const bannerSetting = await this.$store.getters['management/byId'](MANAGEMENT.SETTING, SETTING.BANNERS);\n\n      try {\n        const parsed = JSON.parse(bannerSetting.value);\n\n        this.showHeaderBanner = parsed.showHeader === 'true';\n        this.showFooterBanner = parsed.showFooter === 'true';\n      } catch {}\n\n      this.loaded = true;\n      this.loadRequired = false;\n\n      // Get the existing iframe if it exists\n      let iframeEl = findEmberPage();\n\n      // If the iframe already exists, check if it is ready for us to reuse\n      // by navigating within the app that is already loaded\n      if (iframeEl !== null) {\n        const ready = iframeEl.getAttribute('data-ready') === 'true';\n        const lastDidLoad = iframeEl.getAttribute('data-loaded') === 'true';\n        const doNotReuse = (!!this.inline && !this.forceInlineReuse) || this.forceNew;\n        // Was not inline but now is - can't reuse\n        const inlineChanged = !!this.inline && (iframeEl.parentElement === document.body);\n\n        if (!ready || doNotReuse || !lastDidLoad || inlineChanged) {\n          iframeEl.remove();\n          iframeEl = null;\n        }\n      }\n\n      if (iframeEl === null && process.env.dev) {\n        // Fetch a page to check that the Ember UI is available\n        try {\n          this.error = false;\n          this.loaded = false;\n          this.emberCheck = this.$axios.CancelToken.source();\n\n          // Make a head request to a known asset of the Ember UI\n          const pageUrl = `${ window.location.origin }/assets/images/logos/rke.svg`;\n          const response = await this.$axios.head(pageUrl, {\n            timeout:     PAGE_CHECK_TIMEOUT,\n            cancelToken: this.emberCheck.token,\n          });\n\n          if (response.status !== 200) {\n            this.loaded = true;\n            this.error = true;\n          }\n        } catch (e) {\n          if (!this.$axios.isCancel(e)) {\n            this.loaded = true;\n            this.error = true;\n          }\n        }\n      }\n\n      if (this.error) {\n        return;\n      }\n\n      if (iframeEl === null) {\n        iframeEl = document.createElement('iframe');\n        iframeEl.setAttribute('id', EMBER_FRAME);\n        iframeEl.setAttribute('data-testid', EMBER_FRAME);\n        iframeEl.classList.add(EMBER_FRAME_HIDE_CLASS);\n\n        if (this.inline) {\n          const frameParent = document.getElementById(this.inline);\n\n          frameParent.appendChild(iframeEl);\n        } else {\n          document.body.append(iframeEl);\n        }\n        iframeEl.setAttribute('src', this.src);\n      } else {\n        // Reset scroll position to top\n        if (iframeEl.contentWindow?.scrollTo) {\n          iframeEl.contentWindow.scrollTo(0, 0);\n        }\n\n        // Post a message to navigate within the existing app\n        iframeEl.contentWindow.postMessage({\n          action: 'navigate',\n          name:   this.src\n        });\n\n        // Ensure iframe gets the latest theme if it has changed\n        this.notifyTheme(this.theme);\n\n        const currentUrl = iframeEl.contentWindow.location.pathname;\n        const src = this.trimURL(this.src);\n\n        if (src !== currentUrl) {\n          iframeEl.classList.add(EMBER_FRAME_HIDE_CLASS);\n        } else {\n          iframeEl.classList.remove(EMBER_FRAME_HIDE_CLASS);\n        }\n      }\n\n      this.iframeEl = iframeEl;\n\n      if (!this.inline) {\n        iframeEl.classList.add('ember-iframe');\n        iframeEl.classList.remove('ember-iframe-inline');\n        this.addBannerClasses(this.$refs.emberPage, 'fixed');\n        this.addBannerClasses(iframeEl, 'ember-iframe');\n\n        // If the window manager is open, sync the size\n        if (this.open) {\n          this.syncSize();\n        }\n      } else {\n        iframeEl.classList.remove('ember-iframe');\n        iframeEl.classList.add('ember-iframe-inline');\n        iframeEl.height = 0;\n        this.syncSize();\n      }\n    },\n\n    syncSize() {\n      if (this.heightSync) {\n        clearTimeout(this.heightSync);\n      }\n\n      this.heightSync = setTimeout(() => {\n        this.dosyncSize();\n        this.syncSize();\n      }, 500);\n    },\n\n    dosyncSize() {\n      if (this.inline) {\n        const iframeEl = findEmberPage();\n        const doc = iframeEl.contentWindow.document;\n        const app = doc.getElementById('application');\n        const h = app?.offsetHeight;\n\n        if (h && this.frameHeight !== h) {\n          this.frameHeight = h;\n          iframeEl.height = h;\n        }\n\n        const frameParent = document.getElementById(this.inline);\n        const w = frameParent.offsetWidth;\n\n        if (w && this.frameWidth !== w) {\n          this.frameWidth = w;\n          iframeEl.width = w;\n        }\n      } else {\n        // Ensure the height takes into count the window manger height\n        const wm = document.getElementById(WINDOW_MANAGER);\n\n        if (wm) {\n          const wmh = wm.offsetHeight;\n\n          if (wmh !== this.wmHeight) {\n            // Adjust the bottom\n            const iframeEl = findEmberPage();\n\n            iframeEl.style.height = `calc(100vh - var(--header-height) - ${ wmh }px)`;\n            this.wmHeight = wmh;\n          }\n        }\n      }\n    },\n\n    notifyTheme(theme) {\n      const iframeEl = findEmberPage();\n\n      if (iframeEl) {\n        const emberTheme = theme === 'light' ? 'ui-light' : 'ui-dark';\n\n        // Ensure the embedded UI uses the correct theme\n        iframeEl.contentWindow.postMessage({\n          action: 'set-theme',\n          name:   emberTheme\n        });\n      }\n    },\n\n    trimURL(url) {\n      if (url && url.endsWith('/')) {\n        url = url.substr(0, url.length - 1);\n      }\n\n      return url;\n    },\n\n    // We use PostMessage between the Embedded Ember UI and the Dashboard UI\n    receiveMessage(event) {\n      const msg = event.data;\n\n      if (msg.action === 'navigate') {\n        this.$router.replace({\n          name:   'c-cluster-explorer',\n          params: { cluster: msg.cluster }\n        });\n      } else if (msg.action === 'before-navigation') {\n        this.$emit('before-nav', msg.target);\n\n        // Ember willTransition event\n        if (INTERCEPTS[msg.target]) {\n          const dest = INTERCEPTS[msg.target];\n\n          if (this.isCurrentRoute(dest)) {\n            this.setLoaded(true);\n\n            return;\n          }\n\n          this.setLoaded(false);\n          this.$router.replace(this.fillRoute(dest));\n        }\n      } else if (msg.action === 'loading') {\n        this.setLoaded(!msg.state);\n        this.updateFrameVisibility();\n      } else if (msg.action === 'ready') {\n        // Echo back a ping\n        this.iframeEl.contentWindow.postMessage({ action: 'echo-back' });\n        this.iframeEl.setAttribute('data-ready', true);\n\n        const doc = this.iframeEl.contentWindow?.document?.body;\n\n        if (this.inline) {\n          doc.classList.add('embedded-no-overflow');\n        } else {\n          doc.classList.remove('embedded-no-overflow');\n        }\n        this.syncLocale();\n      } else if (msg.action === 'need-to-load') {\n        this.loadRequired = true;\n      } else if (msg.action === 'did-transition') {\n        if (!this.loadRequired) {\n          this.setLoaded(true);\n          this.updateFrameVisibility();\n          this.dosyncSize();\n        }\n      } else if (msg.action === 'dashboard') {\n        this.iframeEl.setAttribute('data-ready', false);\n        this.$router.replace(msg.page);\n      } else if (msg.action === 'reload') {\n        this.loaded = false;\n        this.iframeEl.remove();\n        this.initFrame();\n      } else if ( msg.action === 'logout' ) {\n        this.loaded = false;\n        this.iframeEl.remove();\n        this.initFrame();\n        this.$store.dispatch('auth/logout');\n      }\n    },\n\n    setLoaded(loaded) {\n      this.loaded = loaded;\n      if (this.iframeEl) {\n        this.iframeEl.setAttribute('data-loaded', loaded);\n      }\n    },\n\n    updateFrameVisibility() {\n      if (this.loaded) {\n        if (this.iframeEl) {\n          this.iframeEl.classList.remove(EMBER_FRAME_HIDE_CLASS);\n\n          // Notify the embedded UI of the primary and primary text colors\n          const primary = window.getComputedStyle(document.body).getPropertyValue('--primary');\n          const primaryText = window.getComputedStyle(document.body).getPropertyValue('--primary-text');\n\n          this.iframeEl.contentWindow.postMessage({\n            action: 'colors',\n            primary,\n            primaryText,\n          });\n        }\n      }\n    },\n\n    fillRoute(route) {\n      if (typeof route === 'object') {\n        // Fill in standard params\n        if (route.params) {\n          if ('cluster' in route.params) {\n            route.params.cluster = this.clusterId;\n          }\n          if ('product' in route.params) {\n            route.params.product = this.productId;\n          }\n        }\n      }\n\n      return route;\n    },\n\n    isCurrentRoute(route) {\n      const current = this.$route;\n\n      if (current.name === route.name) {\n        let same = true;\n\n        Object.keys(current.params).forEach((p) => {\n          if (route.params[p] !== current.params[p]) {\n            same = false;\n          }\n        });\n\n        return same;\n      }\n\n      return false;\n    },\n\n    syncLocale() {\n      const iframeEl = findEmberPage();\n\n      iframeEl?.contentWindow?.ls('user-language')?.sideLoadLanguage(this.locale);\n    }\n  }\n};\n</script>\n\n<template>\n  <div\n    ref=\"emberPage\"\n    class=\"ember-page\"\n  >\n    <Loading\n      v-if=\"!inline\"\n      :loading=\"!loaded\"\n      mode=\"content\"\n      :no-delay=\"true\"\n    />\n    <div\n      v-if=\"inline && !loaded\"\n      v-clean-html=\"t('generic.loading', {}, true)\"\n      class=\"inline-loading\"\n    />\n    <div\n      v-if=\"error\"\n      class=\"ember-page-error\"\n    >\n      <div>{{ t('embedding.unavailable') }}</div>\n      <button\n        class=\"btn role-primary\"\n        @click=\"initFrame()\"\n      >\n        {{ t('embedding.retry') }}\n      </button>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  $banner-height: 2em;\n\n  .fixed-top-banner {\n    top: calc(#{$banner-height} + var(--header-height));\n  }\n\n  .fixed-one-banner {\n    height: calc(100vh - var(--header-height) - #{$banner-height});\n  }\n\n  .fixed-two-banners {\n    height: calc(100vh - var(--header-height) - #{$banner-height} - #{$banner-height});\n  }\n\n  .ember-page {\n    display: flex;\n    height: 100%;\n    padding: 0;\n  }\n\n  .frame {\n    flex: 1;\n    visibility: hidden;\n  }\n  .frame.pop {\n    margin: -20px;\n  }\n\n  .loading {\n    visibility: visible;\n  }\n  .ember-page-error {\n    display: flex;\n    align-items: center;\n    flex: 1;\n    flex-direction: column;\n    justify-content: center;\n    > div {\n      font-size: 20px;\n      padding-bottom: 20px;\n    }\n  }\n  .inline-loading {\n    border: 1px solid var(--border);\n    border-radius: 5px;\n    padding: 10px;\n    text-align: center;\n    width: 100%;\n  }\n</style>\n<style lang=\"scss\">\n  $banner-height: 2em;\n\n  .ember-iframe {\n    border: 0;\n    left: calc(var(--nav-width) + $app-bar-collapsed-width);\n    height: calc(100vh - var(--header-height));\n    position: absolute;\n    top: var(--header-height);\n    width: calc(100vw - var(--nav-width) - $app-bar-collapsed-width);\n    visibility: show;\n  }\n\n  .ember-iframe-top-banner {\n    top: calc(#{$banner-height} + var(--header-height));\n  }\n\n  .ember-iframe-one-banner {\n    height: calc(100vh - var(--header-height) - #{$banner-height});\n  }\n\n  .ember-iframe-two-banners {\n    height: calc(100vh - var(--header-height) - #{$banner-height} - #{$banner-height});\n  }\n\n  .ember-iframe-inline {\n    border: 0;\n    overflow: hidden;\n  }\n\n  .ember-iframe-hidden {\n    visibility: hidden;\n  }\n</style>\n"]}]}