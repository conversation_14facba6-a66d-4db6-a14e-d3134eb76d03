{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/InputWithSelect.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/InputWithSelect.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/InputWithSelect.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACR,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACb,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACb,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACb,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACb,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB;;EAEF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACd;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE5B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5E,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;EACH,CAAC;AACH,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/InputWithSelect.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport labeledFormElement from '@shell/mixins/labeled-form-element';\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport Select from '@shell/components/form/Select';\nexport default {\n  name:       'InputWithSelect',\n  emits:      ['update:value'],\n  components: {\n    LabeledInput,\n    LabeledSelect,\n    Select,\n  },\n  mixins: [labeledFormElement],\n  props:  {\n    disabled: {\n      type:    Boolean,\n      default: false,\n    },\n\n    searchable: {\n      type:    Boolean,\n      default: true,\n    },\n\n    taggable: {\n      type:    Boolean,\n      default: false,\n    },\n\n    selectLabel: {\n      type:    String,\n      default: '',\n    },\n\n    selectValue: {\n      type:    String,\n      default: null,\n    },\n\n    optionLabel: {\n      type:    String,\n      default: 'label',\n    },\n\n    options: {\n      type:     Array,\n      required: true,\n    },\n\n    selectBeforeText: {\n      type:    Boolean,\n      default: true,\n    },\n\n    textLabel: {\n      type:    String,\n      default: '',\n    },\n\n    textRequired: {\n      type:    <PERSON>olean,\n      default: false,\n    },\n\n    textDisabled: {\n      type:    <PERSON>ole<PERSON>,\n      default: false,\n    },\n\n    textValue: {\n      type:    [String, Number],\n      default: '',\n    },\n\n    placeholder: {\n      type:    String,\n      default: '',\n    },\n    textRules: {\n      default: () => [],\n      type:    Array,\n    },\n    selectRules: {\n      default: () => [],\n      type:    Array,\n    }\n\n  },\n\n  data() {\n    return {\n      selected: this.selectValue || this.options[0].value,\n      string:   this.textValue,\n    };\n  },\n\n  computed: {\n    canPaginate() {\n      return false;\n    }\n  },\n\n  methods: {\n    focus() {\n      const comp = this.$refs.text;\n\n      if (comp) {\n        comp.focus();\n      }\n    },\n\n    change() {\n      this.$emit('update:value', { selected: this.selected, text: this.string });\n    },\n  },\n\n  watch: {\n    textValue(value) {\n      this.string = value;\n    },\n  },\n};\n</script>\n\n<template>\n  <div\n    :class=\"{ 'select-after': !selectBeforeText }\"\n    class=\"input-container row\"\n  >\n    <LabeledSelect\n      v-if=\"selectLabel\"\n      v-model:value=\"selected\"\n      :label=\"selectLabel\"\n      :class=\"{ 'in-input': !isView}\"\n      :options=\"options\"\n      :searchable=\"false\"\n      :clearable=\"false\"\n      :disabled=\"disabled || isView\"\n      :taggable=\"taggable\"\n      :create-option=\"(name) => ({ label: name, value: name })\"\n      :multiple=\"false\"\n      :mode=\"mode\"\n      :option-label=\"optionLabel\"\n      :placement=\"$attrs.placement ? $attrs.placement : null\"\n      :v-bind=\"$attrs\"\n      :rules=\"selectRules\"\n      @update:value=\"change\"\n    />\n    <Select\n      v-else\n      v-model:value=\"selected\"\n      :options=\"options\"\n      :searchable=\"searchable\"\n      :disabled=\"disabled || isView\"\n      :clearable=\"false\"\n      class=\"in-input\"\n      :taggable=\"taggable\"\n      :create-option=\"(name) => ({ label: name, value: name })\"\n      :multiple=\"false\"\n      :mode=\"mode\"\n      :option-label=\"optionLabel\"\n      :placement=\"$attrs.placement ? $attrs.placement : null\"\n      :v-bind=\"$attrs\"\n      @update:value=\"change\"\n    />\n    <LabeledInput\n      v-if=\"textLabel || textRules.length > 0\"\n      ref=\"text\"\n      v-model:value=\"string\"\n      class=\"input-string col span-8\"\n      :label=\"textLabel\"\n      :placeholder=\"placeholder\"\n      :disabled=\"disabled || textDisabled\"\n      :required=\"textRequired\"\n      :mode=\"mode\"\n      :rules=\"textRules\"\n      v-bind=\"$attrs\"\n      @update:value=\"change\"\n    >\n      <template #label>\n        <slot name=\"label\" />\n      </template>\n      <template #suffix>\n        <slot name=\"suffix\" />\n      </template>\n    </LabeledInput>\n    <input\n      v-else\n      ref=\"text\"\n      v-model=\"string\"\n      class=\"input-string\"\n      :disabled=\"isView\"\n      :placeholder=\"placeholder\"\n      autocomplete=\"off\"\n      @input=\"change\"\n    >\n  </div>\n</template>\n\n<style lang='scss' scoped>\n.input-container {\n  display: flex;\n\n  &.select-after {\n    height: 100%;\n    flex-direction: row-reverse;\n\n    & .input-string {\n      border-radius: var(--border-radius) 0 0 var(--border-radius);\n      border-right: 0;\n      border-left-width: 1px;\n    }\n\n    & .in-input {\n      border-radius: 0 var(--border-radius) var(--border-radius) 0;\n\n      &.labeled-select {\n        .selected {\n          color: var(--input-text);\n          text-align: center;\n          margin-right: 1em;\n        }\n      }\n\n      &.focused:not(.vs__dropdown-up) {\n        border-bottom-right-radius: 0;\n      }\n\n      &.focused.vs__dropdown-up {\n        border-top-right-radius: 0;\n      }\n    }\n\n    .input-string {\n      &:hover:not(.focused):not(.disabled):not(:focus) {\n        padding-left: 10px !important;\n      }\n      &.focused, &:focus {\n        padding-left: 10px !important;\n      }\n    }\n  }\n\n  & .input-string {\n    padding-right: 0;\n    width: 60%;\n    flex-grow: 1;\n    border-radius: 0 var(--border-radius) var(--border-radius) 0;\n    border-left-width: 0;\n    margin-left: -1px;\n    position: relative;\n    display: table;\n    border-collapse: separate;\n\n    &:hover:not(.focused):not(.disabled):not(:focus):not(.view) {\n      border-left: 1px solid var(--input-hover-border);\n      border-right: 1px solid var(--input-hover-border);\n      padding-left: 9px;\n    }\n    &.focused, &:focus {\n      border-left: 1px solid var(--outline) !important;\n      border-right: 1px solid var(--outline) !important;\n      padding-left: 9px;\n    }\n  }\n\n  & .in-input {\n    margin-right: 0;\n\n    &:hover:not(.focused):not(.disabled):not(.view) {\n      border: 1px solid var(--input-hover-border) !important;\n    }\n\n    &.focused {\n      border: 1px solid var(--outline) !important;\n    }\n\n    &:hover:not(.focused):not(.disabled) {\n      border: 1px solid var(--input-hover-border) !important;\n    }\n\n    &.focused {\n      border: 1px solid var(--outline) !important;\n    }\n\n    &.labeled-select.focused :deep(),\n    &.unlabeled-select.focused :deep() {\n      outline: none;\n    }\n\n    &.labeled-select:not(.disabled):not(.view) :deep(),\n    &.unlabeled-select:not(.disabled):not(.view) :deep() {\n      border: solid 1px var(--input-border);\n    }\n\n    &.labeled-select :deep(),\n    &.unlabeled-select :deep() {\n      box-shadow: none;\n      width: 20%;\n      margin-right: 1px; // push the input box right so the full focus outline of the select can be seen, z-index borks\n      // position: relative;\n\n      .vs__selected {\n        color: var(--input-text);\n      }\n\n      .vs__dropdown-menu {\n        box-shadow: none;\n        .vs__dropdown-option {\n          padding: 3px 5px;\n        }\n      }\n\n      .v-select:not(.vs--disabled) {\n        .vs__dropdown-toggle {\n          border-radius: var(--border-radius) 0 0 var(--border-radius);\n        }\n        &.vs--open {\n          .vs__dropdown-toggle {\n            color: var(--outline) !important;\n          }\n        }\n      }\n    }\n  }\n}\n\n</style>\n"]}]}