{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/ProjectRow.vue?vue&type=style&index=0&id=db070704&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/ProjectRow.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLnJvdyB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgZmxleC1kaXJlY3Rpb246IHJvdzsKICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtZXZlbmx5OwogIH0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/ProjectRow.vue"], "names": [], "mappings": ";EA6GE,CAAC,CAAC,CAAC,EAAE;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/B", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/ProjectRow.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport Select from '@shell/components/form/Select';\nimport UnitInput from '@shell/components/form/UnitInput';\nimport { ROW_COMPUTED } from './shared';\n\nexport default {\n  emits: ['type-change'],\n\n  components: { Select, UnitInput },\n\n  props: {\n    mode: {\n      type:     String,\n      required: true,\n    },\n    types: {\n      type:    Array,\n      default: () => []\n    },\n    type: {\n      type:    String,\n      default: ''\n    },\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  computed: {\n    ...ROW_COMPUTED,\n\n    resourceQuotaLimit: {\n      get() {\n        return this.value.spec.resourceQuota?.limit || {};\n      },\n    },\n\n    namespaceDefaultResourceQuotaLimit: {\n      get() {\n        return this.value.spec.namespaceDefaultResourceQuota?.limit || {};\n      },\n    }\n  },\n\n  methods: {\n    updateType(type) {\n      if (typeof this.value.spec.resourceQuota?.limit[this.type] !== 'undefined') {\n        delete this.value.spec.resourceQuota.limit[this.type];\n      }\n      if (typeof this.value.spec.namespaceDefaultResourceQuota?.limit[this.type] !== 'undefined') {\n        delete this.value.spec.namespaceDefaultResourceQuota.limit[this.type];\n      }\n\n      this.$emit('type-change', type);\n    },\n\n    updateQuotaLimit(prop, type, val) {\n      if (!this.value.spec[prop]) {\n        this.value.spec[prop] = { limit: { } };\n      }\n\n      this.value.spec[prop].limit[type] = val;\n    }\n  },\n};\n</script>\n<template>\n  <div\n    v-if=\"typeOption\"\n    class=\"row\"\n  >\n    <Select\n      :value=\"type\"\n      class=\"mr-10\"\n      :mode=\"mode\"\n      :options=\"types\"\n      data-testid=\"projectrow-type-input\"\n      @update:value=\"updateType($event)\"\n    />\n    <UnitInput\n      :value=\"resourceQuotaLimit[type]\"\n      class=\"mr-10\"\n      :mode=\"mode\"\n      :placeholder=\"typeOption.placeholder\"\n      :increment=\"typeOption.increment\"\n      :input-exponent=\"typeOption.inputExponent\"\n      :base-unit=\"typeOption.baseUnit\"\n      :output-modifier=\"true\"\n      data-testid=\"projectrow-project-quota-input\"\n      @update:value=\"updateQuotaLimit('resourceQuota', type, $event)\"\n    />\n    <UnitInput\n      :value=\"namespaceDefaultResourceQuotaLimit[type]\"\n      :mode=\"mode\"\n      :placeholder=\"typeOption.placeholder\"\n      :increment=\"typeOption.increment\"\n      :input-exponent=\"typeOption.inputExponent\"\n      :base-unit=\"typeOption.baseUnit\"\n      :output-modifier=\"true\"\n      data-testid=\"projectrow-namespace-quota-input\"\n      @update:value=\"updateQuotaLimit('namespaceDefaultResourceQuota', type, $event)\"\n    />\n  </div>\n</template>\n\n<style lang='scss' scoped>\n  .row {\n    display: flex;\n    flex-direction: row;\n    justify-content: space-evenly;\n  }\n</style>\n"]}]}