{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ConsumptionGauge.vue?vue&type=style&index=0&id=9226cebe&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ConsumptionGauge.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5jb25zdW1wdGlvbi1nYXVnZSB7CiAgLm51bWJlcnMgewogICAgZGlzcGxheTogZmxleDsKICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7CiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CgogICAgJi1zdGF0cyB7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7CiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgIGdhcDogMTBweDsKICAgICAgYWxpZ24tc2VsZjogYmFzZWxpbmU7CgogICAgfQoKICAgIC5wZXJjZW50YWdlIHsKICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICAgIGkgewogICAgICAgIG1hcmdpbi1yaWdodDogMTBweDsKICAgICAgfQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ConsumptionGauge.vue"], "names": [], "mappings": ";AA4HA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEtB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,EAAE;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpB;IACF;EACF;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ConsumptionGauge.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport PercentageBar from '@shell/components/PercentageBar';\nimport { formatPercent } from '@shell/utils/string';\n\n/**\n * A detailed view of how much a resource is being consumed.\n */\nexport default {\n  components: { PercentageBar },\n  props:      {\n    /**\n     * The name of the resource to be displayed.\n     */\n    resourceName: {\n      type:    String,\n      default: ''\n    },\n    /**\n     * The total amount of the resource (both used and unused amount).\n     */\n    capacity: {\n      type:     Number,\n      required: true\n    },\n    /**\n     * The amount of the resource that is currently in use.\n     */\n    used: {\n      type:     Number,\n      required: true\n    },\n    /**\n     * The units that should be displayed when referencing amounts of the resource.\n     */\n    units: {\n      type:    String,\n      default: ''\n    },\n    /**\n     * A method which can be used to format the *capacity* and *used* numbers for display.\n     */\n    numberFormatter: {\n      type:    Function,\n      default: (value) => Number.isInteger(value) ? value : value.toFixed(2)\n    },\n\n    /**\n     * Optional map of css color class: percentage stops to apply to bar\n     */\n\n    colorStops: {\n      type:    Object,\n      default: null\n    },\n\n    /**\n     * Reduce the vertial height by changed 'Used' for the resource name\n     */\n    usedAsResourceName: {\n      type:   <PERSON><PERSON><PERSON>,\n      defaut: false\n    }\n  },\n  computed: {\n    displayUnits() {\n      if ( this.units ) {\n        return ` ${ this.units }`;\n      }\n\n      return '';\n    },\n    percentageBarValue() {\n      if (!this.used || !this.capacity) {\n        return 0;\n      }\n\n      return (this.used * 100) / this.capacity;\n    },\n    amountTemplateValues() {\n      return {\n        used:  this.numberFormatter(this.used || 0),\n        total: this.numberFormatter(this.capacity || 0),\n        unit:  this.displayUnits\n      };\n    },\n    formattedPercentage() {\n      return formatPercent(this.percentageBarValue);\n    }\n  }\n};\n</script>\n\n<template>\n  <div class=\"consumption-gauge\">\n    <h3 v-if=\"resourceName && !usedAsResourceName\">\n      {{ resourceName }}\n    </h3>\n    <div class=\"numbers\">\n      <!-- @slot Optional slot to use as the title rather than showing the resource name -->\n      <slot\n        name=\"title\"\n        :amountTemplateValues=\"amountTemplateValues\"\n        :formattedPercentage=\"formattedPercentage\"\n      >\n        <h4 v-if=\"usedAsResourceName\">\n          {{ resourceName }}\n        </h4>\n        <span v-else>{{ t('node.detail.glance.consumptionGauge.used') }}</span>\n        <span class=\"numbers-stats\">\n          {{ t('node.detail.glance.consumptionGauge.amount', amountTemplateValues) }}\n          <span class=\"percentage\"><i>/&nbsp;</i>{{ formattedPercentage }}</span>\n        </span>\n      </slot>\n    </div>\n    <div class=\"mt-10\">\n      <PercentageBar\n        :modelValue=\"percentageBarValue\"\n        :color-stops=\"colorStops\"\n      />\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\">\n.consumption-gauge {\n  .numbers {\n    display: flex;\n    flex-direction: row;\n    justify-content: space-between;\n\n    &-stats {\n      display: flex;\n      flex-direction: row;\n      align-items: center;\n      gap: 10px;\n      align-self: baseline;\n\n    }\n\n    .percentage {\n      font-weight: bold;\n      i {\n        margin-right: 10px;\n      }\n    }\n  }\n}\n</style>\n"]}]}