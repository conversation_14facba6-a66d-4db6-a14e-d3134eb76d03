{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/FileDiff.vue?vue&type=style&index=0&id=c3e1c8b8&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/FileDiff.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5yb290IHsKICBtYXgtd2lkdGg6IDEwMCU7CiAgcG9zaXRpb246IHJlbGF0aXZlOwogIG92ZXJmbG93OiBhdXRvOwp9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/FileDiff.vue"], "names": [], "mappings": ";AAgHA,CAAC,CAAC,CAAC,CAAC,EAAE;EACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAChB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/FileDiff.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { Diff2HtmlUI } from 'diff2html/lib/ui/js/diff2html-ui-slim.js';\n\nimport { createPatch } from 'diff';\n\nexport default {\n  props: {\n    filename: {\n      type:    String,\n      default: 'file.txt',\n    },\n\n    sideBySide: {\n      type:    Boolean,\n      default: false,\n    },\n\n    orig: {\n      type:     String,\n      required: true,\n    },\n\n    neu: {\n      type:     String,\n      required: true,\n    },\n\n    autoResize: {\n      type:    Boolean,\n      default: true,\n    },\n    footerSpace: {\n      type:    Number,\n      default: 0,\n    },\n    minHeight: {\n      type:    Number,\n      default: 200,\n    }\n  },\n\n  mounted() {\n    this.draw();\n  },\n\n  watch: {\n    sideBySide() {\n      this.draw();\n    }\n  },\n\n  methods: {\n    draw() {\n      const targetElement = document.getElementById('diffElement');\n      const patch = createPatch(\n        this.filename,\n        this.orig,\n        this.neu\n      );\n      const configuration = {\n        // UI\n        synchronisedScroll: true,\n\n        // Base\n        outputFormat: this.sideBySide ? 'side-by-side' : 'line-by-line',\n        drawFileList: false,\n        matching:     'words',\n      };\n\n      const diff2htmlUi = new Diff2HtmlUI(targetElement, patch, configuration);\n\n      diff2htmlUi.draw();\n      this.fit();\n    },\n\n    fit() {\n      if ( !this.autoResize ) {\n        return;\n      }\n\n      const container = this.$refs.root;\n\n      if ( !container ) {\n        return;\n      }\n\n      const offset = container.getBoundingClientRect();\n\n      if ( !offset ) {\n        return;\n      }\n\n      const desired = window.innerHeight - offset.top - this.footerSpace;\n\n      container.style.height = `${ Math.max(0, desired) }px`;\n    },\n  },\n};\n</script>\n\n<template>\n  <div>\n    <resize-observer @notify=\"fit\" />\n    <div\n      id=\"diffElement\"\n      ref=\"root\"\n      class=\"root\"\n    />\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.root {\n  max-width: 100%;\n  position: relative;\n  overflow: auto;\n}\n</style>\n\n<style scoped lang=\"scss\">\n@import 'node_modules/diff2html/bundles/css/diff2html.min.css';\n\n:deep() .d2h-wrapper {\n  .d2h-file-header {\n    display: none;\n  }\n\n  .d2h-file-wrapper {\n    border-color: var(--diff-border);\n  }\n\n  .d2h-diff-table {\n    font-family: Menlo,Consolas,monospace;\n    font-size: 13px;\n  }\n\n  .d2h-emptyplaceholder, .d2h-code-side-emptyplaceholder {\n    border-color: var(--diff-linenum-border);\n    background-color: var(--diff-empty-placeholder);\n  }\n\n  .d2h-code-linenumber,\n  .d2h-code-side-linenumber {\n    background-color: var(--diff-linenum-bg);\n    color: var(--diff-linenum);\n    border-color: var(--diff-linenum-border);\n    border-left: 0;\n  }\n\n  .d2h-code-line del,.d2h-code-side-line del {\n    background-color: var(--diff-line-del-bg);\n  }\n\n  .d2h-code-line ins,.d2h-code-side-line ins {\n    background-color: var(--diff-line-ins-bg);\n  }\n\n  .d2h-del {\n    background-color: var(--diff-del-bg);\n    border-color: var(--diff-del-border);\n    color: var(--body-text);\n  }\n\n  .d2h-ins {\n    background-color: var(--diff-ins-bg);\n    border-color: var(--diff-ins-border);\n    color: var(--body-text);\n  }\n\n  .d2h-info {\n    background-color: var(--diff-header-bg);\n    color: var(--diff-header);\n    border-color: var(--diff-header-border);\n  }\n\n  .d2h-file-diff .d2h-del.d2h-change {\n    background-color: var(--diff-chg-del);\n  }\n\n  .d2h-file-diff .d2h-ins.d2h-change {\n    background-color: var(--diff-chg-ins);\n  }\n}\n</style>\n"]}]}