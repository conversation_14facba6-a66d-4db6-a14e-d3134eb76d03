{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayListGrouped.vue?vue&type=template&id=32af8c7f", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayListGrouped.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPEFycmF5TGlzdAogICAgY2xhc3M9ImFycmF5LWxpc3QtZ3JvdXBlZCIKICAgIDp2YWx1ZT0idmFsdWUiCiAgICB2LWJpbmQ9IiRhdHRycyIKICAgIDphZGQtYWxsb3dlZD0iY2FuQWRkICYmICFpc1ZpZXciCiAgICA6bW9kZT0ibW9kZSIKICAgIDppbml0aWFsLWVtcHR5LXJvdz0iaW5pdGlhbEVtcHR5Um93IgogICAgQHVwZGF0ZTp2YWx1ZT0iJGVtaXQoJ3VwZGF0ZTp2YWx1ZScsICRldmVudCkiCiAgICBAYWRkPSIkZW1pdCgnYWRkJykiCiAgICBAcmVtb3ZlPSIkZW1pdCgncmVtb3ZlJywgJGV2ZW50KSIKICA+CiAgICA8dGVtcGxhdGUgdi1zbG90OmNvbHVtbnM9InNjb3BlIj4KICAgICAgPEluZm9Cb3g+CiAgICAgICAgPHNsb3Qgdi1iaW5kPSJzY29wZSIgLz4KICAgICAgPC9JbmZvQm94PgogICAgPC90ZW1wbGF0ZT4KICAgIDx0ZW1wbGF0ZSB2LXNsb3Q6cmVtb3ZlLWJ1dHRvbj0ic2NvcGUiPgogICAgICA8YnV0dG9uCiAgICAgICAgdi1pZj0iY2FuUmVtb3ZlUm93KHNjb3BlLnJvdywgc2NvcGUuaSkiCiAgICAgICAgdHlwZT0iYnV0dG9uIgogICAgICAgIGNsYXNzPSJidG4gcm9sZS1saW5rIGNsb3NlIGJ0bi1zbSIKICAgICAgICA6ZGF0YS10ZXN0aWQ9ImByZW1vdmUtaXRlbS0ke3Njb3BlLml9YCIKICAgICAgICBAY2xpY2s9InNjb3BlLnJlbW92ZSIKICAgICAgPgogICAgICAgIDxpIGNsYXNzPSJpY29uIGljb24teCIgLz4KICAgICAgPC9idXR0b24+CiAgICAgIDxzcGFuIHYtZWxzZSAvPgogICAgPC90ZW1wbGF0ZT4KICAgIDwhLS0gUGFzcyBkb3duIHRlbXBsYXRlcyBwcm92aWRlZCBieSB0aGUgY2FsbGVyIC0tPgogICAgPHRlbXBsYXRlCiAgICAgIHYtZm9yPSIoXywgc2xvdCkgb2YgJHNsb3RzIgogICAgICAjW3Nsb3RdPSJzY29wZSIKICAgICAgOmtleT0ic2xvdCIKICAgID4KICAgICAgPHRlbXBsYXRlIHYtaWY9InR5cGVvZiAkc2xvdHNbc2xvdF0gPT09ICdmdW5jdGlvbiciPgogICAgICAgIDxzbG90CiAgICAgICAgICA6bmFtZT0ic2xvdCIKICAgICAgICAgIHYtYmluZD0ic2NvcGUiCiAgICAgICAgLz4KICAgICAgPC90ZW1wbGF0ZT4KICAgIDwvdGVtcGxhdGU+CiAgPC9BcnJheUxpc3Q+Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayListGrouped.vue"], "names": [], "mappings": ";EA4EE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClC;IACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB;QACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ;MACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC,CAAC;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayListGrouped.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport ArrayList from '@shell/components/form/ArrayList';\nimport InfoBox from '@shell/components/InfoBox';\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\n\nexport default {\n  name:       'ArrayListGrouped',\n  components: { ArrayList, InfoBox },\n  props:      {\n    /**\n     * Allow to remove items by value or computation\n     */\n    canRemove: {\n      type:    [Boolean, Function],\n      default: true,\n    },\n\n    /**\n     * Allow to extend list\n     */\n    canAdd: {\n      type:    Boolean,\n      default: true,\n    },\n    /**\n     * Start with empty row\n     */\n    initialEmptyRow: {\n      type:    Boolean,\n      default: false,\n    },\n\n    /**\n     * Form mode for the component\n     */\n    mode: {\n      type:    String,\n      default: _EDIT,\n    },\n\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      },\n    },\n  },\n\n  emits: ['update:value', 'add', 'remove'],\n\n  computed: {\n    isView() {\n      return this.mode === _VIEW;\n    }\n  },\n\n  methods: {\n    /**\n     * Verify if row can be removed by mode, function and declaration\n     */\n    canRemoveRow(row, idx) {\n      if ( this.isView ) {\n        return false;\n      }\n\n      if ( typeof this.canRemove === 'function' ) {\n        return this.canRemove(row, idx);\n      }\n\n      return this.canRemove;\n    },\n  }\n};\n</script>\n\n<template>\n  <ArrayList\n    class=\"array-list-grouped\"\n    :value=\"value\"\n    v-bind=\"$attrs\"\n    :add-allowed=\"canAdd && !isView\"\n    :mode=\"mode\"\n    :initial-empty-row=\"initialEmptyRow\"\n    @update:value=\"$emit('update:value', $event)\"\n    @add=\"$emit('add')\"\n    @remove=\"$emit('remove', $event)\"\n  >\n    <template v-slot:columns=\"scope\">\n      <InfoBox>\n        <slot v-bind=\"scope\" />\n      </InfoBox>\n    </template>\n    <template v-slot:remove-button=\"scope\">\n      <button\n        v-if=\"canRemoveRow(scope.row, scope.i)\"\n        type=\"button\"\n        class=\"btn role-link close btn-sm\"\n        :data-testid=\"`remove-item-${scope.i}`\"\n        @click=\"scope.remove\"\n      >\n        <i class=\"icon icon-x\" />\n      </button>\n      <span v-else />\n    </template>\n    <!-- Pass down templates provided by the caller -->\n    <template\n      v-for=\"(_, slot) of $slots\"\n      #[slot]=\"scope\"\n      :key=\"slot\"\n    >\n      <template v-if=\"typeof $slots[slot] === 'function'\">\n        <slot\n          :name=\"slot\"\n          v-bind=\"scope\"\n        />\n      </template>\n    </template>\n  </ArrayList>\n</template>\n\n<style lang=\"scss\">\n.array-list-grouped {\n    & > .box {\n      position: relative;\n      display: block;\n\n      & > .remove {\n        position: absolute;\n\n        top: 0;\n        right: 0;\n      }\n\n      & > .info-box {\n        margin-bottom: 0;\n        padding-right: 25px;\n      }\n    }\n}\n\n</style>\n"]}]}