{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ClusterBadge.vue?vue&type=template&id=fb5f6f68&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ClusterBadge.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdgogICAgdi1pZj0iaGFzQmFkZ2UiCiAgICA6c3R5bGU9InsgYmFja2dyb3VuZENvbG9yOiBjbHVzdGVyLmJhZGdlLmNvbG9yLCBjb2xvcjogY2x1c3Rlci5iYWRnZS50ZXh0Q29sb3IgfSIKICAgIGNsYXNzPSJjbHVzdGVyLWJhZGdlIgogICAgOmNsYXNzPSJ7J2NsdXN0ZXItYmFkZ2UtYm9yZGVyJzogc2hvd0JvcmRlcnN9IgogICAgOmFyaWEtbGFiZWw9InQoJ2NsdXN0ZXJCYWRnZS5jbHVzdGVyQ29tbWVudCcsIHsgdGV4dDogY2x1c3Rlci5iYWRnZT8udGV4dCB8fCAnJyB9KSIKICA+CiAgICB7eyBjbHVzdGVyLmJhZGdlLnRleHQgfX0KICA8L2Rpdj4K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ClusterBadge.vue"], "names": [], "mappings": ";EAoBE,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAChF,CAAC,CA<PERSON>,CAAC,CAAC,CAAC,CAAC,CAAC,<PERSON>AC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACpF;IACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ClusterBadge.vue", "sourceRoot": "", "sourcesContent": ["<script>\nexport default {\n  props: {\n    cluster: {\n      type:     Object,\n      required: true,\n    },\n  },\n  computed: {\n    hasBadge() {\n      return !!this.cluster?.badge?.text;\n    },\n    showBorders() {\n      return this.cluster?.badge?.color === 'transparent';\n    },\n  }\n};\n</script>\n\n<template>\n  <div\n    v-if=\"hasBadge\"\n    :style=\"{ backgroundColor: cluster.badge.color, color: cluster.badge.textColor }\"\n    class=\"cluster-badge\"\n    :class=\"{'cluster-badge-border': showBorders}\"\n    :aria-label=\"t('clusterBadge.clusterComment', { text: cluster.badge?.text || '' })\"\n  >\n    {{ cluster.badge.text }}\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .cluster-badge {\n    cursor: default;\n    border-radius: 10px;\n    font-size: 12px;\n    padding: 2px 10px;\n    max-width: 250px;\n    text-overflow: ellipsis;\n    overflow: hidden;\n\n     &-border {\n      border: 1px solid var(--border);\n      border-radius: 5px;\n      color: var(--body-text) !important; // !important is needed to override the color set by the badge when there's a transparent background.\n    }\n  }\n</style>\n"]}]}