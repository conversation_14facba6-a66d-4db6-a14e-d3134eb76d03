{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Dialog.vue?vue&type=style&index=0&id=530e148b&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Dialog.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLm1vZGFsLWRpYWxvZyB7CiAgICBwYWRkaW5nOiAxMHB4OwoKICAgIGg0IHsKICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICB9CgogICAgLmRpYWxvZy1idXR0b25zIHsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsKICAgICAgbWFyZ2luLXRvcDogMTBweDsKCiAgICAgID4gKjpub3QoOmxhc3QtY2hpbGQpIHsKICAgICAgICBtYXJnaW4tcmlnaHQ6IDEwcHg7CiAgICAgIH0KICAgIH0KICB9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Dialog.vue"], "names": [], "mappings": ";EAsHE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEb,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEhB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpB;IACF;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Dialog.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport AsyncButton from '@shell/components/AsyncButton';\nimport AppModal, { DEFAULT_ITERABLE_NODE_SELECTOR } from '@shell/components/AppModal.vue';\n\nexport default {\n  emits: ['okay', 'closed'],\n\n  components: { AsyncButton, AppModal },\n\n  props: {\n    name: {\n      type:     String,\n      required: true,\n    },\n\n    title: {\n      type:     String,\n      required: true,\n    },\n\n    mode: {\n      type:    String,\n      default: '',\n    },\n\n    /**\n     * forcefully set return focus element based on this selector\n     */\n    returnFocusSelector: {\n      type:    String,\n      default: '',\n    },\n\n    /**\n     * will return focus to the first iterable node of this container select\n     */\n    returnFocusFirstIterableNodeSelector: {\n      type:    String,\n      default: DEFAULT_ITERABLE_NODE_SELECTOR,\n    }\n  },\n\n  data() {\n    return { closed: false };\n  },\n\n  methods: {\n    beforeOpen() {\n      this.closed = false;\n    },\n\n    ok(btnCb) {\n      const callback = (ok) => {\n        btnCb(ok);\n        if (ok) {\n          this.closeDialog(true);\n        }\n      };\n\n      this.$emit('okay', callback);\n    },\n\n    closeDialog(result) {\n      if (!this.closed) {\n        this.$emit('closed', result);\n        this.closed = true;\n      }\n    },\n  }\n};\n</script>\n\n<template>\n  <app-modal\n    v-if=\"!closed\"\n    :name=\"name\"\n    height=\"auto\"\n    :scrollable=\"true\"\n    :trigger-focus-trap=\"true\"\n    :return-focus-selector=\"returnFocusSelector\"\n    :return-focus-first-iterable-node-selector=\"returnFocusFirstIterableNodeSelector\"\n    @close=\"closeDialog(false)\"\n    @before-open=\"beforeOpen\"\n  >\n    <div class=\"modal-dialog\">\n      <h4>\n        {{ title }}\n      </h4>\n      <slot />\n      <div class=\"dialog-buttons mt-20\">\n        <slot name=\"buttons\" />\n        <div v-if=\"!$slots.buttons\">\n          <button\n            class=\"btn role-secondary\"\n            @click=\"closeDialog(false)\"\n          >\n            {{ t('generic.cancel') }}\n          </button>\n          <button\n            v-if=\"!mode\"\n            class=\"btn role-primary ml-10\"\n            @click=\"closeDialog(true)\"\n          >\n            {{ t('generic.ok') }}\n          </button>\n          <AsyncButton\n            v-else\n            :mode=\"mode\"\n            class=\"ml-10\"\n            @click=\"ok\"\n          />\n        </div>\n      </div>\n    </div>\n  </app-modal>\n</template>\n\n<style lang=\"scss\" scoped>\n  .modal-dialog {\n    padding: 10px;\n\n    h4 {\n      font-weight: bold;\n    }\n\n    .dialog-buttons {\n      display: flex;\n      justify-content: flex-end;\n      margin-top: 10px;\n\n      > *:not(:last-child) {\n        margin-right: 10px;\n      }\n    }\n  }\n</style>\n"]}]}