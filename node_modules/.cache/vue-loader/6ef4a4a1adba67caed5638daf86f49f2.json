{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceList/ResourceLoadingIndicator.vue?vue&type=style&index=0&id=351c01f1&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceList/ResourceLoadingIndicator.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLnJlc291cmNlLWxvYWRpbmctaW5kaWNhdG9yIHsKICAgIGJvcmRlcjogMXB4IHNvbGlkIHZhcigtLWxpbmspOwogICAgYm9yZGVyLXJhZGl1czogMTBweDsKICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsKICAgIHdpZHRoOiBtaW4tY29udGVudDsKICAgIG92ZXJmbG93OiBoaWRkZW47CgogICAgLnJlc291cmNlLWxvYWRlcjpsYXN0LWNoaWxkIHsKICAgICAgcG9zaXRpb246IGFic29sdXRlOwogICAgICB0b3A6IDA7CgogICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1saW5rKTsKICAgICAgY29sb3I6IHZhcigtLWxpbmstdGV4dCk7CiAgICAgIG92ZXJmbG93OiBoaWRkZW47CiAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7CiAgICB9CgogICAgLnJlc291cmNlLWxvYWRlciB7CiAgICAgIHBhZGRpbmc6IDFweCAxMHB4OwogICAgICB3aWR0aDogbWF4LWNvbnRlbnQ7CgogICAgICAucmwtZmcsIC5ybC1iZyB7CiAgICAgICAgYWxpZ24tY29udGVudDogY2VudGVyOwogICAgICAgIGRpc3BsYXk6IGZsZXg7CgogICAgICAgID4gaSB7CiAgICAgICAgICBmb250LXNpemU6IDE4cHg7CiAgICAgICAgICBsaW5lLWhlaWdodDogMThweDsKICAgICAgICB9CgogICAgICAgID4gc3BhbiB7CiAgICAgICAgICBtYXJnaW4tbGVmdDogNXB4OwogICAgICAgIH0KICAgICAgfQogICAgfQogIH0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceList/ResourceLoadingIndicator.vue"], "names": [], "mappings": ";EAoGE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,EAAE,CAAC;;MAEN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEb,EAAE,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnB;;QAEA,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClB;MACF;IACF;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceList/ResourceLoadingIndicator.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { COUNT } from '@shell/config/types';\n\n/**\n * Loading Indicator for resources - used when we are loading resources incrementally, by page\n */\nexport default {\n\n  name: 'ResourceLoadingIndicator',\n\n  props: {\n    resources: {\n      type:     Array,\n      required: true,\n    },\n    indeterminate: {\n      type:    Boolean,\n      default: false,\n    },\n  },\n\n  data() {\n    const inStore = this.$store.getters['currentStore'](this.resource);\n\n    return { inStore };\n  },\n\n  computed: {\n    // Count of rows - either from the data provided or from the rows for the first resource\n    rowsCount() {\n      if (this.resources.length > 0) {\n        const existingData = this.$store.getters[`${ this.inStore }/all`](this.resources[0]) || [];\n\n        return (existingData || []).length;\n      }\n\n      return 0;\n    },\n\n    // Have we loaded all resources for the types that are needed\n    haveAll() {\n      return this.resources.reduce((acc, r) => {\n        return acc && this.$store.getters[`${ this.inStore }/haveAll`](r);\n      }, true);\n    },\n\n    // Total of all counts of all resources for all of the resources being loaded\n    total() {\n      const clusterCounts = this.$store.getters[`${ this.inStore }/all`](COUNT);\n\n      return this.resources.reduce((acc, r) => {\n        const resourceCounts = clusterCounts?.[0]?.counts?.[r];\n        const resourceCount = resourceCounts?.summary?.count;\n        const count = resourceCount || 0;\n\n        return acc + count;\n      }, 0);\n    },\n\n    // Total count of all of the resources for all of the resources being loaded\n    count() {\n      return this.resources.reduce((acc, r) => {\n        return acc + (this.$store.getters[`${ this.inStore }/all`](r) || []).length;\n      }, 0);\n    },\n\n    // Width style to enable the progress bar style presentation\n    width() {\n      const progress = Math.ceil(100 * (this.count / this.total));\n\n      return `${ progress }%`;\n    }\n  },\n};\n</script>\n\n<template>\n  <div\n    v-if=\"count && !haveAll\"\n    class=\"ml-10 resource-loading-indicator\"\n  >\n    <div class=\"inner\">\n      <div class=\"resource-loader\">\n        <div class=\"rl-bg\">\n          <i class=\"icon icon-spinner icon-spin\" /><span>{{ t( 'resourceLoadingIndicator.loading' ) }} <span v-if=\"!indeterminate\">{{ count }} / {{ total }}</span></span>\n        </div>\n      </div>\n      <div\n        class=\"resource-loader\"\n        :style=\"{width}\"\n      >\n        <div class=\"rl-fg\">\n          <i class=\"icon icon-spinner icon-spin\" /><span>{{ t( 'resourceLoadingIndicator.loading' ) }} <span v-if=\"!indeterminate\">{{ count }} / {{ total }}</span></span>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .resource-loading-indicator {\n    border: 1px solid var(--link);\n    border-radius: 10px;\n    position: relative;\n    width: min-content;\n    overflow: hidden;\n\n    .resource-loader:last-child {\n      position: absolute;\n      top: 0;\n\n      background-color: var(--link);\n      color: var(--link-text);\n      overflow: hidden;\n      white-space: nowrap;\n    }\n\n    .resource-loader {\n      padding: 1px 10px;\n      width: max-content;\n\n      .rl-fg, .rl-bg {\n        align-content: center;\n        display: flex;\n\n        > i {\n          font-size: 18px;\n          line-height: 18px;\n        }\n\n        > span {\n          margin-left: 5px;\n        }\n      }\n    }\n  }\n</style>\n"]}]}