{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/ForceDirectedTreeChart/index.vue?vue&type=template&id=1b75a374", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/ForceDirectedTreeChart/index.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/ForceDirectedTreeChart/index.vue"], "names": [], "mappings": ";EAgWE,CAAC,CAAC,CAAC,CAAC;IACF,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B;MACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B;QACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAClC,CAAC,CAAC,CAAC;QACH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACpC,CAAC,CAAC,CAAC;QACH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC3C,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC;cACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACT;cACE,CAAC,CAAC;gBACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvD;gBACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClE,CAAC,CAAC,CAAC,CAAC;cACJ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cACtB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACnC;oBACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9C,CAAC,CAAC,CAAC,CAAC;cACJ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cAC5B,CAAC,CAAC;gBACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrB;gBACE,CAAC,CAAC,CAAC,CAAC,CAAC;kBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACpB,CAAC;gBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACR,CAAC,CAAC,CAAC,CAAC;cACJ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cAC7B,CAAC,CAAC;gBACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ;gBACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,CAAC,CAAC,CAAC;cACJ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cACxB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/ForceDirectedTreeChart/index.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport * as d3 from 'd3';\nimport { STATES } from '@shell/plugins/dashboard-store/resource-class';\nimport { BadgeState } from '@components/BadgeState';\nimport { getChartIcon } from './chartIcons.js';\n\nexport default {\n  name:       'ForceDirectedTreeChart',\n  components: { BadgeState },\n  props:      {\n    data: {\n      type:     [Array, Object],\n      required: true\n    },\n    fdcConfig: {\n      type:     Object,\n      required: true\n    }\n  },\n  data() {\n    return {\n      dataWatcher:                         undefined,\n      parsedInfo:                          undefined,\n      root:                                undefined,\n      allNodesData:                        undefined,\n      allLinks:                            undefined,\n      rootNode:                            undefined,\n      node:                                undefined,\n      link:                                undefined,\n      svg:                                 undefined,\n      zoom:                                undefined,\n      simulation:                          undefined,\n      isChartFirstRendered:                false,\n      isChartFirstRenderAnimationFinished: false,\n      moreInfo:                            {}\n    };\n  },\n  methods: {\n    watcherFunction(newValue) {\n      if (newValue.length) {\n        if (!this.isChartFirstRendered) {\n          this.parsedInfo = this.fdcConfig.parseData(this.data);\n\n          // set details info and set active state for node\n          this.setDetailsInfo(this.parsedInfo, false);\n          this.parsedInfo.active = true;\n\n          // render and update chart\n          this.renderChart();\n          this.updateChart(true, true);\n          this.isChartFirstRendered = true;\n\n          // here we just look for changes in the status of the nodes and update them accordingly\n        } else {\n          const parsedInfo = this.fdcConfig.parseData(this.data);\n          const flattenedData = this.flatten(parsedInfo);\n          let hasStatusChange = false;\n\n          flattenedData.forEach((item) => {\n            const index = this.allNodesData.findIndex((nodeData) => item.matchingId === nodeData.data.matchingId);\n\n            // apply status change to each node\n            if (index > -1 && this.allNodesData[index].data.state !== item.state) {\n              this.allNodesData[index].data.state = item.state;\n              this.allNodesData[index].data.stateLabel = item.stateLabel;\n              this.allNodesData[index].data.stateColor = item.stateColor;\n              hasStatusChange = true;\n\n              // if node is selected (active), update details info\n              if (this.allNodesData[index].data.active) {\n                this.setDetailsInfo(this.allNodesData[index].data, false);\n              }\n            }\n          });\n\n          if (hasStatusChange) {\n            this.updateChart(false, false);\n          }\n        }\n      }\n    },\n    renderChart() {\n      this.zoom = d3.zoom().scaleExtent([1 / 8, 16]).on('zoom', this.zoomed);\n      const transform = d3.zoomIdentity.scale(1).translate(0, 0);\n\n      this.rootNode = this.svg.append('g')\n        .attr('class', 'root-node');\n\n      this.svg.call(this.zoom);\n      this.svg.call(this.zoom.transform, transform);\n\n      this.simulation = d3.forceSimulation()\n        .force('charge', d3.forceManyBody().strength(this.fdcConfig.simulationParams.fdcStrength).distanceMax(this.fdcConfig.simulationParams.fdcDistanceMax))\n        .force('collision', d3.forceCollide(this.fdcConfig.simulationParams.fdcForceCollide))\n        .force('center', d3.forceCenter( this.fdcConfig.chartWidth / 2, this.fdcConfig.chartHeight / 2 ))\n        .alphaDecay(this.fdcConfig.simulationParams.fdcAlphaDecay)\n        .on('tick', this.ticked)\n        .on('end', () => {\n          if (!this.isChartFirstRenderAnimationFinished) {\n            this.zoomFit();\n            this.isChartFirstRenderAnimationFinished = true;\n          }\n        });\n    },\n    updateChart(isStartingData, isSettingNodesAndLinks) {\n      if (isStartingData) {\n        this.root = d3.hierarchy(this.parsedInfo);\n      }\n\n      if (isSettingNodesAndLinks) {\n        this.allNodesData = this.flatten(this.root);\n        this.allLinks = this.root.links();\n      }\n\n      this.link = this.rootNode\n        .selectAll('.link')\n        .data(this.allLinks, (d) => {\n          return d.target.id;\n        });\n\n      this.link.exit().remove();\n\n      const linkEnter = this.link\n        .enter()\n        .append('line')\n        .attr('class', 'link')\n        .style('opacity', '0.2')\n        .style('stroke-width', 4);\n\n      this.link = linkEnter.merge(this.link);\n\n      this.node = this.rootNode\n        .selectAll('.node')\n        .data(this.allNodesData, (d) => {\n          return d.id;\n        })\n        // this is where we define which prop changes with any data update (status color)\n        .attr('class', this.mainNodeClass);\n\n      this.node.exit().remove();\n\n      // define the node styling and function\n      const nodeEnter = this.node\n        .enter()\n        .append('g')\n        .attr('class', this.mainNodeClass)\n        .style('opacity', 1)\n        .on('click', (ev, d) => {\n          this.setDetailsInfo(d.data, true);\n        })\n        .call(d3.drag()\n          .on('start', this.dragStarted)\n          .on('drag', this.dragging)\n          .on('end', this.dragEnded));\n\n      // draw status circle (inherits color from main node)\n      nodeEnter.append('circle')\n        .attr('r', this.setNodeRadius);\n\n      nodeEnter.append('circle')\n        .attr('r', (d) => {\n          return this.setNodeRadius(d) - 5;\n        })\n        .attr('class', 'node-hover-layer');\n\n      nodeEnter.append('svg').html((d) => {\n        const icon = this.fdcConfig.fetchNodeIcon(d);\n\n        return getChartIcon(icon);\n      })\n        .attr('x', this.nodeImagePosition)\n        .attr('y', this.nodeImagePosition)\n        .attr('height', this.nodeImageSize)\n        .attr('width', this.nodeImageSize);\n\n      this.node = nodeEnter.merge(this.node);\n\n      this.simulation.nodes(this.allNodesData);\n      this.simulation.force('link', d3.forceLink()\n        .id((d) => {\n          return d.id;\n        })\n        .distance(100)\n        .links(this.allLinks)\n      );\n    },\n    mainNodeClass(d) {\n      const lowerCaseStatus = d.data?.state ? d.data.state.toLowerCase() : 'unkown_status';\n      const defaultClassArray = ['node'];\n\n      if (STATES[lowerCaseStatus] && STATES[lowerCaseStatus].color) {\n        defaultClassArray.push(`node-${ STATES[lowerCaseStatus].color }`);\n      } else {\n        defaultClassArray.push(`node-default-fill`);\n      }\n\n      // node active (clicked)\n      if (d.data?.active) {\n        defaultClassArray.push('active');\n      }\n\n      // here we extend the node classes (different chart types)\n      const extendedClassArray = this.fdcConfig.extendNodeClass(d).concat(defaultClassArray);\n\n      return extendedClassArray.join(' ');\n    },\n    setNodeRadius(d) {\n      const { radius } = this.fdcConfig.nodeDimensions(d);\n\n      return radius;\n    },\n    nodeImageSize(d) {\n      const { size } = this.fdcConfig.nodeDimensions(d);\n\n      return size;\n    },\n    nodeImagePosition(d) {\n      const { position } = this.fdcConfig.nodeDimensions(d);\n\n      return position;\n    },\n    setDetailsInfo(data, toUpdate) {\n      // get the data to be displayed on info box, per each different chart\n      this.moreInfo = Object.assign([], this.fdcConfig.infoDetails(data));\n\n      // update to the chart is needed when active state changes\n      if (toUpdate) {\n        this.allNodesData.forEach((item, i) => {\n          if (item.data.matchingId === data.matchingId) {\n            this.allNodesData[i].data.active = true;\n          } else {\n            this.allNodesData[i].data.active = false;\n          }\n        });\n\n        this.updateChart(false, false);\n      }\n    },\n    zoomFit() {\n      const rootNode = d3.select('.root-node');\n\n      if (!rootNode?.node()) {\n        return;\n      }\n\n      const paddingBuffer = 30;\n      const chartDimentions = rootNode.node().getBoundingClientRect();\n      const chartCoordinates = rootNode.node().getBBox();\n      const parent = rootNode.node().parentElement;\n      const fullWidth = parent.clientWidth;\n      const fullHeight = parent.clientHeight;\n      const width = chartDimentions.width;\n      const height = chartDimentions.height;\n      const midX = chartCoordinates.x + width / 2;\n      const midY = chartCoordinates.y + height / 2;\n\n      if (width === 0 || height === 0) {\n        return;\n      } // nothing to fit\n\n      const scale = 1 / Math.max(width / (fullWidth - paddingBuffer), height / (fullHeight - paddingBuffer));\n      const translate = [fullWidth / 2 - scale * midX, fullHeight / 2 - scale * midY];\n\n      const transform = d3.zoomIdentity\n        .translate(translate[0], translate[1])\n        .scale(scale);\n\n      // this update the cached zoom state!!!!! very important so that any transforms from user interaction keep this base!\n      this.svg.call(this.zoom.transform, transform);\n    },\n    ticked() {\n      this.link\n        .attr('x1', (d) => {\n          return d.source.x;\n        })\n        .attr('y1', (d) => {\n          return d.source.y;\n        })\n        .attr('x2', (d) => {\n          return d.target.x;\n        })\n        .attr('y2', (d) => {\n          return d.target.y;\n        });\n\n      this.node\n        .attr('transform', (d) => {\n          return `translate(${ d.x }, ${ d.y })`;\n        });\n    },\n    dragStarted(ev, d) {\n      if (!ev.active) {\n        this.simulation.alphaTarget(0.3).restart();\n      }\n      d.fx = d.x;\n      d.fy = d.y;\n    },\n    dragging(ev, d) {\n      d.fx = ev.x;\n      d.fy = ev.y;\n    },\n    dragEnded(ev, d) {\n      if (!ev.active) {\n        this.simulation.alphaTarget(0);\n      }\n      d.fx = undefined;\n      d.fy = undefined;\n    },\n    zoomed(ev) {\n      this.rootNode.attr('transform', ev.transform);\n    },\n    flatten(root) {\n      const nodes = [];\n      let i = 0;\n\n      function recurse(node) {\n        if (node.children) {\n          node.children.forEach(recurse);\n        }\n        if (!node.id) {\n          node.id = ++i;\n        } else {\n          ++i;\n        }\n        nodes.push(node);\n      }\n      recurse(root);\n\n      return nodes;\n    }\n  },\n  mounted() {\n    // start by appending SVG to define height of chart area\n    this.svg = d3.select('#tree').append('svg')\n      .attr('viewBox', `0 0 ${ this.fdcConfig.chartWidth } ${ this.fdcConfig.chartHeight }`)\n      .attr('preserveAspectRatio', 'none');\n\n    // set watcher for the chart data\n    this.dataWatcher = this.$watch(this.fdcConfig.watcherProp, function(newValue) {\n      this.watcherFunction(newValue);\n    }, {\n      deep:      true,\n      immediate: true\n    });\n  },\n  unmounted() {\n    this.dataWatcher();\n  },\n};\n</script>\n\n<template>\n  <div>\n    <div\n      class=\"chart-container\"\n      data-testid=\"gitrepo_graph\"\n    >\n      <!-- loading status container -->\n      <div\n        v-if=\"!isChartFirstRenderAnimationFinished\"\n        class=\"loading-container\"\n      >\n        <p v-show=\"!isChartFirstRendered\">\n          {{ t('fleet.fdc.loadingChart') }}\n        </p>\n        <p v-show=\"isChartFirstRendered && !isChartFirstRenderAnimationFinished\">\n          {{ t('fleet.fdc.renderingChart') }}\n        </p>\n        <i class=\"mt-10 icon-spinner icon-spin\" />\n      </div>\n      <!-- main div for svg container -->\n      <div id=\"tree\" />\n      <!-- info box -->\n      <div class=\"more-info-container\">\n        <div class=\"more-info\">\n          <table>\n            <tr\n              v-for=\"(item, i) in moreInfo\"\n              :key=\"i\"\n            >\n              <td\n                v-if=\"item.type !== 'single-error'\"\n                :class=\"{'align-middle': item.type === 'state-badge'}\"\n              >\n                <span class=\"more-info-item-label\">{{ t(item.labelKey) }}:</span>\n              </td>\n              <!-- title template -->\n              <td v-if=\"item.type === 'title-link'\">\n                <span v-if=\"item.valueObj.detailLocation\">\n                  <router-link\n                    :to=\"item.valueObj.detailLocation\"\n                  >\n                    {{ item.valueObj.label }}\n                  </router-link>\n                </span>\n                <span v-else>{{ item.valueObj.label }}</span>\n              </td>\n              <!-- state-badge template -->\n              <td\n                v-else-if=\"item.type === 'state-badge'\"\n                class=\"align-middle\"\n              >\n                <span>\n                  <BadgeState\n                    :color=\"`bg-${item.valueObj.stateColor}`\"\n                    :label=\"item.valueObj.stateLabel\"\n                    class=\"state-bagde\"\n                  />\n                </span>\n              </td>\n              <!-- single-error template -->\n              <td\n                v-if=\"item.type === 'single-error'\"\n                class=\"single-error\"\n                colspan=\"2\"\n              >\n                <p>{{ item.value }}</p>\n              </td>\n              <!-- default template -->\n              <td v-else>\n                {{ item.value }}\n              </td>\n            </tr>\n          </table>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\">\n.chart-container {\n  display: flex;\n  background-color: var(--body-bg);\n  position: relative;\n  border: 1px solid var(--border);\n  border-radius: var(--border-radius);\n  min-height: 100px;\n\n  .loading-container {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    border-radius: var(--border-radius);\n    background-color: var(--body-bg);\n    z-index: 2;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    flex-direction: column;\n\n    i {\n      font-size: 24px;\n    }\n  }\n\n  #tree {\n    width: 70%;\n    height: fit-content;\n\n    svg {\n      margin-top: 3px;\n    }\n\n    .link {\n      stroke: var(--darker);\n    }\n\n    .node {\n      cursor: pointer;\n\n      &.active {\n        .node-hover-layer {\n          display: block;\n        }\n      }\n\n      &.repo.active > circle {\n        transform: scale(1.2);\n      }\n\n      &.bundle.active > circle {\n        transform: scale(1.35);\n      }\n\n      &.bundle-deployment.active > circle {\n        transform: scale(1.6);\n      }\n\n      &.node-default-fill > circle,\n      &.repo > circle {\n        fill: var(--muted);\n      }\n      &:not(.repo).node-success > circle {\n        fill: var(--success);\n      }\n      &:not(.repo).node-info > circle {\n        fill: var(--info);\n      }\n      &:not(.repo).node-warning > circle {\n        fill: var(--warning);\n      }\n      &:not(.repo).node-error > circle {\n        fill: var(--error);\n      }\n\n      .node-hover-layer {\n        stroke: var(--body-bg);\n        stroke-width: 2;\n        display: none;\n      }\n    }\n  }\n\n  .more-info-container {\n    width: 30%;\n    position: relative;\n    border-left: 1px solid var(--border);\n    background-color: var(--body-bg);\n    border-top-right-radius: var(--border-radius);\n    border-bottom-right-radius: var(--border-radius);\n    overflow: hidden;\n\n    .more-info {\n      position: absolute;\n      top: 0;\n      left: 0;\n      right:0;\n      bottom:0;\n      width: 100%;\n      padding: 20px;\n      border-top-right-radius: var(--border-radius);\n      border-bottom-right-radius: var(--border-radius);\n      overflow-y: auto;\n\n      table {\n        td {\n          vertical-align: top;\n          padding-bottom: 10px;\n\n          &.align-middle {\n            vertical-align: middle;\n          }\n        }\n\n        .more-info-item-label {\n          color: var(--darker);\n          margin-right: 8px;\n        }\n\n        .single-error {\n          color: var(--error);\n        }\n\n        p {\n          line-height: 1.5em;\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}