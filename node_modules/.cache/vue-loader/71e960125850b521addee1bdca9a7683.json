{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Alert.vue?vue&type=template&id=5345a3e9", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Alert.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPEJhZGdlU3RhdGUKICAgIDpsYWJlbD0ibWVzc2FnZSIKICAgIDpjb2xvcj0iY29sb3IiCiAgICA6aWNvbj0iaWNvbiIKICAvPgo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Alert.vue"], "names": [], "mappings": ";EAiDE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Alert.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { BadgeState } from '@components/BadgeState';\n\nconst STATUS_CLASS_MAP = {\n  success: {\n    color: 'bg-success',\n    icon:  'icon-checkmark'\n  },\n  warning: {\n    color: 'bg-warning',\n    icon:  'icon-warning'\n  },\n  info: {\n    color: 'bg-info',\n    icon:  'icon-info'\n  },\n  error: {\n    color: 'bg-error',\n    icon:  'icon-error'\n  }\n};\n\nexport default {\n  components: { BadgeState },\n  props:      {\n    status: {\n      type: String,\n      validator(value) {\n        return Object.keys(STATUS_CLASS_MAP).includes(value);\n      },\n      required: true\n    },\n    message: {\n      type:     String,\n      required: true\n    }\n  },\n  computed: {\n    color() {\n      return STATUS_CLASS_MAP[this.status].color;\n    },\n    icon() {\n      return STATUS_CLASS_MAP[this.status].icon;\n    }\n  }\n};\n</script>\n\n<template>\n  <BadgeState\n    :label=\"message\"\n    :color=\"color\"\n    :icon=\"icon\"\n  />\n</template>\n"]}]}