{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdown.vue?vue&type=template&id=cd7e8d90&scoped=true&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdown.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPHYtZHJvcGRvd24KICAgIG5vLWF1dG8tZm9jdXMKICAgIDp0cmlnZ2Vycz0iW10iCiAgICA6c2hvd249ImlzTWVudU9wZW4iCiAgICA6YXV0by1oaWRlPSJmYWxzZSIKICAgIDpjb250YWluZXI9InBvcHBlckNvbnRhaW5lciIKICAgIDpwbGFjZW1lbnQ9Iidib3R0b20tZW5kJyIKICAgIEBhcHBseS1zaG93PSJhcHBseVNob3ciCiAgPgogICAgPHNsb3QgbmFtZT0iZGVmYXVsdCI+CiAgICAgIDwhLS1FbXB0eSBzbG90IGNvbnRlbnQgVHJpZ2dlci0tPgogICAgPC9zbG90PgoKICAgIDx0ZW1wbGF0ZSAjcG9wcGVyPgogICAgICA8ZGl2CiAgICAgICAgcmVmPSJkcm9wZG93blRhcmdldCIKICAgICAgICBjbGFzcz0iZHJvcGRvd25UYXJnZXQiCiAgICAgICAgdGFiaW5kZXg9Ii0xIgogICAgICAgIHJvbGU9Im1lbnUiCiAgICAgICAgYXJpYS1vcmllbnRhdGlvbj0idmVydGljYWwiCiAgICAgICAgZHJvcGRvd24tbWVudS1jb2xsZWN0aW9uCiAgICAgICAgOmFyaWEtbGFiZWw9ImFyaWFMYWJlbCB8fCAnRHJvcGRvd24gTWVudSciCiAgICAgICAgQGtleWRvd249ImhhbmRsZUtleWRvd24iCiAgICAgICAgQGtleWRvd24uZG93bj0ic2V0Rm9jdXMoKSIKICAgICAgPgogICAgICAgIDxzbG90IG5hbWU9ImRyb3Bkb3duQ29sbGVjdGlvbiI+CiAgICAgICAgICA8IS0tRW1wdHkgc2xvdCBjb250ZW50LS0+CiAgICAgICAgPC9zbG90PgogICAgICA8L2Rpdj4KICAgIDwvdGVtcGxhdGU+CiAgPC92LWRyb3Bkb3duPgogIDxkaXYKICAgIHJlZj0icG9wcGVyQ29udGFpbmVyIgogICAgY2xhc3M9InBvcHBlckNvbnRhaW5lciIKICAgIEBrZXlkb3duLnRhYj0ic2hvd01lbnUoZmFsc2UpIgogICAgQGtleWRvd24uZXNjYXBlPSJyZXR1cm5Gb2N1cyIKICA+CiAgICA8IS0tRW1wdHkgY29udGFpbmVyIGZvciBtb3VudGluZyBwb3BwZXIgY29udGVudC0tPgogIDwvZGl2Pgo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdown.vue"], "names": [], "mappings": ";EAyDE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB;IACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B;QACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B;IACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnD,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdown.vue", "sourceRoot": "", "sourcesContent": ["<script setup lang=\"ts\">\n/**\n * Offers a list of choices to the user, such as a set of actions or functions.\n * Opened by activating RcDropdownTrigger.\n *\n * Example:\n *\n *  <rc-dropdown :aria-label=\"t('nav.actionMenu.label')\">\n *    <rc-dropdown-trigger tertiary>\n *      <i class=\"icon icon-actions\" />\n *    </rc-dropdown-trigger>\n *    <template #dropdownCollection>\n *      <rc-dropdown-item @click=\"performAction()\">\n *        Action 1\n *      </rc-dropdown-item>\n *      <rc-dropdown-separator />\n *      <rc-dropdown-item @click=\"performAction()\">\n *        Action 2\n *      </rc-dropdown-item>\n *    </template>\n *  </rc-dropdown>\n */\nimport { ref } from 'vue';\nimport { useClickOutside } from '@shell/composables/useClickOutside';\nimport { useDropdownContext } from '@components/RcDropdown/useDropdownContext';\n\ndefineProps<{\n  ariaLabel?: string\n}>();\n\nconst emit = defineEmits(['update:open']);\n\nconst {\n  isMenuOpen,\n  showMenu,\n  returnFocus,\n  setFocus,\n  provideDropdownContext,\n  registerDropdownCollection,\n  handleKeydown,\n} = useDropdownContext(emit);\n\nprovideDropdownContext();\n\nconst popperContainer = ref(null);\nconst dropdownTarget = ref(null);\n\nuseClickOutside(dropdownTarget, () => showMenu(false));\n\nconst applyShow = () => {\n  registerDropdownCollection(dropdownTarget.value);\n  setFocus();\n};\n\n</script>\n\n<template>\n  <v-dropdown\n    no-auto-focus\n    :triggers=\"[]\"\n    :shown=\"isMenuOpen\"\n    :auto-hide=\"false\"\n    :container=\"popperContainer\"\n    :placement=\"'bottom-end'\"\n    @apply-show=\"applyShow\"\n  >\n    <slot name=\"default\">\n      <!--Empty slot content Trigger-->\n    </slot>\n\n    <template #popper>\n      <div\n        ref=\"dropdownTarget\"\n        class=\"dropdownTarget\"\n        tabindex=\"-1\"\n        role=\"menu\"\n        aria-orientation=\"vertical\"\n        dropdown-menu-collection\n        :aria-label=\"ariaLabel || 'Dropdown Menu'\"\n        @keydown=\"handleKeydown\"\n        @keydown.down=\"setFocus()\"\n      >\n        <slot name=\"dropdownCollection\">\n          <!--Empty slot content-->\n        </slot>\n      </div>\n    </template>\n  </v-dropdown>\n  <div\n    ref=\"popperContainer\"\n    class=\"popperContainer\"\n    @keydown.tab=\"showMenu(false)\"\n    @keydown.escape=\"returnFocus\"\n  >\n    <!--Empty container for mounting popper content-->\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .popperContainer {\n    display: contents;\n    &:deep(.v-popper__popper) {\n\n      .v-popper__wrapper {\n        box-shadow: 0px 6px 18px 0px rgba(0, 0, 0, 0.25), 0px 4px 10px 0px rgba(0, 0, 0, 0.15);\n        border-radius: var(--border-radius-lg);\n\n        .v-popper__arrow-container {\n          display: none;\n        }\n\n        .v-popper__inner {\n          padding: 10px 0 10px 0;\n        }\n      }\n    }\n  }\n\n  .dropdownTarget {\n    &:focus-visible, &:focus {\n      outline: none;\n    }\n  }\n</style>\n"]}]}