{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CopyCode.vue?vue&type=style&index=0&id=3ce13a85&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CopyCode.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLmNvcHkgewogICAgY3Vyc29yOiBwb2ludGVyOwogIH0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CopyCode.vue"], "names": [], "mappings": ";EAsEE,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CopyCode.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { isArray } from '@shell/utils/array';\nimport { copyTextToClipboard } from '@shell/utils/clipboard';\nimport { exceptionToErrorsArray } from '@shell/utils/error';\n\nfunction flatten(node) {\n  if ( node.text ) {\n    return node.text;\n  } else if ( isArray(node) ) {\n    return node.map(flatten).join(' ');\n  } else if ( node.children ) {\n    if ( isArray(node.children) ) {\n      return node.children.map(flatten).join(' ');\n    } else {\n      return node.children;\n    }\n  } else {\n    return '';\n  }\n}\n\nexport default {\n  emits: ['copied', 'error'],\n\n  data() {\n    return { copied: false };\n  },\n\n  methods: {\n    clicked($event) {\n      $event.stopPropagation();\n      $event.preventDefault();\n\n      const content = flatten(this.$slots.default()).trim();\n\n      copyTextToClipboard(content).then(() => {\n        this.copied = true;\n\n        setTimeout(() => {\n          this.copied = false;\n        }, 2000);\n        this.$emit('copied');\n      }).catch((e) => {\n        this.$emit('error', exceptionToErrorsArray(e));\n      });\n    },\n  },\n\n  computed: {\n    tooltip() {\n      const content = this.copied ? 'Copied!' : 'Click to Copy';\n\n      return {\n        content,\n        hideOnTargetClick: false\n      };\n    }\n  }\n};\n</script>\n\n<template>\n  <code\n    v-clean-tooltip=\"tooltip\"\n    class=\"copy\"\n    @click.stop.prevent=\"clicked\"\n  ><slot /></code>\n</template>\n\n<style lang=\"scss\" scoped>\n  .copy {\n    cursor: pointer;\n  }\n</style>\n"]}]}