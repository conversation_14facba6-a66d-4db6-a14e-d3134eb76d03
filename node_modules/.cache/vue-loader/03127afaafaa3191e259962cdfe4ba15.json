{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownMenu.vue?vue&type=template&id=31521b37&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownMenu.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPHJjLWRyb3Bkb3duCiAgICA6YXJpYS1sYWJlbD0iZHJvcGRvd25BcmlhTGFiZWwiCiAgICBAdXBkYXRlOm9wZW49IihlOiBib29sZWFuKSA9PiBlbWl0KCd1cGRhdGU6b3BlbicsIGUpIgogID4KICAgIDxyYy1kcm9wZG93bi10cmlnZ2VyCiAgICAgIDpbYnV0dG9uUm9sZV09InRydWUiCiAgICAgIDpbYnV0dG9uU2l6ZV09InRydWUiCiAgICAgIDpkYXRhLXRlc3RpZD0iZGF0YVRlc3RpZCIKICAgICAgOmFyaWEtbGFiZWw9ImJ1dHRvbkFyaWFMYWJlbCIKICAgID4KICAgICAgPGkgY2xhc3M9Imljb24gaWNvbi1hY3Rpb25zIiAvPgogICAgPC9yYy1kcm9wZG93bi10cmlnZ2VyPgogICAgPHRlbXBsYXRlICNkcm9wZG93bkNvbGxlY3Rpb24+CiAgICAgIDx0ZW1wbGF0ZQogICAgICAgIHYtZm9yPSIoYSkgaW4gb3B0aW9ucyIKICAgICAgICA6a2V5PSJhLmxhYmVsIgogICAgICA+CiAgICAgICAgPHJjLWRyb3Bkb3duLWl0ZW0KICAgICAgICAgIHYtaWY9IiFhLmRpdmlkZXIiCiAgICAgICAgICBAY2xpY2s9IihlOiBNb3VzZUV2ZW50KSA9PiBlbWl0KCdzZWxlY3QnLCBlLCBhKSIKICAgICAgICA+CiAgICAgICAgICA8dGVtcGxhdGUgI2JlZm9yZT4KICAgICAgICAgICAgPEljb25PclN2ZwogICAgICAgICAgICAgIHYtaWY9ImEuaWNvbiB8fCBhLnN2ZyIKICAgICAgICAgICAgICA6aWNvbj0iYS5pY29uIgogICAgICAgICAgICAgIDpzcmM9ImEuc3ZnIgogICAgICAgICAgICAgIGNsYXNzPSJpY29uIgogICAgICAgICAgICAgIGNvbG9yPSJoZWFkZXIiCiAgICAgICAgICAgIC8+CiAgICAgICAgICA8L3RlbXBsYXRlPgogICAgICAgICAge3sgYS5sYWJlbCB9fQogICAgICAgIDwvcmMtZHJvcGRvd24taXRlbT4KICAgICAgICA8cmMtZHJvcGRvd24tc2VwYXJhdG9yCiAgICAgICAgICB2LWVsc2UKICAgICAgICAvPgogICAgICA8L3RlbXBsYXRlPgogICAgICA8cmMtZHJvcGRvd24taXRlbQogICAgICAgIHYtaWY9IiFoYXNPcHRpb25zKG9wdGlvbnMpIgogICAgICAgIGRpc2FibGVkCiAgICAgID4KICAgICAgICBObyBhY3Rpb25zIGF2YWlsYWJsZQogICAgICA8L3JjLWRyb3Bkb3duLWl0ZW0+CiAgICA8L3RlbXBsYXRlPgogIDwvcmMtZHJvcGRvd24+Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownMenu.vue"], "names": [], "mappings": ";EAuBE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACtD;IACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B;MACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf;QACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT;QACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownMenu.vue", "sourceRoot": "", "sourcesContent": ["<script setup lang=\"ts\">\nimport {\n  Rc<PERSON><PERSON>down,\n  Rc<PERSON>ropdownItem,\n  RcDropdownSeparator,\n  RcDropdownTrigger\n} from '@components/RcDropdown';\nimport { RcDropdownMenuComponentProps, DropdownOption } from './types';\nimport IconOrSvg from '@shell/components/IconOrSvg';\n\nwithDefaults(defineProps<RcDropdownMenuComponentProps>(), {\n  buttonRole: 'primary',\n  buttonSize: undefined,\n});\n\nconst emit = defineEmits(['update:open', 'select']);\n\nconst hasOptions = (options: DropdownOption[]) => {\n  return options.length !== undefined ? options.length : Object.keys(options).length > 0;\n};\n</script>\n\n<template>\n  <rc-dropdown\n    :aria-label=\"dropdownAriaLabel\"\n    @update:open=\"(e: boolean) => emit('update:open', e)\"\n  >\n    <rc-dropdown-trigger\n      :[buttonRole]=\"true\"\n      :[buttonSize]=\"true\"\n      :data-testid=\"dataTestid\"\n      :aria-label=\"buttonAriaLabel\"\n    >\n      <i class=\"icon icon-actions\" />\n    </rc-dropdown-trigger>\n    <template #dropdownCollection>\n      <template\n        v-for=\"(a) in options\"\n        :key=\"a.label\"\n      >\n        <rc-dropdown-item\n          v-if=\"!a.divider\"\n          @click=\"(e: MouseEvent) => emit('select', e, a)\"\n        >\n          <template #before>\n            <IconOrSvg\n              v-if=\"a.icon || a.svg\"\n              :icon=\"a.icon\"\n              :src=\"a.svg\"\n              class=\"icon\"\n              color=\"header\"\n            />\n          </template>\n          {{ a.label }}\n        </rc-dropdown-item>\n        <rc-dropdown-separator\n          v-else\n        />\n      </template>\n      <rc-dropdown-item\n        v-if=\"!hasOptions(options)\"\n        disabled\n      >\n        No actions available\n      </rc-dropdown-item>\n    </template>\n  </rc-dropdown>\n</template>\n"]}]}