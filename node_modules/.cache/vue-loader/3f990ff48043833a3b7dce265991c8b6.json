{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GrowlManager.vue?vue&type=style&index=0&id=3e7a73b7&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GrowlManager.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLmdyb3dsLWNvbnRhaW5lciB7CiAgICB6LWluZGV4OiAxMDAwOwogICAgcG9zaXRpb246IGFic29sdXRlOwogICAgdG9wOiAwOwogICAgcmlnaHQ6IDA7CiAgICB3aWR0aDogMTAwJTsKCiAgICBAbWVkaWEgb25seSBzY3JlZW4gYW5kIChtaW4td2lkdGg6IG1hcC1nZXQoJGJyZWFrcG9pbnRzLCAnLS12aWV3cG9ydC03JykpIHsKICAgICAgd2lkdGg6IDQyMHB4OwogICAgfQogIH0KCiAgLmdyb3dsLWxpc3QgewogICAgbWF4LWhlaWdodDogY2FsYygxMDB2aCAtIDUwcHgpOwogICAgb3ZlcmZsb3c6IGhpZGRlbjsKICB9CgogIC5ncm93bCB7CiAgICBib3JkZXItcmFkaXVzOiB2YXIoLS1ib3JkZXItcmFkaXVzKTsKICAgIG1hcmdpbjogMTBweDsKICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsKICAgIHdvcmQtYnJlYWs6IGJyZWFrLWFsbDsKICAgIGJveC1zaGFkb3c6IDAgM3B4IDVweCAwcHggdmFyKC0tc2hhZG93KTsKCiAgICAkZ3Jvd2wtaWNvbi1zaXplOiAyMHB4OwoKICAgIC5pY29uLWNvbnRhaW5lciB7CiAgICAgIGFsaWduLXNlbGY6IGNlbnRlcjsKICAgICAgZmxleC1iYXNpczogMTAlOwogICAgICBwYWRkaW5nOiAxMHB4IDIwcHggMTBweCAxMHB4OwogICAgICBpIHsKICAgICAgICBmb250LXNpemU6ICRncm93bC1pY29uLXNpemU7CiAgICAgICAgd2lkdGg6ICRncm93bC1pY29uLXNpemU7CiAgICAgICAgaGVpZ2h0OiAkZ3Jvd2wtaWNvbi1zaXplOwogICAgICB9CiAgICB9CgogICAgLmdyb3dsLW1lc3NhZ2UgewogICAgICBkaXNwbGF5OiBmbGV4OwoKICAgICAgJi5ncm93bC1jZW50ZXIgewogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgIH0KCiAgICAgIC5ncm93bC10ZXh0IHsKICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgICAgICAgZmxleC1iYXNpczogOTAlOwogICAgICAgIHBhZGRpbmc6IDEwcHggMTBweCAxMHB4IDA7CiAgICAgICAgd29yZC1icmVhazogYnJlYWstd29yZDsKICAgICAgICB3aGl0ZS1zcGFjZTogbm9ybWFsOwoKICAgICAgICAuY2xvc2UgewogICAgICAgICAgcG9zaXRpb246IGFic29sdXRlOwogICAgICAgICAgdG9wOiAxMnB4OwogICAgICAgICAgcmlnaHQ6IDEwcHg7CiAgICAgICAgfQogICAgICAgIC5ncm93bC10ZXh0LXRpdGxlIHsKICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDsKICAgICAgICB9CgogICAgICAgID4gUCB7CiAgICAgICAgICBwYWRkaW5nLXRvcDogMnB4OwoKICAgICAgICAgICYuaGFzLXRpdGxlIHsKICAgICAgICAgICAgbWFyZ2luLXRvcDogNXB4OwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgfQogIH0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GrowlManager.vue"], "names": [], "mappings": ";EA6IE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,EAAE,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACxE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACd;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC5B,EAAE;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB;;QAEA,EAAE,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;UAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACjB;QACF;MACF;IACF;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GrowlManager.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapState } from 'vuex';\n\nexport default {\n  data() {\n    return { autoRemoveTimer: null };\n  },\n\n  computed: {\n    ...mapState('growl', ['stack']),\n\n    shouldRun() {\n      return this.stack.length && this.stack.find((x) => x.timeout > 0);\n    }\n  },\n\n  watch: {\n    stack() {\n      if ( this.shouldRun ) {\n        this.startAutoRemove();\n      } else {\n        this.stopAutoRemove();\n      }\n    },\n  },\n\n  methods: {\n    close(growl) {\n      this.$store.dispatch('growl/remove', growl.id);\n    },\n\n    closeAll() {\n      this.$store.dispatch('growl/clear');\n    },\n\n    closeExpired() {\n      const now = new Date().getTime();\n\n      for ( const growl of this.stack ) {\n        if ( !growl.timeout ) {\n          continue;\n        }\n\n        if ( growl.started + growl.timeout < now ) {\n          this.close(growl);\n        }\n      }\n    },\n\n    startAutoRemove() {\n      if ( this.autoRemoveTimer || !this.shouldRun ) {\n        return false;\n      }\n\n      this.autoRemoveTimer = setInterval(() => {\n        this.closeExpired();\n      }, 1000);\n\n      return true;\n    },\n\n    stopAutoRemove() {\n      if ( this.autoRemoveTimer ) {\n        clearInterval(this.autoRemoveTimer);\n        this.autoRemoveTimer = null;\n      }\n    },\n\n    mouseEnter() {\n      this.stopAutoRemove();\n    },\n\n    mouseLeave() {\n      this.startAutoRemove();\n    },\n  }\n};\n\n</script>\n\n<template>\n  <div\n    class=\"growl-container\"\n    @mouseenter=\"mouseEnter\"\n    @mouseleave=\"mouseLeave\"\n  >\n    <div class=\"growl-list\">\n      <div\n        v-for=\"(growl, idx) in stack\"\n        :key=\"growl.id\"\n        role=\"alert\"\n        :aria-labelledby=\"`growl-title-${ growl.id }`\"\n        :aria-describedby=\"`growl-message-${ growl.id }`\"\n        :data-testid=\"`growl-list-item-${idx}`\"\n        :class=\"{'growl': true, ['bg-'+growl.color]: true}\"\n      >\n        <div\n          class=\"growl-message\"\n          :class=\"{'growl-center': !growl.message}\"\n        >\n          <div class=\"icon-container\">\n            <i :class=\"{icon: true, ['icon-'+growl.icon]: true}\" />\n          </div>\n          <div class=\"growl-text\">\n            <i\n              class=\"close hand icon icon-close\"\n              @click=\"close(growl)\"\n            />\n            <div\n              :id=\"`growl-title-${ growl.id }`\"\n              class=\"growl-text-title\"\n            >\n              {{ growl.title }}\n            </div>\n            <p\n              v-if=\"growl.message\"\n              :id=\"`growl-message-${ growl.id }`\"\n              :class=\"{ 'has-title': !!growl.title }\"\n            >\n              {{ growl.message }}\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div\n      v-if=\"stack.length > 1\"\n      class=\"text-right mr-10 mt-10\"\n    >\n      <button\n        type=\"button\"\n        class=\"btn btn-sm role-primary\"\n        @click=\"closeAll()\"\n      >\n        {{ t('growl.clearAll') }}\n      </button>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .growl-container {\n    z-index: 1000;\n    position: absolute;\n    top: 0;\n    right: 0;\n    width: 100%;\n\n    @media only screen and (min-width: map-get($breakpoints, '--viewport-7')) {\n      width: 420px;\n    }\n  }\n\n  .growl-list {\n    max-height: calc(100vh - 50px);\n    overflow: hidden;\n  }\n\n  .growl {\n    border-radius: var(--border-radius);\n    margin: 10px;\n    position: relative;\n    word-break: break-all;\n    box-shadow: 0 3px 5px 0px var(--shadow);\n\n    $growl-icon-size: 20px;\n\n    .icon-container {\n      align-self: center;\n      flex-basis: 10%;\n      padding: 10px 20px 10px 10px;\n      i {\n        font-size: $growl-icon-size;\n        width: $growl-icon-size;\n        height: $growl-icon-size;\n      }\n    }\n\n    .growl-message {\n      display: flex;\n\n      &.growl-center {\n        align-items: center;\n      }\n\n      .growl-text {\n        position: relative;\n        flex-basis: 90%;\n        padding: 10px 10px 10px 0;\n        word-break: break-word;\n        white-space: normal;\n\n        .close {\n          position: absolute;\n          top: 12px;\n          right: 10px;\n        }\n        .growl-text-title {\n          font-size: 16px;\n        }\n\n        > P {\n          padding-top: 2px;\n\n          &.has-title {\n            margin-top: 5px;\n          }\n        }\n      }\n    }\n  }\n</style>\n"]}]}