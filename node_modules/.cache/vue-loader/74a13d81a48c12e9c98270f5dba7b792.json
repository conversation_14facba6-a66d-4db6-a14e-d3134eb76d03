{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/LabeledInput/LabeledInput.vue?vue&type=style&index=0&id=1968f36b&scoped=true&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/LabeledInput/LabeledInput.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5sYWJlbGVkLWlucHV0LnZpZXcgewogIGlucHV0IHsKICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOwogIH0KfQoKLmhpZGVBcnJvd3MgewogIC8qIEhpZGUgYXJyb3dzIG9uIG51bWJlciBpbnB1dCB3aGVuIGl0IG92ZXJsYXBzIHdpdGggdGhlIHVuaXQgKi8KICAvKiBDaHJvbWUsIFNhZmFyaSwgRWRnZSwgT3BlcmEgKi8KICBpbnB1dDo6LXdlYmtpdC1vdXRlci1zcGluLWJ1dHRvbiwKICBpbnB1dDo6LXdlYmtpdC1pbm5lci1zcGluLWJ1dHRvbiB7CiAgICAtd2Via2l0LWFwcGVhcmFuY2U6IG5vbmU7CiAgICBtYXJnaW46IDA7CiAgfQoKICAvKiBGaXJlZm94ICovCiAgaW5wdXRbdHlwZT1udW1iZXJdIHsKICAgIC1tb3otYXBwZWFyYW5jZTogdGV4dGZpZWxkOwogIH0KfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/LabeledInput/LabeledInput.vue"], "names": [], "mappings": ";AAgcA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAClB,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACV,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;EAC/D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACX;;EAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/LabeledInput/LabeledInput.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent, inject } from 'vue';\nimport TextAreaAutoGrow from '@components/Form/TextArea/TextAreaAutoGrow.vue';\nimport LabeledTooltip from '@components/LabeledTooltip/LabeledTooltip.vue';\nimport { escapeHtml, generateRandomAlphaString } from '@shell/utils/string';\nimport cronstrue from 'cronstrue';\nimport { isValidCron } from 'cron-validator';\nimport { debounce } from 'lodash';\nimport { useLabeledFormElement, labeledFormElementProps } from '@shell/composables/useLabeledFormElement';\nimport { useCompactInput } from '@shell/composables/useCompactInput';\n\ninterface NonReactiveProps {\n  onInput: (event: Event) => void | ((event: Event) => void);\n}\n\nconst provideProps: NonReactiveProps = {\n  onInput() {\n    // noop\n  },\n};\n\nexport default defineComponent({\n  components: { LabeledTooltip, TextAreaAutoGrow },\n\n  inheritAttrs: false,\n\n  props: {\n    ...labeledFormElementProps,\n    /**\n     * The type of the Labeled Input.\n     * @values text, cron, multiline, multiline-password\n     */\n    type: {\n      type:    String,\n      default: 'text'\n    },\n\n    /**\n     * The status class of the Labeled Input and tooltip.\n     * @values info, success, warning, error\n     */\n    status: {\n      type:    String,\n      default: null\n    },\n\n    /**\n     * The sub-label for the Labeled Input.\n     */\n    subLabel: {\n      type:    String,\n      default: null\n    },\n\n    /**\n     * The tooltip to display for the Labeled Input.\n     */\n    tooltip: {\n      default: null,\n      type:    [String, Object]\n    },\n\n    /**\n     * Renders the tooltip when hovering the cursor over the Labeled Input.\n     */\n    hoverTooltip: {\n      type:    Boolean,\n      default: true\n    },\n\n    /**\n     * Disables the password manager prompt to save the contents of the Labeled\n     * Input.\n     */\n    ignorePasswordManagers: {\n      default: false,\n      type:    Boolean\n    },\n\n    /**\n     * The max length of the Labeled Input.\n     */\n    maxlength: {\n      type:    Number,\n      default: null\n    },\n\n    /**\n     * Hides arrows on the Labeled Input.\n     * @deprecated This doesn't appear to be in use for Labeled Input.\n     */\n    hideArrows: {\n      type:    Boolean,\n      default: false\n    },\n\n    /**\n     * Optionally delay on input while typing.\n     */\n    delay: {\n      type:    Number,\n      default: 0\n    },\n\n    class: {\n      type:    String,\n      default: ''\n    },\n\n    /**\n     * Optionally use this to comply with a11y IF there's no label\n     * associated with the input\n     */\n    ariaLabel: {\n      type:    String,\n      default: ''\n    }\n  },\n\n  emits: ['change', 'update:value', 'blur', 'update:validation'],\n\n  setup(props, { emit }) {\n    const {\n      focused,\n      onFocusLabeled,\n      onBlurLabeled,\n      isDisabled,\n      validationMessage,\n      requiredField\n    } = useLabeledFormElement(props, emit);\n    const { isCompact } = useCompactInput(props);\n\n    const onInput = inject('onInput', provideProps.onInput);\n\n    return {\n      focused,\n      onFocusLabeled,\n      onBlurLabeled,\n      onInput,\n      isDisabled,\n      validationMessage,\n      requiredField,\n      isCompact,\n    };\n  },\n\n  data() {\n    return {\n      updated:          false,\n      validationErrors: '',\n      inputId:          `input-${ generateRandomAlphaString(12) }`,\n      describedById:    `described-by-${ generateRandomAlphaString(12) }`\n    };\n  },\n\n  computed: {\n    /**\n     * Determines if the Labeled Input should display a label.\n     */\n    hasLabel(): boolean {\n      return this.isCompact ? false : !!this.label || !!this.labelKey || !!this.$slots.label;\n    },\n\n    /**\n     * Determines if the Labeled Input should display a tooltip.\n     */\n    hasTooltip(): boolean {\n      return !!this.tooltip || !!this.tooltipKey;\n    },\n\n    tooltipValue(): string | Record<string, unknown> | undefined {\n      if (this.hasTooltip) {\n        return this.tooltipKey ? this.t(this.tooltipKey) : this.tooltip;\n      }\n\n      return undefined;\n    },\n\n    /**\n     * Determines if the Labeled Input makes use of the suffix slot.\n     */\n    hasSuffix(): boolean {\n      return !!this.$slots.suffix;\n    },\n\n    /**\n     * Determines if the Labeled Input should display a cron hint.\n     */\n    cronHint(): string | undefined {\n      if (this.type !== 'cron' || !this.value) {\n        return;\n      }\n\n      // TODO - #13202: This is required due use of 2 libraries and 3 different libraries through the code.\n      const predefined = [\n        '@yearly',\n        '@annually',\n        '@monthly',\n        '@weekly',\n        '@daily',\n        '@midnight',\n        '@hourly'\n      ];\n      const isPredefined = predefined.includes(this.value as string);\n\n      // refer https://github.com/GuillaumeRochat/cron-validator#readme\n      if (!isPredefined && !isValidCron(this.value as string, {\n        alias:              true,\n        allowBlankDay:      true,\n        allowSevenAsSunday: true,\n      })) {\n        return this.t('generic.invalidCron');\n      }\n\n      try {\n        const hint = cronstrue.toString(this.value as string || '', { verbose: true });\n\n        return hint;\n      } catch (e) {\n        return this.t('generic.invalidCron');\n      }\n    },\n\n    /**\n     * The placeholder value for the Labeled Input.\n     */\n    _placeholder(): string {\n      if (this.placeholder) {\n        return this.placeholder.toString();\n      }\n      if (this.placeholderKey) {\n        return this.t(this.placeholderKey);\n      }\n\n      return '';\n    },\n\n    /**\n     * The max length for the Labeled Input.\n     */\n    _maxlength(): number | undefined {\n      if (this.type === 'text' && this.maxlength) {\n        return this.maxlength;\n      }\n\n      return undefined;\n    },\n\n    className() {\n      return this.class;\n    }\n  },\n\n  mounted() {\n    const id = this.$attrs?.id as string | undefined;\n\n    if (id) {\n      this.inputId = id;\n    }\n  },\n\n  created() {\n    /**\n     * Determines if the Labeled Input @input event should be debounced.\n    */\n    this.onInput = this.delay ? debounce(this.delayInput, this.delay) : this.delayInput;\n  },\n\n  methods: {\n    /**\n     * Attempts to give the Labeled Input focus.\n     */\n    focus(): void {\n      const comp = this.$refs.value as HTMLInputElement;\n\n      if (comp) {\n        comp.focus();\n      }\n    },\n\n    /**\n     * Attempts to select the Labeled Input.\n     * @deprecated\n     */\n    select(): void {\n      const comp = this.$refs.value as HTMLInputElement;\n\n      if (comp) {\n        comp.select();\n      }\n    },\n\n    /**\n     * Emit on input change\n     */\n    onChange(event: Event): void {\n      this.$emit('change', event);\n    },\n\n    /**\n     * Emit on input with delay. Note: Arrow function is avoided due context\n     * binding.\n     *\n     * NOTE: In multiline, TextAreaAutoGrow emits a string with the value\n     * https://github.com/rancher/dashboard/issues/10249\n     */\n    delayInput(val: string | Event): void {\n      const value = typeof val === 'string' ? val : (val?.target as HTMLInputElement)?.value;\n\n      this.$emit('update:value', value);\n    },\n\n    /**\n     * Handles the behavior of the Labeled Input when given focus.\n     * @see labeled-form-element.ts mixin for onFocusLabeled()\n     */\n    onFocus(): void {\n      this.onFocusLabeled();\n    },\n\n    /**\n     * Handles the behavior of the Labeled Input when blurred and emits the blur\n     * event.\n     * @see labeled-form-element.ts mixin for onBlurLabeled()\n     */\n    onBlur(event: string | FocusEvent): void {\n      this.$emit('blur', event);\n      this.onBlurLabeled();\n    },\n\n    escapeHtml\n  }\n});\n</script>\n\n<template>\n  <div\n    :class=\"{\n      'labeled-input': true,\n      focused,\n      [mode]: true,\n      disabled: isDisabled,\n      [status]: status,\n      suffix: hasSuffix,\n      'v-popper--has-tooltip': hasTooltip,\n      'compact-input': isCompact,\n      hideArrows,\n      [className]: true\n    }\"\n  >\n    <slot name=\"label\">\n      <label\n        v-if=\"hasLabel\"\n        :for=\"inputId\"\n      >\n        <t\n          v-if=\"labelKey\"\n          :k=\"labelKey\"\n        />\n        <template v-else-if=\"label\">{{ label }}</template>\n\n        <span\n          v-if=\"requiredField\"\n          class=\"required\"\n        >*</span>\n      </label>\n    </slot>\n\n    <slot name=\"prefix\" />\n\n    <slot name=\"field\">\n      <TextAreaAutoGrow\n        v-if=\"type === 'multiline' || type === 'multiline-password'\"\n        :id=\"inputId\"\n        ref=\"value\"\n        v-bind=\"$attrs\"\n        v-stripped-aria-label=\"!hasLabel && ariaLabel ? ariaLabel : undefined\"\n        :maxlength=\"_maxlength\"\n        :disabled=\"isDisabled\"\n        :value=\"value || ''\"\n        :placeholder=\"_placeholder\"\n        autocapitalize=\"off\"\n        :class=\"{ conceal: type === 'multiline-password' }\"\n        :aria-describedby=\"cronHint || subLabel ? describedById : undefined\"\n        @update:value=\"onInput\"\n        @focus=\"onFocus\"\n        @blur=\"onBlur\"\n      />\n      <input\n        v-else\n        :id=\"inputId\"\n        ref=\"value\"\n        v-stripped-aria-label=\"!hasLabel && ariaLabel ? ariaLabel : undefined\"\n        role=\"textbox\"\n        :class=\"{ 'no-label': !hasLabel }\"\n        v-bind=\"$attrs\"\n        :maxlength=\"_maxlength\"\n        :disabled=\"isDisabled\"\n        :type=\"type === 'cron' ? 'text' : type\"\n        :value=\"value\"\n        :placeholder=\"_placeholder\"\n        autocomplete=\"off\"\n        autocapitalize=\"off\"\n        :data-lpignore=\"ignorePasswordManagers\"\n        :aria-describedby=\"cronHint || subLabel ? describedById : undefined\"\n        @input=\"onInput\"\n        @focus=\"onFocus\"\n        @blur=\"onBlur\"\n        @change=\"onChange\"\n      >\n    </slot>\n\n    <slot name=\"suffix\" />\n    <!-- informational tooltip about field -->\n    <LabeledTooltip\n      v-if=\"hasTooltip\"\n      :hover=\"hoverTooltip\"\n      :value=\"tooltipValue\"\n      :status=\"status\"\n    />\n    <!-- validation tooltip -->\n    <LabeledTooltip\n      v-if=\"!!validationMessage\"\n      :hover=\"hoverTooltip\"\n      :value=\"validationMessage\"\n    />\n    <div\n      v-if=\"cronHint || subLabel\"\n      class=\"sub-label\"\n      data-testid=\"sub-label\"\n    >\n      <div\n        v-if=\"cronHint\"\n        :id=\"describedById\"\n        role=\"alert\"\n        :aria-label=\"cronHint\"\n      >\n        {{ cronHint }}\n      </div>\n      <div\n        v-else-if=\"subLabel\"\n        :id=\"describedById\"\n        v-clean-html=\"subLabel\"\n      />\n    </div>\n  </div>\n</template>\n<style scoped lang=\"scss\">\n.labeled-input.view {\n  input {\n    text-overflow: ellipsis;\n  }\n}\n\n.hideArrows {\n  /* Hide arrows on number input when it overlaps with the unit */\n  /* Chrome, Safari, Edge, Opera */\n  input::-webkit-outer-spin-button,\n  input::-webkit-inner-spin-button {\n    -webkit-appearance: none;\n    margin: 0;\n  }\n\n  /* Firefox */\n  input[type=number] {\n    -moz-appearance: textfield;\n  }\n}\n</style>\n<style>\n.validation-message {\n  padding: 5px;\n  position: absolute;\n  bottom: -35px;\n}\n</style>\n"]}]}