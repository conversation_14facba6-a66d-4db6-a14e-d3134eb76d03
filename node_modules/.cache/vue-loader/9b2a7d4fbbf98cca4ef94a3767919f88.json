{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/NodeGroup.vue?vue&type=style&index=0&id=151d0a74&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/NodeGroup.vue", "mtime": 1754992537319}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci51c2VyLWRhdGF7CiAgJj5idXR0b257CiAgICBmbG9hdDogcmlnaHQ7CiAgfQp9CgoudXBncmFkZS12ZXJzaW9uIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7Cn0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/NodeGroup.vue"], "names": [], "mappings": ";AA80BA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACd;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/NodeGroup.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent, PropType } from 'vue';\nimport { mapGetters, Store } from 'vuex';\nimport debounce from 'lodash/debounce';\n\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\nimport { randomStr } from '@shell/utils/string';\nimport { isEmpty } from '@shell/utils/object';\n\nimport LabeledSelect from '@shell/components/form/LabeledSelect.vue';\nimport LabeledInput from '@components/Form/LabeledInput/LabeledInput.vue';\nimport Checkbox from '@components/Form/Checkbox/Checkbox.vue';\nimport KeyValue from '@shell/components/form/KeyValue.vue';\nimport Banner from '@components/Banner/Banner.vue';\nimport UnitInput from '@shell/components/form/UnitInput.vue';\nimport FileSelector from '@shell/components/form/FileSelector.vue';\n\nimport { MANAGED_TEMPLATE_PREFIX, parseTags } from '../../util/aws';\nimport { AWS } from '../../types';\nimport { DEFAULT_NODE_GROUP_CONFIG } from './CruEKS.vue';\n\n// map between fields in rancher eksConfig and amazon launch templates\nconst launchTemplateFieldMapping: {[key: string]: string} = {\n  imageId:      'ImageId',\n  userData:     'UserData',\n  instanceType: 'InstanceType',\n  ec2SshKey:    '',\n  resourceTags: 'TagSpecifications',\n  diskSize:     'BlockDeviceMappings'\n};\n\nconst DEFAULT_USER_DATA =\n`MIME-Version: 1.0\nContent-Type: multipart/mixed; boundary=\"==MYBOUNDARY==\"\n\n--==MYBOUNDARY==\nContent-Type: text/x-shellscript; charset=\"us-ascii\"\n\n#!/bin/bash\necho \"Running custom user data script\"\n\n--==MYBOUNDARY==--\\\\`;\n\nexport default defineComponent({\n  name: 'EKSNodePool',\n\n  emits: ['update:instanceType', 'update:spotInstanceTypes', 'update:ec2SshKey', 'update:launchTemplate', 'update:nodeRole', 'update:nodeRole', 'update:version', 'update:poolIsUpgrading', 'error', 'update:resourceTags', 'update:diskSize', 'update:nodegroupName', 'update:desiredSize', 'update:minSize', 'update:maxSize', 'update:labels', 'update:tags', 'update:imageId', 'update:gpu', 'update:requestSpotInstances', 'update:userData', 'update:ec2SshKey'],\n\n  components: {\n    LabeledInput,\n    LabeledSelect,\n    KeyValue,\n    Banner,\n    Checkbox,\n    UnitInput,\n    FileSelector\n  },\n\n  props: {\n    nodeRole: {\n      type:    String,\n      default: ''\n    },\n    resourceTags: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    requestSpotInstances: {\n      type:    Boolean,\n      default: false\n    },\n    spotInstanceTypes: {\n      type:    Array,\n      default: () => []\n    },\n    labels: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    tags: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    gpu: {\n      type:    Boolean,\n      default: false\n    },\n    userData: {\n      type:    String,\n      default: ''\n    },\n    instanceType: {\n      type:    String,\n      default: ''\n    },\n    imageId: {\n      type:    [String, null],\n      default: ''\n    },\n    desiredSize: {\n      type:    [Number, String],\n      default: null\n    },\n    minSize: {\n      type:    [Number, String],\n      default: null\n    },\n    maxSize: {\n      type:    [Number, String],\n      default: null\n    },\n    diskSize: {\n      type:    Number,\n      default: null\n    },\n    ec2SshKey: {\n      type:    String,\n      default: ''\n    },\n    nodegroupName: {\n      type:    String,\n      default: ''\n    },\n    region: {\n      type:    String,\n      default: ''\n    },\n    amazonCredentialSecret: {\n      type:    String,\n      default: ''\n    },\n\n    launchTemplate: {\n      type:    Object,\n      default: () => {}\n    },\n\n    version: {\n      type:    String,\n      default: ''\n    },\n\n    clusterVersion: {\n      type:    String,\n      default: ''\n    },\n\n    originalClusterVersion: {\n      type:    String,\n      default: ''\n    },\n\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n\n    ec2Roles: {\n      type:    Array as PropType<AWS.IamRole[]>,\n      default: () => []\n    },\n\n    isNewOrUnprovisioned: {\n      type:    Boolean,\n      default: true\n    },\n\n    poolIsNew: {\n      type:    Boolean,\n      default: false\n    },\n\n    instanceTypeOptions: {\n      type:    Array,\n      default: () => []\n    },\n\n    spotInstanceTypeOptions: {\n      type:    Array,\n      default: () => []\n    },\n\n    launchTemplates: {\n      type:    Array as PropType<AWS.LaunchTemplate[]>,\n      default: () => []\n    },\n\n    sshKeyPairs: {\n      type:    Array as PropType<string[]>,\n      default: () => []\n    },\n\n    normanCluster: {\n      type:    Object,\n      default: null\n    },\n\n    poolIsUpgrading: {\n      type:    Boolean,\n      default: false\n    },\n\n    loadingInstanceTypes: {\n      type:    Boolean,\n      default: false\n    },\n\n    loadingRoles: {\n      type:    Boolean,\n      default: false\n    },\n\n    loadingLaunchTemplates: {\n      type:    Boolean,\n      default: false\n    },\n\n    loadingSshKeyPairs: {\n      type:    Boolean,\n      default: false\n    },\n\n    rules: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  created() {\n    this.debouncedSetValuesFromTemplate = debounce(this.setValuesFromTemplate, 500);\n  },\n\n  data() {\n    const store = this.$store as Store<any>;\n    const t = store.getters['i18n/t'];\n\n    return {\n      originalNodeVersion:   this.version,\n      defaultTemplateOption: { LaunchTemplateName: t('eks.defaultCreateOne') } as AWS.LaunchTemplate,\n\n      defaultNodeRoleOption:          { RoleName: t('eks.defaultCreateOne') },\n      loadingSelectedVersion:         false,\n      // once a specific lt has been selected, an additional query is made to get full information on every version of it\n      selectedLaunchTemplateInfo:     {} as AWS.LaunchTemplateDetail,\n      debouncedSetValuesFromTemplate: null as Function | null,\n      // the keyvalue component needs to be re-rendered if the value prop is updated by parent component when as-map=true\n      // TODO nb file an issue\n      resourceTagKey:                 randomStr()\n    };\n  },\n\n  watch: {\n    selectedLaunchTemplate: {\n      handler(neu) {\n        if (neu && neu.LaunchTemplateId && this.amazonCredentialSecret) {\n          this.fetchLaunchTemplateVersionInfo(this.selectedLaunchTemplate);\n        }\n      },\n      immediate: true\n    },\n\n    amazonCredentialSecret: {\n      handler() {\n        this.fetchLaunchTemplateVersionInfo(this.selectedLaunchTemplate);\n      },\n      immediate: true\n    },\n\n    'selectedVersionData'(neu = {}, old = {}) {\n      this.loadingSelectedVersion = true;\n      if (this.debouncedSetValuesFromTemplate) {\n        this.debouncedSetValuesFromTemplate(neu, old);\n      }\n    },\n\n    'requestSpotInstances'(neu) {\n      if (neu && !this.templateValue('instanceType')) {\n        this.$emit('update:instanceType', null);\n      } else {\n        this.$emit('update:spotInstanceTypes', null);\n      }\n    },\n\n    sshKeyPairs: {\n      handler(neu) {\n        if (!neu.includes(this.ec2SshKey)) {\n          this.$emit('update:ec2SshKey', '');\n        }\n      },\n      deep: true\n    }\n  },\n\n  computed: {\n    ...mapGetters({ t: 'i18n/t' }),\n\n    rancherTemplate() {\n      const eksStatus = this.normanCluster?.eksStatus || {};\n\n      return eksStatus.managedLaunchTemplateID;\n    },\n\n    hasRancherLaunchTemplate() {\n      const eksStatus = this.normanCluster?.eksStatus || {};\n      const nodegroupName = this.nodegroupName;\n      const nodeGroupTemplateVersion = (eksStatus?.managedLaunchTemplateVersions || {})[nodegroupName];\n\n      return isEmpty(this.launchTemplate) && !isEmpty(eksStatus.managedLaunchTemplateID) && !isEmpty(nodeGroupTemplateVersion);\n    },\n\n    hasUserLaunchTemplate() {\n      const { launchTemplate = {} } = this;\n\n      return !!launchTemplate?.id && !!launchTemplate?.version;\n    },\n\n    hasNoLaunchTemplate() {\n      return !this.hasRancherLaunchTemplate && !this.hasUserLaunchTemplate;\n    },\n\n    launchTemplateOptions(): AWS.LaunchTemplate[] {\n      return [this.defaultTemplateOption, ...this.launchTemplates.filter((template) => !(template?.LaunchTemplateName || '').startsWith(MANAGED_TEMPLATE_PREFIX))];\n    },\n\n    selectedLaunchTemplate: {\n      get(): AWS.LaunchTemplate {\n        if (this.hasRancherLaunchTemplate) {\n          return { LaunchTemplateId: this.rancherTemplate, LaunchTemplateName: this.t('eks.nodeGroups.launchTemplate.rancherManaged', { name: this.rancherTemplate }) };\n        }\n        const id = this.launchTemplate?.id;\n\n        return this.launchTemplateOptions.find((lt: AWS.LaunchTemplate) => lt.LaunchTemplateId && lt.LaunchTemplateId === id) || this.defaultTemplateOption;\n      },\n      set(neu: AWS.LaunchTemplate) {\n        if (neu.LaunchTemplateName === this.defaultTemplateOption.LaunchTemplateName) {\n          this.$emit('update:launchTemplate', {});\n\n          return;\n        }\n        const name = neu.LaunchTemplateName;\n        const id = neu.LaunchTemplateId;\n        const version = neu.DefaultVersionNumber;\n\n        this.$emit('update:launchTemplate', {\n          name, id, version\n        });\n      }\n    },\n\n    launchTemplateVersionOptions(): number[] {\n      if (this.selectedLaunchTemplateInfo && this.selectedLaunchTemplateInfo?.LaunchTemplateVersions) {\n        return this.selectedLaunchTemplateInfo.LaunchTemplateVersions.map((version) => version.VersionNumber).sort();\n      }\n\n      return [];\n    },\n\n    selectedVersionInfo(): AWS.LaunchTemplateVersion | null {\n      return (this.selectedLaunchTemplateInfo?.LaunchTemplateVersions || []).find((v: any) => v.VersionNumber === this.launchTemplate?.version) || null;\n    },\n\n    selectedVersionData(): AWS.LaunchTemplateVersionData | undefined {\n      return this.selectedVersionInfo?.LaunchTemplateData;\n    },\n\n    displayNodeRole: {\n      get() {\n        const arn = this.nodeRole;\n\n        if (!arn) {\n          return this.defaultNodeRoleOption;\n        }\n\n        return this.ec2Roles.find((role: AWS.IamRole) => role.Arn === arn) ;\n      },\n      set(neu: AWS.IamRole) {\n        if (neu.Arn) {\n          this.$emit('update:nodeRole', neu.Arn);\n        } else {\n          this.$emit('update:nodeRole', '');\n        }\n      }\n    },\n\n    userDataPlaceholder() {\n      return DEFAULT_USER_DATA;\n    },\n\n    poolIsUnprovisioned() {\n      return this.isNewOrUnprovisioned || this.poolIsNew;\n    },\n\n    clusterWillUpgrade() {\n      return this.clusterVersion !== this.originalClusterVersion;\n    },\n\n    nodeCanUpgrade() {\n      return !this.clusterWillUpgrade && this.originalNodeVersion !== this.clusterVersion && !this.poolIsNew;\n    },\n\n    willUpgrade: {\n      get() {\n        return this.nodeCanUpgrade && this.version === this.clusterVersion;\n      },\n      set(neu: boolean) {\n        if (neu) {\n          this.$emit('update:version', this.clusterVersion);\n          this.$emit('update:poolIsUpgrading', true);\n        } else {\n          this.$emit('update:version', this.originalNodeVersion);\n          this.$emit('update:poolIsUpgrading', false);\n        }\n      }\n    },\n\n    minMaxDesiredErrors() {\n      const errs = (this.rules?.minMaxDesired || []).reduce((errs: string[], rule: Function) => {\n        const err = rule({\n          minSize: this.minSize, maxSize: this.maxSize, desiredSize: this.desiredSize\n        });\n\n        if (err) {\n          errs.push(err);\n        }\n\n        return errs;\n      }, [] as string[]);\n\n      return errs.length ? errs.join(' ') : null;\n    },\n\n    isView() {\n      return this.mode === _VIEW;\n    }\n  },\n\n  methods: {\n    async fetchLaunchTemplateVersionInfo(launchTemplate: AWS.LaunchTemplate) {\n      const { region, amazonCredentialSecret } = this;\n\n      if (!region || !amazonCredentialSecret || this.isView) {\n        return;\n      }\n      const store = this.$store as Store<any>;\n      const ec2Client = await store.dispatch('aws/ec2', { region, cloudCredentialId: amazonCredentialSecret });\n\n      try {\n        if (launchTemplate.LaunchTemplateName !== this.defaultTemplateOption.LaunchTemplateName) {\n          if (launchTemplate.LaunchTemplateId) {\n            this.selectedLaunchTemplateInfo = await ec2Client.describeLaunchTemplateVersions({ LaunchTemplateId: launchTemplate.LaunchTemplateId });\n          } else {\n            this.selectedLaunchTemplateInfo = await ec2Client.describeLaunchTemplateVersions({ LaunchTemplateName: launchTemplate.LaunchTemplateName });\n          }\n        }\n      } catch (err) {\n        this.$emit('error', err);\n      }\n    },\n\n    setValuesFromTemplate(neu = {} as AWS.LaunchTemplateVersionData, old = {} as AWS.LaunchTemplateVersionData) {\n      Object.keys(launchTemplateFieldMapping).forEach((rancherKey: string) => {\n        const awsKey = launchTemplateFieldMapping[rancherKey];\n\n        if (awsKey === 'TagSpecifications') {\n          const { TagSpecifications } = neu;\n\n          if (TagSpecifications) {\n            const tags = {} as {[key:string]: string};\n\n            TagSpecifications.forEach((tag: {Tags?: {Key: string, Value: string}[], ResourceType?: string}) => {\n              if (tag.ResourceType === 'instance' && tag.Tags && tag.Tags.length) {\n                Object.assign(tags, parseTags(tag.Tags));\n              }\n            });\n            this.$emit('update:resourceTags', tags);\n          } else {\n            this.$emit('update:resourceTags', { ...DEFAULT_NODE_GROUP_CONFIG.resourceTags });\n          }\n        } else if (awsKey === 'BlockDeviceMappings') {\n          const { BlockDeviceMappings } = neu;\n\n          if (BlockDeviceMappings && BlockDeviceMappings.length) {\n            const size = BlockDeviceMappings[0]?.Ebs?.VolumeSize;\n\n            this.$emit('update:diskSize', size);\n          } else {\n            this.$emit('update:diskSize', DEFAULT_NODE_GROUP_CONFIG.diskSize);\n          }\n        } else if (this.templateValue(rancherKey)) {\n          this.$emit(`update:${ rancherKey }`, this.templateValue(rancherKey));\n        } else {\n          this.$emit(`update:${ rancherKey }`, DEFAULT_NODE_GROUP_CONFIG[rancherKey as keyof typeof DEFAULT_NODE_GROUP_CONFIG]);\n        }\n      });\n\n      this.$nextTick(() => {\n        this.resourceTagKey = randomStr();\n        this.loadingSelectedVersion = false;\n      });\n    },\n\n    templateValue(field: string): string | null | AWS.TagSpecification | AWS.TagSpecification[] | AWS.BlockDeviceMapping[] {\n      if (this.hasNoLaunchTemplate) {\n        return null;\n      }\n\n      const launchTemplateKey = launchTemplateFieldMapping[field] as keyof AWS.LaunchTemplateVersionData;\n\n      if (!launchTemplateKey) {\n        return null;\n      }\n      const launchTemplateVal = this.selectedVersionData?.[launchTemplateKey];\n\n      if (launchTemplateVal !== undefined && (!(typeof launchTemplateVal === 'object') || !isEmpty(launchTemplateVal))) {\n        if (field === 'diskSize') {\n          const blockMapping = launchTemplateVal[0] as AWS.BlockDeviceMapping;\n\n          return blockMapping?.Ebs?.VolumeSize || null;\n        }\n        if (field === 'resourceTags') {\n          const tags = (launchTemplateVal || []) as AWS.TagSpecification[];\n\n          return tags.filter((tag: AWS.TagSpecification) => tag.ResourceType === 'instance')[0];\n        }\n\n        return launchTemplateVal;\n      }\n\n      return null;\n    },\n  },\n});\n</script>\n\n<template>\n  <div>\n    <h3>{{ t('eks.nodeGroups.groupDetails') }}</h3>\n    <div class=\"row mb-10\">\n      <div class=\"col span-6\">\n        <LabeledInput\n          :value=\"nodegroupName\"\n          label-key=\"eks.nodeGroups.name.label\"\n          :mode=\"mode\"\n          :disabled=\"!poolIsUnprovisioned\"\n          :rules=\"rules.nodegroupName\"\n          data-testid=\"eks-nodegroup-name\"\n          required\n          @update:value=\"$emit('update:nodegroupName', $event)\"\n        />\n      </div>\n\n      <div class=\"col span-6\">\n        <LabeledSelect\n          v-model:value=\"displayNodeRole\"\n          :mode=\"mode\"\n          data-testid=\"eks-noderole\"\n          label-key=\"eks.nodeGroups.nodeRole.label\"\n          :options=\"[defaultNodeRoleOption, ...ec2Roles]\"\n          option-label=\"RoleName\"\n          option-key=\"Arn\"\n          :disabled=\"!poolIsUnprovisioned\"\n          :loading=\"loadingRoles\"\n        />\n      </div>\n    </div>\n    <div class=\"row mb-10\">\n      <div class=\"col span-4\">\n        <LabeledInput\n          type=\"number\"\n          :value=\"desiredSize\"\n          label-key=\"eks.nodeGroups.desiredSize.label\"\n          :mode=\"mode\"\n          :rules=\"rules.desiredSize\"\n          @update:value=\"$emit('update:desiredSize', parseInt($event))\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <LabeledInput\n          type=\"number\"\n          :value=\"minSize\"\n          label-key=\"eks.nodeGroups.minSize.label\"\n          :mode=\"mode\"\n          :rules=\"rules.minSize\"\n          @update:value=\"$emit('update:minSize', parseInt($event))\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <LabeledInput\n          type=\"number\"\n          :value=\"maxSize\"\n          label-key=\"eks.nodeGroups.maxSize.label\"\n          :mode=\"mode\"\n          :rules=\"rules.maxSize\"\n          @update:value=\"$emit('update:maxSize', parseInt($event))\"\n        />\n      </div>\n    </div>\n    <Banner\n      v-if=\"!!minMaxDesiredErrors\"\n      color=\"error\"\n      :label=\"minMaxDesiredErrors\"\n    />\n    <div class=\"row mb-10\">\n      <div class=\"col span-6 mt-20\">\n        <KeyValue\n          :mode=\"mode\"\n          :title=\"t('eks.nodeGroups.groupLabels.label')\"\n          :read-allowed=\"false\"\n          :value=\"labels\"\n          :as-map=\"true\"\n          @update:value=\"$emit('update:labels', $event)\"\n        >\n          <template #title>\n            <h4>\n              {{ t('eks.nodeGroups.groupLabels.label') }}\n            </h4>\n          </template>\n        </KeyValue>\n      </div>\n      <div class=\"col span-6 mt-20\">\n        <KeyValue\n          :mode=\"mode\"\n          :title=\"t('eks.nodeGroups.groupTags.label')\"\n          :read-allowed=\"false\"\n          :as-map=\"true\"\n          :value=\"tags\"\n          data-testid=\"eks-resource-tags-input\"\n          @update:value=\"$emit('update:tags', $event)\"\n        >\n          <template #title>\n            <h4>{{ t('eks.nodeGroups.groupTags.label') }}</h4>\n          </template>\n        </KeyValue>\n      </div>\n    </div>\n    <hr\n      class=\"mb-20\"\n      role=\"none\"\n    >\n    <h3>{{ t('eks.nodeGroups.templateDetails') }}</h3>\n    <Banner\n      v-if=\"clusterWillUpgrade && !poolIsUnprovisioned\"\n      color=\"info\"\n      label-key=\"eks.nodeGroups.kubernetesVersion.clusterWillUpgrade\"\n      data-testid=\"eks-version-upgrade-banner\"\n    />\n    <div class=\"row mb-10\">\n      <div class=\"col span-4 upgrade-version\">\n        <LabeledInput\n          v-if=\"!nodeCanUpgrade\"\n          label-key=\"eks.nodeGroups.kubernetesVersion.label\"\n          :disabled=\"true\"\n          :value=\"version\"\n          data-testid=\"eks-version-display\"\n        />\n        <Checkbox\n          v-else\n          v-model:value=\"willUpgrade\"\n          :label=\"t('eks.nodeGroups.kubernetesVersion.upgrade', {from: originalNodeVersion, to: clusterVersion})\"\n          data-testid=\"eks-version-upgrade-checkbox\"\n          :disabled=\"isView\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <LabeledSelect\n          v-model:value=\"selectedLaunchTemplate\"\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.launchTemplate.label\"\n          :options=\"launchTemplateOptions\"\n          option-label=\"LaunchTemplateName\"\n          option-key=\"LaunchTemplateId\"\n          :disabled=\"!poolIsUnprovisioned\"\n          :loading=\"loadingLaunchTemplates\"\n          data-testid=\"eks-launch-template-dropdown\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <LabeledSelect\n          v-if=\"hasUserLaunchTemplate\"\n          :value=\"launchTemplate.version\"\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.launchTemplate.version\"\n          :options=\"launchTemplateVersionOptions\"\n          data-testid=\"eks-launch-template-version-dropdown\"\n          @update:value=\"$emit('update:launchTemplate', {...launchTemplate, version: $event})\"\n        />\n      </div>\n    </div>\n    <Banner\n      color=\"info\"\n      label-key=\"eks.nodeGroups.imageId.tooltip\"\n    />\n    <div class=\"row mb-10\">\n      <div class=\"col span-4\">\n        <LabeledInput\n          label-key=\"eks.nodeGroups.imageId.label\"\n          :mode=\"mode\"\n          :value=\"imageId\"\n          :disabled=\"hasUserLaunchTemplate\"\n          data-testid=\"eks-image-id-input\"\n          @update:value=\"$emit('update:imageId', $event)\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <LabeledSelect\n          :required=\"!requestSpotInstances && !templateValue('instanceType')\"\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.instanceType.label\"\n          :options=\"instanceTypeOptions\"\n          :loading=\"loadingInstanceTypes\"\n          :value=\"instanceType\"\n          :disabled=\"!!templateValue('instanceType') || requestSpotInstances\"\n          :tooltip=\"(requestSpotInstances && !templateValue('instanceType')) ? t('eks.nodeGroups.instanceType.tooltip'): ''\"\n          :rules=\"!requestSpotInstances ? rules.instanceType : []\"\n          data-testid=\"eks-instance-type-dropdown\"\n          @update:value=\"$emit('update:instanceType', $event)\"\n        />\n      </div>\n\n      <div class=\"col span-4\">\n        <UnitInput\n          :required=\"!templateValue('diskSize')\"\n          label-key=\"eks.nodeGroups.diskSize.label\"\n          :mode=\"mode\"\n          :value=\"diskSize\"\n          suffix=\"GB\"\n          :loading=\"loadingSelectedVersion\"\n          :disabled=\"!!templateValue('diskSize') || loadingSelectedVersion\"\n          :rules=\"rules.diskSize\"\n          data-testid=\"eks-disksize-input\"\n          @update:value=\"$emit('update:diskSize', $event)\"\n        />\n      </div>\n    </div>\n    <Banner\n      v-if=\"requestSpotInstances && hasUserLaunchTemplate\"\n      color=\"warning\"\n      :label=\"t('eks.nodeGroups.requestSpotInstances.warning')\"\n      data-testid=\"eks-spot-instance-banner\"\n    />\n    <div class=\"row mb-10\">\n      <div class=\"col span-4\">\n        <Checkbox\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.gpu.label\"\n          :value=\"gpu\"\n          :disabled=\"!!templateValue('imageId') || hasRancherLaunchTemplate\"\n          :tooltip=\"templateValue('imageId') ? t('eks.nodeGroups.gpu.tooltip') : ''\"\n          data-testid=\"eks-gpu-input\"\n          @update:value=\"$emit('update:gpu', $event)\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <Checkbox\n          :value=\"requestSpotInstances\"\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.requestSpotInstances.label\"\n          :disabled=\"hasRancherLaunchTemplate\"\n          @update:value=\"$emit('update:requestSpotInstances', $event)\"\n        />\n      </div>\n    </div>\n    <div\n      v-if=\"requestSpotInstances && !templateValue('instanceType')\"\n      class=\"row mb-10\"\n    >\n      <div\n        class=\"col span-6\"\n      >\n        <LabeledSelect\n          :mode=\"mode\"\n          :value=\"spotInstanceTypes\"\n          label-key=\"eks.nodeGroups.spotInstanceTypes.label\"\n          :options=\"spotInstanceTypeOptions\"\n          :multiple=\"true\"\n          :loading=\"loadingSelectedVersion || loadingInstanceTypes\"\n          data-testid=\"eks-spot-instance-type-dropdown\"\n          @update:value=\"$emit('update:spotInstanceTypes', $event)\"\n        />\n      </div>\n    </div>\n    <div class=\"row mb-15\">\n      <div class=\"col span-6 user-data\">\n        <LabeledInput\n          label-key=\"eks.nodeGroups.userData.label\"\n          :mode=\"mode\"\n          type=\"multiline\"\n          :value=\"userData\"\n          :disabled=\"hasUserLaunchTemplate\"\n          :placeholder=\"userDataPlaceholder\"\n          :sub-label=\"t('eks.nodeGroups.userData.tooltip', {}, true)\"\n          @update:value=\"$emit('update:userData', $event)\"\n        />\n        <FileSelector\n          :mode=\"mode\"\n          :label=\"t('generic.readFromFile')\"\n          class=\"role-tertiary mt-20\"\n          @selected=\"$emit('update:userData', $event)\"\n        />\n      </div>\n      <div class=\"col span-6\">\n        <LabeledSelect\n          :loading=\"loadingSshKeyPairs\"\n          :value=\"ec2SshKey\"\n          :options=\"sshKeyPairs\"\n          label-key=\"eks.nodeGroups.ec2SshKey.label\"\n          :mode=\"mode\"\n          :disabled=\"hasUserLaunchTemplate\"\n          :taggable=\"true\"\n          :searchable=\"true\"\n          data-testid=\"eks-nodegroup-ec2-key-select\"\n          @update:value=\"$emit('update:ec2SshKey', $event)\"\n        />\n      </div>\n    </div>\n    <div row=\"mb-10\">\n      <div class=\"col span-12 mt-20\">\n        <KeyValue\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.resourceTags.label\"\n          :value=\"resourceTags\"\n          :disabled=\"hasUserLaunchTemplate\"\n          :read-allowed=\"false\"\n          :as-map=\"true\"\n          @update:value=\"$emit('update:resourceTags', $event)\"\n        >\n          <template #title>\n            <h4>\n              {{ t('eks.nodeGroups.resourceTags.label') }}\n            </h4>\n          </template>\n        </KeyValue>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.user-data{\n  &>button{\n    float: right;\n  }\n}\n\n.upgrade-version {\n  display: flex;\n  align-items: center;\n}\n</style>\n"]}]}