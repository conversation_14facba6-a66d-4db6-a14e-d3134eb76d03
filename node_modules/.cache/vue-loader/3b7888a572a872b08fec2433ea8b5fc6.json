{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/LogItem.vue?vue&type=style&index=0&id=845e8778&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/LogItem.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5saW5lIHsKICBmb250LWZhbWlseTogTWVubG8sQ29uc29sYXMsbW9ub3NwYWNlOwogIGNvbG9yOiB2YXIoLS1sb2dzLXRleHQpOwogIGRpc3BsYXk6ZmxleDsKfQoKLnRpbWUgewogIHdoaXRlLXNwYWNlOiBub3dyYXA7CiAgZGlzcGxheTogbm9uZTsKICB3aWR0aDogMDsKICBwYWRkaW5nLXJpZ2h0OiAxNXB4OwogIHVzZXItc2VsZWN0OiBub25lOwp9CgoubXNnIHsKICB3aGl0ZS1zcGFjZTogcHJlOwoKICAuaGlnaGxpZ2h0IHsKICAgIGNvbG9yOiB2YXIoLS1sb2dzLWhpZ2hsaWdodCk7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1sb2dzLWhpZ2hsaWdodC1iZyk7CiAgfQp9Cgo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/LogItem.vue"], "names": [], "mappings": ";AA6CA,CAAC,CAAC,CAAC,CAAC,EAAE;EACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd;;AAEA,CAAC,CAAC,CAAC,CAAC,EAAE;EACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACnB;;AAEA,CAAC,CAAC,CAAC,EAAE;EACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5C;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/LogItem.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport day from 'dayjs';\nimport { DATE_FORMAT, TIME_FORMAT } from '@shell/store/prefs';\nimport { escapeHtml } from '@shell/utils/string';\n\nexport default {\n  props: {\n    source: {\n      type:    Object,\n      default: () => {}\n    }\n  },\n\n  computed: {\n    timeFormatStr() {\n      const dateFormat = escapeHtml( this.$store.getters['prefs/get'](DATE_FORMAT));\n      const timeFormat = escapeHtml( this.$store.getters['prefs/get'](TIME_FORMAT));\n\n      return `${ dateFormat } ${ timeFormat }`;\n    },\n  },\n\n  methods: {\n    format(time) {\n      if ( !time ) {\n        return '';\n      }\n\n      return day(time).format(this.timeFormatStr);\n    }\n  }\n};\n</script>\n\n<template>\n  <div class=\"line\">\n    <span class=\"time\">{{ format(source.time) }}</span>\n    <span\n      v-clean-html=\"source.msg\"\n      class=\"msg\"\n    />\n  </div>\n</template>\n\n<style lang='scss' scoped>\n.line {\n  font-family: <PERSON><PERSON>,Consolas,monospace;\n  color: var(--logs-text);\n  display:flex;\n}\n\n.time {\n  white-space: nowrap;\n  display: none;\n  width: 0;\n  padding-right: 15px;\n  user-select: none;\n}\n\n.msg {\n  white-space: pre;\n\n  .highlight {\n    color: var(--logs-highlight);\n    background-color: var(--logs-highlight-bg);\n  }\n}\n\n</style>\n"]}]}