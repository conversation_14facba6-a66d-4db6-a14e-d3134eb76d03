{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Members/MembershipEditor.vue?vue&type=style&index=0&id=192d741e&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Members/MembershipEditor.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5yb2xlIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgZmxleC1kaXJlY3Rpb246IHJvdzsKfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Members/MembershipEditor.vue"], "names": [], "mappings": ";AAgOA,CAAC,CAAC,CAAC,CAAC,EAAE;EACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACrB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Members/MembershipEditor.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { MANAGEMENT, NORMAN } from '@shell/config/types';\nimport ArrayList from '@shell/components/form/ArrayList';\nimport Principal from '@shell/components/auth/Principal';\nimport Loading from '@shell/components/Loading';\nimport { _CREATE, _VIEW } from '@shell/config/query-params';\nimport { get, set } from '@shell/utils/object';\n\nfunction normalizeId(id) {\n  return id?.replace(':', '/') || id;\n}\n\nexport function canViewMembershipEditor(store, needsProject = false) {\n  return (!!store.getters['management/schemaFor'](MANAGEMENT.PROJECT_ROLE_TEMPLATE_BINDING) || !needsProject) &&\n    !!store.getters['management/schemaFor'](MANAGEMENT.ROLE_TEMPLATE) &&\n    !!store.getters['rancher/schemaFor'](NORMAN.PRINCIPAL);\n}\n\nexport default {\n  emits: ['membership-update'],\n\n  components: {\n    ArrayList, Loading, Principal\n  },\n\n  props: {\n    addMemberDialogName: {\n      type:     String,\n      required: true\n    },\n\n    parentKey: {\n      type:     String,\n      required: true\n    },\n\n    parentId: {\n      type:    String,\n      default: null\n    },\n\n    mode: {\n      type:     String,\n      required: true\n    },\n\n    type: {\n      type:     String,\n      required: true\n    },\n\n    defaultBindingHandler: {\n      type:    Function,\n      default: null,\n    },\n\n    modalSticky: {\n      type:    Boolean,\n      default: false,\n    }\n  },\n\n  async fetch() {\n    const roleBindingRequestParams = { type: this.type, opt: { force: true } };\n\n    if (this.type === NORMAN.PROJECT_ROLE_TEMPLATE_BINDING && this.parentId) {\n      Object.assign(roleBindingRequestParams, { opt: { filter: { projectId: this.parentId.split('/').join(':') }, force: true } });\n    }\n    const userHydration = [\n      this.schema ? this.$store.dispatch(`rancher/findAll`, roleBindingRequestParams) : [],\n      this.$store.dispatch('rancher/findAll', { type: NORMAN.PRINCIPAL }),\n      this.$store.dispatch(`management/findAll`, { type: MANAGEMENT.ROLE_TEMPLATE }),\n      this.$store.dispatch(`management/findAll`, { type: MANAGEMENT.USER })\n    ];\n    const [allBindings] = await Promise.all(userHydration);\n\n    const bindings = allBindings\n      .filter((b) => normalizeId(get(b, this.parentKey)) === normalizeId(this.parentId));\n\n    this['lastSavedBindings'] = [...bindings];\n\n    // Add the current user as the project owner. This will get created by default\n    if (this.mode === _CREATE && bindings.length === 0 && this.defaultBindingHandler) {\n      const defaultBinding = await this.defaultBindingHandler();\n\n      defaultBinding.isDefaultBinding = true;\n      bindings.push(defaultBinding);\n    }\n\n    this['bindings'] = bindings;\n  },\n\n  data() {\n    return {\n      schema:            this.$store.getters[`rancher/schemaFor`](this.type),\n      bindings:          [],\n      lastSavedBindings: [],\n    };\n  },\n\n  computed: {\n    newBindings() {\n      return this.bindings\n        .filter((binding) => !binding.id && !this.lastSavedBindings.includes(binding) && !binding.isDefaultBinding);\n    },\n    removedBindings() {\n      return this.lastSavedBindings\n        .filter((binding) => !this.bindings.includes(binding));\n    },\n    membershipUpdate() {\n      const newBindings = this.newBindings;\n      const removedBindings = this.removedBindings;\n\n      return {\n        newBindings:     this.newBindings,\n        removedBindings: this.removedBindings,\n        save:            (parentId) => {\n          const savedPromises = newBindings.map((binding) => {\n            set(binding, this.parentKey, parentId);\n\n            return binding.save();\n          });\n\n          const removedPromises = removedBindings.map((binding) => binding.remove());\n\n          return Promise.all([...savedPromises, ...removedPromises]);\n        }\n      };\n    },\n\n    isCreate() {\n      return this.mode === _CREATE;\n    },\n\n    isView() {\n      return this.mode === _VIEW;\n    },\n  },\n  watch: {\n    membershipUpdate: {\n      deep: true,\n      handler() {\n        this.$emit('membership-update', this.membershipUpdate);\n      }\n    }\n  },\n\n  methods: {\n    addMember() {\n      this.$store.dispatch('cluster/promptModal', {\n        component:      this.addMemberDialogName,\n        componentProps: { onAdd: this.onAddMember },\n        modalSticky:    this.modalSticky\n      });\n    },\n\n    onAddMember(bindings) {\n      this['bindings'] = [...this.bindings, ...bindings];\n    },\n  }\n};\n</script>\n<template>\n  <Loading v-if=\"$fetchState.pending\" />\n  <ArrayList\n    v-else\n    v-model:value=\"bindings\"\n    :mode=\"mode\"\n    :show-header=\"true\"\n  >\n    <template #column-headers>\n      <div class=\"box mb-0\">\n        <div class=\"column-headers row\">\n          <div class=\"col span-6\">\n            <label class=\"text-label\">{{ t('membershipEditor.user') }}</label>\n          </div>\n          <div class=\"col span-6\">\n            <label class=\"text-label\">{{ t('membershipEditor.role') }}</label>\n          </div>\n        </div>\n      </div>\n    </template>\n    <template #columns=\"{row, i}\">\n      <div class=\"columns row\">\n        <div class=\"col span-6\">\n          <Principal\n            :value=\"row.value.principalId\"\n          />\n        </div>\n        <div\n          :data-testid=\"`role-item-${i}`\"\n          class=\"col span-6 role\"\n        >\n          {{ row.value.roleDisplay }}\n        </div>\n      </div>\n    </template>\n    <template #add>\n      <button\n        type=\"button\"\n        class=\"btn role-primary mt-10\"\n        data-testid=\"add-item\"\n        @click=\"addMember\"\n      >\n        {{ t('generic.add') }}\n      </button>\n    </template>\n    <template #remove-button=\"{remove, i}\">\n      <span v-if=\"(isCreate && i === 0) || isView\" />\n      <button\n        v-else\n        type=\"button\"\n        :disabled=\"isView\"\n        class=\"btn role-link\"\n        :data-testid=\"`remove-item-${i}`\"\n        @click=\"remove\"\n      >\n        {{ t('generic.remove') }}\n      </button>\n    </template>\n  </ArrayList>\n</template>\n\n<style lang=\"scss\" scoped>\n.role {\n  display: flex;\n  align-items: center;\n  flex-direction: row;\n}\n</style>\n"]}]}