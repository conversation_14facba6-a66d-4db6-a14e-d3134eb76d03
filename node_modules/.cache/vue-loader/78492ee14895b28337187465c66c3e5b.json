{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Wizard.vue?vue&type=style&index=0&id=588e932e&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Wizard.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Wizard.vue"], "names": [], "mappings": ";AA0eA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;AAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAExB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;CAEpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;MAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpB;;MAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEnB,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACnB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACd;;QAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;UAEhB,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB;QACF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB;QACF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACb,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACvB;QACF;MACF;;MAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAET,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACX;MACF;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC;;IAEA,CAAC,CAAC,CAAC,CAAC,EAAE;;MAEJ,CAAC,EAAE;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACb;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAElB,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACpB;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B;MACF;;IAEF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB;IACF;;IAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACd;IACF;EACF;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5F,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT;AACF;;AAEA,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACrB;;EAEE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAEZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;MAEd,CAAC,CAAC,CAAC,EAAE;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB;IACF;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Wizard.vue", "sourceRoot": "", "sourcesContent": ["<script>\n\nimport AsyncButton from '@shell/components/AsyncButton';\nimport { Banner } from '@components/Banner';\nimport Loading from '@shell/components/Loading';\nimport { stringify } from '@shell/utils/error';\nimport LazyImage from '@shell/components/LazyImage';\n\n/*\n<PERSON> accepts an array of steps (see props), and creates named slots for each step.\nIt also contains slots for buttons:\n    next\n    cancel\n    finish\n\nWizard will emit these events:\n    next({step})\n    cancel\n    finish\n*/\n\n// i18n-uses wizard.next, wizard.edit, wizard.create, wizard.view, wizard.finish\nexport default {\n  name: 'Wizard',\n\n  emits: ['next', 'cancel', 'finish'],\n\n  components: {\n    AsyncButton,\n    Banner,\n    Loading,\n    LazyImage,\n  },\n\n  props: {\n    /*\n  steps need: {\n    name: String - this will be the slot name\n    label: String - this will appear in the top nav bar below circles\n    subtext: String (optional) - If defined, appears below the step number in the banner. If blank, label is used\n    ready: Boolean - whether or not the step is completed/wizard is able to go to next step\n      if a step has ready=true, the wizard also allows navigation *back* to it\n    hidden: Don't show step, though include in DOM (dynamic steps must be in DOM to determine if they will include themselves in wizard)\n    loading: <PERSON> will block until all steps are not loading\n    nextButton?: {\n      labelKey?: default to `wizard.next`\n      style?:  defaults to `btn role-primary`\n    },\n    previousButton: {\n      disable: defaults to false\n    }\n  }\n  */\n    steps: {\n      type:     Array,\n      required: true\n    },\n\n    // Initial step to show when Wizard loads.\n    initStepIndex: {\n      type:    Number,\n      default: 0\n    },\n\n    // if true, allow users to navigate back to the first step of the Wizard\n    // if false, only way back to step 1 is to cancel and undo all configuration\n    editFirstStep: {\n      type:    Boolean,\n      default: false\n    },\n\n    showBanner: {\n      type:    Boolean,\n      default: true,\n    },\n\n    // whether or not to show the overall title/image on left of banner header in first step\n    initialTitle: {\n      type:    Boolean,\n      default: true\n    },\n\n    // place the same title (e.g. the type of thing being created by wizard) on every page\n    bannerTitle: {\n      type:    String,\n      default: null\n    },\n\n    // circular image left of banner title\n    bannerImage: {\n      type:    String,\n      default: null\n    },\n\n    bannerTitleSubtext: {\n      type:    String,\n      default: null\n    },\n\n    // Verb shown in the header, defaults to finishMode\n    headerMode: {\n      type:    String,\n      default: null\n    },\n\n    // The set of labels to display for the finish AsyncButton\n    finishMode: {\n      type:    String,\n      default: 'finish'\n    },\n\n    // Errors to display above the buttons\n    errors: {\n      type:    Array,\n      default: null,\n    }\n  },\n\n  data() {\n    return { activeStep: null };\n  },\n\n  computed: {\n    errorStrings() {\n      return ( this.errors || [] ).map((x) => stringify(x));\n    },\n\n    activeStepIndex() {\n      return this.visibleSteps.findIndex((s) => s.name === this.activeStep.name);\n    },\n\n    showPrevious() {\n      // If on first step...\n      if (this.activeStepIndex === 0) {\n        return false;\n      }\n      // .. or any previous step isn't hidden\n      for (let stepIndex = 0; stepIndex < this.activeStepIndex; stepIndex++) {\n        const step = this.visibleSteps[stepIndex];\n\n        if (!step) {\n          break;\n        }\n        if (!step.hidden) {\n          return true;\n        }\n      }\n\n      return false;\n    },\n\n    canPrevious() {\n      return !this.activeStep?.previousButton?.disable && (this.activeStepIndex > 1 || this.editFirstStep);\n    },\n\n    canNext() {\n      const activeStep = this.visibleSteps[this.activeStepIndex];\n\n      return (this.activeStepIndex < this.visibleSteps.length - 1) && activeStep.ready;\n    },\n\n    readySteps() {\n      return this.visibleSteps.filter((step) => step.ready);\n    },\n\n    showSteps() {\n      return this.activeStep.showSteps !== false && this.visibleSteps.length > 1;\n    },\n\n    stepsLoaded() {\n      return !this.steps.some((step) => step.loading === true);\n    },\n\n    visibleSteps() {\n      return this.steps.filter((step) => !step.hidden);\n    },\n\n    nextButtonStyle() {\n      return this.activeStep.nextButton?.style || `btn role-primary`;\n    },\n    nextButtonLabel() {\n      return this.activeStep.nextButton?.labelKey || `wizard.next`;\n    }\n  },\n\n  watch: {\n    stepsLoaded(neu, old) {\n      if (!old && neu) {\n        this.activeStep = this.visibleSteps[this.initStepIndex];\n        this.goToStep(this.activeStepIndex + 1);\n      }\n    },\n    errors() {\n      // Ensurce we scroll the errors into view\n      this.$nextTick(() => {\n        this.$refs.wizard.scrollTop = this.$refs.wizard.scrollHeight;\n      });\n    }\n  },\n\n  created() {\n    this.activeStep = this.visibleSteps[this.initStepIndex];\n    this.goToStep(this.activeStepIndex + 1);\n  },\n\n  methods: {\n    goToStep(number, fromNav) {\n      if (number < 1) {\n        return;\n      }\n\n      // if editFirstStep is false, do not allow returning to step 1 (restarting wizard) from top nav\n      if (!this.editFirstStep && (number === 1 && fromNav)) {\n        return;\n      }\n\n      const selected = this.visibleSteps[number - 1];\n\n      if ( !selected || (!this.isAvailable(selected) && number !== 1)) {\n        return;\n      }\n\n      this.activeStep = selected;\n\n      this.$emit('next', { step: selected });\n    },\n\n    cancel() {\n      this.$emit('cancel');\n    },\n\n    finish(cb) {\n      this.$emit('finish', cb);\n    },\n\n    next() {\n      this.goToStep(this.activeStepIndex + 2);\n    },\n\n    back() {\n      this.goToStep(this.activeStepIndex);\n    },\n\n    // a step is not available if ready=false for any previous steps OR if the editFirstStep=false and it is the first step\n    isAvailable(step) {\n      if (!step) {\n        return false;\n      }\n\n      const idx = this.visibleSteps.findIndex((s) => s.name === step.name);\n\n      if (idx === 0 && !this.editFirstStep) {\n        return false;\n      }\n\n      for (let i = 0; i < idx; i++) {\n        if ( this.visibleSteps[i].ready === false ) {\n          return false;\n        }\n      }\n\n      return true;\n    },\n  }\n};\n</script>\n\n<template>\n  <div\n    ref=\"wizard\"\n    class=\"outer-container\"\n  >\n    <Loading\n      v-if=\"!stepsLoaded\"\n      mode=\"relative\"\n    />\n    <!-- Note - Don't v-else this.... the steps need to be included in order to update 'stepsLoaded' -->\n    <div\n      class=\"outer-container\"\n      :class=\"{'hide': !stepsLoaded}\"\n    >\n      <div>\n        <div class=\"header\">\n          <div class=\"title\">\n            <div\n              v-if=\"showBanner\"\n              class=\"top choice-banner\"\n            >\n              <slot\n                v-if=\"!!bannerImage || !!bannerTitle\"\n                name=\"bannerTitle\"\n              >\n                <div\n                  v-show=\"initialTitle || activeStepIndex > 0\"\n                  class=\"title\"\n                >\n                  <!-- Logo -->\n                  <slot name=\"bannerTitleImage\">\n                    <div\n                      v-if=\"bannerImage\"\n                      class=\"round-image\"\n                    >\n                      <LazyImage\n                        :src=\"bannerImage\"\n                        class=\"logo\"\n                      />\n                    </div>\n                  </slot>\n                  <!-- Title with subtext -->\n                  <div class=\"subtitle\">\n                    <h2 v-if=\"bannerTitle\">\n                      {{ bannerTitle }}\n                    </h2>\n                    <span\n                      v-if=\"bannerTitleSubtext\"\n                      class=\"subtext\"\n                    >{{ bannerTitleSubtext }}</span>\n                  </div>\n                </div>\n              </slot>\n              <!-- Step number with subtext -->\n              <div\n                v-if=\"activeStep && showSteps\"\n                class=\"subtitle\"\n              >\n                <h2>{{ !!headerMode ? t(`wizard.${headerMode}`) : t(`asyncButton.${finishMode}.action`) }}: {{ t('wizard.step', {number:activeStepIndex+1}) }}</h2>\n                <slot name=\"bannerSubtext\">\n                  <span\n                    v-if=\"activeStep.subtext !== null\"\n                    class=\"subtext\"\n                  >{{ activeStep.subtext || activeStep.label }}</span>\n                </slot>\n              </div>\n            </div>\n            <div class=\"step-sequence\">\n              <ul\n                v-if=\"showSteps\"\n                class=\"steps\"\n                tabindex=\"0\"\n                @keyup.right.stop=\"selectNext(1)\"\n                @keyup.left.stop=\"selectNext(-1)\"\n              >\n                <template\n                  v-for=\"(step, idx ) in visibleSteps\"\n                  :key=\"idx\"\n                >\n                  <li\n\n                    :id=\"step.name\"\n                    :class=\"{step: true, active: step.name === activeStep.name, disabled: !isAvailable(step)}\"\n                    role=\"presentation\"\n                  >\n                    <span\n                      :aria-controls=\"'step' + idx+1\"\n                      :aria-selected=\"step.name === activeStep.name\"\n                      role=\"tab\"\n                      class=\"controls\"\n                      @click.prevent=\"goToStep(idx+1, true)\"\n                    >\n                      <span\n                        class=\"icon icon-lg\"\n                        :class=\"{'icon-dot': step.name === activeStep.name, 'icon-dot-open':step.name !== activeStep.name}\"\n                      />\n                      <span>\n                        {{ step.label }}\n                      </span>\n                    </span>\n                  </li>\n                  <div\n                    v-if=\"idx!==visibleSteps.length-1\"\n                    :key=\"step.name\"\n                    class=\"divider\"\n                  />\n                </template>\n              </ul>\n            </div>\n          </div>\n        </div>\n        <slot\n          class=\"step-container\"\n          name=\"stepContainer\"\n          :activeStep=\"activeStep\"\n        >\n          <template\n            v-for=\"(step, i) in steps\"\n            :key=\"i\"\n          >\n            <div\n              v-if=\"step.name === activeStep.name || step.hidden\"\n              :key=\"step.name\"\n              class=\"step-container__step\"\n              :class=\"{'hide': step.name !== activeStep.name && step.hidden}\"\n            >\n              <slot\n                :step=\"step\"\n                :name=\"step.name\"\n              />\n            </div>\n          </template>\n        </slot>\n      </div>\n      <slot\n        name=\"controlsContainer\"\n        :showPrevious=\"showPrevious\"\n        :next=\"next\"\n        :back=\"back\"\n        :canNext=\"canNext\"\n        :activeStepIndex=\"activeStepIndex\"\n        :visibleSteps=\"visibleSteps\"\n        :errorStrings=\"errorStrings\"\n        :finish=\"finish\"\n        :cancel=\"cancel\"\n        :activeStep=\"activeStep\"\n      >\n        <div\n          v-for=\"(err,idx) in errorStrings\"\n          :key=\"idx\"\n        >\n          <Banner\n            color=\"error\"\n            :label=\"err\"\n            :closable=\"true\"\n            class=\"footer-error\"\n            @close=\"errors.splice(idx, 1)\"\n          />\n        </div>\n        <div\n          id=\"wizard-footer-controls\"\n          class=\"controls-row pt-20\"\n        >\n          <slot\n            name=\"cancel\"\n            :cancel=\"cancel\"\n          >\n            <button\n              type=\"button\"\n              class=\"btn role-secondary\"\n              @click=\"cancel\"\n            >\n              <t k=\"generic.cancel\" />\n            </button>\n          </slot>\n          <div class=\"controls-steps\">\n            <slot\n              v-if=\"showPrevious\"\n              name=\"back\"\n              :back=\"back\"\n            >\n              <button\n                :disabled=\"!canPrevious || (!editFirstStep && activeStepIndex===1)\"\n                type=\"button\"\n                class=\"btn role-secondary\"\n                @click=\"back()\"\n              >\n                <t k=\"wizard.previous\" />\n              </button>\n            </slot>\n            <slot\n              v-if=\"activeStepIndex === visibleSteps.length-1\"\n              name=\"finish\"\n              :finish=\"finish\"\n            >\n              <AsyncButton\n                :disabled=\"!activeStep.ready\"\n                :mode=\"finishMode\"\n                @click=\"finish\"\n              />\n            </slot>\n            <slot\n              v-else\n              name=\"next\"\n              :next=\"next\"\n            >\n              <button\n                :disabled=\"!canNext\"\n                type=\"button\"\n                :class=\"nextButtonStyle\"\n                @click=\"next()\"\n              >\n                <t :k=\"nextButtonLabel\" />\n              </button>\n            </slot>\n          </div>\n        </div>\n      </slot>\n    </div>\n  </div>\n</template>\n\n<style lang='scss' scoped>\n$spacer: 10px;\n\n.outer-container {\n  display: flex;\n  flex-direction: column;\n  flex: 1;\n  padding: 0;\n  justify-content: space-between;\n}\n\n.header {\n  display: flex;\n  align-content: space-between;\n  align-items: center;\n  margin-bottom: 2*$spacer;\n\n  border-bottom: var(--header-border-size) solid var(--header-border);\n\n $minHeight: 60px;\n  & > .title {\n    flex: 1;\n    min-height: $minHeight;\n    display: flex;\n  }\n  .step-sequence {\n    flex:1;\n    min-height: $minHeight;\n    display: flex;\n\n    .steps {\n      flex: 1;\n      margin: 0 30px;\n      display:flex;\n      justify-content: space-between;\n      list-style-type:none;\n      padding: 0;\n\n      &:focus{\n          outline:none;\n          box-shadow: none;\n      }\n\n      & li.step{\n        display: flex;\n        flex-direction: row;\n        flex-grow: 1;\n        align-items: center;\n\n        & > span > span:last-of-type {\n          padding-bottom: 0;\n        }\n\n        &:last-of-type{\n          flex-grow: 0;\n        }\n\n        & .controls {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          width: 40px;\n          overflow: visible;\n          padding-top: 7px;\n\n          & > span {\n            padding-bottom: 3px;\n            margin-bottom: 5px;\n            white-space: nowrap;\n          }\n        }\n\n        &.active .controls{\n          color: var(--primary);\n        }\n\n        &:not(.disabled){\n          & .controls:hover>*{\n              color: var(--primary) !important;\n              cursor: pointer;\n          }\n        }\n\n        &:not(.active) {\n          & .controls>*{\n            color: var(--input-disabled-text);\n            text-decoration: none;\n          }\n        }\n      }\n\n      & .divider {\n        flex-basis: 100%;\n        border-top: 1px solid var(--border);\n        position: relative;\n        top: 17px;\n\n        .cru__content & {\n          top: 13px;\n        }\n      }\n    }\n  }\n\n  .choice-banner {\n\n    flex-basis: 40%;\n    display: flex;\n    align-items: center;\n    margin-bottom: $spacer;\n\n    &.selected{\n      background-color: var(--accent-btn);\n    }\n\n    &.top {\n\n      H2 {\n        margin: 0px;\n      }\n\n      .title{\n        display: flex;\n        align-items: center;\n        justify-content: space-evenly;\n        position: relative;\n\n        & > .subtitle {\n          margin-right: 20px;\n        }\n      }\n\n      .subtitle{\n        display: flex;\n        flex-direction: column;\n        & .subtext {\n          color: var(--input-label);\n        }\n      }\n\n    }\n\n    &:not(.top){\n      box-shadow: 0px 0px 12px 3px var(--box-bg);\n      flex-direction: row;\n      align-items: center;\n      justify-content: start;\n      &:hover{\n        outline: var(--outline-width) solid var(--outline);\n        cursor: pointer;\n      }\n    }\n\n    & .round-image {\n      min-width: 50px;\n      height: 50px;\n      margin: 10px 10px 10px 0;\n      border-radius: 50%;\n      overflow: hidden;\n      .logo {\n        min-width: 50px;\n        height: 50px;\n      }\n    }\n  }\n}\n\n.step-container {\n  position: relative; // Important for loading indicator in chart's with custom form components\n  flex: 1 1 auto;\n  height: 0;\n  overflow-y: auto;\n  padding: 20px 2px 2px 2px; // Handle borders flush against edge\n  display: flex;\n  flex-direction: column;\n\n  &__step {\n    overflow: hidden;\n    display: flex;\n    flex-direction: column;\n    flex: 1;\n  }\n}\n\n// We have to account for the absolute position of the .controls-row\n.footer-error {\n  margin-top: -40px;\n  margin-bottom: 70px;\n}\n\n  .controls-row {\n\n    // Overrides outlet padding\n    margin-left: -$space-m;\n    margin-right: -$space-m;\n    padding: $space-s $space-m;\n\n    display: flex;\n    justify-content: space-between;\n    padding-top: $spacer;\n\n    border-top: var(--header-border-size) solid var(--header-border);\n    position: absolute;\n    bottom: 0;\n    width: 100%;\n    background: var(--body-bg);\n    .controls-steps {\n\n      .btn {\n        margin-left: $spacer;\n      }\n    }\n  }\n\n</style>\n"]}]}