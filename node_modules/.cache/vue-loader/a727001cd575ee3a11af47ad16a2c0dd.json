{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AuthBanner.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AuthBanner.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCB7IEJhbm5lciB9IGZyb20gJ0Bjb21wb25lbnRzL0Jhbm5lcic7CmltcG9ydCBEaXNhYmxlQXV0aFByb3ZpZGVyTW9kYWwgZnJvbSAnQHNoZWxsL2NvbXBvbmVudHMvRGlzYWJsZUF1dGhQcm92aWRlck1vZGFsJzsKCmV4cG9ydCBkZWZhdWx0IHsKICBjb21wb25lbnRzOiB7CiAgICBCYW5uZXIsCiAgICBEaXNhYmxlQXV0aFByb3ZpZGVyTW9kYWwKICB9LAoKICBwcm9wczogewogICAgdEFyZ3M6IHsKICAgICAgdHlwZTogICAgIE9iamVjdCwKICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgIGRlZmF1bHQ6ICAoKSA9PiB7IH0sCiAgICB9LAogICAgZGlzYWJsZTogewogICAgICB0eXBlOiAgICAgRnVuY3Rpb24sCiAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICBkZWZhdWx0OiAgKCkgPT4geyB9LAogICAgfSwKICAgIGVkaXQ6IHsKICAgICAgdHlwZTogICAgIEZ1bmN0aW9uLAogICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgZGVmYXVsdDogICgpID0+IHsgfSwKICAgIH0KICB9LAoKICBjb21wdXRlZDogewogICAgdmFsdWVzKCkgewogICAgICByZXR1cm4gT2JqZWN0LmVudHJpZXModGhpcy50YWJsZSk7CiAgICB9CiAgfSwKCiAgbWV0aG9kczogewogICAgc2hvd0Rpc2FibGVNb2RhbCgpIHsKICAgICAgdGhpcy4kcmVmcy5kaXNhYmxlQXV0aFByb3ZpZGVyTW9kYWwuc2hvdygpOwogICAgfQogIH0sCn07Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AuthBanner.vue"], "names": [], "mappings": ";AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEjF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IACrB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IACrB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IACrB;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C;EACF,CAAC;AACH,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AuthBanner.vue", "sourceRoot": "", "sourcesContent": ["\n<script>\nimport { Banner } from '@components/Banner';\nimport DisableAuthProviderModal from '@shell/components/DisableAuthProviderModal';\n\nexport default {\n  components: {\n    Banner,\n    DisableAuthProviderModal\n  },\n\n  props: {\n    tArgs: {\n      type:     Object,\n      required: true,\n      default:  () => { },\n    },\n    disable: {\n      type:     Function,\n      required: true,\n      default:  () => { },\n    },\n    edit: {\n      type:     Function,\n      required: true,\n      default:  () => { },\n    }\n  },\n\n  computed: {\n    values() {\n      return Object.entries(this.table);\n    }\n  },\n\n  methods: {\n    showDisableModal() {\n      this.$refs.disableAuthProviderModal.show();\n    }\n  },\n};\n</script>\n\n<template>\n  <div>\n    <Banner\n      color=\"success clearfix\"\n      class=\"banner\"\n    >\n      <div class=\"text\">\n        {{ t('authConfig.stateBanner.enabled', tArgs) }}\n      </div>\n      <slot name=\"actions\" />\n      <button\n        type=\"button\"\n        class=\"btn-sm role-primary\"\n        @click=\"edit\"\n      >\n        {{ t('action.edit') }}\n      </button>\n      <button\n        type=\"button\"\n        class=\"ml-10 btn-sm role-primary bg-error\"\n        @click=\"showDisableModal\"\n      >\n        {{ t('generic.disable') }}\n      </button>\n    </Banner>\n\n    <table\n      v-if=\"!!$slots.rows\"\n      class=\"values\"\n    >\n      <slot name=\"rows\" />\n    </table>\n\n    <slot\n      v-if=\"$slots.footer\"\n      name=\"footer\"\n    />\n\n    <DisableAuthProviderModal\n      ref=\"disableAuthProviderModal\"\n      @disable=\"disable\"\n    />\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.banner {\n  display: flex;\n    align-items: center;\n  .text {\n    flex: 1;\n  }\n}\n\n.values {\n  tr td:not(:first-of-type) {\n    padding-left: 10px;\n  }\n}\n\n</style>\n"]}]}