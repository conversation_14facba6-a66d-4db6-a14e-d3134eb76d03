{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/ProjectRow.vue?vue&type=template&id=db070704&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/ProjectRow.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdgogICAgdi1pZj0idHlwZU9wdGlvbiIKICAgIGNsYXNzPSJyb3ciCiAgPgogICAgPFNlbGVjdAogICAgICA6dmFsdWU9InR5cGUiCiAgICAgIGNsYXNzPSJtci0xMCIKICAgICAgOm1vZGU9Im1vZGUiCiAgICAgIDpvcHRpb25zPSJ0eXBlcyIKICAgICAgZGF0YS10ZXN0aWQ9InByb2plY3Ryb3ctdHlwZS1pbnB1dCIKICAgICAgQHVwZGF0ZTp2YWx1ZT0idXBkYXRlVHlwZSgkZXZlbnQpIgogICAgLz4KICAgIDxVbml0SW5wdXQKICAgICAgOnZhbHVlPSJyZXNvdXJjZVF1b3RhTGltaXRbdHlwZV0iCiAgICAgIGNsYXNzPSJtci0xMCIKICAgICAgOm1vZGU9Im1vZGUiCiAgICAgIDpwbGFjZWhvbGRlcj0idHlwZU9wdGlvbi5wbGFjZWhvbGRlciIKICAgICAgOmluY3JlbWVudD0idHlwZU9wdGlvbi5pbmNyZW1lbnQiCiAgICAgIDppbnB1dC1leHBvbmVudD0idHlwZU9wdGlvbi5pbnB1dEV4cG9uZW50IgogICAgICA6YmFzZS11bml0PSJ0eXBlT3B0aW9uLmJhc2VVbml0IgogICAgICA6b3V0cHV0LW1vZGlmaWVyPSJ0cnVlIgogICAgICBkYXRhLXRlc3RpZD0icHJvamVjdHJvdy1wcm9qZWN0LXF1b3RhLWlucHV0IgogICAgICBAdXBkYXRlOnZhbHVlPSJ1cGRhdGVRdW90YUxpbWl0KCdyZXNvdXJjZVF1b3RhJywgdHlwZSwgJGV2ZW50KSIKICAgIC8+CiAgICA8VW5pdElucHV0CiAgICAgIDp2YWx1ZT0ibmFtZXNwYWNlRGVmYXVsdFJlc291cmNlUXVvdGFMaW1pdFt0eXBlXSIKICAgICAgOm1vZGU9Im1vZGUiCiAgICAgIDpwbGFjZWhvbGRlcj0idHlwZU9wdGlvbi5wbGFjZWhvbGRlciIKICAgICAgOmluY3JlbWVudD0idHlwZU9wdGlvbi5pbmNyZW1lbnQiCiAgICAgIDppbnB1dC1leHBvbmVudD0idHlwZU9wdGlvbi5pbnB1dEV4cG9uZW50IgogICAgICA6YmFzZS11bml0PSJ0eXBlT3B0aW9uLmJhc2VVbml0IgogICAgICA6b3V0cHV0LW1vZGlmaWVyPSJ0cnVlIgogICAgICBkYXRhLXRlc3RpZD0icHJvamVjdHJvdy1uYW1lc3BhY2UtcXVvdGEtaW5wdXQiCiAgICAgIEB1cGRhdGU6dmFsdWU9InVwZGF0ZVF1b3RhTGltaXQoJ25hbWVzcGFjZURlZmF1bHRSZXNvdXJjZVF1b3RhJywgdHlwZSwgJGV2ZW50KSIKICAgIC8+CiAgPC9kaXY+Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/ProjectRow.vue"], "names": [], "mappings": ";EAsEE,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ;IACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChF,CAAC;EACH,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/ProjectRow.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport Select from '@shell/components/form/Select';\nimport UnitInput from '@shell/components/form/UnitInput';\nimport { ROW_COMPUTED } from './shared';\n\nexport default {\n  emits: ['type-change'],\n\n  components: { Select, UnitInput },\n\n  props: {\n    mode: {\n      type:     String,\n      required: true,\n    },\n    types: {\n      type:    Array,\n      default: () => []\n    },\n    type: {\n      type:    String,\n      default: ''\n    },\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  computed: {\n    ...ROW_COMPUTED,\n\n    resourceQuotaLimit: {\n      get() {\n        return this.value.spec.resourceQuota?.limit || {};\n      },\n    },\n\n    namespaceDefaultResourceQuotaLimit: {\n      get() {\n        return this.value.spec.namespaceDefaultResourceQuota?.limit || {};\n      },\n    }\n  },\n\n  methods: {\n    updateType(type) {\n      if (typeof this.value.spec.resourceQuota?.limit[this.type] !== 'undefined') {\n        delete this.value.spec.resourceQuota.limit[this.type];\n      }\n      if (typeof this.value.spec.namespaceDefaultResourceQuota?.limit[this.type] !== 'undefined') {\n        delete this.value.spec.namespaceDefaultResourceQuota.limit[this.type];\n      }\n\n      this.$emit('type-change', type);\n    },\n\n    updateQuotaLimit(prop, type, val) {\n      if (!this.value.spec[prop]) {\n        this.value.spec[prop] = { limit: { } };\n      }\n\n      this.value.spec[prop].limit[type] = val;\n    }\n  },\n};\n</script>\n<template>\n  <div\n    v-if=\"typeOption\"\n    class=\"row\"\n  >\n    <Select\n      :value=\"type\"\n      class=\"mr-10\"\n      :mode=\"mode\"\n      :options=\"types\"\n      data-testid=\"projectrow-type-input\"\n      @update:value=\"updateType($event)\"\n    />\n    <UnitInput\n      :value=\"resourceQuotaLimit[type]\"\n      class=\"mr-10\"\n      :mode=\"mode\"\n      :placeholder=\"typeOption.placeholder\"\n      :increment=\"typeOption.increment\"\n      :input-exponent=\"typeOption.inputExponent\"\n      :base-unit=\"typeOption.baseUnit\"\n      :output-modifier=\"true\"\n      data-testid=\"projectrow-project-quota-input\"\n      @update:value=\"updateQuotaLimit('resourceQuota', type, $event)\"\n    />\n    <UnitInput\n      :value=\"namespaceDefaultResourceQuotaLimit[type]\"\n      :mode=\"mode\"\n      :placeholder=\"typeOption.placeholder\"\n      :increment=\"typeOption.increment\"\n      :input-exponent=\"typeOption.inputExponent\"\n      :base-unit=\"typeOption.baseUnit\"\n      :output-modifier=\"true\"\n      data-testid=\"projectrow-namespace-quota-input\"\n      @update:value=\"updateQuotaLimit('namespaceDefaultResourceQuota', type, $event)\"\n    />\n  </div>\n</template>\n\n<style lang='scss' scoped>\n  .row {\n    display: flex;\n    flex-direction: row;\n    justify-content: space-evenly;\n  }\n</style>\n"]}]}