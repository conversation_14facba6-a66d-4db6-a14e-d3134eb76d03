{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GroupPanel.vue?vue&type=template&id=7f7a1e08&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GroupPanel.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdiBjbGFzcz0iZ3JvdXAtcGFuZWwtb3V0ZXIiPgogICAgPGRpdiBjbGFzcz0iZ3JvdXAtcGFuZWwiPgogICAgICA8ZGl2IGNsYXNzPSJncm91cC1wYW5lbC10aXRsZSI+CiAgICAgICAgPHQKICAgICAgICAgIHYtaWY9ImxhYmVsS2V5IgogICAgICAgICAgOms9ImxhYmVsS2V5IgogICAgICAgIC8+CiAgICAgICAgPHRlbXBsYXRlIHYtZWxzZS1pZj0ibGFiZWwiPgogICAgICAgICAge3sgbGFiZWwgfX0KICAgICAgICA8L3RlbXBsYXRlPgogICAgICA8L2Rpdj4KICAgICAgPGRpdiBjbGFzcz0iZ3JvdXAtcGFuZWwtY29udGVudCI+CiAgICAgICAgPHNsb3QgLz4KICAgICAgPC9kaXY+CiAgICA8L2Rpdj4KICA8L2Rpdj4K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GroupPanel.vue"], "names": [], "mappings": ";EAqBE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC;UACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GroupPanel.vue", "sourceRoot": "", "sourcesContent": ["<script>\nexport default {\n  props: {\n    /**\n     * Label for the group\n     */\n    label: {\n      type:    String,\n      default: null\n    },\n    /**\n     * The i18n key to use for the label\n     */\n    labelKey: {\n      type:    String,\n      default: null\n    },\n  }\n};\n</script>\n<template>\n  <div class=\"group-panel-outer\">\n    <div class=\"group-panel\">\n      <div class=\"group-panel-title\">\n        <t\n          v-if=\"labelKey\"\n          :k=\"labelKey\"\n        />\n        <template v-else-if=\"label\">\n          {{ label }}\n        </template>\n      </div>\n      <div class=\"group-panel-content\">\n        <slot />\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .group-panel {\n    border: 1px solid var(--border);\n    border-radius: 5px;\n    padding: 10px;\n    position: relative;\n    margin-top: 10px;\n    .group-panel-title {\n      position: absolute;\n      top: -7px;\n      background-color: var(--body-bg);\n      padding: 0 5px;\n    }\n    .group-panel-content {\n      position: relative;\n    }\n  }\n</style>\n"]}]}