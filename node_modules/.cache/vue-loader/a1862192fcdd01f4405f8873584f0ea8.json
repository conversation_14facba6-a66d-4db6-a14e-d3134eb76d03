{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/LabeledTooltip/LabeledTooltip.vue?vue&type=template&id=7ac56810&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/LabeledTooltip/LabeledTooltip.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdgogICAgcmVmPSJjb250YWluZXIiCiAgICBjbGFzcz0ibGFiZWxlZC10b29sdGlwIgogICAgOmNsYXNzPSJ7W3N0YXR1c106IHRydWUsIGhvdmVyYWJsZTogaG92ZXJ9IgogID4KICAgIDx0ZW1wbGF0ZSB2LWlmPSJob3ZlciI+CiAgICAgIDxpCiAgICAgICAgdi1jbGVhbi10b29sdGlwPSJ0b29sdGlwQ29udGVudCIKICAgICAgICB2LXN0cmlwcGVkLWFyaWEtbGFiZWw9ImlzT2JqZWN0KHZhbHVlKSA/IHZhbHVlLmNvbnRlbnQgOiB2YWx1ZSIKICAgICAgICA6Y2xhc3M9InsnaG92ZXInOiF2YWx1ZSwgW2ljb25DbGFzc106IHRydWV9IgogICAgICAgIGNsYXNzPSJpY29uIHN0YXR1cy1pY29uIgogICAgICAgIHRhYmluZGV4PSIwIgogICAgICAvPgogICAgPC90ZW1wbGF0ZT4KICAgIDx0ZW1wbGF0ZSB2LWVsc2U+CiAgICAgIDxpCiAgICAgICAgOmNsYXNzPSJ7J2hvdmVyJzohdmFsdWV9IgogICAgICAgIGNsYXNzPSJpY29uIHN0YXR1cy1pY29uIgogICAgICAvPgogICAgICA8ZGl2CiAgICAgICAgdi1pZj0idmFsdWUiCiAgICAgICAgY2xhc3M9InRvb2x0aXAiCiAgICAgICAgeC1wbGFjZW1lbnQ9ImJvdHRvbSIKICAgICAgPgogICAgICAgIDxkaXYgY2xhc3M9InRvb2x0aXAtYXJyb3ciIC8+CiAgICAgICAgPGRpdiBjbGFzcz0idG9vbHRpcC1pbm5lciI+CiAgICAgICAgICB7eyB2YWx1ZSB9fQogICAgICAgIDwvZGl2PgogICAgICA8L2Rpdj4KICAgIDwvdGVtcGxhdGU+CiAgPC9kaXY+Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/LabeledTooltip/LabeledTooltip.vue"], "names": [], "mappings": ";EAsDE,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5C;IACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC;QACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC;QACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC;MACD,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB;QACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/LabeledTooltip/LabeledTooltip.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent } from 'vue';\n\nexport default defineComponent({\n  props: {\n    /**\n     * The Labeled Tooltip value.\n     */\n    value: {\n      type:    [String, Object],\n      default: null\n    },\n\n    /**\n     * The status for the Labeled Tooltip. Controls the Labeled Tooltip class.\n     * @values info, success, warning, error\n     */\n    status: {\n      type:    String,\n      default: 'error'\n    },\n\n    /**\n     * Displays the Labeled Tooltip on mouse hover.\n     */\n    hover: {\n      type:    Boolean,\n      default: true\n    }\n  },\n  computed: {\n    iconClass(): string {\n      return this.status === 'error' ? 'icon-warning' : 'icon-info';\n    },\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    tooltipContent(): {[key: string]: any} | string {\n      if (this.isObject(this.value)) {\n        return {\n          ...{ content: this.value.content, popperClass: [`tooltip-${ status }`] }, ...this.value, triggers: ['hover', 'touch', 'focus']\n        };\n      }\n\n      return this.value ? { content: this.value, triggers: ['hover', 'touch', 'focus'] } : '';\n    }\n  },\n  methods: {\n    isObject(value: string | Record<string, unknown>): value is Record<string, unknown> {\n      return typeof value === 'object' && value !== null && !!value.content;\n    }\n  }\n});\n</script>\n\n<template>\n  <div\n    ref=\"container\"\n    class=\"labeled-tooltip\"\n    :class=\"{[status]: true, hoverable: hover}\"\n  >\n    <template v-if=\"hover\">\n      <i\n        v-clean-tooltip=\"tooltipContent\"\n        v-stripped-aria-label=\"isObject(value) ? value.content : value\"\n        :class=\"{'hover':!value, [iconClass]: true}\"\n        class=\"icon status-icon\"\n        tabindex=\"0\"\n      />\n    </template>\n    <template v-else>\n      <i\n        :class=\"{'hover':!value}\"\n        class=\"icon status-icon\"\n      />\n      <div\n        v-if=\"value\"\n        class=\"tooltip\"\n        x-placement=\"bottom\"\n      >\n        <div class=\"tooltip-arrow\" />\n        <div class=\"tooltip-inner\">\n          {{ value }}\n        </div>\n      </div>\n    </template>\n  </div>\n</template>\n\n<style lang='scss'>\n.labeled-tooltip {\n    position: absolute;\n    width: 100%;\n    height: 100%;\n    left: 0;\n    top: 0;\n\n    &.hoverable {\n      height: 0%;\n    }\n\n     .status-icon {\n        position:  absolute;\n        right: 30px;\n        top: $input-padding-lg;\n        z-index: z-index(hoverOverContent);\n     }\n\n    @mixin tooltipColors($color) {\n        .status-icon {\n            color: $color;\n        }\n    }\n\n    &.error {\n        @include tooltipColors(var(--error));\n\n        .status-icon {\n          top: 7px;\n          right: 5px;\n        }\n    }\n\n    &.warning {\n        @include tooltipColors(var(--warning));\n    }\n\n    &.success {\n        @include tooltipColors(var(--success));\n    }\n}\n\n// Ensure code blocks inside tootips don't look awful\n.v-popper__popper.v-popper--theme-tooltip {\n  .v-popper__inner {\n    pre {\n      padding: 2px;\n      vertical-align: middle;\n    }\n  }\n}\n</style>\n"]}]}