{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ExplorerProjectsNamespaces.vue?vue&type=style&index=1&id=a286d718&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ExplorerProjectsNamespaces.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIC5wc2EtdG9vbHRpcCB7DQogICAgLy8gVGhlc2UgY291bGQgcG9wIHVwIGEgbG90IGFzIHRoZSBtb3VzZSBtb3ZlcyBhcm91bmQsIGtlZXAgdGhlbSBhcyBzbWFsbCBhbmQgdW5pbnRydXNpdmUgYXMgcG9zc2libGUNCiAgICAvLyAoZWFzaWVyIHRvIHRlc3Qgd2l0aCB2LWNsZWFuLXRvb2x0aXA9InsgY29udGVudDogZ2V0UFNBKHJvdyksIGF1dG9IaWRlOiBmYWxzZSwgc2hvdzogdHJ1ZSB9IikNCiAgICBtYXJnaW46IDNweCAwOw0KICAgIHBhZGRpbmc6IDAgOHB4IDAgMjJweDsNCiAgfQ0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ExplorerProjectsNamespaces.vue"], "names": [], "mappings": ";EA6lBE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC/F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EACvB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ExplorerProjectsNamespaces.vue", "sourceRoot": "", "sourcesContent": ["<script>\r\nimport { mapGetters } from 'vuex';\r\nimport ResourceTable, { defaultTableSortGenerationFn } from '@shell/components/ResourceTable';\r\nimport { STATE, AGE, NAME, NS_SNAPSHOT_QUOTA } from '@shell/config/table-headers';\r\nimport { uniq } from '@shell/utils/array';\r\nimport { MANAGEMENT, NAMESPACE, VIRTUAL_TYPES, HCI } from '@shell/config/types';\r\nimport { PROJECT_ID, FLAT_VIEW } from '@shell/config/query-params';\r\nimport { PanelLocation, ExtensionPoint } from '@shell/core/types';\r\nimport ExtensionPanel from '@shell/components/ExtensionPanel';\r\nimport Masthead from '@shell/components/ResourceList/Masthead';\r\nimport { mapPref, GROUP_RESOURCES, ALL_NAMESPACES, DEV } from '@shell/store/prefs';\r\nimport MoveModal from '@shell/components/MoveModal';\r\nimport ButtonMultiAction from '@shell/components/ButtonMultiAction.vue';\r\n\r\nimport { NAMESPACE_FILTER_ALL_ORPHANS } from '@shell/utils/namespace-filter';\r\nimport ResourceFetch from '@shell/mixins/resource-fetch';\r\nimport DOMPurify from 'dompurify';\r\nimport { HARVESTER_NAME as HARVESTER } from '@shell/config/features';\r\n\r\nexport default {\r\n  name:       'ListProjectNamespace',\r\n  components: {\r\n    ExtensionPanel,\r\n    Masthead,\r\n    MoveModal,\r\n    ResourceTable,\r\n    ButtonMultiAction,\r\n  },\r\n  mixins: [ResourceFetch],\r\n\r\n  props: {\r\n    createProjectLocationOverride: {\r\n      type:    Object,\r\n      default: () => null\r\n    },\r\n\r\n    createNamespaceLocationOverride: {\r\n      type:    Object,\r\n      default: () => null\r\n    }\r\n  },\r\n\r\n  async fetch() {\r\n    const inStore = this.$store.getters['currentStore'](NAMESPACE);\r\n\r\n    this.harvesterResourceQuotaSchema = this.$store.getters[`${ inStore }/schemaFor`](HCI.RESOURCE_QUOTA);\r\n    this.schema = this.$store.getters[`${ inStore }/schemaFor`](NAMESPACE);\r\n    this.projectSchema = this.$store.getters[`management/schemaFor`](MANAGEMENT.PROJECT);\r\n\r\n    if ( !this.schema ) {\r\n      // clusterReady:   When switching routes, it will cause clusterReady to change, causing itself to repeat rendering。\r\n      // this.$store.dispatch('loadingError', `Type ${ NAMESPACE } not found`);\r\n\r\n      return;\r\n    }\r\n\r\n    await this.$fetchType(NAMESPACE);\r\n    this.projects = await this.$store.dispatch('management/findAll', { type: MANAGEMENT.PROJECT, opt: { force: true } });\r\n  },\r\n\r\n  data() {\r\n    return {\r\n      loadResources:                [NAMESPACE],\r\n      loadIndeterminate:            true,\r\n      harvesterResourceQuotaSchema: null,\r\n      schema:                       null,\r\n      projects:                     [],\r\n      projectSchema:                null,\r\n      extensionType:                ExtensionPoint.PANEL,\r\n      extensionLocation:            PanelLocation.RESOURCE_LIST,\r\n      MANAGEMENT,\r\n      VIRTUAL_TYPES,\r\n      defaultCreateProjectLocation: {\r\n        name:   'c-cluster-product-resource-create',\r\n        params: {\r\n          product:  this.$store.getters['currentProduct'].name,\r\n          resource: MANAGEMENT.PROJECT\r\n        },\r\n      }\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    ...mapGetters(['currentCluster', 'currentProduct']),\r\n    namespaces() {\r\n      const inStore = this.$store.getters['currentStore'](NAMESPACE);\r\n\r\n      return this.$store.getters[`${ inStore }/all`](NAMESPACE);\r\n    },\r\n    loading() {\r\n      return !this.currentCluster || this.namespaces.length ? false : this.$fetchState.pending;\r\n    },\r\n    showIncrementalLoadingIndicator() {\r\n      return this.perfConfig?.incrementalLoading?.enabled;\r\n    },\r\n    isNamespaceCreatable() {\r\n      return (this.schema?.collectionMethods || []).includes('POST');\r\n    },\r\n    isHarvester() {\r\n      return this.$store.getters['currentProduct'].inStore === HARVESTER;\r\n    },\r\n    headers() {\r\n      const headers = [\r\n        STATE,\r\n        NAME,\r\n      ];\r\n\r\n      if (this.groupPreference === 'none') {\r\n        const projectHeader = {\r\n          name:  'project',\r\n          label: this.t('tableHeaders.project'),\r\n          value: 'project.nameDisplay',\r\n          sort:  ['projectNameSort', 'nameSort'],\r\n        };\r\n\r\n        headers.push(projectHeader);\r\n      }\r\n\r\n      if (this.isHarvester && this.harvesterResourceQuotaSchema) {\r\n        headers.push(NS_SNAPSHOT_QUOTA);\r\n      }\r\n\r\n      headers.push(AGE);\r\n\r\n      return headers;\r\n    },\r\n    projectIdsWithNamespaces() {\r\n      const ids = this.rows\r\n        .map((row) => row.projectId)\r\n        .filter((id) => id);\r\n\r\n      return uniq(ids);\r\n    },\r\n    clusterProjects() {\r\n      const clusterId = this.currentCluster.id;\r\n\r\n      // Get the list of projects from the store so that the list\r\n      // is updated if a new project is created or removed.\r\n      const projectsInAllClusters = this.$store.getters['management/all'](MANAGEMENT.PROJECT);\r\n\r\n      if (this.currentProduct?.customNamespaceFilter && this.currentProduct?.inStore && this.$store.getters[`${ this.currentProduct.inStore }/filterProject`]) {\r\n        return this.$store.getters[`${ this.currentProduct.inStore }/filterProject`];\r\n      }\r\n\r\n      const clustersInProjects = projectsInAllClusters.filter((project) => project.spec.clusterName === clusterId);\r\n\r\n      return clustersInProjects;\r\n    },\r\n    projectsWithoutNamespaces() {\r\n      return this.activeProjects.filter((project) => {\r\n        return !this.projectIdsWithNamespaces.find((item) => project?.id?.endsWith(`/${ item }`));\r\n      });\r\n    },\r\n    // We're using this because we need to show projects as groups even if the project doesn't have any namespaces.\r\n    rowsWithFakeNamespaces() {\r\n      const fakeRows = this.projectsWithoutNamespaces.map((project) => {\r\n        return {\r\n          groupByLabel:     `${ ('resourceTable.groupLabel.notInAProject') }-${ project.id }`,\r\n          isFake:           true,\r\n          mainRowKey:       project.id,\r\n          nameDisplay:      project.spec?.displayName, // Enable filtering by the project name\r\n          project,\r\n          availableActions: []\r\n        };\r\n      });\r\n\r\n      if (this.showMockNotInProjectGroup) {\r\n        fakeRows.push( {\r\n          groupByLabel: this.t('resourceTable.groupLabel.notInAProject'), // Same as the groupByLabel for the namespace model\r\n          mainRowKey:   'fake-empty',\r\n        });\r\n      }\r\n\r\n      return [...this.rows, ...fakeRows];\r\n    },\r\n    createProjectLocation() {\r\n      return this.createProjectLocationOverride || this.defaultCreateProjectLocation;\r\n    },\r\n    groupPreference: mapPref(GROUP_RESOURCES),\r\n    activeNamespaceFilters() {\r\n      return this.$store.getters['activeNamespaceFilters'];\r\n    },\r\n    activeProjectFilters() {\r\n      const activeProjects = {};\r\n\r\n      for (const filter of this.activeNamespaceFilters) {\r\n        const [type, id] = filter.split('://', 2);\r\n\r\n        if (type === 'project') {\r\n          activeProjects[id] = true;\r\n        }\r\n      }\r\n\r\n      return activeProjects;\r\n    },\r\n    activeProjects() {\r\n      const namespaceFilters = this.$store.getters['activeNamespaceFilters'];\r\n\r\n      if (namespaceFilters.includes(NAMESPACE_FILTER_ALL_ORPHANS) && Object.keys(this.activeProjectFilters).length === 0) {\r\n        // If the user wants to only see namespaces that are not\r\n        // in a project, don't show any projects.\r\n        return [];\r\n      }\r\n\r\n      // If the user is not filtering by any projects or namespaces, return\r\n      // all projects in the cluster.\r\n      if (!this.userIsFilteringForSpecificNamespaceOrProject()) {\r\n        return this.clusterProjects;\r\n      }\r\n\r\n      // Filter out projects that are not selected in the top nav.\r\n      return this.clusterProjects.filter((projectData) => {\r\n        const projectId = projectData.id.split('/')[1];\r\n\r\n        return !!this.activeProjectFilters[projectId];\r\n      });\r\n    },\r\n    activeNamespaces() {\r\n      // Apply namespace filters from the top nav.\r\n      const activeNamespaces = this.$store.getters['namespaces']();\r\n\r\n      return this.namespaces.filter((namespaceData) => {\r\n        return !!activeNamespaces[namespaceData.metadata.name];\r\n      });\r\n    },\r\n    filteredRows() {\r\n      return this.groupPreference === 'none' ? this.rows : this.rowsWithFakeNamespaces;\r\n    },\r\n    rows() {\r\n      let isDev;\r\n\r\n      try {\r\n        isDev = this.$store.getters['prefs/get'](ALL_NAMESPACES);\r\n      } catch {\r\n        isDev = this.$store.getters['prefs/get'](DEV);\r\n      }\r\n\r\n      if (isDev) {\r\n        // If all namespaces options are turned on in the user preferences,\r\n        // return all namespaces including system namespaces and RBAC\r\n        // management namespaces.\r\n        return this.activeNamespaces;\r\n      }\r\n\r\n      return this.activeNamespaces.filter((namespace) => {\r\n        const isSettingSystemNamespace = this.$store.getters['systemNamespaces'].includes(namespace.metadata.name);\r\n        const systemNS = namespace.isSystem || namespace.isFleetManaged || isSettingSystemNamespace;\r\n\r\n        return this.currentProduct?.hideSystemResources ? !systemNS : true;\r\n      });\r\n    },\r\n\r\n    canSeeProjectlessNamespaces() {\r\n      return this.currentCluster.canUpdate;\r\n    },\r\n\r\n    showMockNotInProjectGroup() {\r\n      if (!this.canSeeProjectlessNamespaces) {\r\n        return false;\r\n      }\r\n\r\n      const someNamespacesAreNotInProject = !this.rows.some((row) => !row.project);\r\n\r\n      // Hide the \"Not in a Project\" group if the user is filtering\r\n      // for specific namespaces or projects.\r\n      const usingSpecificFilter = this.userIsFilteringForSpecificNamespaceOrProject();\r\n\r\n      return !usingSpecificFilter && someNamespacesAreNotInProject;\r\n    },\r\n\r\n    notInProjectKey() {\r\n      return this.$store.getters['i18n/t']('resourceTable.groupLabel.notInAProject');\r\n    },\r\n    showCreateNsButton() {\r\n      return this.groupPreference !== 'namespace';\r\n    }\r\n  },\r\n  methods: {\r\n    /**\r\n     * Get PSA HTML to be displayed in the tooltips\r\n     */\r\n    getPsaTooltip(row) {\r\n      const dictionary = row.psaTooltipsDescription;\r\n      const list = Object.values(dictionary)\r\n        .sort()\r\n        .map((text) => `<li>${ text }</li>`).join('');\r\n      const title = `<p>${ this.t('podSecurityAdmission.name') }: </p>`;\r\n\r\n      return `${ title }<ul class=\"psa-tooltip\">${ list }</ul>`;\r\n    },\r\n\r\n    userIsFilteringForSpecificNamespaceOrProject() {\r\n      const activeFilters = this.$store.getters['namespaceFilters'];\r\n\r\n      for (let i = 0; i < activeFilters.length; i++) {\r\n        const filter = activeFilters[i];\r\n        const filterType = filter.split('://')[0];\r\n\r\n        if (filterType === 'ns' || filterType === 'project') {\r\n          return true;\r\n        }\r\n      }\r\n\r\n      return false;\r\n    },\r\n    slotName(project) {\r\n      return `main-row:${ project.id }`;\r\n    },\r\n    createNamespaceLocation(group) {\r\n      const project = group.rows[0].project;\r\n\r\n      const location = this.createNamespaceLocationOverride ? { ...this.createNamespaceLocationOverride } : {\r\n        name:   'c-cluster-product-resource-create',\r\n        params: {\r\n          product:  this.$store.getters['currentProduct'].name,\r\n          resource: NAMESPACE\r\n        },\r\n      };\r\n\r\n      location.query = { [PROJECT_ID]: project?.metadata.name };\r\n\r\n      return location;\r\n    },\r\n\r\n    createNamespaceLocationFlatList() {\r\n      const location = this.createNamespaceLocationOverride ? { ...this.createNamespaceLocationOverride } : {\r\n        name:   'c-cluster-product-resource-create',\r\n        params: {\r\n          product:  this.$store.getters['currentProduct']?.name,\r\n          resource: NAMESPACE\r\n        },\r\n      };\r\n\r\n      location.query = { [FLAT_VIEW]: true };\r\n\r\n      return location;\r\n    },\r\n\r\n    showProjectAction(event, group) {\r\n      const project = group.rows[0].project;\r\n\r\n      this.$store.commit(`action-menu/show`, {\r\n        resources: [project],\r\n        elem:      event.target\r\n      });\r\n    },\r\n    showProjectActionButton(group) {\r\n      const project = group.rows[0].project;\r\n\r\n      return !!project;\r\n    },\r\n    projectLabel(group) {\r\n      const row = group.rows[0];\r\n\r\n      if (row.isFake) {\r\n        return DOMPurify.sanitize(\r\n          this.t('resourceTable.groupLabel.project', { name: row.project?.nameDisplay }, true),\r\n          { ALLOWED_TAGS: ['span'] }\r\n        );\r\n      }\r\n\r\n      return row.groupByLabel;\r\n    },\r\n\r\n    projectDescription(group) {\r\n      const project = group.rows[0].project;\r\n\r\n      return project?.description;\r\n    },\r\n\r\n    projectResource(group) {\r\n      const row = group.rows[0];\r\n\r\n      return row.nameDisplay || row.id || '';\r\n    },\r\n\r\n    clearSelection() {\r\n      this.$refs.table.clearSelection();\r\n    },\r\n\r\n    sortGenerationFn() {\r\n      // The sort generation function creates a unique value and is used to create a key including sort details.\r\n      // The unique key determines if the list is redrawn or a cached version is shown.\r\n      // Because we ensure the 'not in a project' group is there via a row, and timing issues, the unqiue key doesn't change\r\n      // after a namespace is removed... so the list won't update... so we need to inject a string to ensure the key is fresh\r\n      const base = defaultTableSortGenerationFn(this.schema, this.$store);\r\n\r\n      return base + (this.showMockNotInProjectGroup ? '-mock' : '');\r\n    },\r\n\r\n  }\r\n};\r\n</script>\r\n\r\n<template>\r\n  <div class=\"project-namespaces outlet\">\r\n    <Masthead\r\n      :schema=\"projectSchema\"\r\n      :type-display=\"t('projectNamespaces.label')\"\r\n      :resource=\"MANAGEMENT.PROJECT\"\r\n      :favorite-resource=\"VIRTUAL_TYPES.PROJECT_NAMESPACES\"\r\n      :create-location=\"createProjectLocation\"\r\n      :create-button-label=\"t('projectNamespaces.createProject')\"\r\n      :show-incremental-loading-indicator=\"showIncrementalLoadingIndicator\"\r\n      :load-resources=\"loadResources\"\r\n      :load-indeterminate=\"loadIndeterminate\"\r\n    >\r\n      <template\r\n        v-if=\"showCreateNsButton\"\r\n        #extraActions\r\n      >\r\n        <router-link\r\n          :to=\"createNamespaceLocationFlatList()\"\r\n          class=\"btn role-primary mr-10\"\r\n          data-testid=\"create_project_namespaces\"\r\n        >\r\n          {{ t('projectNamespaces.createNamespace') }}\r\n        </router-link>\r\n      </template>\r\n    </Masthead>\r\n    <!-- Extensions area -->\r\n    <ExtensionPanel\r\n      :resource=\"{}\"\r\n      :type=\"extensionType\"\r\n      :location=\"extensionLocation\"\r\n    />\r\n    <ResourceTable\r\n      ref=\"table\"\r\n      v-bind=\"{...$attrs, class: null }\"\r\n      class=\"table project-namespaces-table\"\r\n      :schema=\"schema\"\r\n      :headers=\"headers\"\r\n      :rows=\"filteredRows\"\r\n      :groupable=\"true\"\r\n      :sort-generation-fn=\"sortGenerationFn\"\r\n      :loading=\"loading\"\r\n      group-tooltip=\"resourceTable.groupBy.project\"\r\n      key-field=\"_key\"\r\n    >\r\n      <template #group-by=\"group\">\r\n        <div\r\n          class=\"project-bar\"\r\n          :class=\"{'has-description': projectDescription(group.group)}\"\r\n        >\r\n          <div\r\n            v-trim-whitespace\r\n            class=\"group-tab\"\r\n          >\r\n            <div\r\n              v-clean-html=\"projectLabel(group.group)\"\r\n              class=\"project-name\"\r\n            />\r\n            <div\r\n              v-if=\"projectDescription(group.group)\"\r\n              class=\"description text-muted text-small\"\r\n            >\r\n              {{ projectDescription(group.group) }}\r\n            </div>\r\n          </div>\r\n          <div class=\"right\">\r\n            <router-link\r\n              v-if=\"isNamespaceCreatable && (canSeeProjectlessNamespaces || group.group.key !== notInProjectKey)\"\r\n              class=\"create-namespace btn btn-sm role-secondary mr-5\"\r\n              :to=\"createNamespaceLocation(group.group)\"\r\n            >\r\n              {{ t('projectNamespaces.createNamespace') }}\r\n            </router-link>\r\n            <ButtonMultiAction\r\n              class=\"project-action mr-10\"\r\n              :borderless=\"true\"\r\n              :aria-label=\"t('projectNamespaces.tableActionsLabel', { resource: projectResource(group.group) })\"\r\n              :invisible=\"!showProjectActionButton(group.group)\"\r\n              @click=\"showProjectAction($event, group.group)\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </template>\r\n      <template #cell:project=\"{row}\">\r\n        <span v-if=\"row.project\">{{ row.project.nameDisplay }}</span>\r\n        <span\r\n          v-else\r\n          class=\"text-muted\"\r\n        >&ndash;</span>\r\n      </template>\r\n      <template #cell:name=\"{row}\">\r\n        <div class=\"namespace-name\">\r\n          <router-link\r\n            v-if=\"row.detailLocation && !row.hideDetailLocation\"\r\n            :to=\"row.detailLocation\"\r\n          >\r\n            {{ row.name }}\r\n          </router-link>\r\n          <span v-else>\r\n            {{ row.name }}\r\n          </span>\r\n          <i\r\n            v-if=\"row.injectionEnabled\"\r\n            v-clean-tooltip=\"t('projectNamespaces.isIstioInjectionEnabled')\"\r\n            class=\"icon icon-istio ml-5\"\r\n          />\r\n          <i\r\n            v-if=\"row.hasSystemLabels\"\r\n            v-clean-tooltip=\"getPsaTooltip(row)\"\r\n            class=\"icon icon-lock ml-5\"\r\n          />\r\n        </div>\r\n      </template>\r\n      <template\r\n        v-for=\"(project, i) in projectsWithoutNamespaces\"\r\n        :key=\"i\"\r\n        #[slotName(project)]=\"{ fullColspan }\"\r\n      >\r\n        <tr\r\n          class=\"main-row\"\r\n        >\r\n          <td\r\n            class=\"empty text-center\"\r\n            :colspan=\"fullColspan\"\r\n          >\r\n            {{ t('projectNamespaces.noNamespaces') }}\r\n          </td>\r\n        </tr>\r\n      </template>\r\n      <template #main-row:fake-empty=\"{ fullColspan }\">\r\n        <tr class=\"main-row\">\r\n          <td\r\n            class=\"empty text-center\"\r\n            :colspan=\"fullColspan\"\r\n          >\r\n            {{ t('projectNamespaces.noProjectNoNamespaces') }}\r\n          </td>\r\n        </tr>\r\n      </template>\r\n    </ResourceTable>\r\n    <MoveModal @moving=\"clearSelection\" />\r\n  </div>\r\n</template>\r\n<style lang=\"scss\" scoped>\r\n.project-namespaces {\r\n  & :deep() {\r\n    .project-namespaces-table table {\r\n      table-layout: fixed;\r\n    }\r\n\r\n    .project-name {\r\n      line-height: 30px;\r\n    }\r\n\r\n    .project-bar {\r\n      display: flex;\r\n      flex-direction: row;\r\n      justify-content: space-between;\r\n\r\n      .group-tab {\r\n        max-width: calc(100% - 230px);\r\n      }\r\n\r\n      .project-name {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n\r\n        span:first-child {\r\n          padding-right: 8px;\r\n        }\r\n\r\n        span:last-child {\r\n          text-overflow: ellipsis;\r\n          overflow: hidden;\r\n          white-space: nowrap;\r\n        }\r\n      }\r\n\r\n      &.has-description {\r\n        .right {\r\n          margin-top: 5px;\r\n        }\r\n        .group-tab {\r\n          &, &::after {\r\n              height: 50px;\r\n          }\r\n\r\n          &::after {\r\n              right: -20px;\r\n          }\r\n\r\n          .description {\r\n              margin-top: -20px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .namespace-name {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .icon-istio {\r\n        color: var(--primary);\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style lang=\"scss\">\r\n  .psa-tooltip {\r\n    // These could pop up a lot as the mouse moves around, keep them as small and unintrusive as possible\r\n    // (easier to test with v-clean-tooltip=\"{ content: getPSA(row), autoHide: false, show: true }\")\r\n    margin: 3px 0;\r\n    padding: 0 8px 0 22px;\r\n  }\r\n</style>\r\n"]}]}