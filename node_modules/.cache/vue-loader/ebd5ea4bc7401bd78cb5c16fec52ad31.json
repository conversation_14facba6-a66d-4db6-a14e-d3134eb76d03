{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Password.vue?vue&type=style&index=0&id=1cc51f5a&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Password.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLnBhc3N3b3JkIHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwoKICAgIC5sYWJlbGVkLWlucHV0IHsKICAgICAgLmFkZG9uIHsKICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgICAgICAgcGFkZGluZy1sZWZ0OiAxMnB4OwogICAgICAgIG1pbi13aWR0aDogNjVweDsKCiAgICAgICAgLmhpZGUtc2hvdzpmb2N1cy12aXNpYmxlIHsKICAgICAgICAgIEBpbmNsdWRlIGZvY3VzLW91dGxpbmU7CiAgICAgICAgICBvdXRsaW5lLW9mZnNldDogNHB4OwogICAgICAgIH0KICAgICAgfQogICAgfQogICAgLmdlblBhc3N3b3JkIHsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsKICAgIH0KICB9Cgo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Password.vue"], "names": [], "mappings": ";EAgKE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrB;MACF;IACF;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Password.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapGetters } from 'vuex';\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport { CHARSET, randomStr } from '@shell/utils/string';\nimport { copyTextToClipboard } from '@shell/utils/clipboard';\nimport { _CREATE } from '@shell/config/query-params';\n\nexport default {\n  emits: ['update:value', 'blur'],\n\n  components: { LabeledInput },\n  props:      {\n    value: {\n      default: '',\n      type:    String,\n    },\n    isRandom: {\n      default: false,\n      type:    Boolean,\n    },\n    label: {\n      default: '',\n      type:    String,\n    },\n    name: {\n      default: '',\n      type:    String\n    },\n    autocomplete: {\n      type:    String,\n      default: ''\n    },\n    required: {\n      default: false,\n      type:    Boolean,\n    },\n    ignorePasswordManagers: {\n      default: false,\n      type:    Boolean,\n    },\n    mode: {\n      type:    String,\n      default: _CREATE,\n    }\n  },\n  data() {\n    return { reveal: false };\n  },\n  computed: {\n    ...mapGetters({ t: 'i18n/t' }),\n    password: {\n      get() {\n        return this.value;\n      },\n      set(val) {\n        this.$emit('update:value', val);\n      }\n    },\n    attributes() {\n      const attributes = { };\n\n      if (this.name) {\n        attributes.id = this.name;\n        attributes.name = this.name;\n      }\n      if (this.autocomplete) {\n        attributes.autocomplete = this.autocomplete;\n      }\n\n      return attributes;\n    },\n    hideShowLabel() {\n      return this.reveal ? this.t('action.hide') : this.t('action.show');\n    }\n  },\n  watch: {\n    isRandom() {\n      if (this.isRandom) {\n        this.generatePassword();\n      }\n    }\n  },\n  created() {\n    if (this.isRandom) {\n      this.generatePassword();\n    }\n  },\n  methods: {\n    copyTextToClipboard,\n    generatePassword() {\n      this.password = randomStr(16, CHARSET.ALPHA_NUM);\n    },\n    show(reveal) {\n      this.reveal = reveal;\n    },\n    focus() {\n      this.$refs.input.$refs.value.focus();\n    },\n    hideShowFn() {\n      this.reveal ? this.reveal = false : this.reveal = true;\n    }\n  }\n};\n</script>\n\n<template>\n  <div class=\"password\">\n    <LabeledInput\n      ref=\"input\"\n      v-model:value=\"password\"\n      v-bind=\"attributes\"\n      :type=\"isRandom || reveal ? 'text' : 'password'\"\n      :readonly=\"isRandom\"\n      :label=\"label\"\n      :required=\"required\"\n      :disabled=\"isRandom\"\n      :ignore-password-managers=\"ignorePasswordManagers\"\n      :mode=\"mode\"\n      @blur=\"$emit('blur', $event)\"\n    >\n      <template #suffix>\n        <div\n          v-if=\"isRandom\"\n          class=\"addon\"\n        >\n          <a\n            href=\"#\"\n            @click.prevent.stop=\"copyTextToClipboard(password)\"\n          >{{ t('action.copy') }}</a>\n        </div>\n        <div\n          v-else\n          class=\"addon\"\n        >\n          <a\n            href=\"#\"\n            tabindex=\"0\"\n            class=\"hide-show\"\n            role=\"button\"\n            @click.prevent.stop=\"hideShowFn\"\n            @keyup.space.prevent.stop=\"hideShowFn\"\n          >\n            {{ hideShowLabel }}\n          </a>\n        </div>\n      </template>\n    </LabeledInput>\n    <div\n      v-if=\"isRandom\"\n      class=\"mt-10 genPassword\"\n    >\n      <a\n        href=\"#\"\n        @click.prevent.stop=\"generatePassword\"\n      ><i class=\"icon icon-refresh\" /> {{ t('changePassword.newGeneratedPassword') }}</a>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .password {\n    display: flex;\n    flex-direction: column;\n\n    .labeled-input {\n      .addon {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        padding-left: 12px;\n        min-width: 65px;\n\n        .hide-show:focus-visible {\n          @include focus-outline;\n          outline-offset: 4px;\n        }\n      }\n    }\n    .genPassword {\n      display: flex;\n      justify-content: flex-end;\n    }\n  }\n\n</style>\n"]}]}