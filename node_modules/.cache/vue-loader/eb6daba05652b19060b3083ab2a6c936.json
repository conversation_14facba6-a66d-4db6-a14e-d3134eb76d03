{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/Checkbox/Checkbox.vue?vue&type=style&index=0&id=f6954e2a&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/Checkbox/Checkbox.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/Checkbox/Checkbox.vue"], "names": [], "mappings": ";AA8UA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACd;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACjB;AACF;;AAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;IAExB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACrB;EACF;;CAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAClB;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACb;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClC;;EAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACX;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/B;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/B;;EAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7C;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB;EACF;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/Checkbox/Checkbox.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { PropType, defineComponent } from 'vue';\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\nimport { addObject, removeObject } from '@shell/utils/array';\nimport cloneDeep from 'lodash/cloneDeep';\nimport { generateRandomAlphaString } from '@shell/utils/string';\n\nexport default defineComponent({\n  name: 'Checkbox',\n\n  props: {\n    /**\n     * The checkbox value.\n     */\n    value: {\n      type:    [Boolean, Array, String] as PropType<boolean | boolean[] | string>,\n      default: false\n    },\n\n    /**\n     * The checkbox label.\n     */\n    label: {\n      type:    String,\n      default: null\n    },\n\n    /**\n     * The i18n key to use for the checkbox label.\n     */\n    labelKey: {\n      type:    String,\n      default: null\n    },\n\n    /**\n     * Random ID generated for binding label to input.\n     */\n    id: {\n      type:    String,\n      default: generateRandomAlphaString(12)\n    },\n\n    /**\n     * Disable the checkbox.\n     */\n    disabled: {\n      type:    <PERSON>olean,\n      default: false\n    },\n\n    /**\n     * Display an indeterminate state. Useful for cases where a checkbox might\n     * be the parent to child checkboxes, and we need to show that a subset of\n     * children are checked.\n     */\n    indeterminate: {\n      type:    Boolean,\n      default: false\n    },\n\n    /**\n     * The checkbox editing mode.\n     * @values _EDIT, _VIEW\n     */\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n\n    /**\n     * The contents of the checkbox tooltip.\n     */\n    tooltip: {\n      type:    [String, Object],\n      default: null\n    },\n\n    /**\n     * The i18n key to use for the checkbox tooltip.\n     */\n    tooltipKey: {\n      type:    String,\n      default: null\n    },\n\n    /**\n     * A custom value to use when the checkbox is checked.\n     */\n    valueWhenTrue: {\n      type:    [Boolean, String, Number],\n      default: true\n    },\n\n    /**\n     * The i18n key to use for the checkbox description.\n     */\n    descriptionKey: {\n      type:    String,\n      default: null\n    },\n\n    /**\n     * The checkbox description.\n     */\n    description: {\n      type:    String,\n      default: null\n    },\n\n    /**\n     * Primary checkbox displays label so that it stands out more\n     */\n    primary: {\n      type:    Boolean,\n      default: false\n    },\n\n    /**\n     * Use this for usage of checkboxes that don't present a label.\n     * Used for cases such as table checkboxes (group or row)\n     */\n    alternateLabel: {\n      type:    String,\n      default: undefined\n    },\n  },\n\n  emits: ['update:value'],\n\n  data() {\n    return { describedById: `described-by-${ generateRandomAlphaString(12) }` };\n  },\n\n  computed: {\n    /**\n     * Determines if the checkbox is disabled.\n     * @returns boolean: True when the disabled prop is true or when mode is\n     * View.\n     */\n    isDisabled(): boolean {\n      return (this.disabled || this.mode === _VIEW);\n    },\n    /**\n     * Determines if the checkbox is checked when using custom values or\n     * multiple values.\n     * @returns boolean: True when at least one value is true in a collection or\n     * when value matches `this.valueWhenTrue`.\n     */\n    isChecked(): boolean {\n      return this.isMulti(this.value) ? this.findTrueValues(this.value) : this.value === this.valueWhenTrue;\n    },\n\n    /**\n     * Determines if the Labeled Input should display a tooltip.\n     */\n    hasTooltip(): boolean {\n      return !!this.tooltip || !!this.tooltipKey;\n    },\n\n    replacementLabel(): string | undefined {\n      if (!this.label && !this.labelKey && this.alternateLabel) {\n        return this.alternateLabel;\n      }\n\n      return undefined;\n    },\n\n    idForLabel():string {\n      return `${ this.id }-label`;\n    }\n  },\n\n  methods: {\n    /**\n     * Toggles the checked state for the checkbox and emits an 'input' event.\n     */\n    clicked(event: MouseEvent | KeyboardEvent): boolean | void {\n      if ((event.target as HTMLLinkElement).tagName === 'A' && (event.target as HTMLLinkElement).href) {\n        // Ignore links inside the checkbox label so you can click them\n        return true;\n      }\n\n      event.stopPropagation();\n      event.preventDefault();\n\n      if (this.isDisabled) {\n        return;\n      }\n\n      const customEvent = {\n        bubbles:    true,\n        cancelable: false,\n        shiftKey:   event.shiftKey,\n        altKey:     event.altKey,\n        ctrlKey:    event.ctrlKey,\n        metaKey:    event.metaKey\n      };\n\n      const click = new CustomEvent('click', customEvent);\n\n      // Flip the value\n      const value = cloneDeep(this.value);\n\n      if (this.isMulti(value)) {\n        if (this.isChecked) {\n          removeObject(value, this.valueWhenTrue);\n        } else {\n          addObject(value, this.valueWhenTrue);\n        }\n        this.$emit('update:value', value);\n      } else if (this.isString(this.valueWhenTrue)) {\n        if (this.isChecked) {\n          this.$emit('update:value', null);\n        } else {\n          this.$emit('update:value', this.valueWhenTrue);\n        }\n      } else {\n        this.$emit('update:value', !value);\n        this.$el.dispatchEvent(click);\n      }\n    },\n\n    /**\n     * Determines if there are multiple values for the checkbox.\n     */\n    isMulti(value: boolean | boolean[] | string): value is boolean[] {\n      return Array.isArray(value);\n    },\n\n    isString(value: boolean | number | string): value is boolean {\n      return typeof value === 'string';\n    },\n\n    /**\n     * Finds the first true value for multiple checkboxes.\n     * @param value A collection of values for the checkbox.\n     */\n    findTrueValues(value: boolean[]): boolean {\n      return value.find((v) => v === this.valueWhenTrue) || false;\n    }\n  }\n});\n</script>\n\n<template>\n  <div\n    class=\"checkbox-outer-container\"\n    data-checkbox-ctrl\n    :class=\"{\n      'v-popper--has-tooltip': hasTooltip,\n    }\"\n  >\n    <label\n      class=\"checkbox-container\"\n      :class=\"{ 'disabled': isDisabled}\"\n      @keydown.enter.prevent=\"clicked($event)\"\n      @keydown.space.prevent=\"clicked($event)\"\n      @click=\"clicked($event)\"\n    >\n      <input\n        :id=\"id\"\n        :checked=\"isChecked\"\n        :value=\"valueWhenTrue\"\n        type=\"checkbox\"\n        tabindex=\"-1\"\n        @click.stop.prevent\n        @keyup.enter.stop.prevent\n      >\n      <span\n        class=\"checkbox-custom\"\n        :class=\"{indeterminate: indeterminate}\"\n        :tabindex=\"isDisabled ? -1 : 0\"\n        :aria-label=\"replacementLabel\"\n        :aria-checked=\"!!value\"\n        :aria-labelledby=\"labelKey || label ? idForLabel : undefined\"\n        :aria-describedby=\"descriptionKey || description ? describedById : undefined\"\n        role=\"checkbox\"\n      />\n      <span\n        v-if=\"$slots.label || label || labelKey || hasTooltip\"\n        class=\"checkbox-label\"\n        :class=\"{ 'checkbox-primary': primary }\"\n      >\n        <slot name=\"label\">\n          <t\n            v-if=\"labelKey\"\n            :id=\"idForLabel\"\n            :k=\"labelKey\"\n            :raw=\"true\"\n          />\n          <span\n            v-else-if=\"label\"\n            :id=\"idForLabel\"\n          >{{ label }}</span>\n          <i\n            v-if=\"tooltipKey\"\n            v-clean-tooltip=\"{content: t(tooltipKey), triggers: ['hover', 'touch', 'focus']}\"\n            v-stripped-aria-label=\"t(tooltipKey)\"\n            class=\"checkbox-info icon icon-info icon-lg\"\n            :tabindex=\"isDisabled ? -1 : 0\"\n          />\n          <i\n            v-else-if=\"tooltip\"\n            v-clean-tooltip=\"{content: tooltip, triggers: ['hover', 'touch', 'focus']}\"\n            v-stripped-aria-label=\"tooltip\"\n            class=\"checkbox-info icon icon-info icon-lg\"\n            :tabindex=\"isDisabled ? -1 : 0\"\n          />\n        </slot>\n      </span>\n    </label>\n    <div\n      v-if=\"descriptionKey || description\"\n      class=\"checkbox-outer-container-description\"\n    >\n      <t\n        v-if=\"descriptionKey\"\n        :id=\"describedById\"\n        :k=\"descriptionKey\"\n      />\n      <template v-else-if=\"description\">\n        <p :id=\"describedById\">\n          {{ description }}\n        </p>\n      </template>\n    </div>\n    <div class=\"checkbox-outer-container-extra\">\n      <slot name=\"extra\" />\n    </div>\n  </div>\n</template>\n\n<style lang='scss'>\n$fontColor: var(--input-label);\n\n.checkbox-outer-container {\n  display: inline-flex;\n  flex-direction: column;\n  &-description {\n    color: $fontColor;\n    font-size: 14px;\n    margin-left: 19px;\n    margin-top: 5px;\n    opacity: 0.8;\n  }\n  &-extra {\n    font-size: 14px;\n    margin-left: 19px;\n    margin-top: 5px;\n  }\n}\n\n// NOTE: SortableTable depends on the names of this class, do not arbitrarily change.\n.checkbox-container {\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n  margin: 0;\n  cursor: pointer;\n  user-select: none;\n  border-radius: var(--border-radius);\n\n  .checkbox-label {\n    color: var(--input-label);\n    display: inline-flex;\n    margin: 0px 10px 0px 5px;\n\n    &.checkbox-primary {\n      color: inherit;\n      font-weight: 600;\n    }\n  }\n\n  .checkbox-info {\n    line-height: normal;\n    margin-left: 2px;\n\n    &:focus-visible {\n      @include focus-outline;\n      outline-offset: 2px;\n    }\n  }\n\n .checkbox-custom {\n    height: 14px;\n    width: 14px;\n    background-color: var(--body-bg);\n    border-radius: var(--border-radius);\n    border: 1px solid var(--border);\n    flex-shrink: 0;\n\n    &:focus-visible {\n      @include focus-outline;\n      outline-offset: 2px;\n      border-radius: 0;\n    }\n  }\n\n  input {\n    // display: none;\n    opacity: 0;\n    position: absolute;\n    z-index: -1;\n  }\n\n  input:focus-visible ~ .checkbox-custom {\n    @include focus-outline;\n    outline-offset: 2px;\n    border-radius: 0;\n  }\n\n  input:checked ~ .checkbox-custom {\n    background-color:var(--primary);\n    -webkit-transform: rotate(0deg) scale(1);\n    -ms-transform: rotate(0deg) scale(1);\n    transform: rotate(0deg) scale(1);\n    opacity:1;\n    border: 1px solid var(--primary);\n  }\n\n  // Custom Checkbox tick\n  .checkbox-custom::after {\n    position: absolute;\n    content: \"\";\n    left: 0px;\n    top: 0px;\n    height: 0px;\n    width: 0px;\n    border-radius: var(--border-radius);\n    border: solid;\n    border-color: var(--input-text);\n    border-width: 0 3px 3px 0;\n    -webkit-transform: rotate(0deg) scale(0);\n    -ms-transform: rotate(0deg) scale(0);\n    transform: rotate(0deg) scale(0);\n    opacity:1;\n  }\n\n  input:checked ~ .checkbox-custom::after {\n    -webkit-transform: rotate(45deg) scale(1);\n    -ms-transform: rotate(45deg) scale(1);\n    transform: rotate(45deg) scale(1);\n    opacity:1;\n    left: 4px;\n    width: 4px;\n    height: 10px;\n    border: solid;\n    border-color: var(--checkbox-tick);\n    border-width: 0 2px 2px 0;\n    background-color: transparent;\n  }\n\n  input:checked ~ .checkbox-custom.indeterminate::after {\n    -webkit-transform:  scale(1);\n    -ms-transform:  scale(1);\n    transform:  scale(1);\n    opacity:1;\n    left: 3px;\n    top:2px;\n    width: 6px;\n    height: 5px;\n    border: solid;\n    border-color: var(--checkbox-tick);\n    border-width: 0 0 2px 0;\n    background-color: transparent;\n  }\n\n  // Disabled styles\n  &.disabled {\n    .checkbox-custom {\n      background-color: var(--checkbox-disabled-bg);\n      border-color: var(--checkbox-disabled-bg);\n    }\n    input:checked ~ .checkbox-custom {\n      background-color: var(--checkbox-disabled-bg);\n      border-color: var(--checkbox-disabled-bg);\n      &::after {\n        border-color: var(--checkbox-tick-disabled);\n      }\n    }\n  }\n\n  &.disabled {\n    cursor: not-allowed;\n  }\n\n  .checkbox-view {\n    display: flex;\n    flex-direction: column;\n    LABEL {\n      color: $fontColor;\n    }\n  }\n}\n</style>\n"]}]}