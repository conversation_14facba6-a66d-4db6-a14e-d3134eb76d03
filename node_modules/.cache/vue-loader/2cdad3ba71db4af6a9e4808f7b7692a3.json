{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SecretSelector.vue?vue&type=style&index=0&id=584eeb32&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SecretSelector.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5zZWNyZXQtc2VsZWN0b3IgewogIHdpZHRoOiAxMDAlOwogIGxhYmVsIHsKICAgIGRpc3BsYXk6IGJsb2NrOwogIH0KCiAgJiAubGFiZWxlZC1zZWxlY3QgewogICAgbWluLWhlaWdodDogJGlucHV0LWhlaWdodDsKICB9CgogICYgLnZzX19zZWxlY3RlZC1vcHRpb25zIHsKICAgIHBhZGRpbmc6IDhweCAwIDdweCAwOwogIH0KCiAgJiBsYWJlbCB7CiAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7CiAgfQoKICAmLnNob3cta2V5LXNlbGVjdG9yIHsKICAgIC5pbnB1dC1jb250YWluZXIgPiAqIHsKICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrOwogICAgICB3aWR0aDogNTAlOwoKICAgICAgJi5sYWJlbGVkLXNlbGVjdC5mb2N1c2VkIHsKICAgICAgICB6LWluZGV4OiAxMDsKICAgICAgfQoKICAgICAgJjpmaXJzdC1jaGlsZCB7CiAgICAgICAgYm9yZGVyLXRvcC1yaWdodC1yYWRpdXM6IDA7CiAgICAgICAgYm9yZGVyLWJvdHRvbS1yaWdodC1yYWRpdXM6IDA7CiAgICAgICAgbWFyZ2luLXJpZ2h0OiAwOwogICAgICB9CgogICAgICAmOmxhc3QtY2hpbGQgewogICAgICAgIGJvcmRlci10b3AtbGVmdC1yYWRpdXM6IDA7CiAgICAgICAgYm9yZGVyLWJvdHRvbS1sZWZ0LXJhZGl1czogMDsKICAgICAgICBib3JkZXItbGVmdDogbm9uZTsKICAgICAgICBmbG9hdDogcmlnaHQ7CiAgICAgIH0KICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SecretSelector.vue"], "names": [], "mappings": ";AAkOA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B;;EAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC;EACtB;;EAEA,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACb;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACjB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd;IACF;EACF;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SecretSelector.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport ResourceLabeledSelect from '@shell/components/form/ResourceLabeledSelect';\nimport { SECRET } from '@shell/config/types';\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\nimport { SECRET_TYPES as TYPES } from '@shell/config/secret';\nimport { PaginationParamFilter } from '@shell/types/store/pagination.types';\nimport { LABEL_SELECT_KINDS } from '@shell/types/components/labeledSelect';\n\nconst NONE = '__[[NONE]]__';\n\nexport default {\n  emits:      ['update:value'],\n  components: { LabeledSelect, ResourceLabeledSelect },\n\n  props: {\n    value: {\n      type:     [String, Object],\n      required: false,\n      default:  undefined\n    },\n    namespace: {\n      type:     String,\n      required: true\n    },\n    types: {\n      type:    Array,\n      default: () => Object.values(TYPES)\n    },\n    disabled: {\n      type:    Boolean,\n      default: false\n    },\n    mountKey: {\n      type:    String,\n      default: 'valueFrom'\n    },\n    nameKey: {\n      type:    String,\n      default: 'name'\n    },\n    keyKey: {\n      type:    String,\n      default: 'key'\n    },\n    showKeySelector: {\n      type:    Boolean,\n      default: false\n    },\n    secretNameLabel: {\n      type:    String,\n      default: 'Secret Name'\n    },\n    keyNameLabel: {\n      type:    String,\n      default: 'Key'\n    },\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n    inStore: {\n      type:    String,\n      default: 'cluster',\n    }\n  },\n\n  data() {\n    return {\n      secrets:            null,\n      SECRET,\n      allSecretsSettings: {\n        updateResources: (secrets) => {\n          const allSecretsInNamespace = secrets.filter((secret) => this.types.includes(secret._type) && secret.namespace === this.namespace);\n          const mappedSecrets = this.mapSecrets(allSecretsInNamespace.sort((a, b) => a.name.localeCompare(b.name)));\n\n          this.secrets = allSecretsInNamespace; // We need the key from the selected secret\n\n          return mappedSecrets;\n        }\n      },\n      paginateSecretsSetting: {\n        requestSettings: this.paginatePageOptions,\n        updateResources: (secrets) => {\n          const mappedSecrets = this.mapSecrets(secrets);\n\n          this.secrets = secrets; // We need the key from the selected secret. When paginating we won't touch the store, so just pass back here\n\n          return mappedSecrets;\n        }\n      }\n    };\n  },\n\n  computed: {\n    name: {\n      get() {\n        const name = this.showKeySelector ? this.value?.[this.mountKey]?.secretKeyRef?.[this.nameKey] : this.value;\n\n        return name || NONE;\n      },\n      set(name) {\n        const isNone = name === NONE;\n        const correctedName = isNone ? undefined : name;\n\n        if (this.showKeySelector) {\n          this.$emit('update:value', { [this.mountKey]: { secretKeyRef: { [this.nameKey]: correctedName, [this.keyKey]: '' } } });\n        } else {\n          this.$emit('update:value', correctedName);\n        }\n      }\n    },\n\n    key: {\n      get() {\n        return this.value?.[this.mountKey]?.secretKeyRef?.[this.keyKey] || '';\n      },\n      set(key) {\n        this.$emit('update:value', { [this.mountKey]: { secretKeyRef: { [this.nameKey]: this.name, [this.keyKey]: key } } });\n      }\n    },\n\n    keys() {\n      const secret = (this.secrets || []).find((secret) => secret.name === this.name) || {};\n\n      return Object.keys(secret.data || {}).map((key) => ({\n        label: key,\n        value: key\n      }));\n    },\n\n    isView() {\n      return this.mode === _VIEW;\n    },\n\n    isKeyDisabled() {\n      return !this.isView && (!this.name || this.name === NONE || this.disabled);\n    }\n  },\n\n  methods: {\n    /**\n     * Provide a set of options for the LabelSelect ([none, ...{label, value}])\n     */\n    mapSecrets(secrets) {\n      const mappedSecrets = secrets\n        .reduce((res, s) => {\n          if (s.kind === LABEL_SELECT_KINDS.NONE) {\n            return res;\n          }\n\n          if (s.id) {\n            res.push({ label: s.name, value: s.name });\n          } else {\n            res.push(s);\n          }\n\n          return res;\n        }, []);\n\n      return [\n        {\n          label: 'None', value: NONE, kind: LABEL_SELECT_KINDS.NONE\n        },\n        ...mappedSecrets\n      ];\n    },\n\n    /**\n     * @param [LabelSelectPaginationFunctionOptions] opts\n     * @returns LabelSelectPaginationFunctionOptions\n     */\n    paginatePageOptions(opts) {\n      const { opts: { filter } } = opts;\n\n      const filters = !!filter ? [PaginationParamFilter.createSingleField({ field: 'metadata.name', value: filter })] : [];\n\n      filters.push(\n        PaginationParamFilter.createSingleField({ field: 'metadata.namespace', value: this.namespace }),\n        PaginationParamFilter.createSingleField({ field: 'metadata.fields.1', value: this.types.join(',') })\n      );\n\n      return {\n        ...opts,\n        filters,\n        groupByNamespace: false,\n        classify:         true,\n        sort:             [{ asc: true, field: 'metadata.name' }],\n      };\n    },\n  }\n\n};\n</script>\n\n<template>\n  <div\n    class=\"secret-selector\"\n    :class=\"{'show-key-selector': showKeySelector}\"\n  >\n    <div class=\"input-container\">\n      <!-- key by namespace to ensure label select current page is recreated on ns change -->\n      <ResourceLabeledSelect\n        v-model:value=\"name\"\n        :disabled=\"!isView && disabled\"\n        :label=\"secretNameLabel\"\n        :mode=\"mode\"\n        :resource-type=\"SECRET\"\n        :in-store=\"inStore\"\n        :paginated-resource-settings=\"paginateSecretsSetting\"\n        :all-resources-settings=\"allSecretsSettings\"\n      />\n      <LabeledSelect\n        v-if=\"showKeySelector\"\n        v-model:value=\"key\"\n        class=\"col span-6\"\n        :disabled=\"isKeyDisabled\"\n        :options=\"keys\"\n        :label=\"keyNameLabel\"\n        :mode=\"mode\"\n      />\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\">\n.secret-selector {\n  width: 100%;\n  label {\n    display: block;\n  }\n\n  & .labeled-select {\n    min-height: $input-height;\n  }\n\n  & .vs__selected-options {\n    padding: 8px 0 7px 0;\n  }\n\n  & label {\n    display: inline-block;\n  }\n\n  &.show-key-selector {\n    .input-container > * {\n      display: inline-block;\n      width: 50%;\n\n      &.labeled-select.focused {\n        z-index: 10;\n      }\n\n      &:first-child {\n        border-top-right-radius: 0;\n        border-bottom-right-radius: 0;\n        margin-right: 0;\n      }\n\n      &:last-child {\n        border-top-left-radius: 0;\n        border-bottom-left-radius: 0;\n        border-left: none;\n        float: right;\n      }\n    }\n  }\n}\n</style>\n"]}]}