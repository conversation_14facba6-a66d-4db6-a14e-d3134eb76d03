{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ProjectMemberEditor.vue?vue&type=style&index=0&id=6adb9458&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ProjectMemberEditor.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQokZGV0YWlsU2l6ZTogMTFweDsNCg0KOmRlZXAoKSAudHlwZS1kZXNjcmlwdGlvbiB7DQogICAgZm9udC1zaXplOiAkZGV0YWlsU2l6ZTsNCn0NCg0KbGFiZWwucmFkaW8gew0KICBmb250LXNpemU6IDE2cHg7DQp9DQoNCi5jdXN0b20tcGVybWlzc2lvbnMgew0KICBkaXNwbGF5OiBncmlkOw0KICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmciAxZnIgMWZyOw0KICAmLnR3by1jb2x1bW4gew0KICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyIDFmcjsNCiAgfQ0KDQogIDpkZWVwKCkgLmNoZWNrYm94LWxhYmVsIHsNCiAgICBtYXJnaW4tcmlnaHQ6IDA7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ProjectMemberEditor.vue"], "names": [], "mappings": ";AAgUA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;AAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChC;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACjB;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ProjectMemberEditor.vue", "sourceRoot": "", "sourcesContent": ["<script>\r\nimport CreateEditView from '@shell/mixins/create-edit-view';\r\nimport SelectPrincipal from '@shell/components/auth/SelectPrincipal';\r\nimport { MANAGEMENT } from '@shell/config/types';\r\nimport Loading from '@shell/components/Loading';\r\nimport { Card } from '@components/Card';\r\nimport { RadioGroup } from '@components/Form/Radio';\r\nimport { Checkbox } from '@components/Form/Checkbox';\r\nimport { DESCRIPTION } from '@shell/config/labels-annotations';\r\nimport DOMPurify from 'dompurify';\r\n\r\nexport default {\r\n  components: {\r\n    Card,\r\n    Checkbox,\r\n    Loading,\r\n    RadioGroup,\r\n    SelectPrincipal\r\n  },\r\n\r\n  mixins: [CreateEditView],\r\n\r\n  props: {\r\n    value: {\r\n      type:     Object,\r\n      required: true\r\n    },\r\n\r\n    useTwoColumnsForCustom: {\r\n      type:    Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  async fetch() {\r\n    const [, roleTemplates, projects] = await Promise.all([\r\n      this.$store.dispatch('management/findAll', { type: MANAGEMENT.USER }),\r\n      this.$store.dispatch('management/findAll', { type: MANAGEMENT.ROLE_TEMPLATE }),\r\n      this.$store.dispatch('management/findAll', { type: MANAGEMENT.PROJECT })\r\n    ]);\r\n\r\n    this.roleTemplates = roleTemplates;\r\n    this.projects = projects;\r\n  },\r\n  data() {\r\n    this.setRoleTemplateIds(this.value.permissionGroup);\r\n\r\n    return {\r\n      customPermissions: [\r\n        {\r\n          key:   'create-ns',\r\n          label: this.t('projectMembers.projectPermissions.createNs'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'configmaps-manage',\r\n          label: this.t('projectMembers.projectPermissions.configmapsManage'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'ingress-manage',\r\n          label: this.t('projectMembers.projectPermissions.ingressManage'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'projectcatalogs-manage',\r\n          label: this.t('projectMembers.projectPermissions.projectcatalogsManage'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'projectroletemplatebindings-manage',\r\n          label: this.t('projectMembers.projectPermissions.projectroletemplatebindingsManage'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'secrets-manage',\r\n          label: this.t('projectMembers.projectPermissions.secretsManage'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'serviceaccounts-manage',\r\n          label: this.t('projectMembers.projectPermissions.serviceaccountsManage'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'services-manage',\r\n          label: this.t('projectMembers.projectPermissions.servicesManage'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'persistentvolumeclaims-manage',\r\n          label: this.t('projectMembers.projectPermissions.persistentvolumeclaimsManage'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'workloads-manage',\r\n          label: this.t('projectMembers.projectPermissions.workloadsManage'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'configmaps-view',\r\n          label: this.t('projectMembers.projectPermissions.configmapsView'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'ingress-view',\r\n          label: this.t('projectMembers.projectPermissions.ingressView'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'monitoring-ui-view',\r\n          label: this.t('projectMembers.projectPermissions.monitoringUiView'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'projectcatalogs-view',\r\n          label: this.t('projectMembers.projectPermissions.projectcatalogsView'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'projectroletemplatebindings-view',\r\n          label: this.t('projectMembers.projectPermissions.projectroletemplatebindingsView'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'secrets-view',\r\n          label: this.t('projectMembers.projectPermissions.secretsView'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'serviceaccounts-view',\r\n          label: this.t('projectMembers.projectPermissions.serviceaccountsView'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'services-view',\r\n          label: this.t('projectMembers.projectPermissions.servicesView'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'persistentvolumeclaims-view',\r\n          label: this.t('projectMembers.projectPermissions.persistentvolumeclaimsView'),\r\n          value: false,\r\n        },\r\n        {\r\n          key:   'workloads-view',\r\n          label: this.t('projectMembers.projectPermissions.workloadsView'),\r\n          value: false,\r\n        },\r\n      ],\r\n      projects:      [],\r\n      roleTemplates: [],\r\n    };\r\n  },\r\n  computed: {\r\n    customRoles() {\r\n      return this.roleTemplates\r\n        .filter((role) => {\r\n          return !role.builtin && !role.external && !role.hidden && role.context === 'project';\r\n        });\r\n    },\r\n\r\n    options() {\r\n      const customRoles = this.customRoles.map((role) => ({\r\n        label:       this.purifyOption(role.nameDisplay),\r\n        description: this.purifyOption(role.description || role.metadata?.annotations?.[DESCRIPTION] || this.t('projectMembers.projectPermissions.noDescription')),\r\n        value:       this.purifyOption(role.id),\r\n      }));\r\n\r\n      return [\r\n        {\r\n          label:       this.t('projectMembers.projectPermissions.owner.label'),\r\n          description: this.t('projectMembers.projectPermissions.owner.description'),\r\n          value:       'owner'\r\n        },\r\n        {\r\n          label:       this.t('projectMembers.projectPermissions.member.label'),\r\n          description: this.t('projectMembers.projectPermissions.member.description'),\r\n          value:       'member'\r\n        },\r\n        {\r\n          label:       this.t('projectMembers.projectPermissions.readOnly.label'),\r\n          description: this.t('projectMembers.projectPermissions.readOnly.description'),\r\n          value:       'read-only'\r\n        },\r\n        ...customRoles,\r\n        {\r\n          label:       this.t('projectMembers.projectPermissions.custom.label'),\r\n          description: this.t('projectMembers.projectPermissions.custom.description'),\r\n          value:       'custom'\r\n        }\r\n      ];\r\n    },\r\n    customPermissionsUpdate() {\r\n      return this.customPermissions.reduce((acc, customPermissionsItem) => {\r\n        const lockedExist = this.roleTemplates.find((roleTemplateItem) => roleTemplateItem.id === customPermissionsItem.key);\r\n\r\n        if (lockedExist && lockedExist.locked) {\r\n          customPermissionsItem['locked'] = true;\r\n          customPermissionsItem['tooltip'] = this.t('members.clusterPermissions.custom.lockedRole');\r\n        }\r\n\r\n        return [...acc, customPermissionsItem];\r\n      }, []);\r\n    }\r\n  },\r\n  watch: {\r\n    'value.permissionGroup'(newPermissionGroup) {\r\n      this.setRoleTemplateIds(newPermissionGroup);\r\n    },\r\n\r\n    customPermissions: {\r\n      deep: true,\r\n      handler() {\r\n        this.setRoleTemplateIds(this.value.permissionGroup);\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    onAdd(principalId) {\r\n      this.value['principalId'] = principalId;\r\n    },\r\n\r\n    setRoleTemplateIds(permissionGroup) {\r\n      const roleTemplateIds = this.getRoleTemplateIds(permissionGroup);\r\n\r\n      this.value['roleTemplateIds'] = roleTemplateIds;\r\n    },\r\n\r\n    getRoleTemplateIds(permissionGroup) {\r\n      if (permissionGroup === 'owner') {\r\n        return ['project-owner'];\r\n      }\r\n\r\n      if (permissionGroup === 'member') {\r\n        return ['project-member'];\r\n      }\r\n\r\n      if (permissionGroup === 'read-only') {\r\n        return ['read-only'];\r\n      }\r\n\r\n      if (permissionGroup === 'custom') {\r\n        return this.customPermissions\r\n          .filter((permission) => permission.value)\r\n          .map((permission) => permission.key);\r\n      }\r\n\r\n      return [permissionGroup];\r\n    },\r\n    purifyOption(option) {\r\n      return DOMPurify.sanitize(option, { ALLOWED_TAGS: ['span'] });\r\n    }\r\n  }\r\n};\r\n\r\n</script>\r\n\r\n<template>\r\n  <Loading v-if=\"$fetchState.pending\" />\r\n  <div v-else>\r\n    <div class=\"row mt-10\">\r\n      <div class=\"col span-12\">\r\n        <SelectPrincipal\r\n          data-testid=\"cluster-member-select\"\r\n          project\r\n          class=\"mb-20\"\r\n          :mode=\"mode\"\r\n          :retain-selection=\"true\"\r\n          @add=\"onAdd\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <Card\r\n      class=\"m-0\"\r\n      :show-highlight-border=\"false\"\r\n      :show-actions=\"false\"\r\n    >\r\n      <template v-slot:title>\r\n        <div class=\"type-title\">\r\n          <h3>{{ t('projectMembers.projectPermissions.label') }}</h3>\r\n          <div class=\"type-description\">\r\n            {{ t('projectMembers.projectPermissions.description') }}\r\n          </div>\r\n        </div>\r\n      </template>\r\n      <template v-slot:body>\r\n        <RadioGroup\r\n          v-model:value=\"value.permissionGroup\"\r\n          data-testid=\"permission-options\"\r\n          :options=\"options\"\r\n          name=\"permission-group\"\r\n        />\r\n        <div\r\n          v-if=\"value.permissionGroup === 'custom'\"\r\n          class=\"custom-permissions ml-20 mt-10\"\r\n          :class=\"{'two-column': useTwoColumnsForCustom}\"\r\n        >\r\n          <div\r\n            v-for=\"(permission, i) in customPermissionsUpdate\"\r\n            :key=\"i\"\r\n          >\r\n            <Checkbox\r\n              v-model:value=\"permission.value\"\r\n              :data-testid=\"`custom-permission-${i}`\"\r\n              :disabled=\"permission.locked\"\r\n              class=\"mb-5\"\r\n              :label=\"permission.label\"\r\n            />\r\n            <i\r\n              v-if=\"permission.locked\"\r\n              v-clean-tooltip=\"permission.tooltip\"\r\n              class=\"icon icon-lock icon-fw\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </template>\r\n    </Card>\r\n  </div>\r\n</template>\r\n<style lang=\"scss\" scoped>\r\n$detailSize: 11px;\r\n\r\n:deep() .type-description {\r\n    font-size: $detailSize;\r\n}\r\n\r\nlabel.radio {\r\n  font-size: 16px;\r\n}\r\n\r\n.custom-permissions {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr 1fr;\r\n  &.two-column {\r\n    grid-template-columns: 1fr 1fr;\r\n  }\r\n\r\n  :deep() .checkbox-label {\r\n    margin-right: 0;\r\n  }\r\n}\r\n</style>\r\n"]}]}