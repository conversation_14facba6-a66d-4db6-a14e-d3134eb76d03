{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/InputWithSelect.vue?vue&type=style&index=0&id=2753d4c4&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/InputWithSelect.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/InputWithSelect.vue"], "names": [], "mappings": ";AAyMA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE3B,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxB;;IAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;MAE5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACnB;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC/B;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC5B;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B;IACF;EACF;;EAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnB;EACF;;EAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxD;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxD;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAErB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClB;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9D;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC;QACF;MACF;IACF;EACF;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/InputWithSelect.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport labeledFormElement from '@shell/mixins/labeled-form-element';\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport Select from '@shell/components/form/Select';\nexport default {\n  name:       'InputWithSelect',\n  emits:      ['update:value'],\n  components: {\n    LabeledInput,\n    LabeledSelect,\n    Select,\n  },\n  mixins: [labeledFormElement],\n  props:  {\n    disabled: {\n      type:    Boolean,\n      default: false,\n    },\n\n    searchable: {\n      type:    Boolean,\n      default: true,\n    },\n\n    taggable: {\n      type:    Boolean,\n      default: false,\n    },\n\n    selectLabel: {\n      type:    String,\n      default: '',\n    },\n\n    selectValue: {\n      type:    String,\n      default: null,\n    },\n\n    optionLabel: {\n      type:    String,\n      default: 'label',\n    },\n\n    options: {\n      type:     Array,\n      required: true,\n    },\n\n    selectBeforeText: {\n      type:    Boolean,\n      default: true,\n    },\n\n    textLabel: {\n      type:    String,\n      default: '',\n    },\n\n    textRequired: {\n      type:    <PERSON>olean,\n      default: false,\n    },\n\n    textDisabled: {\n      type:    <PERSON>ole<PERSON>,\n      default: false,\n    },\n\n    textValue: {\n      type:    [String, Number],\n      default: '',\n    },\n\n    placeholder: {\n      type:    String,\n      default: '',\n    },\n    textRules: {\n      default: () => [],\n      type:    Array,\n    },\n    selectRules: {\n      default: () => [],\n      type:    Array,\n    }\n\n  },\n\n  data() {\n    return {\n      selected: this.selectValue || this.options[0].value,\n      string:   this.textValue,\n    };\n  },\n\n  computed: {\n    canPaginate() {\n      return false;\n    }\n  },\n\n  methods: {\n    focus() {\n      const comp = this.$refs.text;\n\n      if (comp) {\n        comp.focus();\n      }\n    },\n\n    change() {\n      this.$emit('update:value', { selected: this.selected, text: this.string });\n    },\n  },\n\n  watch: {\n    textValue(value) {\n      this.string = value;\n    },\n  },\n};\n</script>\n\n<template>\n  <div\n    :class=\"{ 'select-after': !selectBeforeText }\"\n    class=\"input-container row\"\n  >\n    <LabeledSelect\n      v-if=\"selectLabel\"\n      v-model:value=\"selected\"\n      :label=\"selectLabel\"\n      :class=\"{ 'in-input': !isView}\"\n      :options=\"options\"\n      :searchable=\"false\"\n      :clearable=\"false\"\n      :disabled=\"disabled || isView\"\n      :taggable=\"taggable\"\n      :create-option=\"(name) => ({ label: name, value: name })\"\n      :multiple=\"false\"\n      :mode=\"mode\"\n      :option-label=\"optionLabel\"\n      :placement=\"$attrs.placement ? $attrs.placement : null\"\n      :v-bind=\"$attrs\"\n      :rules=\"selectRules\"\n      @update:value=\"change\"\n    />\n    <Select\n      v-else\n      v-model:value=\"selected\"\n      :options=\"options\"\n      :searchable=\"searchable\"\n      :disabled=\"disabled || isView\"\n      :clearable=\"false\"\n      class=\"in-input\"\n      :taggable=\"taggable\"\n      :create-option=\"(name) => ({ label: name, value: name })\"\n      :multiple=\"false\"\n      :mode=\"mode\"\n      :option-label=\"optionLabel\"\n      :placement=\"$attrs.placement ? $attrs.placement : null\"\n      :v-bind=\"$attrs\"\n      @update:value=\"change\"\n    />\n    <LabeledInput\n      v-if=\"textLabel || textRules.length > 0\"\n      ref=\"text\"\n      v-model:value=\"string\"\n      class=\"input-string col span-8\"\n      :label=\"textLabel\"\n      :placeholder=\"placeholder\"\n      :disabled=\"disabled || textDisabled\"\n      :required=\"textRequired\"\n      :mode=\"mode\"\n      :rules=\"textRules\"\n      v-bind=\"$attrs\"\n      @update:value=\"change\"\n    >\n      <template #label>\n        <slot name=\"label\" />\n      </template>\n      <template #suffix>\n        <slot name=\"suffix\" />\n      </template>\n    </LabeledInput>\n    <input\n      v-else\n      ref=\"text\"\n      v-model=\"string\"\n      class=\"input-string\"\n      :disabled=\"isView\"\n      :placeholder=\"placeholder\"\n      autocomplete=\"off\"\n      @input=\"change\"\n    >\n  </div>\n</template>\n\n<style lang='scss' scoped>\n.input-container {\n  display: flex;\n\n  &.select-after {\n    height: 100%;\n    flex-direction: row-reverse;\n\n    & .input-string {\n      border-radius: var(--border-radius) 0 0 var(--border-radius);\n      border-right: 0;\n      border-left-width: 1px;\n    }\n\n    & .in-input {\n      border-radius: 0 var(--border-radius) var(--border-radius) 0;\n\n      &.labeled-select {\n        .selected {\n          color: var(--input-text);\n          text-align: center;\n          margin-right: 1em;\n        }\n      }\n\n      &.focused:not(.vs__dropdown-up) {\n        border-bottom-right-radius: 0;\n      }\n\n      &.focused.vs__dropdown-up {\n        border-top-right-radius: 0;\n      }\n    }\n\n    .input-string {\n      &:hover:not(.focused):not(.disabled):not(:focus) {\n        padding-left: 10px !important;\n      }\n      &.focused, &:focus {\n        padding-left: 10px !important;\n      }\n    }\n  }\n\n  & .input-string {\n    padding-right: 0;\n    width: 60%;\n    flex-grow: 1;\n    border-radius: 0 var(--border-radius) var(--border-radius) 0;\n    border-left-width: 0;\n    margin-left: -1px;\n    position: relative;\n    display: table;\n    border-collapse: separate;\n\n    &:hover:not(.focused):not(.disabled):not(:focus):not(.view) {\n      border-left: 1px solid var(--input-hover-border);\n      border-right: 1px solid var(--input-hover-border);\n      padding-left: 9px;\n    }\n    &.focused, &:focus {\n      border-left: 1px solid var(--outline) !important;\n      border-right: 1px solid var(--outline) !important;\n      padding-left: 9px;\n    }\n  }\n\n  & .in-input {\n    margin-right: 0;\n\n    &:hover:not(.focused):not(.disabled):not(.view) {\n      border: 1px solid var(--input-hover-border) !important;\n    }\n\n    &.focused {\n      border: 1px solid var(--outline) !important;\n    }\n\n    &:hover:not(.focused):not(.disabled) {\n      border: 1px solid var(--input-hover-border) !important;\n    }\n\n    &.focused {\n      border: 1px solid var(--outline) !important;\n    }\n\n    &.labeled-select.focused :deep(),\n    &.unlabeled-select.focused :deep() {\n      outline: none;\n    }\n\n    &.labeled-select:not(.disabled):not(.view) :deep(),\n    &.unlabeled-select:not(.disabled):not(.view) :deep() {\n      border: solid 1px var(--input-border);\n    }\n\n    &.labeled-select :deep(),\n    &.unlabeled-select :deep() {\n      box-shadow: none;\n      width: 20%;\n      margin-right: 1px; // push the input box right so the full focus outline of the select can be seen, z-index borks\n      // position: relative;\n\n      .vs__selected {\n        color: var(--input-text);\n      }\n\n      .vs__dropdown-menu {\n        box-shadow: none;\n        .vs__dropdown-option {\n          padding: 3px 5px;\n        }\n      }\n\n      .v-select:not(.vs--disabled) {\n        .vs__dropdown-toggle {\n          border-radius: var(--border-radius) 0 0 var(--border-radius);\n        }\n        &.vs--open {\n          .vs__dropdown-toggle {\n            color: var(--outline) !important;\n          }\n        }\n      }\n    }\n  }\n}\n\n</style>\n"]}]}