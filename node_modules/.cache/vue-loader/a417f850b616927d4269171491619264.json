{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ClusterProviderIcon.vue?vue&type=style&index=0&id=4f930a1e&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ClusterProviderIcon.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLnJhbmNoZXItaWNvbi1maWxsIHsKICAgIGZpbGw6IHZhcigtLXByaW1hcnkpOwogIH0KICAuY2x1c3Rlci1pY29uIHsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgaGVpZ2h0OiAzMnB4OwogICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgICB3aWR0aDogNDJweDsKCiAgICAmLWJvcmRlciB7CiAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHZhcigtLWJvcmRlcik7CiAgICAgIGJvcmRlci1yYWRpdXM6IDVweDsKICAgICAgY29sb3I6IHZhcigtLWJvZHktdGV4dCkgIWltcG9ydGFudDsgLy8gIWltcG9ydGFudCBpcyBuZWVkZWQgdG8gb3ZlcnJpZGUgdGhlIGNvbG9yIHNldCBieSB0aGUgYmFkZ2Ugd2hlbiB0aGVyZSdzIGEgdHJhbnNwYXJlbnQgYmFja2dyb3VuZC4KICAgIH0KICB9CgogIC5jbHVzdGVyLWljb24tc21hbGwgewogICAgaGVpZ2h0OiAyNXB4OwogICAgd2lkdGg6IDI1cHg7CgogICAgLmNsdXN0ZXItb3MtbG9nbyB7CiAgICAgIHdpZHRoOiAyNXB4OwogICAgICBoZWlnaHQ6IDI1cHg7CiAgICB9CgogICAgLmNsdXN0ZXItYmFkZ2UtbG9nbyB7CiAgICAgIHdpZHRoOiAyNXB4OwogICAgICBoZWlnaHQ6IDI1cHg7CiAgICB9CiAgfQoKICAuY2x1c3Rlci1vcy1sb2dvIHsKICAgIHdpZHRoOiAzMnB4OwogICAgaGVpZ2h0OiAzMnB4OwogIH0KICAuY2x1c3Rlci1sb2NhbC1sb2dvIHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICB3aWR0aDogMjVweDsKICB9CiAgLmNsdXN0ZXItYmFkZ2UtbG9nbyB7CiAgICBtaW4td2lkdGg6IDQycHg7CiAgICBoZWlnaHQ6IDMycHg7CiAgICBwYWRkaW5nOiAwcHggNXB4OwogICAgZGlzcGxheTogZmxleDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICAgIGJvcmRlci1yYWRpdXM6IDVweDsKICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogIH0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ClusterProviderIcon.vue"], "names": [], "mappings": ";EAgFE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1I;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACd;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACd;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACd;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ClusterProviderIcon.vue", "sourceRoot": "", "sourcesContent": ["<script>\nexport default {\n  props: {\n    cluster: {\n      type:     Object,\n      required: true,\n    },\n    small: {\n      type:    Boolean,\n      default: false,\n    }\n  },\n\n  computed: {\n    useForIcon() {\n      return !!this.cluster?.badge?.iconText;\n    },\n    showBorders() {\n      return this.cluster?.badge?.color === 'transparent';\n    },\n  }\n};\n</script>\n\n<template>\n  <div\n    v-if=\"cluster\"\n    class=\"cluster-icon\"\n    :class=\"{'cluster-icon-small': small}\"\n  >\n    <div\n      v-if=\"useForIcon\"\n      class=\"cluster-badge-logo\"\n      :class=\"{ 'cluster-icon-border': showBorders}\"\n      :style=\"{ backgroundColor: cluster.badge.color, color: cluster.badge.textColor }\"\n    >\n      {{ cluster.badge.iconText }}\n    </div>\n    <!-- eslint-disable -->\n    <svg \n      v-else-if=\"cluster.isLocal && !cluster.isHarvester\" \n      class=\"cluster-local-logo\" \n      version=\"1.1\" \n      id=\"Layer_1\" \n      xmlns=\"http://www.w3.org/2000/svg\" \n      xmlns:xlink=\"http://www.w3.org/1999/xlink\" \n      x=\"0px\" \n      y=\"0px\" \n      viewBox=\"0 0 100 100\" \n      style=\"enable-background:new 0 0 100 100;\" \n      xml:space=\"preserve\">\n      <title>{{ t('nav.ariaLabel.clusterProvIcon', { cluster: 'local' }) }}</title>\n      <g>\n        <g>\n          <path class=\"rancher-icon-fill\" d=\"M26.0862026,44.4953918H8.6165142c-5.5818157,0-9.3979139-4.6252708-8.4802637-10.1311035l2.858391-17.210701\n            C3.912292,11.6477556,6.1382647,7.1128125,7.8419709,7.1128125s3.1788611,4.5368752,3.1788611,10.1186218v4.4837742\n            c0,5.5817471,4.4044495,9.5409164,9.9862652,9.5409164h5.0791054V44.4953918z\"/>\n        </g>\n        <path class=\"rancher-icon-fill\" d=\"M63.0214729,92.8871841H37.0862045c-6.0751343,0-11.0000019-4.9248657-11.0000019-11V30.3864384\n          c0-6.0751324,4.9248676-11,11.0000019-11h25.9352684c6.0751305,0,11.0000038,4.9248676,11.0000038,11v51.5007477\n          C74.0214767,87.9623184,69.0966034,92.8871841,63.0214729,92.8871841z\"/>\n        <g>\n          <path class=\"rancher-icon-fill\" d=\"M73.9137955,44.4953918h17.4696884c5.5818176,0,9.3979187-4.6252708,8.4802628-10.1311035\n            l-2.8583908-17.210701c-0.9176483-5.5058317-3.1436234-10.0407753-4.8473282-10.0407753\n            s-3.1788635,4.5368752-3.1788635,10.1186218v4.4837742c0,5.5817471-4.4044418,9.5409164-9.9862595,9.5409164h-5.0791092\n            V44.4953918z\"/>\n        </g>\n      </g>\n    </svg>\n    <!-- eslint-enable -->\n    <img\n      v-else-if=\"cluster.providerNavLogo\"\n      class=\"cluster-os-logo\"\n      :src=\"cluster.providerNavLogo\"\n      :alt=\"t('nav.ariaLabel.clusterProvIcon', { cluster: cluster.nameDisplay })\"\n    >\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .rancher-icon-fill {\n    fill: var(--primary);\n  }\n  .cluster-icon {\n    align-items: center;\n    display: flex;\n    height: 32px;\n    justify-content: center;\n    width: 42px;\n\n    &-border {\n      border: 1px solid var(--border);\n      border-radius: 5px;\n      color: var(--body-text) !important; // !important is needed to override the color set by the badge when there's a transparent background.\n    }\n  }\n\n  .cluster-icon-small {\n    height: 25px;\n    width: 25px;\n\n    .cluster-os-logo {\n      width: 25px;\n      height: 25px;\n    }\n\n    .cluster-badge-logo {\n      width: 25px;\n      height: 25px;\n    }\n  }\n\n  .cluster-os-logo {\n    width: 32px;\n    height: 32px;\n  }\n  .cluster-local-logo {\n    display: flex;\n    width: 25px;\n  }\n  .cluster-badge-logo {\n    min-width: 42px;\n    height: 32px;\n    padding: 0px 5px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 5px;\n    font-weight: bold;\n  }\n</style>\n"]}]}