{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/AppModal.vue?vue&type=template&id=529a58e2&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/AppModal.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPHRlbGVwb3J0IHRvPSIjbW9kYWxzIj4KICAgIDx0cmFuc2l0aW9uCiAgICAgIG5hbWU9Im1vZGFsLWZhZGUiCiAgICAgIGFwcGVhcgogICAgPgogICAgICA8ZGl2CiAgICAgICAgY2xhc3M9Im1vZGFsLW92ZXJsYXkiCiAgICAgICAgOmRhdGEtbW9kYWw9Im5hbWUiCiAgICAgICAgQGNsaWNrPSJoYW5kbGVDbGlja091dHNpZGUiCiAgICAgID4KICAgICAgICA8ZGl2CiAgICAgICAgICB2LWJpbmQ9IiRhdHRycyIKICAgICAgICAgIGlkPSJtb2RhbC1jb250YWluZXItZWxlbWVudCIKICAgICAgICAgIHJlZj0ibW9kYWxSZWYiCiAgICAgICAgICA6Y2xhc3M9ImN1c3RvbUNsYXNzIgogICAgICAgICAgY2xhc3M9Im1vZGFsLWNvbnRhaW5lciIKICAgICAgICAgIDpzdHlsZT0ibW9kYWxTdHlsZXMiCiAgICAgICAgICBAY2xpY2suc3RvcAogICAgICAgID4KICAgICAgICAgIDxzbG90PjwhLS1FbXB0eSBjb250ZW50LS0+PC9zbG90PgogICAgICAgIDwvZGl2PgogICAgICA8L2Rpdj4KICAgIDwvdHJhbnNpdGlvbj4KICA8L3RlbGVwb3J0Pgo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/AppModal.vue"], "names": [], "mappings": ";EA6KE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC;IACP;MACE,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B;QACE,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/AppModal.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent } from 'vue';\nimport { DEFAULT_FOCUS_TRAP_OPTS, useBasicSetupFocusTrap, getFirstFocusableElement } from '@shell/composables/focusTrap';\n\nexport const DEFAULT_ITERABLE_NODE_SELECTOR = 'body;';\n\nexport default defineComponent({\n  name: 'AppModal',\n\n  emits: ['close'],\n\n  inheritAttrs: false,\n  props:        {\n    /**\n     * If set to false, it will not be possible to close modal by clicking on\n     * the background or by pressing Esc key.\n     */\n    clickToClose: {\n      type:    Boolean,\n      default: true,\n    },\n    /**\n     * Width in pixels or percents (50, \"50px\", \"50%\").\n     *\n     * Supported string values are <number>% and <number>px\n     */\n    width: {\n      type:    [Number, String],\n      default: 600,\n      validator(value) {\n        if (typeof value === 'number') {\n          return value > 0;\n        }\n\n        if (typeof value === 'string') {\n          return /^(0*(?:[1-9][0-9]*|0)\\.?\\d*)+(px|%)$/.test(value);\n        }\n\n        return false;\n      }\n    },\n    /**\n     * List of class that will be applied to the modal window\n     */\n    customClass: {\n      type:    String,\n      default: '',\n    },\n    /**\n     * Style that will be applied to the modal window\n     */\n    styles: {\n      type:    String,\n      default: '',\n    },\n    /**\n     * Name of the modal\n     */\n    name: {\n      type:    String,\n      default: '',\n    },\n    /**\n     * trigger focus trap\n     */\n    triggerFocusTrap: {\n      type:    Boolean,\n      default: false,\n    },\n    /**\n     * forcefully set return focus element based on this selector\n     */\n    returnFocusSelector: {\n      type:    String,\n      default: '',\n    },\n    /**\n     * will return focus to the first iterable node of this container select\n     */\n    returnFocusFirstIterableNodeSelector: {\n      type:    String,\n      default: DEFAULT_ITERABLE_NODE_SELECTOR,\n    }\n  },\n  computed: {\n    modalWidth(): string {\n      if (this.isValidWidth(this.width)) {\n        const uom = typeof (this.width) === 'number' ? 'px' : '';\n\n        return `${ this.width }${ uom }`;\n      }\n\n      return '600px';\n    },\n    stylesPropToObj(): object {\n      return this.styles.split(';')\n        .map((line) => line.trim().split(':'))\n        .reduce((lines, [key, val]) => {\n          return {\n            ...lines,\n            [key]: val\n          };\n        }, { });\n    },\n    modalStyles(): object {\n      return {\n        width: this.modalWidth,\n        ...this.stylesPropToObj,\n      };\n    },\n  },\n  setup(props) {\n    if (props.triggerFocusTrap) {\n      let opts:any = DEFAULT_FOCUS_TRAP_OPTS;\n\n      // if we have a \"returnFocusFirstIterableNodeSelector\" on top of \"returnFocusSelector\"\n      // then we will use \"returnFocusFirstIterableNodeSelector\" as a fallback of \"returnFocusSelector\"\n      if (props.returnFocusFirstIterableNodeSelector && props.returnFocusFirstIterableNodeSelector !== DEFAULT_ITERABLE_NODE_SELECTOR && props.returnFocusSelector) {\n        opts = {\n          ...DEFAULT_FOCUS_TRAP_OPTS,\n          setReturnFocus: () => {\n            return document.querySelector(props.returnFocusSelector) ? props.returnFocusSelector : getFirstFocusableElement(document.querySelector(props.returnFocusFirstIterableNodeSelector));\n          }\n        };\n      // otherwise, if we are sure of permanent existance of \"returnFocusSelector\"\n      // we just return to that element\n      } else if (props.returnFocusSelector) {\n        opts = {\n          ...DEFAULT_FOCUS_TRAP_OPTS,\n          setReturnFocus: props.returnFocusSelector\n        };\n      }\n\n      useBasicSetupFocusTrap('#modal-container-element', opts);\n    }\n  },\n  mounted() {\n    document.addEventListener('keydown', this.handleEscapeKey);\n  },\n  beforeUnmount() {\n    document.removeEventListener('keydown', this.handleEscapeKey);\n  },\n  methods: {\n    handleClickOutside(event: MouseEvent) {\n      if (\n        this.clickToClose &&\n        this.$refs.modalRef &&\n        !(this.$refs.modalRef as HTMLElement).contains(event.target as Node)\n      ) {\n        this.$emit('close');\n      }\n    },\n    handleEscapeKey(event: KeyboardEvent) {\n      if (this.clickToClose && event.key === 'Escape') {\n        this.$emit('close');\n      }\n    },\n    isValidWidth(value: number | string) {\n      if (typeof value === 'number') {\n        return value > 0;\n      }\n\n      if (typeof value === 'string') {\n        return /^(0*(?:[1-9][0-9]*|0)\\.?\\d*)+(px|%)$/.test(value);\n      }\n\n      return false;\n    }\n  }\n});\n</script>\n\n<template>\n  <teleport to=\"#modals\">\n    <transition\n      name=\"modal-fade\"\n      appear\n    >\n      <div\n        class=\"modal-overlay\"\n        :data-modal=\"name\"\n        @click=\"handleClickOutside\"\n      >\n        <div\n          v-bind=\"$attrs\"\n          id=\"modal-container-element\"\n          ref=\"modalRef\"\n          :class=\"customClass\"\n          class=\"modal-container\"\n          :style=\"modalStyles\"\n          @click.stop\n        >\n          <slot><!--Empty content--></slot>\n        </div>\n      </div>\n    </transition>\n  </teleport>\n</template>\n\n<style lang=\"scss\">\n  .modal-overlay {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100vw;\n    height: 100vh;\n    background-color: var(--overlay-bg);\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    z-index: z-index('modalOverlay');\n\n    .modal-container {\n      background-color: var(--modal-bg);\n      border-radius: var(--border-radius);\n      max-height: 95vh;\n      overflow: auto;\n      border: 2px solid var(--modal-border);\n    }\n  }\n\n  .modal-fade-enter-active,\n  .modal-fade-leave-active {\n    transition: opacity 200ms;\n  }\n\n  .modal-fade-enter,\n  .modal-fade-leave-to {\n    opacity: 0;\n  }\n</style>\n"]}]}