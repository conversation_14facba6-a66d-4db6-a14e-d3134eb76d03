{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/Namespace.vue?vue&type=style&index=0&id=066e7924&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/Namespace.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5oZWFkZXJzIHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBmbGV4LWRpcmVjdGlvbjogcm93OwogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1ldmVubHk7CiAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkIHZhcigtLWJvcmRlcik7CiAgICBoZWlnaHQ6IDMwcHg7CgogICAgZGl2IHsKICAgICAgICB3aWR0aDogMTAwJTsKICAgIH0KfQoKLnJvdzpub3QoOmxhc3Qtb2YtdHlwZSkgewogIG1hcmdpbi1ib3R0b206IDEwcHg7Cn0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/Namespace.vue"], "names": [], "mappings": ";AAoGA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEZ,CAAC,CAAC,EAAE;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;AACJ;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACrB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/Namespace.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport Row from './NamespaceRow';\nimport { QUOTA_COMPUTED } from './shared';\n\nexport default {\n  components: { Row },\n\n  props: {\n    mode: {\n      type:     String,\n      required: true,\n    },\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    project: {\n      type:     Object,\n      required: true\n    },\n    types: {\n      type:    Array,\n      default: () => {\n        return [];\n      }\n    }\n  },\n\n  data() {\n    return { rows: {} };\n  },\n\n  computed: {\n    ...QUOTA_COMPUTED,\n    projectResourceQuotaLimits() {\n      return this.project?.spec?.resourceQuota?.limit || {};\n    },\n    namespaceResourceQuotaLimits() {\n      return this.project.namespaces.map((namespace) => ({\n        ...namespace.resourceQuota.limit,\n        id: namespace.id\n      }));\n    },\n    editableLimits() {\n      return Object.keys(this.projectResourceQuotaLimits);\n    },\n    defaultResourceQuotaLimits() {\n      return this.project.spec.namespaceDefaultResourceQuota.limit;\n    }\n  },\n\n  methods: {\n    remainingTypes(currentType) {\n      return this.mappedTypes\n        .filter((type) => !this.types.includes(type.value) || type.value === currentType);\n    },\n    update(key, value) {\n      const resourceQuota = {\n        limit: {\n          ...this.value.resourceQuota.limit,\n          [key]: value\n        }\n      };\n\n      this.value['resourceQuota'] = resourceQuota;\n    }\n  },\n};\n</script>\n<template>\n  <div>\n    <div class=\"headers mb-10\">\n      <div class=\"mr-10\">\n        <label>{{ t('resourceQuota.headers.resourceType') }}</label>\n      </div>\n      <div class=\"mr-10\">\n        <label>{{ t('resourceQuota.headers.projectResourceAvailability') }}</label>\n      </div>\n      <div class=\"mr-20\">\n        <label>{{ t('resourceQuota.headers.limit') }}</label>\n      </div>\n    </div>\n    <Row\n      v-for=\"(limit, i) in editableLimits\"\n      :key=\"i\"\n      :value=\"value.resourceQuota\"\n      :namespace=\"value\"\n      :mode=\"mode\"\n      :types=\"mappedTypes\"\n      :type=\"limit\"\n      :project-resource-quota-limits=\"projectResourceQuotaLimits\"\n      :default-resource-quota-limits=\"defaultResourceQuotaLimits\"\n      :namespace-resource-quota-limits=\"namespaceResourceQuotaLimits\"\n      @update:value=\"update\"\n    />\n  </div>\n</template>\n<style lang=\"scss\" scoped>\n.headers {\n    display: flex;\n    flex-direction: row;\n    justify-content: space-evenly;\n    align-items: center;\n    border-bottom: 1px solid var(--border);\n    height: 30px;\n\n    div {\n        width: 100%;\n    }\n}\n\n.row:not(:last-of-type) {\n  margin-bottom: 10px;\n}\n</style>\n"]}]}