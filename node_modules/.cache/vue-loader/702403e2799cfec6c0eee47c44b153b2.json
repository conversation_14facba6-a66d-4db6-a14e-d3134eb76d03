{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DashboardMetrics.vue?vue&type=style&index=0&id=e543bcdc&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DashboardMetrics.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouZGFzaGJvYXJkLW1ldHJpY3Mgew0KICAmIDpkZWVwKCkgew0KICAgIC5leHRlcm5hbC1saW5rIHsNCiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICAgIGxlZnQ6IDIwMHB4Ow0KICAgICAgdG9wOiAtNDVweDsNCiAgICB9DQoNCiAgICAuZnJhbWUgew0KICAgICAgdG9wOiAwOw0KICAgIH0NCiAgfQ0KfQ0KDQouZGFzaGJvYXJkLW1ldHJpY3MuZXh0ZXJuYWwtbGluay1wdWxsLWxlZnQgew0KICAmIDpkZWVwKCkgew0KICAgIC5leHRlcm5hbC1saW5rIHsNCiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICAgIGxlZnQ6IDEwcHg7DQogICAgICB0b3A6IC00N3B4Ow0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DashboardMetrics.vue"], "names": [], "mappings": ";AAqGA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACjB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,EAAE,CAAC;IACR;EACF;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACzC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ;EACF;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DashboardMetrics.vue", "sourceRoot": "", "sourcesContent": ["<script>\r\nimport DashboardOptions from '@shell/components/DashboardOptions';\r\nimport GrafanaDashboard from '@shell/components/GrafanaDashboard';\r\nimport { mapGetters } from 'vuex';\r\n\r\nexport default {\r\n  components: { DashboardOptions, GrafanaDashboard },\r\n  props:      {\r\n    detailUrl: {\r\n      type:     String,\r\n      required: true,\r\n    },\r\n    summaryUrl: {\r\n      type:    String,\r\n      default: '',\r\n    },\r\n    vars: {\r\n      type:    Object,\r\n      default: () => ({})\r\n    },\r\n    graphHeight: {\r\n      type:     String,\r\n      required: true\r\n    },\r\n    hasSummaryAndDetail: {\r\n      type:    Boolean,\r\n      default: true,\r\n    },\r\n    // change the grafana url prefix for local clusters in certain monitoring versions\r\n    // project monitoring (projectHelmCharts) supply a grafana url that never needs to be modified in this way\r\n    modifyPrefix: {\r\n      type:    Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      graphOptions: {\r\n        range: '5m', refreshRate: '30s', type: 'detail'\r\n      }\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapGetters(['prefs/theme']),\r\n    graphBackgroundColor() {\r\n      return this.theme === 'dark' ? '#2e3035' : '#f3f4f9';\r\n    },\r\n    theme() {\r\n      return this['prefs/theme'];\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<template>\r\n  <div\r\n    class=\"dashboard-metrics\"\r\n    :class=\"!hasSummaryAndDetail && 'external-link-pull-left'\"\r\n  >\r\n    <div class=\"graph-options mb-10\">\r\n      <DashboardOptions\r\n        v-model:value=\"graphOptions\"\r\n        :has-summary-and-detail=\"hasSummaryAndDetail\"\r\n      />\r\n    </div>\r\n    <div class=\"info\">\r\n      <slot />\r\n    </div>\r\n    <div\r\n      class=\"graphs\"\r\n      :style=\"{height: graphHeight}\"\r\n    >\r\n      <GrafanaDashboard\r\n        v-if=\"graphOptions.type === 'detail'\"\r\n        key=\"'detail'\"\r\n        class=\"col span-12 detail\"\r\n        :background-color=\"graphBackgroundColor\"\r\n        :theme=\"theme\"\r\n        :refresh-rate=\"graphOptions.refreshRate\"\r\n        :range=\"graphOptions.range\"\r\n        :url=\"detailUrl\"\r\n        :vars=\"vars\"\r\n        :modify-prefix=\"modifyPrefix\"\r\n      />\r\n      <GrafanaDashboard\r\n        v-else\r\n        key=\"'summary'\"\r\n        class=\"col span-12 summary\"\r\n        :background-color=\"graphBackgroundColor\"\r\n        :theme=\"theme\"\r\n        :refresh-rate=\"graphOptions.refreshRate\"\r\n        :range=\"graphOptions.range\"\r\n        :url=\"summaryUrl\"\r\n        :vars=\"vars\"\r\n        :modify-prefix=\"modifyPrefix\"\r\n      />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.dashboard-metrics {\r\n  & :deep() {\r\n    .external-link {\r\n      position: absolute;\r\n      left: 200px;\r\n      top: -45px;\r\n    }\r\n\r\n    .frame {\r\n      top: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.dashboard-metrics.external-link-pull-left {\r\n  & :deep() {\r\n    .external-link {\r\n      position: absolute;\r\n      left: 10px;\r\n      top: -47px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}