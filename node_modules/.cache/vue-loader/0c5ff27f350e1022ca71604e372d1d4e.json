{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/LandingPagePreference.vue?vue&type=style&index=0&id=b47e8664&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/LandingPagePreference.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLmN1c3RvbS1wYWdlIHsKICAgIC5jdXN0b20tcGFnZS1vcHRpb25zIHsKICAgICAgbWFyZ2luOiA1cHggMCAwIDIwcHg7CiAgICAgIG1pbi13aWR0aDogMzIwcHg7CiAgICAgIHdpZHRoOiBmaXQtY29udGVudDsKICAgIH0KICB9CiAgLnNldC1sYW5kaW5nLWxlYWRpbiB7CiAgICBwYWRkaW5nLWJvdHRvbTogMTBweDsKICB9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/LandingPagePreference.vue"], "names": [], "mappings": ";EA6IE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB;EACF;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACtB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/LandingPagePreference.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapPref, AFTER_LOGIN_ROUTE } from '@shell/store/prefs';\nimport { mapFeature, MULTI_CLUSTER } from '@shell/store/features';\nimport { RadioGroup, RadioButton } from '@components/Form/Radio';\n\nimport Select from '@shell/components/form/Select';\nimport { MANAGEMENT } from '@shell/config/types';\nimport { filterHiddenLocalCluster, filterOnlyKubernetesClusters } from '@shell/utils/cluster';\n\nexport default {\n  components: {\n    RadioGroup,\n    RadioButton,\n    Select,\n  },\n\n  async fetch() {\n    this.clusters = await this.$store.dispatch('management/findAll', { type: MANAGEMENT.CLUSTER });\n  },\n\n  data() {\n    // Store the route as it was on page load (before the user may have changed it)\n    const customRoute = this.$store.getters['prefs/get'](AFTER_LOGIN_ROUTE);\n\n    return { clusters: [], customRoute };\n  },\n\n  computed: {\n    afterLoginRoute: mapPref(AFTER_LOGIN_ROUTE),\n    mcm:             mapFeature(MULTI_CLUSTER),\n\n    routeFromDropdown: {\n      get() {\n        const route = this.customRoute || {};\n        const out = this.routeDropdownOptions.find((opt) => opt.value.name === route.name && opt.value.params?.cluster === route.params?.cluster);\n\n        return out || this.routeDropdownOptions[0];\n      },\n      set(neu) {\n        this.customRoute = neu;\n        this.afterLoginRoute = neu;\n      }\n    },\n\n    routeRadioOptions() {\n      const options = [\n        {\n          label: this.t('landing.landingPrefs.options.homePage'),\n          value: 'home'\n        },\n        {\n          label: this.t('landing.landingPrefs.options.lastVisited'),\n          value: 'last-visited'\n        },\n        {\n          label: this.t('landing.landingPrefs.options.custom'),\n          value: 'dropdown'\n        }\n      ];\n\n      // Remove the last option if not multi-cluster\n      if (!this.mcm) {\n        options.pop();\n      }\n\n      return options;\n    },\n\n    routeDropdownOptions() {\n      // Drop-down shows list of clusters that can ber set as login landing page\n      const out = [];\n      const kubeClusters = filterHiddenLocalCluster(filterOnlyKubernetesClusters(this.clusters, this.$store), this.$store);\n\n      kubeClusters.forEach((c) => {\n        if (c.isReady) {\n          out.push({\n            label: c.nameDisplay,\n            value: {\n              name:   'c-cluster',\n              params: { cluster: c.id }\n            }\n          });\n        }\n      });\n\n      out.sort((a, b) => (a.label > b.label) ? 1 : ((b.label > a.label) ? -1 : 0));\n\n      return out;\n    }\n  },\n\n  methods: {\n    updateLoginRoute(neu) {\n      if (neu) {\n        this.afterLoginRoute = neu;\n      } else {\n        this.afterLoginRoute = this.routeFromDropdown?.value || this.routeDropdownOptions[0]?.value;\n      }\n    },\n  }\n};\n</script>\n\n<template>\n  <div>\n    <p class=\"set-landing-leadin\">\n      {{ t('landing.landingPrefs.body') }}\n    </p>\n    <RadioGroup\n      id=\"login-route\"\n      :value=\"afterLoginRoute\"\n      name=\"login-route\"\n      :options=\"routeRadioOptions\"\n      @update:value=\"updateLoginRoute\"\n    >\n      <template #2=\"{option}\">\n        <div class=\"custom-page\">\n          <RadioButton\n            :label=\"option.label\"\n            :val=\"false\"\n            :value=\"afterLoginRoute=== 'home' || afterLoginRoute === 'last-visited'\"\n            :v-bind=\"$attrs\"\n            :prevent-focus-on-radio-groups=\"true\"\n            @update:value=\"updateLoginRoute(null)\"\n          />\n          <Select\n            v-model:value=\"routeFromDropdown\"\n            :aria-label=\"t('landing.landingPrefs.ariaLabelTakeMeToCluster')\"\n            :searchable=\"true\"\n            :disabled=\"afterLoginRoute === 'home' || afterLoginRoute === 'last-visited'\"\n            :clearable=\"false\"\n            :options=\"routeDropdownOptions\"\n            class=\"custom-page-options\"\n          />\n        </div>\n      </template>\n    </RadioGroup>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .custom-page {\n    .custom-page-options {\n      margin: 5px 0 0 20px;\n      min-width: 320px;\n      width: fit-content;\n    }\n  }\n  .set-landing-leadin {\n    padding-bottom: 10px;\n  }\n</style>\n"]}]}