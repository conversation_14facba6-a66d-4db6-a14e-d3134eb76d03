{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/ConfigSummary.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/ConfigSummary.vue", "mtime": 1755002061656}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/ConfigSummary.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAElD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAErB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC9C,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACxD,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1D,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MAC1D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACpD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UACvC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAChD,CAAC;MACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;MAElC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE;MACtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC7B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnB,CAAC;;MAED,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtD,CAAC,CAAC;;MAEF,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAClE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B;EACF;AACF,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/ConfigSummary.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent, PropType } from 'vue';\nimport { EKSConfig, EKSNodeGroup, NormanCluster } from '../../types';\nimport LabelValue from '@shell/components/LabelValue.vue';\nimport Banner from '@components/Banner/Banner.vue';\n\nexport default defineComponent({\n  name: 'ConfigSummary',\n\n  components: {\n    LabelValue,\n    Banner\n  },\n\n  props: {\n    normanCluster: {\n      type:     Object as PropType<NormanCluster>,\n      required: true\n    },\n    config: {\n      type:     Object as PropType<EKSConfig>,\n      required: true\n    },\n    nodeGroups: {\n      type:    Array as PropType<EKSNodeGroup[]>,\n      default: () => []\n    },\n    region: {\n      type:    String,\n      default: ''\n    }\n  },\n\n  computed: {\n    clusterName(): string {\n      return this.normanCluster?.name || 'Not set';\n    },\n\n    awsRegion(): string {\n      return this.config?.region || this.region || 'Not set';\n    },\n\n    kubernetesVersion(): string {\n      return this.config?.kubernetesVersion || 'Latest stable';\n    },\n\n    totalNodes(): { min: number; max: number; desired: number } {\n      const totals = this.nodeGroups.reduce((acc, group) => {\n        return {\n          min:     acc.min + (group.minSize || 0),\n          max:     acc.max + (group.maxSize || 0),\n          desired: acc.desired + (group.desiredSize || 0)\n        };\n      }, { min: 0, max: 0, desired: 0 });\n\n      return totals;\n    },\n\n    primaryNodeGroup(): EKSNodeGroup | null {\n      return this.nodeGroups[0] || null;\n    },\n\n    estimatedMonthlyCost(): string {\n      // Simple cost estimation\n      const costMap: Record<string, number> = {\n        't3.small':   15,\n        't3.medium':  30,\n        't3.large':   60,\n        't3.xlarge':  120,\n        't4g.small':  12,\n        't4g.medium': 24,\n        't4g.large':  48,\n        'm5.large':   70,\n        'm5.xlarge':  140,\n        'm6i.large':  70,\n        'm6i.xlarge': 140,\n      };\n\n      let totalCost = 0;\n      this.nodeGroups.forEach(group => {\n        const instanceCost = group.instanceType ? costMap[group.instanceType] || 50 : 50;\n        totalCost += instanceCost * (group.desiredSize || 2);\n      });\n\n      // Add EKS control plane cost ($0.10/hour = ~$73/month)\n      totalCost += 73;\n\n      return `$${totalCost}`;\n    },\n\n    networkingMode(): string {\n      if (this.config?.publicAccess && !this.config?.privateAccess) {\n        return 'Public';\n      } else if (!this.config?.publicAccess && this.config?.privateAccess) {\n        return 'Private';\n      } else if (this.config?.publicAccess && this.config?.privateAccess) {\n        return 'Public and Private';\n      }\n      return 'Default (Public)';\n    }\n  }\n});\n</script>\n\n<template>\n  <div class=\"config-summary\">\n    <div class=\"summary-section\">\n      <h3>\n        <i class=\"icon icon-cluster\" />\n        Cluster Configuration\n      </h3>\n      \n      <div class=\"summary-grid\">\n        <LabelValue\n          name=\"Cluster Name\"\n          :value=\"clusterName\"\n          class=\"summary-item\"\n        />\n        \n        <LabelValue\n          name=\"AWS Region\"\n          :value=\"awsRegion\"\n          class=\"summary-item\"\n        />\n        \n        <LabelValue\n          name=\"Kubernetes Version\"\n          :value=\"kubernetesVersion\"\n          class=\"summary-item\"\n        />\n        \n        <LabelValue\n          name=\"Network Access\"\n          :value=\"networkingMode\"\n          class=\"summary-item\"\n        />\n      </div>\n    </div>\n\n    <div class=\"summary-section mt-20\">\n      <h3>\n        <i class=\"icon icon-nodes\" />\n        Node Configuration\n      </h3>\n      \n      <div\n        v-if=\"primaryNodeGroup\"\n        class=\"node-summary\"\n      >\n        <div class=\"summary-grid\">\n          <LabelValue\n            name=\"Instance Type\"\n            :value=\"primaryNodeGroup.instanceType\"\n            class=\"summary-item\"\n          />\n          \n          <LabelValue\n            name=\"Disk Size\"\n            :value=\"`${primaryNodeGroup.diskSize} GB`\"\n            class=\"summary-item\"\n          />\n          \n          <LabelValue\n            name=\"Auto-scaling\"\n            :value=\"`${totalNodes.min} - ${totalNodes.max} nodes`\"\n            class=\"summary-item\"\n          />\n          \n          <LabelValue\n            name=\"Initial Size\"\n            :value=\"`${totalNodes.desired} nodes`\"\n            class=\"summary-item\"\n          />\n        </div>\n      </div>\n\n      <div\n        v-if=\"nodeGroups.length > 1\"\n        class=\"mt-10\"\n      >\n        <Banner color=\"info\">\n          <p>{{ nodeGroups.length }} node groups configured</p>\n        </Banner>\n      </div>\n    </div>\n\n    <div class=\"summary-section mt-20 cost-section\">\n      <h3>\n        <i class=\"icon icon-dollar\" />\n        Estimated Cost\n      </h3>\n      \n      <div class=\"cost-breakdown\">\n        <div class=\"cost-main\">\n          <span class=\"cost-label\">Monthly estimate:</span>\n          <span class=\"cost-value\">{{ estimatedMonthlyCost }}</span>\n        </div>\n        <p class=\"cost-disclaimer\">\n          * This is a rough estimate based on instance types and does not include data transfer, storage, or other AWS services.\n        </p>\n      </div>\n    </div>\n\n    <div class=\"summary-section mt-20\">\n      <Banner\n        color=\"success\"\n        class=\"ready-banner\"\n      >\n        <div class=\"ready-content\">\n          <i class=\"icon icon-checkmark icon-2x\" />\n          <div>\n            <h4>Ready to create your cluster!</h4>\n            <p>Your cluster will be created with production-ready defaults including:</p>\n            <ul>\n              <li>Automatic security updates</li>\n              <li>Network isolation and security groups</li>\n              <li>CloudWatch logging enabled</li>\n              <li>IAM roles for service accounts (IRSA)</li>\n            </ul>\n          </div>\n        </div>\n      </Banner>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.config-summary {\n  .summary-section {\n    padding: 20px;\n    background: var(--body-bg);\n    border: 1px solid var(--border);\n    border-radius: var(--border-radius);\n\n    h3 {\n      margin: 0 0 20px 0;\n      font-size: 18px;\n      font-weight: 600;\n      display: flex;\n      align-items: center;\n      gap: 10px;\n      color: var(--text-default);\n\n      i {\n        color: var(--primary);\n      }\n    }\n  }\n\n  .summary-grid {\n    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n    gap: 20px;\n\n    .summary-item {\n      padding: 10px;\n      background: var(--nav-bg);\n      border-radius: var(--border-radius);\n      \n      ::v-deep .labeled-value {\n        .label {\n          color: var(--text-muted);\n          font-size: 12px;\n          text-transform: uppercase;\n          margin-bottom: 5px;\n        }\n\n        .value {\n          color: var(--text-default);\n          font-size: 16px;\n          font-weight: 500;\n        }\n      }\n    }\n  }\n\n  .cost-section {\n    background: linear-gradient(135deg, var(--body-bg) 0%, var(--nav-bg) 100%);\n  }\n\n  .cost-breakdown {\n    .cost-main {\n      display: flex;\n      align-items: baseline;\n      gap: 15px;\n      margin-bottom: 10px;\n\n      .cost-label {\n        font-size: 16px;\n        color: var(--text-muted);\n      }\n\n      .cost-value {\n        font-size: 32px;\n        font-weight: 600;\n        color: var(--success);\n      }\n    }\n\n    .cost-disclaimer {\n      font-size: 12px;\n      color: var(--text-muted);\n      font-style: italic;\n      margin: 0;\n    }\n  }\n\n  .ready-banner {\n    .ready-content {\n      display: flex;\n      gap: 20px;\n      align-items: flex-start;\n\n      i {\n        color: var(--success);\n        flex-shrink: 0;\n      }\n\n      h4 {\n        margin: 0 0 10px 0;\n        font-size: 18px;\n        font-weight: 600;\n      }\n\n      p {\n        margin: 0 0 10px 0;\n      }\n\n      ul {\n        margin: 0;\n        padding-left: 20px;\n\n        li {\n          margin: 5px 0;\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}