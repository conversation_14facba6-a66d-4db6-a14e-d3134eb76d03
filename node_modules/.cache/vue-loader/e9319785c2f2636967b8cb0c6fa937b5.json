{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GradientBox.vue?vue&type=template&id=c4203070&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GradientBox.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdgogICAgY2xhc3M9ImdyYWRpZW50LWJveCIKICAgIDpjbGFzcz0ieydzaG93LXRhYic6IHNob3dUYWIsICdwbGFpbic6IHBsYWlufSIKICAgIDpzdHlsZT0ic3R5bGUiCiAgPgogICAgPHNsb3QgLz4KICA8L2Rpdj4K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GradientBox.vue"], "names": [], "mappings": ";EA8DE,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf;IACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GradientBox.vue", "sourceRoot": "", "sourcesContent": ["<script>\nexport default {\n  props: {\n    // a \"r, g, b\" tuple\n    primaryColorVar: {\n      type:    String,\n      default: null,\n    },\n\n    // Show the left side\n    showTab: {\n      type:    Boolean,\n      default: true,\n    },\n    showSolid: {\n      type:    Boolean,\n      default: false,\n    },\n    backgroundOpacityAdjustment: {\n      type:    Number,\n      default: 1\n    },\n    plain: {\n      type:    Boolean,\n      default: false,\n    }\n  },\n\n  computed: {\n    leftColor() {\n      return this.showSolid ? this.primaryColor : this.customizePrimaryColorOpacity(0.25 * this.backgroundOpacityAdjustment);\n    },\n\n    rightColor() {\n      return this.showSolid ? this.primaryColor : this.customizePrimaryColorOpacity(0.125 * this.backgroundOpacityAdjustment);\n    },\n\n    primaryColor() {\n      return this.customizePrimaryColorOpacity(1);\n    },\n\n    style() {\n      if (!this.plain) {\n        const background = `background: transparent linear-gradient(94deg, ${ this.leftColor } 0%, ${ this.rightColor } 100%) 0% 0% no-repeat padding-box;`;\n        const borderLeft = this.showTab ? `border-left: 9px solid ${ this.primaryColor };` : '';\n\n        return `${ background }${ borderLeft }`;\n      }\n\n      return '';\n    },\n  },\n\n  methods: {\n    customizePrimaryColorOpacity(opacity) {\n      return `rgba(var(${ this.primaryColorVar }), ${ opacity })`;\n    }\n  }\n};\n</script>\n\n<template>\n  <div\n    class=\"gradient-box\"\n    :class=\"{'show-tab': showTab, 'plain': plain}\"\n    :style=\"style\"\n  >\n    <slot />\n  </div>\n</template>\n\n<style lang=\"scss\">\n  .gradient-box {\n      border-radius: 5px;\n  }\n </style>\n\n<style lang=\"scss\" scoped>\n  .gradient-box.plain {\n      border: 1px solid var(--border);\n  }\n</style>\n"]}]}