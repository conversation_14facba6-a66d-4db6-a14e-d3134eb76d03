{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetSummary.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetSummary.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetSummary.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE7D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;EAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACd,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR,CAAC;;AAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;EACxD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACd,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR,CAAC;;AAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;EAEb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;EAE3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACnB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEzH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX;;MAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;MAElG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QAC1C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;;UAEnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR;;QAEA,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE/F,CAAC,EAAE,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;;UAEpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR;;QAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEjD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;;UAErB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR;;QAEA,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;UAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1H,CAAC,CAAC;;QAEF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;;UAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR;;QAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE9H,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;;UAEvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR;;QAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAExH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;;UAE1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACxB,CAAC,CAAC;;MAEF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC;IACJ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACpG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC3B,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE9B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B;QACF,CAAC,CAAC;;MAEJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC;IACJ,CAAC;;EAEH,CAAC;;AAEH,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetSummary.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { STATES, STATES_ENUM } from '@shell/plugins/dashboard-store/resource-class';\nimport FleetStatus from '@shell/components/fleet/FleetStatus';\n\nconst getResourcesDefaultState = (labelGetter, stateKey) => {\n  return [\n    STATES_ENUM.READY,\n    STATES_ENUM.NOT_READY,\n    STATES_ENUM.WAIT_APPLIED,\n    STATES_ENUM.MODIFIED,\n    STATES_ENUM.MISSING,\n    STATES_ENUM.ORPHANED,\n    STATES_ENUM.UNKNOWN,\n  ].reduce((acc, state) => {\n    acc[state] = {\n      count:  0,\n      color:  STATES[state].color,\n      label:  labelGetter(`${ stateKey }.${ state }`, null, STATES[state].label ),\n      status: state\n    };\n\n    return acc;\n  }, {});\n};\n\nconst getBundlesDefaultState = (labelGetter, stateKey) => {\n  return [\n    STATES_ENUM.READY,\n    STATES_ENUM.INFO,\n    STATES_ENUM.WARNING,\n    STATES_ENUM.NOT_READY,\n    STATES_ENUM.ERROR,\n    STATES_ENUM.ERR_APPLIED,\n    STATES_ENUM.WAIT_APPLIED,\n    STATES_ENUM.UNKNOWN,\n  ].reduce((acc, state) => {\n    acc[state] = {\n      count:  0,\n      color:  STATES[state].color,\n      label:  labelGetter(`${ stateKey }.${ state }`, null, STATES[state].label ),\n      status: state\n    };\n\n    return acc;\n  }, {});\n};\n\nexport default {\n\n  name: 'FleetSummary',\n\n  components: { FleetStatus },\n\n  props: {\n    bundles: {\n      type:    Array,\n      default: () => [],\n    },\n    value: {\n      type:     Object,\n      required: true,\n    },\n\n    stateKey: {\n      type:    String,\n      default: 'fleet.fleetSummary.state'\n    },\n  },\n\n  computed: {\n\n    repoName() {\n      return this.value.metadata.name;\n    },\n\n    repoNamespace() {\n      return this.value.metadata.namespace;\n    },\n\n    bundleCounts() {\n      const resources = this.bundles.filter((item) => item.namespace === this.repoNamespace && item.repoName === this.repoName);\n\n      if (!resources.length) {\n        return [];\n      }\n\n      const out = { ...getBundlesDefaultState(this.$store.getters['i18n/withFallback'], this.stateKey) };\n\n      resources.forEach(({ status, metadata }) => {\n        if (!status) {\n          out[STATES_ENUM.UNKNOWN].count += 1;\n\n          return;\n        }\n\n        const k = status?.summary?.ready > 0 && status?.summary.desiredReady === status?.summary?.ready;\n\n        if (k) {\n          out.ready.count += 1;\n\n          return;\n        }\n\n        const state = metadata.state?.name?.toLowerCase();\n\n        if (state && out[state]) {\n          out[state].count += 1;\n\n          return;\n        }\n\n        const { conditions } = status;\n\n        const notReady = conditions.find(({ transitioning, message }) => {\n          return transitioning && !message.includes(STATES_ENUM.ERROR) && !message.toLowerCase().includes(STATES_ENUM.ERR_APPLIED);\n        });\n\n        if (!!notReady) {\n          out.notready.count += 1;\n\n          return;\n        }\n\n        // check conditions\n        const errApplied = conditions.find(({ error, message }) => !!error && message.toLowerCase().includes(STATES_ENUM.ERR_APPLIED));\n\n        if (errApplied) {\n          out[STATES_ENUM.ERR_APPLIED].count += 1;\n\n          return;\n        }\n\n        const errorState = conditions.find(({ error, message }) => !!error && message.toLowerCase().includes(STATES_ENUM.ERROR));\n\n        if (out[errorState]) {\n          out[errorState].count += 1;\n\n          return;\n        }\n\n        out.unknown.count += 1;\n      });\n\n      return Object.values(out).map((item) => {\n        item.value = item.count;\n\n        return item;\n      });\n    },\n\n    resourceCounts() {\n      const out = { ...getResourcesDefaultState(this.$store.getters['i18n/withFallback'], this.stateKey) };\n      const resourceStatuses = this.value.allResourceStatuses;\n\n      Object.entries(resourceStatuses.states)\n        .filter(([_, count]) => count > 0)\n        .forEach(([state, count]) => {\n          const k = state?.toLowerCase();\n\n          if (out[k]) {\n            out[k].count += count;\n          } else {\n            out.unknown.count += count;\n          }\n        });\n\n      return Object.values(out).map((item) => {\n        item.value = item.count;\n\n        return item;\n      });\n    },\n\n  },\n\n};\n</script>\n\n<template>\n  <div class=\"row flexwrap\">\n    <FleetStatus\n      v-if=\"bundleCounts.length\"\n      title=\"Bundles\"\n      :values=\"bundleCounts\"\n      value-key=\"count\"\n      data-testid=\"gitrepo-bundle-summary\"\n    />\n    <FleetStatus\n      title=\"Resources\"\n      :values=\"resourceCounts\"\n      value-key=\"count\"\n      data-testid=\"gitrepo-deployment-summary\"\n    />\n  </div>\n</template>\n<style lang=\"scss\" scoped>\n   .flexwrap .fleet-status {\n    max-width: 50%;\n    margin-right: 15px;\n\n    &:last-child {\n      margin: 0\n    }\n  }\n  .countbox {\n    min-width: 150px;\n    width: 12.5%;\n    margin-bottom: 10px;\n  }\n</style>\n"]}]}