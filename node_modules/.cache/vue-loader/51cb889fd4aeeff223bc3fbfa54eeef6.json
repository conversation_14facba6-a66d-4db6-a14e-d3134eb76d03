{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/index.vue?vue&type=template&id=f9b48e2a&scoped=true&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/index.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdgogICAgY2xhc3M9ImlucHV0LWtub3duLXNzaC1ob3N0cyBsYWJlbGVkLWlucHV0IgogICAgZGF0YS10ZXN0aWQ9ImlucHV0LWtub3duLXNzaC1ob3N0cyIKICA+CiAgICA8bGFiZWw+e3sgdCgnc2VjcmV0LnNzaC5rbm93bkhvc3RzJykgfX08L2xhYmVsPgogICAgPGRpdgogICAgICBjbGFzcz0iaG9zdHMtaW5wdXQiCiAgICAgIGRhdGEtdGVzdGlkPSJpbnB1dC1rbm93bi1zc2gtaG9zdHNfc3VtbWFyeSIKICAgID4KICAgICAge3sgc3VtbWFyeSB9fQogICAgPC9kaXY+CiAgICA8dGVtcGxhdGUgdi1pZj0iIWlzVmlld01vZGUiPgogICAgICA8YnV0dG9uCiAgICAgICAgcmVmPSJidXR0b24iCiAgICAgICAgZGF0YS10ZXN0aWQ9ImlucHV0LWtub3duLXNzaC1ob3N0c19vcGVuLWRpYWxvZyIKICAgICAgICBjbGFzcz0ic2hvdy1kaWFsb2ctYnRuIGJ0biIKICAgICAgICBAY2xpY2s9Im9wZW5EaWFsb2ciCiAgICAgID4KICAgICAgICA8aSBjbGFzcz0iaWNvbiBpY29uLWVkaXQiIC8+CiAgICAgIDwvYnV0dG9uPgoKICAgICAgPEtub3duSG9zdHNFZGl0RGlhbG9nCiAgICAgICAgcmVmPSJlZGl0RGlhbG9nIgogICAgICAgIDp2YWx1ZT0idmFsdWUiCiAgICAgICAgOm1vZGU9Im1vZGUiCiAgICAgICAgQGNsb3NlZD0iZGlhbG9nQ2xvc2VkIgogICAgICAvPgogICAgPC90ZW1wbGF0ZT4KICA8L2Rpdj4K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/index.vue"], "names": [], "mappings": ";EAsDE,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpC;IACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,<PERSON>AC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9C,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C;MACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB;QACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/index.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent } from 'vue';\nimport KnownHostsEditDialog from './KnownHostsEditDialog.vue';\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\n\nexport default defineComponent({\n  name: 'SSHKnownHosts',\n\n  emits: ['update:value'],\n\n  props: {\n    value: {\n      type:     String,\n      required: true\n    },\n\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n  },\n\n  components: { KnownHostsEditDialog },\n\n  computed: {\n    isViewMode() {\n      return this.mode === _VIEW;\n    },\n\n    // The number of entries - exclude empty lines and comments\n    entries() {\n      return this.value.split('\\n').filter((line: string) => !!line.trim().length && !line.startsWith('#')).length;\n    },\n\n    summary() {\n      return this.t('secret.ssh.editKnownHosts.entries', { entries: this.entries });\n    }\n  },\n\n  methods: {\n    openDialog() {\n      (this.$refs.button as HTMLInputElement)?.blur();\n      (this.$refs.editDialog as any).showDialog();\n    },\n\n    dialogClosed(result: any) {\n      if (result.success) {\n        this.$emit('update:value', result.value);\n      }\n    }\n  }\n});\n</script>\n<template>\n  <div\n    class=\"input-known-ssh-hosts labeled-input\"\n    data-testid=\"input-known-ssh-hosts\"\n  >\n    <label>{{ t('secret.ssh.knownHosts') }}</label>\n    <div\n      class=\"hosts-input\"\n      data-testid=\"input-known-ssh-hosts_summary\"\n    >\n      {{ summary }}\n    </div>\n    <template v-if=\"!isViewMode\">\n      <button\n        ref=\"button\"\n        data-testid=\"input-known-ssh-hosts_open-dialog\"\n        class=\"show-dialog-btn btn\"\n        @click=\"openDialog\"\n      >\n        <i class=\"icon icon-edit\" />\n      </button>\n\n      <KnownHostsEditDialog\n        ref=\"editDialog\"\n        :value=\"value\"\n        :mode=\"mode\"\n        @closed=\"dialogClosed\"\n      />\n    </template>\n  </div>\n</template>\n<style lang=\"scss\" scoped>\n  .input-known-ssh-hosts {\n    display: flex;\n    justify-content: space-between;\n\n    .hosts-input {\n      cursor: default;\n      line-height: calc(18px + 1px);\n      padding: 18px 0 0 0;\n    }\n\n    .show-dialog-btn {\n      display: contents;\n      background-color: transparent;\n    }\n  }\n</style>\n"]}]}