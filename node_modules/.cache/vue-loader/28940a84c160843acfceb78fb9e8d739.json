{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Type.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Type.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Type.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAElD,CAAC,CAAC,CAAC,CAAC,<PERSON>AC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEjD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE/D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;EAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;EAElC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACxB,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACrF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACxD,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB;;MAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5E,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAExD,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEzC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACrD,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YACxE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACd;QACF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC;;EAEF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACjB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEhE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB;MACF;IACF;EACF;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Type.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport Favorite from '@shell/components/nav/Favorite';\nimport { TYPE_MODES } from '@shell/store/type-map';\n\nimport TabTitle from '@shell/components/TabTitle';\n\nconst showFavoritesFor = [TYPE_MODES.FAVORITE, TYPE_MODES.USED];\n\nexport default {\n\n  components: { Favorite, TabTitle },\n\n  emits: ['selected'],\n\n  props: {\n    type: {\n      type:     Object,\n      required: true\n    },\n\n    isRoot: {\n      type:    Boolean,\n      default: false,\n    },\n\n    depth: {\n      type:    Number,\n      default: 0,\n    },\n  },\n\n  data() {\n    return { near: false };\n  },\n\n  computed: {\n    showFavorite() {\n      return ( this.type.mode && this.near && showFavoritesFor.includes(this.type.mode) );\n    },\n\n    showCount() {\n      return this.count !== undefined && this.count !== null;\n    },\n\n    namespaceIcon() {\n      return this.type.namespaced;\n    },\n\n    count() {\n      if (this.type.count !== undefined) {\n        return this.type.count;\n      }\n\n      const inStore = this.$store.getters['currentStore'](this.type.name);\n\n      return this.$store.getters[`${ inStore }/count`]({ name: this.type.name });\n    },\n\n    isActive() {\n      const typeFullPath = this.$router.resolve(this.type.route)?.fullPath.toLowerCase();\n      const pageFullPath = this.$route.fullPath?.toLowerCase();\n\n      if ( !this.type.exact) {\n        const typeSplit = typeFullPath.split('/');\n        const pageSplit = pageFullPath.split('/');\n\n        for (let index = 0; index < typeSplit.length; ++index) {\n          if ( index >= pageSplit.length || typeSplit[index] !== pageSplit[index] ) {\n            return false;\n          }\n        }\n\n        return true;\n      }\n\n      return typeFullPath === pageFullPath;\n    }\n\n  },\n\n  methods: {\n    setNear(val) {\n      this.near = val;\n    },\n\n    selectType() {\n      // Prevent issues if custom NavLink is used #5047\n      if (this.type?.route) {\n        const typePath = this.$router.resolve(this.type.route)?.fullPath;\n\n        if (typePath !== this.$route.fullPath) {\n          this.$emit('selected');\n        }\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <router-link\n    v-if=\"type.route\"\n    :key=\"type.name\"\n    v-slot=\"{ href, navigate,isExactActive }\"\n    custom\n    :to=\"type.route\"\n  >\n    <li\n      class=\"child nav-type\"\n      :class=\"{'root': isRoot, [`depth-${depth}`]: true, 'router-link-active': isActive, 'router-link-exact-active': isExactActive}\"\n      @click=\"navigate\"\n      @keypress.enter=\"navigate\"\n    >\n      <TabTitle\n        v-if=\"isExactActive\"\n        :show-child=\"false\"\n      >\n        {{ type.labelKey ? t(type.labelKey) : (type.labelDisplay || type.label) }}\n      </TabTitle>\n      <a\n        role=\"link\"\n        :aria-label=\"type.labelKey ? t(type.labelKey) : (type.labelDisplay || type.label)\"\n        :href=\"href\"\n        class=\"type-link\"\n        :aria-current=\"isActive ? 'page' : undefined\"\n        @click=\"selectType(); navigate($event);\"\n        @mouseenter=\"setNear(true)\"\n        @mouseleave=\"setNear(false)\"\n      >\n        <span\n          v-if=\"type.labelKey\"\n          class=\"label\"\n        ><t :k=\"type.labelKey\" /></span>\n        <span\n          v-else\n          v-clean-html=\"type.labelDisplay || type.label\"\n          class=\"label\"\n          :class=\"{'no-icon': !type.icon}\"\n        />\n        <span\n          v-if=\"showFavorite || namespaceIcon || showCount\"\n          class=\"count\"\n        >\n          <Favorite\n            v-if=\"showFavorite\"\n            :resource=\"type.name\"\n          />\n          <i\n            v-if=\"namespaceIcon\"\n            class=\"icon icon-namespace\"\n            :class=\"{'ns-and-icon': showCount}\"\n            data-testid=\"type-namespaced\"\n          />\n          <span\n            v-if=\"showCount\"\n            data-testid=\"type-count\"\n          >{{ count }}</span>\n        </span>\n      </a>\n    </li>\n  </router-link>\n  <li\n    v-else-if=\"type.link\"\n    class=\"child nav-type nav-link\"\n    data-testid=\"link-type\"\n  >\n    <a\n      role=\"link\"\n      :href=\"type.link\"\n      :target=\"type.target\"\n      rel=\"noopener noreferrer nofollow\"\n      :aria-label=\"type.label\"\n    >\n      <span class=\"label\">{{ type.label }}&nbsp;<i class=\"icon icon-external-link\" /></span>\n    </a>\n  </li>\n  <li v-else>\n    {{ type }}?\n  </li>\n</template>\n\n<style lang=\"scss\" scoped>\n  .ns-and-icon {\n    margin-right: 4px;\n  }\n\n  .type-link:focus-visible span.label {\n    @include focus-outline;\n    outline-offset: 2px;\n  }\n\n  .nav-link a:focus-visible .label {\n    @include focus-outline;\n    outline-offset: 2px;\n  }\n\n  .child {\n    margin: 0 var(--outline) 0 0;\n\n    .label {\n      align-items: center;\n      grid-area: label;\n      overflow: hidden;\n      text-overflow: ellipsis;\n\n      &:not(.nav-type) &.no-icon {\n        padding-left: 3px;\n      }\n\n      :deep() .highlight {\n        background: var(--diff-ins-bg);\n        color: var(--body-text);\n        padding: 2px;\n      }\n\n      :deep() .icon {\n        position: relative;\n        color: var(--muted);\n      }\n    }\n\n    A {\n      display: grid;\n      grid-template-areas: \"label count\";\n      grid-template-columns: auto auto;\n      grid-column-gap: 5px;\n      font-size: 14px;\n      line-height: 24px;\n      padding: 7.5px 7px 7.5px 10px;\n      margin: 0 0 0 -3px;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n      color: var(--body-text);\n      height: 33px;\n\n      &:hover {\n        background: var(--nav-hover);\n        text-decoration: none;\n\n        :deep() .icon {\n          color: var(--body-text);\n        }\n      }\n    }\n\n    .favorite {\n      grid-area: favorite;\n      font-size: 12px;\n      position: relative;\n      vertical-align: middle;\n      margin-right: 4px;\n    }\n\n    .count {\n      font-size: 12px;\n      justify-items: center;\n      padding-right: 4px;\n      display: flex;\n      align-items: center;\n    }\n\n    &.nav-type.nav-link {\n      a .label {\n        display: flex;\n      }\n    }\n\n    &.nav-type:not(.depth-0) {\n      A {\n        padding-left: 16px;\n      }\n\n      :deep() .label I {\n        padding-right: 2px;\n      }\n    }\n\n    &.nav-type:is(.depth-1) {\n      A {\n        font-size: 13px;\n        padding-left: 23px;\n      }\n    }\n\n    &.nav-type:not(.depth-0):not(.depth-1) {\n      A {\n        padding-left: 14px;\n      }\n    }\n  }\n\n</style>\n"]}]}