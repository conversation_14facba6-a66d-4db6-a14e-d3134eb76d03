{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/InstallDialog.vue?vue&type=style&index=0&id=6381e649&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/InstallDialog.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLnBsdWdpbi1pbnN0YWxsLWRpYWxvZyB7CiAgICBwYWRkaW5nOiAxMHB4OwoKICAgIGg0IHsKICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICB9CgogICAgLmRpYWxvZy1wYW5lbCB7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICAgIG1pbi1oZWlnaHQ6IDEwMHB4OwoKICAgICAgcCB7CiAgICAgICAgbWFyZ2luLWJvdHRvbTogNXB4OwogICAgICB9CgogICAgICAuZGlhbG9nLWluZm8gewogICAgICAgIGZsZXg6IDE7CiAgICAgIH0KCiAgICAgIC50b2dnbGUtYWR2YW5jZWQgewogICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICAgICAgbWFyZ2luOiAxMHB4IDA7CgogICAgICAgICY6aG92ZXIgewogICAgICAgICAgdGV4dC1kZWNvcmF0aW9uOiBub25lOwogICAgICAgICAgY29sb3I6IHZhcigtLWxpbmspOwogICAgICAgIH0KICAgICAgfQoKICAgICAgLnZlcnNpb24tc2VsZWN0b3IgewogICAgICAgIG1hcmdpbjogMCAxMHB4IDEwcHggMTBweDsKICAgICAgICB3aWR0aDogYXV0bzsKICAgICAgfQogICAgfQoKICAgIC5kaWFsb2ctYnV0dG9ucyB7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7CiAgICAgIG1hcmdpbi10b3A6IDEwcHg7CgogICAgICA+ICo6bm90KDpsYXN0LWNoaWxkKSB7CiAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMHB4OwogICAgICB9CiAgICB9CiAgfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/InstallDialog.vue"], "names": [], "mappings": ";EA6TE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEb,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEjB,EAAE;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACpB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACT;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;QAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEhB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpB;IACF;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/InstallDialog.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport AsyncButton from '@shell/components/AsyncButton';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport AppModal from '@shell/components/AppModal.vue';\nimport { CATALOG, MANAGEMENT } from '@shell/config/types';\nimport { CATALOG as CATALOG_ANNOTATIONS } from '@shell/config/labels-annotations';\nimport { UI_PLUGIN_NAMESPACE } from '@shell/config/uiplugins';\nimport Banner from '@components/Banner/Banner.vue';\nimport { SETTING } from '@shell/config/settings';\n\n// Note: This dialog handles installation and update of a plugin\n\nexport default {\n  emits: ['closed', 'update'],\n\n  components: {\n    AsyncButton,\n    Banner,\n    LabeledSelect,\n    AppModal,\n  },\n\n  async fetch() {\n    this.defaultRegistrySetting = await this.$store.dispatch('management/find', {\n      type: MANAGEMENT.SETTING,\n      id:   SETTING.SYSTEM_DEFAULT_REGISTRY,\n    });\n  },\n\n  data() {\n    return {\n      currentVersion:         '',\n      defaultRegistrySetting: null,\n      plugin:                 undefined,\n      busy:                   false,\n      version:                '',\n      update:                 false,\n      mode:                   '',\n      showModal:              false,\n      chartVersionInfo:       null\n    };\n  },\n\n  computed: {\n    showVersionSelector() {\n      return this.versionOptions?.length > 1;\n    },\n\n    versionOptions() {\n      if (!this.plugin) {\n        return [];\n      }\n\n      // Don't allow update/rollback to current version\n      const versions = this.plugin?.installableVersions?.filter((v) => {\n        if (this.currentVersion) {\n          return v.version !== this.currentVersion;\n        }\n\n        return true;\n      });\n\n      return versions.map((version) => {\n        return {\n          label: version.version,\n          value: version.version,\n        };\n      });\n    },\n\n    buttonMode() {\n      return this.update ? 'update' : 'install';\n    },\n\n    chartVersionLoadsWithoutAuth() {\n      return this.chartVersionInfo?.values?.plugin?.noAuth;\n    },\n\n    returnFocusSelector() {\n      return `[data-testid=\"extension-card-${ this.mode }-btn-${ this.plugin?.name }\"]`;\n    }\n  },\n\n  methods: {\n    showDialog(plugin, mode) {\n      this.plugin = plugin;\n      this.mode = mode;\n\n      // Default to latest version on install (this is default on the plugin)\n      this.version = plugin.displayVersion;\n\n      if (mode === 'update') {\n        this.currentVersion = plugin.displayVersion;\n\n        // Update to latest version, so take the first version\n        if (plugin.installableVersions?.length > 0) {\n          this.version = plugin.installableVersions?.[0]?.version;\n        }\n      } else if (mode === 'rollback') {\n        // Find the newest version once we remove the current version\n        const versionNames = plugin.installableVersions.filter((v) => v.version !== plugin.displayVersion);\n\n        this.currentVersion = plugin.displayVersion;\n\n        if (versionNames.length > 0) {\n          this.version = versionNames[0].version;\n        }\n      }\n\n      // Make sure we have the version available\n      const versionChart = plugin.installableVersions?.find((v) => v.version === this.version);\n\n      if (!versionChart) {\n        this.version = plugin.installableVersions?.[0]?.version;\n      }\n\n      this.busy = false;\n      this.update = mode !== 'install';\n      this.showModal = true;\n    },\n\n    async loadVersionInfo() {\n      try {\n        this.busy = true;\n        const plugin = this.plugin;\n\n        // Find the version that the user wants to install\n        const version = plugin.versions?.find((v) => v.version === this.version);\n\n        if (!version) {\n          this.busy = false;\n\n          return;\n        }\n\n        this.chartVersionInfo = await this.$store.dispatch('catalog/getVersionInfo', {\n          repoType:    version.repoType,\n          repoName:    version.repoName,\n          chartName:   plugin.chart.chartName,\n          versionName: this.version,\n        });\n      } catch (e) {\n      } finally {\n        this.busy = false;\n      }\n    },\n\n    closeDialog(result) {\n      this.showModal = false;\n      this.$emit('closed', result);\n    },\n\n    async install() {\n      this.busy = true;\n\n      const plugin = this.plugin;\n\n      this.$emit('update', plugin.name, 'install');\n\n      // Find the version that the user wants to install\n      const version = plugin.versions?.find((v) => v.version === this.version);\n\n      if (!version) {\n        this.busy = false;\n\n        return;\n      }\n\n      const image = this.chartVersionInfo?.values?.image?.repository || '';\n      // is the image used by the chart in the rancher org?\n      const isRancherImage = image.startsWith('rancher/');\n\n      // See if there is already a plugin with this name\n      let exists = false;\n\n      try {\n        const app = await this.$store.dispatch('management/find', {\n          type: CATALOG.APP,\n          id:   `${ UI_PLUGIN_NAMESPACE }/${ plugin.chart.chartName }`,\n          opt:  { force: true },\n        });\n\n        exists = !!app;\n      } catch (e) {}\n\n      const repoType = version.repoType;\n      const repoName = version.repoName;\n      const repo = this.$store.getters['catalog/repo']({ repoType, repoName });\n\n      const chart = {\n        chartName:   plugin.chart.chartName,\n        version:     this.version,\n        releaseName: plugin.chart.chartName,\n        annotations: {\n          [CATALOG_ANNOTATIONS.SOURCE_REPO_TYPE]: plugin.repoType,\n          [CATALOG_ANNOTATIONS.SOURCE_REPO_NAME]: plugin.repoName\n        },\n        values: {}\n      };\n\n      // Pass in the system default registry property if set - only if the image is in the rancher org\n      const defaultRegistry = this.defaultRegistrySetting?.value || '';\n\n      if (isRancherImage && defaultRegistry) {\n        chart.values.global = chart.values.global || {};\n        chart.values.global.cattle = chart.values.global.cattle || {};\n        chart.values.global.cattle.systemDefaultRegistry = defaultRegistry;\n      }\n\n      const input = {\n        charts:    [chart],\n        // timeout:   this.cmdOptions.timeout > 0 ? `${ this.cmdOptions.timeout }s` : null,\n        // wait:      this.cmdOptions.wait === true,\n        namespace: UI_PLUGIN_NAMESPACE,\n      };\n\n      // Helm action\n      const action = (exists || this.update) ? 'upgrade' : 'install';\n\n      try {\n        const res = await repo.doAction(action, input);\n        const operationId = `${ res.operationNamespace }/${ res.operationName }`;\n\n        this.closeDialog(plugin);\n\n        await repo.waitForOperation(operationId);\n\n        await this.$store.dispatch(`management/find`, {\n          type: CATALOG.OPERATION,\n          id:   operationId\n        });\n      } catch (e) {\n        this.$store.dispatch('growl/error', {\n          title:   this.t('plugins.error.generic'),\n          message: e.message ? e.message : e,\n          timeout: 10000\n        }, { root: true });\n\n        this.closeDialog(plugin);\n      }\n    }\n  },\n  watch: {\n    version() {\n      this.chartVersionInfo = null;\n      this.loadVersionInfo();\n    }\n  }\n};\n</script>\n\n<template>\n  <app-modal\n    v-if=\"showModal\"\n    name=\"installPluginDialog\"\n    height=\"auto\"\n    :scrollable=\"true\"\n    :trigger-focus-trap=\"true\"\n    :return-focus-selector=\"returnFocusSelector\"\n    :return-focus-first-iterable-node-selector=\"'#extensions-main-page'\"\n    @close=\"closeDialog(false)\"\n  >\n    <div\n      v-if=\"plugin\"\n      class=\"plugin-install-dialog\"\n    >\n      <h4 class=\"mt-10\">\n        {{ t(`plugins.${ mode }.title`, { name: plugin.label }) }}\n      </h4>\n      <div class=\"custom mt-10\">\n        <div class=\"dialog-panel\">\n          <p>\n            {{ t(`plugins.${ mode }.prompt`) }}\n          </p>\n          <Banner\n            v-if=\"chartVersionLoadsWithoutAuth\"\n            color=\"warning\"\n            :label=\"t('plugins.warnNoAuth')\"\n          />\n          <Banner\n            v-if=\"!plugin.certified\"\n            color=\"warning\"\n            :label=\"t('plugins.install.warnNotCertified')\"\n          />\n          <LabeledSelect\n            v-if=\"showVersionSelector\"\n            v-model:value=\"version\"\n            label-key=\"plugins.install.version\"\n            :options=\"versionOptions\"\n            class=\"version-selector mt-10\"\n            data-testid=\"install-ext-modal-select-version\"\n          />\n          <div v-else>\n            {{ t('plugins.install.version') }} {{ version }}\n          </div>\n        </div>\n        <div class=\"dialog-buttons\">\n          <button\n            :disabled=\"busy\"\n            class=\"btn role-secondary\"\n            data-testid=\"install-ext-modal-cancel-btn\"\n            @click=\"closeDialog(false)\"\n          >\n            {{ t('generic.cancel') }}\n          </button>\n          <AsyncButton\n            :mode=\"buttonMode\"\n            data-testid=\"install-ext-modal-install-btn\"\n            @click=\"install\"\n          />\n        </div>\n      </div>\n    </div>\n  </app-modal>\n</template>\n\n<style lang=\"scss\" scoped>\n  .plugin-install-dialog {\n    padding: 10px;\n\n    h4 {\n      font-weight: bold;\n    }\n\n    .dialog-panel {\n      display: flex;\n      flex-direction: column;\n      min-height: 100px;\n\n      p {\n        margin-bottom: 5px;\n      }\n\n      .dialog-info {\n        flex: 1;\n      }\n\n      .toggle-advanced {\n        display: flex;\n        align-items: center;\n        cursor: pointer;\n        margin: 10px 0;\n\n        &:hover {\n          text-decoration: none;\n          color: var(--link);\n        }\n      }\n\n      .version-selector {\n        margin: 0 10px 10px 10px;\n        width: auto;\n      }\n    }\n\n    .dialog-buttons {\n      display: flex;\n      justify-content: flex-end;\n      margin-top: 10px;\n\n      > *:not(:last-child) {\n        margin-right: 10px;\n      }\n    }\n  }\n</style>\n"]}]}