{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Conditions.vue?vue&type=template&id=4842edf0", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Conditions.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPFNvcnRhYmxlVGFibGUKICAgIDpoZWFkZXJzPSJoZWFkZXJzIgogICAgOnJvd3M9InJvd3MiCiAgICBrZXktZmllbGQ9ImNvbmRpdGlvbiIKICAgIGRlZmF1bHQtc29ydC1ieT0iY29uZGl0aW9uIgogICAgOnRhYmxlLWFjdGlvbnM9ImZhbHNlIgogICAgOnJvdy1hY3Rpb25zPSJmYWxzZSIKICAgIDpzZWFyY2g9ImZhbHNlIgogID4KICAgIDx0ZW1wbGF0ZSAjY2VsbDpjb25kaXRpb249Intyb3d9Ij4KICAgICAgPHNwYW4gOmNsYXNzPSJ7J3RleHQtZXJyb3InOiByb3cuZXJyb3J9Ij57eyByb3cuY29uZGl0aW9uIH19PC9zcGFuPgogICAgPC90ZW1wbGF0ZT4KCiAgICA8dGVtcGxhdGUgI2NlbGw6c3RhdHVzPSJ7cm93fSI+CiAgICAgIDxzcGFuIDpjbGFzcz0ieyd0ZXh0LWVycm9yJzogcm93LmVycm9yfSI+e3sgcm93LnN0YXR1cyB9fTwvc3Bhbj4KICAgIDwvdGVtcGxhdGU+CiAgPC9Tb3J0YWJsZVRhYmxlPgo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Conditions.vue"], "names": [], "mappings": ";EA2EE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;IACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Conditions.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport SortableTable from '@shell/components/SortableTable';\n\nexport default {\n  components: { SortableTable },\n  props:      {\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  computed: {\n    headers() {\n      return [\n        {\n          name:        'condition',\n          labelKey:    'tableHeaders.condition',\n          value:       'condition',\n          width:       150,\n          sort:        'condition',\n          dashIfEmpty: true,\n        },\n        {\n          name:        'status',\n          labelKey:    'tableHeaders.status',\n          value:       'status',\n          width:       75,\n          sort:        'status',\n          dashIfEmpty: true,\n        },\n        {\n          name:          'time',\n          labelKey:      'tableHeaders.updated',\n          value:         'time',\n          sort:          'time',\n          formatter:     'LiveDate',\n          formatterOpts: { addSuffix: true },\n          width:         125,\n          dashIfEmpty:   true,\n        },\n        {\n          name:        'message',\n          labelKey:    'tableHeaders.message',\n          value:       'message',\n          sort:        ['message'],\n          dashIfEmpty: true,\n        },\n      ];\n    },\n\n    rows() {\n      return (this.value.status?.conditions || []).map((cond) => {\n        let message = cond.message || '';\n\n        if ( cond.reason ) {\n          message = `[${ cond.reason }] ${ message }`.trim();\n        }\n\n        return {\n          condition: cond.type || 'Unknown',\n          status:    cond.status || 'Unknown',\n          error:     cond.error,\n          time:      cond.lastProbeTime || cond.lastUpdateTime || cond.lastTransitionTime,\n          message,\n        };\n      });\n    },\n  }\n};\n</script>\n\n<template>\n  <SortableTable\n    :headers=\"headers\"\n    :rows=\"rows\"\n    key-field=\"condition\"\n    default-sort-by=\"condition\"\n    :table-actions=\"false\"\n    :row-actions=\"false\"\n    :search=\"false\"\n  >\n    <template #cell:condition=\"{row}\">\n      <span :class=\"{'text-error': row.error}\">{{ row.condition }}</span>\n    </template>\n\n    <template #cell:status=\"{row}\">\n      <span :class=\"{'text-error': row.error}\">{{ row.status }}</span>\n    </template>\n  </SortableTable>\n</template>\n"]}]}