{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetBundles.vue?vue&type=template&id=775882e7", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetBundles.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdj4KICAgIDxMb2FkaW5nIHYtaWY9IiRmZXRjaFN0YXRlLnBlbmRpbmciIC8+CiAgICA8ZGl2IHYtZWxzZT4KICAgICAgPEJhbm5lcgogICAgICAgIHYtaWY9ImhpZGRlbiIKICAgICAgICBjb2xvcj0iaW5mbyIKICAgICAgICA6bGFiZWw9InQoJ2ZsZWV0LmJ1bmRsZXMuaGFydmVzdGVyJywge2NvdW50OiBoaWRkZW59ICkiCiAgICAgIC8+CiAgICAgIDxSZXNvdXJjZVRhYmxlCiAgICAgICAgOnNjaGVtYT0ic2NoZW1hIgogICAgICAgIDpoZWFkZXJzPSJoZWFkZXJzIgogICAgICAgIDpyb3dzPSJidW5kbGVzIgogICAgICA+CiAgICAgICAgPHRlbXBsYXRlICNjZWxsOmRlcGxveW1lbnRzUmVhZHk9Intyb3d9Ij4KICAgICAgICAgIDxzcGFuCiAgICAgICAgICAgIHYtaWY9ImRpc3BsYXlXYXJuaW5nKHJvdykiCiAgICAgICAgICAgIGNsYXNzPSJ0ZXh0LXdhcm5pbmciCiAgICAgICAgICA+CiAgICAgICAgICAgIHt7IHJvdy5zdGF0dXMuc3VtbWFyeS5yZWFkeSB8fCAwIH19L3t7IHJvdy5zdGF0dXMuc3VtbWFyeS5kZXNpcmVkUmVhZHkgfX08L3NwYW4+CiAgICAgICAgICA8c3BhbiB2LWVsc2UtaWY9InJvdy5zdGF0dXMgJiYgcm93LnN0YXR1cy5zdW1tYXJ5Ij57eyByb3cuc3RhdHVzLnN1bW1hcnkuZGVzaXJlZFJlYWR5IH19PC9zcGFuPgogICAgICAgICAgPHNwYW4gdi1lbHNlPi08L3NwYW4+CiAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgPC9SZXNvdXJjZVRhYmxlPgogICAgPC9kaXY+CiAgPC9kaXY+Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetBundles.vue"], "names": [], "mappings": ";EA8GE,CAAC,CAAC,CAAC,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACrC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACxD,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB;QACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB;YACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9F,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetBundles.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { FLEET } from '@shell/config/types';\nimport { Banner } from '@components/Banner';\nimport Loading from '@shell/components/Loading';\nimport ResourceTable from '@shell/components/ResourceTable';\nimport {\n  CREATION_DATE,\n  STATE,\n  NAME,\n  FLEET_BUNDLE_LAST_UPDATED,\n} from '@shell/config/table-headers';\nimport { isHarvesterCluster } from '@shell/utils/cluster';\n\nexport default {\n  name: 'FleetBundles',\n\n  components: {\n    Banner, Loading, ResourceTable\n  },\n\n  props: {\n    value: {\n      type:     Object,\n      required: true,\n    },\n  },\n\n  async fetch() {\n    if (this.$store.getters['management/schemaFor']( FLEET.CLUSTER )) {\n      this.allFleet = await this.$store.getters['management/all'](FLEET.CLUSTER);\n    }\n  },\n\n  data() {\n    return { allFleet: [] };\n  },\n\n  computed: {\n\n    allBundlesInRepo() {\n      // gitrepo model has getter for its bundles.\n      return this.value.bundles || [];\n    },\n\n    schema() {\n      return this.$store.getters['management/schemaFor']( FLEET.BUNDLE );\n    },\n\n    repoName() {\n      return this.value.metadata.name;\n    },\n\n    harvesterClusters() {\n      const harvester = {};\n\n      this.allFleet.forEach((c) => {\n        if (isHarvesterCluster(c)) {\n          harvester[c.metadata.name] = c;\n        }\n      });\n\n      return harvester;\n    },\n\n    bundles() {\n      const harvester = this.harvesterClusters;\n\n      return this.allBundlesInRepo.filter((bundle) => {\n        const targets = bundle.spec?.targets || [];\n\n        // Filter out any bundle that has one target whose cluster is a harvester cluster\n        if (targets.length === 1) {\n          return !harvester[targets[0].clusterName];\n        }\n\n        return true;\n      });\n    },\n\n    hidden() {\n      return this.allBundlesInRepo.length - this.bundles.length;\n    },\n\n    headers() {\n      const out = [\n        STATE,\n        NAME,\n        {\n          name:     'deploymentsReady',\n          labelKey: 'tableHeaders.bundleDeploymentsReady',\n          value:    'status.display.readyClusters',\n          sort:     'status.display.readyClusters',\n          search:   ['status.summary.ready', 'status.summary.desiredReady'],\n        },\n        FLEET_BUNDLE_LAST_UPDATED,\n        CREATION_DATE,\n      ];\n\n      return out;\n    },\n  },\n  methods: {\n    displayWarning(row) {\n      return !!row.status?.summary && (row.status.summary.desiredReady !== row.status.summary.ready);\n    }\n  }\n};\n</script>\n\n<template>\n  <div>\n    <Loading v-if=\"$fetchState.pending\" />\n    <div v-else>\n      <Banner\n        v-if=\"hidden\"\n        color=\"info\"\n        :label=\"t('fleet.bundles.harvester', {count: hidden} )\"\n      />\n      <ResourceTable\n        :schema=\"schema\"\n        :headers=\"headers\"\n        :rows=\"bundles\"\n      >\n        <template #cell:deploymentsReady=\"{row}\">\n          <span\n            v-if=\"displayWarning(row)\"\n            class=\"text-warning\"\n          >\n            {{ row.status.summary.ready || 0 }}/{{ row.status.summary.desiredReady }}</span>\n          <span v-else-if=\"row.status && row.status.summary\">{{ row.status.summary.desiredReady }}</span>\n          <span v-else>-</span>\n        </template>\n      </ResourceTable>\n    </div>\n  </div>\n</template>\n"]}]}