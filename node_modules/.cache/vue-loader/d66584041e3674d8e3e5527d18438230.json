{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Inactivity.vue?vue&type=style&index=0&id=ef080212&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Inactivity.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5jYXJkLWFjdGlvbnMgewogICAgZGlzcGxheTogZmxleDsKICAgIHdpZHRoOiAxMDAlOwogICAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsKfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Inactivity.vue"], "names": [], "mappings": ";AAsOA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Inactivity.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport ModalWithCard from '@shell/components/ModalWithCard';\nimport { Banner } from '@components/Banner';\nimport PercentageBar from '@shell/components/PercentageBar.vue';\nimport throttle from 'lodash/throttle';\nimport { MANAGEMENT } from '@shell/config/types';\nimport { DEFAULT_PERF_SETTING, SETTING } from '@shell/config/settings';\n\nlet globalId;\n\nexport default {\n  name:       'Inactivity',\n  components: {\n    ModalWithCard, Banner, PercentageBar\n  },\n  data() {\n    return {\n      enabled:             null,\n      isOpen:              false,\n      isInactive:          false,\n      showModalAfter:      null,\n      inactivityTimeoutId: null,\n      courtesyTimer:       null,\n      courtesyTimerId:     null,\n      courtesyCountdown:   null,\n      trackInactivity:     throttle(this._trackInactivity, 1000),\n      id:                  null,\n    };\n  },\n  async mounted() {\n    // Info: normally, this is done in the fetch hook but for some reasons while awaiting for things that will take a while, it won't be ready by the time mounted() is called, pending for investigation.\n    let settings;\n\n    try {\n      const settingsString = await this.$store.dispatch('management/find', { type: MANAGEMENT.SETTING, id: SETTING.UI_PERFORMANCE });\n\n      settings = settingsString?.value ? JSON.parse(settingsString.value) : DEFAULT_PERF_SETTING;\n    } catch { }\n\n    if (!settings || !settings?.inactivity || !settings?.inactivity.enabled) {\n      return;\n    }\n\n    this.enabled = settings?.inactivity?.enabled || false;\n\n    // Total amount of time before the user's session is lost\n    const thresholdToSeconds = settings?.inactivity?.threshold * 60;\n\n    // Amount of time the user sees the inactivity warning\n    this.courtesyTimer = Math.floor(thresholdToSeconds * 0.1);\n    this.courtesyTimer = Math.min(this.courtesyTimer, 60 * 5); // Never show the modal more than 5 minutes\n    // Amount of time before the user sees the inactivity warning\n    // Note - time before warning is shown + time warning is shown = settings threshold (total amount of time)\n    this.showModalAfter = thresholdToSeconds - this.courtesyTimer;\n\n    console.debug(`Inactivity modal will show after ${ this.showModalAfter / 60 }(m) and be shown for ${ this.courtesyTimer / 60 }(m)`); // eslint-disable-line no-console\n\n    this.courtesyCountdown = this.courtesyTimer;\n\n    if (settings?.inactivity.enabled) {\n      this.trackInactivity();\n      this.addIdleListeners();\n    }\n  },\n  beforeUnmount() {\n    this.removeEventListener();\n    this.clearAllTimeouts();\n  },\n  methods: {\n    _trackInactivity() {\n      if (this.isInactive || this.isOpen || !this.showModalAfter) {\n        return;\n      }\n\n      this.clearAllTimeouts();\n      const endTime = Date.now() + this.showModalAfter * 1000;\n\n      this.id = endTime;\n      globalId = endTime;\n\n      const checkInactivityTimer = () => {\n        const now = Date.now();\n\n        if (this.id !== globalId) {\n          return;\n        }\n\n        if (now >= endTime) {\n          this.isOpen = true;\n          this.startCountdown();\n        } else {\n          this.inactivityTimeoutId = setTimeout(checkInactivityTimer, 1000);\n        }\n      };\n\n      checkInactivityTimer();\n    },\n    startCountdown() {\n      const endTime = Date.now() + (this.courtesyCountdown * 1000);\n\n      const checkCountdown = () => {\n        const now = Date.now();\n\n        if (now >= endTime) {\n          this.isInactive = true;\n          this.unsubscribe();\n          this.clearAllTimeouts();\n        } else {\n          this.courtesyCountdown = Math.floor((endTime - now) / 1000);\n          this.courtesyTimerId = setTimeout(checkCountdown, 1000);\n        }\n      };\n\n      checkCountdown();\n    },\n    addIdleListeners() {\n      document.addEventListener('mousemove', this.trackInactivity);\n      document.addEventListener('mousedown', this.trackInactivity);\n      document.addEventListener('keypress', this.trackInactivity);\n      document.addEventListener('touchmove', this.trackInactivity);\n      document.addEventListener('visibilitychange', this.trackInactivity);\n    },\n    removeEventListener() {\n      document.removeEventListener('mousemove', this.trackInactivity);\n      document.removeEventListener('mousedown', this.trackInactivity);\n      document.removeEventListener('keypress', this.trackInactivity);\n      document.removeEventListener('touchmove', this.trackInactivity);\n      document.removeEventListener('visibilitychange', this.trackInactivity);\n    },\n\n    resume() {\n      this.isInactive = false;\n      this.isOpen = false;\n      this.courtesyCountdown = this.courtesyTimer;\n      this.clearAllTimeouts();\n    },\n\n    refresh() {\n      window.location.reload();\n    },\n\n    unsubscribe() {\n      console.debug('Unsubscribing from all websocket events'); // eslint-disable-line no-console\n      this.$store.dispatch('unsubscribe');\n    },\n    clearAllTimeouts() {\n      clearTimeout(this.inactivityTimeoutId);\n      clearTimeout(this.courtesyTimerId);\n    }\n\n  },\n  computed: {\n    isInactiveTexts() {\n      return this.isInactive ? {\n        title:   this.t('inactivity.titleExpired'),\n        banner:  this.t('inactivity.bannerExpired'),\n        content: this.t('inactivity.contentExpired'),\n      } : {\n        title:   this.t('inactivity.title'),\n        banner:  this.t('inactivity.banner'),\n        content: this.t('inactivity.content'),\n      };\n    },\n    timerPercentageLeft() {\n      return Math.floor((this.courtesyCountdown / this.courtesyTimer ) * 100);\n    },\n    colorStops() {\n      return {\n        0: '--info', 30: '--info', 70: '--info'\n      };\n    },\n  }\n};\n</script>\n\n<template>\n  <ModalWithCard\n    v-if=\"isOpen\"\n    ref=\"inactivityModal\"\n    name=\"inactivityModal\"\n    save-text=\"Continue\"\n    @finish=\"resume\"\n  >\n    <template #title>\n      {{ isInactiveTexts.title }}\n    </template>\n    <span>{{ courtesyCountdown }}</span>\n\n    <template #content>\n      <Banner color=\"info\">\n        {{ isInactiveTexts.banner }}\n      </Banner>\n\n      <p>\n        {{ isInactiveTexts.content }}\n      </p>\n\n      <PercentageBar\n        v-if=\"!isInactive\"\n        class=\"mt-20\"\n        :modelValue=\"timerPercentageLeft\"\n        :color-stops=\"colorStops\"\n      />\n    </template>\n\n    <template\n      #footer\n    >\n      <div class=\"card-actions\">\n        <button\n          v-if=\"!isInactive\"\n          class=\"btn role-tertiary bg-primary\"\n          @click.prevent=\"resume\"\n        >\n          <t k=\"inactivity.cta\" />\n        </button>\n\n        <button\n          v-if=\"isInactive\"\n          class=\"btn role-tertiary bg-primary\"\n          @click.prevent=\"refresh\"\n        >\n          <t k=\"inactivity.ctaExpired\" />\n        </button>\n      </div>\n    </template>\n  </ModalWithCard>\n</template>\n\n<style lang=\"scss\" scoped>\n.card-actions {\n    display: flex;\n    width: 100%;\n    justify-content: flex-end;\n}\n</style>\n"]}]}