{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/AsyncButton.vue?vue&type=style&index=0&id=e67562ca&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/AsyncButton.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci8vIHJlZnJlc2ggbW9kZSBoYXMgaWNvbiArIHRleHQuIFdlIG5lZWQgdG8gZml4IHRoZSBwb3NpdGlvbmluZyBvZiB0aGUgaWNvbiBhbmQgc2l6aW5nCi5tYW51YWwtcmVmcmVzaCBpIHsKICBtYXJnaW46IDAgMCAwIDhweCAhaW1wb3J0YW50OwogIGZvbnQtc2l6ZTogMXJlbSAhaW1wb3J0YW50Owp9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/AsyncButton.vue"], "names": [], "mappings": ";AAqTA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACrF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5B", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/AsyncButton.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent, PropType, inject } from 'vue';\nimport typeHelper from '@shell/utils/type-helpers';\n\nexport const ASYNC_BUTTON_STATES = {\n  ACTION:  'action',\n  WAITING: 'waiting',\n  SUCCESS: 'success',\n  ERROR:   'error',\n};\n\nconst TEXT = 'text';\nconst TOOLTIP = 'tooltip';\n\nexport type AsyncButtonCallback = (success: boolean) => void;\n\ninterface NonReactiveProps {\n  timer: NodeJS.Timeout | undefined;\n}\n\nconst provideProps: NonReactiveProps = { timer: undefined };\n\n// i18n-uses asyncButton.*\nexport default defineComponent({\n  props: {\n    /**\n     * Mode maps to keys in asyncButton.* translations\n     */\n    mode: {\n      type:    String,\n      default: 'edit',\n    },\n    delay: {\n      type:    Number,\n      default: 5000,\n    },\n\n    name: {\n      type:    String,\n      default: null,\n    },\n    disabled: {\n      type:    Boolean,\n      default: false,\n    },\n    type: {\n      type:    String as PropType<'button' | 'submit' | 'reset' | undefined>,\n      default: 'button'\n    },\n    tabIndex: {\n      type:    Number,\n      default: null,\n    },\n\n    actionColor: {\n      type:    String,\n      default: 'role-primary',\n    },\n    waitingColor: {\n      type:    String,\n      default: 'bg-primary',\n    },\n    successColor: {\n      type:    String,\n      default: 'bg-success',\n    },\n    errorColor: {\n      type:    String,\n      default: 'bg-error',\n    },\n\n    actionLabel: {\n      type:    String,\n      default: null,\n    },\n    waitingLabel: {\n      type:    String,\n      default: null,\n    },\n    successLabel: {\n      type:    String,\n      default: null,\n    },\n    errorLabel: {\n      type:    String,\n      default: null,\n    },\n\n    icon: {\n      type:    String,\n      default: null,\n    },\n    labelAs: {\n      type:    String,\n      default: TEXT,\n    },\n    size: {\n      type:    String,\n      default: '',\n    },\n\n    currentPhase: {\n      type:    String,\n      default: ASYNC_BUTTON_STATES.ACTION,\n    },\n\n    /**\n     * Inherited global identifier prefix for tests\n     * Define a term based on the parent component to avoid conflicts on multiple components\n     */\n    componentTestid: {\n      type:    String,\n      default: 'action-button'\n    },\n\n    manual: {\n      type:    Boolean,\n      default: false,\n    },\n\n  },\n\n  setup() {\n    const timer = inject('timer', provideProps.timer);\n\n    return { timer };\n  },\n\n  emits: ['click'],\n\n  data() {\n    return { phase: this.currentPhase };\n  },\n\n  watch: {\n    currentPhase(neu) {\n      this.phase = neu;\n    }\n  },\n\n  computed: {\n    classes(): {btn: boolean, [color: string]: boolean} {\n      const key = `${ this.phase }Color`;\n      const color = typeHelper.memberOfComponent(this, key);\n\n      const out = {\n        btn:     true,\n        [color]: true,\n      };\n\n      if (this.size) {\n        out[`btn-${ this.size }`] = true;\n      }\n\n      return out;\n    },\n\n    displayIcon(): string {\n      const exists = this.$store.getters['i18n/exists'];\n      const t = this.$store.getters['i18n/t'];\n      const key = `asyncButton.${ this.mode }.${ this.phase }Icon`;\n      const defaultKey = `asyncButton.default.${ this.phase }Icon`;\n\n      let out = '';\n\n      if ( this.icon ) {\n        out = this.icon;\n      } else if ( exists(key) ) {\n        out = `icon-${ t(key) }`;\n      } else if ( exists(defaultKey) ) {\n        out = `icon-${ t(defaultKey) }`;\n      }\n\n      if ( this.isSpinning ) {\n        if ( !out ) {\n          out = 'icon-spinner';\n        }\n\n        out += ' icon-spin';\n      }\n\n      return out;\n    },\n\n    displayLabel(): string {\n      const override = typeHelper.memberOfComponent(this, `${ this.phase }Label`);\n      const exists = this.$store.getters['i18n/exists'];\n      const t = this.$store.getters['i18n/t'];\n      const key = `asyncButton.${ this.mode }.${ this.phase }`;\n      const defaultKey = `asyncButton.default.${ this.phase }`;\n\n      if ( override ) {\n        return override;\n      } else if ( exists(key) ) {\n        return t(key);\n      } else if ( exists(defaultKey) ) {\n        return t(defaultKey);\n      } else {\n        return '';\n      }\n    },\n\n    isSpinning(): boolean {\n      return this.phase === ASYNC_BUTTON_STATES.WAITING;\n    },\n\n    isDisabled(): boolean {\n      return this.disabled || this.phase === ASYNC_BUTTON_STATES.WAITING;\n    },\n\n    isManualRefresh() {\n      return this.mode === 'manual-refresh';\n    },\n\n    tooltip(): { content: string, hideOnTargetClick: boolean} | null {\n      if ( this.labelAs === TOOLTIP ) {\n        return {\n          content:           this.displayLabel,\n          hideOnTargetClick: false\n        };\n      }\n\n      return null;\n    }\n  },\n\n  beforeUnmount() {\n    if (this.timer) {\n      clearTimeout(this.timer);\n    }\n  },\n\n  methods: {\n    clicked() {\n      if ( this.isDisabled ) {\n        return;\n      }\n\n      if (this.timer) {\n        clearTimeout(this.timer);\n      }\n\n      // If manual property is set, don't automatically change the button on click\n      if (!this.manual) {\n        this.phase = ASYNC_BUTTON_STATES.WAITING;\n      }\n\n      const cb: AsyncButtonCallback = (success) => {\n        this.done(success);\n      };\n\n      this.$emit('click', cb);\n    },\n\n    done(success: boolean | 'cancelled') {\n      if (success === 'cancelled') {\n        this.phase = ASYNC_BUTTON_STATES.ACTION;\n      } else {\n        this.phase = (success ? ASYNC_BUTTON_STATES.SUCCESS : ASYNC_BUTTON_STATES.ERROR );\n        this.timer = setTimeout(() => {\n          this.timerDone();\n        }, this.delay);\n      }\n    },\n\n    timerDone() {\n      if ( this.phase === ASYNC_BUTTON_STATES.SUCCESS || this.phase === ASYNC_BUTTON_STATES.ERROR ) {\n        this.phase = ASYNC_BUTTON_STATES.ACTION;\n      }\n    },\n\n    focus() {\n      (this.$refs.btn as HTMLElement).focus();\n    }\n  }\n});\n</script>\n\n<template>\n  <button\n    ref=\"btn\"\n    role=\"button\"\n    :class=\"classes\"\n    :name=\"name\"\n    :type=\"type\"\n    :disabled=\"isDisabled\"\n    :aria-disabled=\"isDisabled\"\n    :tab-index=\"tabIndex\"\n    :data-testid=\"componentTestid + '-async-button'\"\n    @click=\"clicked\"\n  >\n    <span\n      v-if=\"isManualRefresh\"\n      :class=\"{'mr-10': displayIcon && size !== 'sm', 'mr-5': displayIcon && size === 'sm'}\"\n    >{{ t('action.refresh') }}</span>\n    <i\n      v-if=\"displayIcon\"\n      v-clean-tooltip=\"tooltip\"\n      :class=\"{icon: true, 'icon-lg': true, [displayIcon]: true, 'mr-0': isManualRefresh}\"\n    />\n    <span\n      v-if=\"labelAs === 'text' && displayLabel\"\n      v-clean-tooltip=\"tooltip\"\n      v-clean-html=\"displayLabel\"\n    />\n  </button>\n</template>\n\n<style lang=\"scss\" scoped>\n// refresh mode has icon + text. We need to fix the positioning of the icon and sizing\n.manual-refresh i {\n  margin: 0 0 0 8px !important;\n  font-size: 1rem !important;\n}\n</style>\n"]}]}