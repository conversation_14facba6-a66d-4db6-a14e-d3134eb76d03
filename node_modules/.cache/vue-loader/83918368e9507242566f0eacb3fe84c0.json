{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/explorer/EventsTable.vue?vue&type=style&index=0&id=0fb0e2a8&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/explorer/EventsTable.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5ldmVudHMtbGluayB7CiAgYWxpZ24tc2VsZjogY2VudGVyOwogIHBhZGRpbmctcmlnaHQ6IDIwcHg7CiAgbWluLXdpZHRoOiAyMDBweDsKfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/explorer/EventsTable.vue"], "names": [], "mappings": ";AAmHA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAClB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/explorer/EventsTable.vue", "sourceRoot": "", "sourcesContent": ["<script>\n\nimport { MESSAGE, NAME, OBJECT, REASON } from '@shell/config/table-headers';\nimport { EVENT } from '@shell/config/types';\nimport PaginatedResourceTable from '@shell/components/PaginatedResourceTable';\nimport { STEVE_EVENT_OBJECT, STEVE_NAME_COL } from '@shell/config/pagination-table-headers';\nimport { headerFromSchemaColString } from '@shell/store/type-map.utils';\nimport { NAME as EXPLORER } from '@shell/config/product/explorer';\n\nexport default {\n  components: { PaginatedResourceTable },\n\n  data() {\n    const reason = {\n      ...REASON,\n      ...{ canBeVariable: true },\n      width: 130,\n    };\n\n    const eventHeaders = [\n      reason,\n      OBJECT,\n      MESSAGE,\n      NAME, {\n        name:        'date',\n        label:       'Date',\n        labelKey:    'clusterIndexPage.sections.events.date.label',\n        value:       'timestamp',\n        sort:        'timestamp:desc',\n        formatter:   'Date',\n        width:       220,\n        defaultSort: true,\n      },\n    ];\n\n    const schema = this.$store.getters['cluster/schemaFor'](EVENT);\n\n    const paginationHeaders = schema ? [\n      reason,\n      STEVE_EVENT_OBJECT,\n      MESSAGE,\n      {\n        ...STEVE_NAME_COL,\n        defaultSort: false,\n      },\n      headerFromSchemaColString('First Seen', schema, this.$store.getters, true),\n      {\n        ...headerFromSchemaColString('Last Seen', schema, this.$store.getters, true),\n        defaultSort: true,\n      },\n      headerFromSchemaColString('Count', schema, this.$store.getters, true),\n    ] : [];\n\n    return {\n      schema,\n      events:        [],\n      eventHeaders,\n      paginationHeaders,\n      allEventsLink: {\n        name:   'c-cluster-product-resource',\n        params: {\n          product:  EXPLORER,\n          resource: EVENT,\n        }\n      }\n    };\n  },\n\n  mounted() {\n    this.dismissRouteHandler = this.$router.beforeEach(this.onRouteChange);\n  },\n\n  methods: {\n    async onRouteChange(to, from, next) {\n      if (this.$route.name !== to.name) {\n        await this.$store.dispatch('cluster/forgetType', EVENT);\n      }\n\n      next();\n    }\n  },\n\n  beforeUnmount() {\n    this.dismissRouteHandler();\n  }\n};\n</script>\n\n<template>\n  <PaginatedResourceTable\n    v-if=\"!!schema\"\n    :schema=\"schema\"\n    :headers=\"eventHeaders\"\n    :pagination-headers=\"paginationHeaders\"\n\n    key-field=\"id\"\n    :search=\"false\"\n    :table-actions=\"false\"\n    :row-actions=\"false\"\n    :groupable=\"false\"\n    :rows-per-page=\"10\"\n  >\n    <template v-slot:header-right>\n      <router-link\n        data-testid=\"events-link\"\n        :to=\"allEventsLink\"\n        class=\"events-link\"\n      >\n        <span>{{ t('glance.eventsTable') }}</span>\n      </router-link>\n    </template>\n  </PaginatedResourceTable>\n</template>\n\n<style lang=\"scss\" scoped>\n.events-link {\n  align-self: center;\n  padding-right: 20px;\n  min-width: 200px;\n}\n</style>\n"]}]}