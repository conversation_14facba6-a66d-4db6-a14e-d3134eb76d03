{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/AssignTo.vue?vue&type=style&index=0&id=65a9934d&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/AssignTo.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLmFzc2lnbi1tb2RhbCB7CiAgICBib3JkZXItcmFkaXVzOiB2YXIoLS1ib3JkZXItcmFkaXVzKTsKICAgIG92ZXJmbG93OiBzY3JvbGw7CiAgICBtYXgtaGVpZ2h0OiAxMDB2aDsKICAgICYgOjotd2Via2l0LXNjcm9sbGJhci1jb3JuZXIgewogICAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsMCwwLDApOwogICAgfQogIH0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/AssignTo.vue"], "names": [], "mappings": ";EAgLE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/AssignTo.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapState, mapGetters } from 'vuex';\nimport { FLEET, NORMAN } from '@shell/config/types';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport KeyValue from '@shell/components/form/KeyValue';\nimport AsyncButton from '@shell/components/AsyncButton';\nimport AppModal from '@shell/components/AppModal.vue';\nimport { Card } from '@components/Card';\nimport { Banner } from '@components/Banner';\nimport { exceptionToErrorsArray } from '@shell/utils/error';\nimport { set } from '@shell/utils/object';\n\nexport default {\n  components: {\n    Card,\n    LabeledSelect,\n    KeyValue,\n    AsyncButton,\n    Banner,\n    AppModal\n  },\n\n  data() {\n    return {\n      errors:        [],\n      labels:        {},\n      moveTo:        this.workspace,\n      loaded:        false,\n      allWorkspaces: [],\n      showModal:     false,\n    };\n  },\n\n  computed: {\n    ...mapState('action-menu', ['showAssignTo', 'toAssign']),\n    ...mapGetters({ t: 'i18n/t' }),\n    ...mapGetters(['workspace']),\n\n    workspaceOptions() {\n      const out = this.allWorkspaces.map((x) => x.metadata?.name).filter((x) => !!x && x !== 'fleet-local');\n\n      return out;\n    },\n\n    resourceCount() {\n      return this.toAssign?.length || 0;\n    }\n  },\n\n  watch: {\n    async showAssignTo(show) {\n      if (show) {\n        await this.$store.dispatch('rancher/findAll', { type: NORMAN.CLUSTER });\n        this.allWorkspaces = await this.$store.dispatch('management/findAll', { type: FLEET.WORKSPACE });\n        this.moveTo = this.workspace;\n        this.loaded = true;\n        this.showModal = true;\n      } else {\n        this.showModal = false;\n      }\n    }\n  },\n\n  methods: {\n    close() {\n      this.errors = [];\n      this.labels = {};\n      this.moveTo = this.workspace;\n      this.$store.commit('action-menu/toggleAssignTo');\n    },\n\n    async apply(buttonDone) {\n      const promises = [];\n\n      this.errors = [];\n\n      for ( const fleetCluster of this.toAssign ) {\n        const c = await this.$store.dispatch(`rancher/clone`, { resource: fleetCluster.norman });\n\n        if ( !c ) {\n          continue;\n        }\n\n        c.fleetWorkspaceName = this.moveTo;\n\n        for ( const k of Object.keys(this.labels) ) {\n          if ( !c._labels ) {\n            set(c, '_labels', {});\n          }\n\n          set(c._labels, k, this.labels[k]);\n        }\n\n        promises.push(c.save());\n      }\n\n      try {\n        await Promise.all(promises);\n        buttonDone(true);\n        this.close();\n      } catch (e) {\n        this.errors = exceptionToErrorsArray(e);\n        buttonDone(false);\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <app-modal\n    v-if=\"showModal\"\n    class=\"assign-modal\"\n    name=\"assignTo\"\n    styles=\"background-color: var(--nav-bg); border-radius: var(--border-radius); max-height: 100vh;\"\n    height=\"auto\"\n    :scrollable=\"true\"\n    @close=\"close\"\n  >\n    <Card\n      v-if=\"loaded\"\n      :show-highlight-border=\"false\"\n    >\n      <template #title>\n        <h4\n          v-clean-html=\"t('assignTo.title', {count: resourceCount}, true)\"\n          class=\"text-default-text\"\n        />\n      </template>\n      <template #body>\n        <div class=\"pl-10 pr-10\">\n          <form>\n            <LabeledSelect\n              v-model:value=\"moveTo\"\n              data-testid=\"workspace_options\"\n              :label=\"t('assignTo.workspace')\"\n              :options=\"workspaceOptions\"\n              placement=\"bottom\"\n            />\n\n            <KeyValue\n              key=\"labels\"\n              v-model:value=\"labels\"\n              class=\"mt-20\"\n              :add-label=\"t('labels.addSetLabel')\"\n              :read-allowed=\"false\"\n            />\n\n            <Banner\n              v-for=\"(err, i) in errors\"\n              :key=\"i\"\n              color=\"error\"\n              :label=\"err\"\n            />\n          </form>\n        </div>\n      </template>\n\n      <template #actions>\n        <button\n          class=\"btn role-secondary\"\n          @click=\"close\"\n        >\n          {{ t('generic.cancel') }}\n        </button>\n\n        <AsyncButton\n          mode=\"apply\"\n          @click=\"apply\"\n        />\n      </template>\n    </Card>\n  </app-modal>\n</template>\n\n<style lang='scss'>\n  .assign-modal {\n    border-radius: var(--border-radius);\n    overflow: scroll;\n    max-height: 100vh;\n    & ::-webkit-scrollbar-corner {\n      background: rgba(0,0,0,0);\n    }\n  }\n</style>\n"]}]}