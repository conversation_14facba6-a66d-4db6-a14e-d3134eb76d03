{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/ToggleSwitch/ToggleSwitch.vue?vue&type=template&id=0f8869ea&scoped=true&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/ToggleSwitch/ToggleSwitch.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPHNwYW4gY2xhc3M9InRvZ2dsZS1jb250YWluZXIiPgogICAgPHNwYW4KICAgICAgY2xhc3M9ImxhYmVsIG5vLXNlbGVjdCBoYW5kIgogICAgICA6Y2xhc3M9InsgYWN0aXZlOiAhc3RhdGV9IgogICAgICBAY2xpY2s9InRvZ2dsZShmYWxzZSkiCiAgICA+e3sgb2ZmTGFiZWwgfX08L3NwYW4+CiAgICA8bGFiZWwgY2xhc3M9InN3aXRjaCBoYW5kIj4KICAgICAgPGlucHV0CiAgICAgICAgcmVmPSJzd2l0Y2hJbnB1dCIKICAgICAgICB0eXBlPSJjaGVja2JveCIKICAgICAgICByb2xlPSJzd2l0Y2giCiAgICAgICAgOmNoZWNrZWQ9InN0YXRlIgogICAgICAgIDphcmlhLWxhYmVsPSJvbkxhYmVsIgogICAgICAgIEBpbnB1dD0idG9nZ2xlKG51bGwpIgogICAgICAgIEBrZXlkb3duLmVudGVyPSJ0b2dnbGUobnVsbCkiCiAgICAgID4KICAgICAgPHNwYW4KICAgICAgICByZWY9InN3aXRjaENocm9tZSIKICAgICAgICBjbGFzcz0ic2xpZGVyIHJvdW5kIgogICAgICAvPgogICAgPC9sYWJlbD4KICAgIDxzcGFuCiAgICAgIGNsYXNzPSJsYWJlbCBuby1zZWxlY3QgaGFuZCIKICAgICAgOmNsYXNzPSJ7IGFjdGl2ZTogc3RhdGV9IgogICAgICBAY2xpY2s9InRvZ2dsZSh0cnVlKSIKICAgID57eyBvbkxhYmVsIH19PC9zcGFuPgogIDwvc3Bhbj4K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/ToggleSwitch/ToggleSwitch.vue"], "names": [], "mappings": ";EAiFE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC,CAAC;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B;MACA,CAAC,CAAC,CAAC,CAAC;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/ToggleSwitch/ToggleSwitch.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent, onMounted, onBeforeUnmount, ref } from 'vue';\n\ntype StateType = boolean | 'true' | 'false' | undefined;\n\nexport default defineComponent({\n  props: {\n    value: {\n      type:    [<PERSON><PERSON><PERSON>, String, Number],\n      default: false\n    },\n\n    offValue: {\n      type:    [<PERSON>olean, String, Number],\n      default: false,\n    },\n\n    onValue: {\n      type:    [<PERSON>olean, String, Number],\n      default: true,\n    },\n\n    offLabel: {\n      type:    String,\n      default: '',\n    },\n\n    onLabel: {\n      type:    String,\n      default: '',\n    },\n  },\n\n  emits: ['update:value'],\n\n  setup() {\n    const switchChrome = ref<HTMLElement | null>(null);\n    const focus = () => {\n      switchChrome.value?.classList.add('focus');\n    };\n\n    const blur = () => {\n      switchChrome.value?.classList.remove('focus');\n    };\n\n    const switchInput = ref<HTMLInputElement | null>(null);\n\n    onMounted(() => {\n      switchInput.value?.addEventListener('focus', focus);\n      switchInput.value?.addEventListener('blur', blur);\n    });\n\n    onBeforeUnmount(() => {\n      switchInput.value?.removeEventListener('focus', focus);\n      switchInput.value?.removeEventListener('blur', blur);\n    });\n  },\n\n  data() {\n    return { state: false as StateType };\n  },\n\n  watch: {\n    value: {\n      handler() {\n        this.state = this.value === this.onValue;\n      },\n      immediate: true\n    }\n  },\n\n  methods: {\n    toggle(neu: StateType | null) {\n      this.state = neu === null ? !this.state : neu;\n      this.$emit('update:value', this.state ? this.onValue : this.offValue);\n    }\n  }\n});\n</script>\n\n<template>\n  <span class=\"toggle-container\">\n    <span\n      class=\"label no-select hand\"\n      :class=\"{ active: !state}\"\n      @click=\"toggle(false)\"\n    >{{ offLabel }}</span>\n    <label class=\"switch hand\">\n      <input\n        ref=\"switchInput\"\n        type=\"checkbox\"\n        role=\"switch\"\n        :checked=\"state\"\n        :aria-label=\"onLabel\"\n        @input=\"toggle(null)\"\n        @keydown.enter=\"toggle(null)\"\n      >\n      <span\n        ref=\"switchChrome\"\n        class=\"slider round\"\n      />\n    </label>\n    <span\n      class=\"label no-select hand\"\n      :class=\"{ active: state}\"\n      @click=\"toggle(true)\"\n    >{{ onLabel }}</span>\n  </span>\n</template>\n\n<style lang=\"scss\" scoped>\n$toggle-height: 16px;\n\n.toggle-container {\n  align-items: center;\n  display: flex;\n\n  span:first-child {\n    padding-right: 6px;\n  }\n  span:last-child {\n    padding-left: 6px;\n  }\n}\n/* The switch - the box around the slider */\n.switch {\n  position: relative;\n  display: inline-block;\n  width: 48px;\n  height: $toggle-height + 8px;\n}\n\n/* Hide default HTML checkbox */\n.switch input {\n  opacity: 0;\n  width: 0;\n  height: 0;\n}\n\n/* The slider */\n.slider {\n  position: absolute;\n  cursor: pointer;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: var(--checkbox-disabled-bg);\n  -webkit-transition: .4s;\n  transition: .4s;\n\n  &.focus {\n    @include focus-outline;\n    outline-offset: 2px;\n    -webkit-transition: 0s;\n    transition: 0s;\n  }\n}\n\n.slider:before {\n  position: absolute;\n  content: \"\";\n  height: $toggle-height;\n  width: $toggle-height;\n  left: 4px;\n  bottom: 4px;\n  background-color: var(--checkbox-tick);\n  -webkit-transition: .4s;\n  transition: .4s;\n}\n\ninput:checked + .slider {\n  background-color: var(--checkbox-ticked-bg);\n}\n\ninput:focus + .slider {\n  box-shadow: 0 0 1px var(--checkbox-ticked-bg);\n}\n\ninput:checked + .slider:before {\n  transform: translateX(24px);\n}\n\n/* Rounded sliders */\n.slider.round {\n  border-radius: 34px;\n}\n\n.slider.round:before {\n  border-radius: 50%;\n}\n</style>\n"]}]}