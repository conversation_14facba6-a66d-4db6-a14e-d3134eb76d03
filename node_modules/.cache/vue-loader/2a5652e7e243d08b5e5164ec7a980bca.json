{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceDetail/Masthead.vue?vue&type=style&index=0&id=c0c5e95c&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceDetail/Masthead.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLm1hc3RoZWFkIHsKICAgIHBhZGRpbmctYm90dG9tOiAxMHB4OwogICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkIHZhcigtLWJvcmRlcik7CiAgICBtYXJnaW4tYm90dG9tOiAxMHB4OwogIH0KCiAgSEVBREVSIHsKICAgIG1hcmdpbjogMDsKICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogbWlubWF4KDAsIDFmcikgYXV0bzsKICB9CgogIC5wcmltYXJ5aGVhZGVyIHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBmbGV4LWRpcmVjdGlvbjogcm93OwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKCiAgICBoMSB7CiAgICAgIG1hcmdpbjogMCAwIDAgLTVweDsKICAgICAgb3ZlcmZsb3cteDogaGlkZGVuOwogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBmbGV4LWRpcmVjdGlvbjogcm93OwogICAgICBhbGlnbi1pdGVtczogY2VudGVyOwoKICAgICAgLm1hc3RoZWFkLXJlc291cmNlLXRpdGxlIHsKICAgICAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsKICAgICAgICBvdmVyZmxvdzogaGlkZGVuOwogICAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7CiAgICAgIH0KCiAgICAgIC5tYXN0aGVhZC1yZXNvdXJjZS1saXN0LWxpbmsgewogICAgICAgIG1hcmdpbjogNXB4OwogICAgICB9CiAgICB9CiAgfQoKICAuc3ViaGVhZGVyewogICAgZGlzcGxheTogZmxleDsKICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7CiAgICBjb2xvcjogdmFyKC0taW5wdXQtbGFiZWwpOwogICAgJiA+ICogewogICAgICBtYXJnaW46IDVweCAyMHB4IDVweCAwcHg7CiAgICB9CgogICAgLmxpdmUtZGF0YSB7CiAgICAgIGNvbG9yOiB2YXIoLS1ib2R5LXRleHQpOwogICAgICBtYXJnaW4tbGVmdDogM3B4OwogICAgfQogIH0KCiAgLnN0YXRlLWJhbm5lciB7CiAgICBtYXJnaW46IDNweCAwIDAgMDsKICB9CgogIC5tYXN0aGVhZC1zdGF0ZSB7CiAgICBtYXJnaW4tbGVmdDogOHB4OwogICAgZm9udC1zaXplOiBpbml0aWFsOwogIH0KCiAgLm1hc3RoZWFkLWlzdGlvIHsKICAgIC5pY29uIHsKICAgICAgdmVydGljYWwtYWxpZ246IG1pZGRsZTsKICAgICAgY29sb3I6IHZhcigtLXByaW1hcnkpOwogICAgfQogIH0KCiAgLmxlZnQtcmlnaHQtc3BsaXQgewogICAgZGlzcGxheTogZ3JpZDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CgogICAgLmxlZnQtaGFsZiB7CiAgICAgIGdyaWQtY29sdW1uOiAxOwogICAgfQoKICAgIC5yaWdodC1oYWxmIHsKICAgICAgZ3JpZC1jb2x1bW46IDI7CiAgICB9CiAgfQoKICBkaXYuYWN0aW9ucy1jb250YWluZXIgPiBkaXYuYWN0aW9ucyB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgZmxleC1kaXJlY3Rpb246IHJvdzsKICAgIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7CiAgfQoKICAucmVzb3VyY2UtZXh0ZXJuYWwgewogICAgZm9udC1zaXplOiAxOHB4OwogIH0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceDetail/Masthead.vue"], "names": [], "mappings": ";EA4lBE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC5C;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEnB,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACb;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,EAAE,EAAE,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1B;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC;EACnB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceDetail/Masthead.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { KUBERNETES, PROJECT } from '@shell/config/labels-annotations';\nimport { FLEET, NAMESPACE, MANAGEMENT, HELM } from '@shell/config/types';\nimport ButtonGroup from '@shell/components/ButtonGroup';\nimport { BadgeState } from '@components/BadgeState';\nimport { Banner } from '@components/Banner';\nimport { get } from '@shell/utils/object';\nimport { NAME as FLEET_NAME } from '@shell/config/product/fleet';\nimport { HIDE_SENSITIVE } from '@shell/store/prefs';\nimport {\n  AS, _DETAIL, _CONFIG, _YAML, MODE, _CREATE, _EDIT, _VIEW, _UNFLAG, _GRAPH\n} from '@shell/config/query-params';\nimport { ExtensionPoint, PanelLocation } from '@shell/core/types';\nimport ExtensionPanel from '@shell/components/ExtensionPanel';\nimport TabTitle from '@shell/components/TabTitle';\n\n// i18n-uses resourceDetail.header.*\n\n/**\n * Resource Detail Masthead component.\n *\n * ToDo: this component seem to be picking up a lot of logic from special cases, could be simplified down to parameters and then customized per use-case via wrapper component\n */\nexport default {\n\n  name: 'MastheadResourceDetail',\n\n  components: {\n    BadgeState, Banner, ButtonGroup, ExtensionPanel, TabTitle\n  },\n  props: {\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n\n    mode: {\n      type:    String,\n      default: 'create'\n    },\n\n    realMode: {\n      type:    String,\n      default: 'create'\n    },\n\n    as: {\n      type:    String,\n      default: _YAML,\n    },\n\n    hasGraph: {\n      type:    Boolean,\n      default: false\n    },\n\n    hasDetail: {\n      type:    Boolean,\n      default: false\n    },\n\n    hasEdit: {\n      type:    Boolean,\n      default: false\n    },\n\n    storeOverride: {\n      type:    String,\n      default: null,\n    },\n\n    resource: {\n      type:    String,\n      default: null,\n    },\n\n    resourceSubtype: {\n      type:    String,\n      default: null,\n    },\n\n    parentRouteOverride: {\n      type:    String,\n      default: null,\n    },\n\n    canViewYaml: {\n      type:    Boolean,\n      default: false,\n    }\n  },\n\n  data() {\n    return {\n      DETAIL_VIEW:       _DETAIL,\n      extensionType:     ExtensionPoint.PANEL,\n      extensionLocation: PanelLocation.DETAILS_MASTHEAD,\n    };\n  },\n\n  computed: {\n    dev() {\n      return this.$store.getters['prefs/dev'];\n    },\n\n    schema() {\n      const inStore = this.storeOverride || this.$store.getters['currentStore'](this.resource);\n\n      return this.$store.getters[`${ inStore }/schemaFor`]( this.resource );\n    },\n\n    isView() {\n      return this.mode === _VIEW;\n    },\n\n    isEdit() {\n      return this.mode === _EDIT;\n    },\n\n    isCreate() {\n      return this.mode === _CREATE;\n    },\n\n    isNamespace() {\n      return this.schema?.id === NAMESPACE;\n    },\n\n    isProject() {\n      return this.schema?.id === MANAGEMENT.PROJECT;\n    },\n\n    isProjectHelmChart() {\n      return this.schema?.id === HELM.PROJECTHELMCHART;\n    },\n\n    hasMultipleNamespaces() {\n      return !!this.value.namespaces;\n    },\n\n    namespace() {\n      if (this.value?.metadata?.namespace) {\n        return this.value?.metadata?.namespace;\n      }\n\n      return null;\n    },\n\n    detailsAction() {\n      return this.value?.detailsAction;\n    },\n\n    shouldHifenize() {\n      return (this.mode === 'view' || this.mode === 'edit') && this.resourceSubtype?.length && this.value?.nameDisplay?.length;\n    },\n\n    namespaceLocation() {\n      if (!this.isNamespace) {\n        return this.value.namespaceLocation || {\n          name:   'c-cluster-product-resource-id',\n          params: {\n            cluster:  this.$route.params.cluster,\n            product:  this.$store.getters['productId'],\n            resource: NAMESPACE,\n            id:       this.$route.params.namespace\n          }\n        };\n      }\n\n      return null;\n    },\n\n    isWorkspace() {\n      return this.$store.getters['productId'] === FLEET_NAME && !!this.value?.metadata?.namespace;\n    },\n\n    workspaceLocation() {\n      return {\n        name:   'c-cluster-product-resource-id',\n        params: {\n          cluster:  this.$route.params.cluster,\n          product:  this.$store.getters['productId'],\n          resource: FLEET.WORKSPACE,\n          id:       this.$route.params.namespace\n        }\n      };\n    },\n\n    project() {\n      if (this.isNamespace) {\n        const cluster = this.$store.getters['currentCluster'];\n\n        if (cluster) {\n          const id = (this.value?.metadata?.labels || {})[PROJECT];\n\n          return this.$store.getters['management/byId'](MANAGEMENT.PROJECT, `${ cluster.id }/${ id }`);\n        }\n      }\n\n      return null;\n    },\n\n    banner() {\n      if (this.value?.stateObj?.error) {\n        const defaultErrorMessage = this.t('resourceDetail.masthead.defaultBannerMessage.error', undefined, true);\n\n        return {\n          color:   'error',\n          message: this.value.stateObj.message || defaultErrorMessage\n        };\n      }\n\n      if (this.value?.spec?.paused) {\n        return {\n          color:   'info',\n          message: this.t('asyncButton.pause.description')\n        };\n      }\n\n      if (this.value?.stateObj?.transitioning) {\n        const defaultTransitioningMessage = this.t('resourceDetail.masthead.defaultBannerMessage.transitioning', undefined, true);\n\n        return {\n          color:   'info',\n          message: this.value.stateObj.message || defaultTransitioningMessage\n        };\n      }\n\n      return null;\n    },\n\n    parent() {\n      const displayName = this.value?.parentNameOverride || this.$store.getters['type-map/labelFor'](this.schema);\n      const product = this.$store.getters['currentProduct'].name;\n\n      const defaultLocation = {\n        name:   'c-cluster-product-resource',\n        params: {\n          resource: this.resource,\n          product,\n        }\n      };\n\n      const location = this.value?.parentLocationOverride || defaultLocation;\n\n      if (this.parentRouteOverride) {\n        location.name = this.parentRouteOverride;\n      }\n\n      const typeOptions = this.$store.getters[`type-map/optionsFor`]( this.resource );\n      const out = {\n        displayName, location, ...typeOptions\n      };\n\n      return out;\n    },\n\n    hideSensitiveData() {\n      return this.$store.getters['prefs/get'](HIDE_SENSITIVE);\n    },\n\n    sensitiveOptions() {\n      return [\n        {\n          tooltipKey: 'resourceDetail.masthead.sensitive.hide',\n          icon:       'icon-hide',\n          value:      true,\n        },\n        {\n          tooltipKey: 'resourceDetail.masthead.sensitive.show',\n          icon:       'icon-show',\n          value:      false\n        }\n      ];\n    },\n\n    viewOptions() {\n      const out = [];\n\n      if ( this.hasDetail ) {\n        out.push({\n          labelKey: 'resourceDetail.masthead.detail',\n          value:    _DETAIL,\n        });\n      }\n\n      if ( this.hasEdit && this.parent?.showConfigView !== false) {\n        out.push({\n          labelKey: 'resourceDetail.masthead.config',\n          value:    _CONFIG,\n        });\n      }\n\n      if ( this.hasGraph ) {\n        out.push({\n          labelKey: 'resourceDetail.masthead.graph',\n          value:    _GRAPH,\n        });\n      }\n\n      if ( this.canViewYaml ) {\n        out.push({\n          labelKey: 'resourceDetail.masthead.yaml',\n          value:    _YAML,\n        });\n      }\n\n      if ( out.length < 2 ) {\n        return null;\n      }\n\n      return out;\n    },\n\n    currentView: {\n      get() {\n        return this.as;\n      },\n\n      set(val) {\n        switch ( val ) {\n        case _DETAIL:\n          this.$router.applyQuery({\n            [MODE]: _UNFLAG,\n            [AS]:   _UNFLAG,\n          });\n          break;\n        case _CONFIG:\n          this.$router.applyQuery({\n            [MODE]: _UNFLAG,\n            [AS]:   _CONFIG,\n          });\n          break;\n        case _GRAPH:\n          this.$router.applyQuery({\n            [MODE]: _UNFLAG,\n            [AS]:   _GRAPH,\n          });\n          break;\n        case _YAML:\n          this.$router.applyQuery({\n            [MODE]: _UNFLAG,\n            [AS]:   _YAML,\n          });\n          break;\n        }\n      },\n    },\n\n    showSensitiveToggle() {\n      return !!this.value.hasSensitiveData && this.mode === _VIEW && this.as !== _YAML;\n    },\n\n    managedWarning() {\n      const { value } = this;\n      const labels = value?.metadata?.labels || {};\n\n      const managedBy = labels[KUBERNETES.MANAGED_BY] || '';\n      const appName = labels[KUBERNETES.MANAGED_NAME] || labels[KUBERNETES.INSTANCE] || '';\n\n      return {\n        show:    this.mode === _EDIT && !!managedBy,\n        type:    value?.kind || '',\n        hasName: appName ? 'yes' : 'no',\n        appName,\n        managedBy,\n      };\n    },\n\n    displayName() {\n      let displayName = this.value.nameDisplay;\n\n      if (this.isProjectHelmChart) {\n        displayName = this.value.projectDisplayName;\n      }\n\n      return this.shouldHifenize ? ` - ${ displayName }` : displayName;\n    },\n\n    location() {\n      const { parent } = this;\n\n      return parent?.location;\n    },\n\n    hideNamespaceLocation() {\n      return this.$store.getters['currentProduct'].hideNamespaceLocation || this.value.namespaceLocation === null;\n    },\n\n    resourceExternalLink() {\n      return this.value.resourceExternalLink;\n    },\n  },\n\n  methods: {\n    get,\n\n    showActions() {\n      this.$store.commit('action-menu/show', {\n        resources: this.value,\n        elem:      this.$refs.actions,\n      });\n    },\n\n    toggleSensitiveData(e) {\n      this.$store.dispatch('prefs/set', { key: HIDE_SENSITIVE, value: !!e });\n    },\n\n    invokeDetailsAction() {\n      const action = this.detailsAction;\n\n      if (action) {\n        const fn = this.value[action.action];\n\n        if (fn) {\n          fn.apply(this.value, []);\n        }\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <div class=\"masthead\">\n    <header>\n      <div class=\"title\">\n        <div class=\"primaryheader\">\n          <h1>\n            <TabTitle\n              v-if=\"isCreate\"\n              :showChild=\"false\"\n            >\n              {{ parent.displayName }}\n            </TabTitle>\n            <TabTitle\n              v-else\n              :showChild=\"false\"\n            >\n              {{ displayName }}\n            </TabTitle>\n            <router-link\n              v-if=\"location\"\n              :to=\"location\"\n              role=\"link\"\n              class=\"masthead-resource-list-link\"\n              :aria-label=\"parent.displayName\"\n            >\n              {{ parent.displayName }}:\n            </router-link>\n            <span v-else>{{ parent.displayName }}:</span>\n            <span v-if=\"value?.detailPageHeaderActionOverride && value?.detailPageHeaderActionOverride(realMode)\">{{ value?.detailPageHeaderActionOverride(realMode) }}</span>\n            <t\n              v-else\n              class=\"masthead-resource-title\"\n              :k=\"'resourceDetail.header.' + realMode\"\n              :subtype=\"resourceSubtype\"\n              :name=\"displayName\"\n              :escapehtml=\"false\"\n            />\n            <BadgeState\n              v-if=\"!isCreate && parent.showState\"\n              class=\"masthead-state\"\n              :value=\"value\"\n            />\n            <span\n              v-if=\"!isCreate && value.injectionEnabled\"\n              class=\"masthead-istio\"\n            >\n              <i\n                v-clean-tooltip=\"t('projectNamespaces.isIstioInjectionEnabled')\"\n                class=\"icon icon-sm icon-istio\"\n              />\n            </span>\n            <a\n              v-if=\"dev && !!resourceExternalLink\"\n              v-clean-tooltip=\"t(resourceExternalLink.tipsKey || 'generic.resourceExternalLinkTips')\"\n              class=\"resource-external\"\n              rel=\"nofollow noopener noreferrer\"\n              target=\"_blank\"\n              :href=\"resourceExternalLink.url\"\n            >\n              <i class=\"icon icon-external-link\" />\n            </a>\n          </h1>\n        </div>\n        <div\n          v-if=\"!isCreate\"\n          class=\"subheader\"\n        >\n          <span v-if=\"isNamespace && project\">{{ t(\"resourceDetail.masthead.project\") }}: <router-link :to=\"project.detailLocation\">{{ project.nameDisplay }}</router-link></span>\n          <span v-else-if=\"isWorkspace\">{{ t(\"resourceDetail.masthead.workspace\") }}: <router-link :to=\"workspaceLocation\">{{ namespace }}</router-link></span>\n          <span v-else-if=\"namespace && !hasMultipleNamespaces\">\n            {{ t(\"resourceDetail.masthead.namespace\") }}:\n            <router-link\n              v-if=\"!hideNamespaceLocation\"\n              :to=\"namespaceLocation\"\n              data-testid=\"masthead-subheader-namespace\"\n            >\n              {{ namespace }}\n            </router-link>\n            <span v-else>\n              {{ namespace }}\n            </span>\n          </span>\n          <span v-if=\"parent.showAge\">\n            {{ t(\"resourceDetail.masthead.age\") }}:\n            <LiveDate\n              class=\"live-date\"\n              :value=\"value.creationTimestamp\"\n            />\n          </span>\n          <span\n            v-if=\"value.showCreatedBy\"\n            data-testid=\"masthead-subheader-createdBy\"\n          >\n            {{ t(\"resourceDetail.masthead.createdBy\") }}:\n            <router-link\n              v-if=\"value.createdBy.location\"\n              :to=\"value.createdBy.location\"\n              data-testid=\"masthead-subheader-createdBy-link\"\n            >\n              {{ value.createdBy.displayName }}\n            </router-link>\n            <span\n              v-else\n              data-testid=\"masthead-subheader-createdBy_plain-text\"\n            >\n              {{ value.createdBy.displayName }}\n            </span>\n          </span>\n          <span v-if=\"value.showPodRestarts\">{{ t(\"resourceDetail.masthead.restartCount\") }}:<span class=\"live-data\"> {{ value.restartCount }}</span></span>\n        </div>\n      </div>\n      <slot name=\"right\">\n        <div class=\"actions-container align-start\">\n          <div class=\"actions\">\n            <button\n              v-if=\"detailsAction && currentView === DETAIL_VIEW && isView\"\n              type=\"button\"\n              class=\"btn role-primary actions mr-10\"\n              :disabled=\"!detailsAction.enabled\"\n              @click=\"invokeDetailsAction\"\n            >\n              {{ detailsAction.label }}\n            </button>\n            <ButtonGroup\n              v-if=\"showSensitiveToggle\"\n              :value=\"!!hideSensitiveData\"\n              icon-size=\"lg\"\n              :options=\"sensitiveOptions\"\n              class=\"mr-10\"\n              @update:value=\"toggleSensitiveData\"\n            />\n\n            <ButtonGroup\n              v-if=\"viewOptions && isView\"\n              v-model:value=\"currentView\"\n              :options=\"viewOptions\"\n              class=\"mr-10\"\n            />\n\n            <button\n              v-if=\"isView\"\n              ref=\"actions\"\n              data-testid=\"masthead-action-menu\"\n              aria-haspopup=\"true\"\n              type=\"button\"\n              class=\"btn role-multi-action actions\"\n              @click=\"showActions\"\n            >\n              <i class=\"icon icon-actions\" />\n            </button>\n          </div>\n        </div>\n      </slot>\n    </header>\n\n    <!-- Extension area -->\n    <ExtensionPanel\n      :resource=\"value\"\n      :type=\"extensionType\"\n      :location=\"extensionLocation\"\n    />\n\n    <Banner\n      v-if=\"banner && isView && !parent.hideBanner\"\n      class=\"state-banner mb-10\"\n      :color=\"banner.color\"\n      :label=\"banner.message\"\n    />\n    <Banner\n      v-if=\"managedWarning.show\"\n      color=\"warning\"\n      class=\"mb-20\"\n      :label=\"t('resourceDetail.masthead.managedWarning', managedWarning)\"\n    />\n\n    <slot />\n  </div>\n</template>\n\n<style lang='scss' scoped>\n  .masthead {\n    padding-bottom: 10px;\n    border-bottom: 1px solid var(--border);\n    margin-bottom: 10px;\n  }\n\n  HEADER {\n    margin: 0;\n    grid-template-columns: minmax(0, 1fr) auto;\n  }\n\n  .primaryheader {\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n\n    h1 {\n      margin: 0 0 0 -5px;\n      overflow-x: hidden;\n      display: flex;\n      flex-direction: row;\n      align-items: center;\n\n      .masthead-resource-title {\n        text-overflow: ellipsis;\n        overflow: hidden;\n        white-space: nowrap;\n      }\n\n      .masthead-resource-list-link {\n        margin: 5px;\n      }\n    }\n  }\n\n  .subheader{\n    display: flex;\n    flex-direction: row;\n    color: var(--input-label);\n    & > * {\n      margin: 5px 20px 5px 0px;\n    }\n\n    .live-data {\n      color: var(--body-text);\n      margin-left: 3px;\n    }\n  }\n\n  .state-banner {\n    margin: 3px 0 0 0;\n  }\n\n  .masthead-state {\n    margin-left: 8px;\n    font-size: initial;\n  }\n\n  .masthead-istio {\n    .icon {\n      vertical-align: middle;\n      color: var(--primary);\n    }\n  }\n\n  .left-right-split {\n    display: grid;\n    align-items: center;\n\n    .left-half {\n      grid-column: 1;\n    }\n\n    .right-half {\n      grid-column: 2;\n    }\n  }\n\n  div.actions-container > div.actions {\n    display: flex;\n    flex-direction: row;\n    justify-content: flex-end;\n  }\n\n  .resource-external {\n    font-size: 18px;\n  }\n</style>\n"]}]}