{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Setting.vue?vue&type=style&index=0&id=08a1e59d&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Setting.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5zZXR0aW5ncy12YWx1ZSBwcmUgewogIG1hcmdpbjogMDsKfQouYWR2YW5jZWQtc2V0dGluZyB7CiAgYm9yZGVyOiAxcHggc29saWQgdmFyKC0tYm9yZGVyKTsKICBwYWRkaW5nOiAyMHB4OwogIGJvcmRlci1yYWRpdXM6IHZhcigtLWJvcmRlci1yYWRpdXMpOwoKICBoMSB7CiAgICBmb250LXNpemU6IDE0cHg7CiAgfQogIGgyIHsKICAgIGZvbnQtc2l6ZTogMTJweDsKICAgIG1hcmdpbi1ib3R0b206IDA7CiAgICBvcGFjaXR5OiAwLjg7CiAgfQp9CgouaGVhZGVyIHsKICBkaXNwbGF5OiBmbGV4OwogIG1hcmdpbi1ib3R0b206IDIwcHg7Cn0KCi50aXRsZSB7CiAgZmxleDogMTsKfQoKLm1vZGlmaWVkIHsKICBtYXJnaW4tbGVmdDogMTBweDsKICBib3JkZXI6IDFweCBzb2xpZCB2YXIoLS1wcmltYXJ5KTsKICBib3JkZXItcmFkaXVzOiA1cHg7CiAgcGFkZGluZzogMnB4IDEwcHg7CiAgZm9udC1zaXplOiAxMnB4Owp9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Setting.vue"], "names": [], "mappings": ";AAgFA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACX;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEnC,CAAC,EAAE;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB;EACA,CAAC,EAAE;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACd;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACrB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACT;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Setting.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport ActionMenu from '@shell/components/ActionMenuShell.vue';\nimport { mapGetters } from 'vuex';\nexport default {\n  name:       'Setting',\n  components: { ActionMenu },\n  props:      {\n    value: {\n      type:     Object,\n      required: true,\n    },\n  },\n  computed: {\n    ...mapGetters({ t: 'i18n/t' }),\n    ...mapGetters({ options: 'action-menu/optionsArray' }),\n  },\n};\n</script>\n\n<template>\n  <div\n    class=\"advanced-setting mb-20\"\n    :data-testid=\"`advanced-setting__option-${value.id}`\"\n  >\n    <div class=\"header\">\n      <div class=\"title\">\n        <h1>\n          {{ value.id }}\n          <span\n            v-if=\"value.fromEnv\"\n            class=\"modified\"\n          >{{ t('advancedSettings.setEnv') }}</span>\n          <span\n            v-else-if=\"value.customized\"\n            class=\"modified\"\n          >{{ t('advancedSettings.modified') }}</span>\n        </h1>\n        <h2>{{ t(`advancedSettings.descriptions.${value.id}`) }}</h2>\n      </div>\n      <div\n        v-if=\"value.hasActions\"\n        class=\"action\"\n      >\n        <action-menu\n          :resource=\"value.data\"\n          :button-aria-label=\"t('advancedSettings.edit.label')\"\n          data-testid=\"action-button\"\n          button-role=\"tertiary\"\n        />\n      </div>\n    </div>\n    <div value>\n      <div v-if=\"value.canHide\">\n        <button\n          class=\"btn btn-sm role-primary\"\n          role=\"button\"\n          :aria-label=\"t('advancedSettings.hideShow')\"\n          @click=\"value.hide = !value.hide\"\n        >\n          {{ value.hide ? t('advancedSettings.show') : t('advancedSettings.hide') }} {{ value.id }}\n        </button>\n      </div>\n      <div\n        v-show=\"!value.canHide || (value.canHide && !value.hide)\"\n        class=\"settings-value\"\n      >\n        <pre v-if=\"value.kind === 'json'\">{{ value.json }}</pre>\n        <pre v-else-if=\"value.kind === 'multiline'\">{{ value.data.value || value.data.default }}</pre>\n        <pre v-else-if=\"value.kind === 'enum'\">{{ t(value.enum) }}</pre>\n        <pre v-else-if=\"value.data.value || value.data.default\">{{ value.data.value || value.data.default }}</pre>\n        <pre\n          v-else\n          class=\"text-muted\"\n        >&lt;{{ t('advancedSettings.none') }}&gt;</pre>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang='scss' scoped>\n.settings-value pre {\n  margin: 0;\n}\n.advanced-setting {\n  border: 1px solid var(--border);\n  padding: 20px;\n  border-radius: var(--border-radius);\n\n  h1 {\n    font-size: 14px;\n  }\n  h2 {\n    font-size: 12px;\n    margin-bottom: 0;\n    opacity: 0.8;\n  }\n}\n\n.header {\n  display: flex;\n  margin-bottom: 20px;\n}\n\n.title {\n  flex: 1;\n}\n\n.modified {\n  margin-left: 10px;\n  border: 1px solid var(--primary);\n  border-radius: 5px;\n  padding: 2px 10px;\n  font-size: 12px;\n}\n</style>\n"]}]}