{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CountBox.vue?vue&type=style&index=0&id=4cd1e4bf&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CountBox.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgICAuY291bnQgewogICAgICAkcGFkZGluZzogMTBweDsKCiAgICAgIHBhZGRpbmc6ICRwYWRkaW5nOwogICAgICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7CiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgIGJvcmRlci13aWR0aDogMnB4OwogICAgICBib3JkZXItc3R5bGU6IHNvbGlkOwogICAgICBib3JkZXItbGVmdDogMDsKCiAgICAgIC5kYXRhIHsKICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICAgICAgZmxleDogMTsKCiAgICAgICAgbGFiZWwgewogICAgICAgICAgb3BhY2l0eTogMC43OwogICAgICAgIH0KCiAgICAgICAgJi5jb21wYWN0IHsKICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogcm93OwoKICAgICAgICAgIGgxIHsKICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMDsKICAgICAgICAgICAgcGFkZGluZy1ib3R0b206IDA7CiAgICAgICAgICB9CgogICAgICAgICAgbGFiZWwgewogICAgICAgICAgICBtYXJnaW4tbGVmdDogNXB4OwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQoKICAgICAgaDEgewogICAgICAgIGZvbnQtc2l6ZTogNDBweDsKICAgICAgICBsaW5lLWhlaWdodDogMzZweDsKICAgICAgICBwYWRkaW5nLWJvdHRvbTogbWF0aC5kaXYoJHBhZGRpbmcsIDIpOwogICAgICAgIG1hcmdpbi1ib3R0b206IDVweDsKICAgICAgfQoKICAgICAgQG1lZGlhIG9ubHkgc2NyZWVuIGFuZCAobWluLXdpZHRoOiBtYXAtZ2V0KCRicmVha3BvaW50cywgJy0tdmlld3BvcnQtNycpKSB7CiAgICAgICAgaDEgewogICAgICAgICAgZm9udC1zaXplOiA0MHB4OwogICAgICAgICAgbGluZS1oZWlnaHQ6IDM2cHg7CiAgICAgICAgfQogICAgICB9CiAgICB9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CountBox.vue"], "names": [], "mappings": ";IA+DI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;MAEd,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;QAEP,CAAC,CAAC,CAAC,CAAC,EAAE;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACd;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;UAEnB,CAAC,EAAE;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACnB;;UAEA,CAAC,CAAC,CAAC,CAAC,EAAE;YACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAClB;QACF;MACF;;MAEA,CAAC,EAAE;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACpB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACxE,CAAC,EAAE;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnB;MACF;IACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CountBox.vue", "sourceRoot": "", "sourcesContent": ["<script>\n\nexport default {\n  name: 'CountB<PERSON>',\n\n  props: {\n    name: {\n      type:     String,\n      required: true\n    },\n    count: {\n      type:     Number,\n      required: true\n    },\n    primaryColorVar: {\n      type:     String,\n      required: true\n    },\n    compact: {\n      type:    Boolean,\n      default: false\n    }\n  },\n  computed: {\n    sideStyle() {\n      return `border-left: 9px solid ${ this.customizePrimaryColorOpacity(1) };`;\n    },\n\n    mainStyle() {\n      return `border-color: ${ this.customizePrimaryColorOpacity(0.25) };`;\n    }\n  },\n\n  methods: {\n    customizePrimaryColorOpacity(opacity) {\n      return `rgba(var(${ this.primaryColorVar }), ${ opacity })`;\n    }\n  }\n};\n</script>\n\n<template>\n  <div\n    class=\"count-container\"\n    :style=\"sideStyle\"\n  >\n    <div\n      class=\"count\"\n      :primary-color-var=\"primaryColorVar\"\n      :style=\"mainStyle\"\n    >\n      <div\n        class=\"data\"\n        :class=\"{ 'compact': compact }\"\n      >\n        <h1>{{ count }}</h1>\n        <label>{{ name }}</label>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n    .count {\n      $padding: 10px;\n\n      padding: $padding;\n      position: relative;\n      display: flex;\n      flex-direction: row;\n      align-items: center;\n      border-width: 2px;\n      border-style: solid;\n      border-left: 0;\n\n      .data {\n        display: flex;\n        flex-direction: column;\n        flex: 1;\n\n        label {\n          opacity: 0.7;\n        }\n\n        &.compact {\n          align-items: center;\n          flex-direction: row;\n\n          h1 {\n            margin-bottom: 0;\n            padding-bottom: 0;\n          }\n\n          label {\n            margin-left: 5px;\n          }\n        }\n      }\n\n      h1 {\n        font-size: 40px;\n        line-height: 36px;\n        padding-bottom: math.div($padding, 2);\n        margin-bottom: 5px;\n      }\n\n      @media only screen and (min-width: map-get($breakpoints, '--viewport-7')) {\n        h1 {\n          font-size: 40px;\n          line-height: 36px;\n        }\n      }\n    }\n</style>\n"]}]}