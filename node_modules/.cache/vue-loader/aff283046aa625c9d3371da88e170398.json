{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CommunityLinks.vue?vue&type=style&index=0&id=21aeb193&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CommunityLinks.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgaDIgewogICAgZGlzcGxheTogZmxleDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CgogICAgaSB7CiAgICAgIGZvbnQtc2l6ZTogMTJweDsKICAgICAgbWFyZ2luLWxlZnQ6IDVweDsKICAgIH0KICB9CiAgLnN1cHBvcnQtbGluazpub3QoOmxhc3QtY2hpbGQpIHsKICAgIG1hcmdpbi1ib3R0b206IDE1cHg7CiAgfQoKICAud2VjaGF0LW1vZGFsIHsKICAgIG1hcmdpbjogNjBweDsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICB9CgogIC5saW5rIHsKICAgIGN1cnNvcjogcG9pbnRlcjsKICB9CgogIC5idG4gewogICAgbWFyZ2luOiAyMHB4IGF1dG8gMDsKICB9CgogIC5xci1pbWcgewogICAgYmFja2dyb3VuZC1pbWFnZTogdXJsKCcuLi9hc3NldHMvaW1hZ2VzL3dlY2hhdC1xci1jb2RlLmpwZycpOwogICAgYmFja2dyb3VuZC1yZXBlYXQ6IG5vLXJlcGVhdDsKICAgIGJhY2tncm91bmQtc2l6ZTogY292ZXI7CiAgICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiBjZW50ZXIgY2VudGVyOwogICAgaGVpZ2h0OiAxMjhweDsKICAgIHdpZHRoOiAxMjhweDsKICAgIG1hcmdpbjogMTVweCBhdXRvIDEwcHg7CiAgfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CommunityLinks.vue"], "names": [], "mappings": ";EAkLE,CAAC,EAAE;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEnB,EAAE;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB;EACF;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB;;EAEA,CAAC,CAAC,CAAC,EAAE;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACxB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CommunityLinks.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport SimpleBox from '@shell/components/SimpleBox';\nimport AppModal from '@shell/components/AppModal.vue';\nimport Closeable from '@shell/mixins/closeable';\nimport { MANAGEMENT } from '@shell/config/types';\nimport { SETTING } from '@shell/config/settings';\nimport { mapGetters } from 'vuex';\nimport { isRancherPrime } from '@shell/config/version';\nimport { fetchLinks } from '@shell/config/home-links';\nimport { processLink } from '@shell/plugins/clean-html';\n\n// i18n-ignore footer.wechat.title, footer.wechat.modalText, footer.wechat.modalText2\nexport default {\n  name: 'CommunityLinks',\n\n  components: { SimpleBox, AppModal },\n\n  props: {\n    linkOptions: {\n      type:    Object,\n      default: () => {\n        return {};\n      },\n    },\n    isSupportPage: {\n      type:    Boolean,\n      default: false,\n    },\n  },\n\n  mixins: [Closeable],\n\n  async fetch() {\n    this.links = await fetchLinks(this.$store, this.hasSupport, this.isSupportPage, (str) => this.t(str));\n  },\n\n  data() {\n    return { links: {}, showWeChatModal: false };\n  },\n\n  computed: {\n    ...mapGetters('i18n', [\n      'selectedLocaleLabel'\n    ]),\n\n    hasOptions() {\n      return !!Object.keys(this.options).length || !!Object.keys(this.$slots).length;\n    },\n\n    hasSupport() {\n      return isRancherPrime() || this.$store.getters['management/byId'](MANAGEMENT.SETTING, SETTING.SUPPORTED )?.value === 'true';\n    },\n\n    options() {\n      // Use linkOptions if provided - used by Harvester\n      if (this.linkOptions && Object.keys(this.linkOptions).length) {\n        const options = [];\n\n        Object.keys(this.linkOptions).forEach((key) => {\n          options.push({\n            key,\n            label: this.t(key),\n            value: this.linkOptions[key]\n          });\n        });\n\n        return options;\n      }\n\n      // Combine the links\n      const all = [];\n\n      if (this.links.custom) {\n        all.push(...this.links.custom);\n      }\n\n      if (this.links.defaults) {\n        all.push(...this.links.defaults.filter((link) => link.enabled));\n      }\n\n      // Process the links\n      return all.map((item) => ({\n        ...item,\n        value: processLink(item.value)\n      }));\n    }\n  },\n  methods: {\n    show() {\n      this.showWeChatModal = true;\n    },\n    close() {\n      this.showWeChatModal = false;\n    }\n  },\n};\n</script>\n\n<template>\n  <div v-if=\"hasOptions\">\n    <SimpleBox\n      :pref=\"pref\"\n      :pref-key=\"prefKey\"\n    >\n      <template #title>\n        <h2>\n          {{ t('customLinks.displayTitle') }}\n        </h2>\n      </template>\n      <div\n        v-for=\"(link, i) in options\"\n        :key=\"i\"\n        class=\"support-link\"\n      >\n        <router-link\n          v-if=\"link.value.startsWith('/') \"\n          :to=\"link.value\"\n          role=\"link\"\n          :aria-label=\"link.label\"\n        >\n          {{ link.label }}\n        </router-link>\n        <a\n          v-else\n          :href=\"link.value\"\n          rel=\"noopener noreferrer nofollow\"\n          target=\"_blank\"\n          role=\"link\"\n          :aria-label=\"link.label\"\n        > {{ link.label }} </a>\n      </div>\n      <slot />\n      <div\n        v-if=\"selectedLocaleLabel === t('locale.zh-hans')\"\n        class=\"support-link\"\n      >\n        <a\n          class=\"link\"\n          tabindex=\"0\"\n          :aria-label=\"t('footer.wechat.title')\"\n          role=\"link\"\n          @click=\"show\"\n          @keyup.enter=\"show\"\n        >\n          {{ t('footer.wechat.title') }}\n        </a>\n      </div>\n    </SimpleBox>\n    <app-modal\n      v-if=\"showWeChatModal\"\n      name=\"wechat-modal\"\n      height=\"auto\"\n      :width=\"640\"\n      @close=\"close\"\n    >\n      <div class=\"wechat-modal\">\n        <h1>{{ t('footer.wechat.modalText') }}</h1>\n        <h1>{{ t('footer.wechat.modalText2') }}</h1>\n        <div class=\"qr-img\" />\n        <div>\n          <button\n            class=\"btn role-primary\"\n            tabindex=\"0\"\n            :aria-label=\"t('generic.close')\"\n            role=\"button\"\n            @click=\"close\"\n            @keyup.enter=\"close\"\n            @keyup.space=\"close\"\n          >\n            {{ t('generic.close') }}\n          </button>\n        </div>\n      </div>\n    </app-modal>\n  </div>\n</template>\n\n<style lang='scss' scoped>\n  h2 {\n    display: flex;\n    align-items: center;\n\n    i {\n      font-size: 12px;\n      margin-left: 5px;\n    }\n  }\n  .support-link:not(:last-child) {\n    margin-bottom: 15px;\n  }\n\n  .wechat-modal {\n    margin: 60px;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n  }\n\n  .link {\n    cursor: pointer;\n  }\n\n  .btn {\n    margin: 20px auto 0;\n  }\n\n  .qr-img {\n    background-image: url('../assets/images/wechat-qr-code.jpg');\n    background-repeat: no-repeat;\n    background-size: cover;\n    background-position: center center;\n    height: 128px;\n    width: 128px;\n    margin: 15px auto 10px;\n  }\n</style>\n"]}]}