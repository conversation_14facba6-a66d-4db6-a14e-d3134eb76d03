{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DetailTop.vue?vue&type=template&id=0388315e", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DetailTop.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DetailTop.vue"], "names": [], "mappings": ";EA+KE,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1B;IACE,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf;MACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B;UACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB;MACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB;QACE,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf;MACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX;UACE,CAAC;YACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC;UACD,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;UAClD;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC;UACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B;UACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAClF,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB;MACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACN,CAAC;QACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC;QACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACjI,CAAC,CAAC,CAAC;MACH,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;EACH,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DetailTop.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport Tag from '@shell/components/Tag';\nimport isEmpty from 'lodash/isEmpty';\nimport DetailText from '@shell/components/DetailText';\nimport { _VIEW } from '@shell/config/query-params';\nimport { ExtensionPoint, PanelLocation } from '@shell/core/types';\nimport ExtensionPanel from '@shell/components/ExtensionPanel';\n\nexport default {\n  components: {\n    DetailText, Tag, ExtensionPanel\n  },\n\n  props: {\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n\n    moreDetails: {\n      type:    Array,\n      default: () => {\n        return [];\n      }\n    },\n\n    /**\n     * Optionally replace key/value and display tooltips for the tab\n     * Dictionary key based\n     */\n    tooltips: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n\n    /**\n     * Optionally display icons next to the tab\n     * Dictionary key based\n     */\n    icons: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  data() {\n    return {\n      extensionType:      ExtensionPoint.PANEL,\n      extensionLocation:  PanelLocation.DETAIL_TOP,\n      annotationsVisible: false,\n      showAllLabels:      false,\n      view:               _VIEW\n    };\n  },\n\n  computed: {\n    namespaces() {\n      return (this.value?.namespaces || []).map((namespace) => {\n        return {\n          name:           namespace?.metadata?.name,\n          detailLocation: namespace.detailLocation\n        };\n      });\n    },\n    details() {\n      const items = [\n        ...(this.moreDetails || []),\n        ...(this.value?.details || []),\n      ].filter((x) => x.separator || (!!`${ x.content }` && x.content !== undefined && x.content !== null));\n\n      const groups = [];\n      let currentGroup = [];\n\n      items.forEach((i) => {\n        if (i.separator) {\n          groups.push(currentGroup);\n          currentGroup = [];\n        } else {\n          currentGroup.push(i);\n        }\n      });\n\n      if (currentGroup.length) {\n        groups.push(currentGroup);\n      }\n\n      return groups;\n    },\n\n    labels() {\n      if (this.showAllLabels || !this.showFilteredSystemLabels) {\n        return this.value?.labels || {};\n      }\n\n      return this.value?.filteredSystemLabels;\n    },\n\n    internalTooltips() {\n      return this.value?.detailTopTooltips || this.tooltips;\n    },\n\n    internalIcons() {\n      return this.value?.detailTopIcons || this.icons;\n    },\n\n    annotations() {\n      return this.value?.annotations || {};\n    },\n\n    description() {\n      return this.value?.description;\n    },\n\n    hasDetails() {\n      return !isEmpty(this.details);\n    },\n\n    hasLabels() {\n      return !isEmpty(this.labels);\n    },\n\n    hasAnnotations() {\n      return !isEmpty(this.annotations);\n    },\n\n    hasDescription() {\n      return !isEmpty(this.description);\n    },\n\n    hasNamespaces() {\n      return !isEmpty(this.namespaces);\n    },\n\n    annotationCount() {\n      return Object.keys(this.annotations || {}).length;\n    },\n\n    isEmpty() {\n      const hasAnything = this.hasDetails || this.hasLabels || this.hasAnnotations || this.hasDescription || this.hasNamespaces;\n\n      return !hasAnything;\n    },\n\n    showFilteredSystemLabels() {\n      // It would be nicer to use hasSystemLabels here, but not all places have implemented it\n      // Instead check that there's a discrepancy between all labels and all labels without system ones\n      if (this.value?.labels && this.value?.filteredSystemLabels) {\n        const labelCount = Object.keys(this.value.labels).length;\n        const filteredSystemLabelsCount = Object.keys(this.value.filteredSystemLabels).length;\n\n        return labelCount !== filteredSystemLabelsCount;\n      }\n\n      return false;\n    },\n  },\n  methods: {\n    toggleLabels() {\n      this.showAllLabels = !this.showAllLabels;\n    },\n\n    toggleAnnotations(ev) {\n      this.annotationsVisible = !this.annotationsVisible;\n    }\n  }\n};\n</script>\n\n<template>\n  <div\n    class=\"detail-top\"\n    :class=\"{empty: isEmpty}\"\n  >\n    <div\n      v-if=\"hasNamespaces\"\n      class=\"labels\"\n    >\n      <span class=\"label\">\n        {{ t('resourceDetail.detailTop.namespaces') }}:\n      </span>\n      <span>\n        <router-link\n          v-for=\"namespace in namespaces\"\n          :key=\"namespace.name\"\n          :to=\"namespace.detailLocation\"\n          class=\"namespaceLinkList\"\n        >\n          {{ namespace.name }}\n        </router-link>\n      </span>\n    </div>\n\n    <div\n      v-if=\"description\"\n      class=\"description\"\n    >\n      <span class=\"label\">\n        {{ t('resourceDetail.detailTop.description') }}:\n      </span>\n      <span class=\"content\">{{ description }}</span>\n    </div>\n\n    <div v-if=\"hasDetails\">\n      <div\n        v-for=\"group, index in details\"\n        :key=\"index\"\n        class=\"details\"\n      >\n        <div\n          v-for=\"(detail, i) in group\"\n          :key=\"i\"\n          class=\"detail\"\n        >\n          <span class=\"label\">\n            {{ detail.label }}:\n          </span>\n          <component\n            :is=\"detail.formatter\"\n            v-if=\"detail.formatter\"\n            :value=\"detail.content\"\n            v-bind=\"detail.formatterOpts\"\n          />\n          <span v-else>{{ detail.content }}</span>\n        </div>\n      </div>\n    </div>\n\n    <div\n      v-if=\"hasLabels\"\n      class=\"labels\"\n    >\n      <div class=\"tags\">\n        <span class=\"label\">\n          {{ t('resourceDetail.detailTop.labels') }}:\n        </span>\n        <Tag\n          v-for=\"(prop, key) in labels\"\n          :key=\"key\"\n        >\n          <i\n            v-if=\"internalIcons[key]\"\n            class=\"icon\"\n            :class=\"internalIcons[key]\"\n          />\n          <span\n            v-if=\"internalTooltips[key]\"\n            v-clean-tooltip=\"prop ? `${key} : ${prop}` : key\"\n          >\n            <span>{{ internalTooltips[key] ? internalTooltips[key] : key }}</span>\n            <span v-if=\"showAllLabels\">: {{ key }}</span>\n          </span>\n          <span v-else>{{ prop ? `${key} : ${prop}` : key }}</span>\n        </Tag>\n        <a\n          v-if=\"showFilteredSystemLabels\"\n          href=\"#\"\n          class=\"detail-top__label-button\"\n          @click.prevent=\"toggleLabels\"\n        >\n          {{ t(`resourceDetail.detailTop.${showAllLabels? 'hideLabels' : 'showLabels'}`) }}\n        </a>\n      </div>\n    </div>\n\n    <div\n      v-if=\"hasAnnotations\"\n      class=\"annotations\"\n    >\n      <span class=\"label\">\n        {{ t('resourceDetail.detailTop.annotations') }}:\n      </span>\n      <a\n        href=\"#\"\n        @click.prevent=\"toggleAnnotations\"\n      >\n        {{ t(`resourceDetail.detailTop.${annotationsVisible? 'hideAnnotations' : 'showAnnotations'}`, {annotations: annotationCount}) }}\n      </a>\n      <div v-if=\"annotationsVisible\">\n        <DetailText\n          v-for=\"(val, key) in annotations\"\n          :key=\"key\"\n          class=\"annotation\"\n          :value=\"val\"\n          :label=\"key\"\n        />\n      </div>\n    </div>\n\n    <!-- Extensions area -->\n    <ExtensionPanel\n      :resource=\"value\"\n      :type=\"extensionType\"\n      :location=\"extensionLocation\"\n    />\n  </div>\n</template>\n\n<style lang=\"scss\">\n  .detail-top {\n    $spacing: 4px;\n\n    &:not(.empty) {\n      // Flip of .masthead padding/margin\n      padding-top: 10px;\n      border-top: 1px solid var(--border);\n      margin-top: 10px;\n    }\n\n    .namespaceLinkList:not(:first-child):before {\n      content: \", \";\n    }\n\n    .tags {\n      display: inline-flex;\n      flex-direction: row;\n      flex-wrap: wrap;\n      position: relative;\n      top: $spacing * math.div(-1, 2);\n\n      .label {\n        position: relative;\n        top: $spacing;\n      }\n\n      .tag {\n        margin: math.div($spacing, 2) $spacing 0 math.div($spacing, 2);\n        font-size: 12px;\n      }\n    }\n\n    .annotation {\n      margin-top: 10px;\n    }\n\n    .label {\n      color: var(--input-label);\n      margin: 0 4px 0 0;\n    }\n\n    &__label-button {\n      padding: 4px;\n    }\n\n    .details {\n      display: flex;\n      flex-direction: row;\n      flex-wrap: wrap;\n\n      .detail {\n        margin-right: 20px;\n        margin-bottom: 3px;\n      }\n      &:not(:first-of-type) {\n        margin-top: 3px;\n      }\n    }\n\n    & > div {\n      &:not(:last-of-type) {\n        margin-bottom: $spacing;\n      }\n    }\n\n    .icon {\n      vertical-align: top;\n    }\n  }\n</style>\n"]}]}