{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Collapse.vue?vue&type=style&index=0&id=5f2401b0&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Collapse.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5hZHZhbmNlZCB7CiAgdXNlci1zZWxlY3Q6IG5vbmU7CiAgY3Vyc29yOiBwb2ludGVyOwogIGxpbmUtaGVpZ2h0OiA0MHB4OwogIGZvbnQtc2l6ZTogMTVweDsKICBmb250LXdlaWdodDogNTAwOwoKICAuZGlzYWJsZWQgewogICAgY3Vyc29yOiBub3QtYWxsb3dlZDsKICB9Cn0KLmNvbnRlbnQgewogIGJhY2tncm91bmQ6IHZhcigtLW5hdi1hY3RpdmUpOwogIHBhZGRpbmc6IDEwcHg7CiAgbWFyZ2luLXRvcDogNnB4OwogIGJvcmRlci1yYWRpdXM6IDRweDsKfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Collapse.vue"], "names": [], "mappings": ";AAqEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB;AACF;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACpB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Collapse.vue", "sourceRoot": "", "sourcesContent": ["<script>\nexport default {\n  name: 'Collapse',\n\n  emits: ['update:open'],\n\n  props: {\n    open: {\n      type:    Boolean,\n      default: true\n    },\n\n    title: {\n      type:    String,\n      default: ''\n    },\n\n    isDisabled: {\n      type:    Boolean,\n      default: false\n    },\n  },\n\n  methods: {\n    showAdvanced() {\n      this.$emit('update:open', !this.open);\n    }\n  }\n};\n</script>\n\n<template>\n  <div\n    class=\"collapse\"\n    :class=\"{ 'disabled': isDisabled }\"\n  >\n    <slot name=\"title\">\n      <div\n        class=\"advanced text-link\"\n        :class=\"{ 'disabled': isDisabled }\"\n        :disabled=\"isDisabled\"\n        data-testid=\"collapse-div\"\n        @click=\"showAdvanced\"\n      >\n        <i\n          v-if=\"open\"\n          class=\"icon icon-chevron-down\"\n          data-testid=\"collapse-icon-down\"\n        />\n        <i\n          v-else\n          class=\"icon icon-chevron-right\"\n          data-testid=\"collapse-icon-right\"\n        />\n        {{ title }}\n      </div>\n    </slot>\n\n    <div\n      v-if=\"open\"\n      class=\"content\"\n      data-testid=\"collapse-content\"\n    >\n      <slot />\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.advanced {\n  user-select: none;\n  cursor: pointer;\n  line-height: 40px;\n  font-size: 15px;\n  font-weight: 500;\n\n  .disabled {\n    cursor: not-allowed;\n  }\n}\n.content {\n  background: var(--nav-active);\n  padding: 10px;\n  margin-top: 6px;\n  border-radius: 4px;\n}\n</style>\n"]}]}