{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CreateDriver.vue?vue&type=template&id=d2e97572", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CreateDriver.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdj4KICAgIDxkaXYgY2xhc3M9InJvdyBtYi0yMCI+CiAgICAgIDxMYWJlbGVkSW5wdXQKICAgICAgICB2LW1vZGVsOnZhbHVlLnRyaW09InZhbHVlLnVybCIKICAgICAgICA6bGFiZWw9InQoJ2RyaXZlcnMuYWRkLmRvd25sb2FkVXJsLmxhYmVsJykiCiAgICAgICAgOnBsYWNlaG9sZGVyPSJ0KCdkcml2ZXJzLmFkZC5kb3dubG9hZFVybC5wbGFjZWhvbGRlcicsIG51bGwsIHRydWUpIgogICAgICAgIDp0b29sdGlwPSJ0KCdkcml2ZXJzLmFkZC5kb3dubG9hZFVybC50b29sdGlwJywgbnVsbCwgdHJ1ZSkiCiAgICAgICAgOm1vZGU9Im1vZGUiCiAgICAgICAgOnJ1bGVzPSJydWxlcy51cmwiCiAgICAgICAgOmRhdGEtdGVzdGlkPSInZHJpdmVyLWNyZWF0ZS11cmwtZmllbGQnIgogICAgICAgIHJlcXVpcmVkCiAgICAgIC8+CiAgICA8L2Rpdj4KICAgIDxkaXYgY2xhc3M9InJvdyBtYi0yMCI+CiAgICAgIDxMYWJlbGVkSW5wdXQKICAgICAgICB2LW1vZGVsOnZhbHVlLnRyaW09InZhbHVlLmNoZWNrc3VtIgogICAgICAgIDpsYWJlbD0idCgnZHJpdmVycy5hZGQuY2hlY2tzdW0ubGFiZWwnKSIKICAgICAgICA6dG9vbHRpcD0idCgnZHJpdmVycy5hZGQuY2hlY2tzdW0udG9vbHRpcCcsIG51bGwsIHRydWUpIgogICAgICAgIDptb2RlPSJtb2RlIgogICAgICAgIDpydWxlcz0icnVsZXMuY2hlY2tzdW0iCiAgICAgICAgOmRhdGEtdGVzdGlkPSInZHJpdmVyLWNyZWF0ZS1jaGVja3N1bS1maWVsZCciCiAgICAgIC8+CiAgICA8L2Rpdj4KICAgIDxkaXYgY2xhc3M9InJvdyBtYi0yMCI+CiAgICAgIDxMYWJlbGVkSW5wdXQKICAgICAgICB2LW1vZGVsOnZhbHVlLnRyaW09InZhbHVlLnVpVXJsIgogICAgICAgIDpsYWJlbD0idCgnZHJpdmVycy5hZGQuY3VzdG9tVWlVcmwubGFiZWwnKSIKICAgICAgICA6dG9vbHRpcD0idCgnZHJpdmVycy5hZGQuY3VzdG9tVWlVcmwudG9vbHRpcCcsIG51bGwsIHRydWUpIgogICAgICAgIDpwbGFjZWhvbGRlcj0idCgnZHJpdmVycy5hZGQuY3VzdG9tVWlVcmwucGxhY2Vob2xkZXInLCBudWxsLCB0cnVlKSIKICAgICAgICA6bW9kZT0ibW9kZSIKICAgICAgICA6cnVsZXM9InJ1bGVzLnVpVXJsIgogICAgICAgIDpkYXRhLXRlc3RpZD0iJ2RyaXZlci1jcmVhdGUtdWl1cmwtZmllbGQnIgogICAgICAvPgogICAgPC9kaXY+CiAgICA8ZGl2IGNsYXNzPSJjb2wgc3Bhbi02Ij4KICAgICAgPEFycmF5TGlzdAogICAgICAgIHYtbW9kZWw6dmFsdWU9InZhbHVlLndoaXRlbGlzdERvbWFpbnMiCiAgICAgICAgOnByb3RpcD0iZmFsc2UiCiAgICAgICAgOm1vZGU9Im1vZGUiCiAgICAgICAgOnRpdGxlPSJ0KCdkcml2ZXJzLmFkZC53aGl0ZWxpc3QnKSIKICAgICAgICA6cnVsZXM9InJ1bGVzLndoaXRlbGlzdERvbWFpbnMiCiAgICAgICAgOmRhdGEtdGVzdGlkPSInZHJpdmVyLWNyZWF0ZS13aGl0ZWxpc3QtbGlzdCciCiAgICAgIC8+CiAgICA8L2Rpdj4KICA8L2Rpdj4K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CreateDriver.vue"], "names": [], "mappings": ";EAmCE,CAAC,CAAC,CAAC,CAAC;IACF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CA<PERSON>,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3C,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CreateDriver.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport ArrayList from '@shell/components/form/ArrayList';\nexport default {\n  name: 'DriverCreate',\n\n  components: {\n    LabeledInput,\n    ArrayList\n  },\n  props: {\n    mode: {\n      type:     String,\n      required: true,\n    },\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    rules: {\n      default: () => ({\n        url:              [],\n        uiUrl:            [],\n        checksum:         [],\n        whitelistDomains: []\n      }),\n      type: Object,\n    }\n  }\n};\n</script>\n\n<template>\n  <div>\n    <div class=\"row mb-20\">\n      <LabeledInput\n        v-model:value.trim=\"value.url\"\n        :label=\"t('drivers.add.downloadUrl.label')\"\n        :placeholder=\"t('drivers.add.downloadUrl.placeholder', null, true)\"\n        :tooltip=\"t('drivers.add.downloadUrl.tooltip', null, true)\"\n        :mode=\"mode\"\n        :rules=\"rules.url\"\n        :data-testid=\"'driver-create-url-field'\"\n        required\n      />\n    </div>\n    <div class=\"row mb-20\">\n      <LabeledInput\n        v-model:value.trim=\"value.checksum\"\n        :label=\"t('drivers.add.checksum.label')\"\n        :tooltip=\"t('drivers.add.checksum.tooltip', null, true)\"\n        :mode=\"mode\"\n        :rules=\"rules.checksum\"\n        :data-testid=\"'driver-create-checksum-field'\"\n      />\n    </div>\n    <div class=\"row mb-20\">\n      <LabeledInput\n        v-model:value.trim=\"value.uiUrl\"\n        :label=\"t('drivers.add.customUiUrl.label')\"\n        :tooltip=\"t('drivers.add.customUiUrl.tooltip', null, true)\"\n        :placeholder=\"t('drivers.add.customUiUrl.placeholder', null, true)\"\n        :mode=\"mode\"\n        :rules=\"rules.uiUrl\"\n        :data-testid=\"'driver-create-uiurl-field'\"\n      />\n    </div>\n    <div class=\"col span-6\">\n      <ArrayList\n        v-model:value=\"value.whitelistDomains\"\n        :protip=\"false\"\n        :mode=\"mode\"\n        :title=\"t('drivers.add.whitelist')\"\n        :rules=\"rules.whitelistDomains\"\n        :data-testid=\"'driver-create-whitelist-list'\"\n      />\n    </div>\n  </div>\n</template>\n"]}]}