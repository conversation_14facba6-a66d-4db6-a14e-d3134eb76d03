{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AuthBanner.vue?vue&type=template&id=0e8d2270&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AuthBanner.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdj4KICAgIDxCYW5uZXIKICAgICAgY29sb3I9InN1Y2Nlc3MgY2xlYXJmaXgiCiAgICAgIGNsYXNzPSJiYW5uZXIiCiAgICA+CiAgICAgIDxkaXYgY2xhc3M9InRleHQiPgogICAgICAgIHt7IHQoJ2F1dGhDb25maWcuc3RhdGVCYW5uZXIuZW5hYmxlZCcsIHRBcmdzKSB9fQogICAgICA8L2Rpdj4KICAgICAgPHNsb3QgbmFtZT0iYWN0aW9ucyIgLz4KICAgICAgPGJ1dHRvbgogICAgICAgIHR5cGU9ImJ1dHRvbiIKICAgICAgICBjbGFzcz0iYnRuLXNtIHJvbGUtcHJpbWFyeSIKICAgICAgICBAY2xpY2s9ImVkaXQiCiAgICAgID4KICAgICAgICB7eyB0KCdhY3Rpb24uZWRpdCcpIH19CiAgICAgIDwvYnV0dG9uPgogICAgICA8YnV0dG9uCiAgICAgICAgdHlwZT0iYnV0dG9uIgogICAgICAgIGNsYXNzPSJtbC0xMCBidG4tc20gcm9sZS1wcmltYXJ5IGJnLWVycm9yIgogICAgICAgIEBjbGljaz0ic2hvd0Rpc2FibGVNb2RhbCIKICAgICAgPgogICAgICAgIHt7IHQoJ2dlbmVyaWMuZGlzYWJsZScpIH19CiAgICAgIDwvYnV0dG9uPgogICAgPC9CYW5uZXI+CgogICAgPHRhYmxlCiAgICAgIHYtaWY9IiEhJHNsb3RzLnJvd3MiCiAgICAgIGNsYXNzPSJ2YWx1ZXMiCiAgICA+CiAgICAgIDxzbG90IG5hbWU9InJvd3MiIC8+CiAgICA8L3RhYmxlPgoKICAgIDxzbG90CiAgICAgIHYtaWY9IiRzbG90cy5mb290ZXIiCiAgICAgIG5hbWU9ImZvb3RlciIKICAgIC8+CgogICAgPERpc2FibGVBdXRoUHJvdmlkZXJNb2RhbAogICAgICByZWY9ImRpc2FibGVBdXRoUHJvdmlkZXJNb2RhbCIKICAgICAgQGRpc2FibGU9ImRpc2FibGUiCiAgICAvPgogIDwvZGl2Pgo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AuthBanner.vue"], "names": [], "mappings": ";EA4CE,CAAC,CAAC,CAAC,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf;MACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACjD,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd;QACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B;QACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAER,CAAC,CAAC,CAAC,CAAC,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf;MACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEP,CAAC,CAAC,CAAC,CAAC;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;EACH,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AuthBanner.vue", "sourceRoot": "", "sourcesContent": ["\n<script>\nimport { Banner } from '@components/Banner';\nimport DisableAuthProviderModal from '@shell/components/DisableAuthProviderModal';\n\nexport default {\n  components: {\n    Banner,\n    DisableAuthProviderModal\n  },\n\n  props: {\n    tArgs: {\n      type:     Object,\n      required: true,\n      default:  () => { },\n    },\n    disable: {\n      type:     Function,\n      required: true,\n      default:  () => { },\n    },\n    edit: {\n      type:     Function,\n      required: true,\n      default:  () => { },\n    }\n  },\n\n  computed: {\n    values() {\n      return Object.entries(this.table);\n    }\n  },\n\n  methods: {\n    showDisableModal() {\n      this.$refs.disableAuthProviderModal.show();\n    }\n  },\n};\n</script>\n\n<template>\n  <div>\n    <Banner\n      color=\"success clearfix\"\n      class=\"banner\"\n    >\n      <div class=\"text\">\n        {{ t('authConfig.stateBanner.enabled', tArgs) }}\n      </div>\n      <slot name=\"actions\" />\n      <button\n        type=\"button\"\n        class=\"btn-sm role-primary\"\n        @click=\"edit\"\n      >\n        {{ t('action.edit') }}\n      </button>\n      <button\n        type=\"button\"\n        class=\"ml-10 btn-sm role-primary bg-error\"\n        @click=\"showDisableModal\"\n      >\n        {{ t('generic.disable') }}\n      </button>\n    </Banner>\n\n    <table\n      v-if=\"!!$slots.rows\"\n      class=\"values\"\n    >\n      <slot name=\"rows\" />\n    </table>\n\n    <slot\n      v-if=\"$slots.footer\"\n      name=\"footer\"\n    />\n\n    <DisableAuthProviderModal\n      ref=\"disableAuthProviderModal\"\n      @disable=\"disable\"\n    />\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.banner {\n  display: flex;\n    align-items: center;\n  .text {\n    flex: 1;\n  }\n}\n\n.values {\n  tr td:not(:first-of-type) {\n    padding-left: 10px;\n  }\n}\n\n</style>\n"]}]}