{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ChartReadme.vue?vue&type=style&index=0&id=29efe5f9&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ChartReadme.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLmNoYXJ0LXJlYWRtZXMgewogICAgJiA+IGgxIHsKICAgICAgIGJvcmRlci10b3A6IHZhcigtLWhlYWRlci1ib3JkZXItc2l6ZSkgc29saWQgdmFyKC0taGVhZGVyLWJvcmRlcik7CiAgICB9CiAgfQogIC5tZCB7CiAgICBvdmVyZmxvdzogYXV0bzsKICAgIG1heC13aWR0aDogMTAwJTsKICAgIGxpbmUtaGVpZ2h0OiAxLjY7CgogICAgOmRlZXAoKSB7CiAgICAgICogKyBIMSwKICAgICAgKiArIEgyLAogICAgICAqICsgSDMsCiAgICAgICogKyBINCwKICAgICAgKiArIEg1LAogICAgICAqICsgSDYgewogICAgICAgIG1hcmdpbi10b3A6IDIwcHg7CiAgICAgIH0KICAgIH0KCiAgICA6ZGVlcCgpIGNvZGUgewogICAgICBmb250LXNpemU6IDEzLjVweDsKICAgICAgd2hpdGUtc3BhY2U6IGJyZWFrLXNwYWNlczsKICAgICAgd29yZC13cmFwOiBicmVhay13b3JkOwogICAgICBwYWRkaW5nLWxlZnQ6IDVweDsKICAgICAgcGFkZGluZy1yaWdodDogNXB4OwogICAgICBib3JkZXI6IDA7CiAgICB9CgogICAgOmRlZXAoKSBwcmUgewogICAgICB3aGl0ZS1zcGFjZTogYnJlYWstc3BhY2VzOwogICAgICB3b3JkLWJyZWFrOiBicmVhay13b3JkOwogICAgfQoKICAgIDpkZWVwKCkgID4gaDE6Zmlyc3Qtb2YtdHlwZSB7CiAgICAgIGRpc3BsYXk6IG5vbmU7CiAgICB9CgogICAgOmRlZXAoKSBwIHsKICAgICAgbWFyZ2luLWJvdHRvbTogMC41ZW07CiAgICB9CiAgfQoK"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ChartReadme.vue"], "names": [], "mappings": ";EAuDE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,EAAE,EAAE,CAAC,EAAE;OACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnE;EACF;EACA,CAAC,CAAC,EAAE;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,EAAE,EAAE,CAAC,CAAC;MACN,EAAE,EAAE,CAAC,CAAC;MACN,EAAE,EAAE,CAAC,CAAC;MACN,EAAE,EAAE,CAAC,CAAC;MACN,EAAE,EAAE,CAAC,CAAC;MACN,EAAE,EAAE,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAClB;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACX;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ChartReadme.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport Markdown from '@shell/components/Markdown';\n\nexport default {\n  components: { Markdown },\n  props:      {\n    versionInfo: {\n      type:     Object,\n      required: true\n    },\n  },\n  data() {\n    return {\n      appReadmeLoaded: false,\n      readmeLoaded:    false,\n    };\n  },\n  computed: {\n    appReadme() {\n      return this.versionInfo?.appReadme || '';\n    },\n    readme() {\n      return this.versionInfo?.readme || '';\n    }\n  },\n};\n</script>\n\n<template>\n  <div class=\"wrapper\">\n    <div class=\"chart-readmes\">\n      <Markdown\n        v-if=\"appReadme\"\n        v-model:value=\"appReadme\"\n        class=\"md md-desc mb-20\"\n        @loaded=\"appReadmeLoaded = true\"\n      />\n      <h1\n        v-if=\"appReadme && readme && appReadmeLoaded && readmeLoaded\"\n        class=\"pt-10\"\n      >\n        {{ t('catalog.install.appReadmeTitle') }}\n      </h1>\n      <Markdown\n        v-if=\"readme\"\n        v-model:value=\"readme\"\n        class=\"md md-desc\"\n        @loaded=\"readmeLoaded = true\"\n      />\n    </div>\n    <div />\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .chart-readmes {\n    & > h1 {\n       border-top: var(--header-border-size) solid var(--header-border);\n    }\n  }\n  .md {\n    overflow: auto;\n    max-width: 100%;\n    line-height: 1.6;\n\n    :deep() {\n      * + H1,\n      * + H2,\n      * + H3,\n      * + H4,\n      * + H5,\n      * + H6 {\n        margin-top: 20px;\n      }\n    }\n\n    :deep() code {\n      font-size: 13.5px;\n      white-space: break-spaces;\n      word-wrap: break-word;\n      padding-left: 5px;\n      padding-right: 5px;\n      border: 0;\n    }\n\n    :deep() pre {\n      white-space: break-spaces;\n      word-break: break-word;\n    }\n\n    :deep()  > h1:first-of-type {\n      display: none;\n    }\n\n    :deep() p {\n      margin-bottom: 0.5em;\n    }\n  }\n\n</style>\n"]}]}