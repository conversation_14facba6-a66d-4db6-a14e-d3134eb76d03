{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/Namespace.vue?vue&type=template&id=066e7924&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/Namespace.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdj4KICAgIDxkaXYgY2xhc3M9ImhlYWRlcnMgbWItMTAiPgogICAgICA8ZGl2IGNsYXNzPSJtci0xMCI+CiAgICAgICAgPGxhYmVsPnt7IHQoJ3Jlc291cmNlUXVvdGEuaGVhZGVycy5yZXNvdXJjZVR5cGUnKSB9fTwvbGFiZWw+CiAgICAgIDwvZGl2PgogICAgICA8ZGl2IGNsYXNzPSJtci0xMCI+CiAgICAgICAgPGxhYmVsPnt7IHQoJ3Jlc291cmNlUXVvdGEuaGVhZGVycy5wcm9qZWN0UmVzb3VyY2VBdmFpbGFiaWxpdHknKSB9fTwvbGFiZWw+CiAgICAgIDwvZGl2PgogICAgICA8ZGl2IGNsYXNzPSJtci0yMCI+CiAgICAgICAgPGxhYmVsPnt7IHQoJ3Jlc291cmNlUXVvdGEuaGVhZGVycy5saW1pdCcpIH19PC9sYWJlbD4KICAgICAgPC9kaXY+CiAgICA8L2Rpdj4KICAgIDxSb3cKICAgICAgdi1mb3I9IihsaW1pdCwgaSkgaW4gZWRpdGFibGVMaW1pdHMiCiAgICAgIDprZXk9ImkiCiAgICAgIDp2YWx1ZT0idmFsdWUucmVzb3VyY2VRdW90YSIKICAgICAgOm5hbWVzcGFjZT0idmFsdWUiCiAgICAgIDptb2RlPSJtb2RlIgogICAgICA6dHlwZXM9Im1hcHBlZFR5cGVzIgogICAgICA6dHlwZT0ibGltaXQiCiAgICAgIDpwcm9qZWN0LXJlc291cmNlLXF1b3RhLWxpbWl0cz0icHJvamVjdFJlc291cmNlUXVvdGFMaW1pdHMiCiAgICAgIDpkZWZhdWx0LXJlc291cmNlLXF1b3RhLWxpbWl0cz0iZGVmYXVsdFJlc291cmNlUXVvdGFMaW1pdHMiCiAgICAgIDpuYW1lc3BhY2UtcmVzb3VyY2UtcXVvdGEtbGltaXRzPSJuYW1lc3BhY2VSZXNvdXJjZVF1b3RhTGltaXRzIgogICAgICBAdXBkYXRlOnZhbHVlPSJ1cGRhdGUiCiAgICAvPgogIDwvZGl2Pgo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/Namespace.vue"], "names": [], "mappings": ";EAwEE,CAAC,CAAC,CAAC,CAAC;IACF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7D,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5E,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtD,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC;EACH,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/Namespace.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport Row from './NamespaceRow';\nimport { QUOTA_COMPUTED } from './shared';\n\nexport default {\n  components: { Row },\n\n  props: {\n    mode: {\n      type:     String,\n      required: true,\n    },\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    project: {\n      type:     Object,\n      required: true\n    },\n    types: {\n      type:    Array,\n      default: () => {\n        return [];\n      }\n    }\n  },\n\n  data() {\n    return { rows: {} };\n  },\n\n  computed: {\n    ...QUOTA_COMPUTED,\n    projectResourceQuotaLimits() {\n      return this.project?.spec?.resourceQuota?.limit || {};\n    },\n    namespaceResourceQuotaLimits() {\n      return this.project.namespaces.map((namespace) => ({\n        ...namespace.resourceQuota.limit,\n        id: namespace.id\n      }));\n    },\n    editableLimits() {\n      return Object.keys(this.projectResourceQuotaLimits);\n    },\n    defaultResourceQuotaLimits() {\n      return this.project.spec.namespaceDefaultResourceQuota.limit;\n    }\n  },\n\n  methods: {\n    remainingTypes(currentType) {\n      return this.mappedTypes\n        .filter((type) => !this.types.includes(type.value) || type.value === currentType);\n    },\n    update(key, value) {\n      const resourceQuota = {\n        limit: {\n          ...this.value.resourceQuota.limit,\n          [key]: value\n        }\n      };\n\n      this.value['resourceQuota'] = resourceQuota;\n    }\n  },\n};\n</script>\n<template>\n  <div>\n    <div class=\"headers mb-10\">\n      <div class=\"mr-10\">\n        <label>{{ t('resourceQuota.headers.resourceType') }}</label>\n      </div>\n      <div class=\"mr-10\">\n        <label>{{ t('resourceQuota.headers.projectResourceAvailability') }}</label>\n      </div>\n      <div class=\"mr-20\">\n        <label>{{ t('resourceQuota.headers.limit') }}</label>\n      </div>\n    </div>\n    <Row\n      v-for=\"(limit, i) in editableLimits\"\n      :key=\"i\"\n      :value=\"value.resourceQuota\"\n      :namespace=\"value\"\n      :mode=\"mode\"\n      :types=\"mappedTypes\"\n      :type=\"limit\"\n      :project-resource-quota-limits=\"projectResourceQuotaLimits\"\n      :default-resource-quota-limits=\"defaultResourceQuotaLimits\"\n      :namespace-resource-quota-limits=\"namespaceResourceQuotaLimits\"\n      @update:value=\"update\"\n    />\n  </div>\n</template>\n<style lang=\"scss\" scoped>\n.headers {\n    display: flex;\n    flex-direction: row;\n    justify-content: space-evenly;\n    align-items: center;\n    border-bottom: 1px solid var(--border);\n    height: 30px;\n\n    div {\n        width: 100%;\n    }\n}\n\n.row:not(:last-of-type) {\n  margin-bottom: 10px;\n}\n</style>\n"]}]}