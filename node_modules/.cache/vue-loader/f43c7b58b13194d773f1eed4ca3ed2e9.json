{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/LabeledTooltip/LabeledTooltip.vue?vue&type=style&index=0&id=7ac56810&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/LabeledTooltip/LabeledTooltip.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5sYWJlbGVkLXRvb2x0aXAgewogICAgcG9zaXRpb246IGFic29sdXRlOwogICAgd2lkdGg6IDEwMCU7CiAgICBoZWlnaHQ6IDEwMCU7CiAgICBsZWZ0OiAwOwogICAgdG9wOiAwOwoKICAgICYuaG92ZXJhYmxlIHsKICAgICAgaGVpZ2h0OiAwJTsKICAgIH0KCiAgICAgLnN0YXR1cy1pY29uIHsKICAgICAgICBwb3NpdGlvbjogIGFic29sdXRlOwogICAgICAgIHJpZ2h0OiAzMHB4OwogICAgICAgIHRvcDogJGlucHV0LXBhZGRpbmctbGc7CiAgICAgICAgei1pbmRleDogei1pbmRleChob3Zlck92ZXJDb250ZW50KTsKICAgICB9CgogICAgQG1peGluIHRvb2x0aXBDb2xvcnMoJGNvbG9yKSB7CiAgICAgICAgLnN0YXR1cy1pY29uIHsKICAgICAgICAgICAgY29sb3I6ICRjb2xvcjsKICAgICAgICB9CiAgICB9CgogICAgJi5lcnJvciB7CiAgICAgICAgQGluY2x1ZGUgdG9vbHRpcENvbG9ycyh2YXIoLS1lcnJvcikpOwoKICAgICAgICAuc3RhdHVzLWljb24gewogICAgICAgICAgdG9wOiA3cHg7CiAgICAgICAgICByaWdodDogNXB4OwogICAgICAgIH0KICAgIH0KCiAgICAmLndhcm5pbmcgewogICAgICAgIEBpbmNsdWRlIHRvb2x0aXBDb2xvcnModmFyKC0td2FybmluZykpOwogICAgfQoKICAgICYuc3VjY2VzcyB7CiAgICAgICAgQGluY2x1ZGUgdG9vbHRpcENvbG9ycyh2YXIoLS1zdWNjZXNzKSk7CiAgICB9Cn0KCi8vIEVuc3VyZSBjb2RlIGJsb2NrcyBpbnNpZGUgdG9vdGlwcyBkb24ndCBsb29rIGF3ZnVsCi52LXBvcHBlcl9fcG9wcGVyLnYtcG9wcGVyLS10aGVtZS10b29sdGlwIHsKICAudi1wb3BwZXJfX2lubmVyIHsKICAgIHByZSB7CiAgICAgIHBhZGRpbmc6IDJweDsKICAgICAgdmVydGljYWwtYWxpZ246IG1pZGRsZTsKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/LabeledTooltip/LabeledTooltip.vue"], "names": [], "mappings": ";AAwFA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACP,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ;;KAEC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACrC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB;IACJ;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACZ;IACJ;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C;AACJ;;AAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACf,CAAC,CAAC,EAAE;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB;EACF;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/LabeledTooltip/LabeledTooltip.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent } from 'vue';\n\nexport default defineComponent({\n  props: {\n    /**\n     * The Labeled Tooltip value.\n     */\n    value: {\n      type:    [String, Object],\n      default: null\n    },\n\n    /**\n     * The status for the Labeled Tooltip. Controls the Labeled Tooltip class.\n     * @values info, success, warning, error\n     */\n    status: {\n      type:    String,\n      default: 'error'\n    },\n\n    /**\n     * Displays the Labeled Tooltip on mouse hover.\n     */\n    hover: {\n      type:    Boolean,\n      default: true\n    }\n  },\n  computed: {\n    iconClass(): string {\n      return this.status === 'error' ? 'icon-warning' : 'icon-info';\n    },\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    tooltipContent(): {[key: string]: any} | string {\n      if (this.isObject(this.value)) {\n        return {\n          ...{ content: this.value.content, popperClass: [`tooltip-${ status }`] }, ...this.value, triggers: ['hover', 'touch', 'focus']\n        };\n      }\n\n      return this.value ? { content: this.value, triggers: ['hover', 'touch', 'focus'] } : '';\n    }\n  },\n  methods: {\n    isObject(value: string | Record<string, unknown>): value is Record<string, unknown> {\n      return typeof value === 'object' && value !== null && !!value.content;\n    }\n  }\n});\n</script>\n\n<template>\n  <div\n    ref=\"container\"\n    class=\"labeled-tooltip\"\n    :class=\"{[status]: true, hoverable: hover}\"\n  >\n    <template v-if=\"hover\">\n      <i\n        v-clean-tooltip=\"tooltipContent\"\n        v-stripped-aria-label=\"isObject(value) ? value.content : value\"\n        :class=\"{'hover':!value, [iconClass]: true}\"\n        class=\"icon status-icon\"\n        tabindex=\"0\"\n      />\n    </template>\n    <template v-else>\n      <i\n        :class=\"{'hover':!value}\"\n        class=\"icon status-icon\"\n      />\n      <div\n        v-if=\"value\"\n        class=\"tooltip\"\n        x-placement=\"bottom\"\n      >\n        <div class=\"tooltip-arrow\" />\n        <div class=\"tooltip-inner\">\n          {{ value }}\n        </div>\n      </div>\n    </template>\n  </div>\n</template>\n\n<style lang='scss'>\n.labeled-tooltip {\n    position: absolute;\n    width: 100%;\n    height: 100%;\n    left: 0;\n    top: 0;\n\n    &.hoverable {\n      height: 0%;\n    }\n\n     .status-icon {\n        position:  absolute;\n        right: 30px;\n        top: $input-padding-lg;\n        z-index: z-index(hoverOverContent);\n     }\n\n    @mixin tooltipColors($color) {\n        .status-icon {\n            color: $color;\n        }\n    }\n\n    &.error {\n        @include tooltipColors(var(--error));\n\n        .status-icon {\n          top: 7px;\n          right: 5px;\n        }\n    }\n\n    &.warning {\n        @include tooltipColors(var(--warning));\n    }\n\n    &.success {\n        @include tooltipColors(var(--success));\n    }\n}\n\n// Ensure code blocks inside tootips don't look awful\n.v-popper__popper.v-popper--theme-tooltip {\n  .v-popper__inner {\n    pre {\n      padding: 2px;\n      vertical-align: middle;\n    }\n  }\n}\n</style>\n"]}]}