{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/TextArea/TextAreaAutoGrow.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/TextArea/TextAreaAutoGrow.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCB7IGRlZmluZUNvbXBvbmVudCwgaW5qZWN0LCBQcm9wVHlwZSB9IGZyb20gJ3Z1ZSc7CmltcG9ydCB7IGRlYm91bmNlIH0gZnJvbSAnbG9kYXNoJzsKaW1wb3J0IHsgX0VESVQsIF9WSUVXIH0gZnJvbSAnQHNoZWxsL2NvbmZpZy9xdWVyeS1wYXJhbXMnOwoKaW50ZXJmYWNlIE5vblJlYWN0aXZlUHJvcHMgewogIHF1ZXVlUmVzaXplKCk6IHZvaWQ7Cn0KCmNvbnN0IHByb3ZpZGVQcm9wczogTm9uUmVhY3RpdmVQcm9wcyA9IHsKICBxdWV1ZVJlc2l6ZSgpIHsKICAgIC8vIG5vb3AKICB9Cn07CgpleHBvcnQgZGVmYXVsdCBkZWZpbmVDb21wb25lbnQoewogIGluaGVyaXRBdHRyczogZmFsc2UsCgogIHByb3BzOiB7CiAgICB2YWx1ZTogewogICAgICB0eXBlOiAgICAgU3RyaW5nLAogICAgICByZXF1aXJlZDogdHJ1ZQogICAgfSwKCiAgICBjbGFzczogewogICAgICB0eXBlOiAgICBbU3RyaW5nLCBBcnJheSwgT2JqZWN0XSBhcyBQcm9wVHlwZTxzdHJpbmcgfCB1bmtub3duW10gfCBSZWNvcmQ8c3RyaW5nLCBib29sZWFuPj4sCiAgICAgIGRlZmF1bHQ6ICcnCiAgICB9LAoKICAgIC8qKgogICAgICogU2V0cyB0aGUgZWRpdCBtb2RlIGZvciBUZXh0IEFyZWEuCiAgICAgKiBAdmFsdWVzIF9FRElULCBfVklFVwogICAgICovCiAgICBtb2RlOiB7CiAgICAgIHR5cGU6ICAgIFN0cmluZywKICAgICAgZGVmYXVsdDogX0VESVQKICAgIH0sCgogICAgLyoqCiAgICAgKiBTZXRzIHRoZSBNaW5pbXVtIGhlaWdodCBmb3IgVGV4dCBBcmVhLiBQcmV2ZW50cyB0aGUgaGVpZ2h0IGZyb20gYmVjb21pbmcKICAgICAqIHNtYWxsZXIgdGhhbiB0aGUgdmFsdWUgc3BlY2lmaWVkIGluIG1pbkhlaWdodC4KICAgICAqLwogICAgbWluSGVpZ2h0OiB7CiAgICAgIHR5cGU6ICAgIE51bWJlciwKICAgICAgZGVmYXVsdDogMjUKICAgIH0sCgogICAgLyoqCiAgICAgKiBTZXRzIHRoZSBtYXhpbXVtIGhlaWdodCBmb3IgVGV4dCBBcmVhLiBQcmV2ZW50cyB0aGUgaGVpZ2h0IGZyb20gYmVjb21pbmcKICAgICAqIGxhcmdlciB0aGFuIHRoZSB2YWx1ZSBzcGVjaWZpZWQgaW4gbWF4SGVpZ2h0LgogICAgICovCiAgICBtYXhIZWlnaHQ6IHsKICAgICAgdHlwZTogICAgTnVtYmVyLAogICAgICBkZWZhdWx0OiAyMDAKICAgIH0sCgogICAgLyoqCiAgICAgKiBUZXh0IHRoYXQgYXBwZWFycyBpbiB0aGUgVGV4dCBBcmVhIHdoZW4gaXQgaGFzIG5vIHZhbHVlIHNldC4KICAgICAqLwogICAgcGxhY2Vob2xkZXI6IHsKICAgICAgdHlwZTogICAgU3RyaW5nLAogICAgICBkZWZhdWx0OiAnJwogICAgfSwKCiAgICAvKioKICAgICAqIFNwZWNpZmllcyB3aGV0aGVyIFRleHQgQXJlYSBpcyBzdWJqZWN0IHRvIHNwZWxsIGNoZWNraW5nIGJ5IHRoZQogICAgICogdW5kZXJseWluZyBicm93c2VyL09TLgogICAgICovCiAgICBzcGVsbGNoZWNrOiB7CiAgICAgIHR5cGU6ICAgIEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IHRydWUKICAgIH0sCgogICAgLyoqCiAgICAgKiBEaXNhYmxlcyB0aGUgVGV4dCBBcmVhLgogICAgICovCiAgICBkaXNhYmxlZDogewogICAgICB0eXBlOiAgICBCb29sZWFuLAogICAgICBkZWZhdWx0OiBmYWxzZQogICAgfQogIH0sCgogIGVtaXRzOiBbJ3VwZGF0ZTp2YWx1ZScsICdwYXN0ZScsICdmb2N1cycsICdibHVyJ10sCgogIHNldHVwKCkgewogICAgY29uc3QgcXVldWVSZXNpemUgPSBpbmplY3QoJ3F1ZXVlUmVzaXplJywgcHJvdmlkZVByb3BzLnF1ZXVlUmVzaXplKTsKCiAgICByZXR1cm4geyBxdWV1ZVJlc2l6ZSB9OwogIH0sCgogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBjdXJIZWlnaHQ6IHRoaXMubWluSGVpZ2h0LAogICAgICBvdmVyZmxvdzogICdoaWRkZW4nCiAgICB9OwogIH0sCgogIGNvbXB1dGVkOiB7CiAgICAvKioKICAgICAqIERldGVybWluZXMgaWYgdGhlIFRleHQgQXJlYSBzaG91bGQgYmUgZGlzYWJsZWQuCiAgICAgKi8KICAgIGlzRGlzYWJsZWQoKTogYm9vbGVhbiB7CiAgICAgIHJldHVybiB0aGlzLmRpc2FibGVkIHx8IHRoaXMubW9kZSA9PT0gX1ZJRVc7CiAgICB9LAoKICAgIC8qKgogICAgICogU2V0cyB0aGUgaGVpZ2h0IHRvIG9uZS1saW5lIGZvciBTU1IgcGFnZWxvYWQgc28gdGhhdCBpdCdzIGFscmVhZHkgcmlnaHQKICAgICAqICh1bmxlc3MgdGhlIGlucHV0IGlzIGxvbmcpCiAgICAgKi8KICAgIHN0eWxlKCk6IHN0cmluZyB7CiAgICAgIHJldHVybiBgaGVpZ2h0OiAkeyB0aGlzLmN1ckhlaWdodCB9cHg7IG92ZXJmbG93OiAkeyB0aGlzLm92ZXJmbG93IH07YDsKICAgIH0sCgogICAgY2xhc3NOYW1lKCk6IHN0cmluZyB8IHVua25vd25bXSB8IFJlY29yZDxzdHJpbmcsIGJvb2xlYW4+IHsKICAgICAgcmV0dXJuIHRoaXMuY2xhc3M7CiAgICB9CiAgfSwKCiAgd2F0Y2g6IHsKICAgICRhdHRyczogewogICAgICBkZWVwOiB0cnVlLAogICAgICBoYW5kbGVyKCkgewogICAgICAgIHRoaXMucXVldWVSZXNpemUoKTsKICAgICAgfQogICAgfQogIH0sCgogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLnF1ZXVlUmVzaXplID0gZGVib3VuY2UodGhpcy5hdXRvU2l6ZSwgMTAwKTsKICB9LAoKICBtb3VudGVkKCkgewogICAgKHRoaXMuJHJlZnMudGEgYXMgSFRNTEVsZW1lbnQpLnN0eWxlLmhlaWdodCA9IGAkeyB0aGlzLmN1ckhlaWdodCB9cHhgOwogICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICB0aGlzLmF1dG9TaXplKCk7CiAgICB9KTsKICB9LAoKICBtZXRob2RzOiB7CiAgICAvKioKICAgICAqIEVtaXRzIHRoZSBpbnB1dCBldmVudCBhbmQgcmVzaXplcyB0aGUgVGV4dCBBcmVhLgogICAgKi8KICAgIG9uSW5wdXQoZXZlbnQ6IEV2ZW50KTogdm9pZCB7CiAgICAgIGNvbnN0IHZhbCA9IChldmVudD8udGFyZ2V0IGFzIEhUTUxJbnB1dEVsZW1lbnQpPy52YWx1ZTsKCiAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZTp2YWx1ZScsIHZhbCk7CiAgICAgIHRoaXMucXVldWVSZXNpemUoKTsKICAgIH0sCgogICAgLyoqCiAgICAgKiBHaXZlcyBmb2N1cyB0byB0aGUgVGV4dCBBcmVhLgogICAgICovCiAgICBmb2N1cygpOiB2b2lkIHsKICAgICAgKHRoaXMuJHJlZnM/LnRhIGFzIEhUTUxFbGVtZW50KS5mb2N1cygpOwogICAgfSwKCiAgICAvKioKICAgICAqIFNldHMgdGhlIG92ZXJmbG93WSBhbmQgaGVpZ2h0IG9mIHRoZSBUZXh0IEFyZWEgYmFzZWQgb24gdGhlIGNvbnRlbnQKICAgICAqIGVudGVyZWQgKGNhbGN1bGF0ZWQgdmlhIHNjcm9sbCBoZWlnaHQpLgogICAgICovCiAgICBhdXRvU2l6ZSgpOiB2b2lkIHsKICAgICAgY29uc3QgZWwgPSB0aGlzLiRyZWZzLnRhIGFzIEhUTUxFbGVtZW50OwoKICAgICAgaWYgKCFlbCkgewogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgZWwuc3R5bGUuaGVpZ2h0ID0gJzFweCc7CgogICAgICBjb25zdCBib3JkZXIgPSBwYXJzZUludChnZXRDb21wdXRlZFN0eWxlKGVsKS5nZXRQcm9wZXJ0eVZhbHVlKCdib3JkZXJUb3BXaWR0aCcpLCAxMCkgfHwgMCArIHBhcnNlSW50KGdldENvbXB1dGVkU3R5bGUoZWwpLmdldFByb3BlcnR5VmFsdWUoJ2JvcmRlckJvdHRvbVdpZHRoJyksIDEwKSB8fCAwOwogICAgICBjb25zdCBuZXUgPSBNYXRoLm1heCh0aGlzLm1pbkhlaWdodCwgTWF0aC5taW4oZWwuc2Nyb2xsSGVpZ2h0ICsgYm9yZGVyLCB0aGlzLm1heEhlaWdodCkpOwoKICAgICAgZWwuc3R5bGUub3ZlcmZsb3dZID0gZWwuc2Nyb2xsSGVpZ2h0ID4gbmV1ID8gJ2F1dG8nIDogJ2hpZGRlbic7CiAgICAgIGVsLnN0eWxlLmhlaWdodCA9IGAkeyBuZXUgfXB4YDsKCiAgICAgIHRoaXMuY3VySGVpZ2h0ID0gbmV1OwogICAgfQogIH0KfSk7Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/TextArea/TextAreaAutoGrow.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACvD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACrB;;AAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EACR;AACF,CAAC;;AAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAClC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KACrB,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACzE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC/C,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACzE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC9C,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACb,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KAC7D,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;KAChE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACvB,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KACxB,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEnE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACxB,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAChD,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KACxE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAC3B,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACvE,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACxD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB;IACF;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjD,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;MAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAC9B,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACpE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACxC,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEvC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACzK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAExF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAE9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACtB;EACF;AACF,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/TextArea/TextAreaAutoGrow.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent, inject, PropType } from 'vue';\nimport { debounce } from 'lodash';\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\n\ninterface NonReactiveProps {\n  queueResize(): void;\n}\n\nconst provideProps: NonReactiveProps = {\n  queueResize() {\n    // noop\n  }\n};\n\nexport default defineComponent({\n  inheritAttrs: false,\n\n  props: {\n    value: {\n      type:     String,\n      required: true\n    },\n\n    class: {\n      type:    [String, Array, Object] as PropType<string | unknown[] | Record<string, boolean>>,\n      default: ''\n    },\n\n    /**\n     * Sets the edit mode for Text Area.\n     * @values _EDIT, _VIEW\n     */\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n\n    /**\n     * Sets the Minimum height for Text Area. Prevents the height from becoming\n     * smaller than the value specified in minHeight.\n     */\n    minHeight: {\n      type:    Number,\n      default: 25\n    },\n\n    /**\n     * Sets the maximum height for Text Area. Prevents the height from becoming\n     * larger than the value specified in maxHeight.\n     */\n    maxHeight: {\n      type:    Number,\n      default: 200\n    },\n\n    /**\n     * Text that appears in the Text Area when it has no value set.\n     */\n    placeholder: {\n      type:    String,\n      default: ''\n    },\n\n    /**\n     * Specifies whether Text Area is subject to spell checking by the\n     * underlying browser/OS.\n     */\n    spellcheck: {\n      type:    Boolean,\n      default: true\n    },\n\n    /**\n     * Disables the Text Area.\n     */\n    disabled: {\n      type:    Boolean,\n      default: false\n    }\n  },\n\n  emits: ['update:value', 'paste', 'focus', 'blur'],\n\n  setup() {\n    const queueResize = inject('queueResize', provideProps.queueResize);\n\n    return { queueResize };\n  },\n\n  data() {\n    return {\n      curHeight: this.minHeight,\n      overflow:  'hidden'\n    };\n  },\n\n  computed: {\n    /**\n     * Determines if the Text Area should be disabled.\n     */\n    isDisabled(): boolean {\n      return this.disabled || this.mode === _VIEW;\n    },\n\n    /**\n     * Sets the height to one-line for SSR pageload so that it's already right\n     * (unless the input is long)\n     */\n    style(): string {\n      return `height: ${ this.curHeight }px; overflow: ${ this.overflow };`;\n    },\n\n    className(): string | unknown[] | Record<string, boolean> {\n      return this.class;\n    }\n  },\n\n  watch: {\n    $attrs: {\n      deep: true,\n      handler() {\n        this.queueResize();\n      }\n    }\n  },\n\n  created() {\n    this.queueResize = debounce(this.autoSize, 100);\n  },\n\n  mounted() {\n    (this.$refs.ta as HTMLElement).style.height = `${ this.curHeight }px`;\n    this.$nextTick(() => {\n      this.autoSize();\n    });\n  },\n\n  methods: {\n    /**\n     * Emits the input event and resizes the Text Area.\n    */\n    onInput(event: Event): void {\n      const val = (event?.target as HTMLInputElement)?.value;\n\n      this.$emit('update:value', val);\n      this.queueResize();\n    },\n\n    /**\n     * Gives focus to the Text Area.\n     */\n    focus(): void {\n      (this.$refs?.ta as HTMLElement).focus();\n    },\n\n    /**\n     * Sets the overflowY and height of the Text Area based on the content\n     * entered (calculated via scroll height).\n     */\n    autoSize(): void {\n      const el = this.$refs.ta as HTMLElement;\n\n      if (!el) {\n        return;\n      }\n\n      el.style.height = '1px';\n\n      const border = parseInt(getComputedStyle(el).getPropertyValue('borderTopWidth'), 10) || 0 + parseInt(getComputedStyle(el).getPropertyValue('borderBottomWidth'), 10) || 0;\n      const neu = Math.max(this.minHeight, Math.min(el.scrollHeight + border, this.maxHeight));\n\n      el.style.overflowY = el.scrollHeight > neu ? 'auto' : 'hidden';\n      el.style.height = `${ neu }px`;\n\n      this.curHeight = neu;\n    }\n  }\n});\n</script>\n\n<template>\n  <textarea\n    ref=\"ta\"\n    :value=\"value\"\n    :data-testid=\"$attrs['data-testid'] ? $attrs['data-testid'] : 'text-area-auto-grow'\"\n    :disabled=\"isDisabled\"\n    :style=\"style\"\n    :placeholder=\"placeholder\"\n    :class=\"className\"\n    class=\"no-resize no-ease\"\n    v-bind=\"$attrs\"\n    :spellcheck=\"spellcheck\"\n    @paste=\"$emit('paste', $event)\"\n    @input=\"onInput($event)\"\n    @focus=\"$emit('focus', $event)\"\n    @blur=\"$emit('blur', $event)\"\n  />\n</template>\n"]}]}