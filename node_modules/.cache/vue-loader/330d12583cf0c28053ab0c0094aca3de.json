{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Footer.vue?vue&type=style&index=0&id=75969353&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Footer.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLmJ1dHRvbnMgewogICAgZGlzcGxheTogZ3JpZDsKICAgIGdyaWQtdGVtcGxhdGUtYXJlYXM6ICAibGVmdCByaWdodCI7CiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6ICJtaW4tY29udGVudCBhdXRvIjsKCiAgICAubGVmdCB7CiAgICAgIGdyaWQtYXJlYTogbGVmdDsKICAgICAgdGV4dC1hbGlnbjogbGVmdDsKCiAgICAgIC5idG4sIGJ1dHRvbiB7CiAgICAgICAgbWFyZ2luOiAwICRjb2x1bW4tZ3V0dGVyIDAgMDsKICAgICAgfQogICAgfQoKICAgIC5yaWdodCB7CiAgICAgIGdyaWQtYXJlYTogcmlnaHQ7CiAgICAgIHRleHQtYWxpZ246IHJpZ2h0OwoKICAgICAgLmJ0biwgYnV0dG9uIHsKICAgICAgICBtYXJnaW46IDAgMCAwICRjb2x1bW4tZ3V0dGVyOwogICAgICB9CiAgICB9CiAgfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Footer.vue"], "names": [], "mappings": ";EA4FE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEzC,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CA<PERSON>,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEhB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC9B;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B;IACF;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Footer.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent } from 'vue';\nimport { _VIEW } from '@shell/config/query-params';\nimport AsyncButton, { AsyncButtonCallback } from '@shell/components/AsyncButton.vue';\nimport Banner from '@components/Banner/Banner.vue';\n\nexport default defineComponent({\n  emits: ['save', 'done'],\n\n  components: { AsyncButton, Banner },\n\n  props: {\n    /**\n     * Current mode of the page\n     * passed to asyncButton to determine lables of the button\n     */\n    mode: {\n      type:     String,\n      required: true,\n    },\n\n    errors: {\n      type:    Array,\n      default: null,\n    },\n\n    disableSave: {\n      type:    Boolean,\n      default: false,\n    }\n  },\n\n  computed: {\n    isView(): boolean {\n      return this.mode === _VIEW;\n    },\n  },\n\n  methods: {\n    save(buttonCb: AsyncButtonCallback) {\n      this.$emit('save', buttonCb);\n    },\n\n    done() {\n      this.$emit('done');\n    }\n  }\n});\n</script>\n<template>\n  <div v-if=\"!isView\">\n    <div class=\"spacer-small\" />\n\n    <div\n      v-for=\"(err,idx) in errors\"\n      :key=\"idx\"\n    >\n      <Banner\n        color=\"error\"\n        :label=\"err\"\n      />\n    </div>\n    <div class=\"buttons\">\n      <div class=\"left\">\n        <slot name=\"left\" />\n      </div>\n      <div class=\"right\">\n        <slot name=\"cancel\">\n          <button\n            type=\"button\"\n            class=\"btn role-secondary\"\n            @click=\"done\"\n          >\n            <t k=\"generic.cancel\" />\n          </button>\n        </slot>\n        <slot name=\"middle\" />\n        <slot name=\"save\">\n          <AsyncButton\n            v-if=\"!isView\"\n            :mode=\"mode\"\n            :disabled=\"disableSave\"\n            @click=\"save\"\n          />\n        </slot>\n        <slot name=\"right\" />\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang='scss'>\n  .buttons {\n    display: grid;\n    grid-template-areas:  \"left right\";\n    grid-template-columns: \"min-content auto\";\n\n    .left {\n      grid-area: left;\n      text-align: left;\n\n      .btn, button {\n        margin: 0 $column-gutter 0 0;\n      }\n    }\n\n    .right {\n      grid-area: right;\n      text-align: right;\n\n      .btn, button {\n        margin: 0 0 0 $column-gutter;\n      }\n    }\n  }\n</style>\n"]}]}