{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetRepos.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetRepos.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetRepos.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CA<PERSON>,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACL,CAAC,CAAC,CAAC;EACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;EAEb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3C,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;MACvC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE7D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpD,CAAC,CAAC;IACJ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC9C,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE/C,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC/C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpD,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAET,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC;MACJ,CAAC;IACH,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvJ,CAAC;EACH,CAAC;AACH,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetRepos.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport ResourceTable from '@shell/components/ResourceTable';\nimport Link from '@shell/components/formatter/Link';\nimport Shortened from '@shell/components/formatter/Shortened';\nimport FleetIntro from '@shell/components/fleet/FleetIntro';\n\nimport {\n  AGE,\n  FLEET_REPO,\n  FLEET_REPO_CLUSTER_SUMMARY,\n  FLEET_REPO_CLUSTERS_READY,\n  FLEET_REPO_PER_CLUSTER_STATE,\n  FLEET_REPO_TARGET,\n  FLEET_SUMMARY,\n  NAME,\n  STATE,\n} from '@shell/config/table-headers';\n\n// i18n-ignore repoDisplay\nexport default {\n\n  name: 'FleetRepos',\n\n  components: {\n    ResourceTable, Link, Shortened, FleetIntro\n  },\n  props: {\n    clusterId: {\n      type:     String,\n      required: false,\n      default:  null,\n    },\n    rows: {\n      type:     Array,\n      required: true,\n    },\n\n    schema: {\n      type:     Object,\n      required: true,\n    },\n\n    loading: {\n      type:     Boolean,\n      required: false,\n    },\n\n    useQueryParamsForSimpleFiltering: {\n      type:    <PERSON>olean,\n      default: false\n    }\n  },\n\n  computed: {\n    filteredRows() {\n      if (!this.rows) {\n        return [];\n      }\n\n      // Returns boolean { [namespace]: true }\n      const selectedWorkspace = this.$store.getters['namespaces']();\n\n      return this.rows.filter((row) => {\n        return !!selectedWorkspace[row.metadata.namespace];\n      });\n    },\n\n    isClusterView() {\n      return !!this.clusterId;\n    },\n\n    noRows() {\n      return !this.filteredRows.length;\n    },\n\n    headers() {\n      // Cluster summary is only shown in the cluster view\n      const summary = this.isClusterView ? [{\n        ...FLEET_REPO_CLUSTER_SUMMARY,\n        formatterOpts: { clusterId: this.clusterId },\n      }] : [FLEET_REPO_CLUSTERS_READY, FLEET_SUMMARY];\n\n      // if hasPerClusterState then use the repo state\n      const state = this.isClusterView ? {\n        ...FLEET_REPO_PER_CLUSTER_STATE,\n        value: (repo) => repo.clusterState(this.clusterId),\n      } : STATE;\n\n      return [\n        state,\n        NAME,\n        FLEET_REPO,\n        FLEET_REPO_TARGET,\n        ...summary,\n        AGE\n      ];\n    },\n  },\n  methods: {\n    parseTargetMode(row) {\n      return row.targetInfo?.mode === 'clusterGroup' ? this.t('fleet.gitRepo.warningTooltip.clusterGroup') : this.t('fleet.gitRepo.warningTooltip.cluster');\n    },\n  },\n};\n</script>\n\n<template>\n  <div>\n    <FleetIntro v-if=\"noRows && !loading\" />\n    <ResourceTable\n      v-if=\"!noRows\"\n      v-bind=\"$attrs\"\n      :schema=\"schema\"\n      :headers=\"headers\"\n      :rows=\"rows\"\n      :loading=\"loading\"\n      :use-query-params-for-simple-filtering=\"useQueryParamsForSimpleFiltering\"\n      key-field=\"_key\"\n    >\n      <template #cell:repo=\"{ row }\">\n        <Link\n          :row=\"row\"\n          :value=\"row.spec.repo || ''\"\n          label-key=\"repoDisplay\"\n          before-icon-key=\"repoIcon\"\n          url-key=\"spec.repo\"\n        />\n        {{ row.cluster }}\n        <template v-if=\"row.commitDisplay\">\n          <div class=\"text-muted\">\n            <Shortened\n              long-value-key=\"status.commit\"\n              :row=\"row\"\n              :value=\"row.commitDisplay\"\n            />\n          </div>\n        </template>\n      </template>\n\n      <template\n        v-if=\"!isClusterView\"\n        #cell:clustersReady=\"{ row }\"\n      >\n        <span\n          v-if=\"!row.clusterInfo\"\n          class=\"text-muted\"\n        >&mdash;</span>\n        <span\n          v-else-if=\"row.clusterInfo.unready\"\n          class=\"text-warning\"\n        >{{ row.clusterInfo.ready }}/{{\n          row.clusterInfo.total }}</span>\n        <span\n          v-else\n          class=\"cluster-count-info\"\n        >\n          {{ row.clusterInfo.ready }}/{{ row.clusterInfo.total }}\n          <i\n            v-if=\"!row.clusterInfo.total\"\n            v-clean-tooltip.bottom=\"parseTargetMode(row)\"\n            class=\"icon icon-warning\"\n          />\n        </span>\n      </template>\n\n      <template #cell:target=\"{ row }\">\n        {{ row.targetInfo.modeDisplay }}\n      </template>\n    </ResourceTable>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.cluster-count-info {\n  display: flex;\n  align-items: center;\n\n  i {\n    margin-left: 5px;\n    font-size: 22px;\n    color: var(--warning);\n  }\n}\n</style>\n"]}]}