{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Markdown.vue?vue&type=style&index=0&id=91d32c1c&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Markdown.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Cgo6ZGVlcCgpIHsKICBQIHsKICAgIGZvbnQtc2l6ZTogaW5pdGlhbDsKICAgIGxpbmUtaGVpZ2h0OiBpbml0aWFsOwogICAgZm9udC13ZWlnaHQ6IGluaXRpYWw7CiAgICBsZXR0ZXItc3BhY2luZzogaW5pdGlhbDsKICAgIGZvbnQtc3R5bGU6IG5vcm1hbDsKICB9Cn0KCi5tYXJrZG93biB7CiAgICBibG9ja3F1b3RlIHsKICAgICAgY29sb3I6IHJnYigxMDEsIDEwOSwgMTE4KTsKICAgICAgYm9yZGVyLWxlZnQ6IDAuMjVlbSBzb2xpZCByZ2IoMjA4LCAyMTUsIDIyMik7CiAgICAgIHBhZGRpbmc6IDAgMWVtOwogICAgICBtYXJnaW4tYm90dG9tOiAxNnB4OwogICAgfQoKICAgIHRhYmxlIHsKICAgICAgYm9yZGVyLWNvbGxhcHNlOiBjb2xsYXBzZTsKICAgIH0KCiAgICBUSCB7CiAgICAgIHRleHQtYWxpZ246IGxlZnQ7CiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlM2U3ZWI7CiAgICB9CgogICAgdGFibGUgdHIgdGggewogICAgICBmb250LXdlaWdodDogYm9sZDsKICAgICAgdGV4dC1hbGlnbjogbGVmdDsKICAgICAgbWFyZ2luOiAwOwogICAgICBwYWRkaW5nOiA2cHggMTNweDsKICAgIH0KCiAgICB0YWJsZSB0ciB0aCB7CiAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgICB0ZXh0LWFsaWduOiBsZWZ0OwogICAgICBtYXJnaW46IDA7CiAgICAgIHBhZGRpbmc6IDZweCAxM3B4OwogICAgfQoKICAgIHRhYmxlIHRyIHRkIHsKICAgICAgdGV4dC1hbGlnbjogbGVmdDsKICAgICAgbWFyZ2luOiAwOwogICAgICBwYWRkaW5nOiA2cHggMTNweDsKICAgICAgYm9yZGVyOiAxcHggc29saWQgI2UzZTdlYjsKICAgIH0KCiAgICB0YWJsZSB0ciB0aCA6Zmlyc3QtY2hpbGQsIHRhYmxlIHRyIHRkIDpmaXJzdC1jaGlsZCB7CiAgICAgIG1hcmdpbi10b3A6IDA7CiAgICB9CgogICAgdGFibGUgdHIgdGggOmxhc3QtY2hpbGQsIHRhYmxlIHRyIHRkIDpsYXN0LWNoaWxkIHsKICAgICAgbWFyZ2luLWJvdHRvbTogMDsKICAgIH0KfQoK"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Markdown.vue"], "names": [], "mappings": ";;AAgFA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACN,EAAE;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrB;;IAEA,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B;;IAEA,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B;;IAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB;;IAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB;;IAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B;;IAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACf;;IAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAClB;AACJ", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Markdown.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport Loading from '@shell/components/Loading';\n\nexport default {\n  emits: ['loaded'],\n\n  components: { Loading },\n\n  props: {\n    value: {\n      type:     String,\n      required: true,\n    }\n  },\n\n  data() {\n    return {\n      loaded: false,\n      marked: null,\n    };\n  },\n\n  computed: {\n    html() {\n      return this.marked.parse(this.value, {\n        renderer: this.markedRenderer,\n        breaks:   true\n      });\n    },\n  },\n\n  async mounted() {\n    const marked = (await import(/* webpackChunkName: \"markdown\" */ 'marked'));\n\n    const renderer = new marked.Renderer();\n    const linkRenderer = renderer.link;\n\n    const base = this.$router ? this.$router.resolve(this.$route).href.replace(/#.*$/, '') : '';\n\n    renderer.link = function(href, title, text) {\n      let external = true;\n\n      // Relative links don't work, since they aren't relative to the dashboard page\n      if (href.startsWith('./')) {\n        return text;\n      }\n\n      if ( href.startsWith('#') ) {\n        href = `${ base }${ href }`;\n        external = false;\n      }\n\n      const rendered = linkRenderer.call(this, href, title, text);\n\n      if ( external ) {\n        return rendered.replace(/^<a /, '<a target=\"_blank\" rel=\"nofollow noopener noreferrer\" ');\n      }\n\n      return rendered;\n    };\n\n    this.marked = marked;\n    this.markedRenderer = renderer;\n    this.loaded = true;\n    this.$emit('loaded', true);\n  }\n};\n</script>\n\n<template>\n  <div\n    v-if=\"loaded\"\n    v-clean-html=\"html\"\n    class=\"markdown\"\n  />\n  <Loading v-else />\n</template>\n\n<style lang=\"scss\">\n\n:deep() {\n  P {\n    font-size: initial;\n    line-height: initial;\n    font-weight: initial;\n    letter-spacing: initial;\n    font-style: normal;\n  }\n}\n\n.markdown {\n    blockquote {\n      color: rgb(101, 109, 118);\n      border-left: 0.25em solid rgb(208, 215, 222);\n      padding: 0 1em;\n      margin-bottom: 16px;\n    }\n\n    table {\n      border-collapse: collapse;\n    }\n\n    TH {\n      text-align: left;\n      border: 1px solid #e3e7eb;\n    }\n\n    table tr th {\n      font-weight: bold;\n      text-align: left;\n      margin: 0;\n      padding: 6px 13px;\n    }\n\n    table tr th {\n      font-weight: bold;\n      text-align: left;\n      margin: 0;\n      padding: 6px 13px;\n    }\n\n    table tr td {\n      text-align: left;\n      margin: 0;\n      padding: 6px 13px;\n      border: 1px solid #e3e7eb;\n    }\n\n    table tr th :first-child, table tr td :first-child {\n      margin-top: 0;\n    }\n\n    table tr th :last-child, table tr td :last-child {\n      margin-bottom: 0;\n    }\n}\n\n</style>\n"]}]}