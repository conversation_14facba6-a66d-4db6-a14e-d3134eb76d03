{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ClusterAppearance.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ClusterAppearance.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ClusterAppearance.vue"], "names": [], "mappings": ";AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;IACV,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;IAC7C,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;EAChD,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChE,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;IAC9B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvE;QACF,CAAC;MACH;;MAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;MAC1B,CAAC;IACH,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;EACH,CAAC;AACH,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ClusterAppearance.vue", "sourceRoot": "", "sourcesContent": ["\n<script>\nimport ClusterIconMenu from '@shell/components/ClusterIconMenu';\nimport { _CREATE, _VIEW } from '@shell/config/query-params';\nimport { CLUSTER_BADGE } from '@shell/config/labels-annotations';\nexport default {\n  title:      'ClusterAppearance',\n  components: { ClusterIconMenu },\n  props:      {\n    name:           { type: String, default: '' },\n    mode:           { type: String, default: _CREATE },\n    currentCluster: { type: Object, default: null }\n  },\n  created() {\n    this.$store.dispatch('customisation/setDefaultPreviewCluster');\n  },\n\n  computed: {\n    disable() {\n      if (this.mode === _VIEW) {\n        return true;\n      }\n\n      return this.name.length <= 1;\n    },\n    clusterPreview() {\n      if (this.mode !== _CREATE) {\n        return {\n          ...this.currentCluster,\n          badge: {\n            iconText: this.currentCluster.metadata.annotations[CLUSTER_BADGE.ICON_TEXT],\n            color:    this.currentCluster.metadata.annotations[CLUSTER_BADGE.COLOR],\n            text:     this.currentCluster.metadata.annotations[CLUSTER_BADGE.TEXT]\n          }\n        };\n      }\n\n      const obj = {\n        ...this.$store.getters['customisation/getPreviewCluster'],\n        label: this.name\n      };\n\n      return obj || {\n        label: this.name,\n        badge: { iconText: null }\n      };\n    },\n  },\n\n  methods: {\n    customBadgeDialog() {\n      this.$store.dispatch('cluster/promptModal', {\n        component:      'AddCustomBadgeDialog',\n        componentProps: {\n          isCreate:        this.mode === _CREATE,\n          mode:            this.mode,\n          clusterName:     this.name,\n          clusterExplorer: this.clusterPreview\n        },\n      });\n    },\n  },\n};\n</script>\n\n<template>\n  <div class=\"cluster-appearance\">\n    <label for=\"name\">\n      {{ t('clusterBadge.setClusterAppearance') }}\n    </label>\n    <div class=\"cluster-appearance-preview\">\n      <span>\n        <ClusterIconMenu :cluster=\"clusterPreview\" />\n      </span>\n      <button\n        :disabled=\"disable\"\n        @click=\"customBadgeDialog\"\n      >\n        <i class=\"icon icon-brush-icon\" />\n        <span>\n          {{ t('clusterBadge.customize') }}\n        </span>\n      </button>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .cluster-appearance {\n    display: flex;\n    flex-direction: column;\n    margin: 3px 35px 0px 0px;\n\n    label {\n      margin: 6px 0 0;\n    }\n\n    &-preview {\n      display: flex;\n      justify-content: center;\n      align-self: start;\n      gap: 10px;\n      justify-content: space-between;\n\n      span {\n        display: flex;\n        align-self: center;\n        height: auto;\n      }\n\n      button {\n        display: flex;\n        align-self: center;\n        height: auto;\n        margin: 0;\n        padding: 0;\n        top: 0;\n        color: var(--link);\n        background: transparent;\n\n        i {\n          margin-right: 2px;\n        }\n\n        &:disabled {\n          color: var(--disabled-text);\n          cursor: not-allowed;\n        }\n      }\n    }\n  }\n</style>\n"]}]}