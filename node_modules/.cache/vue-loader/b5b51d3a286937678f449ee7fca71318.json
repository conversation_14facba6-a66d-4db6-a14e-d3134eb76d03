{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayListSelect.vue?vue&type=template&id=64024a2a&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayListSelect.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxBcnJheUxpc3QNCiAgICB2LWJpbmQ9ImFycmF5TGlzdFByb3BzIg0KICAgIDp2YWx1ZT0idmFsdWUiDQogICAgY2xhc3M9ImFycmF5LWxpc3Qtc2VsZWN0Ig0KICAgIDphZGQtYWxsb3dlZD0iYWRkQWxsb3dlZCB8fCBsb2FkaW5nIg0KICAgIDpsb2FkaW5nPSJsb2FkaW5nIg0KICAgIDpkZWZhdWx0QWRkVmFsdWU9ImRlZmF1bHRBZGRWYWx1ZSINCiAgICA6ZGlzYWJsZWQ9ImRpc2FibGVkIg0KICAgIEB1cGRhdGU6dmFsdWU9IiRlbWl0KCd1cGRhdGU6dmFsdWUnLCAkZXZlbnQpIg0KICA+DQogICAgPHRlbXBsYXRlIHYtc2xvdDpjb2x1bW5zPSJzY29wZSI+DQogICAgICA8U2VsZWN0DQogICAgICAgIDp2YWx1ZT0ic2NvcGUucm93LnZhbHVlIg0KICAgICAgICB2LWJpbmQ9InNlbGVjdFByb3BzIg0KICAgICAgICA6b3B0aW9ucz0iY2FsY3VsYXRlT3B0aW9ucyhzY29wZS5yb3cudmFsdWUpIg0KICAgICAgICA6Z2V0LW9wdGlvbi1sYWJlbD0iZ2V0T3B0aW9uTGFiZWwiDQogICAgICAgIEB1cGRhdGU6dmFsdWU9InVwZGF0ZVJvdyhzY29wZS5pLCAkZXZlbnQpIg0KICAgICAgLz4NCiAgICA8L3RlbXBsYXRlPg0KICA8L0FycmF5TGlzdD4NCg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayListSelect.vue"], "names": [], "mappings": ";EA4EE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9C;IACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3C,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayListSelect.vue", "sourceRoot": "", "sourcesContent": ["<script>\r\nimport ArrayList from '@shell/components/form/ArrayList';\r\nimport Select from '@shell/components/form/Select';\r\n\r\nexport default {\r\n  emits: ['update:value'],\r\n\r\n  components: { ArrayList, Select },\r\n  props:      {\r\n    value: {\r\n      type:     Array,\r\n      required: true\r\n    },\r\n    options: {\r\n      default: null,\r\n      type:    Array\r\n    },\r\n    selectProps: {\r\n      type:    Object,\r\n      default: null,\r\n    },\r\n    arrayListProps: {\r\n      type:    Object,\r\n      default: null\r\n    },\r\n    enableDefaultAddValue: {\r\n      type:    Boolean,\r\n      default: true\r\n    },\r\n    loading: {\r\n      type:    Boolean,\r\n      default: false\r\n    },\r\n    disabled: {\r\n      type:    Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  computed: {\r\n    filteredOptions() {\r\n      return this.options\r\n        .filter((option) => !this.value.includes(option.value));\r\n    },\r\n\r\n    addAllowed() {\r\n      return this.arrayListProps?.addAllowed || this.filteredOptions.length > 0;\r\n    },\r\n\r\n    defaultAddValue() {\r\n      return this.enableDefaultAddValue ? this.options[0]?.value : '';\r\n    },\r\n\r\n    getOptionLabel() {\r\n      return this.selectProps?.getOptionLabel ? (opt) => (this.selectProps?.getOptionLabel(opt) || opt) : undefined;\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    updateRow(index, value) {\r\n      this.value.splice(index, 1, value);\r\n      this.$emit('update:value', this.value);\r\n    },\r\n    calculateOptions(value) {\r\n      const valueOption = this.options.find((o) => o.value === value);\r\n\r\n      if (valueOption) {\r\n        return [valueOption, ...this.filteredOptions];\r\n      }\r\n\r\n      return this.filteredOptions;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<template>\r\n  <ArrayList\r\n    v-bind=\"arrayListProps\"\r\n    :value=\"value\"\r\n    class=\"array-list-select\"\r\n    :add-allowed=\"addAllowed || loading\"\r\n    :loading=\"loading\"\r\n    :defaultAddValue=\"defaultAddValue\"\r\n    :disabled=\"disabled\"\r\n    @update:value=\"$emit('update:value', $event)\"\r\n  >\r\n    <template v-slot:columns=\"scope\">\r\n      <Select\r\n        :value=\"scope.row.value\"\r\n        v-bind=\"selectProps\"\r\n        :options=\"calculateOptions(scope.row.value)\"\r\n        :get-option-label=\"getOptionLabel\"\r\n        @update:value=\"updateRow(scope.i, $event)\"\r\n      />\r\n    </template>\r\n  </ArrayList>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n:deep() .unlabeled-select {\r\n  height: 61px;\r\n  }\r\n</style>\r\n"]}]}