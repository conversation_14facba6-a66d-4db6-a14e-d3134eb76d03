{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceCancelModal.vue?vue&type=style&index=0&id=3d7d24b2&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceCancelModal.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAuY29uZmlybS1tb2RhbCB7CiAgLmJ0biB7CiAgICBtYXJnaW46IDAgMTBweDsKICB9CgogIC5ib2R5IHsKICAgIG1pbi1oZWlnaHQ6IDc1cHg7CiAgICBwYWRkaW5nOiAxMHB4IDAgMCAxNXB4OwogICAgcCB7CiAgICAgIG1hcmdpbi10b3A6IDEwcHg7CiAgICB9CiAgfQogIC5oZWFkZXIgewogICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tZXJyb3IpOwogICAgcGFkZGluZzogMTVweCAwIDAgMTVweDsKICAgIGhlaWdodDogNTBweDsKCiAgICBoNCB7CiAgICAgIGNvbG9yOiB3aGl0ZTsKICAgIH0KICB9CiAgLmZvb3RlciB7CiAgICBib3JkZXItdG9wOiAxcHggc29saWQgdmFyKC0tYm9yZGVyKTsKICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICAgIHBhZGRpbmc6IDEwcHggMCAwIDE1cHg7CiAgICBoZWlnaHQ6IDYwcHg7CiAgfQp9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceCancelModal.vue"], "names": [], "mappings": ";CAkGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,EAAE;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACtB,EAAE;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB;EACF;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEZ,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACd;EACF;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACd;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceCancelModal.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport AppModal from '@shell/components/AppModal.vue';\n\nexport default {\n  emits: ['cancel-cancel', 'confirm-cancel'],\n\n  components: { AppModal },\n\n  props: {\n    isCancelModal: {\n      type:    Boolean,\n      default: false\n    },\n    isForm: {\n      type:    Boolean,\n      default: false\n    },\n  },\n\n  data() {\n    return { showModal: false };\n  },\n\n  watch: {},\n\n  methods: {\n    show() {\n      this.showModal = true;\n    },\n\n    /**\n     * Close the modal, no op\n     */\n    cancelCancel() {\n      this.showModal = false;\n\n      this.$emit('cancel-cancel');\n    },\n\n    /**\n     * Close the modal, cancel has been confirmed\n     */\n    confirmCancel() {\n      this.showModal = false;\n\n      this.$emit('confirm-cancel', this.isCancelModal);\n    },\n  }\n};\n</script>\n\n<template>\n  <app-modal\n    v-if=\"showModal\"\n    customClass=\"confirm-modal\"\n    name=\"cancel-modal\"\n    :width=\"440\"\n    height=\"auto\"\n    @close=\"cancelCancel\"\n  >\n    <div class=\"header\">\n      <h4 class=\"text-default-text\">\n        <t\n          v-if=\"isCancelModal\"\n          k=\"generic.cancel\"\n        />\n        <span v-else>{{ t(\"cruResource.backToForm\") }}</span>\n      </h4>\n    </div>\n    <div class=\"body\">\n      <p v-if=\"isCancelModal\">\n        <t k=\"cruResource.cancelBody\" />\n      </p>\n      <p v-else>\n        <t k=\"cruResource.backBody\" />\n      </p>\n    </div>\n    <div class=\"footer\">\n      <button\n        type=\"button\"\n        class=\"btn role-secondary\"\n        @click=\"cancelCancel\"\n      >\n        {{ isForm ? t(\"cruResource.reviewForm\") : t(\"cruResource.reviewYaml\") }}\n      </button>\n      <button\n        type=\"button\"\n        class=\"btn role-primary\"\n        @click=\"confirmCancel\"\n      >\n        <span v-if=\"isCancelModal\">{{ t(\"cruResource.confirmCancel\") }}</span>\n        <span v-else>{{ t(\"cruResource.confirmBack\") }}</span>\n      </button>\n    </div>\n  </app-modal>\n</template>\n\n<style lang='scss' scoped>\n .confirm-modal {\n  .btn {\n    margin: 0 10px;\n  }\n\n  .body {\n    min-height: 75px;\n    padding: 10px 0 0 15px;\n    p {\n      margin-top: 10px;\n    }\n  }\n  .header {\n    background-color: var(--error);\n    padding: 15px 0 0 15px;\n    height: 50px;\n\n    h4 {\n      color: white;\n    }\n  }\n  .footer {\n    border-top: 1px solid var(--border);\n    text-align: center;\n    padding: 10px 0 0 15px;\n    height: 60px;\n  }\n}\n</style>\n"]}]}