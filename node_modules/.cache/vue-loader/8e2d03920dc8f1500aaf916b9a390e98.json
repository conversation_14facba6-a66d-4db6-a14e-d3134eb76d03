{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GroupPanel.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GroupPanel.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmV4cG9ydCBkZWZhdWx0IHsKICBwcm9wczogewogICAgLyoqCiAgICAgKiBMYWJlbCBmb3IgdGhlIGdyb3VwCiAgICAgKi8KICAgIGxhYmVsOiB7CiAgICAgIHR5cGU6ICAgIFN0cmluZywKICAgICAgZGVmYXVsdDogbnVsbAogICAgfSwKICAgIC8qKgogICAgICogVGhlIGkxOG4ga2V5IHRvIHVzZSBmb3IgdGhlIGxhYmVsCiAgICAgKi8KICAgIGxhYmVsS2V5OiB7CiAgICAgIHR5cGU6ICAgIFN0cmluZywKICAgICAgZGVmYXVsdDogbnVsbAogICAgfSwKICB9Cn07Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GroupPanel.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KACpB,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;IACD,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAClC,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;EACH;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GroupPanel.vue", "sourceRoot": "", "sourcesContent": ["<script>\nexport default {\n  props: {\n    /**\n     * Label for the group\n     */\n    label: {\n      type:    String,\n      default: null\n    },\n    /**\n     * The i18n key to use for the label\n     */\n    labelKey: {\n      type:    String,\n      default: null\n    },\n  }\n};\n</script>\n<template>\n  <div class=\"group-panel-outer\">\n    <div class=\"group-panel\">\n      <div class=\"group-panel-title\">\n        <t\n          v-if=\"labelKey\"\n          :k=\"labelKey\"\n        />\n        <template v-else-if=\"label\">\n          {{ label }}\n        </template>\n      </div>\n      <div class=\"group-panel-content\">\n        <slot />\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .group-panel {\n    border: 1px solid var(--border);\n    border-radius: 5px;\n    padding: 10px;\n    position: relative;\n    margin-top: 10px;\n    .group-panel-title {\n      position: absolute;\n      top: -7px;\n      background-color: var(--body-bg);\n      padding: 0 5px;\n    }\n    .group-panel-content {\n      position: relative;\n    }\n  }\n</style>\n"]}]}