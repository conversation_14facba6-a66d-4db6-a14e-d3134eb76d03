{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/ToggleSwitch/ToggleSwitch.vue?vue&type=style&index=0&id=0f8869ea&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/ToggleSwitch/ToggleSwitch.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiR0b2dnbGUtaGVpZ2h0OiAxNnB4OwoKLnRvZ2dsZS1jb250YWluZXIgewogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgZGlzcGxheTogZmxleDsKCiAgc3BhbjpmaXJzdC1jaGlsZCB7CiAgICBwYWRkaW5nLXJpZ2h0OiA2cHg7CiAgfQogIHNwYW46bGFzdC1jaGlsZCB7CiAgICBwYWRkaW5nLWxlZnQ6IDZweDsKICB9Cn0KLyogVGhlIHN3aXRjaCAtIHRoZSBib3ggYXJvdW5kIHRoZSBzbGlkZXIgKi8KLnN3aXRjaCB7CiAgcG9zaXRpb246IHJlbGF0aXZlOwogIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICB3aWR0aDogNDhweDsKICBoZWlnaHQ6ICR0b2dnbGUtaGVpZ2h0ICsgOHB4Owp9CgovKiBIaWRlIGRlZmF1bHQgSFRNTCBjaGVja2JveCAqLwouc3dpdGNoIGlucHV0IHsKICBvcGFjaXR5OiAwOwogIHdpZHRoOiAwOwogIGhlaWdodDogMDsKfQoKLyogVGhlIHNsaWRlciAqLwouc2xpZGVyIHsKICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgY3Vyc29yOiBwb2ludGVyOwogIHRvcDogMDsKICBsZWZ0OiAwOwogIHJpZ2h0OiAwOwogIGJvdHRvbTogMDsKICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1jaGVja2JveC1kaXNhYmxlZC1iZyk7CiAgLXdlYmtpdC10cmFuc2l0aW9uOiAuNHM7CiAgdHJhbnNpdGlvbjogLjRzOwoKICAmLmZvY3VzIHsKICAgIEBpbmNsdWRlIGZvY3VzLW91dGxpbmU7CiAgICBvdXRsaW5lLW9mZnNldDogMnB4OwogICAgLXdlYmtpdC10cmFuc2l0aW9uOiAwczsKICAgIHRyYW5zaXRpb246IDBzOwogIH0KfQoKLnNsaWRlcjpiZWZvcmUgewogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICBjb250ZW50OiAiIjsKICBoZWlnaHQ6ICR0b2dnbGUtaGVpZ2h0OwogIHdpZHRoOiAkdG9nZ2xlLWhlaWdodDsKICBsZWZ0OiA0cHg7CiAgYm90dG9tOiA0cHg7CiAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tY2hlY2tib3gtdGljayk7CiAgLXdlYmtpdC10cmFuc2l0aW9uOiAuNHM7CiAgdHJhbnNpdGlvbjogLjRzOwp9CgppbnB1dDpjaGVja2VkICsgLnNsaWRlciB7CiAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tY2hlY2tib3gtdGlja2VkLWJnKTsKfQoKaW5wdXQ6Zm9jdXMgKyAuc2xpZGVyIHsKICBib3gtc2hhZG93OiAwIDAgMXB4IHZhcigtLWNoZWNrYm94LXRpY2tlZC1iZyk7Cn0KCmlucHV0OmNoZWNrZWQgKyAuc2xpZGVyOmJlZm9yZSB7CiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDI0cHgpOwp9CgovKiBSb3VuZGVkIHNsaWRlcnMgKi8KLnNsaWRlci5yb3VuZCB7CiAgYm9yZGVyLXJhZGl1czogMzRweDsKfQoKLnNsaWRlci5yb3VuZDpiZWZvcmUgewogIGJvcmRlci1yYWRpdXM6IDUwJTsKfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/ToggleSwitch/ToggleSwitch.vue"], "names": [], "mappings": ";AA+GA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;AAEpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACpB;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACnB;AACF;AACA,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AAC9B;;AAEA,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACX;;AAEA,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,EAAE,CAAC;EACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B;;AAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACrB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACpB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/ToggleSwitch/ToggleSwitch.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent, onMounted, onBeforeUnmount, ref } from 'vue';\n\ntype StateType = boolean | 'true' | 'false' | undefined;\n\nexport default defineComponent({\n  props: {\n    value: {\n      type:    [<PERSON><PERSON><PERSON>, String, Number],\n      default: false\n    },\n\n    offValue: {\n      type:    [<PERSON>olean, String, Number],\n      default: false,\n    },\n\n    onValue: {\n      type:    [<PERSON>olean, String, Number],\n      default: true,\n    },\n\n    offLabel: {\n      type:    String,\n      default: '',\n    },\n\n    onLabel: {\n      type:    String,\n      default: '',\n    },\n  },\n\n  emits: ['update:value'],\n\n  setup() {\n    const switchChrome = ref<HTMLElement | null>(null);\n    const focus = () => {\n      switchChrome.value?.classList.add('focus');\n    };\n\n    const blur = () => {\n      switchChrome.value?.classList.remove('focus');\n    };\n\n    const switchInput = ref<HTMLInputElement | null>(null);\n\n    onMounted(() => {\n      switchInput.value?.addEventListener('focus', focus);\n      switchInput.value?.addEventListener('blur', blur);\n    });\n\n    onBeforeUnmount(() => {\n      switchInput.value?.removeEventListener('focus', focus);\n      switchInput.value?.removeEventListener('blur', blur);\n    });\n  },\n\n  data() {\n    return { state: false as StateType };\n  },\n\n  watch: {\n    value: {\n      handler() {\n        this.state = this.value === this.onValue;\n      },\n      immediate: true\n    }\n  },\n\n  methods: {\n    toggle(neu: StateType | null) {\n      this.state = neu === null ? !this.state : neu;\n      this.$emit('update:value', this.state ? this.onValue : this.offValue);\n    }\n  }\n});\n</script>\n\n<template>\n  <span class=\"toggle-container\">\n    <span\n      class=\"label no-select hand\"\n      :class=\"{ active: !state}\"\n      @click=\"toggle(false)\"\n    >{{ offLabel }}</span>\n    <label class=\"switch hand\">\n      <input\n        ref=\"switchInput\"\n        type=\"checkbox\"\n        role=\"switch\"\n        :checked=\"state\"\n        :aria-label=\"onLabel\"\n        @input=\"toggle(null)\"\n        @keydown.enter=\"toggle(null)\"\n      >\n      <span\n        ref=\"switchChrome\"\n        class=\"slider round\"\n      />\n    </label>\n    <span\n      class=\"label no-select hand\"\n      :class=\"{ active: state}\"\n      @click=\"toggle(true)\"\n    >{{ onLabel }}</span>\n  </span>\n</template>\n\n<style lang=\"scss\" scoped>\n$toggle-height: 16px;\n\n.toggle-container {\n  align-items: center;\n  display: flex;\n\n  span:first-child {\n    padding-right: 6px;\n  }\n  span:last-child {\n    padding-left: 6px;\n  }\n}\n/* The switch - the box around the slider */\n.switch {\n  position: relative;\n  display: inline-block;\n  width: 48px;\n  height: $toggle-height + 8px;\n}\n\n/* Hide default HTML checkbox */\n.switch input {\n  opacity: 0;\n  width: 0;\n  height: 0;\n}\n\n/* The slider */\n.slider {\n  position: absolute;\n  cursor: pointer;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: var(--checkbox-disabled-bg);\n  -webkit-transition: .4s;\n  transition: .4s;\n\n  &.focus {\n    @include focus-outline;\n    outline-offset: 2px;\n    -webkit-transition: 0s;\n    transition: 0s;\n  }\n}\n\n.slider:before {\n  position: absolute;\n  content: \"\";\n  height: $toggle-height;\n  width: $toggle-height;\n  left: 4px;\n  bottom: 4px;\n  background-color: var(--checkbox-tick);\n  -webkit-transition: .4s;\n  transition: .4s;\n}\n\ninput:checked + .slider {\n  background-color: var(--checkbox-ticked-bg);\n}\n\ninput:focus + .slider {\n  box-shadow: 0 0 1px var(--checkbox-ticked-bg);\n}\n\ninput:checked + .slider:before {\n  transform: translateX(24px);\n}\n\n/* Rounded sliders */\n.slider.round {\n  border-radius: 34px;\n}\n\n.slider.round:before {\n  border-radius: 50%;\n}\n</style>\n"]}]}