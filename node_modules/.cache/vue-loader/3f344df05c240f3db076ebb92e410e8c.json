{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DetailTop.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DetailTop.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DetailTop.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE7D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX;IACF,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;KAC9D,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KACrB,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX;IACF,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;KACzC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KACrB,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX;IACF;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACvD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAErG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACjB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;MAErB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACnB,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB;MACF,CAAC,CAAC;;MAEF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACxD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACjC;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvD,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjD,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEzH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACzB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACvF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAChG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC1D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAErF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjD;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD;EACF;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DetailTop.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport Tag from '@shell/components/Tag';\nimport isEmpty from 'lodash/isEmpty';\nimport DetailText from '@shell/components/DetailText';\nimport { _VIEW } from '@shell/config/query-params';\nimport { ExtensionPoint, PanelLocation } from '@shell/core/types';\nimport ExtensionPanel from '@shell/components/ExtensionPanel';\n\nexport default {\n  components: {\n    DetailText, Tag, ExtensionPanel\n  },\n\n  props: {\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n\n    moreDetails: {\n      type:    Array,\n      default: () => {\n        return [];\n      }\n    },\n\n    /**\n     * Optionally replace key/value and display tooltips for the tab\n     * Dictionary key based\n     */\n    tooltips: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n\n    /**\n     * Optionally display icons next to the tab\n     * Dictionary key based\n     */\n    icons: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  data() {\n    return {\n      extensionType:      ExtensionPoint.PANEL,\n      extensionLocation:  PanelLocation.DETAIL_TOP,\n      annotationsVisible: false,\n      showAllLabels:      false,\n      view:               _VIEW\n    };\n  },\n\n  computed: {\n    namespaces() {\n      return (this.value?.namespaces || []).map((namespace) => {\n        return {\n          name:           namespace?.metadata?.name,\n          detailLocation: namespace.detailLocation\n        };\n      });\n    },\n    details() {\n      const items = [\n        ...(this.moreDetails || []),\n        ...(this.value?.details || []),\n      ].filter((x) => x.separator || (!!`${ x.content }` && x.content !== undefined && x.content !== null));\n\n      const groups = [];\n      let currentGroup = [];\n\n      items.forEach((i) => {\n        if (i.separator) {\n          groups.push(currentGroup);\n          currentGroup = [];\n        } else {\n          currentGroup.push(i);\n        }\n      });\n\n      if (currentGroup.length) {\n        groups.push(currentGroup);\n      }\n\n      return groups;\n    },\n\n    labels() {\n      if (this.showAllLabels || !this.showFilteredSystemLabels) {\n        return this.value?.labels || {};\n      }\n\n      return this.value?.filteredSystemLabels;\n    },\n\n    internalTooltips() {\n      return this.value?.detailTopTooltips || this.tooltips;\n    },\n\n    internalIcons() {\n      return this.value?.detailTopIcons || this.icons;\n    },\n\n    annotations() {\n      return this.value?.annotations || {};\n    },\n\n    description() {\n      return this.value?.description;\n    },\n\n    hasDetails() {\n      return !isEmpty(this.details);\n    },\n\n    hasLabels() {\n      return !isEmpty(this.labels);\n    },\n\n    hasAnnotations() {\n      return !isEmpty(this.annotations);\n    },\n\n    hasDescription() {\n      return !isEmpty(this.description);\n    },\n\n    hasNamespaces() {\n      return !isEmpty(this.namespaces);\n    },\n\n    annotationCount() {\n      return Object.keys(this.annotations || {}).length;\n    },\n\n    isEmpty() {\n      const hasAnything = this.hasDetails || this.hasLabels || this.hasAnnotations || this.hasDescription || this.hasNamespaces;\n\n      return !hasAnything;\n    },\n\n    showFilteredSystemLabels() {\n      // It would be nicer to use hasSystemLabels here, but not all places have implemented it\n      // Instead check that there's a discrepancy between all labels and all labels without system ones\n      if (this.value?.labels && this.value?.filteredSystemLabels) {\n        const labelCount = Object.keys(this.value.labels).length;\n        const filteredSystemLabelsCount = Object.keys(this.value.filteredSystemLabels).length;\n\n        return labelCount !== filteredSystemLabelsCount;\n      }\n\n      return false;\n    },\n  },\n  methods: {\n    toggleLabels() {\n      this.showAllLabels = !this.showAllLabels;\n    },\n\n    toggleAnnotations(ev) {\n      this.annotationsVisible = !this.annotationsVisible;\n    }\n  }\n};\n</script>\n\n<template>\n  <div\n    class=\"detail-top\"\n    :class=\"{empty: isEmpty}\"\n  >\n    <div\n      v-if=\"hasNamespaces\"\n      class=\"labels\"\n    >\n      <span class=\"label\">\n        {{ t('resourceDetail.detailTop.namespaces') }}:\n      </span>\n      <span>\n        <router-link\n          v-for=\"namespace in namespaces\"\n          :key=\"namespace.name\"\n          :to=\"namespace.detailLocation\"\n          class=\"namespaceLinkList\"\n        >\n          {{ namespace.name }}\n        </router-link>\n      </span>\n    </div>\n\n    <div\n      v-if=\"description\"\n      class=\"description\"\n    >\n      <span class=\"label\">\n        {{ t('resourceDetail.detailTop.description') }}:\n      </span>\n      <span class=\"content\">{{ description }}</span>\n    </div>\n\n    <div v-if=\"hasDetails\">\n      <div\n        v-for=\"group, index in details\"\n        :key=\"index\"\n        class=\"details\"\n      >\n        <div\n          v-for=\"(detail, i) in group\"\n          :key=\"i\"\n          class=\"detail\"\n        >\n          <span class=\"label\">\n            {{ detail.label }}:\n          </span>\n          <component\n            :is=\"detail.formatter\"\n            v-if=\"detail.formatter\"\n            :value=\"detail.content\"\n            v-bind=\"detail.formatterOpts\"\n          />\n          <span v-else>{{ detail.content }}</span>\n        </div>\n      </div>\n    </div>\n\n    <div\n      v-if=\"hasLabels\"\n      class=\"labels\"\n    >\n      <div class=\"tags\">\n        <span class=\"label\">\n          {{ t('resourceDetail.detailTop.labels') }}:\n        </span>\n        <Tag\n          v-for=\"(prop, key) in labels\"\n          :key=\"key\"\n        >\n          <i\n            v-if=\"internalIcons[key]\"\n            class=\"icon\"\n            :class=\"internalIcons[key]\"\n          />\n          <span\n            v-if=\"internalTooltips[key]\"\n            v-clean-tooltip=\"prop ? `${key} : ${prop}` : key\"\n          >\n            <span>{{ internalTooltips[key] ? internalTooltips[key] : key }}</span>\n            <span v-if=\"showAllLabels\">: {{ key }}</span>\n          </span>\n          <span v-else>{{ prop ? `${key} : ${prop}` : key }}</span>\n        </Tag>\n        <a\n          v-if=\"showFilteredSystemLabels\"\n          href=\"#\"\n          class=\"detail-top__label-button\"\n          @click.prevent=\"toggleLabels\"\n        >\n          {{ t(`resourceDetail.detailTop.${showAllLabels? 'hideLabels' : 'showLabels'}`) }}\n        </a>\n      </div>\n    </div>\n\n    <div\n      v-if=\"hasAnnotations\"\n      class=\"annotations\"\n    >\n      <span class=\"label\">\n        {{ t('resourceDetail.detailTop.annotations') }}:\n      </span>\n      <a\n        href=\"#\"\n        @click.prevent=\"toggleAnnotations\"\n      >\n        {{ t(`resourceDetail.detailTop.${annotationsVisible? 'hideAnnotations' : 'showAnnotations'}`, {annotations: annotationCount}) }}\n      </a>\n      <div v-if=\"annotationsVisible\">\n        <DetailText\n          v-for=\"(val, key) in annotations\"\n          :key=\"key\"\n          class=\"annotation\"\n          :value=\"val\"\n          :label=\"key\"\n        />\n      </div>\n    </div>\n\n    <!-- Extensions area -->\n    <ExtensionPanel\n      :resource=\"value\"\n      :type=\"extensionType\"\n      :location=\"extensionLocation\"\n    />\n  </div>\n</template>\n\n<style lang=\"scss\">\n  .detail-top {\n    $spacing: 4px;\n\n    &:not(.empty) {\n      // Flip of .masthead padding/margin\n      padding-top: 10px;\n      border-top: 1px solid var(--border);\n      margin-top: 10px;\n    }\n\n    .namespaceLinkList:not(:first-child):before {\n      content: \", \";\n    }\n\n    .tags {\n      display: inline-flex;\n      flex-direction: row;\n      flex-wrap: wrap;\n      position: relative;\n      top: $spacing * math.div(-1, 2);\n\n      .label {\n        position: relative;\n        top: $spacing;\n      }\n\n      .tag {\n        margin: math.div($spacing, 2) $spacing 0 math.div($spacing, 2);\n        font-size: 12px;\n      }\n    }\n\n    .annotation {\n      margin-top: 10px;\n    }\n\n    .label {\n      color: var(--input-label);\n      margin: 0 4px 0 0;\n    }\n\n    &__label-button {\n      padding: 4px;\n    }\n\n    .details {\n      display: flex;\n      flex-direction: row;\n      flex-wrap: wrap;\n\n      .detail {\n        margin-right: 20px;\n        margin-bottom: 3px;\n      }\n      &:not(:first-of-type) {\n        margin-top: 3px;\n      }\n    }\n\n    & > div {\n      &:not(:last-of-type) {\n        margin-bottom: $spacing;\n      }\n    }\n\n    .icon {\n      vertical-align: top;\n    }\n  }\n</style>\n"]}]}