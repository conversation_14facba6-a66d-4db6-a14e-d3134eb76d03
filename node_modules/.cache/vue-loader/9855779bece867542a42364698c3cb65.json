{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SortableTable/THead.vue?vue&type=style&index=1&id=06369a08&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SortableTable/THead.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgICAudGFibGUtb3B0aW9ucy1jaGVja2JveCAuY2hlY2tib3gtY3VzdG9tIHsKICAgICAgbWluLXdpZHRoOiAxNHB4OwogICAgfQogICAgLnRhYmxlLW9wdGlvbnMtY2hlY2tib3ggLmNoZWNrYm94LWxhYmVsIHsKICAgICAgY29sb3I6IHZhcigtLWJvZHktdGV4dCk7CiAgICB9CiAg"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SortableTable/THead.vue"], "names": [], "mappings": ";IAygBI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SortableTable/THead.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { Checkbox } from '@components/Form/Checkbox';\nimport { SOME, NONE } from './selection';\nimport { AUTO, CENTER, fitOnScreen } from '@shell/utils/position';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\n\nexport default {\n  emits: ['update-cols-options', 'on-toggle-all', 'group-value-change', 'on-sort-change', 'col-visibility-change'],\n\n  components: { Checkbox, LabeledSelect },\n  props:      {\n    columns: {\n      type:     Array,\n      required: true\n    },\n    sortBy: {\n      type:     String,\n      required: true\n    },\n    defaultSortBy: {\n      type:    String,\n      default: ''\n    },\n    group: {\n      type:    String,\n      default: ''\n    },\n    groupOptions: {\n      type:    Array,\n      default: () => []\n    },\n    descending: {\n      type:     Boolean,\n      required: true\n    },\n    hasAdvancedFiltering: {\n      type:     Boolean,\n      required: false\n    },\n    tableColsOptions: {\n      type:    Array,\n      default: () => [],\n    },\n    tableActions: {\n      type:     Boolean,\n      required: true,\n    },\n    rowActions: {\n      type:     Boolean,\n      required: true,\n    },\n    howMuchSelected: {\n      type:     String,\n      required: true,\n    },\n    checkWidth: {\n      type:    Number,\n      default: 30,\n    },\n    rowActionsWidth: {\n      type:     Number,\n      required: true\n    },\n    subExpandColumn: {\n      type:    Boolean,\n      default: false,\n    },\n    expandWidth: {\n      type:    Number,\n      default: 30,\n    },\n    labelFor: {\n      type:     Function,\n      required: true,\n    },\n    noRows: {\n      type:    Boolean,\n      default: true,\n    },\n    noResults: {\n      type:    Boolean,\n      default: true,\n    },\n    loading: {\n      type:     Boolean,\n      required: false,\n    },\n  },\n\n  data() {\n    return {\n      tableColsOptionsVisibility: false,\n      tableColsMenuPosition:      null\n    };\n  },\n\n  watch: {\n    advancedFilteringValues() {\n      // passing different dummy args to make sure update is triggered\n      this.watcherUpdateLiveAndDelayed(true, false);\n    },\n    tableColsOptionsVisibility(neu) {\n      if (neu) {\n        // check if user clicked outside the table cols options box\n        window.addEventListener('click', this.onClickOutside);\n\n        // update filtering options and toggable cols every time dropdown is open\n        this.$emit('update-cols-options');\n      } else {\n        // unregister click event\n        window.removeEventListener('click', this.onClickOutside);\n      }\n    }\n  },\n  computed: {\n    isAll: {\n      get() {\n        return this.howMuchSelected !== NONE;\n      },\n\n      set(value) {\n        this.$emit('on-toggle-all', value);\n      }\n    },\n    hasAdvGrouping() {\n      return this.group?.length && this.groupOptions?.length;\n    },\n    advGroup: {\n      get() {\n        return this.group || this.advGroup;\n      },\n\n      set(val) {\n        this.$emit('group-value-change', val);\n      }\n    },\n\n    isIndeterminate() {\n      return this.howMuchSelected === SOME;\n    },\n    hasColumnWithSubLabel() {\n      return this.columns.some((col) => col.subLabel);\n    }\n  },\n\n  methods: {\n    changeSort(e, col) {\n      if ( !col.sort ) {\n        return;\n      }\n\n      let desc = false;\n\n      if ( this.sortBy === col.name ) {\n        desc = !this.descending;\n      }\n\n      this.$emit('on-sort-change', col.name, desc);\n    },\n\n    isCurrent(col) {\n      return col.name === this.sortBy;\n    },\n\n    ariaSort(col) {\n      if (this.isCurrent(col)) {\n        return this.descending ? this.t('generic.descending') : this.t('generic.ascending');\n      }\n\n      return this.t('generic.none');\n    },\n\n    tableColsOptionsClick(ev) {\n      // set menu position\n      const menu = document.querySelector('.table-options-container');\n      const elem = document.querySelector('.table-options-btn');\n\n      this.tableColsMenuPosition = fitOnScreen(menu, ev || elem, {\n        overlapX:  true,\n        fudgeX:    326,\n        fudgeY:    -22,\n        positionX: CENTER,\n        positionY: AUTO,\n      });\n\n      // toggle visibility\n      this.tableColsOptionsVisibility = !this.tableColsOptionsVisibility;\n    },\n\n    onClickOutside(event) {\n      const tableOpts = this.$refs['table-options'];\n\n      if (!tableOpts || tableOpts.contains(event.target)) {\n        return;\n      }\n      this.tableColsOptionsVisibility = false;\n    },\n\n    tableOptionsCheckbox(value, label) {\n      this.$emit('col-visibility-change', {\n        label,\n        value\n      });\n    },\n\n    tooltip(col) {\n      if (!col.tooltip) {\n        return null;\n      }\n\n      const exists = this.$store.getters['i18n/exists'];\n\n      return exists(col.tooltip) ? this.t(col.tooltip) : col.tooltip;\n    },\n  }\n\n};\n</script>\n\n<template>\n  <thead>\n    <tr :class=\"{'loading': loading, 'top-aligned': hasColumnWithSubLabel}\">\n      <th\n        v-if=\"tableActions\"\n        :width=\"checkWidth\"\n      >\n        <Checkbox\n          v-model:value=\"isAll\"\n          class=\"check\"\n          data-testid=\"sortable-table_check_select_all\"\n          :indeterminate=\"isIndeterminate\"\n          :disabled=\"noRows || noResults\"\n          :alternate-label=\"t('sortableTable.genericGroupCheckbox')\"\n        />\n      </th>\n      <th\n        v-if=\"subExpandColumn\"\n        :width=\"expandWidth\"\n      />\n      <th\n        v-for=\"(col) in columns\"\n        v-show=\"!hasAdvancedFiltering || (hasAdvancedFiltering && col.isColVisible)\"\n        :key=\"col.name\"\n        :align=\"col.align || 'left'\"\n        :width=\"col.width\"\n        :class=\"{ sortable: col.sort, [col.breakpoint]: !!col.breakpoint}\"\n        :tabindex=\"col.sort ? 0 : -1\"\n        class=\"sortable-table-head-element\"\n        :aria-sort=\"ariaSort(col)\"\n        @click.prevent=\"changeSort($event, col)\"\n        @keyup.enter=\"changeSort($event, col)\"\n        @keyup.space=\"changeSort($event, col)\"\n      >\n        <div\n          class=\"table-header-container\"\n          :class=\"{ 'not-filterable': hasAdvancedFiltering && !col.isFilter }\"\n        >\n          <div\n            v-clean-tooltip=\"tooltip(col)\"\n            class=\"content\"\n          >\n            <span v-clean-html=\"labelFor(col)\" />\n            <span\n              v-if=\"col.subLabel\"\n              class=\"text-muted\"\n            >\n              {{ col.subLabel }}\n            </span>\n          </div>\n          <div\n            v-if=\"col.sort\"\n            class=\"sort\"\n            aria-hidden=\"true\"\n          >\n            <i\n              v-show=\"hasAdvancedFiltering && !col.isFilter\"\n              v-clean-tooltip=\"t('sortableTable.tableHeader.noFilter')\"\n              class=\"icon icon-info not-filter-icon\"\n            />\n            <span class=\"icon-stack\">\n              <i class=\"icon icon-sort icon-stack-1x faded\" />\n              <i\n                v-if=\"isCurrent(col) && !descending\"\n                class=\"icon icon-sort-down icon-stack-1x\"\n                :alt=\"t('sortableTable.alt.sortingIconDesc')\"\n              />\n              <i\n                v-if=\"isCurrent(col) && descending\"\n                class=\"icon icon-sort-up icon-stack-1x\"\n                :alt=\"t('sortableTable.alt.sortingIconAsc')\"\n              />\n            </span>\n          </div>\n        </div>\n      </th>\n      <th\n        v-if=\"rowActions && hasAdvancedFiltering && tableColsOptions.length\"\n        :width=\"rowActionsWidth\"\n      >\n        <div\n          ref=\"table-options\"\n          class=\"table-options-group\"\n        >\n          <button\n            aria-haspopup=\"true\"\n            aria-expanded=\"false\"\n            type=\"button\"\n            class=\"btn btn-sm role-multi-action table-options-btn\"\n            @click=\"tableColsOptionsClick\"\n          >\n            <i class=\"icon icon-actions\" />\n          </button>\n          <div\n            v-show=\"tableColsOptionsVisibility\"\n            class=\"table-options-container\"\n            :style=\"tableColsMenuPosition\"\n          >\n            <div\n              v-if=\"hasAdvGrouping\"\n              class=\"table-options-grouping\"\n            >\n              <span class=\"table-options-col-subtitle\">{{ t('sortableTable.tableHeader.groupBy') }}:</span>\n              <LabeledSelect\n                v-model:value=\"advGroup\"\n                class=\"table-options-grouping-select\"\n                :clearable=\"true\"\n                :options=\"groupOptions\"\n                :disabled=\"false\"\n                :searchable=\"false\"\n                mode=\"edit\"\n                :multiple=\"false\"\n                :taggable=\"false\"\n              />\n            </div>\n            <p class=\"table-options-col-subtitle mb-20\">\n              {{ t('sortableTable.tableHeader.show') }}:\n            </p>\n            <ul>\n              <li\n                v-for=\"(col, index) in tableColsOptions\"\n                v-show=\"col.isTableOption\"\n                :key=\"index\"\n                :class=\"{ 'visible': !col.preventColToggle }\"\n              >\n                <Checkbox\n                  v-show=\"!col.preventColToggle\"\n                  v-model:value=\"col.isColVisible\"\n                  class=\"table-options-checkbox\"\n                  :label=\"col.label\"\n                  @update:value=\"tableOptionsCheckbox($event, col.label)\"\n                />\n              </li>\n            </ul>\n          </div>\n        </div>\n      </th>\n      <th\n        v-else-if=\"rowActions\"\n        :width=\"rowActionsWidth\"\n      />\n    </tr>\n  </thead>\n</template>\n\n  <style lang=\"scss\" scoped>\n    .table-options-group {\n\n      .table-options-btn.role-multi-action {\n        background-color: transparent;\n        border: none;\n        font-size: 18px;\n        &:hover, &:focus {\n          background-color: var(--accent-btn);\n          box-shadow: none;\n        }\n      }\n      .table-options-container {\n        width: 350px;\n        border: 1px solid var(--primary);\n        background-color: var(--body-bg);\n        padding: 20px;\n        z-index: 1;\n\n        .table-options-grouping {\n          display: flex;\n          align-items: center;\n          margin-bottom: 20px;\n\n          span {\n            white-space: nowrap;\n            margin-right: 10px;\n          }\n        }\n\n        ul {\n          list-style: none;\n          margin: 0;\n          padding: 0;\n          max-height: 200px;\n          overflow-y: auto;\n\n          li {\n            margin: 0;\n            padding: 0;\n\n            &.visible {\n              margin: 0 0 10px 0;\n            }\n          }\n        }\n      }\n    }\n\n    .sortable > SPAN {\n      cursor: pointer;\n      user-select: none;\n      white-space: nowrap;\n      &:hover,\n      &:active {\n        text-decoration: underline;\n        color: var(--body-text);\n      }\n    }\n\n    .top-aligned th {\n      vertical-align: top;\n      padding-top: 10px;\n    }\n\n    thead {\n      tr {\n        background-color: var(--sortable-table-header-bg);\n        color: var(--body-text);\n        text-align: left;\n        border-bottom: 1px solid var(--sortable-table-top-divider);\n      }\n    }\n\n    th {\n      padding: 8px 5px;\n      font-weight: normal;\n      border: 0;\n      color: var(--body-text);\n\n      &.sortable-table-head-element:focus-visible {\n        @include focus-outline;\n        outline-offset: -4px;\n      }\n\n      .table-header-container {\n        display: inline-flex;\n\n        .content {\n          display: flex;\n          flex-direction: column;\n        }\n\n        &.not-filterable {\n          margin-top: -2px;\n\n          .icon-stack {\n            margin-top: -2px;\n          }\n        }\n\n        .not-filter-icon {\n          font-size: 16px;\n          color: var(--primary);\n          vertical-align: super;\n        }\n      }\n\n      &:first-child {\n        padding-left: 10px;\n      }\n\n      &:last-child {\n        padding-right: 10px;\n      }\n\n      &:not(.sortable) > SPAN {\n        display: block;\n        margin-bottom: 2px;\n      }\n\n      & A {\n        color: var(--body-text);\n      }\n\n      // Aligns with COLUMN_BREAKPOINTS\n      @media only screen and (max-width: map-get($breakpoints, '--viewport-4')) {\n        // HIDE column on sizes below 480px\n        &.tablet, &.laptop, &.desktop {\n          display: none;\n        }\n      }\n      @media only screen and (max-width: map-get($breakpoints, '--viewport-9')) {\n        // HIDE column on sizes below 992px\n        &.laptop, &.desktop {\n          display: none;\n        }\n      }\n      @media only screen and (max-width: map-get($breakpoints, '--viewport-12')) {\n        // HIDE column on sizes below 1281px\n        &.desktop {\n          display: none;\n        }\n      }\n    }\n\n    .icon-stack {\n      width: 12px;\n    }\n\n    .icon-sort {\n      &.faded {\n        opacity: .3;\n      }\n    }\n  </style>\n  <style lang=\"scss\">\n    .table-options-checkbox .checkbox-custom {\n      min-width: 14px;\n    }\n    .table-options-checkbox .checkbox-label {\n      color: var(--body-text);\n    }\n  </style>\n"]}]}