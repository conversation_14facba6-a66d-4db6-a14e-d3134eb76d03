{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SelectOrCreateAuthSecret.vue?vue&type=style&index=0&id=6b37ac6b&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SelectOrCreateAuthSecret.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5zZWxlY3Qtb3ItY3JlYXRlLWF1dGgtc2VjcmV0IGRpdi5sYWJlbGVkLXNlbGVjdCB7CiAgbWluLWhlaWdodDogJGlucHV0LWhlaWdodDsKfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SelectOrCreateAuthSecret.vue"], "names": [], "mappings": ";AAirBA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SelectOrCreateAuthSecret.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { _EDIT } from '@shell/config/query-params';\nimport { Banner } from '@components/Banner';\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport SSHKnownHosts from '@shell/components/form/SSHKnownHosts';\nimport { AUTH_TYPE, NORMAN, SECRET } from '@shell/config/types';\nimport { SECRET_TYPES } from '@shell/config/secret';\nimport { base64Encode } from '@shell/utils/crypto';\nimport { addObjects, insertAt } from '@shell/utils/array';\nimport { sortBy } from '@shell/utils/sort';\nimport {\n  FilterArgs,\n  PaginationFilterField,\n  PaginationParamFilter,\n} from '@shell/types/store/pagination.types';\n\nexport default {\n  name: 'SelectOrCreateAuthSecret',\n\n  emits: ['inputauthval', 'update:value'],\n\n  components: {\n    Banner,\n    LabeledInput,\n    LabeledSelect,\n    SSHKnownHosts,\n  },\n\n  props: {\n    mode: {\n      type:    String,\n      default: _EDIT,\n    },\n\n    preSelect: {\n      type:    Object,\n      default: null,\n    },\n\n    value: {\n      type:    [String, Object],\n      default: null,\n    },\n\n    inStore: {\n      type:    String,\n      default: 'cluster',\n    },\n\n    labelKey: {\n      type:    String,\n      default: 'selectOrCreateAuthSecret.label',\n    },\n\n    namespace: {\n      type:     String,\n      required: true,\n    },\n\n    /**\n     * Limit the selection of an existing secret to the namespace provided\n     */\n    limitToNamespace: {\n      type:    Boolean,\n      default: true,\n    },\n\n    generateName: {\n      type:    String,\n      default: 'auth-',\n    },\n\n    allowNone: {\n      type:    Boolean,\n      default: true,\n    },\n\n    allowSsh: {\n      type:    Boolean,\n      default: true,\n    },\n\n    allowBasic: {\n      type:    Boolean,\n      default: true,\n    },\n\n    allowS3: {\n      type:    Boolean,\n      default: false,\n    },\n\n    allowRke: {\n      type:    Boolean,\n      default: false,\n    },\n\n    registerBeforeHook: {\n      type:     Function,\n      required: true,\n    },\n\n    hookName: {\n      type:    String,\n      default: 'registerAuthSecret'\n    },\n\n    appendUniqueIdToHook: {\n      type:    Boolean,\n      default: false\n    },\n\n    hookPriority: {\n      type:    Number,\n      default: 99,\n    },\n\n    vertical: {\n      type:    Boolean,\n      default: false,\n    },\n\n    /**\n     * This component is used in MultiStep Process\n     * So when user click through to final step and submit the form\n     * This component get recreated therefore register `doCreate` as a hook each time\n     * Also, the parent step component is not aware that credential is created\n     *\n     * This property is implement to prevent this issue and delegate it to parent component.\n     */\n    delegateCreateToParent: {\n      type:    Boolean,\n      default: false,\n    },\n\n    /**\n     * Set to false to make a fresh http request to secrets every time. This can be used when secrets have already been used for\n     * another purpose on the same page\n     *\n     * Set to true to cache the response\n     */\n    cacheSecrets: {\n      type:    Boolean,\n      default: false,\n    },\n\n    showSshKnownHosts: {\n      type:    Boolean,\n      default: false,\n    },\n  },\n\n  async fetch() {\n    if ( (this.allowSsh || this.allowBasic || this.allowRke) && this.$store.getters[`${ this.inStore }/schemaFor`](SECRET) ) {\n      if (this.$store.getters[`${ this.inStore }/paginationEnabled`](SECRET)) {\n        // Filter results via api (because we shouldn't be fetching them all...)\n        this.filteredSecrets = await this.filterSecretsByApi();\n      } else {\n        // Cannot yet filter via api, so fetch all and filter later on\n        this.allSecrets = await this.$store.dispatch(\n          `${ this.inStore }/findAll`,\n          { type: SECRET }\n        );\n      }\n    }\n\n    if ( this.allowS3 && this.$store.getters['rancher/canList'](NORMAN.CLOUD_CREDENTIAL) ) {\n      // Avoid an async call and loading screen if already loaded by someone else\n      if (this.$store.getters['rancher/haveAll'](NORMAN.CLOUD_CREDENTIAL)) {\n        this.allCloudCreds = this.$store.getters['rancher/all'](NORMAN.CLOUD_CREDENTIAL);\n      } else {\n        this.allCloudCreds = await this.$store.dispatch('rancher/findAll', { type: NORMAN.CLOUD_CREDENTIAL });\n      }\n    } else {\n      this.allCloudCreds = [];\n    }\n\n    if ( !this.value ) {\n      this.publicKey = this.preSelect?.publicKey || '';\n      this.privateKey = this.preSelect?.privateKey || '';\n      this.sshKnownHosts = this.preSelect?.sshKnownHosts || '';\n    }\n\n    this.updateSelectedFromValue();\n    this.update();\n  },\n\n  data() {\n    return {\n      allCloudCreds: [],\n\n      allSecrets:      null,\n      filteredSecrets: null,\n\n      selected: null,\n\n      filterByNamespace: this.namespace && this.limitToNamespace,\n\n      publicKey:     '',\n      privateKey:    '',\n      sshKnownHosts: '',\n      uniqueId:      new Date().getTime(), // Allows form state to be individually tracked if the form is in a list\n\n      SSH:   AUTH_TYPE._SSH,\n      BASIC: AUTH_TYPE._BASIC,\n      S3:    AUTH_TYPE._S3,\n      RKE:   AUTH_TYPE._RKE,\n    };\n  },\n\n  computed: {\n    secretTypes() {\n      const types = [];\n\n      if ( this.allowSsh ) {\n        types.push(SECRET_TYPES.SSH);\n      }\n\n      if ( this.allowBasic ) {\n        types.push(SECRET_TYPES.BASIC);\n      }\n\n      if ( this.allowRke ) {\n        types.push(SECRET_TYPES.RKE_AUTH_CONFIG);\n      }\n\n      return types;\n    },\n\n    /**\n     * Fitler secrets given their namespace and required secret type\n     *\n     * Convert secrets to list of options and suplement with custom entries\n     */\n    options() {\n      let filteredSecrets = [];\n\n      if (this.allSecrets) {\n        // Fitler secrets given their namespace and required secret type\n        filteredSecrets = this.allSecrets\n          .filter((x) => this.filterByNamespace ? x.metadata.namespace === this.namespace : true\n          )\n          .filter((x) => {\n            // Must match one of the required types\n            if (\n              this.secretTypes.length &&\n              !this.secretTypes.includes(x._type)\n            ) {\n              return false;\n            }\n\n            return true;\n          });\n      } else if (this.filteredSecrets) {\n        filteredSecrets = this.filteredSecrets;\n      }\n\n      let out = filteredSecrets.map((x) => {\n        const {\n          dataPreview, subTypeDisplay, metadata, id\n        } = x;\n\n        const label =\n          subTypeDisplay && dataPreview ? `${ metadata.name } (${ subTypeDisplay }: ${ dataPreview })` : `${ metadata.name } (${ subTypeDisplay })`;\n\n        return {\n          label,\n          group: metadata.namespace,\n          value: id,\n        };\n      });\n\n      if (this.allowS3) {\n        const more = this.allCloudCreds\n          .filter((x) => ['aws', 's3'].includes(x.provider))\n          .map((x) => {\n            return {\n              label: `${ x.nameDisplay } (${ x.providerDisplay })`,\n              group: 'Cloud Credentials',\n              value: x.id,\n            };\n          });\n\n        addObjects(out, more);\n      }\n\n      if ( !this.limitToNamespace ) {\n        out = sortBy(out, 'group');\n        if ( out.length ) {\n          let lastGroup = '';\n\n          for ( let i = 0 ; i < out.length ; i++ ) {\n            if ( out[i].group !== lastGroup ) {\n              lastGroup = out[i].group;\n\n              insertAt(out, i, {\n                kind:     'title',\n                label:    this.t('selectOrCreateAuthSecret.namespaceGroup', { name: lastGroup }),\n                disabled: true,\n              });\n\n              i++;\n            }\n          }\n        }\n      }\n\n      if ( out.length ) {\n        out.unshift({\n          kind:     'title',\n          label:    this.t('selectOrCreateAuthSecret.chooseExisting'),\n          disabled: true\n        });\n      }\n      if ( this.allowNone ) {\n        out.unshift({\n          label: this.t('generic.none'),\n          value: AUTH_TYPE._NONE,\n        });\n      }\n\n      if (this.allowSsh || this.allowS3 || this.allowBasic || this.allowRke) {\n        out.unshift({\n          label:    'divider',\n          disabled: true,\n          kind:     'divider'\n        });\n      }\n\n      if ( this.allowSsh ) {\n        out.unshift({\n          label: this.t('selectOrCreateAuthSecret.createSsh'),\n          value: AUTH_TYPE._SSH,\n          kind:  'highlighted'\n        });\n      }\n\n      if ( this.allowS3 ) {\n        out.unshift({\n          label: this.t('selectOrCreateAuthSecret.createS3'),\n          value: AUTH_TYPE._S3,\n          kind:  'highlighted'\n        });\n      }\n\n      if ( this.allowBasic ) {\n        out.unshift({\n          label: this.t('selectOrCreateAuthSecret.createBasic'),\n          value: AUTH_TYPE._BASIC,\n          kind:  'highlighted'\n        });\n      }\n\n      // Note here about order\n      if ( this.allowRke ) {\n        out.unshift({\n          label: this.t('selectOrCreateAuthSecret.createRKE'),\n          value: AUTH_TYPE._RKE,\n          kind:  'highlighted'\n        });\n      }\n\n      return out;\n    },\n\n    firstCol() {\n      if ( this.vertical ) {\n        return '';\n      }\n\n      if ( this.selected === AUTH_TYPE._SSH || this.selected === AUTH_TYPE._BASIC || this.selected === AUTH_TYPE._RKE || this.selected === AUTH_TYPE._S3 ) {\n        return 'col span-4';\n      }\n\n      return 'col span-6';\n    },\n\n    moreCols() {\n      if ( this.vertical ) {\n        return 'mt-20';\n      }\n\n      return (this.selected === AUTH_TYPE._SSH) && this.showSshKnownHosts ? 'col span-3' : 'col span-4';\n    }\n  },\n\n  watch: {\n    selected:      'update',\n    publicKey:     'updateKeyVal',\n    privateKey:    'updateKeyVal',\n    sshKnownHosts: 'updateKeyVal',\n    value:         'updateSelectedFromValue',\n\n    async namespace(ns) {\n      if (ns && !this.selected.startsWith(`${ ns }/`)) {\n        this.selected = AUTH_TYPE._NONE;\n      }\n\n      // if ns has changed and we're filtering by api... we need to re-fetch entries\n      if (this.filteredSecrets && this.filterByNamespace) {\n        this.filteredSecrets = await this.filterSecretsByApi();\n      }\n    },\n  },\n\n  created() {\n    if (this.registerBeforeHook) {\n      const hookName = this.appendUniqueIdToHook ? this.hookName + this.uniqueId : this.hookName;\n\n      if (!this.delegateCreateToParent) {\n        this.registerBeforeHook(this.doCreate, hookName, this.hookPriority);\n      }\n    } else {\n      throw new Error('Before Hook is missing');\n    }\n  },\n\n  methods: {\n    updateSelectedFromValue() {\n      let selected = this.preSelect?.selected || AUTH_TYPE._NONE;\n\n      if ( this.value ) {\n        if ( typeof this.value === 'object' ) {\n          selected = `${ this.value.namespace }/${ this.value.name }`;\n        } else if ( this.value.includes('/') || this.value.includes(':') ) {\n          selected = this.value;\n        } else if ( this.namespace ) {\n          selected = `${ this.namespace }/${ this.value }`;\n        } else {\n          selected = this.value;\n        }\n      }\n\n      this.selected = selected;\n    },\n    async filterSecretsByApi() {\n      const findPageArgs = {\n        // Of type ActionFindPageArgs\n        namespaced: this.filterByNamespace ? this.namespace : '',\n        pagination: new FilterArgs({\n          filters: [\n            PaginationParamFilter.createMultipleFields(\n              this.secretTypes.map(\n                (t) => new PaginationFilterField({\n                  field: 'metadata.fields.1',\n                  value: t,\n                })\n              )\n            ),\n          ],\n        }),\n      };\n\n      if (this.cacheSecrets) {\n        return await this.$store.dispatch(`${ this.inStore }/findPage`, {\n          type: SECRET,\n          opt:  findPageArgs,\n        });\n      }\n\n      const url = this.$store.getters[`${ this.inStore }/urlFor`](\n        SECRET,\n        null,\n        findPageArgs\n      );\n      const res = await this.$store.dispatch(`cluster/request`, { url });\n\n      return res?.data || [];\n    },\n\n    updateKeyVal() {\n      if ( ![AUTH_TYPE._SSH, AUTH_TYPE._BASIC, AUTH_TYPE._S3, AUTH_TYPE._RKE].includes(this.selected) ) {\n        this.privateKey = '';\n        this.publicKey = '';\n        this.sshKnownHosts = '';\n      }\n\n      const value = {\n        selected:   this.selected,\n        privateKey: this.privateKey,\n        publicKey:  this.publicKey,\n      };\n\n      if (this.sshKnownHosts) {\n        value.sshKnownHosts = this.sshKnownHosts;\n      }\n\n      this.$emit('inputauthval', value);\n    },\n\n    update() {\n      if ( (!this.selected || [AUTH_TYPE._SSH, AUTH_TYPE._BASIC, AUTH_TYPE._S3, AUTH_TYPE._RKE, AUTH_TYPE._NONE].includes(this.selected))) {\n        this.$emit('update:value', null);\n      } else if ( this.selected.includes(':') ) {\n        // Cloud creds\n        this.$emit('update:value', this.selected);\n      } else {\n        const split = this.selected.split('/');\n\n        if ( this.limitToNamespace ) {\n          this.$emit('update:value', split[1]);\n        } else {\n          const out = {\n            namespace: split[0],\n            name:      split[1]\n          };\n\n          this.$emit('update:value', out);\n        }\n      }\n\n      this.updateKeyVal();\n    },\n\n    async doCreate() {\n      if ( ![AUTH_TYPE._SSH, AUTH_TYPE._BASIC, AUTH_TYPE._S3, AUTH_TYPE._RKE].includes(this.selected) || this.delegateCreateToParent ) {\n        return;\n      }\n\n      let secret;\n\n      if ( this.selected === AUTH_TYPE._S3 ) {\n        secret = await this.$store.dispatch(`rancher/create`, {\n          type:               NORMAN.CLOUD_CREDENTIAL,\n          s3credentialConfig: {\n            accessKey: this.publicKey,\n            secretKey: this.privateKey,\n          },\n        });\n      } else {\n        secret = await this.$store.dispatch(`${ this.inStore }/create`, {\n          type:     SECRET,\n          metadata: {\n            namespace:    this.namespace,\n            generateName: this.generateName\n          },\n        });\n\n        let type, publicField, privateField;\n\n        switch ( this.selected ) {\n        case AUTH_TYPE._SSH:\n          type = SECRET_TYPES.SSH;\n          publicField = 'ssh-publickey';\n          privateField = 'ssh-privatekey';\n          break;\n        case AUTH_TYPE._BASIC:\n          type = SECRET_TYPES.BASIC;\n          publicField = 'username';\n          privateField = 'password';\n          break;\n        case AUTH_TYPE._RKE:\n          type = SECRET_TYPES.RKE_AUTH_CONFIG;\n          // Set the 'auth' key to be the base64 of the username and password concatenated with a ':' character\n          secret.data = { auth: base64Encode(`${ this.publicKey }:${ this.privateKey }`) };\n          break;\n        default:\n          throw new Error('Unknown type');\n        }\n\n        secret._type = type;\n\n        // Set the data if not set by one of the specific cases above\n        if (!secret.data) {\n          secret.data = {\n            [publicField]:  base64Encode(this.publicKey),\n            [privateField]: base64Encode(this.privateKey),\n          };\n\n          // Add ssh known hosts data key - we will add a key with an empty value if the inout field was left blank\n          // This ensures on edit of the secret, we allow the user to edit the known_hosts field\n          if ((this.selected === AUTH_TYPE._SSH) && this.showSshKnownHosts) {\n            secret.data.known_hosts = base64Encode(this.sshKnownHosts || '');\n          }\n        }\n      }\n\n      await secret.save();\n\n      await this.$nextTick(() => {\n        this.selected = secret.id;\n      });\n\n      return secret;\n    },\n  },\n};\n</script>\n\n<template>\n  <div\n    class=\"select-or-create-auth-secret\"\n  >\n    <div\n      class=\"mt-20\"\n      :class=\"{'row': !vertical}\"\n    >\n      <div :class=\"firstCol\">\n        <LabeledSelect\n          v-model:value=\"selected\"\n          data-testid=\"auth-secret-select\"\n          :mode=\"mode\"\n          :label-key=\"labelKey\"\n          :loading=\"$fetchState.pending\"\n          :options=\"options\"\n          :selectable=\"option => !option.disabled\"\n        />\n      </div>\n      <template v-if=\"selected === SSH\">\n        <div :class=\"moreCols\">\n          <LabeledInput\n            v-model:value=\"publicKey\"\n            data-testid=\"auth-secret-ssh-public-key\"\n            :mode=\"mode\"\n            type=\"multiline\"\n            label-key=\"selectOrCreateAuthSecret.ssh.publicKey\"\n          />\n        </div>\n        <div :class=\"moreCols\">\n          <LabeledInput\n            v-model:value=\"privateKey\"\n            data-testid=\"auth-secret-ssh-private-key\"\n            :mode=\"mode\"\n            type=\"multiline\"\n            label-key=\"selectOrCreateAuthSecret.ssh.privateKey\"\n          />\n        </div>\n        <div\n          v-if=\"showSshKnownHosts\"\n          class=\"col span-2\"\n        >\n          <SSHKnownHosts\n            v-model:value=\"sshKnownHosts\"\n            data-testid=\"auth-secret-known-ssh-hosts\"\n            :mode=\"mode\"\n          />\n        </div>\n      </template>\n      <template v-else-if=\"selected === BASIC || selected === RKE\">\n        <Banner\n          v-if=\"selected === RKE\"\n          color=\"info\"\n          :class=\"moreCols\"\n        >\n          {{ t('selectOrCreateAuthSecret.rke.info', {}, true) }}\n        </Banner>\n        <div :class=\"moreCols\">\n          <LabeledInput\n            v-model:value=\"publicKey\"\n            data-testid=\"auth-secret-basic-username\"\n            :mode=\"mode\"\n            label-key=\"selectOrCreateAuthSecret.basic.username\"\n          />\n        </div>\n        <div :class=\"moreCols\">\n          <LabeledInput\n            v-model:value=\"privateKey\"\n            data-testid=\"auth-secret-basic-password\"\n            :mode=\"mode\"\n            type=\"password\"\n            label-key=\"selectOrCreateAuthSecret.basic.password\"\n          />\n        </div>\n      </template>\n      <template v-else-if=\"selected === S3\">\n        <div :class=\"moreCols\">\n          <LabeledInput\n            v-model:value=\"publicKey\"\n            data-testid=\"auth-secret-s3-public-key\"\n            :mode=\"mode\"\n            label-key=\"selectOrCreateAuthSecret.s3.accessKey\"\n          />\n        </div>\n        <div :class=\"moreCols\">\n          <LabeledInput\n            v-model:value=\"privateKey\"\n            data-testid=\"auth-secret-s3-private-key\"\n            :mode=\"mode\"\n            type=\"password\"\n            label-key=\"selectOrCreateAuthSecret.s3.secretKey\"\n          />\n        </div>\n      </template>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\">\n.select-or-create-auth-secret div.labeled-select {\n  min-height: $input-height;\n}\n</style>\n"]}]}