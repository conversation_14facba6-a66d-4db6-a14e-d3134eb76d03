{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdown.vue?vue&type=style&index=0&id=cd7e8d90&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdown.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLnBvcHBlckNvbnRhaW5lciB7CiAgICBkaXNwbGF5OiBjb250ZW50czsKICAgICY6ZGVlcCgudi1wb3BwZXJfX3BvcHBlcikgewoKICAgICAgLnYtcG9wcGVyX193cmFwcGVyIHsKICAgICAgICBib3gtc2hhZG93OiAwcHggNnB4IDE4cHggMHB4IHJnYmEoMCwgMCwgMCwgMC4yNSksIDBweCA0cHggMTBweCAwcHggcmdiYSgwLCAwLCAwLCAwLjE1KTsKICAgICAgICBib3JkZXItcmFkaXVzOiB2YXIoLS1ib3JkZXItcmFkaXVzLWxnKTsKCiAgICAgICAgLnYtcG9wcGVyX19hcnJvdy1jb250YWluZXIgewogICAgICAgICAgZGlzcGxheTogbm9uZTsKICAgICAgICB9CgogICAgICAgIC52LXBvcHBlcl9faW5uZXIgewogICAgICAgICAgcGFkZGluZzogMTBweCAwIDEwcHggMDsKICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9CgogIC5kcm9wZG93blRhcmdldCB7CiAgICAmOmZvY3VzLXZpc2libGUsICY6Zm9jdXMgewogICAgICBvdXRsaW5lOiBub25lOwogICAgfQogIH0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdown.vue"], "names": [], "mappings": ";EAmGE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;MAExB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACxB;MACF;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdown.vue", "sourceRoot": "", "sourcesContent": ["<script setup lang=\"ts\">\n/**\n * Offers a list of choices to the user, such as a set of actions or functions.\n * Opened by activating RcDropdownTrigger.\n *\n * Example:\n *\n *  <rc-dropdown :aria-label=\"t('nav.actionMenu.label')\">\n *    <rc-dropdown-trigger tertiary>\n *      <i class=\"icon icon-actions\" />\n *    </rc-dropdown-trigger>\n *    <template #dropdownCollection>\n *      <rc-dropdown-item @click=\"performAction()\">\n *        Action 1\n *      </rc-dropdown-item>\n *      <rc-dropdown-separator />\n *      <rc-dropdown-item @click=\"performAction()\">\n *        Action 2\n *      </rc-dropdown-item>\n *    </template>\n *  </rc-dropdown>\n */\nimport { ref } from 'vue';\nimport { useClickOutside } from '@shell/composables/useClickOutside';\nimport { useDropdownContext } from '@components/RcDropdown/useDropdownContext';\n\ndefineProps<{\n  ariaLabel?: string\n}>();\n\nconst emit = defineEmits(['update:open']);\n\nconst {\n  isMenuOpen,\n  showMenu,\n  returnFocus,\n  setFocus,\n  provideDropdownContext,\n  registerDropdownCollection,\n  handleKeydown,\n} = useDropdownContext(emit);\n\nprovideDropdownContext();\n\nconst popperContainer = ref(null);\nconst dropdownTarget = ref(null);\n\nuseClickOutside(dropdownTarget, () => showMenu(false));\n\nconst applyShow = () => {\n  registerDropdownCollection(dropdownTarget.value);\n  setFocus();\n};\n\n</script>\n\n<template>\n  <v-dropdown\n    no-auto-focus\n    :triggers=\"[]\"\n    :shown=\"isMenuOpen\"\n    :auto-hide=\"false\"\n    :container=\"popperContainer\"\n    :placement=\"'bottom-end'\"\n    @apply-show=\"applyShow\"\n  >\n    <slot name=\"default\">\n      <!--Empty slot content Trigger-->\n    </slot>\n\n    <template #popper>\n      <div\n        ref=\"dropdownTarget\"\n        class=\"dropdownTarget\"\n        tabindex=\"-1\"\n        role=\"menu\"\n        aria-orientation=\"vertical\"\n        dropdown-menu-collection\n        :aria-label=\"ariaLabel || 'Dropdown Menu'\"\n        @keydown=\"handleKeydown\"\n        @keydown.down=\"setFocus()\"\n      >\n        <slot name=\"dropdownCollection\">\n          <!--Empty slot content-->\n        </slot>\n      </div>\n    </template>\n  </v-dropdown>\n  <div\n    ref=\"popperContainer\"\n    class=\"popperContainer\"\n    @keydown.tab=\"showMenu(false)\"\n    @keydown.escape=\"returnFocus\"\n  >\n    <!--Empty container for mounting popper content-->\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .popperContainer {\n    display: contents;\n    &:deep(.v-popper__popper) {\n\n      .v-popper__wrapper {\n        box-shadow: 0px 6px 18px 0px rgba(0, 0, 0, 0.25), 0px 4px 10px 0px rgba(0, 0, 0, 0.15);\n        border-radius: var(--border-radius-lg);\n\n        .v-popper__arrow-container {\n          display: none;\n        }\n\n        .v-popper__inner {\n          padding: 10px 0 10px 0;\n        }\n      }\n    }\n  }\n\n  .dropdownTarget {\n    &:focus-visible, &:focus {\n      outline: none;\n    }\n  }\n</style>\n"]}]}