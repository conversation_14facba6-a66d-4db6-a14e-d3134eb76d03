{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/YamlEditor.vue?vue&type=style&index=0&id=59c7c2b7&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/YamlEditor.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci55YW1sLWVkaXRvciB7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwoKICAuZmlsbCB7CiAgICBmbGV4OiAxOwogIH0KCiAgLmNvZGVtaXJyb3ItY29udGFpbmVyICB7CiAgICBwb3NpdGlvbjogcmVsYXRpdmU7CgogICAgLkNvZGVNaXJyb3IgewogICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS15YW1sLWVkaXRvci1iZyk7CiAgICAgICYgLkNvZGVNaXJyb3ItZ3V0dGVycyB7CiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0teWFtbC1lZGl0b3ItYmcpOwogICAgICB9CiAgICB9CiAgfQoKICAuZGlmZi1tb2RlIHsKICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWRpZmYtaGVhZGVyLWJnKTsKICAgIHBhZGRpbmc6IDVweCA1cHg7CgogICAgYm9yZGVyLWJvdHRvbS1yaWdodC1yYWRpdXM6IDA7CiAgICBib3JkZXItYm90dG9tLWxlZnQtcmFkaXVzOiAwOwogIH0KCiAgLmQyaC1maWxlLXdyYXBwZXIgewogICAgYm9yZGVyLXRvcC1yaWdodC1yYWRpdXM6IDA7CiAgfQp9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/YamlEditor.vue"], "names": [], "mappings": ";AAwQA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEtB,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAC9B;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAC5B;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/YamlEditor.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport jsyaml from 'js-yaml';\nimport { mapPref, DIFF } from '@shell/store/prefs';\nimport isEmpty from 'lodash/isEmpty';\nimport { saferDump } from '@shell/utils/create-yaml';\nimport CodeMirror from './CodeMirror';\nimport FileDiff from './FileDiff';\n\nexport const EDITOR_MODES = {\n  EDIT_CODE: 'EDIT_CODE',\n  VIEW_CODE: 'VIEW_CODE',\n  DIFF_CODE: 'DIFF_CODE'\n};\n\nexport default {\n  emits: ['update:value', 'newObject', 'onInput', 'onReady', 'onChanges', 'validationChanged'],\n\n  components: {\n    CodeMirror,\n    FileDiff\n  },\n  props: {\n    editorMode: {\n      type:    String,\n      default: EDITOR_MODES.EDIT_CODE,\n      validator(value) {\n        return Object.values(EDITOR_MODES).includes(value);\n      }\n    },\n\n    mode: {\n      type:    String,\n      default: '',\n    },\n\n    asObject: {\n      type:    Boolean,\n      default: false,\n    },\n\n    initialYamlValues: {\n      type:    [String, Object],\n      default: '',\n    },\n\n    scrolling: {\n      type:    Boolean,\n      default: true,\n    },\n\n    value: {\n      type:    [String, Object],\n      default: '',\n    },\n\n    hidePreviewButtons: {\n      type:    Boolean,\n      default: false,\n    },\n\n    /**\n     * Inherited global identifier prefix for tests\n     * Define a term based on the parent component to avoid conflicts on multiple components\n     */\n    componentTestid: {\n      type:    String,\n      default: 'yaml-editor'\n    }\n  },\n\n  data() {\n    const { initialYamlValues, value } = this;\n    let curValue;\n    let original;\n\n    if ( this.asObject ) {\n      curValue = saferDump(value);\n    } else {\n      curValue = value || '';\n    }\n\n    if ( this.asObject && initialYamlValues) {\n      original = saferDump(initialYamlValues);\n    } else {\n      original = initialYamlValues;\n    }\n\n    if ( isEmpty(original) ) {\n      original = value;\n    }\n\n    return { original, curValue };\n  },\n\n  computed: {\n    codeMirrorOptions() {\n      const readOnly = this.editorMode === EDITOR_MODES.VIEW_CODE;\n\n      const gutters = [];\n\n      if ( !readOnly ) {\n        gutters.push('CodeMirror-lint-markers');\n      }\n\n      gutters.push('CodeMirror-foldgutter');\n\n      return {\n        readOnly,\n        gutters,\n        mode:            'yaml',\n        lint:            !readOnly,\n        lineNumbers:     !readOnly,\n        styleActiveLine: false,\n        tabSize:         2,\n        indentWithTabs:  false,\n        cursorBlinkRate: ( readOnly ? -1 : 530 ),\n        extraKeys:       {\n          'Ctrl-Space': 'autocomplete',\n\n          Tab: (cm) => {\n            if (cm.somethingSelected()) {\n              cm.indentSelection('add');\n\n              return;\n            }\n\n            cm.execCommand('insertSoftTab');\n          },\n\n          'Shift-Tab': (cm) => {\n            cm.indentSelection('subtract');\n          }\n        },\n        screenReaderLabel: this.t('import.editor.label'),\n        // @TODO find a better way to display the outline\n        // foldOptions: {\n        //   widget: (from, to) => {\n        //     const count = to.line - from.line;\n\n        //     return count ? `\\u21A4${ count }\\u21A6` : '\\u2194';\n        //   }\n        // }\n      };\n    },\n\n    isPreview() {\n      return this.editorMode === EDITOR_MODES.DIFF_CODE;\n    },\n\n    diffMode: mapPref(DIFF),\n\n    showCodeEditor() {\n      return [EDITOR_MODES.EDIT_CODE, EDITOR_MODES.VIEW_CODE].includes(this.editorMode);\n    },\n  },\n\n  watch: {\n    showUploadPrompt(neu) {\n      if (neu) {\n        this.$refs.yamluploader.click();\n      }\n    },\n  },\n\n  methods: {\n    focus() {\n      if ( this.$refs.cm ) {\n        this.$refs.cm.focus();\n      }\n    },\n\n    refresh() {\n      if ( this.$refs.cm ) {\n        this.$refs.cm.refresh();\n      }\n    },\n\n    onInput(value) {\n      if ( !this.asObject ) {\n        this.$emit('update:value', ...arguments);\n      }\n\n      try {\n        const parsed = jsyaml.load(value);\n\n        if ( this.asObject ) {\n          this.$emit('update:value', parsed);\n        } else {\n          this.$emit('newObject', parsed);\n        }\n      } catch (ex) {}\n\n      this.$emit('onInput', ...arguments);\n    },\n\n    onReady() {\n      this.$emit('onReady', ...arguments);\n    },\n\n    onChanges() {\n      this.$emit('onChanges', ...arguments);\n    },\n\n    updateValue(value) {\n      this.curValue = value;\n      this.$refs.cm.updateValue(value);\n    }\n  }\n};\n</script>\n\n<template>\n  <div class=\"yaml-editor\">\n    <div class=\"text-right\">\n      <span\n        v-if=\"isPreview && !hidePreviewButtons\"\n        v-trim-whitespace\n        class=\"btn-group btn-sm diff-mode\"\n      >\n        <button\n          role=\"button\"\n          :aria-label=\"t('generic.unified')\"\n          type=\"button\"\n          class=\"btn btn-sm bg-default\"\n          :class=\"{'active': diffMode !== 'split'}\"\n          @click=\"diffMode='unified'\"\n        >{{ t('generic.unified') }}</button>\n        <button\n          role=\"button\"\n          :aria-label=\"t('generic.split')\"\n          type=\"button\"\n          class=\"btn btn-sm bg-default\"\n          :class=\"{'active': diffMode === 'split'}\"\n          @click=\"diffMode='split'\"\n        >{{ t('generic.split') }}</button>\n      </span>\n    </div>\n    <CodeMirror\n      v-if=\"showCodeEditor\"\n      ref=\"cm\"\n      :class=\"{fill: true, scrolling: scrolling}\"\n      :value=\"curValue\"\n      :options=\"codeMirrorOptions\"\n      :showKeyMapBox=\"true\"\n      :data-testid=\"componentTestid + '-code-mirror'\"\n      :mode=\"mode\"\n      @onInput=\"onInput\"\n      @onReady=\"onReady\"\n      @onChanges=\"onChanges\"\n      @validationChanged=\"$emit('validationChanged', $event)\"\n    />\n    <FileDiff\n      v-else\n      :class=\"{fill: true, scrolling: scrolling}\"\n      :filename=\"'.yaml'\"\n      :side-by-side=\"diffMode === 'split'\"\n      :orig=\"original\"\n      :neu=\"curValue\"\n      :footer-space=\"80\"\n    />\n  </div>\n</template>\n\n<style lang=\"scss\">\n.yaml-editor {\n  display: flex;\n  flex-direction: column;\n\n  .fill {\n    flex: 1;\n  }\n\n  .codemirror-container  {\n    position: relative;\n\n    .CodeMirror {\n      background-color: var(--yaml-editor-bg);\n      & .CodeMirror-gutters {\n        background-color: var(--yaml-editor-bg);\n      }\n    }\n  }\n\n  .diff-mode {\n    background-color: var(--diff-header-bg);\n    padding: 5px 5px;\n\n    border-bottom-right-radius: 0;\n    border-bottom-left-radius: 0;\n  }\n\n  .d2h-file-wrapper {\n    border-top-right-radius: 0;\n  }\n}\n</style>\n"]}]}