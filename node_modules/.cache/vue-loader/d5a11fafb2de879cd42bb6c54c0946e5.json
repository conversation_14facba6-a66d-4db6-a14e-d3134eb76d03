{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetStatus.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetStatus.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetStatus.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEtF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;EAEb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IACD,CAAC,CAAC,CAAC,EAAE;MACH,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC;IACD,CAAC,CAAC,CAAC,EAAE;MACH,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;MACzJ,CAAC;IACH,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC/C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAErC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAChD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC;;QAEF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACvC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACzB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;;MAEX,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACf,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACT,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UAChB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QACvD;MACF;;MAEA,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC7B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;;UAEf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACZ;QACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC;;MAEF,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAClE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MACtD;;MAEA,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;QAEzE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC;;MAEF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9C,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACtB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B;MACF;IACF,CAAC;EACH;AACF,CAAC;;AAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;EAClC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3C,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEpD,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE9D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACZ", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetStatus.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { sortBy } from '@shell/utils/sort';\nimport { get } from '@shell/utils/object';\nimport { stateSort, STATES_ENUM } from '@shell/plugins/dashboard-store/resource-class';\n\nexport default {\n\n  name: 'FleetStatus',\n\n  props: {\n    values: {\n      type:     Array,\n      required: true,\n    },\n    colorKey: {\n      type:    String,\n      default: 'color',\n    },\n    labelKey: {\n      type:    String,\n      default: 'label',\n    },\n    valueKey: {\n      type:    String,\n      default: 'value',\n    },\n    min: {\n      type:    Number,\n      default: 0\n    },\n    max: {\n      type:    Number,\n      default: null,\n    },\n    minPercent: {\n      type:    Number,\n      default: 5,\n    },\n    showZeros: {\n      type:    Boolean,\n      default: false,\n    },\n\n    title: {\n      type:    String,\n      default: 'Resources'\n    }\n  },\n\n  computed: {\n    meta() {\n      return {\n        total:      this.values.map((x) => x.value).reduce((a, b) => a + b, 0),\n        readyCount: this.values.filter((x) => x.status === STATES_ENUM.SUCCESS || x.status === STATES_ENUM.READY).map((x) => x.value).reduce((a, b) => a + b, 0)\n      };\n    },\n\n    pieces() {\n      let out = [...this.values].reduce((prev, obj) => {\n        const color = get(obj, this.colorKey);\n        const label = get(obj, this.labelKey);\n        const value = get(obj, this.valueKey);\n\n        if ( obj[this.valueKey] === 0 && !this.showZeros) {\n          return prev;\n        }\n\n        prev.push({\n          color,\n          label,\n          value,\n          sort: stateSort(color),\n        });\n\n        return prev;\n      }, []);\n\n      const minPercent = this.minPercent || 0;\n      const min = this.min || 0;\n      let max = this.max;\n      let sum = 0;\n\n      if ( !this.max ) {\n        max = 100;\n        if ( out.length ) {\n          max = out.map((x) => x.value).reduce((a, b) => a + b);\n        }\n      }\n\n      out = this.values.map((obj) => {\n        if (obj.value === 0 ) {\n          obj.percent = 0;\n\n          return obj;\n        }\n        const percent = Math.max(minPercent, toPercent(obj.value, min, max));\n\n        obj.percent = percent;\n        sum += percent;\n\n        return obj;\n      });\n\n      // If the sum is bigger than 100%, take it out of the biggest piece\n      if ( sum > 100 ) {\n        sortBy(out, 'percent', true)[0].percent -= sum - 100;\n      }\n\n      out = this.values.map((obj) => {\n        obj.style = `width: ${ obj.percent }%; background: var(--${ obj.color })`;\n\n        return obj;\n      });\n\n      return [...out].filter((obj) => obj.percent);\n    },\n  },\n\n  methods: {\n    showMenu(show) {\n      if (this.$refs.popover) {\n        if (show) {\n          this.$refs.popover.show();\n        } else {\n          this.$refs.popover.hide();\n        }\n      }\n    },\n  }\n};\n\nfunction toPercent(value, min, max) {\n  value = Math.max(min, Math.min(max, value));\n  let per = value / (max - min) * 100; // Percent 0-100\n\n  per = Math.floor(per * 100) / 100; // Round to 2 decimal places\n\n  return per;\n}\n\n</script>\n<template>\n  <div class=\"fleet-status\">\n    <div class=\"count\">\n      {{ meta.total }}\n    </div>\n    <div class=\"progress-container\">\n      <div class=\"header\">\n        <div class=\"title\">\n          {{ title }}\n        </div>\n        <div\n          class=\"resources-dropdown\"\n          tabindex=\"0\"\n          @blur=\"showMenu(false)\"\n          @click=\"showMenu(true)\"\n          @focus.capture=\"showMenu(true)\"\n        >\n          <v-dropdown\n            ref=\"popover\"\n            placement=\"bottom\"\n            offset=\"-10\"\n            :triggers=\"[]\"\n            :delay=\"{show: 0, hide: 0}\"\n            :flip=\"false\"\n            :container=\"false\"\n            popper-class=\"fleet-summary-tooltip\"\n          >\n            <div class=\"meta-title\">\n              {{ meta.readyCount }} / {{ meta.total }} {{ title }} ready <i class=\"icon toggle icon-chevron-down\" />\n            </div>\n            <template #popper>\n              <div class=\"resources-status-list\">\n                <ul\n                  class=\"list-unstyled dropdown\"\n                  @click.stop=\"showMenu(false)\"\n                >\n                  <li\n                    v-for=\"(val, idx) in values\"\n                    :key=\"idx\"\n                  >\n                    <span>{{ val.label }}</span><span class=\"list-count\">{{ val.count }}</span>\n                  </li>\n                </ul>\n              </div>\n            </template>\n          </v-dropdown>\n        </div>\n      </div>\n      <div\n        v-trim-whitespace\n        :class=\"{progress: true, multi: pieces.length > 1}\"\n      >\n        <div\n          v-for=\"(piece, idx) of pieces\"\n          :key=\"idx\"\n          v-trim-whitespace\n          :primary-color-var=\"piece.color\"\n          :class=\"{'piece': true, [piece.color]: true}\"\n          :style=\"piece.style\"\n        />\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  $progress-divider-width: 1px;\n  $progress-border-radius: 90px;\n  $progress-height:        10px;\n  $progress-width:         100%;\n\n  .fleet-status {\n    display: flex;\n    width: 100%;\n    border: 1px solid var(--border);\n    border-radius: 10px\n  }\n\n  .header {\n    display: flex;\n    margin-bottom: 15px;\n    justify-content: space-between;\n  }\n\n  .progress-container {\n    width: 100%;\n    padding: 15px;\n\n  }\n\n  .count {\n    padding: 15px;\n    background-color: var(--tabbed-container-bg);\n    border-radius: 10px 0 0 10px;\n    display: flex;\n    align-items: center;\n    min-width: 60px;\n    justify-content: center;\n    font-size: $font-size-h2\n  }\n\n  .progress {\n    display: block;\n    border-radius: $progress-border-radius;\n    background-color: var(--progress-bg);\n    height: $progress-height;\n    width: $progress-width;\n\n    .piece {\n      display: inline-block;\n      vertical-align: top;\n      height: $progress-height;\n      border-radius: 0;\n      border-right: $progress-divider-width solid var(--progress-divider);\n      vertical-align: top;\n\n      &:first-child {\n        border-top-left-radius: $progress-border-radius;\n        border-bottom-left-radius: $progress-border-radius;\n      }\n\n      &:last-child {\n        border-top-right-radius: $progress-border-radius;\n        border-bottom-right-radius: $progress-border-radius;\n        border-right: 0;\n      }\n    }\n  }\n\n  .piece.bg-success:only-child {\n    opacity: 0.5;\n  }\n\n  .meta-title {\n    display: flex;\n    align-items: center;\n\n    &:hover {\n      cursor: pointer;\n      color: var(--link);\n    }\n\n    .icon {\n      margin: 4px 0 0 5px;\n      opacity: 0.3;\n    }\n  }\n\n  .resources-dropdown {\n    li {\n        display: flex;\n        justify-content: space-between;\n        margin: 10px 5px;\n    }\n\n    .list-count {\n        margin-left: 30px;\n    }\n }\n</style>\n"]}]}