{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CountBox.vue?vue&type=template&id=4cd1e4bf&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CountBox.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdgogICAgY2xhc3M9ImNvdW50LWNvbnRhaW5lciIKICAgIDpzdHlsZT0ic2lkZVN0eWxlIgogID4KICAgIDxkaXYKICAgICAgY2xhc3M9ImNvdW50IgogICAgICA6cHJpbWFyeS1jb2xvci12YXI9InByaW1hcnlDb2xvclZhciIKICAgICAgOnN0eWxlPSJtYWluU3R5bGUiCiAgICA+CiAgICAgIDxkaXYKICAgICAgICBjbGFzcz0iZGF0YSIKICAgICAgICA6Y2xhc3M9InsgJ2NvbXBhY3QnOiBjb21wYWN0IH0iCiAgICAgID4KICAgICAgICA8aDE+e3sgY291bnQgfX08L2gxPgogICAgICAgIDxsYWJlbD57eyBuYW1lIH19PC9sYWJlbD4KICAgICAgPC9kaXY+CiAgICA8L2Rpdj4KICA8L2Rpdj4K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CountBox.vue"], "names": [], "mappings": ";EA0CE,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB;IACE,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB;MACE,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAChC;QACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CountBox.vue", "sourceRoot": "", "sourcesContent": ["<script>\n\nexport default {\n  name: 'CountB<PERSON>',\n\n  props: {\n    name: {\n      type:     String,\n      required: true\n    },\n    count: {\n      type:     Number,\n      required: true\n    },\n    primaryColorVar: {\n      type:     String,\n      required: true\n    },\n    compact: {\n      type:    Boolean,\n      default: false\n    }\n  },\n  computed: {\n    sideStyle() {\n      return `border-left: 9px solid ${ this.customizePrimaryColorOpacity(1) };`;\n    },\n\n    mainStyle() {\n      return `border-color: ${ this.customizePrimaryColorOpacity(0.25) };`;\n    }\n  },\n\n  methods: {\n    customizePrimaryColorOpacity(opacity) {\n      return `rgba(var(${ this.primaryColorVar }), ${ opacity })`;\n    }\n  }\n};\n</script>\n\n<template>\n  <div\n    class=\"count-container\"\n    :style=\"sideStyle\"\n  >\n    <div\n      class=\"count\"\n      :primary-color-var=\"primaryColorVar\"\n      :style=\"mainStyle\"\n    >\n      <div\n        class=\"data\"\n        :class=\"{ 'compact': compact }\"\n      >\n        <h1>{{ count }}</h1>\n        <label>{{ name }}</label>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n    .count {\n      $padding: 10px;\n\n      padding: $padding;\n      position: relative;\n      display: flex;\n      flex-direction: row;\n      align-items: center;\n      border-width: 2px;\n      border-style: solid;\n      border-left: 0;\n\n      .data {\n        display: flex;\n        flex-direction: column;\n        flex: 1;\n\n        label {\n          opacity: 0.7;\n        }\n\n        &.compact {\n          align-items: center;\n          flex-direction: row;\n\n          h1 {\n            margin-bottom: 0;\n            padding-bottom: 0;\n          }\n\n          label {\n            margin-left: 5px;\n          }\n        }\n      }\n\n      h1 {\n        font-size: 40px;\n        line-height: 36px;\n        padding-bottom: math.div($padding, 2);\n        margin-bottom: 5px;\n      }\n\n      @media only screen and (min-width: map-get($breakpoints, '--viewport-7')) {\n        h1 {\n          font-size: 40px;\n          line-height: 36px;\n        }\n      }\n    }\n</style>\n"]}]}