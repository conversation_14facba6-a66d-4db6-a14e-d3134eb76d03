{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Type.vue?vue&type=style&index=0&id=4c933e29&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Type.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Type.vue"], "names": [], "mappings": ";EAsLE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACnB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;;IAE5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACd;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB;IACF;;IAEA,EAAE;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAErB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB;MACF;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvB,EAAE;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACpB;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,EAAE;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpB;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrC,EAAE;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpB;IACF;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Type.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport Favorite from '@shell/components/nav/Favorite';\nimport { TYPE_MODES } from '@shell/store/type-map';\n\nimport TabTitle from '@shell/components/TabTitle';\n\nconst showFavoritesFor = [TYPE_MODES.FAVORITE, TYPE_MODES.USED];\n\nexport default {\n\n  components: { Favorite, TabTitle },\n\n  emits: ['selected'],\n\n  props: {\n    type: {\n      type:     Object,\n      required: true\n    },\n\n    isRoot: {\n      type:    Boolean,\n      default: false,\n    },\n\n    depth: {\n      type:    Number,\n      default: 0,\n    },\n  },\n\n  data() {\n    return { near: false };\n  },\n\n  computed: {\n    showFavorite() {\n      return ( this.type.mode && this.near && showFavoritesFor.includes(this.type.mode) );\n    },\n\n    showCount() {\n      return this.count !== undefined && this.count !== null;\n    },\n\n    namespaceIcon() {\n      return this.type.namespaced;\n    },\n\n    count() {\n      if (this.type.count !== undefined) {\n        return this.type.count;\n      }\n\n      const inStore = this.$store.getters['currentStore'](this.type.name);\n\n      return this.$store.getters[`${ inStore }/count`]({ name: this.type.name });\n    },\n\n    isActive() {\n      const typeFullPath = this.$router.resolve(this.type.route)?.fullPath.toLowerCase();\n      const pageFullPath = this.$route.fullPath?.toLowerCase();\n\n      if ( !this.type.exact) {\n        const typeSplit = typeFullPath.split('/');\n        const pageSplit = pageFullPath.split('/');\n\n        for (let index = 0; index < typeSplit.length; ++index) {\n          if ( index >= pageSplit.length || typeSplit[index] !== pageSplit[index] ) {\n            return false;\n          }\n        }\n\n        return true;\n      }\n\n      return typeFullPath === pageFullPath;\n    }\n\n  },\n\n  methods: {\n    setNear(val) {\n      this.near = val;\n    },\n\n    selectType() {\n      // Prevent issues if custom NavLink is used #5047\n      if (this.type?.route) {\n        const typePath = this.$router.resolve(this.type.route)?.fullPath;\n\n        if (typePath !== this.$route.fullPath) {\n          this.$emit('selected');\n        }\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <router-link\n    v-if=\"type.route\"\n    :key=\"type.name\"\n    v-slot=\"{ href, navigate,isExactActive }\"\n    custom\n    :to=\"type.route\"\n  >\n    <li\n      class=\"child nav-type\"\n      :class=\"{'root': isRoot, [`depth-${depth}`]: true, 'router-link-active': isActive, 'router-link-exact-active': isExactActive}\"\n      @click=\"navigate\"\n      @keypress.enter=\"navigate\"\n    >\n      <TabTitle\n        v-if=\"isExactActive\"\n        :show-child=\"false\"\n      >\n        {{ type.labelKey ? t(type.labelKey) : (type.labelDisplay || type.label) }}\n      </TabTitle>\n      <a\n        role=\"link\"\n        :aria-label=\"type.labelKey ? t(type.labelKey) : (type.labelDisplay || type.label)\"\n        :href=\"href\"\n        class=\"type-link\"\n        :aria-current=\"isActive ? 'page' : undefined\"\n        @click=\"selectType(); navigate($event);\"\n        @mouseenter=\"setNear(true)\"\n        @mouseleave=\"setNear(false)\"\n      >\n        <span\n          v-if=\"type.labelKey\"\n          class=\"label\"\n        ><t :k=\"type.labelKey\" /></span>\n        <span\n          v-else\n          v-clean-html=\"type.labelDisplay || type.label\"\n          class=\"label\"\n          :class=\"{'no-icon': !type.icon}\"\n        />\n        <span\n          v-if=\"showFavorite || namespaceIcon || showCount\"\n          class=\"count\"\n        >\n          <Favorite\n            v-if=\"showFavorite\"\n            :resource=\"type.name\"\n          />\n          <i\n            v-if=\"namespaceIcon\"\n            class=\"icon icon-namespace\"\n            :class=\"{'ns-and-icon': showCount}\"\n            data-testid=\"type-namespaced\"\n          />\n          <span\n            v-if=\"showCount\"\n            data-testid=\"type-count\"\n          >{{ count }}</span>\n        </span>\n      </a>\n    </li>\n  </router-link>\n  <li\n    v-else-if=\"type.link\"\n    class=\"child nav-type nav-link\"\n    data-testid=\"link-type\"\n  >\n    <a\n      role=\"link\"\n      :href=\"type.link\"\n      :target=\"type.target\"\n      rel=\"noopener noreferrer nofollow\"\n      :aria-label=\"type.label\"\n    >\n      <span class=\"label\">{{ type.label }}&nbsp;<i class=\"icon icon-external-link\" /></span>\n    </a>\n  </li>\n  <li v-else>\n    {{ type }}?\n  </li>\n</template>\n\n<style lang=\"scss\" scoped>\n  .ns-and-icon {\n    margin-right: 4px;\n  }\n\n  .type-link:focus-visible span.label {\n    @include focus-outline;\n    outline-offset: 2px;\n  }\n\n  .nav-link a:focus-visible .label {\n    @include focus-outline;\n    outline-offset: 2px;\n  }\n\n  .child {\n    margin: 0 var(--outline) 0 0;\n\n    .label {\n      align-items: center;\n      grid-area: label;\n      overflow: hidden;\n      text-overflow: ellipsis;\n\n      &:not(.nav-type) &.no-icon {\n        padding-left: 3px;\n      }\n\n      :deep() .highlight {\n        background: var(--diff-ins-bg);\n        color: var(--body-text);\n        padding: 2px;\n      }\n\n      :deep() .icon {\n        position: relative;\n        color: var(--muted);\n      }\n    }\n\n    A {\n      display: grid;\n      grid-template-areas: \"label count\";\n      grid-template-columns: auto auto;\n      grid-column-gap: 5px;\n      font-size: 14px;\n      line-height: 24px;\n      padding: 7.5px 7px 7.5px 10px;\n      margin: 0 0 0 -3px;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n      color: var(--body-text);\n      height: 33px;\n\n      &:hover {\n        background: var(--nav-hover);\n        text-decoration: none;\n\n        :deep() .icon {\n          color: var(--body-text);\n        }\n      }\n    }\n\n    .favorite {\n      grid-area: favorite;\n      font-size: 12px;\n      position: relative;\n      vertical-align: middle;\n      margin-right: 4px;\n    }\n\n    .count {\n      font-size: 12px;\n      justify-items: center;\n      padding-right: 4px;\n      display: flex;\n      align-items: center;\n    }\n\n    &.nav-type.nav-link {\n      a .label {\n        display: flex;\n      }\n    }\n\n    &.nav-type:not(.depth-0) {\n      A {\n        padding-left: 16px;\n      }\n\n      :deep() .label I {\n        padding-right: 2px;\n      }\n    }\n\n    &.nav-type:is(.depth-1) {\n      A {\n        font-size: 13px;\n        padding-left: 23px;\n      }\n    }\n\n    &.nav-type:not(.depth-0):not(.depth-1) {\n      A {\n        padding-left: 14px;\n      }\n    }\n  }\n\n</style>\n"]}]}