{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetClusters.vue?vue&type=style&index=0&id=4338021a&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetClusters.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLmxhYmVscy1yb3cgewogICAgdGQgewogICAgICBwYWRkaW5nLXRvcDowOwogICAgICAudGFnIHsKICAgICAgICBtYXJnaW4tcmlnaHQ6IDVweDsKICAgICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7CiAgICAgICAgbWFyZ2luLXRvcDogMnB4OwogICAgICB9CiAgICB9CiAgfQogIC5sYWJlbHMgewogICAgZGlzcGxheTogaW5saW5lOwogICAgZmxleC13cmFwOiB3cmFwOwoKICAgIC5sYWJlbCB7CiAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICAgICAgbWFyZ2luLXRvcDogMnB4OwogICAgfQogIH0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetClusters.vue"], "names": [], "mappings": ";EA4LE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,EAAE;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACjB;IACF;EACF;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACjB;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetClusters.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport ResourceTable from '@shell/components/ResourceTable';\nimport Tag from '@shell/components/Tag.vue';\nimport { STATE, NAME, AGE, FLEET_SUMMARY } from '@shell/config/table-headers';\nimport { FLEET, MANAGEMENT } from '@shell/config/types';\n\nexport default {\n  components: { ResourceTable, Tag },\n\n  props: {\n    rows: {\n      type:     Array,\n      required: true,\n    },\n\n    schema: {\n      type:    Object,\n      default: null,\n    },\n\n    loading: {\n      type:    Boolean,\n      default: false,\n    },\n    useQueryParamsForSimpleFiltering: {\n      type:    <PERSON>olean,\n      default: false\n    }\n  },\n\n  computed: {\n    MANAGEMENT_CLUSTER() {\n      return MANAGEMENT.CLUSTER;\n    },\n\n    headers() {\n      const out = [\n        STATE,\n        NAME,\n        {\n          name:     'bundlesReady',\n          labelKey: 'tableHeaders.bundlesReady',\n          value:    'status.display.readyBundles',\n          sort:     'status.summary.ready',\n          search:   false,\n        },\n        {\n          name:     'reposReady',\n          labelKey: 'tableHeaders.reposReady',\n          value:    'status.readyGitRepos',\n          sort:     'status.summary.ready',\n          search:   false,\n        },\n        FLEET_SUMMARY,\n        {\n          name:          'lastSeen',\n          labelKey:      'tableHeaders.lastSeen',\n          value:         'status.agent.lastSeen',\n          sort:          'status.agent.lastSeen',\n          search:        false,\n          formatter:     'LiveDate',\n          formatterOpts: { addSuffix: true },\n          width:         120,\n        },\n        AGE,\n      ];\n\n      return out;\n    },\n\n    pagingParams() {\n      const schema = this.$store.getters[`management/schemaFor`](FLEET.CLUSTER);\n\n      return {\n        singularLabel: this.$store.getters['type-map/labelFor'](schema),\n        pluralLabel:   this.$store.getters['type-map/labelFor'](schema, 99),\n      };\n    },\n  },\n\n  methods: {\n    toggleCustomLabels(row) {\n      row['displayCustomLabels'] = !row.displayCustomLabels;\n    }\n  }\n};\n</script>\n\n<template>\n  <ResourceTable\n    v-bind=\"$attrs\"\n    :schema=\"schema\"\n    :headers=\"headers\"\n    :rows=\"rows\"\n    :sub-rows=\"true\"\n    :loading=\"loading\"\n    :use-query-params-for-simple-filtering=\"useQueryParamsForSimpleFiltering\"\n    key-field=\"_key\"\n  >\n    <template #cell:workspace=\"{row}\">\n      <span v-if=\"row.type !== MANAGEMENT_CLUSTER && row.metadata.namespace\">{{ row.metadata.namespace }}</span>\n      <span\n        v-else\n        class=\"text-muted\"\n      >&mdash;</span>\n    </template>\n\n    <template #cell:reposReady=\"{row}\">\n      <span\n        v-if=\"!row.repoInfo\"\n        class=\"text-muted\"\n      >&mdash;</span>\n      <span\n        v-else-if=\"row.repoInfo.unready\"\n        class=\"text-warning\"\n      >{{ row.repoInfo.ready }}/{{ row.repoInfo.total }}</span>\n      <span v-else>{{ row.repoInfo.total }}</span>\n    </template>\n\n    <template #cell:bundlesReady=\"{row}\">\n      <span\n        v-if=\"row.bundleInfo.noValidData\"\n        class=\"text-muted\"\n      >&mdash;</span>\n      <span\n        v-else-if=\"row.bundleInfo.ready !== row.bundleInfo.total\"\n        class=\"text-warning\"\n      >{{ row.bundleInfo.ready }}/{{ row.bundleInfo.total }}</span>\n      <span\n        v-else\n        :class=\"{'text-error': !row.bundleInfo.total}\"\n      >{{ row.bundleInfo.total }}</span>\n    </template>\n\n    <template #sub-row=\"{fullColspan, row, onRowMouseEnter, onRowMouseLeave}\">\n      <tr\n        class=\"labels-row sub-row\"\n        @mouseenter=\"onRowMouseEnter\"\n        @mouseleave=\"onRowMouseLeave\"\n      >\n        <template v-if=\"row.customLabels.length\">\n          <td>&nbsp;</td>\n          <td>&nbsp;</td>\n          <td :colspan=\"fullColspan-2\">\n            <span\n              v-if=\"row.customLabels.length\"\n              class=\"mt-5\"\n            > {{ t('fleet.cluster.labels') }}:\n              <span\n                v-for=\"(label, i) in row.customLabels\"\n                :key=\"i\"\n                class=\"mt-5 labels\"\n              >\n                <Tag\n                  v-if=\"i < 7\"\n                  class=\"mr-5 label\"\n                >\n                  {{ label }}\n                </Tag>\n                <Tag\n                  v-else-if=\"i > 6 && row.displayCustomLabels\"\n                  class=\"mr-5 label\"\n                >\n                  {{ label }}\n                </Tag>\n              </span>\n              <a\n                v-if=\"row.customLabels.length > 7\"\n                href=\"#\"\n                @click.prevent=\"toggleCustomLabels(row)\"\n              >\n                {{ t(`fleet.cluster.${row.displayCustomLabels? 'hideLabels' : 'showLabels'}`) }}\n              </a>\n            </span>\n          </td>\n        </template>\n        <td\n          v-else\n          :colspan=\"fullColspan\"\n        >\n&nbsp;\n        </td>\n      </tr>\n    </template>\n  </ResourceTable>\n</template>\n\n<style lang='scss' scoped>\n  .labels-row {\n    td {\n      padding-top:0;\n      .tag {\n        margin-right: 5px;\n        display: inline-block;\n        margin-top: 2px;\n      }\n    }\n  }\n  .labels {\n    display: inline;\n    flex-wrap: wrap;\n\n    .label {\n      display: inline-block;\n      margin-top: 2px;\n    }\n  }\n</style>\n"]}]}