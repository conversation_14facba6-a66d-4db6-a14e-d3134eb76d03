{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownMenu.vue?vue&type=script&setup=true&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownMenu.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgZGVmaW5lQ29tcG9uZW50IGFzIF9kZWZpbmVDb21wb25lbnQgfSBmcm9tICd2dWUnCmltcG9ydCB7CiAgUmNEcm9wZG93biwKICBSY0Ryb3Bkb3duSXRlbSwKICBSY0Ryb3Bkb3duU2VwYXJhdG9yLAogIFJjRHJvcGRvd25UcmlnZ2VyCn0gZnJvbSAnQGNvbXBvbmVudHMvUmNEcm9wZG93bic7CmltcG9ydCB7IFJjRHJvcGRvd25NZW51Q29tcG9uZW50UHJvcHMsIERyb3Bkb3duT3B0aW9uIH0gZnJvbSAnLi90eXBlcyc7CmltcG9ydCBJY29uT3JTdmcgZnJvbSAnQHNoZWxsL2NvbXBvbmVudHMvSWNvbk9yU3ZnJzsKCgpleHBvcnQgZGVmYXVsdCAvKkBfX1BVUkVfXyovX2RlZmluZUNvbXBvbmVudCh7CiAgX19uYW1lOiAnUmNEcm9wZG93bk1lbnUnLAogIHByb3BzOiB7CiAgICBvcHRpb25zOiB7IHR5cGU6IEFycmF5LCByZXF1aXJlZDogdHJ1ZSB9LAogICAgYnV0dG9uUm9sZTogeyB0eXBlOiBudWxsLCByZXF1aXJlZDogZmFsc2UsIGRlZmF1bHQ6ICdwcmltYXJ5JyB9LAogICAgYnV0dG9uU2l6ZTogeyB0eXBlOiBudWxsLCByZXF1aXJlZDogZmFsc2UsIGRlZmF1bHQ6IHVuZGVmaW5lZCB9LAogICAgYnV0dG9uQXJpYUxhYmVsOiB7IHR5cGU6IFN0cmluZywgcmVxdWlyZWQ6IGZhbHNlIH0sCiAgICBkcm9wZG93bkFyaWFMYWJlbDogeyB0eXBlOiBTdHJpbmcsIHJlcXVpcmVkOiBmYWxzZSB9LAogICAgZGF0YVRlc3RpZDogeyB0eXBlOiBTdHJpbmcsIHJlcXVpcmVkOiBmYWxzZSB9CiAgfSwKICBlbWl0czogWyd1cGRhdGU6b3BlbicsICdzZWxlY3QnXSwKICBzZXR1cChfX3Byb3BzOiBhbnksIHsgZXhwb3NlOiBfX2V4cG9zZSwgZW1pdDogX19lbWl0IH0pIHsKICBfX2V4cG9zZSgpOwoKCgpjb25zdCBlbWl0ID0gX19lbWl0OwoKY29uc3QgaGFzT3B0aW9ucyA9IChvcHRpb25zOiBEcm9wZG93bk9wdGlvbltdKSA9PiB7CiAgcmV0dXJuIG9wdGlvbnMubGVuZ3RoICE9PSB1bmRlZmluZWQgPyBvcHRpb25zLmxlbmd0aCA6IE9iamVjdC5rZXlzKG9wdGlvbnMpLmxlbmd0aCA+IDA7Cn07Cgpjb25zdCBfX3JldHVybmVkX18gPSB7IGVtaXQsIGhhc09wdGlvbnMsIGdldCBSY0Ryb3Bkb3duKCkgeyByZXR1cm4gUmNEcm9wZG93biB9LCBnZXQgUmNEcm9wZG93bkl0ZW0oKSB7IHJldHVybiBSY0Ryb3Bkb3duSXRlbSB9LCBnZXQgUmNEcm9wZG93blNlcGFyYXRvcigpIHsgcmV0dXJuIFJjRHJvcGRvd25TZXBhcmF0b3IgfSwgZ2V0IFJjRHJvcGRvd25UcmlnZ2VyKCkgeyByZXR1cm4gUmNEcm9wZG93blRyaWdnZXIgfSwgZ2V0IEljb25PclN2ZygpIHsgcmV0dXJuIEljb25PclN2ZyB9IH0KT2JqZWN0LmRlZmluZVByb3BlcnR5KF9fcmV0dXJuZWRfXywgJ19faXNTY3JpcHRTZXR1cCcsIHsgZW51bWVyYWJsZTogZmFsc2UsIHZhbHVlOiB0cnVlIH0pCnJldHVybiBfX3JldHVybmVkX18KfQoKfSk="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownMenu.vue"], "sourcesContent": ["<script setup lang=\"ts\">\nimport {\n  Rc<PERSON><PERSON>down,\n  Rc<PERSON>ropdownItem,\n  RcDropdownSeparator,\n  RcDropdownTrigger\n} from '@components/RcDropdown';\nimport { RcDropdownMenuComponentProps, DropdownOption } from './types';\nimport IconOrSvg from '@shell/components/IconOrSvg';\n\nwithDefaults(defineProps<RcDropdownMenuComponentProps>(), {\n  buttonRole: 'primary',\n  buttonSize: undefined,\n});\n\nconst emit = defineEmits(['update:open', 'select']);\n\nconst hasOptions = (options: DropdownOption[]) => {\n  return options.length !== undefined ? options.length : Object.keys(options).length > 0;\n};\n</script>\n\n<template>\n  <rc-dropdown\n    :aria-label=\"dropdownAriaLabel\"\n    @update:open=\"(e: boolean) => emit('update:open', e)\"\n  >\n    <rc-dropdown-trigger\n      :[buttonRole]=\"true\"\n      :[buttonSize]=\"true\"\n      :data-testid=\"dataTestid\"\n      :aria-label=\"buttonAriaLabel\"\n    >\n      <i class=\"icon icon-actions\" />\n    </rc-dropdown-trigger>\n    <template #dropdownCollection>\n      <template\n        v-for=\"(a) in options\"\n        :key=\"a.label\"\n      >\n        <rc-dropdown-item\n          v-if=\"!a.divider\"\n          @click=\"(e: MouseEvent) => emit('select', e, a)\"\n        >\n          <template #before>\n            <IconOrSvg\n              v-if=\"a.icon || a.svg\"\n              :icon=\"a.icon\"\n              :src=\"a.svg\"\n              class=\"icon\"\n              color=\"header\"\n            />\n          </template>\n          {{ a.label }}\n        </rc-dropdown-item>\n        <rc-dropdown-separator\n          v-else\n        />\n      </template>\n      <rc-dropdown-item\n        v-if=\"!hasOptions(options)\"\n        disabled\n      >\n        No actions available\n      </rc-dropdown-item>\n    </template>\n  </rc-dropdown>\n</template>\n"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;;;;;;;;;;;;;;AAOnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAsC;;AAEnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxF,CAAC;;;;;;;"}]}