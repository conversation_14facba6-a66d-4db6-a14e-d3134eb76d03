{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/CatalogList/CatalogUninstallDialog.vue?vue&type=style&index=0&id=76f028b3&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/CatalogList/CatalogUninstallDialog.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLnBsdWdpbi1pbnN0YWxsLWRpYWxvZyB7CiAgICBwYWRkaW5nOiAxMHB4OwoKICAgIGg0IHsKICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICB9CgogICAgLmRpYWxvZy1wYW5lbCB7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICAgIG1pbi1oZWlnaHQ6IDEwMHB4OwoKICAgICAgLmRpYWxvZy1pbmZvIHsKICAgICAgICBmbGV4OiAxOwogICAgICB9CiAgICB9CgogICAgLmRpYWxvZy1idXR0b25zIHsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsKICAgICAgbWFyZ2luLXRvcDogMTBweDsKCiAgICAgID4gKjpub3QoOmxhc3QtY2hpbGQpIHsKICAgICAgICBtYXJnaW4tcmlnaHQ6IDEwcHg7CiAgICAgIH0KICAgIH0KICB9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/CatalogList/CatalogUninstallDialog.vue"], "names": [], "mappings": ";EAqKE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEb,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACT;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEhB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpB;IACF;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/CatalogList/CatalogUninstallDialog.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapGetters } from 'vuex';\n\nimport { CATALOG, UI_PLUGIN, SERVICE, WORKLOAD_TYPES } from '@shell/config/types';\nimport { UI_PLUGIN_LABELS, UI_PLUGIN_NAMESPACE } from '@shell/config/uiplugins';\nimport { allHash } from '@shell/utils/promise';\n\nimport AsyncButton from '@shell/components/AsyncButton';\nimport AppModal from '@shell/components/AppModal.vue';\n\nexport default {\n  emits: ['closed', 'refresh', 'update'],\n\n  components: { AsyncButton, AppModal },\n\n  async fetch() {\n    if ( this.$store.getters['management/schemaFor'](UI_PLUGIN) ) {\n      const plugins = this.$store.dispatch('management/findAll', { type: UI_PLUGIN });\n\n      this.plugins = plugins || [];\n    }\n  },\n\n  data() {\n    return {\n      catalog:             undefined,\n      busy:                false,\n      plugins:             null,\n      showModal:           false,\n      returnFocusSelector: '[data-testid=\"extensions-catalog-load-dialog\"]'\n    };\n  },\n\n  computed: {\n    ...mapGetters({ allCharts: 'catalog/charts' }),\n\n    pluginsFromCatalogImage() {\n      return this.plugins.filter((p) => p.metadata?.labels?.[UI_PLUGIN_LABELS.CATALOG_IMAGE]);\n    }\n  },\n\n  methods: {\n    showDialog(catalog) {\n      this.catalog = catalog;\n      this.busy = false;\n      this.showModal = true;\n    },\n\n    closeDialog(result) {\n      this.showModal = false;\n      this.$emit('closed', result);\n\n      if ( result ) {\n        this.$emit('refresh');\n      }\n    },\n\n    async uninstall() {\n      this.busy = true;\n\n      const catalog = this.catalog;\n      const apps = await this.$store.dispatch('management/findAll', { type: CATALOG.APP });\n      const pluginApps = apps.filter((app) => {\n        if ( app.namespace === UI_PLUGIN_NAMESPACE ) {\n          // Find the related apps from the deployed helm repository\n          const charts = this.allCharts.filter((chart) => chart.repoName === catalog.repo?.metadata?.name);\n\n          return charts.some((chart) => chart.chartName === app.metadata.name);\n        }\n\n        return false;\n      });\n\n      await this.removeCatalogResources(catalog);\n\n      if ( pluginApps.length ) {\n        try {\n          pluginApps.forEach((app) => {\n            this.$emit('update', app.name, 'uninstall');\n            app.remove();\n          });\n        } catch (e) {\n          this.$store.dispatch('growl/error', {\n            title:   this.t('plugins.error.generic'),\n            message: e.message ? e.message : e,\n            timeout: 10000\n          }, { root: true });\n        }\n\n        await this.$store.dispatch('management/findAll', { type: CATALOG.OPERATION });\n      }\n\n      this.closeDialog(catalog);\n    },\n\n    async removeCatalogResources(catalog) {\n      const selector = `${ UI_PLUGIN_LABELS.CATALOG_IMAGE }=${ catalog.name }`;\n      const namespace = UI_PLUGIN_NAMESPACE;\n\n      if ( selector ) {\n        const hash = await allHash({\n          deployment: this.$store.dispatch('management/findMatching', {\n            type: WORKLOAD_TYPES.DEPLOYMENT, selector, namespace\n          }),\n          service: this.$store.dispatch('management/findMatching', {\n            type: SERVICE, selector, namespace\n          }),\n          repo: this.$store.dispatch('management/findMatching', { type: CATALOG.CLUSTER_REPO, selector })\n        });\n\n        for ( const resource of Object.keys(hash) ) {\n          if ( hash[resource] ) {\n            hash[resource].forEach((r) => r.remove());\n          }\n        }\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <app-modal\n    v-if=\"showModal\"\n    name=\"uninstallCatalogDialog\"\n    height=\"auto\"\n    :scrollable=\"true\"\n    :trigger-focus-trap=\"true\"\n    :return-focus-selector=\"returnFocusSelector\"\n    @close=\"closeDialog(false)\"\n  >\n    <div\n      v-if=\"catalog\"\n      class=\"plugin-install-dialog\"\n    >\n      <h4 class=\"mt-10\">\n        {{ t('plugins.uninstall.title', { name: catalog.name }) }}\n      </h4>\n      <div class=\"mt-10 dialog-panel\">\n        <div class=\"dialog-info\">\n          <p>\n            {{ t('plugins.uninstall.catalog') }}\n          </p>\n        </div>\n        <div class=\"dialog-buttons\">\n          <button\n            :disabled=\"busy\"\n            class=\"btn role-secondary\"\n            data-testid=\"uninstall-ext-modal-cancel-btn\"\n            @click=\"closeDialog(false)\"\n          >\n            {{ t('generic.cancel') }}\n          </button>\n          <AsyncButton\n            mode=\"uninstall\"\n            data-testid=\"uninstall-ext-modal-uninstall-btn\"\n            @click=\"uninstall()\"\n          />\n        </div>\n      </div>\n    </div>\n  </app-modal>\n</template>\n\n<style lang=\"scss\" scoped>\n  .plugin-install-dialog {\n    padding: 10px;\n\n    h4 {\n      font-weight: bold;\n    }\n\n    .dialog-panel {\n      display: flex;\n      flex-direction: column;\n      min-height: 100px;\n\n      .dialog-info {\n        flex: 1;\n      }\n    }\n\n    .dialog-buttons {\n      display: flex;\n      justify-content: flex-end;\n      margin-top: 10px;\n\n      > *:not(:last-child) {\n        margin-right: 10px;\n      }\n    }\n  }\n</style>\n"]}]}