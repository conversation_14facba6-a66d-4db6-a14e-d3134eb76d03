{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/RuleSelector.vue?vue&type=template&id=c5ef2e9a&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/RuleSelector.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdgogICAgY2xhc3M9InJ1bGUtc2VsZWN0b3IiCiAgICA6Y2xhc3M9IntbbW9kZV06IHRydWV9IgogID4KICAgIDxBcnJheUxpc3QKICAgICAgOnZhbHVlPSJ2YWx1ZSIKICAgICAgOnByb3RpcD0iZmFsc2UiCiAgICAgIDpzaG93LWhlYWRlcj0idHJ1ZSIKICAgICAgOmFkZC1sYWJlbD0iYWRkTGFiZWwiCiAgICAgIDpkZWZhdWx0LWFkZC12YWx1ZT0iZGVmYXVsdEFkZFZhbHVlIgogICAgICA6bW9kZT0ibW9kZSIKICAgICAgQHVwZGF0ZTp2YWx1ZT0iJGVtaXQoJ2lucHV0JywgJGV2ZW50KSIKICAgID4KICAgICAgPHRlbXBsYXRlIHYtc2xvdDpjb2x1bW4taGVhZGVycz4KICAgICAgICA8ZGl2IGNsYXNzPSJib3giPgogICAgICAgICAgPGRpdiBjbGFzcz0ia2V5Ij4KICAgICAgICAgICAgS2V5CiAgICAgICAgICA8L2Rpdj4KICAgICAgICAgIDxkaXYgY2xhc3M9Im9wZXJhdG9yIj4KICAgICAgICAgICAgT3BlcmF0b3IKICAgICAgICAgIDwvZGl2PgogICAgICAgICAgPGRpdiBjbGFzcz0idmFsdWUiPgogICAgICAgICAgICBWYWx1ZQogICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8ZGl2IC8+CiAgICAgICAgPC9kaXY+CiAgICAgIDwvdGVtcGxhdGU+CiAgICAgIDx0ZW1wbGF0ZSB2LXNsb3Q6Y29sdW1ucz0ic2NvcGUiPgogICAgICAgIDxkaXYgY2xhc3M9ImtleSI+CiAgICAgICAgICA8TGFiZWxlZElucHV0CiAgICAgICAgICAgIHYtbW9kZWw6dmFsdWU9InNjb3BlLnJvdy52YWx1ZS5rZXkiCiAgICAgICAgICAgIDptb2RlPSJtb2RlIgogICAgICAgICAgLz4KICAgICAgICA8L2Rpdj4KICAgICAgICA8ZGl2IGNsYXNzPSJvcGVyYXRvciI+CiAgICAgICAgICA8U2VsZWN0CiAgICAgICAgICAgIDptb2RlPSJtb2RlIgogICAgICAgICAgICA6dmFsdWU9InNjb3BlLnJvdy52YWx1ZS5vcGVyYXRvciIKICAgICAgICAgICAgOm9wdGlvbnM9Im9wZXJhdG9yT3B0aW9ucyIKICAgICAgICAgICAgQHVwZGF0ZTp2YWx1ZT0ib25PcGVyYXRvcklucHV0KHNjb3BlLCAkZXZlbnQpIgogICAgICAgICAgLz4KICAgICAgICA8L2Rpdj4KICAgICAgICA8ZGl2IGNsYXNzPSJ2YWx1ZSI+CiAgICAgICAgICA8TGFiZWxlZElucHV0CiAgICAgICAgICAgIDpkaXNhYmxlZD0iaXNWYWx1ZURpc2FibGVkKHNjb3BlKSIKICAgICAgICAgICAgOnZhbHVlPSJnZXRWYWx1ZShzY29wZSkiCiAgICAgICAgICAgIDptb2RlPSJtb2RlIgogICAgICAgICAgICBAdXBkYXRlOnZhbHVlPSJvblZhbHVlSW5wdXQoc2NvcGUsICRldmVudCkiCiAgICAgICAgICAvPgogICAgICAgIDwvZGl2PgogICAgICA8L3RlbXBsYXRlPgogICAgPC9BcnJheUxpc3Q+CiAgPC9kaXY+Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/RuleSelector.vue"], "names": [], "mappings": ";EA4GE,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB;IACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC;MACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/C,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/RuleSelector.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { _VIEW } from '@shell/config/query-params';\nimport ArrayList from '@shell/components/form/ArrayList';\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport Select from '@shell/components/form/Select';\n\nconst OPERATOR_VALUES = {\n  IS_SET:      'Exists',\n  IS_NOT_SET:  'DoesNotExist',\n  IN_LIST:     'In',\n  NOT_IN_LIST: 'NotIn'\n};\n\nexport default {\n  emits: ['update:value', 'input'],\n\n  components: {\n    ArrayList,\n    LabeledInput,\n    Select\n  },\n\n  props: {\n    value: {\n      type:    Array,\n      default: null\n    },\n    mode: {\n      type:    String,\n      default: _VIEW\n    },\n    addLabel: {\n      type:     String,\n      required: true\n    }\n  },\n\n  data() {\n    return {\n      operatorOptions: [\n        {\n          label: 'is set',\n          value: OPERATOR_VALUES.IS_SET,\n        },\n        {\n          label: 'is not set',\n          value: OPERATOR_VALUES.IS_NOT_SET,\n        },\n        {\n          label: 'in list',\n          value: OPERATOR_VALUES.IN_LIST,\n        },\n        {\n          label: 'not in list',\n          value: OPERATOR_VALUES.NOT_IN_LIST,\n        }\n      ],\n      optionsWithValueDisabled: [\n        OPERATOR_VALUES.IS_SET,\n        OPERATOR_VALUES.IS_NOT_SET\n      ],\n      defaultAddValue: {\n        key:      '',\n        operator: OPERATOR_VALUES.IS_SET,\n      }\n    };\n  },\n\n  computed: {\n    localValue: {\n      get() {\n        return this.value;\n      },\n      set(localValue) {\n        this.$emit('update:value', localValue);\n      }\n    }\n  },\n\n  methods: {\n    onOperatorInput(scope, operator) {\n      scope.row.value.operator = operator;\n      if (this.optionsWithValueDisabled.includes(operator)) {\n        if (scope.row.value.values) {\n          delete scope.row.value.values;\n        }\n      } else {\n        scope.row.value.values = scope.row.value.values || [];\n      }\n      scope.queueUpdate();\n    },\n\n    isValueDisabled(scope) {\n      return this.optionsWithValueDisabled.includes(scope.row.value.operator);\n    },\n    getValue(scope) {\n      return scope.row.value.values?.join(',') || '';\n    },\n    onValueInput(scope, rawValue) {\n      scope.row.value.values = rawValue.split(',')\n        .map((entry) => entry.trim());\n      scope.queueUpdate();\n    }\n  },\n};\n</script>\n\n<template>\n  <div\n    class=\"rule-selector\"\n    :class=\"{[mode]: true}\"\n  >\n    <ArrayList\n      :value=\"value\"\n      :protip=\"false\"\n      :show-header=\"true\"\n      :add-label=\"addLabel\"\n      :default-add-value=\"defaultAddValue\"\n      :mode=\"mode\"\n      @update:value=\"$emit('input', $event)\"\n    >\n      <template v-slot:column-headers>\n        <div class=\"box\">\n          <div class=\"key\">\n            Key\n          </div>\n          <div class=\"operator\">\n            Operator\n          </div>\n          <div class=\"value\">\n            Value\n          </div>\n          <div />\n        </div>\n      </template>\n      <template v-slot:columns=\"scope\">\n        <div class=\"key\">\n          <LabeledInput\n            v-model:value=\"scope.row.value.key\"\n            :mode=\"mode\"\n          />\n        </div>\n        <div class=\"operator\">\n          <Select\n            :mode=\"mode\"\n            :value=\"scope.row.value.operator\"\n            :options=\"operatorOptions\"\n            @update:value=\"onOperatorInput(scope, $event)\"\n          />\n        </div>\n        <div class=\"value\">\n          <LabeledInput\n            :disabled=\"isValueDisabled(scope)\"\n            :value=\"getValue(scope)\"\n            :mode=\"mode\"\n            @update:value=\"onValueInput(scope, $event)\"\n          />\n        </div>\n      </template>\n    </ArrayList>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.rule-selector {\n  &:not(.view) table {\n    table-layout: initial;\n  }\n\n   :deep() .box {\n    display: grid;\n    grid-template-columns: 25% 25% 25% 15%;\n    column-gap: 1.75%;\n    align-items: center;\n    margin-bottom: 10px;\n\n    .key,\n    .value,\n    .operator {\n      height: 100%;\n    }\n  }\n}\n</style>\n"]}]}