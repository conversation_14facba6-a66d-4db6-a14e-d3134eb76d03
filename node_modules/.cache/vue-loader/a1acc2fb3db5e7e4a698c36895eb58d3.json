{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DraggableZone.vue?vue&type=style&index=0&id=4c5155e6&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DraggableZone.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DraggableZone.vue"], "names": [], "mappings": ";;EAmGE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACX;EACF;;EAEA,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,EAAE,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACd;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACd;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DraggableZone.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent } from 'vue';\nimport { mapState } from 'vuex';\nimport { BOTTOM, CENTER, LEFT, RIGHT } from '@shell/utils/position';\n\ntype Zone = null | typeof CENTER | typeof RIGHT | typeof BOTTOM | typeof LEFT;\n\nexport interface Drag {\n  active: boolean;\n  zone: Zone;\n}\n\ninterface Data {\n  drag: Drag;\n}\n\nexport default defineComponent({\n  data(): Data {\n    return {\n      drag: {\n        active: false,\n        zone:   CENTER,\n      },\n    };\n  },\n\n  computed: {\n\n    ...mapState('wm', ['userPin']),\n\n    pin: {\n      get(): Zone {\n        return this.userPin;\n      },\n\n      set(pin: Zone) {\n        if (pin === CENTER) {\n          return;\n        }\n        window.localStorage.setItem('wm-pin', pin as string);\n        this.$store.commit('wm/setUserPin', pin);\n      },\n    },\n\n  },\n\n  methods: {\n\n    onDragStart() {\n      this.drag.active = true;\n    },\n\n    onDragOver(event: DragEvent, zone: Zone) {\n      this.drag.zone = zone;\n      if (zone !== CENTER) {\n        event.preventDefault();\n      }\n    },\n\n    onDragEnd() {\n      this.pin = this.drag.zone;\n      this.drag = {\n        active: false,\n        zone:   CENTER,\n      };\n    },\n\n  }\n});\n</script>\n\n<template>\n  <div v-if=\"drag.active\">\n    <span\n      v-if=\"drag.zone != pin\"\n      class=\"pin-effect-area\"\n      :class=\"drag.zone\"\n    />\n    <span\n      class=\"drag-area center\"\n      @dragover=\"onDragOver($event, 'center')\"\n    />\n    <span\n      class=\"drag-area right\"\n      @dragover=\"onDragOver($event, 'right')\"\n    />\n    <span\n      class=\"drag-area bottom\"\n      @dragover=\"onDragOver($event, 'bottom')\"\n    />\n    <span\n      class=\"drag-area left\"\n      @dragover=\"onDragOver($event, 'left')\"\n    />\n  </div>\n</template>\n\n<style lang='scss' scoped>\n\n  .pin-effect-area {\n    position: absolute;\n    z-index: 997;\n    width: 0;\n    height: 0;\n    border-style: hidden;\n\n    &.right {\n      top: 55px;\n      right: 0;\n      width: 300px;\n      transition: width .5s ease;\n      height: 100%;\n      background-image: linear-gradient(to right, var(--drag-over-outer-bg), var(--drag-over-inner-bg));\n      border-left: 1px;\n      border-style: hidden hidden hidden dashed;\n    }\n\n    &.left {\n      top: 55px;\n      left: 0;\n      width: 300px;\n      transition: width .5s ease;\n      height: 100%;\n      background-image: linear-gradient(to left, var(--drag-over-outer-bg), var(--drag-over-inner-bg));\n      border-right: 1px;\n      border-style: hidden dashed hidden hidden;\n    }\n\n    &.bottom {\n      bottom: 0;\n      height: 270px;\n      transition: height .5s ease;\n      width: 100%;\n      background-image: linear-gradient(to top, var(--drag-over-inner-bg), var(--drag-over-outer-bg));\n      border-top: 1px;\n      border-style: dashed hidden hidden hidden;\n    }\n\n    &.center {\n      width: 0;\n      height: 0;\n    }\n  }\n\n  // ToDo make height and width as input variable\n  .drag-area {\n    position: absolute;\n    z-index: 999;\n    width: 0;\n    height: 0;\n    opacity: 0;\n\n    &.center {\n      top: 0;\n      right: 0;\n      width: 100%;\n      height: 100%;\n      z-index: 998;\n    }\n\n    &.right {\n      top: 55px;\n      right: 0;\n      width: 300px;\n      height: 100%;\n    }\n\n    &.left {\n      top: 55px;\n      left: 0;\n      width: 300px;\n      height: 100%;\n    }\n\n    &.bottom {\n      bottom: 0;\n      height: 270px;\n      width: 100%;\n    }\n  }\n</style>\n"]}]}