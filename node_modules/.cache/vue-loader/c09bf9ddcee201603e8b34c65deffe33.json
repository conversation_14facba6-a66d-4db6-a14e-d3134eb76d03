{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Jump.vue?vue&type=style&index=0&id=65ab091d&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Jump.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLnNlYXJjaCwgLnNlYXJjaDpob3ZlciB7CiAgICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1kcm9wZG93bi1iZyk7CiAgICBib3JkZXItcmFkaXVzOiAwOwogICAgYm94LXNoYWRvdzogbm9uZTsKICB9CgogIC5zZWFyY2g6Zm9jdXMtdmlzaWJsZSB7CiAgICBvdXRsaW5lLW9mZnNldDogLTJweDsKICB9CgogIC5yZXN1bHRzIHsKICAgIG1hcmdpbi10b3A6IC0xcHg7CiAgICBvdmVyZmxvdy15OiBhdXRvOwogICAgcGFkZGluZzogMTBweDsKICAgIGNvbG9yOiB2YXIoLS1kcm9wZG93bi10ZXh0KTsKICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWRyb3Bkb3duLWJnKTsKICAgIGJvcmRlcjogMXB4IHNvbGlkIHZhcigtLWRyb3Bkb3duLWJvcmRlcik7CiAgICBoZWlnaHQ6IDc1dmg7CiAgfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Jump.vue"], "names": [], "mappings": ";EAkHE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACtB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACd", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Jump.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport debounce from 'lodash/debounce';\nimport Group from '@shell/components/nav/Group';\nimport { isMac } from '@shell/utils/platform';\nimport { BOTH, TYPE_MODES } from '@shell/store/type-map';\nimport { COUNT } from '@shell/config/types';\n\nexport default {\n  emits: ['closeSearch'],\n\n  components: { Group },\n\n  data() {\n    return {\n      isMac,\n      value:  '',\n      groups: null,\n    };\n  },\n\n  watch: {\n    value() {\n      this.queueUpdate();\n    },\n  },\n\n  mounted() {\n    this.updateMatches();\n    this.queueUpdate = debounce(this.updateMatches, 250);\n\n    this.$refs.input.focus();\n  },\n\n  methods: {\n    updateMatches() {\n      const clusterId = this.$store.getters['clusterId'];\n      const productId = this.$store.getters['productId'];\n      const product = this.$store.getters['currentProduct'];\n\n      const allTypesByMode = this.$store.getters['type-map/allTypes'](productId, [TYPE_MODES.ALL]) || {};\n      const allTypes = allTypesByMode[TYPE_MODES.ALL];\n      const out = this.$store.getters['type-map/getTree'](productId, TYPE_MODES.ALL, allTypes, clusterId, BOTH, null, this.value);\n\n      // Suplement the output with count info. Usualy the `Type` component would handle this individualy... but scales real bad so give it\n      // some help\n      const counts = this.$store.getters[`${ product.inStore }/all`](COUNT)?.[0]?.counts || {};\n\n      out.forEach((o) => {\n        o.children?.forEach((t) => {\n          const count = counts[t.name];\n\n          t.count = count ? count.summary.count || 0 : null;\n          t.byNamespace = count ? count.namespaces : {};\n          t.revision = count ? count.revision : null;\n        });\n      });\n\n      this.groups = out;\n\n      // Hide top-level groups with no children (or one child that is an overview)\n      this.groups.forEach((g) => {\n        const isRoot = g.isRoot || g.name === 'Root';\n        const hidden = isRoot || g.children?.length === 0 || (g.children?.length === 1 && g.children[0].overview);\n\n        g.hidden = !!hidden;\n      });\n    }\n  },\n};\n</script>\n\n<template>\n  <div>\n    <p\n      id=\"describe-filter-resource-search\"\n      hidden\n    >\n      {{ t('nav.resourceSearch.filteringDescription') }}\n    </p>\n    <input\n      ref=\"input\"\n      v-model=\"value\"\n      :placeholder=\"t('nav.resourceSearch.placeholder')\"\n      class=\"search\"\n      role=\"textbox\"\n      :aria-label=\"t('nav.resourceSearch.label')\"\n      aria-describedby=\"describe-filter-resource-search\"\n      @keyup.esc=\"$emit('closeSearch')\"\n    >\n    <div class=\"results\">\n      <div\n        v-for=\"g in groups\"\n        :key=\"g.name\"\n        class=\"package\"\n      >\n        <Group\n          v-if=\"!g.hidden\"\n          :key=\"g.name\"\n          id-prefix=\"\"\n          :group=\"g\"\n          :can-collapse=\"false\"\n          :fixed-open=\"true\"\n          @close=\"$emit('closeSearch')\"\n        >\n          <template #accordion>\n            <h6>{{ g.label }}</h6>\n          </template>\n        </Group>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .search, .search:hover {\n    position: relative;\n    background-color: var(--dropdown-bg);\n    border-radius: 0;\n    box-shadow: none;\n  }\n\n  .search:focus-visible {\n    outline-offset: -2px;\n  }\n\n  .results {\n    margin-top: -1px;\n    overflow-y: auto;\n    padding: 10px;\n    color: var(--dropdown-text);\n    background-color: var(--dropdown-bg);\n    border: 1px solid var(--dropdown-border);\n    height: 75vh;\n  }\n</style>\n"]}]}