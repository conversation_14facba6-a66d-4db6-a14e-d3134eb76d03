{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayList.vue?vue&type=style&index=0&id=3f1a9d0f&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayList.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLnRpdGxlIHsKICAgIG1hcmdpbi1ib3R0b206IDEwcHg7CiAgfQoKICAucmVxdWlyZWQgewogICAgY29sb3I6IHZhcigtLWVycm9yKTsKICB9CgogIC5ib3ggewogICAgZGlzcGxheTogZ3JpZDsKICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogYXV0byAkYXJyYXktbGlzdC1yZW1vdmUtbWFyZ2luOwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgIG1hcmdpbi1ib3R0b206IDEwcHg7CiAgICAudmFsdWUgewogICAgICBmbGV4OiAxOwogICAgICBJTlBVVCB7CiAgICAgICAgaGVpZ2h0OiAkdW5sYWJlbGVkLWlucHV0LWhlaWdodDsKICAgICAgfQogICAgfQogIH0KICAucmVtb3ZlIHsKICAgIHRleHQtYWxpZ246IHJpZ2h0OwogIH0KICAuZm9vdGVyIHsKICAgIC5wcm90aXAgewogICAgICBmbG9hdDogcmlnaHQ7CiAgICAgIHBhZGRpbmc6IDVweCAwOwogICAgfQogIH0KCiAgLnJlcXVpcmVkIHsKICAgIGNvbG9yOiB2YXIoLS1lcnJvcik7CiAgfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayList.vue"], "names": [], "mappings": ";EAyYE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,EAAE;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC;IACF;EACF;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAChB;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayList.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport debounce from 'lodash/debounce';\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\nimport { removeAt } from '@shell/utils/array';\nimport { TextAreaAutoGrow } from '@components/Form/TextArea';\nimport { clone } from '@shell/utils/object';\nimport { LabeledInput } from '@components/Form/LabeledInput';\nconst DEFAULT_PROTIP = 'Tip: Paste lines into any list field for easy bulk entry';\n\nexport default {\n  emits: ['add', 'remove', 'update:value'],\n\n  components: { TextAreaAutoGrow, LabeledInput },\n  props:      {\n    value: {\n      type:    Array,\n      default: null,\n    },\n    mode: {\n      type:    String,\n      default: _EDIT,\n    },\n    initialEmptyRow: {\n      type:    Boolean,\n      default: false,\n    },\n    title: {\n      type:    String,\n      default: ''\n    },\n    protip: {\n      type:    [String, Boolean],\n      default: DEFAULT_PROTIP,\n    },\n    showHeader: {\n      type:    Boolean,\n      default: false,\n    },\n    valueLabel: {\n      type:    String,\n      default: 'Value',\n    },\n    valuePlaceholder: {\n      type:    String,\n      default: 'e.g. bar'\n    },\n    valueMultiline: {\n      type:    Boolean,\n      default: false,\n    },\n    addIcon: {\n      type:    String,\n      default: '',\n    },\n    addLabel: {\n      type:    String,\n      default: '',\n    },\n    addAllowed: {\n      type:    Boolean,\n      default: true,\n    },\n    addDisabled: {\n      type:    Boolean,\n      default: false,\n    },\n    removeLabel: {\n      type:    String,\n      default: '',\n    },\n    removeAllowed: {\n      type:    Boolean,\n      default: true,\n    },\n    defaultAddValue: {\n      type:    [String, Number, Object, Array],\n      default: ''\n    },\n    loading: {\n      type:    Boolean,\n      default: false\n    },\n    disabled: {\n      type:    Boolean,\n      default: false,\n    },\n    required: {\n      type:    Boolean,\n      default: false\n    },\n    rules: {\n      default:   () => [],\n      type:      Array,\n      // we only want functions in the rules array\n      validator: (rules) => rules.every((rule) => ['function'].includes(typeof rule))\n    },\n    a11yLabel: {\n      type:    String,\n      default: '',\n    },\n  },\n  data() {\n    const input = (Array.isArray(this.value) ? this.value : []).slice();\n    const rows = [];\n\n    for ( const value of input ) {\n      rows.push({ value });\n    }\n    if ( !rows.length && this.initialEmptyRow ) {\n      const value = this.defaultAddValue ? clone(this.defaultAddValue) : '';\n\n      rows.push({ value });\n    }\n\n    return { rows, lastUpdateWasFromValue: false };\n  },\n  computed: {\n    _addLabel() {\n      return this.addLabel || this.t('generic.add');\n    },\n    _removeLabel() {\n      return this.removeLabel || this.t('generic.remove');\n    },\n\n    isView() {\n      return this.mode === _VIEW;\n    },\n    showAdd() {\n      return this.addAllowed;\n    },\n    disableAdd() {\n      return this.addDisabled;\n    },\n    showRemove() {\n      return this.removeAllowed;\n    },\n    isDefaultProtip() {\n      return this.protip === DEFAULT_PROTIP;\n    },\n    showProtip() {\n      if (this.protip && !this.isDefaultProtip) {\n        return true;\n      }\n\n      return !this.valueMultiline && this.protip;\n    }\n  },\n  watch: {\n    value: {\n      deep: true,\n      handler() {\n        this.lastUpdateWasFromValue = true;\n        this.rows = (this.value || []).map((v) => ({ value: v }));\n      }\n    },\n\n    rows: {\n      deep: true,\n      handler(newValue, oldValue) {\n        // lastUpdateWasFromValue is used to break a cycle where when rows are updated\n        // this was called which then forced rows to updated again\n        if (!this.lastUpdateWasFromValue) {\n          this.queueUpdate();\n        }\n        this.lastUpdateWasFromValue = false;\n      }\n    }\n  },\n  created() {\n    this.queueUpdate = debounce(this.update, 50);\n  },\n  methods: {\n    add() {\n      this.rows.push({ value: clone(this.defaultAddValue) });\n      if (this.defaultAddValue) {\n        this.queueUpdate();\n      }\n      this.$nextTick(() => {\n        const inputs = this.$refs.value;\n\n        if ( inputs && inputs.length > 0 ) {\n          inputs[inputs.length - 1].focus();\n        }\n        this.$emit('add');\n      });\n    },\n    /**\n     * Remove item and emits removed row and its own index value\n     */\n    remove(row, index) {\n      this.$emit('remove', { row, index });\n      removeAt(this.rows, index);\n      this.queueUpdate();\n    },\n\n    /**\n     * Cleanup rows and emit input\n     */\n    update() {\n      if ( this.isView ) {\n        return;\n      }\n      const out = [];\n\n      for ( const row of this.rows ) {\n        const trim = !this.valueMultiline && (typeof row.value === 'string');\n        const value = trim ? row.value.trim() : row.value;\n\n        if ( typeof value !== 'undefined' ) {\n          out.push(value);\n        }\n      }\n      this.$emit('update:value', out);\n    },\n\n    /**\n     * Handle paste event, e.g. split multiple lines in rows\n     */\n    onPaste(index, event) {\n      event.preventDefault();\n      const text = event.clipboardData.getData('text/plain');\n\n      if (this.valueMultiline) {\n        // Allow to paste multiple lines\n        this.rows[index].value = text;\n      } else {\n        // Prevent to paste the value and emit text in multiple rows\n        const split = text.split('\\n').map((value) => ({ value }));\n\n        event.preventDefault();\n        this.rows.splice(index, 1, ...split);\n      }\n\n      this.update();\n    }\n  },\n};\n</script>\n\n<template>\n  <div>\n    <div\n      v-if=\"title\"\n      class=\"clearfix\"\n    >\n      <slot name=\"title\">\n        <h3>\n          {{ title }}\n          <span\n            v-if=\"required\"\n            class=\"required\"\n          >*</span>\n          <i\n            v-if=\"showProtip\"\n            v-clean-tooltip=\"protip\"\n            class=\"icon icon-info\"\n          />\n        </h3>\n      </slot>\n    </div>\n\n    <template v-if=\"rows.length\">\n      <div v-if=\"showHeader\">\n        <slot name=\"column-headers\">\n          <label class=\"value text-label mb-10\">\n            {{ valueLabel }}\n          </label>\n        </slot>\n      </div>\n      <div\n        v-for=\"(row, idx) in rows\"\n        :key=\"idx\"\n        :data-testid=\"`array-list-box${ idx }`\"\n        class=\"box\"\n      >\n        <slot\n          name=\"columns\"\n          :queueUpdate=\"queueUpdate\"\n          :i=\"idx\"\n          :rows=\"rows\"\n          :row=\"row\"\n          :mode=\"mode\"\n          :isView=\"isView\"\n        >\n          <div class=\"value\">\n            <slot\n              name=\"value\"\n              :row=\"row\"\n              :mode=\"mode\"\n              :isView=\"isView\"\n              :queue-update=\"queueUpdate\"\n            >\n              <TextAreaAutoGrow\n                v-if=\"valueMultiline\"\n                ref=\"value\"\n                v-model:value=\"row.value\"\n                :data-testid=\"`textarea-${idx}`\"\n                :placeholder=\"valuePlaceholder\"\n                :mode=\"mode\"\n                :disabled=\"disabled\"\n                @paste=\"onPaste(idx, $event)\"\n                @update:value=\"queueUpdate\"\n              />\n              <LabeledInput\n                v-else-if=\"rules.length > 0\"\n                ref=\"value\"\n                v-model:value=\"row.value\"\n                :data-testid=\"`labeled-input-${idx}`\"\n                :placeholder=\"valuePlaceholder\"\n                :disabled=\"isView || disabled\"\n                :rules=\"rules\"\n                :compact=\"false\"\n                @paste=\"onPaste(idx, $event)\"\n                @update:value=\"queueUpdate\"\n              />\n              <input\n                v-else\n                ref=\"value\"\n                v-model=\"row.value\"\n                :data-testid=\"`input-${idx}`\"\n                :placeholder=\"valuePlaceholder\"\n                :disabled=\"isView || disabled\"\n                :aria-label=\"a11yLabel ? a11yLabel : undefined\"\n                @paste=\"onPaste(idx, $event)\"\n              >\n            </slot>\n          </div>\n        </slot>\n        <div\n          v-if=\"showRemove\"\n          class=\"remove\"\n        >\n          <slot\n            name=\"remove-button\"\n            :remove=\"() => remove(row, idx)\"\n            :i=\"idx\"\n            :row=\"row\"\n          >\n            <button\n              type=\"button\"\n              :disabled=\"isView\"\n              class=\"btn role-link\"\n              :data-testid=\"`remove-item-${idx}`\"\n              :aria-label=\"`${_removeLabel} ${idx + 1}`\"\n              role=\"button\"\n              @click=\"remove(row, idx)\"\n            >\n              {{ _removeLabel }}\n            </button>\n          </slot>\n        </div>\n      </div>\n    </template>\n    <div v-else>\n      <slot name=\"empty\">\n        <div\n          v-if=\"mode==='view'\"\n          class=\"text-muted\"\n        >\n          &mdash;\n        </div>\n      </slot>\n    </div>\n    <div\n      v-if=\"showAdd && !isView\"\n      class=\"footer mt-20\"\n    >\n      <slot\n        v-if=\"showAdd\"\n        name=\"add\"\n        :add=\"add\"\n      >\n        <button\n          type=\"button\"\n          class=\"btn role-tertiary add\"\n          :disabled=\"loading || disableAdd\"\n          data-testid=\"array-list-button\"\n          :aria-label=\"_addLabel\"\n          role=\"button\"\n          @click=\"add()\"\n        >\n          <i\n            class=\"mr-5 icon\"\n            :class=\"loading ? ['icon-lg', 'icon-spinner','icon-spin']: [addIcon]\"\n          />\n          {{ _addLabel }}\n        </button>\n      </slot>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .title {\n    margin-bottom: 10px;\n  }\n\n  .required {\n    color: var(--error);\n  }\n\n  .box {\n    display: grid;\n    grid-template-columns: auto $array-list-remove-margin;\n    align-items: center;\n    margin-bottom: 10px;\n    .value {\n      flex: 1;\n      INPUT {\n        height: $unlabeled-input-height;\n      }\n    }\n  }\n  .remove {\n    text-align: right;\n  }\n  .footer {\n    .protip {\n      float: right;\n      padding: 5px 0;\n    }\n  }\n\n  .required {\n    color: var(--error);\n  }\n</style>\n"]}]}