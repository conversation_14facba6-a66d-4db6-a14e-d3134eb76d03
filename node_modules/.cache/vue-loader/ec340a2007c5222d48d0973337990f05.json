{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/RuleSelector.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/RuleSelector.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/RuleSelector.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CA<PERSON>;AACxD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAElD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrB,CAAC;;AAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEhC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC;MACF,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC;IACF,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC;IACF;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACpD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B;MACF,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACvD;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzE,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAChD,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB;EACF,CAAC;AACH,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/RuleSelector.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { _VIEW } from '@shell/config/query-params';\nimport ArrayList from '@shell/components/form/ArrayList';\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport Select from '@shell/components/form/Select';\n\nconst OPERATOR_VALUES = {\n  IS_SET:      'Exists',\n  IS_NOT_SET:  'DoesNotExist',\n  IN_LIST:     'In',\n  NOT_IN_LIST: 'NotIn'\n};\n\nexport default {\n  emits: ['update:value', 'input'],\n\n  components: {\n    ArrayList,\n    LabeledInput,\n    Select\n  },\n\n  props: {\n    value: {\n      type:    Array,\n      default: null\n    },\n    mode: {\n      type:    String,\n      default: _VIEW\n    },\n    addLabel: {\n      type:     String,\n      required: true\n    }\n  },\n\n  data() {\n    return {\n      operatorOptions: [\n        {\n          label: 'is set',\n          value: OPERATOR_VALUES.IS_SET,\n        },\n        {\n          label: 'is not set',\n          value: OPERATOR_VALUES.IS_NOT_SET,\n        },\n        {\n          label: 'in list',\n          value: OPERATOR_VALUES.IN_LIST,\n        },\n        {\n          label: 'not in list',\n          value: OPERATOR_VALUES.NOT_IN_LIST,\n        }\n      ],\n      optionsWithValueDisabled: [\n        OPERATOR_VALUES.IS_SET,\n        OPERATOR_VALUES.IS_NOT_SET\n      ],\n      defaultAddValue: {\n        key:      '',\n        operator: OPERATOR_VALUES.IS_SET,\n      }\n    };\n  },\n\n  computed: {\n    localValue: {\n      get() {\n        return this.value;\n      },\n      set(localValue) {\n        this.$emit('update:value', localValue);\n      }\n    }\n  },\n\n  methods: {\n    onOperatorInput(scope, operator) {\n      scope.row.value.operator = operator;\n      if (this.optionsWithValueDisabled.includes(operator)) {\n        if (scope.row.value.values) {\n          delete scope.row.value.values;\n        }\n      } else {\n        scope.row.value.values = scope.row.value.values || [];\n      }\n      scope.queueUpdate();\n    },\n\n    isValueDisabled(scope) {\n      return this.optionsWithValueDisabled.includes(scope.row.value.operator);\n    },\n    getValue(scope) {\n      return scope.row.value.values?.join(',') || '';\n    },\n    onValueInput(scope, rawValue) {\n      scope.row.value.values = rawValue.split(',')\n        .map((entry) => entry.trim());\n      scope.queueUpdate();\n    }\n  },\n};\n</script>\n\n<template>\n  <div\n    class=\"rule-selector\"\n    :class=\"{[mode]: true}\"\n  >\n    <ArrayList\n      :value=\"value\"\n      :protip=\"false\"\n      :show-header=\"true\"\n      :add-label=\"addLabel\"\n      :default-add-value=\"defaultAddValue\"\n      :mode=\"mode\"\n      @update:value=\"$emit('input', $event)\"\n    >\n      <template v-slot:column-headers>\n        <div class=\"box\">\n          <div class=\"key\">\n            Key\n          </div>\n          <div class=\"operator\">\n            Operator\n          </div>\n          <div class=\"value\">\n            Value\n          </div>\n          <div />\n        </div>\n      </template>\n      <template v-slot:columns=\"scope\">\n        <div class=\"key\">\n          <LabeledInput\n            v-model:value=\"scope.row.value.key\"\n            :mode=\"mode\"\n          />\n        </div>\n        <div class=\"operator\">\n          <Select\n            :mode=\"mode\"\n            :value=\"scope.row.value.operator\"\n            :options=\"operatorOptions\"\n            @update:value=\"onOperatorInput(scope, $event)\"\n          />\n        </div>\n        <div class=\"value\">\n          <LabeledInput\n            :disabled=\"isValueDisabled(scope)\"\n            :value=\"getValue(scope)\"\n            :mode=\"mode\"\n            @update:value=\"onValueInput(scope, $event)\"\n          />\n        </div>\n      </template>\n    </ArrayList>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.rule-selector {\n  &:not(.view) table {\n    table-layout: initial;\n  }\n\n   :deep() .box {\n    display: grid;\n    grid-template-columns: 25% 25% 25% 15%;\n    column-gap: 1.75%;\n    align-items: center;\n    margin-bottom: 10px;\n\n    .key,\n    .value,\n    .operator {\n      height: 100%;\n    }\n  }\n}\n</style>\n"]}]}