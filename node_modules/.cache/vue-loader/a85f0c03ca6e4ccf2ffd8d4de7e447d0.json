{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SimpleBox.vue?vue&type=style&index=1&id=fabb7f74&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SimpleBox.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5zaW1wbGUtYm94IHsKICAkcGFkZGluZzogMTVweDsKCiAgYmFja2dyb3VuZDogdmFyKC0tc2ltcGxlLWJveC1iZykgMCUgMCUgbm8tcmVwZWF0IHBhZGRpbmctYm94OwogIGJveC1zaGFkb3c6IDBweCAwcHggMTBweCB2YXIoLS1zaW1wbGUtYm94LXNoYWRvdyk7CiAgYm9yZGVyOiAxcHggc29saWQgdmFyKC0tc2ltcGxlLWJveC1ib3JkZXIpOwogIHBhZGRpbmc6ICRwYWRkaW5nOwoKICAudG9wIHsKICAgIGxpbmUtaGVpZ2h0OiAyNHB4OwogICAgZm9udC1zaXplOiAxOHB4OwogICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkIHZhcigtLXNpbXBsZS1ib3gtZGl2aWRlcik7CiAgICBwYWRkaW5nLWJvdHRvbTogJHBhZGRpbmc7CiAgICBtYXJnaW46IDAgLTE1cHggMTBweCAtMTVweDsKICAgIHBhZGRpbmc6IDAgMTVweCAxNXB4IDE1cHg7CiAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgZGlzcGxheTogZmxleAoKICAgICYgQlVUVE9OIHsKICAgICAgcGFkZGluZzogMDsKICAgICAgaGVpZ2h0OiBmaXQtY29udGVudDsKICAgICAgYWxpZ24tc2VsZjogZmxleC1zdGFydDsKICAgIH0KCiAgICAmIEgyewogICAgICBtYXJnaW4tYm90dG9tOiAwOwogICAgfQogIH0KCiAgLmNvbnRlbnQgewogICAgcGFkZGluZzogJHBhZGRpbmc7CiAgfQp9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SimpleBox.vue"], "names": [], "mappings": ";AAuFA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEjB,CAAC,CAAC,CAAC,EAAE;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEZ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB;;IAEA,EAAE,CAAC,CAAC;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAClB;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SimpleBox.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport Closeable from '@shell/mixins/closeable';\n\nexport default {\n  name: 'SimpleBox',\n\n  emits: ['close'],\n\n  mixins: [Closeable],\n\n  props: {\n    title: {\n      type:    String,\n      default: null,\n    },\n\n    canClose: {\n      type:    Boolean,\n      default: false\n    }\n  },\n\n  methods: {\n    closeBox(event) {\n      this.hide();\n      this.$emit('close', event);\n    }\n  }\n};\n</script>\n\n<template>\n  <div\n    v-if=\"shown\"\n    class=\"simple-box\"\n    data-testid=\"simple-box-container\"\n  >\n    <div\n      v-if=\"title || canClose || $slots.title\"\n      class=\"top\"\n    >\n      <slot name=\"title\">\n        <h2\n          v-if=\"title\"\n          data-testid=\"simple-box-title\"\n        >\n          {{ title }}\n        </h2>\n      </slot>\n      <div\n        v-if=\"canClose || pref\"\n        class=\"close-button\"\n        data-testid=\"simple-box-close\"\n        @click=\"closeBox($event)\"\n      >\n        <i class=\"icon icon-close\" />\n      </div>\n    </div>\n    <div class=\"content\">\n      <slot />\n    </div>\n  </div>\n</template>\n<style lang=\"scss\" scoped>\n.top {\n  display: flex;\n  position: relative;\n  > h2 {\n    flex: 1;\n  }\n}\n.close-button {\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 2px;\n  > i {\n    font-size: 14px;\n    opacity: 0.5;\n  }\n  &:hover {\n    background-color: var(--wm-closer-hover-bg);\n  }\n}\n</style>\n<style lang=\"scss\">\n.simple-box {\n  $padding: 15px;\n\n  background: var(--simple-box-bg) 0% 0% no-repeat padding-box;\n  box-shadow: 0px 0px 10px var(--simple-box-shadow);\n  border: 1px solid var(--simple-box-border);\n  padding: $padding;\n\n  .top {\n    line-height: 24px;\n    font-size: 18px;\n    border-bottom: 1px solid var(--simple-box-divider);\n    padding-bottom: $padding;\n    margin: 0 -15px 10px -15px;\n    padding: 0 15px 15px 15px;\n    align-items: center;\n    display: flex\n\n    & BUTTON {\n      padding: 0;\n      height: fit-content;\n      align-self: flex-start;\n    }\n\n    & H2{\n      margin-bottom: 0;\n    }\n  }\n\n  .content {\n    padding: $padding;\n  }\n}\n</style>\n"]}]}