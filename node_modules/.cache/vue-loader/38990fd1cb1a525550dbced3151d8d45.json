{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/NodeGroup.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/NodeGroup.vue", "mtime": 1754992537319}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/NodeGroup.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAElE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAExD,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC;;AAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAErC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAErB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEpc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX;IACF,CAAC;IACD,CAAC,CAAC,CAAC,EAAE;MACH,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX;IACF;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE9F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MAClH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACvD,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClH,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE;MACF,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClE,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;MACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC/C;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1B,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpC;MACF,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACX;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;IAE9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAErD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACzB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACrD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEhG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1H,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEpC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1D,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtE,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9J,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC/J;QACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAElC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrJ,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC5E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;UAEvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR;QACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAExC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAClC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC;MACJ;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC9F,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9G;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACX,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE;MACtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnJ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;MACrE,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACnC;MACF;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxG,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpE,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C;MACF;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACxF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5E,CAAC,CAAC;;QAEF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAElB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;MAE/C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR;MACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;MAExG,CAAC,CAAC,EAAE;QACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACvF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACzI,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAC7I;QACF;MACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1B;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACtE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAErD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAClC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;;UAEjC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;cACjG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1C;YACF,CAAC,CAAC;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACzC,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAClF;QACF,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC3C,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;;UAEnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACrD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE;QACF,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtE,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvH;MACF,CAAC,CAAC;;MAEF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,CAAC;IACJ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb;;MAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAElG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb;MACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEvE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACxB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEnE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC9C;QACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC5B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEhE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC;EACH,CAAC;AACH,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/NodeGroup.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent, PropType } from 'vue';\nimport { mapGetters, Store } from 'vuex';\nimport debounce from 'lodash/debounce';\n\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\nimport { randomStr } from '@shell/utils/string';\nimport { isEmpty } from '@shell/utils/object';\n\nimport LabeledSelect from '@shell/components/form/LabeledSelect.vue';\nimport LabeledInput from '@components/Form/LabeledInput/LabeledInput.vue';\nimport Checkbox from '@components/Form/Checkbox/Checkbox.vue';\nimport KeyValue from '@shell/components/form/KeyValue.vue';\nimport Banner from '@components/Banner/Banner.vue';\nimport UnitInput from '@shell/components/form/UnitInput.vue';\nimport FileSelector from '@shell/components/form/FileSelector.vue';\n\nimport { MANAGED_TEMPLATE_PREFIX, parseTags } from '../../util/aws';\nimport { AWS } from '../../types';\nimport { DEFAULT_NODE_GROUP_CONFIG } from './CruEKS.vue';\n\n// map between fields in rancher eksConfig and amazon launch templates\nconst launchTemplateFieldMapping: {[key: string]: string} = {\n  imageId:      'ImageId',\n  userData:     'UserData',\n  instanceType: 'InstanceType',\n  ec2SshKey:    '',\n  resourceTags: 'TagSpecifications',\n  diskSize:     'BlockDeviceMappings'\n};\n\nconst DEFAULT_USER_DATA =\n`MIME-Version: 1.0\nContent-Type: multipart/mixed; boundary=\"==MYBOUNDARY==\"\n\n--==MYBOUNDARY==\nContent-Type: text/x-shellscript; charset=\"us-ascii\"\n\n#!/bin/bash\necho \"Running custom user data script\"\n\n--==MYBOUNDARY==--\\\\`;\n\nexport default defineComponent({\n  name: 'EKSNodePool',\n\n  emits: ['update:instanceType', 'update:spotInstanceTypes', 'update:ec2SshKey', 'update:launchTemplate', 'update:nodeRole', 'update:nodeRole', 'update:version', 'update:poolIsUpgrading', 'error', 'update:resourceTags', 'update:diskSize', 'update:nodegroupName', 'update:desiredSize', 'update:minSize', 'update:maxSize', 'update:labels', 'update:tags', 'update:imageId', 'update:gpu', 'update:requestSpotInstances', 'update:userData', 'update:ec2SshKey'],\n\n  components: {\n    LabeledInput,\n    LabeledSelect,\n    KeyValue,\n    Banner,\n    Checkbox,\n    UnitInput,\n    FileSelector\n  },\n\n  props: {\n    nodeRole: {\n      type:    String,\n      default: ''\n    },\n    resourceTags: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    requestSpotInstances: {\n      type:    Boolean,\n      default: false\n    },\n    spotInstanceTypes: {\n      type:    Array,\n      default: () => []\n    },\n    labels: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    tags: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    gpu: {\n      type:    Boolean,\n      default: false\n    },\n    userData: {\n      type:    String,\n      default: ''\n    },\n    instanceType: {\n      type:    String,\n      default: ''\n    },\n    imageId: {\n      type:    [String, null],\n      default: ''\n    },\n    desiredSize: {\n      type:    [Number, String],\n      default: null\n    },\n    minSize: {\n      type:    [Number, String],\n      default: null\n    },\n    maxSize: {\n      type:    [Number, String],\n      default: null\n    },\n    diskSize: {\n      type:    Number,\n      default: null\n    },\n    ec2SshKey: {\n      type:    String,\n      default: ''\n    },\n    nodegroupName: {\n      type:    String,\n      default: ''\n    },\n    region: {\n      type:    String,\n      default: ''\n    },\n    amazonCredentialSecret: {\n      type:    String,\n      default: ''\n    },\n\n    launchTemplate: {\n      type:    Object,\n      default: () => {}\n    },\n\n    version: {\n      type:    String,\n      default: ''\n    },\n\n    clusterVersion: {\n      type:    String,\n      default: ''\n    },\n\n    originalClusterVersion: {\n      type:    String,\n      default: ''\n    },\n\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n\n    ec2Roles: {\n      type:    Array as PropType<AWS.IamRole[]>,\n      default: () => []\n    },\n\n    isNewOrUnprovisioned: {\n      type:    Boolean,\n      default: true\n    },\n\n    poolIsNew: {\n      type:    Boolean,\n      default: false\n    },\n\n    instanceTypeOptions: {\n      type:    Array,\n      default: () => []\n    },\n\n    spotInstanceTypeOptions: {\n      type:    Array,\n      default: () => []\n    },\n\n    launchTemplates: {\n      type:    Array as PropType<AWS.LaunchTemplate[]>,\n      default: () => []\n    },\n\n    sshKeyPairs: {\n      type:    Array as PropType<string[]>,\n      default: () => []\n    },\n\n    normanCluster: {\n      type:    Object,\n      default: null\n    },\n\n    poolIsUpgrading: {\n      type:    Boolean,\n      default: false\n    },\n\n    loadingInstanceTypes: {\n      type:    Boolean,\n      default: false\n    },\n\n    loadingRoles: {\n      type:    Boolean,\n      default: false\n    },\n\n    loadingLaunchTemplates: {\n      type:    Boolean,\n      default: false\n    },\n\n    loadingSshKeyPairs: {\n      type:    Boolean,\n      default: false\n    },\n\n    rules: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  created() {\n    this.debouncedSetValuesFromTemplate = debounce(this.setValuesFromTemplate, 500);\n  },\n\n  data() {\n    const store = this.$store as Store<any>;\n    const t = store.getters['i18n/t'];\n\n    return {\n      originalNodeVersion:   this.version,\n      defaultTemplateOption: { LaunchTemplateName: t('eks.defaultCreateOne') } as AWS.LaunchTemplate,\n\n      defaultNodeRoleOption:          { RoleName: t('eks.defaultCreateOne') },\n      loadingSelectedVersion:         false,\n      // once a specific lt has been selected, an additional query is made to get full information on every version of it\n      selectedLaunchTemplateInfo:     {} as AWS.LaunchTemplateDetail,\n      debouncedSetValuesFromTemplate: null as Function | null,\n      // the keyvalue component needs to be re-rendered if the value prop is updated by parent component when as-map=true\n      // TODO nb file an issue\n      resourceTagKey:                 randomStr()\n    };\n  },\n\n  watch: {\n    selectedLaunchTemplate: {\n      handler(neu) {\n        if (neu && neu.LaunchTemplateId && this.amazonCredentialSecret) {\n          this.fetchLaunchTemplateVersionInfo(this.selectedLaunchTemplate);\n        }\n      },\n      immediate: true\n    },\n\n    amazonCredentialSecret: {\n      handler() {\n        this.fetchLaunchTemplateVersionInfo(this.selectedLaunchTemplate);\n      },\n      immediate: true\n    },\n\n    'selectedVersionData'(neu = {}, old = {}) {\n      this.loadingSelectedVersion = true;\n      if (this.debouncedSetValuesFromTemplate) {\n        this.debouncedSetValuesFromTemplate(neu, old);\n      }\n    },\n\n    'requestSpotInstances'(neu) {\n      if (neu && !this.templateValue('instanceType')) {\n        this.$emit('update:instanceType', null);\n      } else {\n        this.$emit('update:spotInstanceTypes', null);\n      }\n    },\n\n    sshKeyPairs: {\n      handler(neu) {\n        if (!neu.includes(this.ec2SshKey)) {\n          this.$emit('update:ec2SshKey', '');\n        }\n      },\n      deep: true\n    }\n  },\n\n  computed: {\n    ...mapGetters({ t: 'i18n/t' }),\n\n    rancherTemplate() {\n      const eksStatus = this.normanCluster?.eksStatus || {};\n\n      return eksStatus.managedLaunchTemplateID;\n    },\n\n    hasRancherLaunchTemplate() {\n      const eksStatus = this.normanCluster?.eksStatus || {};\n      const nodegroupName = this.nodegroupName;\n      const nodeGroupTemplateVersion = (eksStatus?.managedLaunchTemplateVersions || {})[nodegroupName];\n\n      return isEmpty(this.launchTemplate) && !isEmpty(eksStatus.managedLaunchTemplateID) && !isEmpty(nodeGroupTemplateVersion);\n    },\n\n    hasUserLaunchTemplate() {\n      const { launchTemplate = {} } = this;\n\n      return !!launchTemplate?.id && !!launchTemplate?.version;\n    },\n\n    hasNoLaunchTemplate() {\n      return !this.hasRancherLaunchTemplate && !this.hasUserLaunchTemplate;\n    },\n\n    launchTemplateOptions(): AWS.LaunchTemplate[] {\n      return [this.defaultTemplateOption, ...this.launchTemplates.filter((template) => !(template?.LaunchTemplateName || '').startsWith(MANAGED_TEMPLATE_PREFIX))];\n    },\n\n    selectedLaunchTemplate: {\n      get(): AWS.LaunchTemplate {\n        if (this.hasRancherLaunchTemplate) {\n          return { LaunchTemplateId: this.rancherTemplate, LaunchTemplateName: this.t('eks.nodeGroups.launchTemplate.rancherManaged', { name: this.rancherTemplate }) };\n        }\n        const id = this.launchTemplate?.id;\n\n        return this.launchTemplateOptions.find((lt: AWS.LaunchTemplate) => lt.LaunchTemplateId && lt.LaunchTemplateId === id) || this.defaultTemplateOption;\n      },\n      set(neu: AWS.LaunchTemplate) {\n        if (neu.LaunchTemplateName === this.defaultTemplateOption.LaunchTemplateName) {\n          this.$emit('update:launchTemplate', {});\n\n          return;\n        }\n        const name = neu.LaunchTemplateName;\n        const id = neu.LaunchTemplateId;\n        const version = neu.DefaultVersionNumber;\n\n        this.$emit('update:launchTemplate', {\n          name, id, version\n        });\n      }\n    },\n\n    launchTemplateVersionOptions(): number[] {\n      if (this.selectedLaunchTemplateInfo && this.selectedLaunchTemplateInfo?.LaunchTemplateVersions) {\n        return this.selectedLaunchTemplateInfo.LaunchTemplateVersions.map((version) => version.VersionNumber).sort();\n      }\n\n      return [];\n    },\n\n    selectedVersionInfo(): AWS.LaunchTemplateVersion | null {\n      return (this.selectedLaunchTemplateInfo?.LaunchTemplateVersions || []).find((v: any) => v.VersionNumber === this.launchTemplate?.version) || null;\n    },\n\n    selectedVersionData(): AWS.LaunchTemplateVersionData | undefined {\n      return this.selectedVersionInfo?.LaunchTemplateData;\n    },\n\n    displayNodeRole: {\n      get() {\n        const arn = this.nodeRole;\n\n        if (!arn) {\n          return this.defaultNodeRoleOption;\n        }\n\n        return this.ec2Roles.find((role: AWS.IamRole) => role.Arn === arn) ;\n      },\n      set(neu: AWS.IamRole) {\n        if (neu.Arn) {\n          this.$emit('update:nodeRole', neu.Arn);\n        } else {\n          this.$emit('update:nodeRole', '');\n        }\n      }\n    },\n\n    userDataPlaceholder() {\n      return DEFAULT_USER_DATA;\n    },\n\n    poolIsUnprovisioned() {\n      return this.isNewOrUnprovisioned || this.poolIsNew;\n    },\n\n    clusterWillUpgrade() {\n      return this.clusterVersion !== this.originalClusterVersion;\n    },\n\n    nodeCanUpgrade() {\n      return !this.clusterWillUpgrade && this.originalNodeVersion !== this.clusterVersion && !this.poolIsNew;\n    },\n\n    willUpgrade: {\n      get() {\n        return this.nodeCanUpgrade && this.version === this.clusterVersion;\n      },\n      set(neu: boolean) {\n        if (neu) {\n          this.$emit('update:version', this.clusterVersion);\n          this.$emit('update:poolIsUpgrading', true);\n        } else {\n          this.$emit('update:version', this.originalNodeVersion);\n          this.$emit('update:poolIsUpgrading', false);\n        }\n      }\n    },\n\n    minMaxDesiredErrors() {\n      const errs = (this.rules?.minMaxDesired || []).reduce((errs: string[], rule: Function) => {\n        const err = rule({\n          minSize: this.minSize, maxSize: this.maxSize, desiredSize: this.desiredSize\n        });\n\n        if (err) {\n          errs.push(err);\n        }\n\n        return errs;\n      }, [] as string[]);\n\n      return errs.length ? errs.join(' ') : null;\n    },\n\n    isView() {\n      return this.mode === _VIEW;\n    }\n  },\n\n  methods: {\n    async fetchLaunchTemplateVersionInfo(launchTemplate: AWS.LaunchTemplate) {\n      const { region, amazonCredentialSecret } = this;\n\n      if (!region || !amazonCredentialSecret || this.isView) {\n        return;\n      }\n      const store = this.$store as Store<any>;\n      const ec2Client = await store.dispatch('aws/ec2', { region, cloudCredentialId: amazonCredentialSecret });\n\n      try {\n        if (launchTemplate.LaunchTemplateName !== this.defaultTemplateOption.LaunchTemplateName) {\n          if (launchTemplate.LaunchTemplateId) {\n            this.selectedLaunchTemplateInfo = await ec2Client.describeLaunchTemplateVersions({ LaunchTemplateId: launchTemplate.LaunchTemplateId });\n          } else {\n            this.selectedLaunchTemplateInfo = await ec2Client.describeLaunchTemplateVersions({ LaunchTemplateName: launchTemplate.LaunchTemplateName });\n          }\n        }\n      } catch (err) {\n        this.$emit('error', err);\n      }\n    },\n\n    setValuesFromTemplate(neu = {} as AWS.LaunchTemplateVersionData, old = {} as AWS.LaunchTemplateVersionData) {\n      Object.keys(launchTemplateFieldMapping).forEach((rancherKey: string) => {\n        const awsKey = launchTemplateFieldMapping[rancherKey];\n\n        if (awsKey === 'TagSpecifications') {\n          const { TagSpecifications } = neu;\n\n          if (TagSpecifications) {\n            const tags = {} as {[key:string]: string};\n\n            TagSpecifications.forEach((tag: {Tags?: {Key: string, Value: string}[], ResourceType?: string}) => {\n              if (tag.ResourceType === 'instance' && tag.Tags && tag.Tags.length) {\n                Object.assign(tags, parseTags(tag.Tags));\n              }\n            });\n            this.$emit('update:resourceTags', tags);\n          } else {\n            this.$emit('update:resourceTags', { ...DEFAULT_NODE_GROUP_CONFIG.resourceTags });\n          }\n        } else if (awsKey === 'BlockDeviceMappings') {\n          const { BlockDeviceMappings } = neu;\n\n          if (BlockDeviceMappings && BlockDeviceMappings.length) {\n            const size = BlockDeviceMappings[0]?.Ebs?.VolumeSize;\n\n            this.$emit('update:diskSize', size);\n          } else {\n            this.$emit('update:diskSize', DEFAULT_NODE_GROUP_CONFIG.diskSize);\n          }\n        } else if (this.templateValue(rancherKey)) {\n          this.$emit(`update:${ rancherKey }`, this.templateValue(rancherKey));\n        } else {\n          this.$emit(`update:${ rancherKey }`, DEFAULT_NODE_GROUP_CONFIG[rancherKey as keyof typeof DEFAULT_NODE_GROUP_CONFIG]);\n        }\n      });\n\n      this.$nextTick(() => {\n        this.resourceTagKey = randomStr();\n        this.loadingSelectedVersion = false;\n      });\n    },\n\n    templateValue(field: string): string | null | AWS.TagSpecification | AWS.TagSpecification[] | AWS.BlockDeviceMapping[] {\n      if (this.hasNoLaunchTemplate) {\n        return null;\n      }\n\n      const launchTemplateKey = launchTemplateFieldMapping[field] as keyof AWS.LaunchTemplateVersionData;\n\n      if (!launchTemplateKey) {\n        return null;\n      }\n      const launchTemplateVal = this.selectedVersionData?.[launchTemplateKey];\n\n      if (launchTemplateVal !== undefined && (!(typeof launchTemplateVal === 'object') || !isEmpty(launchTemplateVal))) {\n        if (field === 'diskSize') {\n          const blockMapping = launchTemplateVal[0] as AWS.BlockDeviceMapping;\n\n          return blockMapping?.Ebs?.VolumeSize || null;\n        }\n        if (field === 'resourceTags') {\n          const tags = (launchTemplateVal || []) as AWS.TagSpecification[];\n\n          return tags.filter((tag: AWS.TagSpecification) => tag.ResourceType === 'instance')[0];\n        }\n\n        return launchTemplateVal;\n      }\n\n      return null;\n    },\n  },\n});\n</script>\n\n<template>\n  <div>\n    <h3>{{ t('eks.nodeGroups.groupDetails') }}</h3>\n    <div class=\"row mb-10\">\n      <div class=\"col span-6\">\n        <LabeledInput\n          :value=\"nodegroupName\"\n          label-key=\"eks.nodeGroups.name.label\"\n          :mode=\"mode\"\n          :disabled=\"!poolIsUnprovisioned\"\n          :rules=\"rules.nodegroupName\"\n          data-testid=\"eks-nodegroup-name\"\n          required\n          @update:value=\"$emit('update:nodegroupName', $event)\"\n        />\n      </div>\n\n      <div class=\"col span-6\">\n        <LabeledSelect\n          v-model:value=\"displayNodeRole\"\n          :mode=\"mode\"\n          data-testid=\"eks-noderole\"\n          label-key=\"eks.nodeGroups.nodeRole.label\"\n          :options=\"[defaultNodeRoleOption, ...ec2Roles]\"\n          option-label=\"RoleName\"\n          option-key=\"Arn\"\n          :disabled=\"!poolIsUnprovisioned\"\n          :loading=\"loadingRoles\"\n        />\n      </div>\n    </div>\n    <div class=\"row mb-10\">\n      <div class=\"col span-4\">\n        <LabeledInput\n          type=\"number\"\n          :value=\"desiredSize\"\n          label-key=\"eks.nodeGroups.desiredSize.label\"\n          :mode=\"mode\"\n          :rules=\"rules.desiredSize\"\n          @update:value=\"$emit('update:desiredSize', parseInt($event))\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <LabeledInput\n          type=\"number\"\n          :value=\"minSize\"\n          label-key=\"eks.nodeGroups.minSize.label\"\n          :mode=\"mode\"\n          :rules=\"rules.minSize\"\n          @update:value=\"$emit('update:minSize', parseInt($event))\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <LabeledInput\n          type=\"number\"\n          :value=\"maxSize\"\n          label-key=\"eks.nodeGroups.maxSize.label\"\n          :mode=\"mode\"\n          :rules=\"rules.maxSize\"\n          @update:value=\"$emit('update:maxSize', parseInt($event))\"\n        />\n      </div>\n    </div>\n    <Banner\n      v-if=\"!!minMaxDesiredErrors\"\n      color=\"error\"\n      :label=\"minMaxDesiredErrors\"\n    />\n    <div class=\"row mb-10\">\n      <div class=\"col span-6 mt-20\">\n        <KeyValue\n          :mode=\"mode\"\n          :title=\"t('eks.nodeGroups.groupLabels.label')\"\n          :read-allowed=\"false\"\n          :value=\"labels\"\n          :as-map=\"true\"\n          @update:value=\"$emit('update:labels', $event)\"\n        >\n          <template #title>\n            <h4>\n              {{ t('eks.nodeGroups.groupLabels.label') }}\n            </h4>\n          </template>\n        </KeyValue>\n      </div>\n      <div class=\"col span-6 mt-20\">\n        <KeyValue\n          :mode=\"mode\"\n          :title=\"t('eks.nodeGroups.groupTags.label')\"\n          :read-allowed=\"false\"\n          :as-map=\"true\"\n          :value=\"tags\"\n          data-testid=\"eks-resource-tags-input\"\n          @update:value=\"$emit('update:tags', $event)\"\n        >\n          <template #title>\n            <h4>{{ t('eks.nodeGroups.groupTags.label') }}</h4>\n          </template>\n        </KeyValue>\n      </div>\n    </div>\n    <hr\n      class=\"mb-20\"\n      role=\"none\"\n    >\n    <h3>{{ t('eks.nodeGroups.templateDetails') }}</h3>\n    <Banner\n      v-if=\"clusterWillUpgrade && !poolIsUnprovisioned\"\n      color=\"info\"\n      label-key=\"eks.nodeGroups.kubernetesVersion.clusterWillUpgrade\"\n      data-testid=\"eks-version-upgrade-banner\"\n    />\n    <div class=\"row mb-10\">\n      <div class=\"col span-4 upgrade-version\">\n        <LabeledInput\n          v-if=\"!nodeCanUpgrade\"\n          label-key=\"eks.nodeGroups.kubernetesVersion.label\"\n          :disabled=\"true\"\n          :value=\"version\"\n          data-testid=\"eks-version-display\"\n        />\n        <Checkbox\n          v-else\n          v-model:value=\"willUpgrade\"\n          :label=\"t('eks.nodeGroups.kubernetesVersion.upgrade', {from: originalNodeVersion, to: clusterVersion})\"\n          data-testid=\"eks-version-upgrade-checkbox\"\n          :disabled=\"isView\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <LabeledSelect\n          v-model:value=\"selectedLaunchTemplate\"\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.launchTemplate.label\"\n          :options=\"launchTemplateOptions\"\n          option-label=\"LaunchTemplateName\"\n          option-key=\"LaunchTemplateId\"\n          :disabled=\"!poolIsUnprovisioned\"\n          :loading=\"loadingLaunchTemplates\"\n          data-testid=\"eks-launch-template-dropdown\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <LabeledSelect\n          v-if=\"hasUserLaunchTemplate\"\n          :value=\"launchTemplate.version\"\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.launchTemplate.version\"\n          :options=\"launchTemplateVersionOptions\"\n          data-testid=\"eks-launch-template-version-dropdown\"\n          @update:value=\"$emit('update:launchTemplate', {...launchTemplate, version: $event})\"\n        />\n      </div>\n    </div>\n    <Banner\n      color=\"info\"\n      label-key=\"eks.nodeGroups.imageId.tooltip\"\n    />\n    <div class=\"row mb-10\">\n      <div class=\"col span-4\">\n        <LabeledInput\n          label-key=\"eks.nodeGroups.imageId.label\"\n          :mode=\"mode\"\n          :value=\"imageId\"\n          :disabled=\"hasUserLaunchTemplate\"\n          data-testid=\"eks-image-id-input\"\n          @update:value=\"$emit('update:imageId', $event)\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <LabeledSelect\n          :required=\"!requestSpotInstances && !templateValue('instanceType')\"\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.instanceType.label\"\n          :options=\"instanceTypeOptions\"\n          :loading=\"loadingInstanceTypes\"\n          :value=\"instanceType\"\n          :disabled=\"!!templateValue('instanceType') || requestSpotInstances\"\n          :tooltip=\"(requestSpotInstances && !templateValue('instanceType')) ? t('eks.nodeGroups.instanceType.tooltip'): ''\"\n          :rules=\"!requestSpotInstances ? rules.instanceType : []\"\n          data-testid=\"eks-instance-type-dropdown\"\n          @update:value=\"$emit('update:instanceType', $event)\"\n        />\n      </div>\n\n      <div class=\"col span-4\">\n        <UnitInput\n          :required=\"!templateValue('diskSize')\"\n          label-key=\"eks.nodeGroups.diskSize.label\"\n          :mode=\"mode\"\n          :value=\"diskSize\"\n          suffix=\"GB\"\n          :loading=\"loadingSelectedVersion\"\n          :disabled=\"!!templateValue('diskSize') || loadingSelectedVersion\"\n          :rules=\"rules.diskSize\"\n          data-testid=\"eks-disksize-input\"\n          @update:value=\"$emit('update:diskSize', $event)\"\n        />\n      </div>\n    </div>\n    <Banner\n      v-if=\"requestSpotInstances && hasUserLaunchTemplate\"\n      color=\"warning\"\n      :label=\"t('eks.nodeGroups.requestSpotInstances.warning')\"\n      data-testid=\"eks-spot-instance-banner\"\n    />\n    <div class=\"row mb-10\">\n      <div class=\"col span-4\">\n        <Checkbox\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.gpu.label\"\n          :value=\"gpu\"\n          :disabled=\"!!templateValue('imageId') || hasRancherLaunchTemplate\"\n          :tooltip=\"templateValue('imageId') ? t('eks.nodeGroups.gpu.tooltip') : ''\"\n          data-testid=\"eks-gpu-input\"\n          @update:value=\"$emit('update:gpu', $event)\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <Checkbox\n          :value=\"requestSpotInstances\"\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.requestSpotInstances.label\"\n          :disabled=\"hasRancherLaunchTemplate\"\n          @update:value=\"$emit('update:requestSpotInstances', $event)\"\n        />\n      </div>\n    </div>\n    <div\n      v-if=\"requestSpotInstances && !templateValue('instanceType')\"\n      class=\"row mb-10\"\n    >\n      <div\n        class=\"col span-6\"\n      >\n        <LabeledSelect\n          :mode=\"mode\"\n          :value=\"spotInstanceTypes\"\n          label-key=\"eks.nodeGroups.spotInstanceTypes.label\"\n          :options=\"spotInstanceTypeOptions\"\n          :multiple=\"true\"\n          :loading=\"loadingSelectedVersion || loadingInstanceTypes\"\n          data-testid=\"eks-spot-instance-type-dropdown\"\n          @update:value=\"$emit('update:spotInstanceTypes', $event)\"\n        />\n      </div>\n    </div>\n    <div class=\"row mb-15\">\n      <div class=\"col span-6 user-data\">\n        <LabeledInput\n          label-key=\"eks.nodeGroups.userData.label\"\n          :mode=\"mode\"\n          type=\"multiline\"\n          :value=\"userData\"\n          :disabled=\"hasUserLaunchTemplate\"\n          :placeholder=\"userDataPlaceholder\"\n          :sub-label=\"t('eks.nodeGroups.userData.tooltip', {}, true)\"\n          @update:value=\"$emit('update:userData', $event)\"\n        />\n        <FileSelector\n          :mode=\"mode\"\n          :label=\"t('generic.readFromFile')\"\n          class=\"role-tertiary mt-20\"\n          @selected=\"$emit('update:userData', $event)\"\n        />\n      </div>\n      <div class=\"col span-6\">\n        <LabeledSelect\n          :loading=\"loadingSshKeyPairs\"\n          :value=\"ec2SshKey\"\n          :options=\"sshKeyPairs\"\n          label-key=\"eks.nodeGroups.ec2SshKey.label\"\n          :mode=\"mode\"\n          :disabled=\"hasUserLaunchTemplate\"\n          :taggable=\"true\"\n          :searchable=\"true\"\n          data-testid=\"eks-nodegroup-ec2-key-select\"\n          @update:value=\"$emit('update:ec2SshKey', $event)\"\n        />\n      </div>\n    </div>\n    <div row=\"mb-10\">\n      <div class=\"col span-12 mt-20\">\n        <KeyValue\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.resourceTags.label\"\n          :value=\"resourceTags\"\n          :disabled=\"hasUserLaunchTemplate\"\n          :read-allowed=\"false\"\n          :as-map=\"true\"\n          @update:value=\"$emit('update:resourceTags', $event)\"\n        >\n          <template #title>\n            <h4>\n              {{ t('eks.nodeGroups.resourceTags.label') }}\n            </h4>\n          </template>\n        </KeyValue>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.user-data{\n  &>button{\n    float: right;\n  }\n}\n\n.upgrade-version {\n  display: flex;\n  align-items: center;\n}\n</style>\n"]}]}