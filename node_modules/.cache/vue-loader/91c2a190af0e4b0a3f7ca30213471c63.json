{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/PromptChangePassword.vue?vue&type=style&index=0&id=63744d72&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/PromptChangePassword.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLnByb21wdC1wYXNzd29yZCB7CiAgICA6ZGVlcCgpIC5jYXJkLXdyYXAgewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwoKICAgICAgLmNhcmQtYm9keSB7CiAgICAgICAgZmxleDogMTsKICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHN0YXJ0OwogICAgICAgICYgPiBkaXYgewogICAgICAgICAgZmxleDogMTsKICAgICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgfQogICAgICB9CgogICAgICAuY2FyZC1hY3Rpb25zIHsKICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7CiAgICAgICAgd2lkdGg6IDEwMCU7CiAgICAgIH0KICAgIH0KICB9CgogIC5wcm9tcHQtcGFzc3dvcmQgewogICAgZmxleDogMTsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBmb3JtIHsKICAgICAgZmxleDogMTsKICAgIH0KICB9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/PromptChangePassword.vue"], "names": [], "mappings": ";EA6FE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,EAAE,EAAE,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,EAAE;MACH,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACT;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/PromptChangePassword.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapGetters } from 'vuex';\nimport ChangePassword from '@shell/components/form/ChangePassword';\nimport { Card } from '@components/Card';\nimport AsyncButton from '@shell/components/AsyncButton';\nimport AppModal from '@shell/components/AppModal.vue';\n\nexport default {\n  components: {\n    Card, AsyncButton, ChangePassword, AppModal\n  },\n  data() {\n    return {\n      valid: false, password: '', showModal: false\n    };\n  },\n  computed: { ...mapGetters({ t: 'i18n/t' }) },\n  methods:  {\n    show(show) {\n      if (show) {\n        this.showModal = true;\n      } else {\n        this.showModal = false;\n      }\n    },\n    async submit(buttonCb) {\n      try {\n        await this.$refs.changePassword.save();\n        this.show(false);\n        buttonCb(true);\n      } catch (err) {\n        buttonCb(false);\n      }\n    }\n  },\n};\n</script>\n\n<template>\n  <app-modal\n    v-if=\"showModal\"\n    custom-class=\"change-password-modal\"\n    data-testid=\"change-password__modal\"\n    name=\"password-modal\"\n    :width=\"500\"\n    :height=\"465\"\n    :trigger-focus-trap=\"true\"\n    @close=\"show(false)\"\n  >\n    <Card\n      class=\"prompt-password\"\n      :show-highlight-border=\"false\"\n    >\n      <template #title>\n        <h4 class=\"text-default-text\">\n          {{ t(\"changePassword.title\") }}\n        </h4>\n      </template>\n\n      <template #body>\n        <form @submit.prevent>\n          <ChangePassword\n            ref=\"changePassword\"\n            @valid=\"valid = $event\"\n          />\n        </form>\n      </template>\n\n      <template #actions>\n        <!-- type reset is required by lastpass -->\n        <button\n          class=\"btn role-secondary\"\n          role=\"button\"\n          :aria-label=\"t('changePassword.cancel')\"\n          type=\"reset\"\n          @click=\"show(false)\"\n        >\n          {{ t(\"changePassword.cancel\") }}\n        </button>\n        <AsyncButton\n          type=\"submit\"\n          mode=\"apply\"\n          class=\"btn bg-error ml-10\"\n          :disabled=\"!valid\"\n          value=\"LOGIN\"\n          @click=\"submit\"\n        />\n      </template>\n    </Card>\n  </app-modal>\n</template>\n\n<style lang=\"scss\" scoped>\n  .prompt-password {\n    :deep() .card-wrap {\n      display: flex;\n      flex-direction: column;\n\n      .card-body {\n        flex: 1;\n        justify-content: start;\n        & > div {\n          flex: 1;\n          display: flex;\n        }\n      }\n\n      .card-actions {\n        display: flex;\n        justify-content: flex-end;\n        width: 100%;\n      }\n    }\n  }\n\n  .prompt-password {\n    flex: 1;\n    display: flex;\n    form {\n      flex: 1;\n    }\n  }\n</style>\n"]}]}