{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Card/Card.vue?vue&type=style&index=0&id=35b78590&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Card/Card.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAuY2FyZC1jb250YWluZXIgewogICYuaGlnaGxpZ2h0LWJvcmRlciB7CiAgICBib3JkZXItbGVmdDogNXB4IHNvbGlkIHZhcigtLXByaW1hcnkpOwogIH0KICBib3JkZXItcmFkaXVzOiB2YXIoLS1ib3JkZXItcmFkaXVzKTsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtYmFzaXM6IDQwJTsKICBtYXJnaW46IDEwcHg7CiAgbWluLWhlaWdodDogMTAwcHg7CiAgcGFkZGluZzogMTBweDsKICBib3gtc2hhZG93OiAwIDAgMjBweCB2YXIoLS1zaGFkb3cpOwogICY6bm90KC50b3ApIHsKICAgIGFsaWduLWl0ZW1zOiB0b3A7CiAgICBmbGV4LWRpcmVjdGlvbjogcm93OwogICAganVzdGlmeS1jb250ZW50OiBzdGFydDsKICB9CiAgLmNhcmQtd3JhcCB7CiAgICB3aWR0aDogMTAwJTsKICB9CiAgICYgLmNhcmQtYm9keSB7CiAgICBjb2xvcjogdmFyKC0taW5wdXQtbGFiZWwpOwogICAgZGlzcGxheTogZmxleDsKICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICAgfQogICAmIC5jYXJkLWFjdGlvbnMgewogICAgIGFsaWduLXNlbGY6IGVuZDsKICAgICBkaXNwbGF5OiBmbGV4OwogICAgIHBhZGRpbmctdG9wOiAyMHB4OwogICB9CiAgICYgLmNhcmQtdGl0bGUgewogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICB3aWR0aDogMTAwJTsKICAgICBoNSB7CiAgICAgICBtYXJnaW46IDA7CiAgICAgfQogICAgLmZsZXgtcmlnaHQgewogICAgICBtYXJnaW4tbGVmdDogYXV0bzsKICAgIH0KICAgfQoKICAvLyBTdGlja3kgbW9kZSB3aWxsIHN0aWNrIGhlYWRlciBhbmQgZm9vdGVyIHRvIHRvcCBhbmQgYm90dG9tIHdpdGggY29udGVudCBpbiB0aGUgbWlkZGxlIHNjcm9sbGluZwogICAmLmNhcmQtc3RpY2t5IHsKICAgICAgLy8gZGlzcGxheTogZmxleDsKICAgICAgLy8gZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsKCiAgICAuY2FyZC13cmFwIHsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKCiAgICAgIC5jYXJkLWJvZHkgewogICAgICAgIGp1c3RpZnktY29udGVudDogZmxleC1zdGFydDsKICAgICAgICBvdmVyZmxvdzogYXV0bzsKICAgICAgfQoKICAgICAgPiAqIHsKICAgICAgICBmbGV4OiAwOwogICAgICB9CgogICAgICAuY2FyZC1ib2R5IHsKICAgICAgICBmbGV4OiAxOwogICAgICB9CiAgICB9CiAgIH0KIH0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Card/Card.vue"], "names": [], "mappings": ";CAuHC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvC;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb;GACC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACxB;GACA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;KACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;GACnB;GACA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KACV,CAAC,EAAE;OACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;KACX;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB;GACD;;EAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GAChG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB;;MAEA,EAAE,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACT;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACT;IACF;GACD;CACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Card/Card.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent, PropType } from 'vue';\nimport { useBasicSetupFocusTrap } from '@shell/composables/focusTrap';\n\nexport default defineComponent({\n\n  name:  'Card',\n  props: {\n    /**\n     * The card's title.\n     */\n    title: {\n      type:    String,\n      default: ''\n    },\n    /**\n     * The text content for the card's body.\n     */\n    content: {\n      type:    String,\n      default: ''\n    },\n    /**\n     * The function to invoke when the default action button is clicked.\n     */\n    buttonAction: {\n      type:    Function as PropType<(event: MouseEvent) => void>,\n      default: (): void => { }\n    },\n    /**\n     * The text for the default action button.\n     */\n    buttonText: {\n      type:    String,\n      default: 'go'\n    },\n    /**\n     * Toggles the card's highlight-border class.\n     */\n    showHighlightBorder: {\n      type:    Boolean,\n      default: true\n    },\n    /**\n     * Toggles the card's Actions section.\n     */\n    showActions: {\n      type:    Boolean,\n      default: true\n    },\n    sticky: {\n      type:    Boolean,\n      default: false,\n    },\n    triggerFocusTrap: {\n      type:    Boolean,\n      default: false,\n    },\n  },\n  setup(props) {\n    if (props.triggerFocusTrap) {\n      useBasicSetupFocusTrap('#focus-trap-card-container-element', {\n        // needs to be false because of import YAML modal from header\n        // where the YAML editor itself is a focus trap\n        // and we can't have it superseed the \"escape key\" to blur that UI element\n        // In this case the focus trap moves the focus out of the modal\n        // correctly once it closes because of the \"onBeforeUnmount\" trigger\n        escapeDeactivates: false,\n        allowOutsideClick: true,\n      });\n    }\n  }\n});\n</script>\n\n<template>\n  <div\n    id=\"focus-trap-card-container-element\"\n    class=\"card-container\"\n    :class=\"{'highlight-border': showHighlightBorder, 'card-sticky': sticky}\"\n    data-testid=\"card\"\n  >\n    <div class=\"card-wrap\">\n      <div\n        class=\"card-title\"\n        data-testid=\"card-title-slot\"\n      >\n        <slot name=\"title\">\n          {{ title }}\n        </slot>\n      </div>\n      <hr>\n      <div\n        class=\"card-body\"\n        data-testid=\"card-body-slot\"\n      >\n        <slot name=\"body\">\n          {{ content }}\n        </slot>\n      </div>\n      <div\n        v-if=\"showActions\"\n        class=\"card-actions\"\n        data-testid=\"card-actions-slot\"\n      >\n        <slot name=\"actions\">\n          <button\n            class=\"btn role-primary\"\n            @click=\"buttonAction\"\n          >\n            {{ buttonText }}\n          </button>\n        </slot>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang='scss'>\n .card-container {\n  &.highlight-border {\n    border-left: 5px solid var(--primary);\n  }\n  border-radius: var(--border-radius);\n  display: flex;\n  flex-basis: 40%;\n  margin: 10px;\n  min-height: 100px;\n  padding: 10px;\n  box-shadow: 0 0 20px var(--shadow);\n  &:not(.top) {\n    align-items: top;\n    flex-direction: row;\n    justify-content: start;\n  }\n  .card-wrap {\n    width: 100%;\n  }\n   & .card-body {\n    color: var(--input-label);\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n   }\n   & .card-actions {\n     align-self: end;\n     display: flex;\n     padding-top: 20px;\n   }\n   & .card-title {\n    align-items: center;\n    display: flex;\n    width: 100%;\n     h5 {\n       margin: 0;\n     }\n    .flex-right {\n      margin-left: auto;\n    }\n   }\n\n  // Sticky mode will stick header and footer to top and bottom with content in the middle scrolling\n   &.card-sticky {\n      // display: flex;\n      // flex-direction: column;\n      overflow: hidden;\n\n    .card-wrap {\n      display: flex;\n      flex-direction: column;\n\n      .card-body {\n        justify-content: flex-start;\n        overflow: auto;\n      }\n\n      > * {\n        flex: 0;\n      }\n\n      .card-body {\n        flex: 1;\n      }\n    }\n   }\n }\n</style>\n"]}]}