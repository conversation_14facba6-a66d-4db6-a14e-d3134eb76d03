{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DetailText.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DetailText.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DetailText.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEhD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;EAE3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChD,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEpD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACrB,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjE;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACxB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACjG;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAExB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE;QACjE,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE9B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;UAExC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACR;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC;;MAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;MAEpD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1D,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzD,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjE,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAC/B,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC;EACH;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DetailText.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapGetters } from 'vuex';\nimport { asciiLike, nlToBr } from '@shell/utils/string';\nimport { HIDE_SENSITIVE } from '@shell/store/prefs';\nimport CopyToClipboard from '@shell/components/CopyToClipboard';\nimport CodeMirror from '@shell/components/CodeMirror';\nimport { binarySize } from '@shell/utils/crypto';\n\nexport default {\n  components: { CopyToClipboard, CodeMirror },\n\n  props: {\n    label: {\n      type:    String,\n      default: null,\n    },\n\n    labelKey: {\n      type:    String,\n      default: null,\n    },\n\n    value: {\n      type:    String,\n      default: null,\n    },\n\n    maxLength: {\n      type:    Number,\n      default: 640, // Ought to be enough for anybody\n    },\n\n    binary: {\n      type:    Boolean,\n      default: null, // Autodetect\n    },\n\n    conceal: {\n      type:    Boolean,\n      default: false\n    },\n\n    monospace: {\n      type:    Boolean,\n      default: true\n    },\n\n    copy: {\n      type:    Boolean,\n      default: true\n    }\n  },\n\n  data() {\n    const expanded = this.value.length <= this.maxLength;\n\n    return { expanded };\n  },\n\n  computed: {\n    isBinary() {\n      if ( this.binary === null ) {\n        return typeof this.value === 'string' && !asciiLike(this.value);\n      }\n\n      return this.binary;\n    },\n\n    size() {\n      return `${ this.value }`.length;\n    },\n\n    isLong() {\n      return this.size > this.maxLength;\n    },\n\n    isEmpty() {\n      return this.size === 0;\n    },\n\n    body() {\n      if (this.isBinary) {\n        return this.t('detailText.binary', { n: this.value.length ? binarySize(this.value) : 0 }, true);\n      }\n\n      if (this.expanded) {\n        return this.value;\n      }\n\n      return this.value.slice(0, this.maxLength);\n    },\n\n    jsonStr() {\n      const value = this.value;\n\n      if ( value && ( value.startsWith('{') || value.startsWith('[') ) ) {\n        try {\n          let parsed = JSON.parse(value);\n\n          parsed = JSON.stringify(parsed, null, 2);\n\n          return parsed;\n        } catch {\n        }\n      }\n\n      return null;\n    },\n\n    bodyHtml() {\n      // Includes escapeHtml()\n      return nlToBr(this.body);\n    },\n\n    plusMore() {\n      if (this.expanded) {\n        return this.t('detailText.collapse');\n      }\n\n      const more = Math.max(this.size - this.maxLength, 0);\n\n      return this.t('detailText.plusMore', { n: more }).trim();\n    },\n\n    hideSensitiveData() {\n      return this.$store.getters['prefs/get'](HIDE_SENSITIVE);\n    },\n\n    concealed() {\n      return this.conceal && this.hideSensitiveData && !this.isBinary;\n    },\n\n    ...mapGetters({ t: 'i18n/t' })\n  },\n  methods: {\n    expand() {\n      this.expanded = !this.expanded;\n    },\n  }\n};\n</script>\n\n<template>\n  <div :class=\"{'force-wrap': true, 'with-copy':copy}\">\n    <h5\n      v-if=\"labelKey\"\n      v-t=\"labelKey\"\n    />\n    <h5 v-else-if=\"label\">\n      {{ label }}\n    </h5>\n\n    <span\n      v-if=\"isEmpty\"\n      v-t=\"'detailText.empty'\"\n      class=\"text-italic\"\n    />\n    <span\n      v-else-if=\"isBinary\"\n      class=\"text-italic\"\n    >{{ body }}</span>\n\n    <CodeMirror\n      v-else-if=\"jsonStr\"\n      :options=\"{mode:{name:'javascript', json:true}, lineNumbers:false, foldGutter:false, readOnly:true}\"\n      :value=\"jsonStr\"\n      :class=\"{'conceal': concealed}\"\n    />\n\n    <span\n      v-else\n      v-clean-html=\"bodyHtml\"\n      data-testid=\"detail-top_html\"\n      :class=\"{'conceal': concealed, 'monospace': monospace && !isBinary}\"\n    />\n\n    <template v-if=\"!isBinary && !jsonStr && isLong && !expanded\">\n      <a\n        href=\"#\"\n        @click.prevent=\"expand\"\n      >{{ plusMore }}</a>\n    </template>\n\n    <CopyToClipboard\n      v-if=\"copy && !isBinary\"\n      :text=\"value\"\n      class=\"role-tertiary\"\n      action-color=\"\"\n    />\n  </div>\n</template>\n\n<style lang='scss' scoped>\n.with-copy {\n  border: solid 1px var(--border);\n  border-radius: var(--border-radius);\n  padding: 10px;\n  position: relative;\n  background-color: var(--input-bg);\n  border-radius: var(--border-radius);\n  border: solid var(--border-width) var(--input-border);\n\n  > button {\n    position: absolute;\n    top: -1px;\n    right: -1px;\n    border-radius: 0 0 0 var(--border-radius);\n  }\n}\n\n.monospace {\n  white-space: pre-wrap;\n  word-wrap: break-all\n}\n</style>\n"]}]}