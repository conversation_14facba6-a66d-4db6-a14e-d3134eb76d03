{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Banner/Banner.vue?vue&type=style&index=0&id=7d024178&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Banner/Banner.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Banner/Banner.vue"], "names": [], "mappings": ";AA0HA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;AAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B;;IAEA,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;IAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B;;IAEA,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACnB;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACtB;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC/B;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,EAAE,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;QAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACrB;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB;EACF;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Banner/Banner.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent } from 'vue';\nimport { nlToBr } from '@shell/utils/string';\nimport { stringify } from '@shell/utils/error';\n\nexport default defineComponent({\n  props: {\n    /**\n     * A color class that represents the color of the banner.\n     * @values primary, secondary, success, warning, error, info\n     */\n    color: {\n      type:    String,\n      default: 'secondary'\n    },\n    /**\n     * The label to display as the banner's default content.\n     */\n    label: {\n      type:    [String, Error, Object],\n      default: null\n    },\n    /**\n     * The i18n key for the label to display as the banner's default content.\n     */\n    labelKey: {\n      type:    String,\n      default: null\n    },\n    /**\n     * Add icon for the banner\n     */\n    icon: {\n      type:    String,\n      default: null\n    },\n    /**\n     * Toggles the banner's close button.\n     */\n    closable: {\n      type:    Boolean,\n      default: false\n    },\n    /**\n     * Toggles the stacked class for the banner.\n     */\n    stacked: {\n      type:    Boolean,\n      default: false\n    }\n  },\n  emits:    ['close'],\n  computed: {\n    /**\n     * Return message text as label.\n     */\n    messageLabel(): string | void {\n      return !(typeof this.label === 'string') ? stringify(this.label) : undefined;\n    }\n  },\n  methods: { nlToBr }\n});\n</script>\n<template>\n  <div\n    class=\"banner\"\n    :class=\"{\n      [color]: true,\n    }\"\n    role=\"banner\"\n  >\n    <div\n      v-if=\"icon\"\n      class=\"banner__icon\"\n      data-testid=\"banner-icon\"\n    >\n      <i\n        class=\"icon icon-2x\"\n        :class=\"icon\"\n      />\n    </div>\n    <div\n      class=\"banner__content\"\n      data-testid=\"banner-content\"\n      :class=\"{\n        closable,\n        stacked,\n        icon\n      }\"\n    >\n      <slot>\n        <t\n          v-if=\"labelKey\"\n          :k=\"labelKey\"\n          :raw=\"true\"\n        />\n        <span v-else-if=\"messageLabel\">{{ messageLabel }}</span>\n        <span\n          v-else\n          v-clean-html=\"nlToBr(label)\"\n        />\n      </slot>\n      <div\n        v-if=\"closable\"\n        class=\"banner__content__closer\"\n        tabindex=\"0\"\n        role=\"button\"\n        :aria-label=\"t('generic.close')\"\n        @click=\"$emit('close')\"\n        @keyup.enter=\"$emit('close')\"\n        @keyup.space=\"$emit('close')\"\n      >\n        <i\n          data-testid=\"banner-close\"\n          class=\"icon icon-close closer-icon\"\n        />\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n$left-border-size: 4px;\n$icon-size: 24px;\n\n.banner {\n  display: flex;\n  margin: 15px 0;\n  position: relative;\n  width: 100%;\n  color: var(--body-text);\n\n  &__icon {\n    width: $icon-size * 2;\n    flex-grow: 1;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    box-sizing: content-box;\n\n    .primary & {\n      background: var(--primary);\n    }\n\n    .secondary & {\n      background: var(--default);\n    }\n\n    .success & {\n      background: var(--success);\n    }\n\n    .info & {\n      background: var(--info);\n    }\n\n    .warning & {\n      background: var(--warning);\n    }\n\n    .error & {\n      background: var(--error);\n      color: var(--primary-text);\n    }\n  }\n\n  &__content {\n    padding: 10px;\n    transition: all 0.2s ease;\n    line-height: 20px;\n    width: 100%;\n    border-left: solid $left-border-size transparent;\n    display: flex;\n    gap: 3px;\n\n    .primary & {\n      background: var(--primary);\n      border-color: var(--primary);\n    }\n\n    .secondary & {\n      background: var(--default-banner-bg);\n      border-color: var(--default);\n    }\n\n    .success & {\n      background: var(--success-banner-bg);\n      border-color: var(--success);\n    }\n\n    .info & {\n      background: var(--info-banner-bg);\n      border-color: var(--info);\n    }\n\n    .warning & {\n      background: var(--warning-banner-bg);\n      border-color: var(--warning);\n    }\n\n    .error & {\n      background: var(--error-banner-bg);\n      border-color: var(--error);\n      color: var(--error);\n    }\n\n    &.stacked {\n      padding: 0 10px;\n      margin: 0;\n      transition: none;\n      &:first-child {\n        padding-top: 10px;\n      }\n      &:last-child {\n        padding-bottom: 10px;\n      }\n    }\n\n    &.closable {\n      padding-right: $icon-size * 2;\n    }\n\n    &__closer {\n      display: flex;\n      align-items: center;\n\n      cursor: pointer;\n      position: absolute;\n      top: 0;\n      right: 0;\n      bottom: 0;\n      width: $icon-size;\n      line-height: $icon-size;\n      text-align: center;\n      outline: none;\n\n      .closer-icon {\n        opacity: 0.7;\n\n        &:hover {\n          opacity: 1;\n          color: var(--link);\n        }\n      }\n\n      &:focus-visible i {\n        @include focus-outline;\n        outline-offset: 2px;\n      }\n    }\n\n    &.icon {\n      border-left: none;\n    }\n  }\n}\n</style>\n"]}]}