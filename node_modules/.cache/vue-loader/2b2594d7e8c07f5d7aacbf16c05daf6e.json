{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Group.vue?vue&type=style&index=0&id=5dc959c0&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Group.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Group.vue"], "names": [], "mappings": ";EA2SE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEb,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB;;IAEA,EAAE,EAAE;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACvB;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd;MACA,EAAE,CAAC,EAAE;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACzB;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrB;MACF;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACtB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEzC,CAAC,EAAE;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C;MACF;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;QAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB;;QAEA,EAAE,CAAC,EAAE;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACzB;;QAEA,EAAE,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACR,CAAC,CAAC,CAAC,EAAE,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnB;MACF;;MAEA,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAChB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAClB,EAAE,CAAC,EAAE;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC;QACA,EAAE,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpB;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,EAAE,CAAC,EAAE;UACH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACxB;;QAEA,EAAE,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACR,CAAC,CAAC,CAAC,EAAE,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1B;MACF;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEV,CAAC,EAAE,EAAE,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC;;IAEA,EAAE;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACrB,EAAE;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,EAAE;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpB;IACF;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Group.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport Type from '@shell/components/nav/Type';\nexport default {\n  name: 'Group',\n\n  components: { Type },\n\n  emits: ['expand', 'close'],\n\n  props: {\n    depth: {\n      type:    Number,\n      default: 0,\n    },\n\n    idPrefix: {\n      type:     String,\n      required: true,\n    },\n\n    group: {\n      type:     Object,\n      required: true,\n    },\n\n    childrenKey: {\n      type:    String,\n      default: 'children',\n    },\n\n    canCollapse: {\n      type:    Boolean,\n      default: true,\n    },\n\n    showHeader: {\n      type:    Boolean,\n      default: true,\n    },\n\n    fixedOpen: {\n      type:    Boolean,\n      default: false,\n    }\n  },\n\n  data() {\n    const id = (this.idPrefix || '') + this.group.name;\n\n    return { id, expanded: false };\n  },\n\n  computed: {\n    isGroupActive() {\n      return this.isOverview || (this.hasActiveRoute() && this.isExpanded && this.showHeader);\n    },\n\n    hasChildren() {\n      return this.group.children?.length > 0;\n    },\n\n    hasOverview() {\n      return this.group.children?.[0]?.overview;\n    },\n\n    onlyHasOverview() {\n      return this.group.children && this.group.children.length === 1 && this.hasOverview;\n    },\n\n    isOverview() {\n      if (this.group.children && this.group.children.length > 0) {\n        const grp = this.group.children[0];\n        const overviewRoute = grp?.route;\n\n        if (overviewRoute && grp.overview) {\n          const route = this.$router.resolve(overviewRoute || {});\n\n          return this.$route.fullPath.split('#')[0] === route?.fullPath;\n        }\n      }\n\n      return false;\n    },\n\n    isExpanded: {\n      get() {\n        return this.fixedOpen || this.group.isRoot || !!this.expanded;\n      },\n      set(v) {\n        this.expanded = v;\n      }\n    }\n  },\n\n  methods: {\n    expandGroup() {\n      this.isExpanded = true;\n      this.$emit('expand', this.group);\n    },\n\n    groupSelected() {\n      // Don't auto-select first group entry if we're already expanded and contain the currently-selected nav item\n      if (this.hasActiveRoute() && this.isExpanded) {\n        return;\n      } else {\n        // Remove all active class if click on group header and not active route\n        const headerEl = document.querySelectorAll('.header');\n\n        headerEl.forEach((el) => {\n          el.classList.remove('active');\n        });\n      }\n      this.expandGroup();\n\n      const items = this.group[this.childrenKey];\n\n      // Navigate to one of the child items (by default the first child)\n      if (items && items.length > 0) {\n        let index = 0;\n\n        // If there is a default type, use it\n        if (this.group.defaultType) {\n          const found = items.findIndex((i) => i.name === this.group.defaultType);\n\n          index = (found === -1) ? 0 : found;\n        }\n\n        const route = items[index].route;\n\n        if (route) {\n          this.$router.replace(route);\n        }\n      }\n    },\n\n    selectType() {\n      this.groupSelected();\n      this.close();\n    },\n\n    close() {\n      this.$emit('close');\n    },\n\n    // User clicked on the expander icon, so toggle the expansion so the user can see inside the group\n    peek($event) {\n      // Add active class to the current header if click on chevron icon\n      $event.target.parentElement.classList.remove('active');\n      if (this.hasActiveRoute() && this.isExpanded) {\n        $event.target.parentElement.classList.add('active');\n      }\n      this.isExpanded = !this.isExpanded;\n      $event.stopPropagation();\n    },\n\n    hasActiveRoute(items) {\n      if (!items) {\n        items = this.group;\n      }\n\n      for (const item of items.children) {\n        if (item.children && this.hasActiveRoute(item)) {\n          return true;\n        } else if (item.route) {\n          const navLevels = ['cluster', 'product', 'resource'];\n          const matchesNavLevel = navLevels.filter((param) => !this.$route.params[param] || this.$route.params[param] !== item.route.params[param]).length === 0;\n          const withoutHash = this.$route.hash ? this.$route.fullPath.slice(0, this.$route.fullPath.indexOf(this.$route.hash)) : this.$route.fullPath;\n          const withoutQuery = withoutHash.split('?')[0];\n\n          if (matchesNavLevel || this.$router.resolve(item.route).fullPath === withoutQuery) {\n            return true;\n          }\n        }\n      }\n\n      return false;\n    },\n\n    syncNav() {\n      const refs = this.$refs.groups;\n\n      if (refs) {\n        // Only expand one group - so after the first has been expanded, no more will\n        let canExpand = true;\n\n        refs.forEach((grp) => {\n          if (!grp.group.isRoot) {\n            if (canExpand) {\n              const isActive = this.hasActiveRoute(grp.group);\n\n              if (isActive) {\n                grp.isExpanded = true;\n                canExpand = false;\n                this.$nextTick(() => grp.syncNav());\n              }\n            }\n          }\n        });\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <div\n    class=\"accordion\"\n    :class=\"{[`depth-${depth}`]: true, 'expanded': isExpanded, 'has-children': hasChildren, 'group-highlight': isGroupActive}\"\n  >\n    <div\n      v-if=\"showHeader\"\n      class=\"header\"\n      :class=\"{'active': isOverview, 'noHover': !canCollapse}\"\n      role=\"button\"\n      tabindex=\"0\"\n      :aria-label=\"group.labelDisplay || group.label || ''\"\n      @click=\"groupSelected()\"\n      @keyup.enter=\"groupSelected()\"\n      @keyup.space=\"groupSelected()\"\n    >\n      <slot name=\"header\">\n        <router-link\n          v-if=\"hasOverview\"\n          :to=\"group.children[0].route\"\n          :exact=\"group.children[0].exact\"\n          :tabindex=\"-1\"\n        >\n          <h6>\n            <span v-clean-html=\"group.labelDisplay || group.label\" />\n          </h6>\n        </router-link>\n        <h6\n          v-else\n        >\n          <span v-clean-html=\"group.labelDisplay || group.label\" />\n        </h6>\n      </slot>\n      <i\n        v-if=\"!onlyHasOverview && canCollapse\"\n        class=\"icon toggle toggle-accordion\"\n        :class=\"{'icon-chevron-right': !isExpanded, 'icon-chevron-down': isExpanded}\"\n        role=\"button\"\n        tabindex=\"0\"\n        :aria-label=\"t('nav.ariaLabel.collapseExpand')\"\n        @click=\"peek($event, true)\"\n        @keyup.enter=\"peek($event, true)\"\n        @keyup.space=\"peek($event, true)\"\n      />\n    </div>\n    <ul\n      v-if=\"isExpanded\"\n      class=\"list-unstyled body\"\n      v-bind=\"$attrs\"\n    >\n      <template\n        v-for=\"(child, idx) in group[childrenKey]\"\n        :key=\"idx\"\n      >\n        <li\n          v-if=\"child.divider\"\n          :key=\"idx\"\n        >\n          <hr>\n        </li>\n        <!-- <div v-else-if=\"child[childrenKey] && hideGroup(child[childrenKey])\" :key=\"child.name\">\n          HIDDEN\n        </div> -->\n        <li\n          v-else-if=\"child[childrenKey]\"\n          :key=\"child.name\"\n        >\n          <Group\n            ref=\"groups\"\n            :key=\"id+'_'+child.name+'_children'\"\n            :id-prefix=\"id+'_'\"\n            :depth=\"depth + 1\"\n            :children-key=\"childrenKey\"\n            :can-collapse=\"canCollapse\"\n            :group=\"child\"\n            :fixed-open=\"fixedOpen\"\n            @selected=\"groupSelected($event)\"\n            @expand=\"expandGroup($event)\"\n            @close=\"close($event)\"\n          />\n        </li>\n        <Type\n          v-else-if=\"!child.overview || group.name === 'starred'\"\n          :key=\"id+'_' + child.name + '_type'\"\n          :is-root=\"depth == 0 && !showHeader\"\n          :type=\"child\"\n          :depth=\"depth\"\n          @selected=\"selectType($event)\"\n        />\n      </template>\n    </ul>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .header {\n    position: relative;\n    cursor: pointer;\n    color: var(--body-text);\n    height: 33px;\n    outline: none;\n\n    H6 {\n      color: var(--body-text);\n      user-select: none;\n      text-transform: none;\n      font-size: 14px;\n    }\n\n    > A {\n      display: block;\n      box-sizing:border-box;\n      height: 100%;\n      &:hover{\n        text-decoration: none;\n      }\n      &:focus{\n        outline:none;\n      }\n      > H6 {\n        text-transform: none;\n        padding: 8px 0 8px 16px;\n      }\n    }\n  }\n\n  .accordion {\n    .header {\n      &:focus-visible {\n        h6 span {\n          @include focus-outline;\n          outline-offset: 2px;\n        }\n      }\n      .toggle-accordion:focus-visible {\n        @include focus-outline;\n        outline-offset: -6px;\n      }\n\n      &.active {\n        color: var(--primary-hover-text);\n        background-color: var(--primary-hover-bg);\n\n        h6 {\n          padding: 8px 0 8px 16px;\n          font-weight: bold;\n          color: var(--primary-hover-text);\n        }\n\n        &:hover {\n          background-color: var(--primary-hover-bg);\n        }\n      }\n      &:hover:not(.active) {\n        background-color: var(--nav-hover);\n      }\n    }\n  }\n\n  .accordion {\n    &.depth-0 {\n      > .header {\n\n        &.noHover {\n          cursor: default;\n        }\n\n        > H6 {\n          text-transform: none;\n          padding: 8px 0 8px 16px;\n        }\n\n        > I {\n          position: absolute;\n          right: 0;\n          top: 0;\n          padding: 10px 10px 9px 7px;\n          user-select: none;\n        }\n      }\n\n      > .body {\n        margin-left: 0;\n      }\n\n      &.group-highlight {\n        background: var(--nav-active);\n      }\n    }\n\n    &.depth-1 {\n      > .header {\n        padding-left: 20px;\n        > H6 {\n          line-height: 18px;\n          padding: 8px 0 7px 5px !important;\n        }\n        > I {\n          padding: 10px 7px 9px 7px !important;\n        }\n      }\n\n      &:deep() .type-link > .label {\n        padding-left: 10px;\n      }\n    }\n\n    &:not(.depth-0) {\n      > .header {\n        > H6 {\n          // Child groups that aren't linked themselves\n          display: inline-block;\n          padding: 5px 0 5px 5px;\n        }\n\n        > I {\n          position: absolute;\n          right: 0;\n          top: 0;\n          padding: 6px 8px 6px 8px;\n        }\n      }\n    }\n  }\n\n  .body :deep() > .child.router-link-active,\n  .header :deep() > .child.router-link-exact-active {\n    padding: 0;\n\n    A, A I {\n      color: var(--primary-hover-text);\n    }\n\n    A {\n      color: var(--primary-hover-text);\n      background-color: var(--primary-hover-bg);\n      font-weight: bold;\n    }\n  }\n\n  .body :deep() > .child {\n    A {\n      border-left: solid 5px transparent;\n      line-height: 16px;\n      font-size: 14px;\n      padding-left: 24px;\n      display: flex;\n      justify-content: space-between;\n    }\n\n    A:focus {\n      outline: none;\n    }\n\n    &.root {\n      background: transparent;\n      A {\n        padding-left: 14px;\n      }\n    }\n  }\n</style>\n"]}]}