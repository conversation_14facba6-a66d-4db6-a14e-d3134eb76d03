{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/TopLevelMenu.vue?vue&type=template&id=4367c1c6&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/TopLevelMenu.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/TopLevelMenu.vue"], "names": [], "mappings": ";EA8cE,CAAC,CAAC,CAAC,CAAC;IACF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACf,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C;QACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB;YACE,CAAC,CAAC,CAAC;cACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;cAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC;cACH,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACnC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1C;gBACE,CAAC,CAAC,CAAC;kBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;kBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC;kBACH,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACpB,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;cACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB;cACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC7B,CAAC;kBACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7B,CAAC;cACH,CAAC,CAAC,CAAC,CAAC,CAAC;;cAEL,CAAC,CAAC,CAAC;gBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf;gBACE,CAAC,CAAC,CAAC,CAAC,CAAC;kBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;kBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC;gBACA,CAAC;kBACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACnC,CAAC;gBACD,CAAC;kBACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1B,CAAC;cACH,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACvB,CAAC,CAAC,CAAC,CAAC;cACF,CAAC;gBACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChC;gBACE,CAAC;kBACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,CAAC;gBACD,CAAC,CAAC,CAAC,CAAC;kBACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAClC,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;cACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB;cACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cACpE;gBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACb,CAAC;gBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC;cACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B;cACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC;gBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB;gBACE,CAAC,CAAC,CAAC;kBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB;kBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;oBAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;oBACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;oBAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChC;oBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9B,CAAC;oBACD,CAAC,CAAC,CAAC;sBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrB;sBACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;sBACnB,CAAC;wBACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACpB;wBACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBACpB,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;oBAC5B,CAAC;kBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACR,CAAC,CAAC,CAAC,CAAC;oBACF,CAAC,CAAC,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;kBACxD;oBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9B,CAAC;oBACD,CAAC,CAAC,CAAC;sBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrB;sBACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;sBACnB,CAAC;wBACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACpB;wBACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBACpB,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;oBAC5B,CAAC;kBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACR,CAAC,CAAC,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;kBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;kBACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvB;kBACE,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;;cAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cAC9B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvB,CAAC,CAAC,CAAC;kBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB;kBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;oBACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;oBACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;oBACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChC;oBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9B,CAAC;oBACD,CAAC,CAAC,CAAC;sBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrB;sBACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;sBACnB,CAAC;wBACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACpB;wBACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBACpB,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;sBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC;kBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACR,CAAC,CAAC,CAAC,CAAC;oBACF,CAAC,CAAC,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;kBACjD;oBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9B,CAAC;oBACD,CAAC,CAAC,CAAC;sBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrB;sBACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;sBACnB,CAAC;wBACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACpB;wBACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBACpB,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;sBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC;kBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACR,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;;cAEL,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cAC3B,CAAC,CAAC,CAAC;gBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtB;gBACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAChC,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3C,EAAE,CAAC;cACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxC;cACE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACvE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtC,CAAC,CAAC,CAAC;gBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB;gBACE,CAAC,CAAC,CAAC;gBACH,CAAC,CAAC,CAAC,CAAC,CAAC;kBACH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACR,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC;gBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChB;gBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACpE;kBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACb,CAAC;kBACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvC,CAAC,CAAC,CAAC;gBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB;gBACE,CAAC,CAAC,CAAC;gBACH,CAAC,CAAC,CAAC,CAAC,CAAC;kBACH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACR,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC;gBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChB;gBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACrE;kBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACb,CAAC;kBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACd,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf;UACE,CAAC,CAAC,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC;cACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvC;cACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/TopLevelMenu.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport BrandImage from '@shell/components/BrandImage';\nimport ClusterIconMenu from '@shell/components/ClusterIconMenu';\nimport IconOrSvg from '../IconOrSvg';\nimport { BLANK_CLUSTER } from '@shell/store/store-types.js';\nimport { mapGetters } from 'vuex';\nimport { CAPI, COUNT, MANAGEMENT } from '@shell/config/types';\nimport { MENU_MAX_CLUSTERS, PINNED_CLUSTERS } from '@shell/store/prefs';\nimport { sortBy } from '@shell/utils/sort';\nimport { ucFirst } from '@shell/utils/string';\nimport { KEY } from '@shell/utils/platform';\nimport { getVersionInfo } from '@shell/utils/version';\nimport { SETTING } from '@shell/config/settings';\nimport { getProductFromRoute } from '@shell/utils/router';\nimport { isRancherPrime } from '@shell/config/version';\nimport Pinned from '@shell/components/nav/Pinned';\nimport { TopLevelMenuHelperPagination, TopLevelMenuHelperLegacy } from '@shell/components/nav/TopLevelMenu.helper';\nimport { debounce } from 'lodash';\nimport { sameContents } from '@shell/utils/array';\n\nexport default {\n  components: {\n    BrandImage,\n    ClusterIconMenu,\n    IconOrSvg,\n    Pinned\n  },\n\n  data() {\n    const { displayVersion, fullVersion } = getVersionInfo(this.$store);\n    const hasProvCluster = this.$store.getters[`management/schemaFor`](CAPI.RANCHER_CLUSTER);\n\n    const canPagination = this.$store.getters[`management/paginationEnabled`]({\n      id:      MANAGEMENT.CLUSTER,\n      context: 'side-bar',\n    }) && this.$store.getters[`management/paginationEnabled`]({\n      id:      CAPI.RANCHER_CLUSTER,\n      context: 'side-bar',\n    });\n    const helper = canPagination ? new TopLevelMenuHelperPagination({ $store: this.$store }) : new TopLevelMenuHelperLegacy({ $store: this.$store });\n    const provClusters = !canPagination && hasProvCluster ? this.$store.getters[`management/all`](CAPI.RANCHER_CLUSTER) : [];\n    const mgmtClusters = !canPagination ? this.$store.getters[`management/all`](MANAGEMENT.CLUSTER) : [];\n\n    if (!canPagination) {\n      // Reduce the impact of the initial load, but only if we're not making a request\n      const args = {\n        pinnedIds:   this.pinnedIds,\n        searchTerm:  this.search,\n        unPinnedMax: this.maxClustersToShow\n      };\n\n      helper.update(args);\n    }\n\n    return {\n      shown:             false,\n      displayVersion,\n      fullVersion,\n      clusterFilter:     '',\n      hasProvCluster,\n      maxClustersToShow: MENU_MAX_CLUSTERS,\n      emptyCluster:      BLANK_CLUSTER,\n      routeCombo:        false,\n\n      canPagination,\n      helper,\n      debouncedHelperUpdateSlow:   debounce((...args) => this.helper.update(...args), 1000),\n      debouncedHelperUpdateMedium: debounce((...args) => this.helper.update(...args), 750),\n      debouncedHelperUpdateQuick:  debounce((...args) => this.helper.update(...args), 200),\n      provClusters,\n      mgmtClusters,\n    };\n  },\n\n  computed: {\n    ...mapGetters(['clusterId']),\n    ...mapGetters(['clusterReady', 'isRancher', 'currentCluster', 'currentProduct', 'isRancherInHarvester']),\n    ...mapGetters({ features: 'features/get' }),\n\n    pinnedIds() {\n      return this.$store.getters['prefs/get'](PINNED_CLUSTERS);\n    },\n\n    showClusterSearch() {\n      return this.allClustersCount > this.maxClustersToShow;\n    },\n\n    allClustersCount() {\n      const counts = this.$store.getters[`management/all`](COUNT)?.[0]?.counts || {};\n      const count = counts[MANAGEMENT.CLUSTER] || {};\n\n      return count?.summary.count;\n    },\n\n    // New\n    search() {\n      return (this.clusterFilter || '').toLowerCase();\n    },\n\n    // New\n    showPinClusters() {\n      return !this.clusterFilter;\n    },\n\n    // New\n    searchActive() {\n      return !!this.search;\n    },\n\n    /**\n     * Only Clusters that are pinned\n     *\n     * (see description of helper.clustersPinned for more details)\n     */\n    pinFiltered() {\n      return this.hasProvCluster ? this.helper.clustersPinned : [];\n    },\n\n    /**\n     * Used to shown unpinned clusters OR results of text search\n     *\n     * (see description of helper.clustersOthers for more details)\n     */\n    clustersFiltered() {\n      return this.hasProvCluster ? this.helper.clustersOthers : [];\n    },\n\n    pinnedClustersHeight() {\n      const pinCount = this.pinFiltered.length;\n      const height = pinCount > 2 ? (pinCount * 43) : 90;\n\n      return `min-height: ${ height }px`;\n    },\n    clusterFilterCount() {\n      return this.clusterFilter ? this.clustersFiltered.length : this.allClustersCount;\n    },\n\n    multiClusterApps() {\n      const options = this.options;\n\n      return options.filter((opt) => {\n        const filterApps = (opt.inStore === 'management' || opt.isMultiClusterApp) && opt.category !== 'configuration' && opt.category !== 'legacy';\n\n        if (this.isRancherInHarvester) {\n          return filterApps && opt.category !== 'hci';\n        } else {\n          // We expect the location of Virtualization Management to remain the same when rancher-manage-support is not enabled\n          return filterApps;\n        }\n      });\n    },\n\n    configurationApps() {\n      const options = this.options;\n\n      return options.filter((opt) => opt.category === 'configuration');\n    },\n\n    hciApps() {\n      const options = this.options;\n\n      return options.filter((opt) => this.isRancherInHarvester && opt.category === 'hci');\n    },\n\n    options() {\n      const cluster = this.clusterId || this.$store.getters['defaultClusterId'];\n\n      // TODO plugin routes\n      const entries = this.$store.getters['type-map/activeProducts']?.map((p) => {\n        // Try product-specific index first\n        const to = p.to || {\n          name:   `c-cluster-${ p.name }`,\n          params: { cluster }\n        };\n\n        const matched = this.$router.getRoutes().filter((route) => route.name === to.name);\n\n        if ( !matched.length ) {\n          to.name = 'c-cluster-product';\n          to.params.product = p.name;\n        }\n\n        return {\n          label:             this.$store.getters['i18n/withFallback'](`product.\"${ p.name }\"`, null, ucFirst(p.name)),\n          icon:              `icon-${ p.icon || 'copy' }`,\n          svg:               p.svg,\n          value:             p.name,\n          removable:         p.removable !== false,\n          inStore:           p.inStore || 'cluster',\n          weight:            p.weight || 1,\n          category:          p.category || 'none',\n          to,\n          isMultiClusterApp: p.isMultiClusterApp,\n        };\n      });\n\n      return sortBy(entries, ['weight']);\n    },\n\n    canEditSettings() {\n      return (this.$store.getters['management/schemaFor'](MANAGEMENT.SETTING)?.resourceMethods || []).includes('PUT');\n    },\n\n    hasSupport() {\n      return isRancherPrime() || this.$store.getters['management/byId'](MANAGEMENT.SETTING, SETTING.SUPPORTED )?.value === 'true';\n    },\n\n    isCurrRouteClusterExplorer() {\n      return this.$route?.name?.startsWith('c-cluster');\n    },\n\n    productFromRoute() {\n      return getProductFromRoute(this.$route);\n    },\n\n    aboutText() {\n      // If a version number (starts with 'v') then use that\n      if (this.displayVersion.startsWith('v')) {\n        // Don't show the '.0' for a minor release (e.g. 2.8.0, 2.9.0 etc)\n        return !this.displayVersion.endsWith('.0') ? this.displayVersion : this.displayVersion.substr(0, this.displayVersion.length - 2);\n      }\n\n      // Default fallback to 'About'\n      return this.t('about.title');\n    },\n\n    largeAboutText() {\n      return this.aboutText.length > 6;\n    },\n\n    appBar() {\n      let activeFound = false;\n\n      // order is important for the object keys here\n      // since we want to check last pinFiltered and clustersFiltered\n      const appBar = {\n        hciApps:           this.hciApps,\n        multiClusterApps:  this.multiClusterApps,\n        configurationApps: this.configurationApps,\n        pinFiltered:       this.pinFiltered,\n        clustersFiltered:  this.clustersFiltered,\n      };\n\n      Object.keys(appBar).forEach((menuSection) => {\n        const menuSectionItems = appBar[menuSection];\n        const isClusterCheck = menuSection === 'pinFiltered' || menuSection === 'clustersFiltered';\n\n        // need to reset active state on other menu items\n        menuSectionItems.forEach((item) => {\n          item.isMenuActive = false;\n\n          if (!activeFound && this.checkActiveRoute(item, isClusterCheck)) {\n            activeFound = true;\n            item.isMenuActive = true;\n          }\n        });\n      });\n\n      return appBar;\n    }\n  },\n\n  // See https://github.com/rancher/dashboard/issues/12831 for outstanding performance related work\n  watch: {\n    $route() {\n      this.shown = false;\n    },\n\n    // Before SSP world all of these changes were kicked off given Vue change detection to properties in a computed method.\n    // Changes could come from two scenarios\n    // 1. Changes made by the user (pin / search). Could be tens per second\n    // 2. Changes made by rancher to clusters (state, label, etc change). Could be hundreds a second\n    // They can be restricted to help the churn caused from above\n    // 1. When SSP enabled reduce http spam\n    // 2. When SSP is disabled (legacy) reduce fn churn (this was a known performance customer issue)\n\n    pinnedIds: {\n      immediate: true,\n      handler(neu, old) {\n        if (sameContents(neu, old)) {\n          return;\n        }\n\n        // Low throughput (user click). Changes should be shown quickly\n        this.updateClusters(neu, 'quick');\n      }\n    },\n\n    search() {\n      // Medium throughput. Changes should be shown quickly, unless we want to reduce http spam in SSP world\n      this.updateClusters(this.pinnedIds, this.canPagination ? 'medium' : 'quick');\n    },\n\n    provClusters: {\n      handler(neu, old) {\n        // Potentially incredibly high throughput. Changes should be at least limited (slow if state change, quick if added/removed). Shouldn't get here if SSP\n        this.updateClusters(this.pinnedIds, neu?.length === old?.length ? 'slow' : 'quick');\n      },\n      deep:      true,\n      immediate: true,\n    },\n\n    mgmtClusters: {\n      handler(neu, old) {\n        // Potentially incredibly high throughput. Changes should be at least limited (slow if state change, quick if added/removed). Shouldn't get here if SSP\n        this.updateClusters(this.pinnedIds, neu?.length === old?.length ? 'slow' : 'quick');\n      },\n      deep:      true,\n      immediate: true,\n    },\n\n  },\n\n  mounted() {\n    document.addEventListener('keyup', this.handler);\n  },\n\n  beforeUnmount() {\n    document.removeEventListener('keyup', this.handler);\n  },\n\n  methods: {\n    checkActiveRoute(obj, isClusterRoute) {\n      // for Cluster links in main nav: check if route is a cluster explorer one + check if route cluster matches cluster obj id + check if curr product matches route product\n      if (isClusterRoute) {\n        return this.isCurrRouteClusterExplorer && this.$route?.params?.cluster === obj?.id && this.productFromRoute === this.currentProduct?.name;\n      }\n\n      // for remaining main nav items, check if curr product matches route product is enough\n      return this.productFromRoute === obj?.value;\n    },\n\n    handleKeyComboClick() {\n      this.routeCombo = !this.routeCombo;\n    },\n\n    clusterMenuClick(ev, cluster) {\n      if (this.routeCombo) {\n        ev.preventDefault();\n\n        if (this.isCurrRouteClusterExplorer && this.productFromRoute === this.currentProduct?.name) {\n          const clusterRoute = {\n            name:   this.$route.name,\n            params: { ...this.$route.params },\n            query:  { ...this.$route.query }\n          };\n\n          clusterRoute.params.cluster = cluster.id;\n\n          return this.$router.push(clusterRoute);\n        }\n      }\n\n      return this.$router.push(cluster.clusterRoute);\n    },\n\n    handler(e) {\n      if (e.keyCode === KEY.ESCAPE ) {\n        this.hide();\n      }\n    },\n\n    hide() {\n      this.shown = false;\n      if (this.clustersFiltered === 0) {\n        this.clusterFilter = '';\n      }\n    },\n\n    toggle() {\n      this.shown = !this.shown;\n    },\n\n    async goToHarvesterCluster() {\n      const localCluster = this.$store.getters['management/all'](CAPI.RANCHER_CLUSTER).find((C) => C.id === 'fleet-local/local');\n\n      try {\n        await localCluster.goToHarvesterCluster();\n      } catch {\n      }\n    },\n\n    getTooltipConfig(item, showWhenClosed = false) {\n      if (!item) {\n        return;\n      }\n\n      let contentText = '';\n      let content;\n      let popperClass = '';\n\n      // this is the normal tooltip scenario where we are just passing a string\n      if (typeof item === 'string') {\n        contentText = item;\n        content = this.shown ? null : contentText;\n\n      // if key combo is pressed, then we update the tooltip as well\n      } else if (this.routeCombo &&\n        typeof item === 'object' &&\n        !Array.isArray(item) &&\n        item !== null &&\n        item.ready) {\n        contentText = this.t('nav.keyComboTooltip');\n\n        if (showWhenClosed) {\n          content = !this.shown ? contentText : null;\n        } else {\n          content = this.shown ? contentText : null;\n        }\n\n      // this is scenario where we show a tooltip when we are on the expanded menu to show full description\n      } else {\n        contentText = item.label;\n        // this adds a class to the tooltip container so that we can control the max width\n        popperClass = 'menu-description-tooltip';\n\n        if (item.description) {\n          contentText += `<br><br>${ item.description }`;\n        }\n\n        if (showWhenClosed) {\n          content = !this.shown ? contentText : null;\n        } else {\n          content = this.shown ? contentText : null;\n\n          // this adds a class to adjust tooltip position so it doesn't overlap the cluster pinning action\n          popperClass += ' description-tooltip-pos-adjustment';\n        }\n      }\n\n      return {\n        content,\n        placement:     'right',\n        popperOptions: { modifiers: { preventOverflow: { enabled: false }, hide: { enabled: false } } },\n        popperClass\n      };\n    },\n\n    updateClusters(pinnedIds, speed = 'slow' | 'medium' | 'quick') {\n      const args = {\n        pinnedIds,\n        searchTerm:  this.search,\n        unPinnedMax: this.maxClustersToShow\n      };\n\n      switch (speed) {\n      case 'slow':\n        this.debouncedHelperUpdateSlow(args);\n        break;\n      case 'medium':\n        this.debouncedHelperUpdateMedium(args);\n        break;\n      case 'quick':\n        this.debouncedHelperUpdateQuick(args);\n        break;\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <div>\n    <!-- Overlay -->\n    <div\n      v-if=\"shown\"\n      class=\"side-menu-glass\"\n      @click=\"hide()\"\n    />\n    <transition name=\"fade\">\n      <!-- Side menu -->\n      <div\n        data-testid=\"side-menu\"\n        class=\"side-menu\"\n        :class=\"{'menu-open': shown, 'menu-close':!shown}\"\n        tabindex=\"-1\"\n        role=\"navigation\"\n        :aria-label=\"t('nav.ariaLabel.topLevelMenu')\"\n      >\n        <!-- Logo and name -->\n        <div class=\"title\">\n          <div\n            data-testid=\"top-level-menu\"\n            :aria-label=\"t('nav.expandCollapseAppBar')\"\n            role=\"button\"\n            tabindex=\"0\"\n            class=\"menu\"\n            @keyup.enter=\"toggle()\"\n            @keyup.space=\"toggle()\"\n            @click=\"toggle()\"\n          >\n            <svg\n              class=\"menu-icon\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n              height=\"24\"\n              viewBox=\"0 0 24 24\"\n              width=\"24\"\n              :alt=\"t('nav.alt.mainMenuIcon')\"\n            ><path\n              d=\"M0 0h24v24H0z\"\n              fill=\"none\"\n            /><path d=\"M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z\" /></svg>\n          </div>\n          <div class=\"side-menu-logo\">\n            <BrandImage\n              data-testid=\"side-menu__brand-img\"\n              :alt=\"t('nav.alt.mainMenuRancherLogo')\"\n              file-name=\"rancher-logo.svg\"\n            />\n          </div>\n        </div>\n\n        <!-- Menu body -->\n        <div class=\"body\">\n          <div>\n            <!-- Home button -->\n            <div @click=\"hide()\">\n              <router-link\n                class=\"option cluster selector home\"\n                :to=\"{ name: 'home' }\"\n                role=\"link\"\n                :aria-label=\"t('nav.ariaLabel.homePage')\"\n              >\n                <svg\n                  v-clean-tooltip=\"getTooltipConfig(t('nav.home'))\"\n                  class=\"top-menu-icon\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  width=\"24\"\n                ><path\n                  d=\"M0 0h24v24H0z\"\n                  fill=\"none\"\n                /><path d=\"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z\" /></svg>\n                <div class=\"home-text\">\n                  {{ t('nav.home') }}\n                </div>\n              </router-link>\n            </div>\n            <!-- Search bar -->\n            <div\n              v-if=\"showClusterSearch\"\n              class=\"clusters-search\"\n            >\n              <div class=\"clusters-search-count\">\n                <span>{{ clusterFilterCount }}</span>\n                {{ t('nav.search.clusters') }}\n                <i\n                  v-if=\"clusterFilter\"\n                  class=\"icon icon-filter_alt\"\n                />\n              </div>\n\n              <div\n                class=\"search\"\n              >\n                <input\n                  ref=\"clusterFilter\"\n                  v-model=\"clusterFilter\"\n                  :placeholder=\"t('nav.search.placeholder')\"\n                  :tabindex=\"!shown ? -1 : 0\"\n                  :aria-label=\"t('nav.search.ariaLabel')\"\n                >\n                <i\n                  class=\"magnifier icon icon-search\"\n                  :class=\"{ active: clusterFilter }\"\n                />\n                <i\n                  v-if=\"clusterFilter\"\n                  class=\"icon icon-close\"\n                  @click=\"clusterFilter=''\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <!-- Harvester extras -->\n          <template v-if=\"hciApps.length\">\n            <div class=\"category\" />\n            <div>\n              <a\n                v-if=\"isRancherInHarvester\"\n                class=\"option\"\n                tabindex=\"0\"\n                @click=\"goToHarvesterCluster()\"\n              >\n                <i\n                  class=\"icon icon-dashboard app-icon\"\n                />\n                <div>\n                  {{ t('nav.harvesterDashboard') }}\n                </div>\n              </a>\n            </div>\n            <div\n              v-for=\"(a, i) in appBar.hciApps\"\n              :key=\"i\"\n              @click=\"hide()\"\n            >\n              <router-link\n                class=\"option\"\n                :to=\"a.to\"\n                :class=\"{'active-menu-link': a.isMenuActive }\"\n                role=\"link\"\n                :aria-label=\"`${t('nav.ariaLabel.harvesterCluster')} ${ a.label }`\"\n              >\n                <IconOrSvg\n                  class=\"app-icon\"\n                  :icon=\"a.icon\"\n                  :src=\"a.svg\"\n                />\n                <div>{{ a.label }}</div>\n              </router-link>\n            </div>\n          </template>\n\n          <!-- Cluster menu -->\n          <template v-if=\"!!allClustersCount\">\n            <div\n              ref=\"clusterList\"\n              class=\"clusters\"\n              :style=\"pinnedClustersHeight\"\n            >\n              <!-- Pinned Clusters -->\n              <div\n                v-if=\"showPinClusters && pinFiltered.length\"\n                class=\"clustersPinned\"\n              >\n                <div\n                  v-for=\"(c, index) in appBar.pinFiltered\"\n                  :key=\"index\"\n                  :data-testid=\"`pinned-ready-cluster-${index}`\"\n                  @click=\"hide()\"\n                >\n                  <button\n                    v-if=\"c.ready\"\n                    v-shortkey.push=\"{windows: ['alt'], mac: ['option']}\"\n                    :data-testid=\"`pinned-menu-cluster-${ c.id }`\"\n                    class=\"cluster selector option\"\n                    :class=\"{'active-menu-link': c.isMenuActive }\"\n                    :to=\"c.clusterRoute\"\n                    role=\"button\"\n                    :aria-label=\"`${t('nav.ariaLabel.cluster')} ${ c.label }`\"\n                    @click.prevent=\"clusterMenuClick($event, c)\"\n                    @shortkey=\"handleKeyComboClick\"\n                  >\n                    <ClusterIconMenu\n                      v-clean-tooltip=\"getTooltipConfig(c, true)\"\n                      :cluster=\"c\"\n                      :route-combo=\"routeCombo\"\n                      class=\"rancher-provider-icon\"\n                    />\n                    <div\n                      v-clean-tooltip=\"getTooltipConfig(c)\"\n                      class=\"cluster-name\"\n                    >\n                      <p>{{ c.label }}</p>\n                      <p\n                        v-if=\"c.description\"\n                        class=\"description\"\n                      >\n                        {{ c.description }}\n                      </p>\n                    </div>\n                    <Pinned\n                      :cluster=\"c\"\n                      :tab-order=\"shown ? 0 : -1\"\n                    />\n                  </button>\n                  <span\n                    v-else\n                    class=\"option cluster selector disabled\"\n                    :data-testid=\"`pinned-menu-cluster-disabled-${ c.id }`\"\n                  >\n                    <ClusterIconMenu\n                      v-clean-tooltip=\"getTooltipConfig(c, true)\"\n                      :cluster=\"c\"\n                      class=\"rancher-provider-icon\"\n                    />\n                    <div\n                      v-clean-tooltip=\"getTooltipConfig(c)\"\n                      class=\"cluster-name\"\n                    >\n                      <p>{{ c.label }}</p>\n                      <p\n                        v-if=\"c.description\"\n                        class=\"description\"\n                      >\n                        {{ c.description }}\n                      </p>\n                    </div>\n                    <Pinned\n                      :cluster=\"c\"\n                      :tab-order=\"shown ? 0 : -1\"\n                    />\n                  </span>\n                </div>\n                <div\n                  v-if=\"clustersFiltered.length > 0\"\n                  class=\"category-title\"\n                >\n                  <hr>\n                </div>\n              </div>\n\n              <!-- Clusters Search result -->\n              <div class=\"clustersList\">\n                <div\n                  v-for=\"(c, index) in appBar.clustersFiltered\"\n                  :key=\"index\"\n                  :data-testid=\"`top-level-menu-cluster-${index}`\"\n                  @click=\"hide()\"\n                >\n                  <button\n                    v-if=\"c.ready\"\n                    v-shortkey.push=\"{windows: ['alt'], mac: ['option']}\"\n                    :data-testid=\"`menu-cluster-${ c.id }`\"\n                    class=\"cluster selector option\"\n                    :class=\"{'active-menu-link': c.isMenuActive }\"\n                    :to=\"c.clusterRoute\"\n                    role=\"button\"\n                    :aria-label=\"`${t('nav.ariaLabel.cluster')} ${ c.label }`\"\n                    @click=\"clusterMenuClick($event, c)\"\n                    @shortkey=\"handleKeyComboClick\"\n                  >\n                    <ClusterIconMenu\n                      v-clean-tooltip=\"getTooltipConfig(c, true)\"\n                      :cluster=\"c\"\n                      :route-combo=\"routeCombo\"\n                      class=\"rancher-provider-icon\"\n                    />\n                    <div\n                      v-clean-tooltip=\"getTooltipConfig(c)\"\n                      class=\"cluster-name\"\n                    >\n                      <p>{{ c.label }}</p>\n                      <p\n                        v-if=\"c.description\"\n                        class=\"description\"\n                      >\n                        {{ c.description }}\n                      </p>\n                    </div>\n                    <Pinned\n                      :class=\"{'showPin': c.pinned}\"\n                      :tab-order=\"shown ? 0 : -1\"\n                      :cluster=\"c\"\n                    />\n                  </button>\n                  <span\n                    v-else\n                    class=\"option cluster selector disabled\"\n                    :data-testid=\"`menu-cluster-disabled-${ c.id }`\"\n                  >\n                    <ClusterIconMenu\n                      v-clean-tooltip=\"getTooltipConfig(c, true)\"\n                      :cluster=\"c\"\n                      class=\"rancher-provider-icon\"\n                    />\n                    <div\n                      v-clean-tooltip=\"getTooltipConfig(c)\"\n                      class=\"cluster-name\"\n                    >\n                      <p>{{ c.label }}</p>\n                      <p\n                        v-if=\"c.description\"\n                        class=\"description\"\n                      >\n                        {{ c.description }}\n                      </p>\n                    </div>\n                    <Pinned\n                      :class=\"{'showPin': c.pinned}\"\n                      :tab-order=\"shown ? 0 : -1\"\n                      :cluster=\"c\"\n                    />\n                  </span>\n                </div>\n              </div>\n\n              <!-- No clusters message -->\n              <div\n                v-if=\"clustersFiltered.length === 0 && searchActive\"\n                data-testid=\"top-level-menu-no-results\"\n                class=\"none-matching\"\n              >\n                {{ t('nav.search.noResults') }}\n              </div>\n            </div>\n\n            <!-- See all clusters -->\n            <router-link\n              v-if=\"allClustersCount > maxClustersToShow\"\n              class=\"clusters-all\"\n              :to=\"{name: 'c-cluster-product-resource', params: {\n                cluster: emptyCluster,\n                product: 'manager',\n                resource: 'provisioning.cattle.io.cluster'\n              } }\"\n              role=\"link\"\n              :aria-label=\"t('nav.ariaLabel.seeAll')\"\n            >\n              <span>\n                {{ shown ? t('nav.seeAllClusters') : t('nav.seeAllClustersCollapsed') }}\n                <i class=\"icon icon-chevron-right\" />\n              </span>\n            </router-link>\n          </template>\n\n          <!-- MULTI CLUSTER APPS -->\n          <div class=\"category\">\n            <template v-if=\"multiClusterApps.length\">\n              <div\n                class=\"category-title\"\n              >\n                <hr>\n                <span>\n                  {{ t('nav.categories.multiCluster') }}\n                </span>\n              </div>\n              <div\n                v-for=\"(a, i) in appBar.multiClusterApps\"\n                :key=\"i\"\n                @click=\"hide()\"\n              >\n                <router-link\n                  class=\"option\"\n                  :class=\"{'active-menu-link': a.isMenuActive }\"\n                  :to=\"a.to\"\n                  role=\"link\"\n                  :aria-label=\"`${t('nav.ariaLabel.multiClusterApps')} ${ a.label }`\"\n                >\n                  <IconOrSvg\n                    v-clean-tooltip=\"getTooltipConfig(a.label)\"\n                    class=\"app-icon\"\n                    :icon=\"a.icon\"\n                    :src=\"a.svg\"\n                  />\n                  <span class=\"option-link\">{{ a.label }}</span>\n                </router-link>\n              </div>\n            </template>\n\n            <!-- Configuration apps menu -->\n            <template v-if=\"configurationApps.length\">\n              <div\n                class=\"category-title\"\n              >\n                <hr>\n                <span>\n                  {{ t('nav.categories.configuration') }}\n                </span>\n              </div>\n              <div\n                v-for=\"(a, i) in appBar.configurationApps\"\n                :key=\"i\"\n                @click=\"hide()\"\n              >\n                <router-link\n                  class=\"option\"\n                  :class=\"{'active-menu-link': a.isMenuActive }\"\n                  :to=\"a.to\"\n                  role=\"link\"\n                  :aria-label=\"`${t('nav.ariaLabel.configurationApps')} ${ a.label }`\"\n                >\n                  <IconOrSvg\n                    v-clean-tooltip=\"getTooltipConfig(a.label)\"\n                    class=\"app-icon\"\n                    :icon=\"a.icon\"\n                    :src=\"a.svg\"\n                  />\n                  <div>{{ a.label }}</div>\n                </router-link>\n              </div>\n            </template>\n          </div>\n        </div>\n\n        <!-- Footer -->\n        <div\n          class=\"footer\"\n        >\n          <div\n            v-if=\"canEditSettings\"\n            class=\"support\"\n            @click=\"hide()\"\n          >\n            <router-link\n              :to=\"{name: 'support'}\"\n              role=\"link\"\n              :aria-label=\"t('nav.ariaLabel.support')\"\n            >\n              {{ t('nav.support', {hasSupport}) }}\n            </router-link>\n          </div>\n          <div\n            class=\"version\"\n            :class=\"{'version-small': largeAboutText}\"\n            @click=\"hide()\"\n          >\n            <router-link\n              :to=\"{ name: 'about' }\"\n              role=\"link\"\n              :aria-label=\"t('nav.ariaLabel.about')\"\n            >\n              {{ aboutText }}\n            </router-link>\n          </div>\n        </div>\n      </div>\n    </transition>\n  </div>\n</template>\n\n<style lang=\"scss\">\n  .menu-description-tooltip {\n    max-width: 200px;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n  }\n\n  .description-tooltip-pos-adjustment {\n    // needs !important so that we can\n    // offset the tooltip a bit so it doesn't\n    // overlap the pin icon and cause bad UX\n    left: 48px !important;\n  }\n\n  .localeSelector, .footer-tooltip {\n    z-index: 1000;\n  }\n\n  .localeSelector {\n    .v-popper__inner {\n      padding: 10px 0;\n    }\n\n    .v-popper__arrow-container {\n      display: none;\n    }\n\n    .v-popper:focus {\n      outline: 0;\n    }\n  }\n\n  .theme-dark .cluster-name .description {\n    color: var(--input-label) !important;\n  }\n  .theme-dark .body .option  {\n    &:hover .cluster-name .description,\n    &.router-link-active .cluster-name .description,\n    &.active-menu-link .cluster-name .description {\n      color: var(--side-menu-desc) !important;\n  }\n  }\n</style>\n\n<style lang=\"scss\" scoped>\n  $clear-search-size: 20px;\n  $icon-size: 25px;\n  $option-padding: 9px;\n  $option-padding-left: 14px;\n  $option-height: $icon-size + $option-padding + $option-padding;\n\n  .side-menu {\n    .menu {\n      position: absolute;\n      width: $app-bar-collapsed-width;\n      height: 54px;\n      top: 0;\n      grid-area: menu;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n\n      &:focus-visible {\n        outline: none;\n\n        .menu-icon {\n          @include focus-outline;\n          outline-offset: 4px;  // Ensure there is space around the menu icon for the focus indication\n        }\n      }\n\n      .menu-icon {\n        width: 25px;\n        height: 25px;\n        fill: var(--header-btn-text);\n      }\n    }\n\n    position: absolute;\n    top: 0;\n    left: 0px;\n    bottom: 0;\n    width: $app-bar-collapsed-width;\n    background-color: var(--topmenu-bg);\n    z-index: 100;\n    border-right: 1px solid var(--topmost-border);\n    display: flex;\n    flex-direction: column;\n    padding: 0;\n    overflow: hidden;\n    transition: width 250ms;\n\n    &:focus, &:focus-visible {\n      outline: 0;\n    }\n\n    .option:focus-visible {\n      outline: 0;\n    }\n\n    &.menu-open {\n      width: 300px;\n      box-shadow: 3px 1px 3px var(--shadow);\n\n      // because of accessibility, we force pin action to be visible on menu open\n      .pin {\n        display: block !important;\n\n        &:focus-visible {\n          @include focus-outline;\n          outline-offset: 4px;\n        }\n      }\n    }\n\n    .title {\n      display: flex;\n      height: 55px;\n      flex: 0 0 55px;\n      width: 100%;\n      justify-content: flex-start;\n      align-items: center;\n\n      .menu {\n        display: flex;\n        justify-content: center;\n      }\n      .menu-icon {\n        width: 25px;\n        height: 25px;\n      }\n    }\n    .home {\n      svg {\n        width: 25px;\n        height: 25px;\n        margin-left: 9px;\n      }\n    }\n    .home-text {\n      margin-left: $option-padding-left - 7;\n    }\n    .body {\n      flex: 1;\n      display: flex;\n      flex-direction: column;\n      margin: 10px 0;\n      width: 300px;\n      overflow: auto;\n\n      .option {\n        align-items: center;\n        cursor: pointer;\n        display: flex;\n        color: var(--link);\n        font-size: 14px;\n        height: $option-height;\n        white-space: nowrap;\n        background-color: transparent;\n        width: 100%;\n        border-radius: 0;\n        border: none;\n\n        .cluster-badge-logo-text {\n          color: var(--default-active-text);\n          font-weight: 500;\n        }\n\n        .pin {\n          font-size: 16px;\n          margin-left: auto;\n          display: none;\n          color: var(--body-text);\n          &.showPin {\n            display: block;\n          }\n        }\n\n        .cluster-name {\n          line-height: normal;\n\n          & > p {\n            width: 182px;\n            white-space: nowrap;\n            overflow: hidden;\n            text-overflow: ellipsis;\n            text-align: left;\n\n            &.description {\n              font-size: 12px;\n              padding-right: 8px;\n              color: var(--darker);\n            }\n          }\n        }\n\n        &:hover {\n          text-decoration: none;\n\n          .pin {\n            display: block;\n            color: var(--darker-text);\n          }\n        }\n        &.disabled {\n          background: transparent;\n          cursor: not-allowed;\n\n          .rancher-provider-icon,\n          .cluster-name p {\n            filter: grayscale(1);\n            color: var(--muted) !important;\n          }\n\n          .pin {\n            cursor: pointer;\n          }\n        }\n\n        &:focus {\n          outline: 0;\n          box-shadow: none;\n        }\n\n        > i, > img {\n          display: block;\n          font-size: $icon-size;\n          margin-right: 14px;\n          &:not(.pin){\n            width: 42px;\n          }\n        }\n\n        .rancher-provider-icon,\n        svg {\n          margin-right: 16px;\n          fill: var(--link);\n        }\n\n        .top-menu-icon {\n          outline-offset: 4px;\n        }\n\n        &.router-link-active, &.active-menu-link {\n          &:focus-visible {\n            .top-menu-icon, .app-icon {\n              @include focus-outline;\n            }\n          }\n\n          &:focus-visible .rancher-provider-icon {\n            @include focus-outline;\n            outline-offset: -4px;\n          }\n\n          background: var(--primary-hover-bg);\n          color: var(--primary-hover-text);\n\n          svg {\n            fill: var(--primary-hover-text);\n          }\n\n          i {\n            color: var(--primary-hover-text);\n          }\n\n          div .description {\n            color: var(--default);\n          }\n        }\n\n        &:focus-visible {\n          .top-menu-icon, .rancher-provider-icon, .app-icon {\n            @include focus-outline;\n          }\n        }\n\n        &:hover {\n          color: var(--primary-hover-text);\n          background: var(--primary-hover-bg);\n          > div {\n            color: var(--primary-hover-text);\n\n            .description {\n              color: var(--default);\n            }\n          }\n          svg {\n            fill: var(--primary-hover-text);\n          }\n          div {\n            color: var(--primary-hover-text);\n          }\n          &.disabled {\n            background: transparent;\n            color: var(--muted);\n\n            > .pin {\n              color:var(--default-text);\n              display: block;\n            }\n          }\n        }\n      }\n\n      .option, .option-disabled {\n        padding: $option-padding 0 $option-padding $option-padding-left;\n      }\n\n      .search {\n        position: relative;\n        > input {\n          background-color: transparent;\n          padding-right: 35px;\n          padding-left: 25px;\n          height: 32px;\n        }\n        > .magnifier {\n          position: absolute;\n          top: 12px;\n          left: 8px;\n          width: 12px;\n          height: 12px;\n          font-size: 12px;\n          opacity: 0.4;\n\n          &.active {\n            opacity: 1;\n\n            &:hover {\n              color: var(--body-text);\n            }\n          }\n        }\n        > i {\n          position: absolute;\n          font-size: 12px;\n          top: 12px;\n          right: 8px;\n          opacity: 0.7;\n          cursor: pointer;\n          &:hover {\n            color: var(--disabled-bg);\n          }\n        }\n      }\n\n      .clusters-all {\n        display: flex;\n        flex-direction: row-reverse;\n        margin-right: 16px;\n        margin-top: 10px;\n\n        &:focus-visible {\n          outline: none;\n        }\n\n        span {\n          display: flex;\n          align-items: center;\n        }\n\n        &:focus-visible span {\n          @include focus-outline;\n          outline-offset: 4px;\n        }\n      }\n\n      .clusters {\n        overflow-y: auto;\n\n         a, span {\n          margin: 0;\n         }\n\n        &-search {\n          display: flex;\n          align-items: center;\n          gap: 14px;\n          margin: 16px 0;\n          height: 42px;\n\n          .search {\n            transition: all 0.25s ease-in-out;\n            transition-delay: 2s;\n            width: 72%;\n            height: 36px;\n\n            input {\n              height: 100%;\n            }\n          }\n\n          &-count {\n            position: relative;\n            display: flex;\n            flex-direction: column;\n            width: 42px;\n            height: 42px;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            color: var(--default-active-text);\n            margin-left: $option-padding-left;\n            border-radius: 5px;\n            font-size: 10px;\n            font-weight: bold;\n\n            span {\n              font-size: 14px;\n            }\n\n            .router-link-active {\n              &:hover {\n                text-decoration: none;\n              }\n            }\n\n            i {\n              font-size: 12px;\n              position: absolute;\n              right: -3px;\n              top: 2px;\n            }\n          }\n        }\n      }\n\n      .none-matching {\n        width: 100%;\n        text-align: center;\n        padding: 8px\n      }\n\n      .clustersPinned {\n        .category {\n          &-title {\n            margin: 8px 0;\n            margin-left: 16px;\n            hr {\n              margin: 0;\n              width: 94%;\n              transition: all 0.25s ease-in-out;\n              max-width: 100%;\n            }\n          }\n        }\n        .pin {\n          display: block;\n        }\n      }\n\n      .category {\n        display: flex;\n        flex-direction: column;\n        place-content: flex-end;\n        flex: 1;\n\n        &-title {\n          display: flex;\n          flex-direction: row;\n          align-items: flex-start;\n          align-items: center;\n          margin: 15px 0;\n          margin-left: 16px;\n          font-size: 14px;\n          text-transform: uppercase;\n\n          span {\n            transition: all 0.25s ease-in-out;\n            display: flex;\n            max-height: 16px;\n          }\n\n          hr {\n            margin: 0;\n            max-width: 50px;\n            width: 0;\n            transition: all 0.25s ease-in-out;\n          }\n        }\n\n         i {\n            padding-left: $option-padding-left - 5;\n          }\n      }\n    }\n\n    &.menu-open {\n      .option {\n        &.router-link-active, &.active-menu-link {\n          &:focus-visible {\n            @include focus-outline;\n            border-radius: 0;\n            outline-offset: -4px;\n\n            .top-menu-icon, .app-icon, .rancher-provider-icon {\n              outline: none;\n              border-radius: 0;\n            }\n          }\n        }\n\n        &:focus-visible {\n          @include focus-outline;\n          outline-offset: -4px;\n\n          .top-menu-icon, .app-icon, .rancher-provider-icon {\n            outline: none;\n            border-radius: 0;\n          }\n        }\n      }\n    }\n\n    &.menu-close {\n      .side-menu-logo  {\n        opacity: 0;\n      }\n      .category {\n        &-title {\n          span {\n            opacity: 0;\n          }\n\n          hr {\n            width: 40px;\n          }\n        }\n      }\n      .clusters-all {\n        flex-direction: row;\n        margin-left: $option-padding-left + 2;\n\n        span {\n          i {\n            display: none;\n          }\n        }\n      }\n\n      .clustersPinned {\n        .category {\n          &-title {\n            hr {\n              width: 40px;\n            }\n          }\n        }\n      }\n\n      .footer {\n        margin: 20px 10px;\n        width: 50px;\n\n        .support {\n          display: none;\n        }\n\n        .version{\n          text-align: center;\n\n          &.version-small {\n            font-size: 12px;\n          }\n        }\n      }\n    }\n\n    .footer {\n      margin: 20px;\n      width: 240px;\n      display: flex;\n      flex: 0;\n      flex-direction: row;\n      > * {\n        flex: 1;\n        color: var(--link);\n\n        &:first-child {\n          text-align: left;\n        }\n        &:last-child {\n          text-align: right;\n        }\n        text-align: center;\n      }\n\n      .support a:focus-visible {\n        @include focus-outline;\n        outline-offset: 4px;\n      }\n\n      .version {\n        cursor: pointer;\n\n        a:focus-visible {\n          @include focus-outline;\n          outline-offset: 4px;\n        }\n      }\n    }\n  }\n\n  .side-menu-glass {\n    position: absolute;\n    top: 0;\n    left: 0px;\n    bottom: 0;\n    width: 100vw;\n    z-index: 99;\n    opacity: 1;\n  }\n\n  .side-menu-logo {\n    align-items: center;\n    display: flex;\n    transform: translateX($app-bar-collapsed-width);\n    opacity: 1;\n    max-width: 200px;\n    width: 100%;\n    justify-content: center;\n    transition: all 0.5s;\n    overflow: hidden;\n    & IMG {\n      object-fit: contain;\n      height: 21px;\n      max-width: 200px;\n    }\n  }\n\n  .fade-enter-active, .fade-leave-active {\n    transition: all 0.25s;\n    transition-timing-function: ease;\n  }\n\n  .fade-leave-active {\n    transition: all 0.25s;\n  }\n\n  .fade-leave-to {\n    left: -300px;\n  }\n\n  .fade-enter {\n    left: -300px;\n  }\n\n  .locale-chooser {\n    cursor: pointer;\n  }\n\n  .localeSelector {\n    :deep() .v-popper__inner {\n      padding: 50px 0;\n    }\n\n    :deep() .v-popper__arrow-container {\n      display: none;\n    }\n\n    :deep() .v-popper:focus {\n      outline: 0;\n    }\n\n    li {\n      padding: 8px 20px;\n\n      &:hover {\n        background-color: var(--primary-hover-bg);\n        color: var(--primary-hover-text);\n        text-decoration: none;\n      }\n    }\n  }\n</style>\n"]}]}