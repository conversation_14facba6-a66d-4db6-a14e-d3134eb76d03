{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/Radio/RadioGroup.vue?vue&type=style&index=0&id=065d92ec&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/Radio/RadioGroup.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5yYWRpby1ncm91cCB7CiAgJjpmb2N1cywgJjpmb2N1cy12aXNpYmxlIHsKICAgIGJvcmRlcjogbm9uZTsKICAgIG91dGxpbmU6IG5vbmU7CiAgfQoKICAmOmZvY3VzLXZpc2libGUgLnJhZGlvLWJ1dHRvbi1jaGVja2VkIHsKICAgIEBpbmNsdWRlIGZvY3VzLW91dGxpbmU7CiAgfQoKICBoMyB7CiAgICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgfQoKICAmLnJvdyB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgLnJhZGlvLWNvbnRhaW5lciB7CiAgICAgIG1hcmdpbi1yaWdodDogMTBweDsKICAgIH0KICB9CgogIC5sYWJlbHsKICAgIGZvbnQtc2l6ZTogMTRweCAhaW1wb3J0YW50OwogIH0KfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/Radio/RadioGroup.vue"], "names": [], "mappings": ";AAgRA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB;;EAEA,CAAC,EAAE;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB;;EAEA,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACpB;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B;AACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/Radio/RadioGroup.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { PropType, defineComponent } from 'vue';\nimport { _VIEW } from '@shell/config/query-params';\nimport RadioButton from '@components/Form/Radio/RadioButton.vue';\n\ninterface Option {\n  value: unknown,\n  label: string,\n  description?: string,\n}\n\nexport default defineComponent({\n  components: { RadioButton },\n  props:      {\n    /**\n     * Name for the checkbox grouping, must be unique on page.\n     */\n    name: {\n      type:     String,\n      required: true\n    },\n\n    /**\n     * Options can be an array of {label, value}, or just values.\n     */\n    options: {\n      type:     Array as PropType<Option[] | string[]>,\n      required: true\n    },\n\n    /**\n     * If options are just values, then labels can be a corresponding display\n     * value.\n     */\n    labels: {\n      type:    Array as PropType<string[]>,\n      default: null\n    },\n\n    /**\n     * The selected value.\n     */\n    value: {\n      type:    [Boolean, String, Object],\n      default: null\n    },\n\n    /**\n     * Disable the radio group.\n     */\n    disabled: {\n      type:    Boolean,\n      default: false\n    },\n\n    /**\n     * The radio group editing mode.\n     * @values _EDIT, _VIEW\n     */\n    mode: {\n      type:    String,\n      default: 'edit'\n    },\n\n    /**\n     * Label for above the radios.\n     */\n    label: {\n      type:    String,\n      default: null\n    },\n\n    /**\n     * The i18n key to use for the radio group label.\n     */\n    labelKey: {\n      type:    String,\n      default: null\n    },\n\n    /**\n     * Radio group tooltip.\n     */\n    tooltip: {\n      type:    [String, Object],\n      default: null\n    },\n\n    /**\n     * The i18n key to use for the radio group tooltip.\n     */\n    tooltipKey: {\n      type:    String,\n      default: null\n    },\n\n    /**\n     * Show radio buttons in column or row.\n     */\n    row: {\n      type:    Boolean,\n      default: false\n    }\n  },\n\n  emits: ['update:value'],\n\n  data() {\n    return { currFocusedElem: undefined as undefined | EventTarget | null };\n  },\n\n  computed: {\n    /**\n     * Creates a collection of Options from the provided props.\n     */\n    normalizedOptions(): Option[] {\n      const out: Option[] = [];\n\n      for (let i = 0; i < this.options.length; i++) {\n        const opt = this.options[i];\n\n        if (typeof opt === 'object' && opt) {\n          out.push(opt);\n        } else if (this.labels) {\n          out.push({\n            label: this.labels[i],\n            value: opt\n          });\n        } else {\n          out.push({\n            label: opt,\n            value: opt\n          });\n        }\n      }\n\n      return out;\n    },\n\n    /**\n     * Determines the view mode for the radio group.\n     */\n    isView(): boolean {\n      return this.mode === _VIEW;\n    },\n\n    /**\n     * Determines if the radio group is disabled.\n     */\n    isDisabled(): boolean {\n      return (this.disabled || this.isView);\n    },\n    radioGroupLabel(): string {\n      return this.labelKey ? this.t(this.labelKey) : this.label ? this.label : '';\n    }\n  },\n\n  beforeUnmount() {\n    const radioGroup = this.$refs?.radioGroup as HTMLInputElement;\n\n    radioGroup.removeEventListener('focusin', this.focusChanged);\n  },\n\n  mounted() {\n    const radioGroup = this.$refs?.radioGroup as HTMLInputElement;\n\n    radioGroup.addEventListener('focusin', this.focusChanged);\n  },\n\n  methods: {\n    focusChanged(ev: Event) {\n      this.currFocusedElem = ev.target;\n    },\n    /**\n     * Keyboard left/right event listener to select next/previous option. Emits\n     * the input event.\n     */\n    clickNext(direction: number): void {\n      // moving focus away from a custom group element and pressing arrow keys\n      // should not have any effect on the group - custom UI for radiogroup option(s)\n      if (this.currFocusedElem !== this.$refs?.radioGroup) {\n        return;\n      }\n\n      const opts = this.normalizedOptions;\n      const selected = opts.find((x) => x.value === this.value);\n      let newIndex = (selected ? opts.indexOf(selected) : -1) + direction;\n\n      if (newIndex >= opts.length) {\n        newIndex = opts.length - 1;\n      } else if (newIndex < 0) {\n        newIndex = 0;\n      }\n\n      this.$emit('update:value', opts[newIndex].value);\n    }\n  }\n});\n</script>\n\n<template>\n  <div>\n    <!-- Label -->\n    <div\n      v-if=\"label || labelKey || tooltip || tooltipKey || $slots.label\"\n      class=\"radio-group label\"\n    >\n      <slot name=\"label\">\n        <h3>\n          <t\n            v-if=\"labelKey\"\n            :k=\"labelKey\"\n          />\n          <template v-else-if=\"label\">\n            {{ label }}\n          </template>\n          <i\n            v-if=\"tooltipKey\"\n            v-clean-tooltip=\"t(tooltipKey)\"\n            class=\"icon icon-info icon-lg\"\n          />\n          <i\n            v-else-if=\"tooltip\"\n            v-clean-tooltip=\"tooltip\"\n            class=\"icon icon-info icon-lg\"\n          />\n        </h3>\n      </slot>\n    </div>\n\n    <!-- Group -->\n    <div\n      ref=\"radioGroup\"\n      role=\"radiogroup\"\n      :aria-label=\"radioGroupLabel\"\n      class=\"radio-group\"\n      :class=\"{'row':row}\"\n      tabindex=\"0\"\n      @keydown.down.prevent.stop=\"clickNext(1)\"\n      @keydown.up.prevent.stop=\"clickNext(-1)\"\n      @keydown.space.enter.stop.prevent\n    >\n      <div\n        v-for=\"(option, i) in normalizedOptions\"\n        :key=\"i\"\n      >\n        <slot\n          :v-bind=\"$attrs\"\n          :option=\"option\"\n          :is-disabled=\"isDisabled\"\n          :name=\"i\"\n        >\n          <!-- Default input -->\n          <RadioButton\n            :name=\"name\"\n            :value=\"value\"\n            :label=\"option.label\"\n            :description=\"option.description\"\n            :val=\"option.value\"\n            :disabled=\"isDisabled\"\n            :data-testid=\"`radio-button-${i}`\"\n            :mode=\"mode\"\n            :prevent-focus-on-radio-groups=\"true\"\n            @update:value=\"$emit('update:value', $event)\"\n          />\n        </slot>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang='scss'>\n.radio-group {\n  &:focus, &:focus-visible {\n    border: none;\n    outline: none;\n  }\n\n  &:focus-visible .radio-button-checked {\n    @include focus-outline;\n  }\n\n  h3 {\n    position: relative;\n  }\n\n  &.row {\n    display: flex;\n    .radio-container {\n      margin-right: 10px;\n    }\n  }\n\n  .label{\n    font-size: 14px !important;\n  }\n}\n</style>\n"]}]}