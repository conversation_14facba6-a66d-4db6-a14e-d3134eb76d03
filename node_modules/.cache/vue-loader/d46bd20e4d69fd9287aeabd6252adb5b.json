{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/NodeAffinity.vue?vue&type=template&id=6fbce0fc", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/NodeAffinity.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/NodeAffinity.vue"], "names": [], "mappings": ";EA8LE,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B;IACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB;QACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClD,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;cACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB;cACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7D,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/NodeAffinity.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport debounce from 'lodash/debounce';\nimport { _VIEW } from '@shell/config/query-params';\nimport { mapGetters } from 'vuex';\nimport { get, isEmpty, clone } from '@shell/utils/object';\nimport { NODE } from '@shell/config/types';\nimport MatchExpressions from '@shell/components/form/MatchExpressions';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport { randomStr } from '@shell/utils/string';\nimport ArrayListGrouped from '@shell/components/form/ArrayListGrouped';\n\nexport default {\n  emits: ['update:value'],\n\n  components: {\n    ArrayListGrouped, MatchExpressions, LabeledSelect, LabeledInput\n  },\n\n  props: {\n    // value should be NodeAffinity or VolumeNodeAffinity\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n\n    mode: {\n      type:    String,\n      default: 'create'\n    },\n\n    // has select for matching fields or expressions (used for node affinity)\n    // https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.25/#nodeselectorterm-v1-core\n    matchingSelectorDisplay: {\n      type:    Boolean,\n      default: false,\n    },\n  },\n\n  data() {\n    // VolumeNodeAffinity only has 'required' field\n    if (this.value.required) {\n      return { nodeSelectorTerms: this.value.required.nodeSelectorTerms };\n    } else {\n      const { preferredDuringSchedulingIgnoredDuringExecution = [], requiredDuringSchedulingIgnoredDuringExecution = {} } = this.value;\n      const { nodeSelectorTerms = [] } = requiredDuringSchedulingIgnoredDuringExecution;\n      const allSelectorTerms = [...preferredDuringSchedulingIgnoredDuringExecution, ...nodeSelectorTerms].map((term) => {\n        const neu = clone(term);\n\n        neu._id = randomStr(4);\n        if (term.preference) {\n          Object.assign(neu, term.preference);\n          delete neu.preference;\n        }\n\n        return neu;\n      });\n\n      return {\n        allSelectorTerms,\n        weightedNodeSelectorTerms: preferredDuringSchedulingIgnoredDuringExecution,\n        defaultWeight:             1,\n        // rules in MatchExpressions.vue can not catch changes what happens on parent component\n        // we need re-render it via key changing\n        rerenderNums:              randomStr(4)\n      };\n    }\n  },\n\n  computed: {\n    ...mapGetters({ t: 'i18n/t' }),\n    isView() {\n      return this.mode === _VIEW;\n    },\n    hasWeighted() {\n      return !!this.weightedNodeSelectorTerms;\n    },\n    node() {\n      return NODE;\n    },\n    affinityOptions() {\n      const out = [this.t('workload.scheduling.affinity.preferred'), this.t('workload.scheduling.affinity.required')];\n\n      return out;\n    }\n  },\n\n  created() {\n    this.queueUpdate = debounce(this.update, 500);\n  },\n\n  methods: {\n    update() {\n      const out = {};\n      const requiredDuringSchedulingIgnoredDuringExecution = { nodeSelectorTerms: [] };\n      const preferredDuringSchedulingIgnoredDuringExecution = [] ;\n\n      this.allSelectorTerms.forEach((t) => {\n        const term = { ...t };\n\n        // the 'matching' field isn't part of the affinity spec: including this in the save request will cause a flood of errors that might cause the request to fail\n        // same deal with term.preference.weight\n        if (term.matchExpressions) {\n          term.matchExpressions = (term.matchExpressions || []).map((expression) => {\n            const out = { ...expression };\n\n            delete out.matching;\n\n            return out;\n          });\n        }\n\n        if (term.matchFields) {\n          term.matchFields = (term.matchFields || []).map((field) => {\n            const out = { ...field };\n\n            delete out.matching;\n\n            return out;\n          });\n        }\n\n        if (term.weight) {\n          const neu = { weight: term.weight, preference: term };\n\n          delete neu.preference.weight;\n\n          preferredDuringSchedulingIgnoredDuringExecution.push(neu);\n        } else {\n          requiredDuringSchedulingIgnoredDuringExecution.nodeSelectorTerms.push(term);\n        }\n      });\n\n      if (preferredDuringSchedulingIgnoredDuringExecution.length) {\n        out.preferredDuringSchedulingIgnoredDuringExecution = preferredDuringSchedulingIgnoredDuringExecution;\n      }\n      if (requiredDuringSchedulingIgnoredDuringExecution.nodeSelectorTerms.length) {\n        out.requiredDuringSchedulingIgnoredDuringExecution = requiredDuringSchedulingIgnoredDuringExecution;\n      }\n\n      this.$emit('update:value', out);\n    },\n\n    remove() {\n      this.rerenderNums = randomStr(4);\n      this.queueUpdate();\n    },\n\n    changePriority(term) {\n      if (term.weight) {\n        delete term['weight'];\n      } else {\n        term['weight'] = 1;\n      }\n      this.update();\n    },\n\n    priorityDisplay(term) {\n      return 'weight' in term ? this.t('workload.scheduling.affinity.preferred') : this.t('workload.scheduling.affinity.required');\n    },\n\n    updateExpressions(row, expressions) {\n      const expressionsMatching = {\n        matchFields:      [],\n        matchExpressions: []\n      };\n\n      if (expressions.length) {\n        expressions.forEach((expression) => {\n          expressionsMatching[expression.matching || 'matchExpressions'].push(expression);\n        });\n\n        row['matchFields'] = expressionsMatching.matchFields;\n        row['matchExpressions'] = expressionsMatching.matchExpressions;\n\n        this.update();\n      }\n    },\n\n    get,\n\n    isEmpty\n  }\n\n};\n</script>\n\n<template>\n  <div\n    class=\"row\"\n    @update:value=\"queueUpdate\"\n  >\n    <div class=\"col span-12\">\n      <ArrayListGrouped\n        v-model:value=\"allSelectorTerms\"\n        class=\"mt-20\"\n        :mode=\"mode\"\n        :default-add-value=\"{matchExpressions:[]}\"\n        :add-label=\"t('workload.scheduling.affinity.addNodeSelector')\"\n        @remove=\"remove\"\n      >\n        <template #default=\"props\">\n          <div class=\"row\">\n            <div class=\"col span-9\">\n              <LabeledSelect\n                :options=\"affinityOptions\"\n                :value=\"priorityDisplay(props.row.value)\"\n                :label=\"t('workload.scheduling.affinity.priority')\"\n                :mode=\"mode\"\n                :data-testid=\"`node-affinity-priority-index${props.i}`\"\n                @update:value=\"(changePriority(props.row.value))\"\n              />\n            </div>\n            <div\n              v-if=\"'weight' in props.row.value\"\n              class=\"col span-3\"\n            >\n              <LabeledInput\n                v-model:value.number=\"props.row.value.weight\"\n                :mode=\"mode\"\n                type=\"number\"\n                min=\"1\"\n                max=\"100\"\n                :label=\"t('workload.scheduling.affinity.weight.label')\"\n                :placeholder=\"t('workload.scheduling.affinity.weight.placeholder')\"\n                :data-testid=\"`node-affinity-weight-index${props.i}`\"\n                @update:value=\"update\"\n              />\n            </div>\n          </div>\n          <MatchExpressions\n            :value=\"matchingSelectorDisplay ? props.row.value : props.row.value.matchExpressions\"\n            :matching-selector-display=\"matchingSelectorDisplay\"\n            :mode=\"mode\"\n            class=\"col span-12 mt-20\"\n            :type=\"node\"\n            :show-remove=\"false\"\n            :data-testid=\"`node-affinity-expressions-index${props.i}`\"\n            @update:value=\"(updateExpressions(props.row.value, $event))\"\n          />\n        </template>\n      </ArrayListGrouped>\n    </div>\n  </div>\n</template>\n\n<style>\n</style>\n"]}]}