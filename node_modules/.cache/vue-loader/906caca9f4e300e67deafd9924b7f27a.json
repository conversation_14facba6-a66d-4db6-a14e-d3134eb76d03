{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/CatalogList/CatalogLoadDialog.vue?vue&type=style&index=0&id=3efa8c75&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/CatalogList/CatalogLoadDialog.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLnBsdWdpbi1pbnN0YWxsLWRpYWxvZyB7CiAgICBwYWRkaW5nOiAxMHB4OwoKICAgIGg0IHsKICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICB9CgogICAgLmRpYWxvZy1wYW5lbCB7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICAgIG1pbi1oZWlnaHQ6IDEwMHB4OwoKICAgICAgcCB7CiAgICAgICAgbWFyZ2luLWJvdHRvbTogNXB4OwogICAgICB9CgogICAgICAuZGlhbG9nLWluZm8gewogICAgICAgIGZsZXg6IDE7CiAgICAgIH0KICAgIH0KCiAgICAuZGlhbG9nLWJ1dHRvbnMgewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kOwogICAgICBtYXJnaW4tdG9wOiAxMHB4OwoKICAgICAgPiAqOm5vdCg6bGFzdC1jaGlsZCkgewogICAgICAgIG1hcmdpbi1yaWdodDogMTBweDsKICAgICAgfQogICAgfQogIH0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/CatalogList/CatalogLoadDialog.vue"], "names": [], "mappings": ";EAugBE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEb,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEjB,EAAE;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACpB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACT;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEhB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpB;IACF;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/CatalogList/CatalogLoadDialog.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapGetters } from 'vuex';\nimport isEmpty from 'lodash/isEmpty';\n\nimport { CATALOG, SECRET, SERVICE, WORKLOAD_TYPES } from '@shell/config/types';\nimport { UI_PLUGIN_LABELS, UI_PLUGIN_NAMESPACE } from '@shell/config/uiplugins';\nimport { TYPES as SECRET_TYPES } from '@shell/models/secret';\nimport { allHash } from '@shell/utils/promise';\n\nimport ResourceManager from '@shell/mixins/resource-manager';\n\nimport AsyncButton from '@shell/components/AsyncButton';\nimport AppModal from '@shell/components/AppModal';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport Loading from '@shell/components/Loading.vue';\nimport { Banner } from '@components/Banner';\nimport { LabeledInput } from '@components/Form/LabeledInput';\n\nconst DEFAULT_DEPLOYMENT = {\n  type:     WORKLOAD_TYPES.DEPLOYMENT,\n  metadata: {\n    name:      '',\n    namespace: UI_PLUGIN_NAMESPACE,\n    labels:    {}\n  },\n  spec: {\n    replicas: 1,\n    selector: { matchLabels: {} },\n    template: {\n      metadata: {\n        namespace: UI_PLUGIN_NAMESPACE,\n        labels:    {}\n      },\n      spec: {\n        containers: [\n          {\n            image:           '',\n            imagePullPolicy: 'Always',\n            name:            'container-0'\n          }\n        ],\n        imagePullSecrets: [],\n        restartPolicy:    'Always'\n      }\n    }\n  }\n};\n\nconst DEFAULT_SERVICE = {\n  type:     SERVICE,\n  metadata: {\n    name:      '',\n    namespace: UI_PLUGIN_NAMESPACE,\n    labels:    { [UI_PLUGIN_LABELS.CATALOG_IMAGE]: '' }\n  },\n  spec: {\n    ports: [\n      {\n        name:       '',\n        port:       8080,\n        protocol:   'TCP',\n        targetPort: 8080\n      }\n    ],\n    selector: { [UI_PLUGIN_LABELS.CATALOG_IMAGE]: '' },\n    type:     'ClusterIP'\n  }\n};\n\nconst DEFAULT_REPO = {\n  type:     CATALOG.CLUSTER_REPO,\n  metadata: {\n    name:   '',\n    labels: { [UI_PLUGIN_LABELS.CATALOG_IMAGE]: '' }\n  },\n  spec: { url: null }\n};\n\nconst initialState = () => {\n  const deploymentValues = structuredClone(DEFAULT_DEPLOYMENT);\n  const serviceValues = structuredClone(DEFAULT_SERVICE);\n  const repoValues = structuredClone(DEFAULT_REPO);\n\n  return {\n    deploymentValues,\n    serviceValues,\n    repoValues,\n    canModifyName:              true,\n    canModifyImage:             true,\n    imagePullSecrets:           [],\n    imagePullNamespacedSecrets: [],\n    extensionUrl:               null,\n    extensionDeployment:        null,\n    extensionSvc:               null,\n    extensionRepo:              null,\n    extensionCrd:               null\n  };\n};\n\nexport default {\n  emits: ['closed', 'refresh'],\n\n  components: {\n    AsyncButton, Banner, LabeledInput, Loading, LabeledSelect, AppModal,\n  },\n\n  mixins: [ResourceManager],\n\n  async fetch() {\n    const hash = {};\n\n    if ( this.$store.getters['management/canList'](WORKLOAD_TYPES.DEPLOYMENT) ) {\n      hash.deployments = this.$store.dispatch('management/findAll', { type: WORKLOAD_TYPES.DEPLOYMENT });\n    }\n\n    if ( this.$store.getters['management/canList'](SERVICE) ) {\n      hash.services = this.$store.dispatch('management/findAll', { type: SERVICE });\n    }\n\n    await allHash(hash);\n\n    this.secondaryResourceData = this.secondaryResourceDataConfig();\n    this.resourceManagerFetchSecondaryResources(this.secondaryResourceData);\n  },\n\n  data() {\n    return {\n      ...initialState(),\n      secondaryResourceData: null,\n      showModal:             false,\n      returnFocusSelector:   '[data-testid=\"extensions-catalog-load-dialog\"]'\n    };\n  },\n\n  computed: {\n    ...mapGetters({ allRepos: 'catalog/repos' }),\n\n    namespacedDeployments() {\n      return this.$store.getters['management/all'](WORKLOAD_TYPES.DEPLOYMENT).filter((dep) => dep.metadata.namespace === UI_PLUGIN_NAMESPACE);\n    },\n\n    namespacedServices() {\n      return this.$store.getters['management/all'](SERVICE).filter((svc) => svc.metadata.namespace === UI_PLUGIN_NAMESPACE);\n    }\n  },\n\n  methods: {\n    secondaryResourceDataConfig() {\n      return {\n        namespace: UI_PLUGIN_NAMESPACE,\n        data:      {\n          [SECRET]: {\n            applyTo: [\n              {\n                var:         'imagePullNamespacedSecrets',\n                parsingFunc: (data) => {\n                  return data.filter((secret) => (secret._type === SECRET_TYPES.DOCKER || secret._type === SECRET_TYPES.DOCKER_JSON));\n                }\n              }\n            ]\n          }\n        }\n      };\n    },\n\n    showDialog() {\n      this.showModal = true;\n    },\n\n    closeDialog(result) {\n      this.showModal = false;\n      this.$emit('closed', result);\n\n      // Reset state\n      Object.assign(this.$data, initialState());\n      this.secondaryResourceData = this.secondaryResourceDataConfig();\n      this.resourceManagerFetchSecondaryResources(this.secondaryResourceData);\n    },\n\n    async loadImage(btnCb) {\n      try {\n        if (!isEmpty(this.deploymentValues.spec.template.spec.containers[0].image)) {\n          const image = this.deploymentValues.spec.template.spec.containers[0].image;\n          const name = this.extractImageName(image);\n\n          if (name) {\n            // Create deployment\n            await this.loadDeployment(image, name, btnCb);\n\n            if (this.extensionDeployment) {\n              // Create service\n              await this.loadService(name, btnCb);\n            }\n\n            if (this.extensionSvc) {\n              // Create helm repo\n              await this.loadRepo(name, btnCb);\n            }\n\n            if (this.extensionRepo) {\n              btnCb(true);\n              this.closeDialog();\n              this.$store.dispatch('growl/success', {\n                title:   this.t('plugins.manageCatalog.imageLoad.success.title', { name }),\n                message: this.t('plugins.manageCatalog.imageLoad.success.message'),\n                timeout: 4000,\n              }, { root: true });\n              this.$emit('refresh');\n            }\n          } else {\n            throw new Error('Unable to determine image name');\n          }\n        }\n      } catch (e) {\n        this.handleGrowlError(e, true);\n        btnCb(false);\n      }\n    },\n\n    async loadDeployment(image, name, btnCb) {\n      const exists = this.namespacedDeployments.find((dep) => dep.spec.template.spec.containers[0].image === image);\n\n      if (!exists) {\n        // Sets deploymentValues with name, labels, and imagePullSecrets\n        const deploymentValues = this.parseDeploymentValues(name);\n\n        this.extensionDeployment = await this.$store.dispatch('management/create', deploymentValues);\n\n        try {\n          await this.extensionDeployment.save();\n        } catch (e) {\n          this.handleGrowlError(e, true);\n          btnCb(false);\n        }\n      } else {\n        const error = {\n          _statusText: this.t('plugins.manageCatalog.imageLoad.error.exists.deployment.title'),\n          message:     this.t('plugins.manageCatalog.imageLoad.error.exists.deployment.message', { image })\n        };\n\n        this.handleGrowlError(error);\n        btnCb(false);\n      }\n    },\n\n    async loadService(name, btnCb) {\n      const serviceName = `${ name }-svc`;\n      const exists = this.namespacedServices.find((svc) => svc.metadata.name === serviceName);\n\n      if (exists) {\n        const error = {\n          _statusText: this.t('plugins.manageCatalog.imageLoad.error.exists.service.title'),\n          message:     this.t('plugins.manageCatalog.imageLoad.error.exists.service.message', { svc: serviceName })\n        };\n\n        this.handleGrowlError(error, true);\n        btnCb(false);\n\n        return;\n      }\n\n      // Sets serviceValues with name, label, and selector\n      const serviceValues = this.parseServiceValues(name, serviceName);\n      const serviceResource = await this.$store.dispatch('management/create', serviceValues);\n\n      try {\n        await serviceResource.save();\n      } catch (e) {\n        this.handleGrowlError(e, true);\n        btnCb(false);\n\n        return;\n      }\n\n      try {\n        this.extensionSvc = await this.$store.dispatch('management/find', {\n          type:      SERVICE,\n          id:        `${ UI_PLUGIN_NAMESPACE }/${ serviceResource.metadata.name }`,\n          namespace: UI_PLUGIN_NAMESPACE\n        });\n\n        if (this.extensionSvc) {\n          this.extensionUrl = `http://${ this.extensionSvc.spec.clusterIP }:${ this.extensionSvc.spec.ports[0].port }`;\n        } else {\n          throw new Error('Error fetching extension service');\n        }\n      } catch (e) {\n        this.handleGrowlError(e, true);\n        btnCb(false);\n      }\n    },\n\n    async loadRepo(name, btnCb) {\n      const chartName = `${ name }-charts`;\n      const exists = this.allRepos.find((repo) => repo.metadata.name === chartName);\n\n      if (exists) {\n        const error = {\n          _statusText: this.t('plugins.manageCatalog.imageLoad.error.exists.repo.title'),\n          message:     this.t('plugins.manageCatalog.imageLoad.error.exists.repo.message', { repo: chartName })\n        };\n\n        this.handleGrowlError(error);\n        btnCb(false);\n        this.clean();\n\n        return;\n      }\n\n      // Set repoValues with name, label, and url\n      const repoValues = this.parseRepoValues(chartName);\n\n      this.extensionRepo = await this.$store.dispatch('management/create', repoValues);\n\n      try {\n        await this.extensionRepo.save();\n      } catch (e) {\n        this.handleGrowlError(e, true);\n        btnCb(false);\n      }\n    },\n\n    parseDeploymentValues(name) {\n      let out = {};\n\n      this.deploymentValues.metadata['name'] = name;\n\n      const addLabel = { [UI_PLUGIN_LABELS.CATALOG_IMAGE]: name };\n      const addTo = ['metadata.labels', 'spec.selector.matchLabels', 'spec.template.metadata.labels'];\n\n      // Populates workloadselector labels\n      out = this.assignLabels(this.deploymentValues, addLabel, addTo);\n\n      if (this.imagePullSecrets.length) {\n        out.spec.template.spec.imagePullSecrets = this.imagePullSecrets.map((secret) => {\n          return { name: secret };\n        });\n      }\n\n      return out;\n    },\n\n    parseServiceValues(name, serviceName) {\n      const out = this.serviceValues;\n\n      out.metadata.name = serviceName;\n      out.metadata.labels[UI_PLUGIN_LABELS.CATALOG_IMAGE] = name;\n      out.spec.selector[UI_PLUGIN_LABELS.CATALOG_IMAGE] = name;\n\n      return out;\n    },\n\n    parseRepoValues(chartName) {\n      const out = this.repoValues;\n\n      out.metadata.name = chartName;\n      out.metadata.labels[UI_PLUGIN_LABELS.CATALOG_IMAGE] = this.deploymentValues.metadata.name;\n      out.spec.url = this.extensionUrl;\n\n      return out;\n    },\n\n    assignLabels(source, labels, args) {\n      for (let i = 0; i < args.length; i++) {\n        const path = args[i].split('.');\n        let currentObj = source;\n\n        for (let j = 0; j < path.length - 1; j++) {\n          currentObj = currentObj[path[j]];\n        }\n\n        currentObj[path[path.length - 1]] = labels;\n      }\n\n      return source;\n    },\n\n    extractImageVersion(image) {\n      // Returns the version number with optional pre-release identifiers\n      const regex = /:(\\d+\\.\\d+\\.\\d+([-\\w\\d]+)*)$/;\n      const matches = regex.exec(image);\n\n      if (matches) {\n        return matches[1];\n      }\n\n      return null;\n    },\n\n    extractImageName(image) {\n      // Returns the name within the image that prefixes the version number\n      const regex = /\\/([^/:]+)(?::\\d+\\.\\d+\\.\\d+(-[a-zA-Z0-9]+)?|$)/;\n      const matches = regex.exec(image);\n\n      if (matches) {\n        return matches[1];\n      }\n\n      return null;\n    },\n\n    clean() {\n      // Remove failed resources\n      if (this.extensionDeployment) {\n        this.extensionDeployment.remove();\n      }\n      if (this.extensionSvc) {\n        this.extensionSvc.remove();\n      }\n      if (this.extensionRepo) {\n        this.extensionRepo.remove();\n      }\n    },\n\n    handleGrowlError(e, clean = false) {\n      const error = e?.data || e;\n\n      this.$store.dispatch('growl/error', {\n        title:   error._statusText,\n        message: error.message,\n        timeout: 5000,\n      }, { root: true });\n\n      if (clean) {\n        this.clean();\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <app-modal\n    v-if=\"showModal\"\n    name=\"catalogLoadDialog\"\n    height=\"auto\"\n    :scrollable=\"true\"\n    :trigger-focus-trap=\"true\"\n    :return-focus-selector=\"returnFocusSelector\"\n    @close=\"closeDialog()\"\n  >\n    <Loading\n      v-if=\"$fetchState.loading\"\n      mode=\"relative\"\n    />\n    <div\n      v-else\n      class=\"plugin-install-dialog\"\n    >\n      <div>\n        <h4>\n          {{ t('plugins.manageCatalog.imageLoad.load') }}\n        </h4>\n        <p>\n          {{ t('plugins.manageCatalog.imageLoad.prompt') }}\n        </p>\n\n        <div class=\"custom mt-10\">\n          <Banner\n            color=\"info\"\n            :label=\"t('plugins.manageCatalog.imageLoad.banner')\"\n            class=\"mt-10\"\n          />\n        </div>\n\n        <div class=\"custom mt-10\">\n          <div class=\"fields\">\n            <LabeledInput\n              v-model:value.trim=\"deploymentValues.spec.template.spec.containers[0].image\"\n              label-key=\"plugins.manageCatalog.imageLoad.fields.image.label\"\n              placeholder-key=\"plugins.manageCatalog.imageLoad.fields.image.placeholder\"\n            />\n          </div>\n        </div>\n        <div class=\"custom mt-10\">\n          <div class=\"fields\">\n            <LabeledSelect\n              v-model:value=\"imagePullSecrets\"\n              :label=\"t('plugins.manageCatalog.imageLoad.fields.imagePullSecrets.label')\"\n              :tooltip=\"t('plugins.manageCatalog.imageLoad.fields.imagePullSecrets.tooltip')\"\n              :multiple=\"true\"\n              :taggable=\"true\"\n              :options=\"imagePullNamespacedSecrets\"\n              option-label=\"metadata.name\"\n              :reduce=\"service => service.metadata.name\"\n            />\n            <Banner\n              color=\"warning\"\n              class=\"mt-10\"\n            >\n              <span v-clean-html=\"t('plugins.manageCatalog.imageLoad.fields.secrets.banner', {}, true)\" />\n            </Banner>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"custom mt-10\">\n        <div class=\"fields\">\n          <div class=\"dialog-buttons mt-20\">\n            <button\n              class=\"btn role-secondary\"\n              data-testid=\"image-load-ext-modal-cancel-btn\"\n              @click=\"closeDialog()\"\n            >\n              {{ t('generic.cancel') }}\n            </button>\n            <AsyncButton\n              mode=\"load\"\n              data-testid=\"image-load-ext-modal-install-btn\"\n              @click=\"loadImage\"\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  </app-modal>\n</template>\n\n<style lang=\"scss\" scoped>\n  .plugin-install-dialog {\n    padding: 10px;\n\n    h4 {\n      font-weight: bold;\n    }\n\n    .dialog-panel {\n      display: flex;\n      flex-direction: column;\n      min-height: 100px;\n\n      p {\n        margin-bottom: 5px;\n      }\n\n      .dialog-info {\n        flex: 1;\n      }\n    }\n\n    .dialog-buttons {\n      display: flex;\n      justify-content: flex-end;\n      margin-top: 10px;\n\n      > *:not(:last-child) {\n        margin-right: 10px;\n      }\n    }\n  }\n</style>\n"]}]}