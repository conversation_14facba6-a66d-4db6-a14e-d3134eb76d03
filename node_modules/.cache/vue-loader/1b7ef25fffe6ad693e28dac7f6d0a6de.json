{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/PluginInfoPanel.vue?vue&type=style&index=0&id=e2327fee&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/PluginInfoPanel.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/PluginInfoPanel.vue"], "names": [], "mappings": ";EA4SE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,EAAE,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAErD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACV;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACjE;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACjE;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACnD;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACvC;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB;MACF;;MAEA,CAAC,EAAE;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACT;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACX;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;UAEZ,EAAE,EAAE;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACd;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7C;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACtB;QACF;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;QAEP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;QAET,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB;MACF;IACF;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/PluginInfoPanel.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapGetters } from 'vuex';\nimport ChartReadme from '@shell/components/ChartReadme';\nimport { Banner } from '@components/Banner';\nimport LazyImage from '@shell/components/LazyImage';\nimport { MANAGEMENT } from '@shell/config/types';\nimport { SETTING } from '@shell/config/settings';\nimport { useWatcherBasedSetupFocusTrapWithDestroyIncluded } from '@shell/composables/focusTrap';\n\nexport default {\n  async fetch() {\n    const bannerSetting = await this.$store.getters['management/byId'](MANAGEMENT.SETTING, SETTING.BANNERS);\n    const { showHeader, bannerHeader } = JSON.parse(bannerSetting.value);\n\n    if (showHeader === 'true') {\n      const headerBannerFontSize = Number(bannerHeader?.fontSize?.split('px')[0] ?? 0);\n\n      this.headerBannerSize = headerBannerFontSize * 2;\n    }\n  },\n  components: {\n    Banner,\n    ChartReadme,\n    LazyImage\n  },\n  data() {\n    return {\n      showSlideIn:      false,\n      info:             undefined,\n      infoVersion:      undefined,\n      versionInfo:      undefined,\n      versionError:     undefined,\n      defaultIcon:      require('~shell/assets/images/generic-plugin.svg'),\n      headerBannerSize: 0,\n      isActive:         false\n    };\n  },\n  created() {\n    useWatcherBasedSetupFocusTrapWithDestroyIncluded(() => this.showSlideIn, '#slide-in-content-element');\n  },\n  computed: {\n    ...mapGetters({ theme: 'prefs/theme' }),\n\n    applyDarkModeBg() {\n      if (this.theme === 'dark') {\n        return { 'dark-mode': true };\n      }\n\n      return {};\n    },\n  },\n  watch: {\n    showSlideIn: {\n      handler(neu) {\n        // we register the global event on slidein visibility\n        // so that it doesn't collide with other global events\n        if (neu) {\n          document.addEventListener('keyup', this.handleEscapeKey);\n        } else {\n          document.removeEventListener('keyup', this.handleEscapeKey);\n        }\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    show(info) {\n      this.info = info;\n      this.showSlideIn = true;\n      this.version = null;\n      this.versionInfo = null;\n      this.versionError = null;\n\n      this.loadPluginVersionInfo();\n    },\n\n    hide() {\n      this.showSlideIn = false;\n    },\n\n    async loadPluginVersionInfo(version) {\n      const versionName = version || this.info.displayVersion;\n\n      const isVersionNotCompatible = this.info.versions?.find((v) => v.version === versionName && !v.isVersionCompatible);\n\n      if (!this.info.chart || isVersionNotCompatible) {\n        return;\n      }\n\n      this.infoVersion = versionName;\n\n      this.versionError = false;\n      this.versionInfo = undefined;\n\n      try {\n        this.versionInfo = await this.$store.dispatch('catalog/getVersionInfo', {\n          repoType:  this.info.chart.repoType,\n          repoName:  this.info.chart.repoName,\n          chartName: this.info.chart.chartName,\n          versionName\n        });\n        // Here we set us versionInfo. The returned\n        // object contains everything all info\n        // about a currently installed app, and it has the\n        // following keys:\n        //\n        // - appReadme: A short overview of what the app does. This\n        //   forms the first few paragraphs of the chart info when\n        //   you install a Helm chart app through Rancher.\n        // - chart: Metadata about the Helm chart, including the\n        //   name and version.\n        // - readme: This is more detailed information that appears\n        //   under the heading \"Chart Information (Helm README)\" when\n        //   you install or upgrade a Helm chart app through Rancher,\n        //   below the app README.\n        // - values: All Helm chart values for the currently installed\n        //   app.\n      } catch (e) {\n        this.versionError = true;\n        console.error('Unable to fetch VersionInfo: ', e); // eslint-disable-line no-console\n      }\n    },\n\n    handleVersionBtnTooltip(version) {\n      if (!version.isVersionCompatible && Object.keys(version.versionIncompatibilityData).length) {\n        return this.t(version.versionIncompatibilityData?.tooltipKey, { required: version.versionIncompatibilityData?.required, mainHost: version.versionIncompatibilityData?.mainHost });\n      }\n\n      return '';\n    },\n\n    handleVersionBtnClass(version) {\n      return { 'version-active': version.version === this.infoVersion, disabled: !version.isVersionCompatible };\n    },\n\n    onEnter() {\n      this.isActive = true; // Set active state after the transition\n    },\n\n    onLeave() {\n      this.isActive = false; // Remove active state when fully closed\n    },\n\n    handleEscapeKey(event) {\n      event.stopPropagation();\n\n      if (event.key === 'Escape') {\n        this.hide();\n      }\n    }\n  }\n};\n</script>\n<template>\n  <div\n    class=\"plugin-info-panel\"\n    :style=\"`--banner-top-offset: ${headerBannerSize}px`\"\n  >\n    <div\n      v-if=\"showSlideIn\"\n      class=\"glass\"\n      data-testid=\"extension-details-bg\"\n      @click=\"hide()\"\n    />\n    <transition\n      name=\"slide\"\n      @after-enter=\"onEnter\"\n      @after-leave=\"onLeave\"\n    >\n      <div\n        v-if=\"showSlideIn\"\n        id=\"slide-in-content-element\"\n        class=\"slideIn\"\n        data-testid=\"extension-details\"\n        :class=\"{'active': isActive}\"\n      >\n        <div\n          v-if=\"info\"\n          class=\"plugin-info-content\"\n        >\n          <div class=\"plugin-header\">\n            <div\n              class=\"plugin-icon\"\n              :class=\"applyDarkModeBg\"\n            >\n              <LazyImage\n                v-if=\"info.icon\"\n                :initial-src=\"defaultIcon\"\n                :error-src=\"defaultIcon\"\n                :src=\"info.icon\"\n                class=\"icon plugin-icon-img\"\n              />\n              <img\n                v-else\n                :src=\"defaultIcon\"\n                class=\"icon plugin-icon-img\"\n              >\n            </div>\n            <div class=\"plugin-title\">\n              <h2\n                class=\"slideIn__header\"\n                data-testid=\"extension-details-title\"\n              >\n                {{ info.label }}\n              </h2>\n              <p class=\"plugin-description\">\n                {{ info.description }}\n              </p>\n            </div>\n            <div class=\"plugin-close\">\n              <div class=\"slideIn__header__buttons\">\n                <div\n                  class=\"slideIn__header__button\"\n                  data-testid=\"extension-details-close\"\n                  role=\"button\"\n                  :aria-label=\"t('plugins.closePluginPanel')\"\n                  tabindex=\"0\"\n                  @click=\"hide()\"\n                  @keyup.enter.space=\"hide()\"\n                >\n                  <i class=\"icon icon-close\" />\n                </div>\n              </div>\n            </div>\n          </div>\n          <div>\n            <Banner\n              v-if=\"info.builtin\"\n              color=\"warning\"\n              :label=\"t('plugins.descriptions.built-in')\"\n              class=\"mt-10\"\n            />\n            <template v-else>\n              <Banner\n                v-if=\"!info.certified\"\n                color=\"warning\"\n                :label=\"t('plugins.descriptions.third-party')\"\n                class=\"mt-10\"\n              />\n              <Banner\n                v-if=\"info.experimental\"\n                color=\"warning\"\n                :label=\"t('plugins.descriptions.experimental')\"\n                class=\"mt-10\"\n              />\n            </template>\n          </div>\n\n          <h3 v-if=\"info.versions.length\">\n            {{ t('plugins.info.versions') }}\n          </h3>\n          <div class=\"plugin-versions mb-10\">\n            <div\n              v-for=\"v in info.versions\"\n              :key=\"`${v.name}-${v.version}`\"\n            >\n              <a\n                v-clean-tooltip=\"handleVersionBtnTooltip(v)\"\n                class=\"version-link\"\n                :class=\"handleVersionBtnClass(v)\"\n                :tabindex=\"!v.isVersionCompatible ? -1 : 0\"\n                role=\"button\"\n                :aria-label=\"t('plugins.viewVersionDetails', {name: v.name, version: v.version})\"\n                @click=\"loadPluginVersionInfo(v.version)\"\n                @keyup.enter.space=\"loadPluginVersionInfo(v.version)\"\n              >\n                {{ v.version }}\n              </a>\n            </div>\n          </div>\n\n          <div v-if=\"versionError\">\n            {{ t('plugins.info.versionError') }}\n          </div>\n          <h3 v-if=\"versionInfo\">\n            {{ t('plugins.info.detail') }}\n          </h3>\n          <div\n            v-if=\"versionInfo\"\n            class=\"plugin-info-detail\"\n          >\n            <ChartReadme\n              v-if=\"versionInfo\"\n              :version-info=\"versionInfo\"\n            />\n          </div>\n          <div v-if=\"!info.versions.length\">\n            <h3>\n              {{ t('plugins.info.versions') }}\n            </h3>\n            <div class=\"version-link version-active version-builtin\">\n              {{ info.displayVersion }}\n            </div>\n          </div>\n        </div>\n      </div>\n    </transition>\n  </div>\n</template>\n<style lang=\"scss\" scoped>\n  .plugin-info-panel {\n    position: fixed;\n    top: 0;\n    left: 0;\n    z-index: 1;\n\n    $slideout-width: 35%;\n    $title-height: 50px;\n    $padding: 5px;\n    $slideout-width: 35%;\n    --banner-top-offset: 0;\n    $header-height: calc(54px + var(--banner-top-offset));\n\n    .glass {\n      z-index: 9;\n      position: fixed;\n      top: $header-height;\n      height: calc(100% - $header-height);\n      left: 0;\n      width: 100%;\n      opacity: 0;\n    }\n\n    .slideIn {\n      border-left: var(--header-border-size) solid var(--header-border);\n      position: fixed;\n      top: $header-height;\n      right: -$slideout-width;\n      height: calc(100% - $header-height);\n      background-color: var(--topmenu-bg);\n      width: $slideout-width;\n      z-index: 10;\n      display: flex;\n      flex-direction: column;\n      padding: 10px;\n\n      &.active {\n        right: 0;\n      }\n\n      /* Enter animation */\n      &.slide-enter-active {\n        transition: right 0.5s ease; /* Animates both enter and leave */\n      }\n\n      &.slide-leave-active {\n        transition: right 0.5s ease; /* Animates both enter and leave */\n      }\n\n      &.slide-enter-from,\n      &.slide-leave-to {\n        right: -$slideout-width; /* Off-screen position */\n      }\n\n      &.slide-enter-to,\n      &.slide-leave-from {\n        right: 0; /* Fully visible position */\n      }\n\n      &__header {\n        text-transform: capitalize;\n      }\n\n      .plugin-info-content {\n        display: flex;\n        flex-direction: column;\n        overflow: hidden;\n\n        .plugin-info-detail {\n          overflow: auto;\n        }\n      }\n\n      h3 {\n        font-size: 14px;\n        margin: 15px 0 10px 0;\n        opacity: 0.7;\n        text-transform: uppercase;\n      }\n\n      .plugin-header {\n        border-bottom: 1px solid var(--border);\n        display: flex;\n        padding-bottom: 20px;\n\n        .plugin-title {\n          flex: 1;\n        }\n      }\n\n      .plugin-icon {\n        font-size: 40px;\n        margin-right:10px;\n        color: #888;\n        width: 44px;\n        height: 44px;\n\n        &.dark-mode {\n          border-radius: calc(2 * var(--border-radius));\n          overflow: hidden;\n          background-color: white;\n        }\n\n        .plugin-icon-img {\n          height: 40px;\n          width: 40px;\n          -o-object-fit: contain;\n          object-fit: contain;\n          position: relative;\n          top: 2px;\n          left: 2px;\n        }\n      }\n\n      .plugin-versions {\n        display: flex;\n        flex-wrap: wrap;\n      }\n\n      .plugin-description {\n        font-size: 15px;\n      }\n\n      .version-link {\n        cursor: pointer;\n        border: 1px solid var(--link);\n        padding: 2px 8px;\n        border-radius: 5px;\n        user-select: none;\n        margin: 0 5px 5px 0;\n        display: block;\n\n        &.version-active {\n          color: var(--link-text);\n          background: var(--link);\n        }\n\n        &.disabled {\n          cursor: not-allowed;\n          color: var(--disabled-text) !important;\n          background-color: var(--disabled-bg) !important;\n          border-color: var(--disabled-bg) !important;\n          text-decoration: none !important;\n        }\n\n        &.version-builtin {\n          display: inline-block;\n        }\n\n        &:focus-visible {\n          @include focus-outline;\n        }\n      }\n\n      &__header {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n\n        &__buttons {\n          display: flex;\n        }\n\n        &__button {\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          padding: 2px;\n\n          > i {\n            font-size: 20px;\n            opacity: 0.5;\n          }\n\n          &:hover {\n            background-color: var(--wm-closer-hover-bg);\n          }\n\n          &:focus-visible {\n            @include focus-outline;\n            outline-offset: -2px;\n          }\n        }\n      }\n\n      .chart-content__tabs {\n        display: flex;\n        flex-direction: column;\n        flex: 1;\n\n        height: 0;\n\n        padding-bottom: 10px;\n\n        :deep() .chart-readmes {\n          flex: 1;\n          overflow: auto;\n        }\n      }\n    }\n  }\n</style>\n"]}]}