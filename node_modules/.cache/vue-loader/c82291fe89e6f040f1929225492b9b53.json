{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/UninstallDialog.vue?vue&type=style&index=0&id=30c1d950&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/UninstallDialog.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLnBsdWdpbi1pbnN0YWxsLWRpYWxvZyB7CiAgICBwYWRkaW5nOiAxMHB4OwoKICAgIGg0IHsKICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICB9CgogICAgLmRpYWxvZy1wYW5lbCB7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICAgIG1pbi1oZWlnaHQ6IDEwMHB4OwoKICAgICAgLmRpYWxvZy1pbmZvIHsKICAgICAgICBmbGV4OiAxOwogICAgICB9CiAgICB9CgogICAgLmRpYWxvZy1idXR0b25zIHsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsKICAgICAgbWFyZ2luLXRvcDogMTBweDsKCiAgICAgID4gKjpub3QoOmxhc3QtY2hpbGQpIHsKICAgICAgICBtYXJnaW4tcmlnaHQ6IDEwcHg7CiAgICAgIH0KICAgIH0KICB9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/UninstallDialog.vue"], "names": [], "mappings": ";EA4HE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEb,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACT;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEhB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpB;IACF;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/pages/c/_cluster/uiplugins/UninstallDialog.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapGetters } from 'vuex';\n\nimport AsyncButton from '@shell/components/AsyncButton';\nimport AppModal from '@shell/components/AppModal.vue';\nimport { CATALOG } from '@shell/config/types';\nimport { UI_PLUGIN_NAMESPACE } from '@shell/config/uiplugins';\n\nexport default {\n  emits: ['closed', 'update'],\n\n  components: {\n    AsyncButton,\n    AppModal,\n  },\n\n  data() {\n    return {\n      plugin: undefined, busy: false, showModal: false\n    };\n  },\n\n  computed: {\n    ...mapGetters({ allCharts: 'catalog/charts' }),\n    returnFocusSelector() {\n      return `[data-testid=\"extension-card-uninstall-btn-${ this.plugin?.name }\"]`;\n    }\n  },\n\n  methods: {\n    showDialog(plugin) {\n      this.plugin = plugin;\n      this.busy = false;\n      this.showModal = true;\n    },\n    closeDialog(result) {\n      this.showModal = false;\n      this.$emit('closed', result);\n    },\n    async uninstall() {\n      this.busy = true;\n\n      const plugin = this.plugin;\n\n      this.$emit('update', plugin.name, 'uninstall');\n\n      // Delete the CR if this is a developer plugin (there is no Helm App, so need to remove the CRD ourselves)\n      if (plugin.uiplugin?.isDeveloper) {\n        // Delete the custom resource\n        await plugin.uiplugin.remove();\n      }\n\n      // Find the app for this plugin\n      const apps = await this.$store.dispatch('management/findAll', { type: CATALOG.APP });\n\n      const pluginApp = apps.find((app) => {\n        return app.namespace === UI_PLUGIN_NAMESPACE && app.name === plugin.name;\n      });\n\n      if (pluginApp) {\n        try {\n          await pluginApp.remove();\n        } catch (e) {\n          this.$store.dispatch('growl/error', {\n            title:   this.t('plugins.error.generic'),\n            message: e.message ? e.message : e,\n            timeout: 10000\n          }, { root: true });\n        }\n\n        await this.$store.dispatch('management/findAll', { type: CATALOG.OPERATION });\n      }\n\n      this.closeDialog(plugin);\n    }\n  }\n};\n</script>\n\n<template>\n  <app-modal\n    v-if=\"showModal\"\n    name=\"uninstallPluginDialog\"\n    height=\"auto\"\n    :scrollable=\"true\"\n    :trigger-focus-trap=\"true\"\n    :return-focus-selector=\"returnFocusSelector\"\n    :return-focus-first-iterable-node-selector=\"'#extensions-main-page'\"\n    @close=\"closeDialog(false)\"\n  >\n    <div\n      v-if=\"plugin\"\n      class=\"plugin-install-dialog\"\n    >\n      <h4 class=\"mt-10\">\n        {{ t('plugins.uninstall.title', { name: plugin.label }) }}\n      </h4>\n      <div class=\"mt-10 dialog-panel\">\n        <div class=\"dialog-info\">\n          <p>\n            {{ t('plugins.uninstall.prompt') }}\n          </p>\n        </div>\n        <div class=\"dialog-buttons\">\n          <button\n            :disabled=\"busy\"\n            class=\"btn role-secondary\"\n            data-testid=\"uninstall-ext-modal-cancel-btn\"\n            @click=\"closeDialog(false)\"\n          >\n            {{ t('generic.cancel') }}\n          </button>\n          <AsyncButton\n            mode=\"uninstall\"\n            data-testid=\"uninstall-ext-modal-uninstall-btn\"\n            @click=\"uninstall()\"\n          />\n        </div>\n      </div>\n    </div>\n  </app-modal>\n</template>\n\n<style lang=\"scss\" scoped>\n  .plugin-install-dialog {\n    padding: 10px;\n\n    h4 {\n      font-weight: bold;\n    }\n\n    .dialog-panel {\n      display: flex;\n      flex-direction: column;\n      min-height: 100px;\n\n      .dialog-info {\n        flex: 1;\n      }\n    }\n\n    .dialog-buttons {\n      display: flex;\n      justify-content: flex-end;\n      margin-top: 10px;\n\n      > *:not(:last-child) {\n        margin-right: 10px;\n      }\n    }\n  }\n</style>\n"]}]}