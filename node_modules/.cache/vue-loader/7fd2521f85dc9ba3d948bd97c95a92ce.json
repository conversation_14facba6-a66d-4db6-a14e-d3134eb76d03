{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/StatusTable.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/StatusTable.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgew0KICBMQVNUX1VQREFURUQsIFRZUEUsIFJFQVNPTiwgTUVTU0FHRSwgU1RBVFVTDQp9IGZyb20gJ0BzaGVsbC9jb25maWcvdGFibGUtaGVhZGVycyc7DQppbXBvcnQgU29ydGFibGVUYWJsZSBmcm9tICdAc2hlbGwvY29tcG9uZW50cy9Tb3J0YWJsZVRhYmxlJzsNCmltcG9ydCB7IGNvcHlUZXh0VG9DbGlwYm9hcmQgfSBmcm9tICdAc2hlbGwvdXRpbHMvY2xpcGJvYXJkJzsNCmltcG9ydCB7IGV4Y2VwdGlvblRvRXJyb3JzQXJyYXkgfSBmcm9tICdAc2hlbGwvdXRpbHMvZXJyb3InOw0KZXhwb3J0IGRlZmF1bHQgew0KICBlbWl0czogWydlcnJvciddLA0KDQogIGNvbXBvbmVudHM6IHsgU29ydGFibGVUYWJsZSB9LA0KICBwcm9wczogICAgICB7DQogICAgcmVzb3VyY2U6IHsNCiAgICAgIHR5cGU6ICAgICBPYmplY3QsDQogICAgICByZXF1aXJlZDogdHJ1ZQ0KICAgIH0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICBjb25zdCBzdGF0dXNIZWFkZXJzID0gWw0KICAgICAgVFlQRSwNCiAgICAgIFNUQVRVUywNCiAgICAgIExBU1RfVVBEQVRFRCwNCiAgICAgIFJFQVNPTiwNCiAgICAgIE1FU1NBR0UNCiAgICBdOw0KDQogICAgcmV0dXJuIHsNCiAgICAgIHN0YXR1c1Jvd3M6IHRoaXMucmVzb3VyY2Uuc3RhdHVzLmNvbmRpdGlvbnMgfHwgW10sDQogICAgICBzdGF0dXNIZWFkZXJzDQogICAgfTsNCiAgfSwNCg0KICBtZXRob2RzOiB7DQogICAgY2xpY2tlZCgkZXZlbnQpIHsNCiAgICAgICRldmVudC5zdG9wUHJvcGFnYXRpb24oKTsNCiAgICAgICRldmVudC5wcmV2ZW50RGVmYXVsdCgpOw0KDQogICAgICBjb3B5VGV4dFRvQ2xpcGJvYXJkKHRoaXMuJHNsb3RzLmRlZmF1bHQoKVswXS50ZXh0KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5jb3BpZWQgPSB0cnVlOw0KDQogICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgIHRoaXMuY29waWVkID0gZmFsc2U7DQogICAgICAgIH0sIDIwMDApOw0KICAgICAgfSkuY2F0Y2goKGUpID0+IHsNCiAgICAgICAgdGhpcy4kZW1pdCgnZXJyb3InLCBleGNlcHRpb25Ub0Vycm9yc0FycmF5KGUpKTsNCiAgICAgIH0pOw0KICAgIH0sDQogIH0NCn07DQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/StatusTable.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5C,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;QAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD,CAAC,CAAC;IACJ,CAAC;EACH;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/StatusTable.vue", "sourceRoot": "", "sourcesContent": ["<script>\r\nimport {\r\n  LAST_UPDATED, TYPE, REASON, MESSAGE, STATUS\r\n} from '@shell/config/table-headers';\r\nimport SortableTable from '@shell/components/SortableTable';\r\nimport { copyTextToClipboard } from '@shell/utils/clipboard';\r\nimport { exceptionToErrorsArray } from '@shell/utils/error';\r\nexport default {\r\n  emits: ['error'],\r\n\r\n  components: { SortableTable },\r\n  props:      {\r\n    resource: {\r\n      type:     Object,\r\n      required: true\r\n    }\r\n  },\r\n  data() {\r\n    const statusHeaders = [\r\n      TYPE,\r\n      STATUS,\r\n      LAST_UPDATED,\r\n      REASON,\r\n      MESSAGE\r\n    ];\r\n\r\n    return {\r\n      statusRows: this.resource.status.conditions || [],\r\n      statusHeaders\r\n    };\r\n  },\r\n\r\n  methods: {\r\n    clicked($event) {\r\n      $event.stopPropagation();\r\n      $event.preventDefault();\r\n\r\n      copyTextToClipboard(this.$slots.default()[0].text).then(() => {\r\n        this.copied = true;\r\n\r\n        setTimeout(() => {\r\n          this.copied = false;\r\n        }, 2000);\r\n      }).catch((e) => {\r\n        this.$emit('error', exceptionToErrorsArray(e));\r\n      });\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<template>\r\n  <SortableTable\r\n    :rows=\"statusRows\"\r\n    :headers=\"statusHeaders\"\r\n    :table-actions=\"false\"\r\n    :row-actions=\"false\"\r\n    key-field=\"key\"\r\n    default-sort-by=\"state\"\r\n  />\r\n</template>\r\n"]}]}