{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CodeMirror.vue?vue&type=style&index=0&id=6922e7cf&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CodeMirror.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CodeMirror.vue"], "names": [], "mappings": ";EAgTE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAExB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACtB;UACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAClB;UACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACd;QACF;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;QAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB;QACF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAClB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7C;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACxC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACT;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE7B,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACjB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;UAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACZ;QACF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;YAEX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC1H;UACF;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACpB;QACF;MACF;IACF;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CodeMirror.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { KEYMAP } from '@shell/store/prefs';\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\n\nexport default {\n  name: 'CodeMirror',\n\n  emits: ['onReady', 'onInput', 'onChanges', 'onFocus', 'validationChanged'],\n\n  props: {\n    /**\n     * Sets the edit mode for Text Area.\n     * @values _EDIT, _VIEW\n     */\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n    value: {\n      type:     String,\n      required: true,\n    },\n    options: {\n      type:    Object,\n      default: () => {}\n    },\n    asTextArea: {\n      type:    Boolean,\n      default: false\n    },\n    showKeyMapBox: {\n      type:    Boolean,\n      default: false\n    },\n  },\n\n  data() {\n    return {\n      codeMirrorRef:          null,\n      loaded:                 false,\n      removeKeyMapBox:        false,\n      hasLintErrors:          false,\n      currFocusedElem:        undefined,\n      isCodeMirrorFocused:    false,\n      codeMirrorContainerRef: undefined\n    };\n  },\n\n  computed: {\n    isDisabled() {\n      return this.mode === _VIEW;\n    },\n\n    combinedOptions() {\n      const theme = this.$store.getters['prefs/theme'];\n      const keymap = this.$store.getters['prefs/get'](KEYMAP);\n\n      const out = {\n        // codemirror default options\n        tabSize:                 2,\n        indentWithTabs:          false,\n        mode:                    'yaml',\n        keyMap:                  keymap,\n        theme:                   `base16-${ theme }`,\n        lineNumbers:             true,\n        line:                    true,\n        styleActiveLine:         false,\n        lineWrapping:            true,\n        foldGutter:              true,\n        styleSelectedText:       true,\n        showCursorWhenSelecting: true,\n        autocorrect:             false,\n      };\n\n      if (this.asTextArea) {\n        out.lineNumbers = false;\n        out.foldGutter = false;\n        out.tabSize = 0;\n        out.extraKeys = { Tab: false };\n      }\n\n      Object.assign(out, this.options);\n\n      // parent components control lint with a boolean; if linting is enabled, we need to override that boolean with a custom error handler to wire lint errors into dashboard validation\n      if (this.options?.lint) {\n        out.lint = { onUpdateLinting: this.handleLintErrors };\n      }\n\n      // fixes https://github.com/rancher/dashboard/issues/13653\n      // we can't use the inert HTML prop on the parent because it disables all interaction\n      out.readOnly = this.isDisabled ? 'nocursor' : false;\n\n      return out;\n    },\n\n    keyMapTooltip() {\n      if (this.combinedOptions?.keyMap) {\n        const name = this.t(`prefs.keymap.${ this.combinedOptions.keyMap }`);\n\n        return this.t('codeMirror.keymap.indicatorToolip', { name });\n      }\n\n      return null;\n    },\n\n    isNonDefaultKeyMap() {\n      return this.combinedOptions?.keyMap !== 'sublime';\n    },\n\n    isCodeMirrorContainerFocused() {\n      return this.currFocusedElem === this.codeMirrorContainerRef;\n    },\n\n    codeMirrorContainerTabIndex() {\n      return this.isCodeMirrorFocused ? 0 : -1;\n    }\n  },\n\n  created() {\n    if (window.__codeMirrorLoader) {\n      window.__codeMirrorLoader().then(() => {\n        this.loaded = true;\n      });\n    } else {\n      console.error('Code mirror loader not available'); // eslint-disable-line no-console\n    }\n  },\n\n  async mounted() {\n    const el = this.$refs.codeMirrorContainer;\n\n    el.addEventListener('keydown', this.handleKeyPress);\n    this.codeMirrorContainerRef = this.$refs.codeMirrorContainer;\n  },\n\n  beforeUnmount() {\n    const el = this.$refs.codeMirrorContainer;\n\n    el.removeEventListener('keydown', this.handleKeyPress);\n  },\n\n  watch: {\n    hasLintErrors(neu) {\n      this.$emit('validationChanged', !neu);\n    },\n\n    isCodeMirrorContainerFocused: {\n      handler(neu) {\n        const codeMirrorEl = this.codeMirrorRef?.getInputField();\n\n        if (codeMirrorEl) {\n          codeMirrorEl.tabIndex = neu ? -1 : 0;\n        }\n      },\n      immediate: true\n    }\n  },\n\n  methods: {\n    focusChanged(ev, isBlurred = false) {\n      if (isBlurred) {\n        this.currFocusedElem = undefined;\n      } else {\n        this.currFocusedElem = ev.target;\n      }\n    },\n\n    handleKeyPress(ev) {\n      // allows pressing escape in the editor, useful for modal editing with vim\n      if (this.isCodeMirrorFocused && ev.code === 'Escape') {\n        ev.preventDefault();\n        ev.stopPropagation();\n      }\n\n      // make focus leave the editor for it's parent container so that we can tab\n      const didPressEscapeSequence = ev.shiftKey && ev.code === 'Escape';\n\n      if (this.isCodeMirrorFocused && didPressEscapeSequence) {\n        this.$refs?.codeMirrorContainer?.focus();\n      }\n\n      // if parent container is focused and we press a trigger, focus goes to the editor inside\n      if (this.isCodeMirrorContainerFocused && (ev.code === 'Enter' || ev.code === 'Space')) {\n        this.codeMirrorRef.focus();\n      }\n    },\n    /**\n     * Codemirror yaml linting uses js-yaml parse\n     * it does not distinguish between warnings and errors so we will treat all yaml lint messages as errors\n     * other codemirror linters (eg json) will report from, to, severity where severity may be 'warning' or 'error'\n     * only 'error' level linting will trigger a validation event from this component\n    */\n    handleLintErrors(diagnostics = []) {\n      const hasLintErrors = diagnostics.filter((d) => !d.severity || d.severity === 'error').length > 0;\n\n      this.hasLintErrors = hasLintErrors;\n    },\n\n    focus() {\n      if ( this.$refs.codeMirrorRef ) {\n        this.$refs.codeMirrorRef.cminstance.focus();\n      }\n    },\n\n    refresh() {\n      if ( this.$refs.codeMirrorRef ) {\n        this.$refs.codeMirrorRef.refresh();\n      }\n    },\n\n    onReady(codeMirrorRef) {\n      this.$emit('validationChanged', true);\n\n      this.$nextTick(() => {\n        codeMirrorRef.refresh();\n        this.codeMirrorRef = codeMirrorRef;\n      });\n      this.$emit('onReady', codeMirrorRef);\n    },\n\n    onInput(newCode) {\n      this.$emit('onInput', newCode);\n    },\n\n    onChanges(codeMirrorRef, changes) {\n      this.$emit('onChanges', codeMirrorRef, changes);\n    },\n\n    onFocus() {\n      this.isCodeMirrorFocused = true;\n      this.$emit('onFocus', true);\n    },\n\n    onBlur() {\n      this.isCodeMirrorFocused = false;\n      this.$emit('onFocus', false);\n    },\n\n    updateValue(value) {\n      if ( this.$refs.codeMirrorRef ) {\n        this.$refs.codeMirrorRef.cminstance.doc.setValue(value);\n      }\n    },\n\n    closeKeyMapInfo() {\n      this.removeKeyMapBox = true;\n    },\n  }\n};\n</script>\n\n<template>\n  <div\n    ref=\"codeMirrorContainer\"\n    :tabindex=\"codeMirrorContainerTabIndex\"\n    class=\"code-mirror code-mirror-container\"\n    :class=\"{['as-text-area']: asTextArea}\"\n    @focusin=\"focusChanged\"\n    @blur=\"focusChanged($event, true)\"\n  >\n    <div v-if=\"loaded\">\n      <div\n        v-if=\"showKeyMapBox && !removeKeyMapBox && keyMapTooltip && isNonDefaultKeyMap\"\n        class=\"keymap overlay\"\n      >\n        <div\n          v-clean-tooltip=\"keyMapTooltip\"\n          class=\"keymap-indicator\"\n          data-testid=\"code-mirror-keymap\"\n          @click=\"closeKeyMapInfo\"\n        >\n          <i class=\"icon icon-keyboard keymap-icon\" />\n          <div class=\"close-indicator\">\n            <i class=\"icon icon-close icon-sm\" />\n          </div>\n        </div>\n      </div>\n      <Codemirror\n        id=\"code-mirror-el\"\n        ref=\"codeMirrorRef\"\n        :value=\"value\"\n        :options=\"combinedOptions\"\n        :disabled=\"isDisabled\"\n        :original-style=\"true\"\n        @ready=\"onReady\"\n        @input=\"onInput\"\n        @changes=\"onChanges\"\n        @focus=\"onFocus\"\n        @blur=\"onBlur\"\n      />\n      <span\n        v-show=\"isCodeMirrorFocused\"\n        class=\"escape-text\"\n        role=\"alert\"\n        :aria-describedby=\"t('wm.containerShell.escapeText')\"\n      >{{ t('codeMirror.escapeText') }}</span>\n    </div>\n    <div v-else>\n      Loading...\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\">\n  $code-mirror-animation-time: 0.1s;\n\n  .code-mirror {\n    &.code-mirror-container:focus-visible {\n      @include focus-outline;\n    }\n\n    &.as-text-area .codemirror-container{\n      min-height: 40px;\n      position: relative;\n      display: block;\n      box-sizing: border-box;\n      width: 100%;\n      padding: 10px;\n      background-color: var(--input-bg);\n      border-radius: var(--border-radius);\n      border: solid var(--border-width) var(--input-border);\n      color: var(--input-text);\n\n      &:hover {\n        border-color: var(--input-hover-border);\n      }\n\n      &:focus, &.focus {\n        outline: none;\n        border-color: var(--outline);\n      }\n\n      .CodeMirror-wrap pre {\n        word-break: break-word;\n      }\n      .CodeMirror-code {\n        .CodeMirror-line {\n          &:not(:last-child)>span:after,\n          .cm-markdown-single-trailing-space-odd:before,\n          .cm-markdown-single-trailing-space-even:before {\n            color: var(--muted);\n            position: absolute;\n            line-height: 20px;\n            pointer-events: none;\n          }\n          &:not(:last-child)>span:after {\n            content: '↵';\n            margin-left: 2px;\n          }\n          .cm-markdown-single-trailing-space-odd:before,\n          .cm-markdown-single-trailing-space-even:before {\n            font-weight: bold;\n            content: '·';\n          }\n        }\n      }\n\n      .CodeMirror-lines {\n        color: var(--input-text);\n        padding: 0;\n\n        .CodeMirror-line > span > span {\n          &.cm-overlay {\n            font-family: monospace;\n          }\n        }\n\n        .CodeMirror-line > span {\n          font-family: $body-font;\n        }\n      }\n\n      .CodeMirror-sizer {\n        min-height: 20px;\n      }\n\n      .CodeMirror-selected {\n        background-color: var(--primary) !important;\n      }\n\n      .CodeMirror-selectedtext {\n        color: var(--primary-text);\n      }\n\n      .CodeMirror-line::selection,\n      .CodeMirror-line > span::selection,\n      .CodeMirror-line > span > span::selection {\n        color: var(--primary-text);\n        background-color: var(--primary);\n      }\n\n      .CodeMirror-line::-moz-selection,\n      .CodeMirror-line > span::-moz-selection,\n      .CodeMirror-line > span > span::-moz-selection {\n        color: var(--primary-text);\n        background-color: var(--primary);\n      }\n\n      .CodeMirror-gutters .CodeMirror-foldgutter:empty {\n        display: none;\n      }\n    }\n  }\n\n  .code-mirror {\n    position: relative;\n    margin-bottom: 20px;\n\n    .escape-text {\n      font-size: 12px;\n      position: absolute;\n      bottom: -20px;\n      left: 0;\n    }\n\n    .codemirror-container {\n      z-index: 0;\n      font-size: inherit !important;\n\n      //rm no longer extant selector\n      .CodeMirror {\n        height: initial;\n        background: none\n      }\n\n      .CodeMirror-gutters {\n        background: inherit;\n      }\n    }\n\n    .keymap.overlay {\n      position: absolute;\n      display: flex;\n      top: 7px;\n      right: 7px;\n      z-index: 1;\n      cursor: pointer;\n\n      .keymap-indicator {\n        width: 48px;\n        height: 32px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        border: 1px solid transparent;\n        color: var(--darker);\n        background-color: var(--overlay-bg);\n        font-size: 12px;\n\n        .close-indicator {\n          width: 0;\n\n          .icon-close {\n            color: var(--primary);\n            opacity: 0;\n          }\n        }\n\n        .keymap-icon {\n          font-size: 24px;\n          opacity: 0.8;\n          transition: margin-right $code-mirror-animation-time ease-in-out;\n        }\n\n        &:hover {\n          border: 1px solid var(--primary);\n          border-radius: var(--border-radius);;\n\n          .close-indicator {\n            margin-left: -6px;\n            width: auto;\n\n            .icon-close {\n              opacity: 1;\n              transition: opacity $code-mirror-animation-time ease-in-out $code-mirror-animation-time; // Only animate when being shown\n            }\n          }\n\n          .keymap-icon {\n            opacity: 0.6;\n            margin-right: 10px;\n          }\n        }\n      }\n    }\n  }\n\n</style>\n"]}]}