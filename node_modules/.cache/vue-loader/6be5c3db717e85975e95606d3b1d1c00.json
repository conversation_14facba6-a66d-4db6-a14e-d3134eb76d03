{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayList.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayList.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayList.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEjF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAExC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACpB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACb,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACb,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACb,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACb,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;IAEf,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACtB;IACA,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MAC1C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;MAErE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACtB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAChD,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACxC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MAC3D;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7E,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC;IACF;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9C,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACtD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE/B,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC;IACJ,CAAC;IACD,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAC1D,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAC5B,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR;MACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;MAEd,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QAC7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEjD,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB;MACF;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;KACtD,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEtD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAC/B,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;QAE1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf;EACF,CAAC;AACH,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ArrayList.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport debounce from 'lodash/debounce';\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\nimport { removeAt } from '@shell/utils/array';\nimport { TextAreaAutoGrow } from '@components/Form/TextArea';\nimport { clone } from '@shell/utils/object';\nimport { LabeledInput } from '@components/Form/LabeledInput';\nconst DEFAULT_PROTIP = 'Tip: Paste lines into any list field for easy bulk entry';\n\nexport default {\n  emits: ['add', 'remove', 'update:value'],\n\n  components: { TextAreaAutoGrow, LabeledInput },\n  props:      {\n    value: {\n      type:    Array,\n      default: null,\n    },\n    mode: {\n      type:    String,\n      default: _EDIT,\n    },\n    initialEmptyRow: {\n      type:    Boolean,\n      default: false,\n    },\n    title: {\n      type:    String,\n      default: ''\n    },\n    protip: {\n      type:    [String, Boolean],\n      default: DEFAULT_PROTIP,\n    },\n    showHeader: {\n      type:    Boolean,\n      default: false,\n    },\n    valueLabel: {\n      type:    String,\n      default: 'Value',\n    },\n    valuePlaceholder: {\n      type:    String,\n      default: 'e.g. bar'\n    },\n    valueMultiline: {\n      type:    Boolean,\n      default: false,\n    },\n    addIcon: {\n      type:    String,\n      default: '',\n    },\n    addLabel: {\n      type:    String,\n      default: '',\n    },\n    addAllowed: {\n      type:    Boolean,\n      default: true,\n    },\n    addDisabled: {\n      type:    Boolean,\n      default: false,\n    },\n    removeLabel: {\n      type:    String,\n      default: '',\n    },\n    removeAllowed: {\n      type:    Boolean,\n      default: true,\n    },\n    defaultAddValue: {\n      type:    [String, Number, Object, Array],\n      default: ''\n    },\n    loading: {\n      type:    Boolean,\n      default: false\n    },\n    disabled: {\n      type:    Boolean,\n      default: false,\n    },\n    required: {\n      type:    Boolean,\n      default: false\n    },\n    rules: {\n      default:   () => [],\n      type:      Array,\n      // we only want functions in the rules array\n      validator: (rules) => rules.every((rule) => ['function'].includes(typeof rule))\n    },\n    a11yLabel: {\n      type:    String,\n      default: '',\n    },\n  },\n  data() {\n    const input = (Array.isArray(this.value) ? this.value : []).slice();\n    const rows = [];\n\n    for ( const value of input ) {\n      rows.push({ value });\n    }\n    if ( !rows.length && this.initialEmptyRow ) {\n      const value = this.defaultAddValue ? clone(this.defaultAddValue) : '';\n\n      rows.push({ value });\n    }\n\n    return { rows, lastUpdateWasFromValue: false };\n  },\n  computed: {\n    _addLabel() {\n      return this.addLabel || this.t('generic.add');\n    },\n    _removeLabel() {\n      return this.removeLabel || this.t('generic.remove');\n    },\n\n    isView() {\n      return this.mode === _VIEW;\n    },\n    showAdd() {\n      return this.addAllowed;\n    },\n    disableAdd() {\n      return this.addDisabled;\n    },\n    showRemove() {\n      return this.removeAllowed;\n    },\n    isDefaultProtip() {\n      return this.protip === DEFAULT_PROTIP;\n    },\n    showProtip() {\n      if (this.protip && !this.isDefaultProtip) {\n        return true;\n      }\n\n      return !this.valueMultiline && this.protip;\n    }\n  },\n  watch: {\n    value: {\n      deep: true,\n      handler() {\n        this.lastUpdateWasFromValue = true;\n        this.rows = (this.value || []).map((v) => ({ value: v }));\n      }\n    },\n\n    rows: {\n      deep: true,\n      handler(newValue, oldValue) {\n        // lastUpdateWasFromValue is used to break a cycle where when rows are updated\n        // this was called which then forced rows to updated again\n        if (!this.lastUpdateWasFromValue) {\n          this.queueUpdate();\n        }\n        this.lastUpdateWasFromValue = false;\n      }\n    }\n  },\n  created() {\n    this.queueUpdate = debounce(this.update, 50);\n  },\n  methods: {\n    add() {\n      this.rows.push({ value: clone(this.defaultAddValue) });\n      if (this.defaultAddValue) {\n        this.queueUpdate();\n      }\n      this.$nextTick(() => {\n        const inputs = this.$refs.value;\n\n        if ( inputs && inputs.length > 0 ) {\n          inputs[inputs.length - 1].focus();\n        }\n        this.$emit('add');\n      });\n    },\n    /**\n     * Remove item and emits removed row and its own index value\n     */\n    remove(row, index) {\n      this.$emit('remove', { row, index });\n      removeAt(this.rows, index);\n      this.queueUpdate();\n    },\n\n    /**\n     * Cleanup rows and emit input\n     */\n    update() {\n      if ( this.isView ) {\n        return;\n      }\n      const out = [];\n\n      for ( const row of this.rows ) {\n        const trim = !this.valueMultiline && (typeof row.value === 'string');\n        const value = trim ? row.value.trim() : row.value;\n\n        if ( typeof value !== 'undefined' ) {\n          out.push(value);\n        }\n      }\n      this.$emit('update:value', out);\n    },\n\n    /**\n     * Handle paste event, e.g. split multiple lines in rows\n     */\n    onPaste(index, event) {\n      event.preventDefault();\n      const text = event.clipboardData.getData('text/plain');\n\n      if (this.valueMultiline) {\n        // Allow to paste multiple lines\n        this.rows[index].value = text;\n      } else {\n        // Prevent to paste the value and emit text in multiple rows\n        const split = text.split('\\n').map((value) => ({ value }));\n\n        event.preventDefault();\n        this.rows.splice(index, 1, ...split);\n      }\n\n      this.update();\n    }\n  },\n};\n</script>\n\n<template>\n  <div>\n    <div\n      v-if=\"title\"\n      class=\"clearfix\"\n    >\n      <slot name=\"title\">\n        <h3>\n          {{ title }}\n          <span\n            v-if=\"required\"\n            class=\"required\"\n          >*</span>\n          <i\n            v-if=\"showProtip\"\n            v-clean-tooltip=\"protip\"\n            class=\"icon icon-info\"\n          />\n        </h3>\n      </slot>\n    </div>\n\n    <template v-if=\"rows.length\">\n      <div v-if=\"showHeader\">\n        <slot name=\"column-headers\">\n          <label class=\"value text-label mb-10\">\n            {{ valueLabel }}\n          </label>\n        </slot>\n      </div>\n      <div\n        v-for=\"(row, idx) in rows\"\n        :key=\"idx\"\n        :data-testid=\"`array-list-box${ idx }`\"\n        class=\"box\"\n      >\n        <slot\n          name=\"columns\"\n          :queueUpdate=\"queueUpdate\"\n          :i=\"idx\"\n          :rows=\"rows\"\n          :row=\"row\"\n          :mode=\"mode\"\n          :isView=\"isView\"\n        >\n          <div class=\"value\">\n            <slot\n              name=\"value\"\n              :row=\"row\"\n              :mode=\"mode\"\n              :isView=\"isView\"\n              :queue-update=\"queueUpdate\"\n            >\n              <TextAreaAutoGrow\n                v-if=\"valueMultiline\"\n                ref=\"value\"\n                v-model:value=\"row.value\"\n                :data-testid=\"`textarea-${idx}`\"\n                :placeholder=\"valuePlaceholder\"\n                :mode=\"mode\"\n                :disabled=\"disabled\"\n                @paste=\"onPaste(idx, $event)\"\n                @update:value=\"queueUpdate\"\n              />\n              <LabeledInput\n                v-else-if=\"rules.length > 0\"\n                ref=\"value\"\n                v-model:value=\"row.value\"\n                :data-testid=\"`labeled-input-${idx}`\"\n                :placeholder=\"valuePlaceholder\"\n                :disabled=\"isView || disabled\"\n                :rules=\"rules\"\n                :compact=\"false\"\n                @paste=\"onPaste(idx, $event)\"\n                @update:value=\"queueUpdate\"\n              />\n              <input\n                v-else\n                ref=\"value\"\n                v-model=\"row.value\"\n                :data-testid=\"`input-${idx}`\"\n                :placeholder=\"valuePlaceholder\"\n                :disabled=\"isView || disabled\"\n                :aria-label=\"a11yLabel ? a11yLabel : undefined\"\n                @paste=\"onPaste(idx, $event)\"\n              >\n            </slot>\n          </div>\n        </slot>\n        <div\n          v-if=\"showRemove\"\n          class=\"remove\"\n        >\n          <slot\n            name=\"remove-button\"\n            :remove=\"() => remove(row, idx)\"\n            :i=\"idx\"\n            :row=\"row\"\n          >\n            <button\n              type=\"button\"\n              :disabled=\"isView\"\n              class=\"btn role-link\"\n              :data-testid=\"`remove-item-${idx}`\"\n              :aria-label=\"`${_removeLabel} ${idx + 1}`\"\n              role=\"button\"\n              @click=\"remove(row, idx)\"\n            >\n              {{ _removeLabel }}\n            </button>\n          </slot>\n        </div>\n      </div>\n    </template>\n    <div v-else>\n      <slot name=\"empty\">\n        <div\n          v-if=\"mode==='view'\"\n          class=\"text-muted\"\n        >\n          &mdash;\n        </div>\n      </slot>\n    </div>\n    <div\n      v-if=\"showAdd && !isView\"\n      class=\"footer mt-20\"\n    >\n      <slot\n        v-if=\"showAdd\"\n        name=\"add\"\n        :add=\"add\"\n      >\n        <button\n          type=\"button\"\n          class=\"btn role-tertiary add\"\n          :disabled=\"loading || disableAdd\"\n          data-testid=\"array-list-button\"\n          :aria-label=\"_addLabel\"\n          role=\"button\"\n          @click=\"add()\"\n        >\n          <i\n            class=\"mr-5 icon\"\n            :class=\"loading ? ['icon-lg', 'icon-spinner','icon-spin']: [addIcon]\"\n          />\n          {{ _addLabel }}\n        </button>\n      </slot>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .title {\n    margin-bottom: 10px;\n  }\n\n  .required {\n    color: var(--error);\n  }\n\n  .box {\n    display: grid;\n    grid-template-columns: auto $array-list-remove-margin;\n    align-items: center;\n    margin-bottom: 10px;\n    .value {\n      flex: 1;\n      INPUT {\n        height: $unlabeled-input-height;\n      }\n    }\n  }\n  .remove {\n    text-align: right;\n  }\n  .footer {\n    .protip {\n      float: right;\n      padding: 5px 0;\n    }\n  }\n\n  .required {\n    color: var(--error);\n  }\n</style>\n"]}]}