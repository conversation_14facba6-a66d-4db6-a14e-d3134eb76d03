{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Setting.vue?vue&type=template&id=08a1e59d&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Setting.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Setting.vue"], "names": [], "mappings": ";EAoBE,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD;IACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC;UACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACb,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzC,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9D,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf;QACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC;UACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC1F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB;QACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7F,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzG,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Setting.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport ActionMenu from '@shell/components/ActionMenuShell.vue';\nimport { mapGetters } from 'vuex';\nexport default {\n  name:       'Setting',\n  components: { ActionMenu },\n  props:      {\n    value: {\n      type:     Object,\n      required: true,\n    },\n  },\n  computed: {\n    ...mapGetters({ t: 'i18n/t' }),\n    ...mapGetters({ options: 'action-menu/optionsArray' }),\n  },\n};\n</script>\n\n<template>\n  <div\n    class=\"advanced-setting mb-20\"\n    :data-testid=\"`advanced-setting__option-${value.id}`\"\n  >\n    <div class=\"header\">\n      <div class=\"title\">\n        <h1>\n          {{ value.id }}\n          <span\n            v-if=\"value.fromEnv\"\n            class=\"modified\"\n          >{{ t('advancedSettings.setEnv') }}</span>\n          <span\n            v-else-if=\"value.customized\"\n            class=\"modified\"\n          >{{ t('advancedSettings.modified') }}</span>\n        </h1>\n        <h2>{{ t(`advancedSettings.descriptions.${value.id}`) }}</h2>\n      </div>\n      <div\n        v-if=\"value.hasActions\"\n        class=\"action\"\n      >\n        <action-menu\n          :resource=\"value.data\"\n          :button-aria-label=\"t('advancedSettings.edit.label')\"\n          data-testid=\"action-button\"\n          button-role=\"tertiary\"\n        />\n      </div>\n    </div>\n    <div value>\n      <div v-if=\"value.canHide\">\n        <button\n          class=\"btn btn-sm role-primary\"\n          role=\"button\"\n          :aria-label=\"t('advancedSettings.hideShow')\"\n          @click=\"value.hide = !value.hide\"\n        >\n          {{ value.hide ? t('advancedSettings.show') : t('advancedSettings.hide') }} {{ value.id }}\n        </button>\n      </div>\n      <div\n        v-show=\"!value.canHide || (value.canHide && !value.hide)\"\n        class=\"settings-value\"\n      >\n        <pre v-if=\"value.kind === 'json'\">{{ value.json }}</pre>\n        <pre v-else-if=\"value.kind === 'multiline'\">{{ value.data.value || value.data.default }}</pre>\n        <pre v-else-if=\"value.kind === 'enum'\">{{ t(value.enum) }}</pre>\n        <pre v-else-if=\"value.data.value || value.data.default\">{{ value.data.value || value.data.default }}</pre>\n        <pre\n          v-else\n          class=\"text-muted\"\n        >&lt;{{ t('advancedSettings.none') }}&gt;</pre>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang='scss' scoped>\n.settings-value pre {\n  margin: 0;\n}\n.advanced-setting {\n  border: 1px solid var(--border);\n  padding: 20px;\n  border-radius: var(--border-radius);\n\n  h1 {\n    font-size: 14px;\n  }\n  h2 {\n    font-size: 12px;\n    margin-bottom: 0;\n    opacity: 0.8;\n  }\n}\n\n.header {\n  display: flex;\n  margin-bottom: 20px;\n}\n\n.title {\n  flex: 1;\n}\n\n.modified {\n  margin-left: 10px;\n  border: 1px solid var(--primary);\n  border-radius: 5px;\n  padding: 2px 10px;\n  font-size: 12px;\n}\n</style>\n"]}]}