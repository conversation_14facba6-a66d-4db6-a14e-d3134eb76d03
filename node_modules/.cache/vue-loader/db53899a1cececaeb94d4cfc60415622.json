{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ClusterAppearance.vue?vue&type=style&index=0&id=294d68f6&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ClusterAppearance.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLmNsdXN0ZXItYXBwZWFyYW5jZSB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICAgIG1hcmdpbjogM3B4IDM1cHggMHB4IDBweDsKCiAgICBsYWJlbCB7CiAgICAgIG1hcmdpbjogNnB4IDAgMDsKICAgIH0KCiAgICAmLXByZXZpZXcgewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICAgICAgYWxpZ24tc2VsZjogc3RhcnQ7CiAgICAgIGdhcDogMTBweDsKICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwoKICAgICAgc3BhbiB7CiAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICBhbGlnbi1zZWxmOiBjZW50ZXI7CiAgICAgICAgaGVpZ2h0OiBhdXRvOwogICAgICB9CgogICAgICBidXR0b24gewogICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgYWxpZ24tc2VsZjogY2VudGVyOwogICAgICAgIGhlaWdodDogYXV0bzsKICAgICAgICBtYXJnaW46IDA7CiAgICAgICAgcGFkZGluZzogMDsKICAgICAgICB0b3A6IDA7CiAgICAgICAgY29sb3I6IHZhcigtLWxpbmspOwogICAgICAgIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50OwoKICAgICAgICBpIHsKICAgICAgICAgIG1hcmdpbi1yaWdodDogMnB4OwogICAgICAgIH0KCiAgICAgICAgJjpkaXNhYmxlZCB7CiAgICAgICAgICBjb2xvcjogdmFyKC0tZGlzYWJsZWQtdGV4dCk7CiAgICAgICAgICBjdXJzb3I6IG5vdC1hbGxvd2VkOwogICAgICAgIH0KICAgICAgfQogICAgfQogIH0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ClusterAppearance.vue"], "names": [], "mappings": ";EAwFE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;IAExB,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IACjB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE9B,CAAC,CAAC,CAAC,EAAE;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACd;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACV,CAAC,CAAC,CAAC,EAAE,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEvB,EAAE;UACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACnB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB;MACF;IACF;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ClusterAppearance.vue", "sourceRoot": "", "sourcesContent": ["\n<script>\nimport ClusterIconMenu from '@shell/components/ClusterIconMenu';\nimport { _CREATE, _VIEW } from '@shell/config/query-params';\nimport { CLUSTER_BADGE } from '@shell/config/labels-annotations';\nexport default {\n  title:      'ClusterAppearance',\n  components: { ClusterIconMenu },\n  props:      {\n    name:           { type: String, default: '' },\n    mode:           { type: String, default: _CREATE },\n    currentCluster: { type: Object, default: null }\n  },\n  created() {\n    this.$store.dispatch('customisation/setDefaultPreviewCluster');\n  },\n\n  computed: {\n    disable() {\n      if (this.mode === _VIEW) {\n        return true;\n      }\n\n      return this.name.length <= 1;\n    },\n    clusterPreview() {\n      if (this.mode !== _CREATE) {\n        return {\n          ...this.currentCluster,\n          badge: {\n            iconText: this.currentCluster.metadata.annotations[CLUSTER_BADGE.ICON_TEXT],\n            color:    this.currentCluster.metadata.annotations[CLUSTER_BADGE.COLOR],\n            text:     this.currentCluster.metadata.annotations[CLUSTER_BADGE.TEXT]\n          }\n        };\n      }\n\n      const obj = {\n        ...this.$store.getters['customisation/getPreviewCluster'],\n        label: this.name\n      };\n\n      return obj || {\n        label: this.name,\n        badge: { iconText: null }\n      };\n    },\n  },\n\n  methods: {\n    customBadgeDialog() {\n      this.$store.dispatch('cluster/promptModal', {\n        component:      'AddCustomBadgeDialog',\n        componentProps: {\n          isCreate:        this.mode === _CREATE,\n          mode:            this.mode,\n          clusterName:     this.name,\n          clusterExplorer: this.clusterPreview\n        },\n      });\n    },\n  },\n};\n</script>\n\n<template>\n  <div class=\"cluster-appearance\">\n    <label for=\"name\">\n      {{ t('clusterBadge.setClusterAppearance') }}\n    </label>\n    <div class=\"cluster-appearance-preview\">\n      <span>\n        <ClusterIconMenu :cluster=\"clusterPreview\" />\n      </span>\n      <button\n        :disabled=\"disable\"\n        @click=\"customBadgeDialog\"\n      >\n        <i class=\"icon icon-brush-icon\" />\n        <span>\n          {{ t('clusterBadge.customize') }}\n        </span>\n      </button>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .cluster-appearance {\n    display: flex;\n    flex-direction: column;\n    margin: 3px 35px 0px 0px;\n\n    label {\n      margin: 6px 0 0;\n    }\n\n    &-preview {\n      display: flex;\n      justify-content: center;\n      align-self: start;\n      gap: 10px;\n      justify-content: space-between;\n\n      span {\n        display: flex;\n        align-self: center;\n        height: auto;\n      }\n\n      button {\n        display: flex;\n        align-self: center;\n        height: auto;\n        margin: 0;\n        padding: 0;\n        top: 0;\n        color: var(--link);\n        background: transparent;\n\n        i {\n          margin-right: 2px;\n        }\n\n        &:disabled {\n          color: var(--disabled-text);\n          cursor: not-allowed;\n        }\n      }\n    }\n  }\n</style>\n"]}]}