{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ChangePassword.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ChangePassword.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ChangePassword.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE3D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACnC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9C,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEhC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1H,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC1D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;MACjD,CAAC,CAAC;MACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClD,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC,CAAC,CAAC,wBAAwB;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC;IACH,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;IAE9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrE,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC/B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb;MACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC;MACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACd;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;MAEtH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtG;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC1B,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACvF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE9B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC3F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACzB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C,CAAC,CAAC;IACJ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACxB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB;MACF,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnD,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACvF,CAAC,CAAC;IACJ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACrD,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,QAAQ;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1E,CAAC;QACH,CAAC,CAAC;MACJ,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACX;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC3D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,GAAG;YACJ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACnH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACZ;QACF,CAAC,CAAC;;QAEF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC9C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACT,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzE,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxE;QACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACX;IACF,CAAC;EACH,CAAC;AACH,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ChangePassword.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapGetters } from 'vuex';\nimport { Banner } from '@components/Banner';\nimport { Checkbox } from '@components/Form/Checkbox';\nimport Password from '@shell/components/form/Password';\nimport { NORMAN } from '@shell/config/types';\nimport { _CREATE, _EDIT } from '@shell/config/query-params';\n\n// Component handles three use cases\n// 1) isChange - Current user is changing their own password\n// 2) isCreate - New password is for a new user\n// 3) isEdit - New password is for an existing user\nexport default {\n  emits: ['valid', 'update:value'],\n\n  components: {\n    Checkbox, Banner, Password\n  },\n  props: {\n    mode: {\n      type:    String,\n      default: null\n    },\n    mustChangePassword: {\n      type:    Boolean,\n      default: false\n    }\n  },\n  async fetch() {\n    if (this.isChange) {\n      // Fetch the username for hidden input fields. The value itself is not needed if create or changing another user's password\n      const users = await this.$store.dispatch('rancher/findAll', {\n        type: NORMAN.USER,\n        opt:  { url: '/v3/users', filter: { me: true } }\n      });\n      const user = users?.[0];\n\n      this.username = user?.username;\n    }\n    this.userChangeOnLogin = this.mustChangePassword;\n  },\n  data(ctx) {\n    return {\n      username:                   '',\n      errorMessages:              [],\n      pCanShowMismatchedPassword: false,\n      pIsRandomGenerated:         false,\n      form:                       {\n        deleteKeys:        false,\n        currentP:          '',\n        newP:              '',\n        genP:              '',\n        confirmP:          '',\n        userChangeOnLogin: false,\n      },\n    };\n  },\n  computed: {\n    ...mapGetters({ t: 'i18n/t' }),\n\n    isRandomGenerated: {\n      get() {\n        return this.pIsRandomGenerated;\n      },\n\n      set(isRandomGenerated) {\n        this.pIsRandomGenerated = isRandomGenerated;\n        this.errorMessages = [];\n        this.validate();\n      }\n    },\n\n    passwordGen: {\n      get() {\n        return this.form.genP;\n      },\n\n      set(p) {\n        this.form.genP = p;\n        this.validate();\n      }\n    },\n\n    passwordCurrent: {\n      get() {\n        return this.form.currentP;\n      },\n\n      set(p) {\n        this.form.currentP = p;\n        this.validate();\n      }\n    },\n\n    passwordNew: {\n      get() {\n        return this.form.newP;\n      },\n\n      set(p) {\n        this.form.newP = p;\n        this.validate();\n      }\n    },\n\n    passwordConfirm: {\n      get() {\n        return this.form.confirmP;\n      },\n\n      set(p) {\n        this.form.confirmP = p;\n        this.validate();\n      }\n    },\n\n    userChangeOnLogin: {\n      get() {\n        return this.form.userChangeOnLogin;\n      },\n\n      set(p) {\n        this.form.userChangeOnLogin = p;\n        this.validate();\n      }\n    },\n\n    passwordConfirmBlurred: {\n      get() {\n        return this.pCanShowMismatchedPassword;\n      },\n\n      set(p) {\n        this.pCanShowMismatchedPassword = p;\n        this.validate();\n      }\n    },\n\n    password() {\n      return this.isRandomGenerated ? this.passwordGen : this.passwordNew;\n    },\n\n    isChange() {\n      // Change password prompt\n      return !this.mode;\n    },\n\n    isCreateEdit() {\n      return this.isCreate || this.isEdit;\n    },\n\n    isCreate() {\n      return this.mode === _CREATE;\n    },\n\n    isEdit() {\n      // Edit user prompt\n      return this.mode === _EDIT;\n    },\n\n    userGeneratedPasswordsRequired() {\n      if (this.isChange) {\n        return true;\n      }\n      if (this.isCreate) {\n        return !this.isRandomGenerated;\n      }\n      if (this.isEdit) {\n        return !!this.passwordNew || !!this.passwordConfirm;\n      }\n\n      return false;\n    }\n  },\n  created() {\n    // Catch the 'create' case and there's no content\n    this.validate();\n  },\n\n  methods: {\n    passwordsMatch() {\n      const match = this.passwordNew === this.passwordConfirm;\n\n      this.errorMessages = this.passwordConfirmBlurred && !match ? [this.t('changePassword.errors.mismatchedPassword')] : [];\n\n      return match;\n    },\n\n    baseIsUserGenPasswordValid() {\n      return this.passwordsMatch() && !!this.passwordNew;\n    },\n\n    isValid() {\n      if (this.isChange) {\n        return !!this.passwordCurrent && (this.isRandomGenerated ? true : this.baseIsUserGenPasswordValid());\n      }\n\n      if (this.isRandomGenerated) {\n        // If we're not changing current user... and password is randomly generated... there'll be no new/confirm mismatch\n        return true;\n      }\n\n      if (this.isCreate) {\n        return this.baseIsUserGenPasswordValid();\n      }\n\n      if (this.isEdit) {\n        // If the user generated password is required... ensure it's valid\n        return this.userGeneratedPasswordsRequired ? this.baseIsUserGenPasswordValid() : true;\n      }\n\n      return false;\n    },\n\n    validate() {\n      const isValid = this.isValid();\n\n      if (isValid) {\n        // Covers the case where we don't re-evaluate the error messages (don't need to at the time)\n        this.errorMessages = [];\n      }\n\n      this.$emit('valid', isValid);\n      this.$emit('update:value', {\n        password:          this.password,\n        userChangeOnLogin: this.userChangeOnLogin\n      });\n    },\n\n    async save(user) {\n      if (this.isChange) {\n        await this.changePassword();\n        if (this.form.deleteKeys) {\n          await this.deleteKeys();\n        }\n      } else if (this.isEdit) {\n        return this.setPassword(user);\n      }\n    },\n\n    async setPassword(user) {\n      // Error handling is catered for by caller\n      await this.$store.dispatch('rancher/resourceAction', {\n        type:       NORMAN.USER,\n        actionName: 'setpassword',\n        resource:   user,\n        body:       { newPassword: this.isRandomGenerated ? this.form.genP : this.form.newP },\n      });\n    },\n\n    async changePassword() {\n      try {\n        await this.$store.dispatch('rancher/collectionAction', {\n          type:       NORMAN.USER,\n          actionName: 'changepassword',\n          body:       {\n            currentPassword: this.form.currentP,\n            newPassword:     this.isRandomGenerated ? this.form.genP : this.form.newP\n          },\n        });\n      } catch (err) {\n        this.errorMessages = [err.message || this.t('changePassword.errors.failedToChange')];\n        throw err;\n      }\n    },\n\n    async deleteKeys() {\n      try {\n        const tokens = await this.$store.dispatch('rancher/findAll', {\n          type: NORMAN.TOKEN,\n          opt:  {\n            // Ensure we have any new tokens since last fetched... and that we don't attempt to delete previously deleted tokens\n            force: true\n          }\n        });\n\n        await Promise.all(tokens.reduce((res, token) => {\n          if (!token.current) {\n            res.push(token.remove());\n          }\n\n          return res;\n        }, []));\n      } catch (err) {\n        if (err.message) {\n          this.errorMessages = [err.message];\n        } else if (err.length > 1) {\n          this.errorMessages = [this.t('changePassword.errors.failedDeleteKeys')];\n        } else {\n          this.errorMessages = [this.t('changePassword.errors.failedDeleteKey')];\n        }\n        throw err;\n      }\n    },\n  },\n};\n</script>\n\n<template>\n  <div\n    class=\"change-password\"\n    :class=\"{'change': isChange, 'create': isCreate, 'edit': isEdit}\"\n  >\n    <div class=\"form\">\n      <div class=\"fields\">\n        <Checkbox\n          v-if=\"isChange\"\n          v-model:value=\"form.deleteKeys\"\n          label-key=\"changePassword.deleteKeys.label\"\n          class=\"mt-10\"\n        />\n        <Checkbox\n          v-if=\"isCreateEdit\"\n          v-model:value=\"userChangeOnLogin\"\n          label-key=\"changePassword.changeOnLogin.label\"\n          class=\"mt-10 type\"\n        />\n        <Checkbox\n          v-if=\"isCreateEdit\"\n          v-model:value=\"isRandomGenerated\"\n          label-key=\"changePassword.generatePassword.label\"\n          class=\"mt-10 type\"\n        />\n\n        <!-- Create two 'invisible fields' for password managers -->\n        <input\n          id=\"username\"\n          type=\"text\"\n          name=\"username\"\n          autocomplete=\"username\"\n          :value=\"username\"\n          tabindex=\"-1\"\n          :data-lpignore=\"!isChange\"\n        >\n        <input\n          id=\"password\"\n          type=\"password\"\n          name=\"password\"\n          autocomplete=\"password\"\n          :value=\"password\"\n          tabindex=\"-1\"\n          :data-lpignore=\"!isChange\"\n        >\n        <Password\n          v-if=\"isChange\"\n          v-model:value=\"passwordCurrent\"\n          data-testid=\"account__current_password\"\n          class=\"mt-10\"\n          :required=\"true\"\n          :label=\"t('changePassword.currentPassword.label')\"\n        />\n        <div\n          v-if=\"isRandomGenerated\"\n          :class=\"{'row': isCreateEdit}\"\n        >\n          <div :class=\"{'col': isCreateEdit, 'span-8': isCreateEdit}\">\n            <Password\n              v-model:value=\"passwordGen\"\n              class=\"mt-10\"\n              :is-random=\"true\"\n              :required=\"false\"\n              :label=\"t('changePassword.randomGen.generated.label')\"\n            />\n          </div>\n        </div>\n        <div\n          v-else\n          class=\"userGen\"\n          :class=\"{'row': isCreateEdit}\"\n        >\n          <div :class=\"{'col': isCreateEdit, 'span-4': isCreateEdit}\">\n            <Password\n              v-model:value=\"passwordNew\"\n              data-testid=\"account__new_password\"\n              class=\"mt-10\"\n              :label=\"t('changePassword.userGen.newPassword.label')\"\n              :required=\"userGeneratedPasswordsRequired\"\n              :ignore-password-managers=\"!isChange\"\n            />\n          </div>\n          <div :class=\"{'col': isCreateEdit, 'span-4': isCreateEdit}\">\n            <Password\n              v-model:value=\"passwordConfirm\"\n              data-testid=\"account__confirm_password\"\n              class=\"mt-10\"\n              :label=\"t('changePassword.userGen.confirmPassword.label')\"\n              :required=\"userGeneratedPasswordsRequired\"\n              :ignore-password-managers=\"!isChange\"\n              @blur=\"passwordConfirmBlurred = true\"\n            />\n          </div>\n        </div>\n      </div>\n      <Checkbox\n        v-if=\"isChange\"\n        v-model:value=\"isRandomGenerated\"\n        label-key=\"changePassword.generatePassword.label\"\n        class=\"mt-10 type\"\n      />\n    </div>\n    <div\n      v-if=\"errorMessages && errorMessages.length\"\n      class=\"text-error\"\n      :class=\"{'row': isCreateEdit}\"\n    >\n      <div :class=\"{'col': isCreateEdit, 'span-8': isCreateEdit}\">\n        <Banner\n          v-for=\"(err, i) in errorMessages\"\n          :key=\"i\"\n          role=\"alert\"\n          :aria-label=\"err\"\n          color=\"error\"\n          :label=\"err\"\n          class=\"mb-0\"\n        />\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .change-password {\n    display: flex;\n    flex-direction: column;\n\n    &.change {\n      .form .fields {\n        height: 240px;\n      }\n    }\n\n    &.create, &.edit {\n      height: 185px;\n      .form {\n        .fields {\n          display: flex;\n          flex-direction: column;\n        }\n      }\n    }\n\n    .form {\n      display: flex;\n      flex-direction: column;\n      .fields{\n        #username, #password {\n          height: 0;\n          width: 0;\n          background-size: 0;\n          padding: 0;\n          border: none;\n        }\n      }\n    }\n\n    .text-error {\n      height: 53px;\n    }\n  }\n\n</style>\n"]}]}