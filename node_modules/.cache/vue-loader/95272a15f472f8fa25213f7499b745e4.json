{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DisableAuthProviderModal.vue?vue&type=template&id=434b50ef", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DisableAuthProviderModal.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGFwcC1tb2RhbAogICAgdi1pZj0ic2hvd01vZGFsIgogICAgY3VzdG9tLWNsYXNzPSJyZW1vdmUtbW9kYWwiCiAgICBuYW1lPSJkaXNhYmxlQXV0aFByb3ZpZGVyTW9kYWwiCiAgICA6d2lkdGg9IjQwMCIKICAgIGhlaWdodD0iYXV0byIKICAgIHN0eWxlcz0ibWF4LWhlaWdodDogMTAwdmg7IgogICAgQGNsb3NlPSJjbG9zZSIKICA+CiAgICA8Q2FyZAogICAgICBjbGFzcz0iZGlzYWJsZS1hdXRoLXByb3ZpZGVyIgogICAgICA6c2hvdy1oaWdobGlnaHQtYm9yZGVyPSJmYWxzZSIKICAgID4KICAgICAgPHRlbXBsYXRlICN0aXRsZT4KICAgICAgICA8aDQgY2xhc3M9InRleHQtZGVmYXVsdC10ZXh0Ij4KICAgICAgICAgIHt7IHQoJ3Byb21wdFJlbW92ZS50aXRsZScpIH19CiAgICAgICAgPC9oND4KICAgICAgPC90ZW1wbGF0ZT4KICAgICAgPHRlbXBsYXRlICNib2R5PgogICAgICAgIDxkaXYgY2xhc3M9Im1iLTEwIj4KICAgICAgICAgIDxwIHYtY2xlYW4taHRtbD0idCgncHJvbXB0UmVtb3ZlLmF0dGVtcHRpbmdUb1JlbW92ZUF1dGhDb25maWcnLCBudWxsLCB0cnVlKSIgLz4KICAgICAgICA8L2Rpdj4KICAgICAgPC90ZW1wbGF0ZT4KICAgICAgPHRlbXBsYXRlICNhY3Rpb25zPgogICAgICAgIDxidXR0b24KICAgICAgICAgIGNsYXNzPSJidG4gcm9sZS1zZWNvbmRhcnkiCiAgICAgICAgICBAY2xpY2s9ImNsb3NlIgogICAgICAgID4KICAgICAgICAgIHt7IHQoJ2dlbmVyaWMuY2FuY2VsJykgfX0KICAgICAgICA8L2J1dHRvbj4KICAgICAgICA8ZGl2IGNsYXNzPSJzcGFjZXIiIC8+CiAgICAgICAgPGJ1dHRvbgogICAgICAgICAgY2xhc3M9ImJ0biByb2xlLXByaW1hcnkgYmctZXJyb3IgbWwtMTAiCiAgICAgICAgICA6ZGF0YS10ZXN0aWQ9ImNvbXBvbmVudFRlc3RpZCArICctY29uZmlybS1idXR0b24nIgogICAgICAgICAgQGNsaWNrPSJkaXNhYmxlIgogICAgICAgID4KICAgICAgICAgIHt7IHQoJ2dlbmVyaWMuZGlzYWJsZScpIH19CiAgICAgICAgPC9idXR0b24+CiAgICAgIDwvdGVtcGxhdGU+CiAgICA8L0NhcmQ+CiAgPC9hcHAtbW9kYWw+Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DisableAuthProviderModal.vue"], "names": [], "mappings": ";EAuCE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf;IACE,CAAC,CAAC,CAAC,CAAC;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B;MACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChF,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf;UACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB;UACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DisableAuthProviderModal.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { Card } from '@components/Card';\nimport AppModal from '@shell/components/AppModal.vue';\n\nexport default {\n  name: 'PromptRemove',\n\n  emits: ['disable'],\n\n  components: { Card, AppModal },\n  props:      {\n    /**\n     * Inherited global identifier prefix for tests\n     * Define a term based on the parent component to avoid conflicts on multiple components\n     */\n    componentTestid: {\n      type:    String,\n      default: 'disable-auth-provider'\n    }\n  },\n  data() {\n    return { showModal: false };\n  },\n  methods: {\n    show() {\n      this.showModal = true;\n    },\n    close() {\n      this.showModal = false;\n    },\n    disable() {\n      this.showModal = false;\n      this.$emit('disable');\n    },\n  }\n};\n</script>\n\n<template>\n  <app-modal\n    v-if=\"showModal\"\n    custom-class=\"remove-modal\"\n    name=\"disableAuthProviderModal\"\n    :width=\"400\"\n    height=\"auto\"\n    styles=\"max-height: 100vh;\"\n    @close=\"close\"\n  >\n    <Card\n      class=\"disable-auth-provider\"\n      :show-highlight-border=\"false\"\n    >\n      <template #title>\n        <h4 class=\"text-default-text\">\n          {{ t('promptRemove.title') }}\n        </h4>\n      </template>\n      <template #body>\n        <div class=\"mb-10\">\n          <p v-clean-html=\"t('promptRemove.attemptingToRemoveAuthConfig', null, true)\" />\n        </div>\n      </template>\n      <template #actions>\n        <button\n          class=\"btn role-secondary\"\n          @click=\"close\"\n        >\n          {{ t('generic.cancel') }}\n        </button>\n        <div class=\"spacer\" />\n        <button\n          class=\"btn role-primary bg-error ml-10\"\n          :data-testid=\"componentTestid + '-confirm-button'\"\n          @click=\"disable\"\n        >\n          {{ t('generic.disable') }}\n        </button>\n      </template>\n    </Card>\n  </app-modal>\n</template>\n\n<style lang='scss'>\n  .disable-auth-provider {\n    &.card-container {\n      box-shadow: none;\n    }\n    #confirm {\n      width: 90%;\n      margin-left: 3px;\n    }\n\n    .remove-modal {\n        border-radius: var(--border-radius);\n        overflow: scroll;\n        max-height: 100vh;\n        & ::-webkit-scrollbar-corner {\n          background: rgba(0,0,0,0);\n        }\n    }\n\n    .actions {\n      text-align: right;\n    }\n\n    .card-actions {\n      display: flex;\n\n      .spacer {\n        flex: 1;\n      }\n    }\n  }\n</style>\n"]}]}