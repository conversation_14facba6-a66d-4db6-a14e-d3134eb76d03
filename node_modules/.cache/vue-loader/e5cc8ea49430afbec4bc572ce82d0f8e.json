{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownItem.vue?vue&type=template&id=c902f22a&scoped=true&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownItem.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdgogICAgcmVmPSJkcm9wZG93bk1lbnVJdGVtIgogICAgZHJvcGRvd24tbWVudS1pdGVtCiAgICB0YWJpbmRleD0iLTEiCiAgICByb2xlPSJtZW51aXRlbSIKICAgIDpkaXNhYmxlZD0iZGlzYWJsZWQgfHwgbnVsbCIKICAgIDphcmlhLWRpc2FibGVkPSJkaXNhYmxlZCB8fCBmYWxzZSIKICAgIEBjbGljay5zdG9wPSJoYW5kbGVDbGljayIKICAgIEBrZXlkb3duLmVudGVyLnNwYWNlPSJoYW5kbGVBY3RpdmF0ZSIKICAgIEBrZXlkb3duLnVwLmRvd24uc3RvcD0iaGFuZGxlS2V5ZG93biIKICA+CiAgICA8c2xvdCBuYW1lPSJiZWZvcmUiPgogICAgICA8IS0tRW1wdHkgc2xvdCBjb250ZW50LS0+CiAgICA8L3Nsb3Q+CiAgICA8c2xvdCBuYW1lPSJkZWZhdWx0Ij4KICAgICAgPCEtLUVtcHR5IHNsb3QgY29udGVudC0tPgogICAgPC9zbG90PgogIDwvZGl2Pgo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownItem.vue"], "names": [], "mappings": ";EA4EE,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtC;IACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACR,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownItem.vue", "sourceRoot": "", "sourcesContent": ["<script setup lang=\"ts\">\n/**\n * An item for a dropdown menu. Used in conjunction with RcDropdown.\n */\nimport { inject } from 'vue';\nimport { DropdownContext, defaultContext } from './types';\n\nconst props = defineProps({ disabled: Boolean });\nconst emits = defineEmits(['click']);\n\nconst { close, dropdownItems } = inject<DropdownContext>('dropdownContext') || defaultContext;\n\n/**\n * Handles keydown events to navigate between dropdown items.\n * @param {KeyboardEvent} e - The keydown event.\n */\nconst handleKeydown = (e: KeyboardEvent) => {\n  const activeItem = document.activeElement;\n\n  const activeIndex = dropdownItems.value.indexOf(activeItem || new HTMLElement());\n\n  if (activeIndex < 0) {\n    return;\n  }\n\n  const shouldAdvance = e.key === 'ArrowDown';\n\n  const newIndex = findNewIndex(shouldAdvance, activeIndex, dropdownItems.value);\n\n  if (dropdownItems.value[newIndex] instanceof HTMLElement) {\n    dropdownItems.value[newIndex].focus();\n  }\n};\n\n/**\n * Finds the new index for the dropdown item based on the key pressed.\n * @param shouldAdvance - Whether to advance to the next or previous item.\n * @param activeIndex - Current active index.\n * @param itemsArr - Array of dropdown items.\n * @returns The new index.\n */\nconst findNewIndex = (shouldAdvance: boolean, activeIndex: number, itemsArr: Element[]) => {\n  const newIndex = shouldAdvance ? activeIndex + 1 : activeIndex - 1;\n\n  if (newIndex > itemsArr.length - 1) {\n    return 0;\n  }\n\n  if (newIndex < 0) {\n    return itemsArr.length - 1;\n  }\n\n  return newIndex;\n};\n\nconst handleClick = (e: MouseEvent) => {\n  if (props.disabled) {\n    return;\n  }\n\n  emits('click', e);\n  close();\n};\n\n/**\n * Handles keydown events to activate the dropdown item.\n * @param e - The keydown event.\n */\nconst handleActivate = (e: KeyboardEvent) => {\n  if (e?.target instanceof HTMLElement) {\n    e?.target?.click();\n  }\n};\n</script>\n\n<template>\n  <div\n    ref=\"dropdownMenuItem\"\n    dropdown-menu-item\n    tabindex=\"-1\"\n    role=\"menuitem\"\n    :disabled=\"disabled || null\"\n    :aria-disabled=\"disabled || false\"\n    @click.stop=\"handleClick\"\n    @keydown.enter.space=\"handleActivate\"\n    @keydown.up.down.stop=\"handleKeydown\"\n  >\n    <slot name=\"before\">\n      <!--Empty slot content-->\n    </slot>\n    <slot name=\"default\">\n      <!--Empty slot content-->\n    </slot>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  [dropdown-menu-item] {\n    display: flex;\n    gap: 8px;\n    align-items: center;\n    padding: 9px 8px;\n    margin: 0 9px;\n    border-radius: 4px;\n\n    &:hover {\n      cursor: pointer;\n      background-color: var(--dropdown-hover-bg);\n    }\n    &:focus-visible, &:focus {\n      @include focus-outline;\n      outline-offset: 0;\n    }\n    &[disabled] {\n      color: var(--disabled-text);\n      &:hover {\n        cursor: not-allowed;\n      }\n    }\n  }\n</style>\n"]}]}