{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/TextArea/TextAreaAutoGrow.vue?vue&type=template&id=3d1ee14d&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/TextArea/TextAreaAutoGrow.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPHRleHRhcmVhCiAgICByZWY9InRhIgogICAgOnZhbHVlPSJ2YWx1ZSIKICAgIDpkYXRhLXRlc3RpZD0iJGF0dHJzWydkYXRhLXRlc3RpZCddID8gJGF0dHJzWydkYXRhLXRlc3RpZCddIDogJ3RleHQtYXJlYS1hdXRvLWdyb3cnIgogICAgOmRpc2FibGVkPSJpc0Rpc2FibGVkIgogICAgOnN0eWxlPSJzdHlsZSIKICAgIDpwbGFjZWhvbGRlcj0icGxhY2Vob2xkZXIiCiAgICA6Y2xhc3M9ImNsYXNzTmFtZSIKICAgIGNsYXNzPSJuby1yZXNpemUgbm8tZWFzZSIKICAgIHYtYmluZD0iJGF0dHJzIgogICAgOnNwZWxsY2hlY2s9InNwZWxsY2hlY2siCiAgICBAcGFzdGU9IiRlbWl0KCdwYXN0ZScsICRldmVudCkiCiAgICBAaW5wdXQ9Im9uSW5wdXQoJGV2ZW50KSIKICAgIEBmb2N1cz0iJGVtaXQoJ2ZvY3VzJywgJGV2ZW50KSIKICAgIEBibHVyPSIkZW1pdCgnYmx1cicsICRldmVudCkiCiAgLz4K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/TextArea/TextAreaAutoGrow.vue"], "names": [], "mappings": ";EAsLE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Form/TextArea/TextAreaAutoGrow.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent, inject, PropType } from 'vue';\nimport { debounce } from 'lodash';\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\n\ninterface NonReactiveProps {\n  queueResize(): void;\n}\n\nconst provideProps: NonReactiveProps = {\n  queueResize() {\n    // noop\n  }\n};\n\nexport default defineComponent({\n  inheritAttrs: false,\n\n  props: {\n    value: {\n      type:     String,\n      required: true\n    },\n\n    class: {\n      type:    [String, Array, Object] as PropType<string | unknown[] | Record<string, boolean>>,\n      default: ''\n    },\n\n    /**\n     * Sets the edit mode for Text Area.\n     * @values _EDIT, _VIEW\n     */\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n\n    /**\n     * Sets the Minimum height for Text Area. Prevents the height from becoming\n     * smaller than the value specified in minHeight.\n     */\n    minHeight: {\n      type:    Number,\n      default: 25\n    },\n\n    /**\n     * Sets the maximum height for Text Area. Prevents the height from becoming\n     * larger than the value specified in maxHeight.\n     */\n    maxHeight: {\n      type:    Number,\n      default: 200\n    },\n\n    /**\n     * Text that appears in the Text Area when it has no value set.\n     */\n    placeholder: {\n      type:    String,\n      default: ''\n    },\n\n    /**\n     * Specifies whether Text Area is subject to spell checking by the\n     * underlying browser/OS.\n     */\n    spellcheck: {\n      type:    Boolean,\n      default: true\n    },\n\n    /**\n     * Disables the Text Area.\n     */\n    disabled: {\n      type:    Boolean,\n      default: false\n    }\n  },\n\n  emits: ['update:value', 'paste', 'focus', 'blur'],\n\n  setup() {\n    const queueResize = inject('queueResize', provideProps.queueResize);\n\n    return { queueResize };\n  },\n\n  data() {\n    return {\n      curHeight: this.minHeight,\n      overflow:  'hidden'\n    };\n  },\n\n  computed: {\n    /**\n     * Determines if the Text Area should be disabled.\n     */\n    isDisabled(): boolean {\n      return this.disabled || this.mode === _VIEW;\n    },\n\n    /**\n     * Sets the height to one-line for SSR pageload so that it's already right\n     * (unless the input is long)\n     */\n    style(): string {\n      return `height: ${ this.curHeight }px; overflow: ${ this.overflow };`;\n    },\n\n    className(): string | unknown[] | Record<string, boolean> {\n      return this.class;\n    }\n  },\n\n  watch: {\n    $attrs: {\n      deep: true,\n      handler() {\n        this.queueResize();\n      }\n    }\n  },\n\n  created() {\n    this.queueResize = debounce(this.autoSize, 100);\n  },\n\n  mounted() {\n    (this.$refs.ta as HTMLElement).style.height = `${ this.curHeight }px`;\n    this.$nextTick(() => {\n      this.autoSize();\n    });\n  },\n\n  methods: {\n    /**\n     * Emits the input event and resizes the Text Area.\n    */\n    onInput(event: Event): void {\n      const val = (event?.target as HTMLInputElement)?.value;\n\n      this.$emit('update:value', val);\n      this.queueResize();\n    },\n\n    /**\n     * Gives focus to the Text Area.\n     */\n    focus(): void {\n      (this.$refs?.ta as HTMLElement).focus();\n    },\n\n    /**\n     * Sets the overflowY and height of the Text Area based on the content\n     * entered (calculated via scroll height).\n     */\n    autoSize(): void {\n      const el = this.$refs.ta as HTMLElement;\n\n      if (!el) {\n        return;\n      }\n\n      el.style.height = '1px';\n\n      const border = parseInt(getComputedStyle(el).getPropertyValue('borderTopWidth'), 10) || 0 + parseInt(getComputedStyle(el).getPropertyValue('borderBottomWidth'), 10) || 0;\n      const neu = Math.max(this.minHeight, Math.min(el.scrollHeight + border, this.maxHeight));\n\n      el.style.overflowY = el.scrollHeight > neu ? 'auto' : 'hidden';\n      el.style.height = `${ neu }px`;\n\n      this.curHeight = neu;\n    }\n  }\n});\n</script>\n\n<template>\n  <textarea\n    ref=\"ta\"\n    :value=\"value\"\n    :data-testid=\"$attrs['data-testid'] ? $attrs['data-testid'] : 'text-area-auto-grow'\"\n    :disabled=\"isDisabled\"\n    :style=\"style\"\n    :placeholder=\"placeholder\"\n    :class=\"className\"\n    class=\"no-resize no-ease\"\n    v-bind=\"$attrs\"\n    :spellcheck=\"spellcheck\"\n    @paste=\"$emit('paste', $event)\"\n    @input=\"onInput($event)\"\n    @focus=\"$emit('focus', $event)\"\n    @blur=\"$emit('blur', $event)\"\n  />\n</template>\n"]}]}