{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/AppModal.vue?vue&type=style&index=0&id=529a58e2&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/AppModal.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLm1vZGFsLW92ZXJsYXkgewogICAgcG9zaXRpb246IGFic29sdXRlOwogICAgdG9wOiAwOwogICAgbGVmdDogMDsKICAgIHdpZHRoOiAxMDB2dzsKICAgIGhlaWdodDogMTAwdmg7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1vdmVybGF5LWJnKTsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICB6LWluZGV4OiB6LWluZGV4KCdtb2RhbE92ZXJsYXknKTsKCiAgICAubW9kYWwtY29udGFpbmVyIHsKICAgICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tbW9kYWwtYmcpOwogICAgICBib3JkZXItcmFkaXVzOiB2YXIoLS1ib3JkZXItcmFkaXVzKTsKICAgICAgbWF4LWhlaWdodDogOTV2aDsKICAgICAgb3ZlcmZsb3c6IGF1dG87CiAgICAgIGJvcmRlcjogMnB4IHNvbGlkIHZhcigtLW1vZGFsLWJvcmRlcik7CiAgICB9CiAgfQoKICAubW9kYWwtZmFkZS1lbnRlci1hY3RpdmUsCiAgLm1vZGFsLWZhZGUtbGVhdmUtYWN0aXZlIHsKICAgIHRyYW5zaXRpb246IG9wYWNpdHkgMjAwbXM7CiAgfQoKICAubW9kYWwtZmFkZS1lbnRlciwKICAubW9kYWwtZmFkZS1sZWF2ZS10byB7CiAgICBvcGFjaXR5OiAwOwogIH0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/AppModal.vue"], "names": [], "mappings": ";EAwME,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,EAAE,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEhC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACZ", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/AppModal.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent } from 'vue';\nimport { DEFAULT_FOCUS_TRAP_OPTS, useBasicSetupFocusTrap, getFirstFocusableElement } from '@shell/composables/focusTrap';\n\nexport const DEFAULT_ITERABLE_NODE_SELECTOR = 'body;';\n\nexport default defineComponent({\n  name: 'AppModal',\n\n  emits: ['close'],\n\n  inheritAttrs: false,\n  props:        {\n    /**\n     * If set to false, it will not be possible to close modal by clicking on\n     * the background or by pressing Esc key.\n     */\n    clickToClose: {\n      type:    Boolean,\n      default: true,\n    },\n    /**\n     * Width in pixels or percents (50, \"50px\", \"50%\").\n     *\n     * Supported string values are <number>% and <number>px\n     */\n    width: {\n      type:    [Number, String],\n      default: 600,\n      validator(value) {\n        if (typeof value === 'number') {\n          return value > 0;\n        }\n\n        if (typeof value === 'string') {\n          return /^(0*(?:[1-9][0-9]*|0)\\.?\\d*)+(px|%)$/.test(value);\n        }\n\n        return false;\n      }\n    },\n    /**\n     * List of class that will be applied to the modal window\n     */\n    customClass: {\n      type:    String,\n      default: '',\n    },\n    /**\n     * Style that will be applied to the modal window\n     */\n    styles: {\n      type:    String,\n      default: '',\n    },\n    /**\n     * Name of the modal\n     */\n    name: {\n      type:    String,\n      default: '',\n    },\n    /**\n     * trigger focus trap\n     */\n    triggerFocusTrap: {\n      type:    Boolean,\n      default: false,\n    },\n    /**\n     * forcefully set return focus element based on this selector\n     */\n    returnFocusSelector: {\n      type:    String,\n      default: '',\n    },\n    /**\n     * will return focus to the first iterable node of this container select\n     */\n    returnFocusFirstIterableNodeSelector: {\n      type:    String,\n      default: DEFAULT_ITERABLE_NODE_SELECTOR,\n    }\n  },\n  computed: {\n    modalWidth(): string {\n      if (this.isValidWidth(this.width)) {\n        const uom = typeof (this.width) === 'number' ? 'px' : '';\n\n        return `${ this.width }${ uom }`;\n      }\n\n      return '600px';\n    },\n    stylesPropToObj(): object {\n      return this.styles.split(';')\n        .map((line) => line.trim().split(':'))\n        .reduce((lines, [key, val]) => {\n          return {\n            ...lines,\n            [key]: val\n          };\n        }, { });\n    },\n    modalStyles(): object {\n      return {\n        width: this.modalWidth,\n        ...this.stylesPropToObj,\n      };\n    },\n  },\n  setup(props) {\n    if (props.triggerFocusTrap) {\n      let opts:any = DEFAULT_FOCUS_TRAP_OPTS;\n\n      // if we have a \"returnFocusFirstIterableNodeSelector\" on top of \"returnFocusSelector\"\n      // then we will use \"returnFocusFirstIterableNodeSelector\" as a fallback of \"returnFocusSelector\"\n      if (props.returnFocusFirstIterableNodeSelector && props.returnFocusFirstIterableNodeSelector !== DEFAULT_ITERABLE_NODE_SELECTOR && props.returnFocusSelector) {\n        opts = {\n          ...DEFAULT_FOCUS_TRAP_OPTS,\n          setReturnFocus: () => {\n            return document.querySelector(props.returnFocusSelector) ? props.returnFocusSelector : getFirstFocusableElement(document.querySelector(props.returnFocusFirstIterableNodeSelector));\n          }\n        };\n      // otherwise, if we are sure of permanent existance of \"returnFocusSelector\"\n      // we just return to that element\n      } else if (props.returnFocusSelector) {\n        opts = {\n          ...DEFAULT_FOCUS_TRAP_OPTS,\n          setReturnFocus: props.returnFocusSelector\n        };\n      }\n\n      useBasicSetupFocusTrap('#modal-container-element', opts);\n    }\n  },\n  mounted() {\n    document.addEventListener('keydown', this.handleEscapeKey);\n  },\n  beforeUnmount() {\n    document.removeEventListener('keydown', this.handleEscapeKey);\n  },\n  methods: {\n    handleClickOutside(event: MouseEvent) {\n      if (\n        this.clickToClose &&\n        this.$refs.modalRef &&\n        !(this.$refs.modalRef as HTMLElement).contains(event.target as Node)\n      ) {\n        this.$emit('close');\n      }\n    },\n    handleEscapeKey(event: KeyboardEvent) {\n      if (this.clickToClose && event.key === 'Escape') {\n        this.$emit('close');\n      }\n    },\n    isValidWidth(value: number | string) {\n      if (typeof value === 'number') {\n        return value > 0;\n      }\n\n      if (typeof value === 'string') {\n        return /^(0*(?:[1-9][0-9]*|0)\\.?\\d*)+(px|%)$/.test(value);\n      }\n\n      return false;\n    }\n  }\n});\n</script>\n\n<template>\n  <teleport to=\"#modals\">\n    <transition\n      name=\"modal-fade\"\n      appear\n    >\n      <div\n        class=\"modal-overlay\"\n        :data-modal=\"name\"\n        @click=\"handleClickOutside\"\n      >\n        <div\n          v-bind=\"$attrs\"\n          id=\"modal-container-element\"\n          ref=\"modalRef\"\n          :class=\"customClass\"\n          class=\"modal-container\"\n          :style=\"modalStyles\"\n          @click.stop\n        >\n          <slot><!--Empty content--></slot>\n        </div>\n      </div>\n    </transition>\n  </teleport>\n</template>\n\n<style lang=\"scss\">\n  .modal-overlay {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100vw;\n    height: 100vh;\n    background-color: var(--overlay-bg);\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    z-index: z-index('modalOverlay');\n\n    .modal-container {\n      background-color: var(--modal-bg);\n      border-radius: var(--border-radius);\n      max-height: 95vh;\n      overflow: auto;\n      border: 2px solid var(--modal-border);\n    }\n  }\n\n  .modal-fade-enter-active,\n  .modal-fade-leave-active {\n    transition: opacity 200ms;\n  }\n\n  .modal-fade-enter,\n  .modal-fade-leave-to {\n    opacity: 0;\n  }\n</style>\n"]}]}