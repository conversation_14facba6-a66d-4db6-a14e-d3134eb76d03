{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/AwsComplianceBanner.vue?vue&type=style&index=0&id=18b0d611&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/AwsComplianceBanner.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5hd3MtY29tcGxpYW5jZSB7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1lcnJvcik7CiAgICBjb2xvcjogdmFyKC0tZXJyb3ItdGV4dCk7CiAgICBsaW5lLWhlaWdodDogMmVtOwogICAgd2lkdGg6IDEwMCU7CiAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyOwoKICAgID5wewogICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICAgIH0KfQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/AwsComplianceBanner.vue"], "names": [], "mappings": ";AAmCA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEtB,CAAC,CAAC;QACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB;AACJ", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/AwsComplianceBanner.vue", "sourceRoot": "", "sourcesContent": ["<script>\n// This component is used to show warnings when current usage exceeds support purchased through AWS Marketplace\nimport { MANAGEMENT } from '@shell/config/types';\n\nexport default {\n  async fetch() {\n    if ( this.$store.getters['management/schemaFor'](MANAGEMENT.USER_NOTIFICATION)) {\n      this.notifications = await this.$store.dispatch('management/findAll', { type: MANAGEMENT.USER_NOTIFICATION });\n    }\n  },\n\n  data() {\n    return { notifications: [] };\n  },\n\n  computed: {\n    // The notification will always come from the csp-adapter component & there will be no more than one\n    // Backend will remove the notification when its no longer relevant\n    awsNotification() {\n      return this.notifications.find((notification) => notification.componentName === 'csp-adapter');\n    }\n  }\n};\n</script>\n\n<template>\n  <div\n    v-if=\"awsNotification\"\n    class=\"aws-compliance\"\n  >\n    {{ awsNotification.message }}\n  </div>\n</template>\n\n<style lang='scss' scoped>\n.aws-compliance {\n    background-color: var(--error);\n    color: var(--error-text);\n    line-height: 2em;\n    width: 100%;\n        text-align: center;\n\n    >p{\n        text-align: center;\n    }\n}\n</style>\n"]}]}