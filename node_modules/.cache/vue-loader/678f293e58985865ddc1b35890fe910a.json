{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DetailTop.vue?vue&type=style&index=0&id=0388315e&lang=scss", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DetailTop.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/css-loader/dist/cjs.js", "mtime": 1753522223631}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/stylePostLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/postcss-loader/dist/cjs.js", "mtime": 1753522227088}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/sass-loader/dist/cjs.js", "mtime": 1753522223011}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgLmRldGFpbC10b3AgewogICAgJHNwYWNpbmc6IDRweDsKCiAgICAmOm5vdCguZW1wdHkpIHsKICAgICAgLy8gRmxpcCBvZiAubWFzdGhlYWQgcGFkZGluZy9tYXJnaW4KICAgICAgcGFkZGluZy10b3A6IDEwcHg7CiAgICAgIGJvcmRlci10b3A6IDFweCBzb2xpZCB2YXIoLS1ib3JkZXIpOwogICAgICBtYXJnaW4tdG9wOiAxMHB4OwogICAgfQoKICAgIC5uYW1lc3BhY2VMaW5rTGlzdDpub3QoOmZpcnN0LWNoaWxkKTpiZWZvcmUgewogICAgICBjb250ZW50OiAiLCAiOwogICAgfQoKICAgIC50YWdzIHsKICAgICAgZGlzcGxheTogaW5saW5lLWZsZXg7CiAgICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7CiAgICAgIGZsZXgtd3JhcDogd3JhcDsKICAgICAgcG9zaXRpb246IHJlbGF0aXZlOwogICAgICB0b3A6ICRzcGFjaW5nICogbWF0aC5kaXYoLTEsIDIpOwoKICAgICAgLmxhYmVsIHsKICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgICAgICAgdG9wOiAkc3BhY2luZzsKICAgICAgfQoKICAgICAgLnRhZyB7CiAgICAgICAgbWFyZ2luOiBtYXRoLmRpdigkc3BhY2luZywgMikgJHNwYWNpbmcgMCBtYXRoLmRpdigkc3BhY2luZywgMik7CiAgICAgICAgZm9udC1zaXplOiAxMnB4OwogICAgICB9CiAgICB9CgogICAgLmFubm90YXRpb24gewogICAgICBtYXJnaW4tdG9wOiAxMHB4OwogICAgfQoKICAgIC5sYWJlbCB7CiAgICAgIGNvbG9yOiB2YXIoLS1pbnB1dC1sYWJlbCk7CiAgICAgIG1hcmdpbjogMCA0cHggMCAwOwogICAgfQoKICAgICZfX2xhYmVsLWJ1dHRvbiB7CiAgICAgIHBhZGRpbmc6IDRweDsKICAgIH0KCiAgICAuZGV0YWlscyB7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7CiAgICAgIGZsZXgtd3JhcDogd3JhcDsKCiAgICAgIC5kZXRhaWwgewogICAgICAgIG1hcmdpbi1yaWdodDogMjBweDsKICAgICAgICBtYXJnaW4tYm90dG9tOiAzcHg7CiAgICAgIH0KICAgICAgJjpub3QoOmZpcnN0LW9mLXR5cGUpIHsKICAgICAgICBtYXJnaW4tdG9wOiAzcHg7CiAgICAgIH0KICAgIH0KCiAgICAmID4gZGl2IHsKICAgICAgJjpub3QoOmxhc3Qtb2YtdHlwZSkgewogICAgICAgIG1hcmdpbi1ib3R0b206ICRzcGFjaW5nOwogICAgICB9CiAgICB9CgogICAgLmljb24gewogICAgICB2ZXJ0aWNhbC1hbGlnbjogdG9wOwogICAgfQogIH0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DetailTop.vue"], "names": [], "mappings": ";EAgTE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;IACf;;IAEA,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE/B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf;;MAEA,CAAC,CAAC,CAAC,EAAE;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IACnB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACpB;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACjB;IACF;;IAEA,EAAE,EAAE,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACrB;EACF", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DetailTop.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport Tag from '@shell/components/Tag';\nimport isEmpty from 'lodash/isEmpty';\nimport DetailText from '@shell/components/DetailText';\nimport { _VIEW } from '@shell/config/query-params';\nimport { ExtensionPoint, PanelLocation } from '@shell/core/types';\nimport ExtensionPanel from '@shell/components/ExtensionPanel';\n\nexport default {\n  components: {\n    DetailText, Tag, ExtensionPanel\n  },\n\n  props: {\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n\n    moreDetails: {\n      type:    Array,\n      default: () => {\n        return [];\n      }\n    },\n\n    /**\n     * Optionally replace key/value and display tooltips for the tab\n     * Dictionary key based\n     */\n    tooltips: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n\n    /**\n     * Optionally display icons next to the tab\n     * Dictionary key based\n     */\n    icons: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  data() {\n    return {\n      extensionType:      ExtensionPoint.PANEL,\n      extensionLocation:  PanelLocation.DETAIL_TOP,\n      annotationsVisible: false,\n      showAllLabels:      false,\n      view:               _VIEW\n    };\n  },\n\n  computed: {\n    namespaces() {\n      return (this.value?.namespaces || []).map((namespace) => {\n        return {\n          name:           namespace?.metadata?.name,\n          detailLocation: namespace.detailLocation\n        };\n      });\n    },\n    details() {\n      const items = [\n        ...(this.moreDetails || []),\n        ...(this.value?.details || []),\n      ].filter((x) => x.separator || (!!`${ x.content }` && x.content !== undefined && x.content !== null));\n\n      const groups = [];\n      let currentGroup = [];\n\n      items.forEach((i) => {\n        if (i.separator) {\n          groups.push(currentGroup);\n          currentGroup = [];\n        } else {\n          currentGroup.push(i);\n        }\n      });\n\n      if (currentGroup.length) {\n        groups.push(currentGroup);\n      }\n\n      return groups;\n    },\n\n    labels() {\n      if (this.showAllLabels || !this.showFilteredSystemLabels) {\n        return this.value?.labels || {};\n      }\n\n      return this.value?.filteredSystemLabels;\n    },\n\n    internalTooltips() {\n      return this.value?.detailTopTooltips || this.tooltips;\n    },\n\n    internalIcons() {\n      return this.value?.detailTopIcons || this.icons;\n    },\n\n    annotations() {\n      return this.value?.annotations || {};\n    },\n\n    description() {\n      return this.value?.description;\n    },\n\n    hasDetails() {\n      return !isEmpty(this.details);\n    },\n\n    hasLabels() {\n      return !isEmpty(this.labels);\n    },\n\n    hasAnnotations() {\n      return !isEmpty(this.annotations);\n    },\n\n    hasDescription() {\n      return !isEmpty(this.description);\n    },\n\n    hasNamespaces() {\n      return !isEmpty(this.namespaces);\n    },\n\n    annotationCount() {\n      return Object.keys(this.annotations || {}).length;\n    },\n\n    isEmpty() {\n      const hasAnything = this.hasDetails || this.hasLabels || this.hasAnnotations || this.hasDescription || this.hasNamespaces;\n\n      return !hasAnything;\n    },\n\n    showFilteredSystemLabels() {\n      // It would be nicer to use hasSystemLabels here, but not all places have implemented it\n      // Instead check that there's a discrepancy between all labels and all labels without system ones\n      if (this.value?.labels && this.value?.filteredSystemLabels) {\n        const labelCount = Object.keys(this.value.labels).length;\n        const filteredSystemLabelsCount = Object.keys(this.value.filteredSystemLabels).length;\n\n        return labelCount !== filteredSystemLabelsCount;\n      }\n\n      return false;\n    },\n  },\n  methods: {\n    toggleLabels() {\n      this.showAllLabels = !this.showAllLabels;\n    },\n\n    toggleAnnotations(ev) {\n      this.annotationsVisible = !this.annotationsVisible;\n    }\n  }\n};\n</script>\n\n<template>\n  <div\n    class=\"detail-top\"\n    :class=\"{empty: isEmpty}\"\n  >\n    <div\n      v-if=\"hasNamespaces\"\n      class=\"labels\"\n    >\n      <span class=\"label\">\n        {{ t('resourceDetail.detailTop.namespaces') }}:\n      </span>\n      <span>\n        <router-link\n          v-for=\"namespace in namespaces\"\n          :key=\"namespace.name\"\n          :to=\"namespace.detailLocation\"\n          class=\"namespaceLinkList\"\n        >\n          {{ namespace.name }}\n        </router-link>\n      </span>\n    </div>\n\n    <div\n      v-if=\"description\"\n      class=\"description\"\n    >\n      <span class=\"label\">\n        {{ t('resourceDetail.detailTop.description') }}:\n      </span>\n      <span class=\"content\">{{ description }}</span>\n    </div>\n\n    <div v-if=\"hasDetails\">\n      <div\n        v-for=\"group, index in details\"\n        :key=\"index\"\n        class=\"details\"\n      >\n        <div\n          v-for=\"(detail, i) in group\"\n          :key=\"i\"\n          class=\"detail\"\n        >\n          <span class=\"label\">\n            {{ detail.label }}:\n          </span>\n          <component\n            :is=\"detail.formatter\"\n            v-if=\"detail.formatter\"\n            :value=\"detail.content\"\n            v-bind=\"detail.formatterOpts\"\n          />\n          <span v-else>{{ detail.content }}</span>\n        </div>\n      </div>\n    </div>\n\n    <div\n      v-if=\"hasLabels\"\n      class=\"labels\"\n    >\n      <div class=\"tags\">\n        <span class=\"label\">\n          {{ t('resourceDetail.detailTop.labels') }}:\n        </span>\n        <Tag\n          v-for=\"(prop, key) in labels\"\n          :key=\"key\"\n        >\n          <i\n            v-if=\"internalIcons[key]\"\n            class=\"icon\"\n            :class=\"internalIcons[key]\"\n          />\n          <span\n            v-if=\"internalTooltips[key]\"\n            v-clean-tooltip=\"prop ? `${key} : ${prop}` : key\"\n          >\n            <span>{{ internalTooltips[key] ? internalTooltips[key] : key }}</span>\n            <span v-if=\"showAllLabels\">: {{ key }}</span>\n          </span>\n          <span v-else>{{ prop ? `${key} : ${prop}` : key }}</span>\n        </Tag>\n        <a\n          v-if=\"showFilteredSystemLabels\"\n          href=\"#\"\n          class=\"detail-top__label-button\"\n          @click.prevent=\"toggleLabels\"\n        >\n          {{ t(`resourceDetail.detailTop.${showAllLabels? 'hideLabels' : 'showLabels'}`) }}\n        </a>\n      </div>\n    </div>\n\n    <div\n      v-if=\"hasAnnotations\"\n      class=\"annotations\"\n    >\n      <span class=\"label\">\n        {{ t('resourceDetail.detailTop.annotations') }}:\n      </span>\n      <a\n        href=\"#\"\n        @click.prevent=\"toggleAnnotations\"\n      >\n        {{ t(`resourceDetail.detailTop.${annotationsVisible? 'hideAnnotations' : 'showAnnotations'}`, {annotations: annotationCount}) }}\n      </a>\n      <div v-if=\"annotationsVisible\">\n        <DetailText\n          v-for=\"(val, key) in annotations\"\n          :key=\"key\"\n          class=\"annotation\"\n          :value=\"val\"\n          :label=\"key\"\n        />\n      </div>\n    </div>\n\n    <!-- Extensions area -->\n    <ExtensionPanel\n      :resource=\"value\"\n      :type=\"extensionType\"\n      :location=\"extensionLocation\"\n    />\n  </div>\n</template>\n\n<style lang=\"scss\">\n  .detail-top {\n    $spacing: 4px;\n\n    &:not(.empty) {\n      // Flip of .masthead padding/margin\n      padding-top: 10px;\n      border-top: 1px solid var(--border);\n      margin-top: 10px;\n    }\n\n    .namespaceLinkList:not(:first-child):before {\n      content: \", \";\n    }\n\n    .tags {\n      display: inline-flex;\n      flex-direction: row;\n      flex-wrap: wrap;\n      position: relative;\n      top: $spacing * math.div(-1, 2);\n\n      .label {\n        position: relative;\n        top: $spacing;\n      }\n\n      .tag {\n        margin: math.div($spacing, 2) $spacing 0 math.div($spacing, 2);\n        font-size: 12px;\n      }\n    }\n\n    .annotation {\n      margin-top: 10px;\n    }\n\n    .label {\n      color: var(--input-label);\n      margin: 0 4px 0 0;\n    }\n\n    &__label-button {\n      padding: 4px;\n    }\n\n    .details {\n      display: flex;\n      flex-direction: row;\n      flex-wrap: wrap;\n\n      .detail {\n        margin-right: 20px;\n        margin-bottom: 3px;\n      }\n      &:not(:first-of-type) {\n        margin-top: 3px;\n      }\n    }\n\n    & > div {\n      &:not(:last-of-type) {\n        margin-bottom: $spacing;\n      }\n    }\n\n    .icon {\n      vertical-align: top;\n    }\n  }\n</style>\n"]}]}