{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceList/ResourceLoadingIndicator.vue?vue&type=template&id=351c01f1&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceList/ResourceLoadingIndicator.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdgogICAgdi1pZj0iY291bnQgJiYgIWhhdmVBbGwiCiAgICBjbGFzcz0ibWwtMTAgcmVzb3VyY2UtbG9hZGluZy1pbmRpY2F0b3IiCiAgPgogICAgPGRpdiBjbGFzcz0iaW5uZXIiPgogICAgICA8ZGl2IGNsYXNzPSJyZXNvdXJjZS1sb2FkZXIiPgogICAgICAgIDxkaXYgY2xhc3M9InJsLWJnIj4KICAgICAgICAgIDxpIGNsYXNzPSJpY29uIGljb24tc3Bpbm5lciBpY29uLXNwaW4iIC8+PHNwYW4+e3sgdCggJ3Jlc291cmNlTG9hZGluZ0luZGljYXRvci5sb2FkaW5nJyApIH19IDxzcGFuIHYtaWY9IiFpbmRldGVybWluYXRlIj57eyBjb3VudCB9fSAvIHt7IHRvdGFsIH19PC9zcGFuPjwvc3Bhbj4KICAgICAgICA8L2Rpdj4KICAgICAgPC9kaXY+CiAgICAgIDxkaXYKICAgICAgICBjbGFzcz0icmVzb3VyY2UtbG9hZGVyIgogICAgICAgIDpzdHlsZT0ie3dpZHRofSIKICAgICAgPgogICAgICAgIDxkaXYgY2xhc3M9InJsLWZnIj4KICAgICAgICAgIDxpIGNsYXNzPSJpY29uIGljb24tc3Bpbm5lciBpY29uLXNwaW4iIC8+PHNwYW4+e3sgdCggJ3Jlc291cmNlTG9hZGluZ0luZGljYXRvci5sb2FkaW5nJyApIH19IDxzcGFuIHYtaWY9IiFpbmRldGVybWluYXRlIj57eyBjb3VudCB9fSAvIHt7IHRvdGFsIH19PC9zcGFuPjwvc3Bhbj4KICAgICAgICA8L2Rpdj4KICAgICAgPC9kaXY+CiAgICA8L2Rpdj4KICA8L2Rpdj4K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceList/ResourceLoadingIndicator.vue"], "names": [], "mappings": ";EA6EE,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzC;IACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjK,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB;QACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjK,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceList/ResourceLoadingIndicator.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { COUNT } from '@shell/config/types';\n\n/**\n * Loading Indicator for resources - used when we are loading resources incrementally, by page\n */\nexport default {\n\n  name: 'ResourceLoadingIndicator',\n\n  props: {\n    resources: {\n      type:     Array,\n      required: true,\n    },\n    indeterminate: {\n      type:    Boolean,\n      default: false,\n    },\n  },\n\n  data() {\n    const inStore = this.$store.getters['currentStore'](this.resource);\n\n    return { inStore };\n  },\n\n  computed: {\n    // Count of rows - either from the data provided or from the rows for the first resource\n    rowsCount() {\n      if (this.resources.length > 0) {\n        const existingData = this.$store.getters[`${ this.inStore }/all`](this.resources[0]) || [];\n\n        return (existingData || []).length;\n      }\n\n      return 0;\n    },\n\n    // Have we loaded all resources for the types that are needed\n    haveAll() {\n      return this.resources.reduce((acc, r) => {\n        return acc && this.$store.getters[`${ this.inStore }/haveAll`](r);\n      }, true);\n    },\n\n    // Total of all counts of all resources for all of the resources being loaded\n    total() {\n      const clusterCounts = this.$store.getters[`${ this.inStore }/all`](COUNT);\n\n      return this.resources.reduce((acc, r) => {\n        const resourceCounts = clusterCounts?.[0]?.counts?.[r];\n        const resourceCount = resourceCounts?.summary?.count;\n        const count = resourceCount || 0;\n\n        return acc + count;\n      }, 0);\n    },\n\n    // Total count of all of the resources for all of the resources being loaded\n    count() {\n      return this.resources.reduce((acc, r) => {\n        return acc + (this.$store.getters[`${ this.inStore }/all`](r) || []).length;\n      }, 0);\n    },\n\n    // Width style to enable the progress bar style presentation\n    width() {\n      const progress = Math.ceil(100 * (this.count / this.total));\n\n      return `${ progress }%`;\n    }\n  },\n};\n</script>\n\n<template>\n  <div\n    v-if=\"count && !haveAll\"\n    class=\"ml-10 resource-loading-indicator\"\n  >\n    <div class=\"inner\">\n      <div class=\"resource-loader\">\n        <div class=\"rl-bg\">\n          <i class=\"icon icon-spinner icon-spin\" /><span>{{ t( 'resourceLoadingIndicator.loading' ) }} <span v-if=\"!indeterminate\">{{ count }} / {{ total }}</span></span>\n        </div>\n      </div>\n      <div\n        class=\"resource-loader\"\n        :style=\"{width}\"\n      >\n        <div class=\"rl-fg\">\n          <i class=\"icon icon-spinner icon-spin\" /><span>{{ t( 'resourceLoadingIndicator.loading' ) }} <span v-if=\"!indeterminate\">{{ count }} / {{ total }}</span></span>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .resource-loading-indicator {\n    border: 1px solid var(--link);\n    border-radius: 10px;\n    position: relative;\n    width: min-content;\n    overflow: hidden;\n\n    .resource-loader:last-child {\n      position: absolute;\n      top: 0;\n\n      background-color: var(--link);\n      color: var(--link-text);\n      overflow: hidden;\n      white-space: nowrap;\n    }\n\n    .resource-loader {\n      padding: 1px 10px;\n      width: max-content;\n\n      .rl-fg, .rl-bg {\n        align-content: center;\n        display: flex;\n\n        > i {\n          font-size: 18px;\n          line-height: 18px;\n        }\n\n        > span {\n          margin-left: 5px;\n        }\n      }\n    }\n  }\n</style>\n"]}]}