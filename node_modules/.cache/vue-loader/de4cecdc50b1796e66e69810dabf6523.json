{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/MoveModal.vue?vue&type=template&id=41b4bd2e", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/MoveModal.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxhcHAtbW9kYWwNCiAgICB2LWlmPSJzaG93TW9kYWwiDQogICAgY2xhc3M9Im1vdmUtbW9kYWwiDQogICAgOm5hbWU9Im1vZGFsTmFtZSINCiAgICA6d2lkdGg9IjQ0MCINCiAgICBoZWlnaHQ9ImF1dG8iDQogICAgQGNsb3NlPSJjbG9zZSINCiAgPg0KICAgIDxMb2FkaW5nIHYtaWY9IiRmZXRjaFN0YXRlLnBlbmRpbmciIC8+DQogICAgPENhcmQNCiAgICAgIHYtZWxzZQ0KICAgICAgY2xhc3M9Im1vdmUtbW9kYWwtY2FyZCINCiAgICAgIDpzaG93LWhpZ2hsaWdodC1ib3JkZXI9ImZhbHNlIg0KICAgID4NCiAgICAgIDx0ZW1wbGF0ZSAjdGl0bGU+DQogICAgICAgIDxoNCBjbGFzcz0idGV4dC1kZWZhdWx0LXRleHQiPg0KICAgICAgICAgIHt7IHQoJ21vdmVNb2RhbC50aXRsZScpIH19DQogICAgICAgIDwvaDQ+DQogICAgICA8L3RlbXBsYXRlPg0KICAgICAgPHRlbXBsYXRlICNib2R5Pg0KICAgICAgICA8ZGl2Pg0KICAgICAgICAgIHt7IHQoJ21vdmVNb2RhbC5kZXNjcmlwdGlvbicpIH19DQogICAgICAgICAgPHVsIGNsYXNzPSJuYW1lc3BhY2VzIj4NCiAgICAgICAgICAgIDxsaQ0KICAgICAgICAgICAgICB2LWZvcj0iKG5hbWVzcGFjZSwgaSkgaW4gdG9Nb3ZlIg0KICAgICAgICAgICAgICA6a2V5PSJpIg0KICAgICAgICAgICAgPg0KICAgICAgICAgICAgICB7eyBuYW1lc3BhY2UubmFtZURpc3BsYXkgfX0NCiAgICAgICAgICAgIDwvbGk+DQogICAgICAgICAgPC91bD4NCiAgICAgICAgPC9kaXY+DQogICAgICAgIDxMYWJlbGVkU2VsZWN0DQogICAgICAgICAgdi1tb2RlbDp2YWx1ZT0idGFyZ2V0UHJvamVjdCINCiAgICAgICAgICA6b3B0aW9ucz0icHJvamVjdE9wdGlvbnMiDQogICAgICAgICAgOmxhYmVsPSJ0KCdtb3ZlTW9kYWwudGFyZ2V0UHJvamVjdCcpIg0KICAgICAgICAvPg0KICAgICAgPC90ZW1wbGF0ZT4NCiAgICAgIDx0ZW1wbGF0ZSAjYWN0aW9ucz4NCiAgICAgICAgPGJ1dHRvbg0KICAgICAgICAgIGNsYXNzPSJidG4gcm9sZS1zZWNvbmRhcnkiDQogICAgICAgICAgQGNsaWNrPSJjbG9zZSINCiAgICAgICAgPg0KICAgICAgICAgIHt7IHQoJ2dlbmVyaWMuY2FuY2VsJykgfX0NCiAgICAgICAgPC9idXR0b24+DQogICAgICAgIDxBc3luY0J1dHRvbg0KICAgICAgICAgIDphY3Rpb24tbGFiZWw9InQoJ21vdmVNb2RhbC5tb3ZlQnV0dG9uTGFiZWwnKSINCiAgICAgICAgICBjbGFzcz0iYnRuIGJnLXByaW1hcnkgbWwtMTAiDQogICAgICAgICAgOmRpc2FibGVkPSIhdGFyZ2V0UHJvamVjdCINCiAgICAgICAgICBAY2xpY2s9Im1vdmUiDQogICAgICAgIC8+DQogICAgICA8L3RlbXBsYXRlPg0KICAgIDwvQ2FyZD4NCiAgPC9hcHAtbW9kYWw+DQo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/MoveModal.vue"], "names": [], "mappings": ";EAyFE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf;IACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACrC,CAAC,CAAC,CAAC,CAAC;MACF,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B;MACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC;UACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAC/B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC;cACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACT;cACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC5B,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf;UACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/MoveModal.vue", "sourceRoot": "", "sourcesContent": ["<script>\r\nimport { mapState, mapGetters } from 'vuex';\r\nimport { Card } from '@components/Card';\r\nimport AsyncButton from '@shell/components/AsyncButton';\r\nimport AppModal from '@shell/components/AppModal.vue';\r\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\r\nimport { MANAGEMENT } from '@shell/config/types';\r\nimport Loading from '@shell/components/Loading';\r\nimport { PROJECT } from '@shell/config/labels-annotations';\r\n\r\nexport default {\r\n  emits: ['moving'],\r\n\r\n  components: {\r\n    AsyncButton, Card, LabeledSelect, Loading, AppModal\r\n  },\r\n\r\n  async fetch() {\r\n    this.projects = await this.$store.dispatch('management/findAll', { type: MANAGEMENT.PROJECT });\r\n  },\r\n\r\n  data() {\r\n    return {\r\n      modalName: 'move-modal', projects: [], targetProject: null, showModal: false\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    ...mapState('action-menu', ['showPromptMove', 'toMove']),\r\n    ...mapGetters(['currentCluster']),\r\n\r\n    excludedProjects() {\r\n      return this.toMove.filter((namespace) => !!namespace.project).map((namespace) => namespace.project.shortId);\r\n    },\r\n\r\n    projectOptions() {\r\n      return this.projects.reduce((inCluster, project) => {\r\n        if (!this.excludedProjects.includes(project.shortId) && project.spec?.clusterName === this.currentCluster.id) {\r\n          inCluster.push({\r\n            value: project.shortId,\r\n            label: project.nameDisplay\r\n          });\r\n        }\r\n\r\n        return inCluster;\r\n      }, []);\r\n    }\r\n  },\r\n\r\n  watch: {\r\n    showPromptMove(show) {\r\n      if (show) {\r\n        this.showModal = true;\r\n      } else {\r\n        this.showModal = false;\r\n      }\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    close() {\r\n      this.$store.commit('action-menu/togglePromptMove');\r\n    },\r\n\r\n    async move(finish) {\r\n      const cluster = this.$store.getters['currentCluster'];\r\n      const clusterWithProjectId = `${ cluster.id }:${ this.targetProject }`;\r\n\r\n      const promises = this.toMove.map((namespace) => {\r\n        namespace.setLabel(PROJECT, this.targetProject);\r\n        namespace.setAnnotation(PROJECT, clusterWithProjectId);\r\n\r\n        return namespace.save();\r\n      });\r\n\r\n      try {\r\n        this.$emit('moving');\r\n        await Promise.all(promises);\r\n        finish(true);\r\n        this.targetProject = null;\r\n        this.close();\r\n      } catch (ex) {\r\n        finish(false);\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n<template>\r\n  <app-modal\r\n    v-if=\"showModal\"\r\n    class=\"move-modal\"\r\n    :name=\"modalName\"\r\n    :width=\"440\"\r\n    height=\"auto\"\r\n    @close=\"close\"\r\n  >\r\n    <Loading v-if=\"$fetchState.pending\" />\r\n    <Card\r\n      v-else\r\n      class=\"move-modal-card\"\r\n      :show-highlight-border=\"false\"\r\n    >\r\n      <template #title>\r\n        <h4 class=\"text-default-text\">\r\n          {{ t('moveModal.title') }}\r\n        </h4>\r\n      </template>\r\n      <template #body>\r\n        <div>\r\n          {{ t('moveModal.description') }}\r\n          <ul class=\"namespaces\">\r\n            <li\r\n              v-for=\"(namespace, i) in toMove\"\r\n              :key=\"i\"\r\n            >\r\n              {{ namespace.nameDisplay }}\r\n            </li>\r\n          </ul>\r\n        </div>\r\n        <LabeledSelect\r\n          v-model:value=\"targetProject\"\r\n          :options=\"projectOptions\"\r\n          :label=\"t('moveModal.targetProject')\"\r\n        />\r\n      </template>\r\n      <template #actions>\r\n        <button\r\n          class=\"btn role-secondary\"\r\n          @click=\"close\"\r\n        >\r\n          {{ t('generic.cancel') }}\r\n        </button>\r\n        <AsyncButton\r\n          :action-label=\"t('moveModal.moveButtonLabel')\"\r\n          class=\"btn bg-primary ml-10\"\r\n          :disabled=\"!targetProject\"\r\n          @click=\"move\"\r\n        />\r\n      </template>\r\n    </Card>\r\n  </app-modal>\r\n</template>\r\n\r\n<style lang='scss'>\r\n  .move-modal {\r\n    .namespaces {\r\n      max-height: 200px;\r\n      overflow-y: scroll;\r\n    }\r\n\r\n    .move-modal-card {\r\n        box-shadow: none;\r\n\r\n        border-radius: var(--border-radius);\r\n    }\r\n\r\n    .actions {\r\n      text-align: right;\r\n    }\r\n    .card-actions {\r\n      display: flex;\r\n      justify-content: center;\r\n    }\r\n  }\r\n</style>\r\n"]}]}