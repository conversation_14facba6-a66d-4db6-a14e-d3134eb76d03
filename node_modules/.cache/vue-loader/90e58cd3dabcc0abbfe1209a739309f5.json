{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Networking.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Networking.vue", "mtime": 1754992537319}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCB7IF9DUkVBVEUsIF9FRElULCBfVklFVyB9IGZyb20gJ0BzaGVsbC9jb25maWcvcXVlcnktcGFyYW1zJzsKaW1wb3J0IHsgUHJvcFR5cGUsIGRlZmluZUNvbXBvbmVudCB9IGZyb20gJ3Z1ZSc7CmltcG9ydCB7IFN0b3JlLCBtYXBHZXR0ZXJzIH0gZnJvbSAndnVleCc7CmltcG9ydCBMYWJlbGVkU2VsZWN0IGZyb20gJ0BzaGVsbC9jb21wb25lbnRzL2Zvcm0vTGFiZWxlZFNlbGVjdC52dWUnOwppbXBvcnQgQ2hlY2tib3ggZnJvbSAnQGNvbXBvbmVudHMvRm9ybS9DaGVja2JveC9DaGVja2JveC52dWUnOwppbXBvcnQgQXJyYXlMaXN0IGZyb20gJ0BzaGVsbC9jb21wb25lbnRzL2Zvcm0vQXJyYXlMaXN0LnZ1ZSc7CmltcG9ydCBCYW5uZXIgZnJvbSAnQGNvbXBvbmVudHMvQmFubmVyL0Jhbm5lci52dWUnOwoKaW1wb3J0IFJhZGlvR3JvdXAgZnJvbSAnQGNvbXBvbmVudHMvRm9ybS9SYWRpby9SYWRpb0dyb3VwLnZ1ZSc7CgppbXBvcnQgeyBBV1MgfSBmcm9tICcuLi8uLi90eXBlcyc7CgpleHBvcnQgZGVmYXVsdCBkZWZpbmVDb21wb25lbnQoewogIG5hbWU6ICdFS1NOZXR3b3JraW5nJywKCiAgZW1pdHM6IFsndXBkYXRlOnN1Ym5ldHMnLCAndXBkYXRlOnNlY3VyaXR5R3JvdXBzJywgJ2Vycm9yJywgJ3VwZGF0ZTpwdWJsaWNBY2Nlc3MnLCAndXBkYXRlOnByaXZhdGVBY2Nlc3MnLCAndXBkYXRlOnB1YmxpY0FjY2Vzc1NvdXJjZXMnXSwKCiAgY29tcG9uZW50czogewogICAgTGFiZWxlZFNlbGVjdCwKICAgIEFycmF5TGlzdCwKICAgIENoZWNrYm94LAogICAgUmFkaW9Hcm91cCwKICAgIEJhbm5lcgogIH0sCgogIHByb3BzOiB7CiAgICBtb2RlOiB7CiAgICAgIHR5cGU6ICAgIFN0cmluZywKICAgICAgZGVmYXVsdDogX0VESVQKICAgIH0sCgogICAgcmVnaW9uOiB7CiAgICAgIHR5cGU6ICAgIFN0cmluZywKICAgICAgZGVmYXVsdDogJycKICAgIH0sCgogICAgYW1hem9uQ3JlZGVudGlhbFNlY3JldDogewogICAgICB0eXBlOiAgICBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICcnCiAgICB9LAoKICAgIHN1Ym5ldHM6IHsKICAgICAgdHlwZTogICAgQXJyYXkgYXMgUHJvcFR5cGU8c3RyaW5nW10+LAogICAgICBkZWZhdWx0OiAoKSA9PiBbXQogICAgfSwKCiAgICBzZWN1cml0eUdyb3VwczogewogICAgICB0eXBlOiAgICBBcnJheSBhcyBQcm9wVHlwZTxzdHJpbmdbXT4sCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdCiAgICB9LAoKICAgIHB1YmxpY0FjY2VzczogewogICAgICB0eXBlOiAgICBCb29sZWFuLAogICAgICBkZWZhdWx0OiBmYWxzZQogICAgfSwKCiAgICBwcml2YXRlQWNjZXNzOiB7CiAgICAgIHR5cGU6ICAgIEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlCiAgICB9LAoKICAgIHB1YmxpY0FjY2Vzc1NvdXJjZXM6IHsKICAgICAgdHlwZTogICAgQXJyYXksCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdCiAgICB9LAoKICAgIHN0YXR1c1N1Ym5ldHM6IHsKICAgICAgdHlwZTogICAgQXJyYXkgYXMgUHJvcFR5cGU8c3RyaW5nW10+LAogICAgICBkZWZhdWx0OiAoKSA9PiBbXQogICAgfSwKCiAgICBydWxlczogewogICAgICB0eXBlOiAgICBPYmplY3QsCiAgICAgIGRlZmF1bHQ6ICgpID0+IHt9CiAgICB9CiAgfSwKCiAgd2F0Y2g6IHsKICAgIGFtYXpvbkNyZWRlbnRpYWxTZWNyZXQ6IHsKICAgICAgaGFuZGxlcihuZXUpIHsKICAgICAgICBpZiAobmV1ICYmICF0aGlzLmlzVmlldykgewogICAgICAgICAgdGhpcy5mZXRjaFZwY3MoKTsKICAgICAgICAgIHRoaXMuZmV0Y2hTZWN1cml0eUdyb3VwcygpOwogICAgICAgIH0KICAgICAgfSwKICAgICAgaW1tZWRpYXRlOiB0cnVlCiAgICB9LAogICAgcmVnaW9uOiB7CiAgICAgIGhhbmRsZXIobmV1ICkgewogICAgICAgIGlmIChuZXUgJiYgIXRoaXMuaXNWaWV3KSB7CiAgICAgICAgICB0aGlzLmZldGNoVnBjcygpOwogICAgICAgICAgdGhpcy5mZXRjaFNlY3VyaXR5R3JvdXBzKCk7CiAgICAgICAgfQogICAgICB9LAogICAgICBpbW1lZGlhdGU6IHRydWUKICAgIH0sCgogICAgJ2Nob29zZVN1Ym5ldCcobmV1OiBib29sZWFuKSB7CiAgICAgIGlmICghbmV1KSB7CiAgICAgICAgdGhpcy4kZW1pdCgndXBkYXRlOnN1Ym5ldHMnLCBbXSk7CiAgICAgIH0KICAgIH0sCgogICAgc2VsZWN0ZWRWcGMobmV1OiBzdHJpbmcsIG9sZDogc3RyaW5nKSB7CiAgICAgIGlmICghIW9sZCkgewogICAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZTpzZWN1cml0eUdyb3VwcycsIFtdKTsKICAgICAgfQogICAgfQoKICB9LAoKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbG9hZGluZ1ZwY3M6ICAgICAgICAgICBmYWxzZSwKICAgICAgbG9hZGluZ1NlY3VyaXR5R3JvdXBzOiBmYWxzZSwKICAgICAgdnBjSW5mbzogICAgICAgICAgICAgICBbXSBhcyBBV1MuVlBDW10sCiAgICAgIHN1Ym5ldEluZm86ICAgICAgICAgICAgW10gYXMgQVdTLlN1Ym5ldFtdLAogICAgICBzZWN1cml0eUdyb3VwSW5mbzogICAgIHt9IGFzIHtTZWN1cml0eUdyb3VwczogQVdTLlNlY3VyaXR5R3JvdXBbXX0sCiAgICAgIGNob29zZVN1Ym5ldDogICAgICAgICAgISF0aGlzLnN1Ym5ldHMgJiYgISF0aGlzLnN1Ym5ldHMubGVuZ3RoCiAgICB9OwogIH0sCgogIGNvbXB1dGVkOiB7CiAgICAuLi5tYXBHZXR0ZXJzKHsgdDogJ2kxOG4vdCcgfSksCiAgICAvLyBtYXAgc3VibmV0cyB0byBWUENzCiAgICAvLyB7W3ZwYyBpZF06IFtzdWJuZXRzXX0KICAgIHZwY09wdGlvbnMoKSB7CiAgICAgIGNvbnN0IG91dDoge2tleTpzdHJpbmcsIGxhYmVsOnN0cmluZywgX2lzU3VibmV0Pzpib29sZWFuLCBraW5kPzpzdHJpbmd9W10gPSBbXTsKICAgICAgY29uc3QgdnBjczogQVdTLlZQQ1tdID0gdGhpcy52cGNJbmZvIHx8IFtdOwogICAgICBjb25zdCBzdWJuZXRzOiBBV1MuU3VibmV0W10gPSB0aGlzLnN1Ym5ldEluZm8gfHwgW107CiAgICAgIGNvbnN0IG1hcHBlZFN1Ym5ldHM6IHtba2V5OnN0cmluZ106IEFXUy5TdWJuZXRbXX0gPSB7fTsKCiAgICAgIHN1Ym5ldHMuZm9yRWFjaCgocykgPT4gewogICAgICAgIGlmICghbWFwcGVkU3VibmV0c1tzLlZwY0lkXSkgewogICAgICAgICAgbWFwcGVkU3VibmV0c1tzLlZwY0lkXSA9IFtzXTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgbWFwcGVkU3VibmV0c1tzLlZwY0lkXS5wdXNoKHMpOwogICAgICAgIH0KICAgICAgfSk7CiAgICAgIHZwY3MuZm9yRWFjaCgodikgPT4gewogICAgICAgIGNvbnN0IHsgVnBjSWQgPSAnJywgVGFncyA9IFtdIH0gPSB2OwogICAgICAgIGNvbnN0IG5hbWVUYWcgPSBUYWdzLmZpbmQoKHQpID0+IHsKICAgICAgICAgIHJldHVybiB0LktleSA9PT0gJ05hbWUnOwogICAgICAgIH0pPy5WYWx1ZTsKCiAgICAgICAgY29uc3QgZm9ybU9wdGlvbiA9IHsKICAgICAgICAgIGtleTogVnBjSWQsIGxhYmVsOiBgJHsgbmFtZVRhZyB9ICgkeyBWcGNJZCB9KWAsIGtpbmQ6ICdncm91cCcKICAgICAgICB9OwoKICAgICAgICBvdXQucHVzaChmb3JtT3B0aW9uKTsKICAgICAgICBpZiAobWFwcGVkU3VibmV0c1tWcGNJZF0pIHsKICAgICAgICAgIG1hcHBlZFN1Ym5ldHNbVnBjSWRdLmZvckVhY2goKHMpID0+IHsKICAgICAgICAgICAgY29uc3QgeyBTdWJuZXRJZCwgVGFncyA9IFtdIH0gPSBzOwogICAgICAgICAgICBjb25zdCBuYW1lVGFnID0gVGFncy5maW5kKCh0KSA9PiB7CiAgICAgICAgICAgICAgcmV0dXJuIHQuS2V5ID09PSAnTmFtZSc7CiAgICAgICAgICAgIH0pPy5WYWx1ZTsKCiAgICAgICAgICAgIGNvbnN0IHN1Ym5ldEZvcm1PcHRpb24gPSB7CiAgICAgICAgICAgICAga2V5OiAgICAgICBTdWJuZXRJZCwKICAgICAgICAgICAgICBsYWJlbDogICAgIGAkeyBuYW1lVGFnIH0gKCR7IFN1Ym5ldElkIH0pYCwKICAgICAgICAgICAgICBfaXNTdWJuZXQ6IHRydWUsCiAgICAgICAgICAgICAgZGlzYWJsZWQ6ICAhIXRoaXMuc2VsZWN0ZWRWcGMgJiYgVnBjSWQgIT09IHRoaXMuc2VsZWN0ZWRWcGMKICAgICAgICAgICAgfTsKCiAgICAgICAgICAgIG91dC5wdXNoKHN1Ym5ldEZvcm1PcHRpb24pOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKCiAgICAgIHJldHVybiBvdXQ7CiAgICB9LAoKICAgIHNlY3VyaXR5R3JvdXBPcHRpb25zKCkgewogICAgICBjb25zdCBhbGxHcm91cHMgPSB0aGlzLnNlY3VyaXR5R3JvdXBJbmZvPy5TZWN1cml0eUdyb3VwcyB8fCBbXTsKCiAgICAgIHJldHVybiBhbGxHcm91cHMucmVkdWNlKChvcHRzLCBzZykgPT4gewogICAgICAgIGlmIChzZy5WcGNJZCAhPT0gdGhpcy5zZWxlY3RlZFZwYykgewogICAgICAgICAgcmV0dXJuIG9wdHM7CiAgICAgICAgfQogICAgICAgIG9wdHMucHVzaCh7CiAgICAgICAgICBsYWJlbDogYCR7IHNnLkdyb3VwTmFtZSB9ICgkeyBzZy5Hcm91cElkIH0pYCwKICAgICAgICAgIHZhbHVlOiBzZy5Hcm91cElkCiAgICAgICAgfSk7CgogICAgICAgIHJldHVybiBvcHRzOwogICAgICB9LCBbXSBhcyB7bGFiZWw6IHN0cmluZywgdmFsdWU6IHN0cmluZ31bXSk7CiAgICB9LAoKICAgIGRpc3BsYXlTdWJuZXRzOiB7CiAgICAgIGdldCgpOiB7a2V5OnN0cmluZywgbGFiZWw6c3RyaW5nLCBfaXNTdWJuZXQ/OmJvb2xlYW4sIGtpbmQ/OnN0cmluZ31bXSB8IHN0cmluZ1tdIHsKICAgICAgICBjb25zdCBzdWJuZXRzOiBzdHJpbmdbXSA9IHRoaXMuY2hvb3NlU3VibmV0ID8gdGhpcy5zdWJuZXRzIDogdGhpcy5zdGF0dXNTdWJuZXRzOwoKICAgICAgICAvLyB2cGNPcHRpb25zIHdpbGwgYmUgZW1wdHkgaW4gJ3ZpZXcgY29uZmlnJyBtb2RlLCB3aGVyZSBhd3MgQVBJIHJlcXVlc3RzIGFyZSBub3QgbWFkZQogICAgICAgIHJldHVybiB0aGlzLnZwY09wdGlvbnMubGVuZ3RoID8gdGhpcy52cGNPcHRpb25zLmZpbHRlcigob3B0aW9uKSA9PiBzdWJuZXRzLmluY2x1ZGVzKG9wdGlvbi5rZXkpKSA6IHN1Ym5ldHM7CiAgICAgIH0sCiAgICAgIHNldChuZXU6IHtrZXk6c3RyaW5nLCBsYWJlbDpzdHJpbmcsIF9pc1N1Ym5ldD86Ym9vbGVhbiwga2luZD86c3RyaW5nfVtdKSB7CiAgICAgICAgdGhpcy4kZW1pdCgndXBkYXRlOnN1Ym5ldHMnLCBuZXUubWFwKChzKSA9PiBzLmtleSkpOwogICAgICB9CiAgICB9LAoKICAgIHNlbGVjdGVkVnBjKCkgewogICAgICBpZiAoIXRoaXMuY2hvb3NlU3VibmV0KSB7CiAgICAgICAgcmV0dXJuIG51bGw7CiAgICAgIH0KCiAgICAgIHJldHVybiAodGhpcy5zdWJuZXRJbmZvIHx8IFtdKS5maW5kKChzKSA9PiB0aGlzLnN1Ym5ldHMuaW5jbHVkZXMocy5TdWJuZXRJZCkpPy5WcGNJZDsKICAgIH0sCgogICAgaXNOZXcoKTogYm9vbGVhbiB7CiAgICAgIHJldHVybiB0aGlzLm1vZGUgPT09IF9DUkVBVEU7CiAgICB9LAoKICAgIGlzVmlldygpOmJvb2xlYW4gewogICAgICByZXR1cm4gdGhpcy5tb2RlID09PSBfVklFVzsKICAgIH0KICB9LAoKICBtZXRob2RzOiB7CiAgICBhc3luYyBmZXRjaFZwY3MoKSB7CiAgICAgIHRoaXMubG9hZGluZ1ZwY3MgPSB0cnVlOwogICAgICBjb25zdCB7IHJlZ2lvbiwgYW1hem9uQ3JlZGVudGlhbFNlY3JldCB9ID0gdGhpczsKCiAgICAgIGlmICghcmVnaW9uIHx8ICFhbWF6b25DcmVkZW50aWFsU2VjcmV0KSB7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGNvbnN0IGVjMkNsaWVudCA9IGF3YWl0IHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdhd3MvZWMyJywgeyByZWdpb24sIGNsb3VkQ3JlZGVudGlhbElkOiBhbWF6b25DcmVkZW50aWFsU2VjcmV0IH0pOwoKICAgICAgdHJ5IHsKICAgICAgICB0aGlzLnZwY0luZm8gPSBhd2FpdCB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnYXdzL2RlcGFnaW5hdGVMaXN0JywgeyBjbGllbnQ6IGVjMkNsaWVudCwgY21kOiAnZGVzY3JpYmVWcGNzJyB9KTsKICAgICAgICB0aGlzLnN1Ym5ldEluZm8gPSBhd2FpdCB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnYXdzL2RlcGFnaW5hdGVMaXN0JywgeyBjbGllbnQ6IGVjMkNsaWVudCwgY21kOiAnZGVzY3JpYmVTdWJuZXRzJyB9KTsKICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgdGhpcy4kZW1pdCgnZXJyb3InLCBlcnIpOwogICAgICB9CiAgICAgIHRoaXMubG9hZGluZ1ZwY3MgPSBmYWxzZTsKICAgIH0sCgogICAgYXN5bmMgZmV0Y2hTZWN1cml0eUdyb3VwcygpIHsKICAgICAgdGhpcy5sb2FkaW5nU2VjdXJpdHlHcm91cHMgPSB0cnVlOwogICAgICBjb25zdCB7IHJlZ2lvbiwgYW1hem9uQ3JlZGVudGlhbFNlY3JldCB9ID0gdGhpczsKCiAgICAgIGlmICghcmVnaW9uIHx8ICFhbWF6b25DcmVkZW50aWFsU2VjcmV0KSB7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGNvbnN0IGVjMkNsaWVudCA9IGF3YWl0IHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdhd3MvZWMyJywgeyByZWdpb24sIGNsb3VkQ3JlZGVudGlhbElkOiBhbWF6b25DcmVkZW50aWFsU2VjcmV0IH0pOwoKICAgICAgdHJ5IHsKICAgICAgICB0aGlzLnNlY3VyaXR5R3JvdXBJbmZvID0gYXdhaXQgZWMyQ2xpZW50LmRlc2NyaWJlU2VjdXJpdHlHcm91cHMoeyB9KTsKICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgdGhpcy4kZW1pdCgnZXJyb3InLCBlcnIpOwogICAgICB9CiAgICAgIHRoaXMubG9hZGluZ1NlY3VyaXR5R3JvdXBzID0gZmFsc2U7CiAgICB9CiAgfQp9KTsK"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Networking.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAElD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE9D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAErB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAExI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B;MACF,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACZ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B;MACF,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC3B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClC;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACzC;IACF;;EAEF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/D,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC9B,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACrB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MAC9E,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC1C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACnD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;MAEtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACrB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAC9B,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC;MACF,CAAC,CAAC;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAClB,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAET,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9D,CAAC;;QAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YAClC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;cAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAET,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;cACvB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;cACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5D,CAAC;;YAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;;MAEF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACZ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAE9D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;QACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC;;QAEF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC/E,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE/E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5G,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;MAE/C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR;MACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE9G,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC3G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACnH,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1B;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;MAE/C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR;MACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE9G,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACtE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1B;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC;EACF;AACF,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Networking.vue", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { _CREATE, _EDIT, _VIEW } from '@shell/config/query-params';\nimport { PropType, defineComponent } from 'vue';\nimport { Store, mapGetters } from 'vuex';\nimport LabeledSelect from '@shell/components/form/LabeledSelect.vue';\nimport Checkbox from '@components/Form/Checkbox/Checkbox.vue';\nimport ArrayList from '@shell/components/form/ArrayList.vue';\nimport Banner from '@components/Banner/Banner.vue';\n\nimport RadioGroup from '@components/Form/Radio/RadioGroup.vue';\n\nimport { AWS } from '../../types';\n\nexport default defineComponent({\n  name: 'EKSNetworking',\n\n  emits: ['update:subnets', 'update:securityGroups', 'error', 'update:publicAccess', 'update:privateAccess', 'update:publicAccessSources'],\n\n  components: {\n    LabeledSelect,\n    ArrayList,\n    Checkbox,\n    RadioGroup,\n    Banner\n  },\n\n  props: {\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n\n    region: {\n      type:    String,\n      default: ''\n    },\n\n    amazonCredentialSecret: {\n      type:    String,\n      default: ''\n    },\n\n    subnets: {\n      type:    Array as PropType<string[]>,\n      default: () => []\n    },\n\n    securityGroups: {\n      type:    Array as PropType<string[]>,\n      default: () => []\n    },\n\n    publicAccess: {\n      type:    Boolean,\n      default: false\n    },\n\n    privateAccess: {\n      type:    Boolean,\n      default: false\n    },\n\n    publicAccessSources: {\n      type:    Array,\n      default: () => []\n    },\n\n    statusSubnets: {\n      type:    Array as PropType<string[]>,\n      default: () => []\n    },\n\n    rules: {\n      type:    Object,\n      default: () => {}\n    }\n  },\n\n  watch: {\n    amazonCredentialSecret: {\n      handler(neu) {\n        if (neu && !this.isView) {\n          this.fetchVpcs();\n          this.fetchSecurityGroups();\n        }\n      },\n      immediate: true\n    },\n    region: {\n      handler(neu ) {\n        if (neu && !this.isView) {\n          this.fetchVpcs();\n          this.fetchSecurityGroups();\n        }\n      },\n      immediate: true\n    },\n\n    'chooseSubnet'(neu: boolean) {\n      if (!neu) {\n        this.$emit('update:subnets', []);\n      }\n    },\n\n    selectedVpc(neu: string, old: string) {\n      if (!!old) {\n        this.$emit('update:securityGroups', []);\n      }\n    }\n\n  },\n\n  data() {\n    return {\n      loadingVpcs:           false,\n      loadingSecurityGroups: false,\n      vpcInfo:               [] as AWS.VPC[],\n      subnetInfo:            [] as AWS.Subnet[],\n      securityGroupInfo:     {} as {SecurityGroups: AWS.SecurityGroup[]},\n      chooseSubnet:          !!this.subnets && !!this.subnets.length\n    };\n  },\n\n  computed: {\n    ...mapGetters({ t: 'i18n/t' }),\n    // map subnets to VPCs\n    // {[vpc id]: [subnets]}\n    vpcOptions() {\n      const out: {key:string, label:string, _isSubnet?:boolean, kind?:string}[] = [];\n      const vpcs: AWS.VPC[] = this.vpcInfo || [];\n      const subnets: AWS.Subnet[] = this.subnetInfo || [];\n      const mappedSubnets: {[key:string]: AWS.Subnet[]} = {};\n\n      subnets.forEach((s) => {\n        if (!mappedSubnets[s.VpcId]) {\n          mappedSubnets[s.VpcId] = [s];\n        } else {\n          mappedSubnets[s.VpcId].push(s);\n        }\n      });\n      vpcs.forEach((v) => {\n        const { VpcId = '', Tags = [] } = v;\n        const nameTag = Tags.find((t) => {\n          return t.Key === 'Name';\n        })?.Value;\n\n        const formOption = {\n          key: VpcId, label: `${ nameTag } (${ VpcId })`, kind: 'group'\n        };\n\n        out.push(formOption);\n        if (mappedSubnets[VpcId]) {\n          mappedSubnets[VpcId].forEach((s) => {\n            const { SubnetId, Tags = [] } = s;\n            const nameTag = Tags.find((t) => {\n              return t.Key === 'Name';\n            })?.Value;\n\n            const subnetFormOption = {\n              key:       SubnetId,\n              label:     `${ nameTag } (${ SubnetId })`,\n              _isSubnet: true,\n              disabled:  !!this.selectedVpc && VpcId !== this.selectedVpc\n            };\n\n            out.push(subnetFormOption);\n          });\n        }\n      });\n\n      return out;\n    },\n\n    securityGroupOptions() {\n      const allGroups = this.securityGroupInfo?.SecurityGroups || [];\n\n      return allGroups.reduce((opts, sg) => {\n        if (sg.VpcId !== this.selectedVpc) {\n          return opts;\n        }\n        opts.push({\n          label: `${ sg.GroupName } (${ sg.GroupId })`,\n          value: sg.GroupId\n        });\n\n        return opts;\n      }, [] as {label: string, value: string}[]);\n    },\n\n    displaySubnets: {\n      get(): {key:string, label:string, _isSubnet?:boolean, kind?:string}[] | string[] {\n        const subnets: string[] = this.chooseSubnet ? this.subnets : this.statusSubnets;\n\n        // vpcOptions will be empty in 'view config' mode, where aws API requests are not made\n        return this.vpcOptions.length ? this.vpcOptions.filter((option) => subnets.includes(option.key)) : subnets;\n      },\n      set(neu: {key:string, label:string, _isSubnet?:boolean, kind?:string}[]) {\n        this.$emit('update:subnets', neu.map((s) => s.key));\n      }\n    },\n\n    selectedVpc() {\n      if (!this.chooseSubnet) {\n        return null;\n      }\n\n      return (this.subnetInfo || []).find((s) => this.subnets.includes(s.SubnetId))?.VpcId;\n    },\n\n    isNew(): boolean {\n      return this.mode === _CREATE;\n    },\n\n    isView():boolean {\n      return this.mode === _VIEW;\n    }\n  },\n\n  methods: {\n    async fetchVpcs() {\n      this.loadingVpcs = true;\n      const { region, amazonCredentialSecret } = this;\n\n      if (!region || !amazonCredentialSecret) {\n        return;\n      }\n      const ec2Client = await this.$store.dispatch('aws/ec2', { region, cloudCredentialId: amazonCredentialSecret });\n\n      try {\n        this.vpcInfo = await this.$store.dispatch('aws/depaginateList', { client: ec2Client, cmd: 'describeVpcs' });\n        this.subnetInfo = await this.$store.dispatch('aws/depaginateList', { client: ec2Client, cmd: 'describeSubnets' });\n      } catch (err) {\n        this.$emit('error', err);\n      }\n      this.loadingVpcs = false;\n    },\n\n    async fetchSecurityGroups() {\n      this.loadingSecurityGroups = true;\n      const { region, amazonCredentialSecret } = this;\n\n      if (!region || !amazonCredentialSecret) {\n        return;\n      }\n      const ec2Client = await this.$store.dispatch('aws/ec2', { region, cloudCredentialId: amazonCredentialSecret });\n\n      try {\n        this.securityGroupInfo = await ec2Client.describeSecurityGroups({ });\n      } catch (err) {\n        this.$emit('error', err);\n      }\n      this.loadingSecurityGroups = false;\n    }\n  }\n});\n</script>\n\n<template>\n  <div>\n    <Banner\n      color=\"info\"\n      label-key=\"eks.publicAccess.tooltip\"\n    />\n    <div class=\"row mb-10\">\n      <div class=\"col span-6\">\n        <Checkbox\n          :value=\"publicAccess\"\n          :mode=\"mode\"\n          label-key=\"eks.publicAccess.label\"\n          @update:value=\"$emit('update:publicAccess', $event)\"\n        />\n        <Checkbox\n          :value=\"privateAccess\"\n          :mode=\"mode\"\n          label-key=\"eks.privateAccess.label\"\n          @update:value=\"$emit('update:privateAccess', $event)\"\n        />\n      </div>\n    </div>\n    <div class=\"row mb-10\">\n      <div class=\"col span-6\">\n        <ArrayList\n          :value=\"publicAccessSources\"\n          :mode=\"mode\"\n          :disabled=\"!publicAccess\"\n          :add-allowed=\"publicAccess\"\n          :add-label=\"t('eks.publicAccessSources.addEndpoint')\"\n          data-testid=\"eks-public-access-sources\"\n          @update:value=\"$emit('update:publicAccessSources', $event)\"\n        >\n          <template #title>\n            {{ t('eks.publicAccessSources.label') }}\n          </template>\n        </ArrayList>\n      </div>\n    </div>\n    <div class=\"row mb-10 mt-20\">\n      <div\n        v-if=\"isNew\"\n        class=\"col span-6\"\n      >\n        <RadioGroup\n          v-model:value=\"chooseSubnet\"\n          name=\"subnet-mode\"\n          :mode=\"mode\"\n          :options=\"[{label: t('eks.subnets.default'), value: false},{label: t('eks.subnets.useCustom'), value: true}]\"\n          label-key=\"eks.subnets.title\"\n          :disabled=\"!isNew\"\n        />\n      </div>\n    </div>\n    <div\n      class=\"row mb-10\"\n    >\n      <div\n        v-if=\"chooseSubnet || !isNew\"\n        class=\"col span-6\"\n      >\n        <LabeledSelect\n          v-model:value=\"displaySubnets\"\n          :disabled=\"!isNew\"\n          :mode=\"mode\"\n          label-key=\"eks.vpcSubnet.label\"\n          :options=\"vpcOptions\"\n          :loading=\"loadingVpcs\"\n          option-key=\"key\"\n          :multiple=\"true\"\n          :rules=\"rules && rules.subnets\"\n          data-testid=\"eks-subnets-dropdown\"\n        >\n          <template #option=\"option\">\n            <span :class=\"{'pl-30': option._isSubnet}\">{{ option.label }}</span>\n          </template>\n        </LabeledSelect>\n      </div>\n      <div\n        v-if=\"chooseSubnet\"\n        class=\"col span-6\"\n      >\n        <LabeledSelect\n          :mode=\"mode\"\n          :disabled=\"!isNew\"\n          label-key=\"eks.securityGroups.label\"\n          :tooltip=\"t('eks.securityGroups.tooltip')\"\n          :options=\"securityGroupOptions\"\n          :multiple=\"true\"\n          :value=\"securityGroups\"\n          :loading=\"loadingSecurityGroups\"\n          data-testid=\"eks-security-groups-dropdown\"\n          @update:value=\"$emit('update:securityGroups', $event)\"\n        />\n      </div>\n    </div>\n  </div>\n</template>\n"]}]}