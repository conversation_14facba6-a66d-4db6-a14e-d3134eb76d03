{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/KnownHostsEditDialog.vue?vue&type=template&id=73593971&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/KnownHostsEditDialog.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/KnownHostsEditDialog.vue"], "names": [], "mappings": ";EA+EE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B;IACE,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B;MACE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC3C,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B;cACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B;cACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/KnownHostsEditDialog.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\nimport CodeMirror from '@shell/components/CodeMirror';\nimport FileSelector from '@shell/components/form/FileSelector.vue';\nimport AppModal from '@shell/components/AppModal.vue';\n\nexport default {\n  emits: ['closed'],\n\n  components: {\n    FileSelector,\n    AppModal,\n    CodeMirror,\n  },\n\n  props: {\n    value: {\n      type:     String,\n      required: true\n    },\n\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n  },\n\n  data() {\n    const codeMirrorOptions = {\n      readOnly:        this.isView,\n      gutters:         ['CodeMirror-foldgutter'],\n      mode:            'text/x-properties',\n      lint:            false,\n      lineNumbers:     !this.isView,\n      styleActiveLine: false,\n      tabSize:         2,\n      indentWithTabs:  false,\n      cursorBlinkRate: 530,\n    };\n\n    return {\n      codeMirrorOptions,\n      text:      this.value,\n      showModal: false,\n    };\n  },\n\n  computed: {\n    isView() {\n      return this.mode === _VIEW;\n    }\n  },\n\n  methods: {\n    onTextChange(value) {\n      this.text = value?.trim();\n    },\n\n    showDialog() {\n      this.showModal = true;\n    },\n\n    closeDialog(result) {\n      if (!result) {\n        this.text = this.value;\n      }\n\n      this.showModal = false;\n\n      this.$emit('closed', {\n        success: result,\n        value:   this.text,\n      });\n    },\n  }\n};\n</script>\n\n<template>\n  <app-modal\n    v-if=\"showModal\"\n    ref=\"sshKnownHostsDialog\"\n    data-testid=\"sshKnownHostsDialog\"\n    height=\"auto\"\n    :scrollable=\"true\"\n    @close=\"closeDialog(false)\"\n  >\n    <div\n      class=\"ssh-known-hosts-dialog\"\n    >\n      <h4 class=\"mt-10\">\n        {{ t('secret.ssh.editKnownHosts.title') }}\n      </h4>\n      <div class=\"custom mt-10\">\n        <div class=\"dialog-panel\">\n          <CodeMirror\n            :value=\"text\"\n            data-testid=\"ssh-known-hosts-dialog_code-mirror\"\n            :options=\"codeMirrorOptions\"\n            :showKeyMapBox=\"true\"\n            @onInput=\"onTextChange\"\n          />\n        </div>\n        <div class=\"dialog-actions\">\n          <div class=\"action-pannel file-selector\">\n            <FileSelector\n              class=\"btn role-secondary\"\n              data-testid=\"ssh-known-hosts-dialog_file-selector\"\n              :label=\"t('generic.readFromFile')\"\n              @selected=\"onTextChange\"\n            />\n          </div>\n          <div class=\"action-pannel form-actions\">\n            <button\n              class=\"btn role-secondary\"\n              data-testid=\"ssh-known-hosts-dialog_cancel-btn\"\n              @click=\"closeDialog(false)\"\n            >\n              {{ t('generic.cancel') }}\n            </button>\n            <button\n              class=\"btn role-primary\"\n              data-testid=\"ssh-known-hosts-dialog_save-btn\"\n              @click=\"closeDialog(true)\"\n            >\n              {{ t('generic.save') }}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </app-modal>\n</template>\n\n<style lang=\"scss\" scoped>\n  .ssh-known-hosts-dialog {\n    padding: 15px;\n\n    h4 {\n      font-weight: bold;\n      margin-bottom: 20px;\n    }\n\n    .dialog-panel {\n      display: flex;\n      flex-direction: column;\n      min-height: 100px;\n\n      :deep() .code-mirror {\n        display: flex;\n        flex-direction: column;\n        resize: none;\n\n        .codemirror-container {\n          border: 1px solid var(--border);\n        }\n\n        .CodeMirror,\n        .CodeMirror-gutters {\n          min-height: 400px;\n          max-height: 400px;\n          background-color: var(--yaml-editor-bg);\n        }\n\n        .CodeMirror-gutters {\n          width: 25px;\n        }\n\n        .CodeMirror-linenumber {\n          padding-left: 0;\n        }\n      }\n    }\n\n    .dialog-actions {\n      display: flex;\n      justify-content: space-between;\n\n      .action-pannel {\n        margin-top: 10px;\n      }\n\n      .form-actions {\n        display: flex;\n        justify-content: flex-end;\n\n        > *:not(:last-child) {\n          margin-right: 10px;\n        }\n      }\n    }\n  }\n</style>\n"]}]}