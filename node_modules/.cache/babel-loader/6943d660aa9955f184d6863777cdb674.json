{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Bar.vue?vue&type=template&id=1c9b98df&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Bar.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgbm9ybWFsaXplU3R5bGUgYXMgX25vcm1hbGl6ZVN0eWxlLCBjcmVhdGVFbGVtZW50Vk5vZGUgYXMgX2NyZWF0ZUVsZW1lbnRWTm9kZSwgcmVuZGVyTGlzdCBhcyBfcmVuZGVyTGlzdCwgRnJhZ21lbnQgYXMgX0ZyYWdtZW50LCBvcGVuQmxvY2sgYXMgX29wZW5CbG9jaywgY3JlYXRlRWxlbWVudEJsb2NrIGFzIF9jcmVhdGVFbGVtZW50QmxvY2ssIGNyZWF0ZVRleHRWTm9kZSBhcyBfY3JlYXRlVGV4dFZOb2RlIH0gZnJvbSAidnVlIgoKZXhwb3J0IGZ1bmN0aW9uIHJlbmRlcihfY3R4LCBfY2FjaGUsICRwcm9wcywgJHNldHVwLCAkZGF0YSwgJG9wdGlvbnMpIHsKICByZXR1cm4gKF9vcGVuQmxvY2soKSwgX2NyZWF0ZUVsZW1lbnRCbG9jaygiZGl2IiwgewogICAgY2xhc3M6ICJiYXIiLAogICAgc3R5bGU6IF9ub3JtYWxpemVTdHlsZSgkb3B0aW9ucy5iYXJTdHlsZSkKICB9LCBbCiAgICBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCB7CiAgICAgIGNsYXNzOiAiaW5kaWNhdG9yIiwKICAgICAgc3R5bGU6IF9ub3JtYWxpemVTdHlsZSgkb3B0aW9ucy5pbmRpY2F0b3JTdHlsZSkKICAgIH0sIG51bGwsIDQgLyogU1RZTEUgKi8pLAogICAgX2NhY2hlWzBdIHx8IChfY2FjaGVbMF0gPSBfY3JlYXRlVGV4dFZOb2RlKCkpLAogICAgKF9vcGVuQmxvY2sodHJ1ZSksIF9jcmVhdGVFbGVtZW50QmxvY2soX0ZyYWdtZW50LCBudWxsLCBfcmVuZGVyTGlzdCgkb3B0aW9ucy5zbGljZVN0eWxlcywgKHNsaWNlU3R5bGUsIGkpID0+IHsKICAgICAgcmV0dXJuIChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soImRpdiIsIHsKICAgICAgICBrZXk6IGksCiAgICAgICAgY2xhc3M6ICJzbGljZSIsCiAgICAgICAgc3R5bGU6IF9ub3JtYWxpemVTdHlsZShzbGljZVN0eWxlKQogICAgICB9LCBudWxsLCA0IC8qIFNUWUxFICovKSkKICAgIH0pLCAxMjggLyogS0VZRURfRlJBR01FTlQgKi8pKQogIF0sIDQgLyogU1RZTEUgKi8pKQp9"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Bar.vue"], "names": [], "mappings": ";;;wBAyCE,oBAcM;IAbJ,KAAK,EAAC,KAAK;IACV,KAAK,kBAAE,iBAAQ;;IAEhB,oBAGE;MAFA,KAAK,EAAC,WAAW;MAChB,KAAK,kBAAE,uBAAc;;;uBAExB,oBAKE,6BAJ0B,oBAAW,GAA7B,UAAU,EAAE,CAAC;4BADvB,oBAKE;QAHC,GAAG,EAAE,CAAC;QACP,KAAK,EAAC,OAAO;QACZ,KAAK,kBAAE,UAAU", "sourcesContent": ["<script>\r\nexport default {\r\n  props: {\r\n    percentage: {\r\n      type:     Number,\r\n      required: true\r\n    },\r\n    primaryColor: {\r\n      type:    String,\r\n      default: '--primary'\r\n    },\r\n    secondaryColor: {\r\n      type:    String,\r\n      default: '--border'\r\n    },\r\n    slices: {\r\n      type:    Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  computed: {\r\n    indicatorStyle() {\r\n      return {\r\n        width:           `${ this.percentage }%`,\r\n        backgroundColor: `var(${ this.primaryColor })`\r\n      };\r\n    },\r\n    barStyle() {\r\n      return { backgroundColor: `var(${ this.secondaryColor })` };\r\n    },\r\n    sliceStyles() {\r\n      return this.slices.map((slice) => ({\r\n        left:       `${ slice }%`,\r\n        visibility: slice < this.percentage ? 'visible' : 'hidden'\r\n      }));\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<template>\r\n  <div\n    class=\"bar\"\n    :style=\"barStyle\"\n  >\r\n    <div\n      class=\"indicator\"\n      :style=\"indicatorStyle\"\n    />\r\n    <div\n      v-for=\"(sliceStyle, i) in sliceStyles\"\n      :key=\"i\"\n      class=\"slice\"\n      :style=\"sliceStyle\"\n    />\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.bar {\r\n    $height: 15px;\r\n\r\n    width: 100%;\r\n    height: $height;\r\n    border-radius: math.div($height, 2);\r\n    overflow: hidden;\r\n    position: relative;\r\n\r\n    .indicator {\r\n        height: 100%;\r\n    }\r\n\r\n    .slice {\r\n      position: absolute;\r\n      top: 0;\r\n      bottom: 0;\r\n      width: 1px;\r\n      background-color: var(--body-bg);\r\n    }\r\n}\r\n</style>\r\n"]}]}