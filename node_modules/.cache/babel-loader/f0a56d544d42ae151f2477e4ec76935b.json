{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??clonedRuleSet-44.use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/ConfigSummary.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/ConfigSummary.vue", "mtime": 1755002061656}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/ConfigSummary.vue"], "names": [], "mappings": "AACA,OAAO,EAAE,eAAe,EAAW,MAAO,KAAK,CAAA;AAE/C,OAAO,UAAS,MAAO,kCAAkC,CAAA;AACzD,OAAO,MAAK,MAAO,+BAA+B,CAAA;AAElD,eAAe,eAAe,CAAC;IAC7B,IAAI,EAAE,eAAe;IAErB,UAAU,EAAE;QACV,UAAU;QACV,MAAK;KACN;IAED,KAAK,EAAE;QACL,aAAa,EAAE;YACb,IAAI,EAAM,MAAiC;YAC3C,QAAQ,EAAE,IAAG;SACd;QACD,MAAM,EAAE;YACN,IAAI,EAAM,MAA6B;YACvC,QAAQ,EAAE,IAAG;SACd;QACD,UAAU,EAAE;YACV,IAAI,EAAK,KAAiC;YAC1C,OAAO,EAAE,GAAG,EAAC,CAAE,EAAC;SACjB;QACD,MAAM,EAAE;YACN,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,EAAC;SACZ;KACD;IAED,QAAQ,EAAE;QACR,WAAW;;YACT,OAAO,CAAA,MAAA,IAAI,CAAC,aAAa,0CAAE,IAAG,KAAK,SAAS,CAAA;QAC9C,CAAC;QAED,SAAS;;YACP,OAAO,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,MAAK,KAAK,IAAI,CAAC,MAAK,IAAK,SAAS,CAAA;QACxD,CAAC;QAED,iBAAiB;;YACf,OAAO,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,iBAAgB,KAAK,eAAe,CAAA;QAC1D,CAAC;QAED,UAAU;YACR,MAAM,MAAK,GAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAC;gBAClD,OAAO;oBACL,GAAG,EAAM,GAAG,CAAC,GAAE,GAAI,CAAC,KAAK,CAAC,OAAM,IAAK,CAAC,CAAC;oBACvC,GAAG,EAAM,GAAG,CAAC,GAAE,GAAI,CAAC,KAAK,CAAC,OAAM,IAAK,CAAC,CAAC;oBACvC,OAAO,EAAE,GAAG,CAAC,OAAM,GAAI,CAAC,KAAK,CAAC,WAAU,IAAK,CAAC,CAAA;iBAC/C,CAAA;YACH,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,OAAO,EAAE,CAAA,EAAG,CAAC,CAAA;YAElC,OAAO,MAAM,CAAA;QACf,CAAC;QAED,gBAAgB;YACd,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAA,IAAK,IAAI,CAAA;QACnC,CAAC;QAED,oBAAoB;YAClB,yBAAwB;YACxB,MAAM,OAAO,GAA2B;gBACtC,UAAU,EAAI,EAAE;gBAChB,WAAW,EAAG,EAAE;gBAChB,UAAU,EAAI,EAAE;gBAChB,WAAW,EAAG,GAAG;gBACjB,WAAW,EAAG,EAAE;gBAChB,YAAY,EAAE,EAAE;gBAChB,WAAW,EAAG,EAAE;gBAChB,UAAU,EAAI,EAAE;gBAChB,WAAW,EAAG,GAAG;gBACjB,WAAW,EAAG,EAAE;gBAChB,YAAY,EAAE,GAAG;aAClB,CAAA;YAED,IAAI,SAAQ,GAAI,CAAC,CAAA;YACjB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAI,CAAE,EAAC;gBAC7B,MAAM,YAAW,GAAI,KAAK,CAAC,YAAW,CAAE,CAAA,CAAE,OAAO,CAAC,KAAK,CAAC,YAAY,CAAA,IAAK,EAAC,CAAE,CAAA,CAAE,EAAE,CAAA;gBAChF,SAAQ,IAAK,YAAW,GAAI,CAAC,KAAK,CAAC,WAAU,IAAK,CAAC,CAAC,CAAA;YACtD,CAAC,CAAC,CAAA;YAEF,uDAAsD;YACtD,SAAQ,IAAK,EAAE,CAAA;YAEf,OAAO,IAAI,SAAS,EAAE,CAAA;QACxB,CAAC;QAED,cAAc;;YACZ,IAAI,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,YAAW,KAAK,CAAC,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,aAAa,CAAA,EAAE,CAAA;gBAC5D,OAAO,QAAQ,CAAA;YACjB,CAAA;iBAAO,IAAI,CAAC,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,YAAW,CAAA,KAAK,MAAA,IAAI,CAAC,MAAM,0CAAE,aAAa,CAAA,EAAE,CAAA;gBACnE,OAAO,SAAS,CAAA;YAClB,CAAA;iBAAO,IAAI,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,YAAW,MAAK,MAAA,IAAI,CAAC,MAAM,0CAAE,aAAa,CAAA,EAAE,CAAA;gBAClE,OAAO,oBAAoB,CAAA;YAC7B,CAAA;YACA,OAAO,kBAAkB,CAAA;QAC3B,CAAA;KACF;CACD,CAAC,CAAA", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/ConfigSummary.vue.tsx", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent, PropType } from 'vue';\nimport { EKSConfig, EKSNodeGroup, NormanCluster } from '../../types';\nimport LabelValue from '@shell/components/LabelValue.vue';\nimport Banner from '@components/Banner/Banner.vue';\n\nexport default defineComponent({\n  name: 'ConfigSummary',\n\n  components: {\n    LabelValue,\n    Banner\n  },\n\n  props: {\n    normanCluster: {\n      type:     Object as PropType<NormanCluster>,\n      required: true\n    },\n    config: {\n      type:     Object as PropType<EKSConfig>,\n      required: true\n    },\n    nodeGroups: {\n      type:    Array as PropType<EKSNodeGroup[]>,\n      default: () => []\n    },\n    region: {\n      type:    String,\n      default: ''\n    }\n  },\n\n  computed: {\n    clusterName(): string {\n      return this.normanCluster?.name || 'Not set';\n    },\n\n    awsRegion(): string {\n      return this.config?.region || this.region || 'Not set';\n    },\n\n    kubernetesVersion(): string {\n      return this.config?.kubernetesVersion || 'Latest stable';\n    },\n\n    totalNodes(): { min: number; max: number; desired: number } {\n      const totals = this.nodeGroups.reduce((acc, group) => {\n        return {\n          min:     acc.min + (group.minSize || 0),\n          max:     acc.max + (group.maxSize || 0),\n          desired: acc.desired + (group.desiredSize || 0)\n        };\n      }, { min: 0, max: 0, desired: 0 });\n\n      return totals;\n    },\n\n    primaryNodeGroup(): EKSNodeGroup | null {\n      return this.nodeGroups[0] || null;\n    },\n\n    estimatedMonthlyCost(): string {\n      // Simple cost estimation\n      const costMap: Record<string, number> = {\n        't3.small':   15,\n        't3.medium':  30,\n        't3.large':   60,\n        't3.xlarge':  120,\n        't4g.small':  12,\n        't4g.medium': 24,\n        't4g.large':  48,\n        'm5.large':   70,\n        'm5.xlarge':  140,\n        'm6i.large':  70,\n        'm6i.xlarge': 140,\n      };\n\n      let totalCost = 0;\n      this.nodeGroups.forEach(group => {\n        const instanceCost = group.instanceType ? costMap[group.instanceType] || 50 : 50;\n        totalCost += instanceCost * (group.desiredSize || 2);\n      });\n\n      // Add EKS control plane cost ($0.10/hour = ~$73/month)\n      totalCost += 73;\n\n      return `$${totalCost}`;\n    },\n\n    networkingMode(): string {\n      if (this.config?.publicAccess && !this.config?.privateAccess) {\n        return 'Public';\n      } else if (!this.config?.publicAccess && this.config?.privateAccess) {\n        return 'Private';\n      } else if (this.config?.publicAccess && this.config?.privateAccess) {\n        return 'Public and Private';\n      }\n      return 'Default (Public)';\n    }\n  }\n});\n</script>\n\n<template>\n  <div class=\"config-summary\">\n    <div class=\"summary-section\">\n      <h3>\n        <i class=\"icon icon-cluster\" />\n        Cluster Configuration\n      </h3>\n      \n      <div class=\"summary-grid\">\n        <LabelValue\n          name=\"Cluster Name\"\n          :value=\"clusterName\"\n          class=\"summary-item\"\n        />\n        \n        <LabelValue\n          name=\"AWS Region\"\n          :value=\"awsRegion\"\n          class=\"summary-item\"\n        />\n        \n        <LabelValue\n          name=\"Kubernetes Version\"\n          :value=\"kubernetesVersion\"\n          class=\"summary-item\"\n        />\n        \n        <LabelValue\n          name=\"Network Access\"\n          :value=\"networkingMode\"\n          class=\"summary-item\"\n        />\n      </div>\n    </div>\n\n    <div class=\"summary-section mt-20\">\n      <h3>\n        <i class=\"icon icon-nodes\" />\n        Node Configuration\n      </h3>\n      \n      <div\n        v-if=\"primaryNodeGroup\"\n        class=\"node-summary\"\n      >\n        <div class=\"summary-grid\">\n          <LabelValue\n            name=\"Instance Type\"\n            :value=\"primaryNodeGroup.instanceType\"\n            class=\"summary-item\"\n          />\n          \n          <LabelValue\n            name=\"Disk Size\"\n            :value=\"`${primaryNodeGroup.diskSize} GB`\"\n            class=\"summary-item\"\n          />\n          \n          <LabelValue\n            name=\"Auto-scaling\"\n            :value=\"`${totalNodes.min} - ${totalNodes.max} nodes`\"\n            class=\"summary-item\"\n          />\n          \n          <LabelValue\n            name=\"Initial Size\"\n            :value=\"`${totalNodes.desired} nodes`\"\n            class=\"summary-item\"\n          />\n        </div>\n      </div>\n\n      <div\n        v-if=\"nodeGroups.length > 1\"\n        class=\"mt-10\"\n      >\n        <Banner color=\"info\">\n          <p>{{ nodeGroups.length }} node groups configured</p>\n        </Banner>\n      </div>\n    </div>\n\n    <div class=\"summary-section mt-20 cost-section\">\n      <h3>\n        <i class=\"icon icon-dollar\" />\n        Estimated Cost\n      </h3>\n      \n      <div class=\"cost-breakdown\">\n        <div class=\"cost-main\">\n          <span class=\"cost-label\">Monthly estimate:</span>\n          <span class=\"cost-value\">{{ estimatedMonthlyCost }}</span>\n        </div>\n        <p class=\"cost-disclaimer\">\n          * This is a rough estimate based on instance types and does not include data transfer, storage, or other AWS services.\n        </p>\n      </div>\n    </div>\n\n    <div class=\"summary-section mt-20\">\n      <Banner\n        color=\"success\"\n        class=\"ready-banner\"\n      >\n        <div class=\"ready-content\">\n          <i class=\"icon icon-checkmark icon-2x\" />\n          <div>\n            <h4>Ready to create your cluster!</h4>\n            <p>Your cluster will be created with production-ready defaults including:</p>\n            <ul>\n              <li>Automatic security updates</li>\n              <li>Network isolation and security groups</li>\n              <li>CloudWatch logging enabled</li>\n              <li>IAM roles for service accounts (IRSA)</li>\n            </ul>\n          </div>\n        </div>\n      </Banner>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.config-summary {\n  .summary-section {\n    padding: 20px;\n    background: var(--body-bg);\n    border: 1px solid var(--border);\n    border-radius: var(--border-radius);\n\n    h3 {\n      margin: 0 0 20px 0;\n      font-size: 18px;\n      font-weight: 600;\n      display: flex;\n      align-items: center;\n      gap: 10px;\n      color: var(--text-default);\n\n      i {\n        color: var(--primary);\n      }\n    }\n  }\n\n  .summary-grid {\n    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n    gap: 20px;\n\n    .summary-item {\n      padding: 10px;\n      background: var(--nav-bg);\n      border-radius: var(--border-radius);\n      \n      ::v-deep .labeled-value {\n        .label {\n          color: var(--text-muted);\n          font-size: 12px;\n          text-transform: uppercase;\n          margin-bottom: 5px;\n        }\n\n        .value {\n          color: var(--text-default);\n          font-size: 16px;\n          font-weight: 500;\n        }\n      }\n    }\n  }\n\n  .cost-section {\n    background: linear-gradient(135deg, var(--body-bg) 0%, var(--nav-bg) 100%);\n  }\n\n  .cost-breakdown {\n    .cost-main {\n      display: flex;\n      align-items: baseline;\n      gap: 15px;\n      margin-bottom: 10px;\n\n      .cost-label {\n        font-size: 16px;\n        color: var(--text-muted);\n      }\n\n      .cost-value {\n        font-size: 32px;\n        font-weight: 600;\n        color: var(--success);\n      }\n    }\n\n    .cost-disclaimer {\n      font-size: 12px;\n      color: var(--text-muted);\n      font-style: italic;\n      margin: 0;\n    }\n  }\n\n  .ready-banner {\n    .ready-content {\n      display: flex;\n      gap: 20px;\n      align-items: flex-start;\n\n      i {\n        color: var(--success);\n        flex-shrink: 0;\n      }\n\n      h4 {\n        margin: 0 0 10px 0;\n        font-size: 18px;\n        font-weight: 600;\n      }\n\n      p {\n        margin: 0 0 10px 0;\n      }\n\n      ul {\n        margin: 0;\n        padding-left: 20px;\n\n        li {\n          margin: 5px 0;\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}