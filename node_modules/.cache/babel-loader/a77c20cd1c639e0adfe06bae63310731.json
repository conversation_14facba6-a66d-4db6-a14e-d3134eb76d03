{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/utils/dom.js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/utils/dom.js", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGZ1bmN0aW9uIGdldFBhcmVudChlbCwgcGFyZW50U2VsZWN0b3IpIHsKICBlbCA9IGVsPy5wYXJlbnRFbGVtZW50OwoKICBpZiAoIWVsKSB7CiAgICByZXR1cm4gbnVsbDsKICB9CgogIGNvbnN0IG1hdGNoRm4gPSBlbC5tYXRjaGVzIHx8IGVsLm1hdGNoZXNTZWxlY3RvcjsKCiAgaWYgKCFtYXRjaEZuLmNhbGwoZWwsIHBhcmVudFNlbGVjdG9yKSkgewogICAgcmV0dXJuIGdldFBhcmVudChlbCwgcGFyZW50U2VsZWN0b3IpOwogIH0KCiAgcmV0dXJuIGVsOwp9Cg=="}]}