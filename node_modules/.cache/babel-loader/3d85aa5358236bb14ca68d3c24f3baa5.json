{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Int.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Int.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCB7IExhYmVsZWRJbnB1dCB9IGZyb20gJ0Bjb21wb25lbnRzL0Zvcm0vTGFiZWxlZElucHV0JzsKaW1wb3J0IFF1ZXN0aW9uIGZyb20gJy4vUXVlc3Rpb24nOwoKZXhwb3J0IGRlZmF1bHQgewogIGVtaXRzOiBbJ3VwZGF0ZTp2YWx1ZSddLAoKICBjb21wb25lbnRzOiB7IExhYmVsZWRJbnB1dCB9LAogIG1peGluczogICAgIFtRdWVzdGlvbl0KfTsK"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Int.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvB,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Int.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport Question from './Question';\n\nexport default {\n  emits: ['update:value'],\n\n  components: { LabeledInput },\n  mixins:     [Question]\n};\n</script>\n\n<template>\n  <div\n    :data-testid=\"`int-row-${question.variable}`\"\n    class=\"row\"\n  >\n    <div class=\"col span-6\">\n      <LabeledInput\n        type=\"text\"\n        :mode=\"mode\"\n        :label=\"displayLabel\"\n        :placeholder=\"question.default\"\n        :required=\"question.required\"\n        :value=\"value\"\n        :disabled=\"disabled\"\n        :tooltip=\"displayTooltip\"\n        :rules=\"rules\"\n        :data-testid=\"`int-input-${question.variable}`\"\n        @update:value=\"val = parseInt($event, 10); if ( !isNaN(val) ) { $emit('update:value', val) }\"\n      />\n    </div>\n    <div\n      v-if=\"showDescription\"\n      :data-testid=\"`int-description-${question.variable}`\"\n      class=\"col span-6 mt-10\"\n    >\n      {{ displayDescription }}\n    </div>\n  </div>\n</template>\n"]}]}