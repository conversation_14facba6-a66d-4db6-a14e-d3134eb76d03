{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Probe.vue?vue&type=template&id=e721c4b0&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Probe.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Probe.vue"], "names": ["t"], "mappings": ";;qBAoJS,KAAK,EAAC,gBAAgB;;;EAMrB,KAAK,EAAC,gBAAgB;;qBAIvB,KAAK,EAAC,KAAK;;EAEZ,aAAW,EAAC,kBAAkB;EAC9B,KAAK,EAAC,mBAAmB;;;;EAavB,KAAK,EAAC,cAAc;;;;EAMpB,aAAW,EAAC,kBAAkB;;qBAezB,aAAW,EAAC,kBAAkB;;;EAcnC,aAAW,EAAC,oBAAoB;;;;EAkBhC,aAAW,EAAC,qBAAqB;;sBAE5B,KAAK,EAAC,aAAa;sBAYvB,KAAK,EAAC,kBAAkB;;;EAGxB,KAAK,EAAE,uCAAuC;EAC/C,KAAK,EAAC,UAAU;;;;EAOlB,KAAK,EAAC,mBAAmB;;sBAEpB,KAAK,EAAC,KAAK;;EAEZ,aAAW,EAAC,2BAA2B;EACvC,KAAK,EAAC,YAAY;;;EAalB,aAAW,EAAC,iCAAiC;EAC7C,KAAK,EAAC,YAAY;;;EAalB,aAAW,EAAC,4BAA4B;EACxC,KAAK,EAAC,YAAY;;sBAgBjB,KAAK,EAAC,KAAK;;EAEZ,aAAW,EAAC,8BAA8B;EAC1C,KAAK,EAAC,YAAY;;;EAalB,aAAW,EAAC,8BAA8B;EAC1C,KAAK,EAAC,YAAY;;sBAiBf,KAAK,EAAC,KAAK;sBACT,KAAK,EAAC,aAAa;;;;;;;;;;wBA7LlC,oBAsNM;IArNJ,oBASM,OATN,UASM;MARJ,oBAOK;0CANA,YAAK,IAAG,GACX;SACQ,kBAAW;2CADnB,oBAIE,KAJF,UAIE;yCAFiB,kBAAW;;;;;;IAKlC,oBA0MM,OA1MN,UA0MM;MAzMJ,oBAgFM,OAhFN,UAgFM;QA5EJ,aAOE;UANQ,KAAK,EAAE,UAAI;;kDAAJ,UAAI;YAKJ,eAAM;;UAJpB,IAAI,EAAE,WAAI;UACV,KAAK,EAAEA,MAAC;UACR,OAAO,EAAE,oBAAW;UACpB,WAAW,EAAEA,MAAC;;;SAKT,UAAI,IAAI,UAAI;2BADpB,oBAGE,OAHF,UAGE;;;QAEF,mCAAmB;;SAEX,UAAI,eAAe,UAAI;2BAD/B,oBA0BM,OA1BN,UA0BM;cAtBJ,aASE;gBARQ,KAAK,EAAS,aAAO,CAAC,IAAI;;wDAAZ,aAAO,CAAC,IAAI;kBAOnB,eAAM;;gCAPrB,gBAAmC;gBACnC,IAAI,EAAC,QAAQ;gBACb,GAAG,EAAC,GAAG;gBACP,GAAG,EAAC,OAAO;gBACV,IAAI,EAAE,WAAI;gBACV,KAAK,EAAEA,MAAC;gBACR,WAAW,EAAEA,MAAC;;;0CAIjB,oBAA4B,SAAvB,KAAK,EAAC,cAAc;;cAEzB,oBAQM,OARN,UAQM;gBAPJ,aAME;kBALQ,KAAK,EAAE,aAAO,CAAC,IAAI;;0DAAZ,aAAO,CAAC,IAAI;oBAIZ,eAAM;;kBAHpB,IAAI,EAAE,WAAI;kBACV,KAAK,EAAEA,MAAC;kBACR,WAAW,EAAEA,MAAC;;;;;;QAMrB,4BAAY;;SAEJ,UAAI;2BADZ,oBAeM,OAfN,UAeM;cAXJ,aASE;gBARQ,KAAK,EAAS,eAAS,CAAC,IAAI;;wDAAd,eAAS,CAAC,IAAI;kBAOrB,eAAM;;gCAPrB,gBAAqC;gBACrC,IAAI,EAAC,QAAQ;gBACb,GAAG,EAAC,GAAG;gBACP,GAAG,EAAC,OAAO;gBACV,IAAI,EAAE,WAAI;gBACV,KAAK,EAAEA,MAAC;gBACR,WAAW,EAAEA,MAAC;;;0CAGjB,oBAA4B,SAAvB,KAAK,EAAC,cAAc;;;;QAG3B,6BAAa;;SAEL,UAAI;2BADZ,oBAaM,OAbN,UAaM;cATJ,oBAOM,OAPN,WAOM;gBANJ,aAKE;kBAJQ,KAAK,EAAE,UAAI,CAAC,OAAO;;0DAAZ,UAAI,CAAC,OAAO;oBAGZ,eAAM;;kBAFpB,KAAK,EAAEA,MAAC;kBACR,WAAW,EAAEA,MAAC;;;;0CAInB,oBAA4B,SAAvB,KAAK,EAAC,cAAc;;;;;MAI7B,oBAMM,OANN,WAMM;SAJI,UAAI,IAAI,UAAI;2BADpB,oBAIC,MAJD,WAIC;;;;MAGH,6BAAa;;QAEJ,eAAM;yBADf,oBA6GM,OA7GN,WA6GM;YAzGJ,oBA2CM,OA3CN,WA2CM;cA1CJ,oBAaM,OAbN,WAaM;gBATJ,aAQE;kBAPQ,KAAK,EAAE,WAAK,CAAC,aAAa;;0DAAnB,WAAK,CAAC,aAAa;oBAMnB,eAAM;;kBALpB,IAAI,EAAE,WAAI;kBACV,KAAK,EAAEA,MAAC;kBACT,GAAG,EAAC,GAAG;kBACN,MAAM,EAAEA,MAAC;kBACT,WAAW,EAAEA,MAAC;;;;cAInB,oBAaM,OAbN,WAaM;gBATJ,aAQE;kBAPQ,KAAK,EAAE,WAAK,CAAC,mBAAmB;;0DAAzB,WAAK,CAAC,mBAAmB;oBAMzB,eAAM;;kBALpB,IAAI,EAAE,WAAI;kBACV,MAAM,EAAEA,MAAC;kBACT,KAAK,EAAEA,MAAC;kBACT,GAAG,EAAC,GAAG;kBACN,WAAW,EAAEA,MAAC;;;;cAInB,oBAaM,OAbN,WAaM;gBATJ,aAQE;kBAPQ,KAAK,EAAE,WAAK,CAAC,cAAc;;0DAApB,WAAK,CAAC,cAAc;oBAMpB,eAAM;;kBALrB,GAAG,EAAC,GAAG;kBACN,IAAI,EAAE,WAAI;kBACV,MAAM,EAAEA,MAAC;kBACT,KAAK,EAAEA,MAAC;kBACR,WAAW,EAAEA,MAAC;;;;;wCAMrB,oBAA4B,SAAvB,KAAK,EAAC,cAAc;;YAEzB,oBA6BM,OA7BN,WA6BM;cA5BJ,oBAaM,OAbN,WAaM;gBATJ,aAQE;kBAPQ,KAAK,EAAS,WAAK,CAAC,gBAAgB;;0DAAtB,WAAK,CAAC,gBAAgB;oBAM7B,eAAM;;kCANrB,gBAA6C;kBAC7C,IAAI,EAAC,QAAQ;kBACb,GAAG,EAAC,GAAG;kBACN,IAAI,EAAE,WAAI;kBACV,KAAK,EAAEA,MAAC;kBACR,WAAW,EAAEA,MAAC;;;;cAInB,oBAaM,OAbN,WAaM;gBATJ,aAQE;kBAPQ,KAAK,EAAS,WAAK,CAAC,gBAAgB;;0DAAtB,WAAK,CAAC,gBAAgB;oBAM7B,eAAM;;kCANrB,gBAA6C;kBAC7C,IAAI,EAAC,QAAQ;kBACb,GAAG,EAAC,GAAG;kBACN,IAAI,EAAE,WAAI;kBACV,KAAK,EAAEA,MAAC;kBACR,WAAW,EAAEA,MAAC;;;;;aAML,UAAI,eAAe,UAAI;+BAAvC,oBA0BW;8CAzBT,oBAA4B,SAAvB,KAAK,EAAC,cAAc;;kBAEzB,oBAsBM,OAtBN,WAsBM;oBArBJ,oBAoBM,OApBN,WAoBM;sBAnBJ,aAkBW;wBAjBD,KAAK,EAAE,aAAO,CAAC,WAAW;;kEAAnB,aAAO,CAAC,WAAW;0BAUnB,eAAM;;wBATrB,aAAW,EAAC,0BAA0B;wBACtC,UAAQ,EAAC,MAAM;wBACd,IAAI,EAAE,WAAI;wBACV,QAAM,EAAE,KAAK;wBACb,cAAY,EAAE,KAAK;wBACnB,KAAK,EAAEA,MAAC;wBACR,WAAS,EAAEA,MAAC;wBACZ,aAAW,EAAEA,MAAC;wBACd,WAAS,EAAEA,MAAC;;wBAGF,KAAK,WACd,CAEK;0BAFL,oBAEK,6BADAA,MAAC", "sourcesContent": ["<script>\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\nimport { clone } from '@shell/utils/object';\nimport UnitInput from '@shell/components/form/UnitInput';\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport ShellInput from '@shell/components/form/ShellInput';\nimport KeyValue from '@shell/components/form/KeyValue';\n\nconst KINDS = [\n  'none',\n  'HTTP',\n  'HTTPS',\n  'tcp',\n  'exec',\n];\n\nexport default {\n  emits: ['update:value'],\n\n  components: {\n    LabeledInput, LabeledSelect, UnitInput, ShellInput, KeyValue,\n  },\n\n  props: {\n    value: {\n      type:    [Object, null],\n      default: null,\n    },\n    mode: {\n      type:    String,\n      default: _EDIT,\n    },\n\n    label: {\n      type:    String,\n      default: 'Probe',\n    },\n\n    description: {\n      type:    String,\n      default: '',\n    },\n  },\n\n  data() {\n    let kind = 'none';\n    let probe = null;\n    let exec = null;\n    let httpGet = null;\n    let tcpSocket = null;\n\n    if ( this.value ) {\n      probe = clone(this.value);\n\n      if ( probe.exec ) {\n        kind = 'exec';\n      } else if ( probe.httpGet ) {\n        if ( (probe.httpGet.scheme || '').toLowerCase() === 'https' ) {\n          kind = 'HTTPS';\n        } else {\n          kind = 'HTTP';\n        }\n      } else if ( probe.tcpSocket ) {\n        kind = 'tcp';\n      }\n    } else {\n      probe = {\n        failureThreshold:    3,\n        successThreshold:    1,\n        initialDelaySeconds: 0,\n        timeoutSeconds:      1,\n        periodSeconds:       10,\n        exec:                null,\n        httpGet:             null,\n        tcpSocket:           null,\n      };\n    }\n\n    exec = probe.exec || {};\n    httpGet = probe.httpGet || {};\n    tcpSocket = probe.tcpSocket || {};\n\n    return {\n      probe, kind, exec, httpGet, tcpSocket\n    };\n  },\n\n  computed: {\n    isView() {\n      return this.mode === _VIEW;\n    },\n\n    isNone() {\n      return this.kind === 'none';\n    },\n\n    kindOptions() {\n      return KINDS.map((k) => {\n        return { label: this.t(`workload.container.healthCheck.kind.${ k }`), value: k };\n      });\n    }\n  },\n\n  watch: {\n    kind() {\n      this.update();\n    }\n  },\n\n  methods: {\n    update() {\n      const probe = this.probe;\n\n      if ( this.isNone ) {\n        this.$emit('update:value', null);\n\n        return;\n      }\n\n      switch ( this.kind ) {\n      case 'HTTP':\n      case 'HTTPS':\n        this.httpGet.scheme = this.kind;\n        probe.httpGet = this.httpGet;\n        probe.tcpSocket = null;\n        probe.exec = null;\n        break;\n      case 'tcp':\n        probe.httpGet = null;\n        probe.tcpSocket = this.tcpSocket;\n        probe.exec = null;\n        break;\n      case 'exec':\n        probe.httpGet = null;\n        probe.tcpSocket = null;\n        probe.exec = this.exec;\n        break;\n      }\n\n      this.$emit('update:value', probe);\n    }\n  },\n};\n</script>\n\n<template>\n  <div>\n    <div class=\"title clearfix\">\n      <h3>\n        {{ label }}\n        <i\n          v-if=\"description\"\n          v-clean-tooltip=\"description\"\n          class=\"icon icon-info\"\n        />\n      </h3>\n    </div>\n    <div class=\"row\">\n      <div\n        data-testid=\"input-probe-kind\"\n        class=\"col span-11-of-23\"\n      >\n        <LabeledSelect\n          v-model:value=\"kind\"\n          :mode=\"mode\"\n          :label=\"t('probe.type.label')\"\n          :options=\"kindOptions\"\n          :placeholder=\"t('probe.type.placeholder')\"\n          @update:value=\"update\"\n        />\n\n        <div\n          v-if=\"kind && kind!=='none'\"\n          class=\"spacer-small\"\n        />\n\n        <!-- HTTP/HTTPS -->\n        <div\n          v-if=\"kind === 'HTTP' || kind === 'HTTPS'\"\n          data-testid=\"input-probe-port\"\n        >\n          <LabeledInput\n            v-model:value.number=\"httpGet.port\"\n            type=\"number\"\n            min=\"1\"\n            max=\"65535\"\n            :mode=\"mode\"\n            :label=\"t('probe.httpGet.port.label')\"\n            :placeholder=\"t('probe.httpGet.port.placeholder')\"\n            @update:value=\"update\"\n          />\n\n          <div class=\"spacer-small\" />\n\n          <div data-testid=\"input-probe-path\">\n            <LabeledInput\n              v-model:value=\"httpGet.path\"\n              :mode=\"mode\"\n              :label=\"t('probe.httpGet.path.label')\"\n              :placeholder=\"t('probe.httpGet.path.placeholder')\"\n              @update:value=\"update\"\n            />\n          </div>\n        </div>\n\n        <!-- TCP -->\n        <div\n          v-if=\"kind === 'tcp'\"\n          data-testid=\"input-probe-socket\"\n        >\n          <LabeledInput\n            v-model:value.number=\"tcpSocket.port\"\n            type=\"number\"\n            min=\"1\"\n            max=\"65535\"\n            :mode=\"mode\"\n            :label=\"t('probe.httpGet.port.label')\"\n            :placeholder=\"t('probe.httpGet.port.placeholderDeux')\"\n            @update:value=\"update\"\n          />\n          <div class=\"spacer-small\" />\n        </div>\n\n        <!-- Exec -->\n        <div\n          v-if=\"kind === 'exec'\"\n          data-testid=\"input-probe-command\"\n        >\n          <div class=\"col span-12\">\n            <ShellInput\n              v-model:value=\"exec.command\"\n              :label=\"t('probe.command.label')\"\n              :placeholder=\"t('probe.command.placeholder')\"\n              @update:value=\"update\"\n            />\n          </div>\n          <div class=\"spacer-small\" />\n        </div>\n      </div>\n\n      <div class=\"col span-1-of-13\">\n        <hr\n          v-if=\"kind && kind!=='none'\"\n          :style=\"{'position':'relative', 'margin':'0px'}\"\n          class=\"vertical\"\n        >\n      </div>\n\n      <!-- none -->\n      <div\n        v-if=\"!isNone\"\n        class=\"col span-11-of-23\"\n      >\n        <div class=\"row\">\n          <div\n            data-testid=\"input-probe-periodSeconds\"\n            class=\"col span-4\"\n          >\n            <UnitInput\n              v-model:value=\"probe.periodSeconds\"\n              :mode=\"mode\"\n              :label=\"t('probe.checkInterval.label')\"\n              min=\"1\"\n              :suffix=\"t('suffix.sec')\"\n              :placeholder=\"t('probe.checkInterval.placeholder')\"\n              @update:value=\"update\"\n            />\n          </div>\n          <div\n            data-testid=\"input-probe-initialDelaySeconds\"\n            class=\"col span-4\"\n          >\n            <UnitInput\n              v-model:value=\"probe.initialDelaySeconds\"\n              :mode=\"mode\"\n              :suffix=\"t('suffix.sec')\"\n              :label=\"t('probe.initialDelay.label')\"\n              min=\"0\"\n              :placeholder=\"t('probe.initialDelay.placeholder')\"\n              @update:value=\"update\"\n            />\n          </div>\n          <div\n            data-testid=\"input-probe-timeoutSeconds\"\n            class=\"col span-4\"\n          >\n            <UnitInput\n              v-model:value=\"probe.timeoutSeconds\"\n              min=\"0\"\n              :mode=\"mode\"\n              :suffix=\"t('suffix.sec')\"\n              :label=\"t('probe.timeout.label')\"\n              :placeholder=\"t('probe.timeout.placeholder')\"\n              @update:value=\"update\"\n            />\n          </div>\n        </div>\n\n        <div class=\"spacer-small\" />\n\n        <div class=\"row\">\n          <div\n            data-testid=\"input-probe-successThreshold\"\n            class=\"col span-6\"\n          >\n            <LabeledInput\n              v-model:value.number=\"probe.successThreshold\"\n              type=\"number\"\n              min=\"1\"\n              :mode=\"mode\"\n              :label=\"t('probe.successThreshold.label')\"\n              :placeholder=\"t('probe.successThreshold.placeholder')\"\n              @update:value=\"update\"\n            />\n          </div>\n          <div\n            data-testid=\"input-probe-failureThreshold\"\n            class=\"col span-6\"\n          >\n            <LabeledInput\n              v-model:value.number=\"probe.failureThreshold\"\n              type=\"number\"\n              min=\"1\"\n              :mode=\"mode\"\n              :label=\"t('probe.failureThreshold.label')\"\n              :placeholder=\"t('probe.failureThreshold.placeholder')\"\n              @update:value=\"update\"\n            />\n          </div>\n        </div>\n\n        <template v-if=\"kind === 'HTTP' || kind === 'HTTPS'\">\n          <div class=\"spacer-small\" />\n\n          <div class=\"row\">\n            <div class=\"col span-12\">\n              <KeyValue\n                v-model:value=\"httpGet.httpHeaders\"\n                data-testid=\"input-probe-http-headers\"\n                key-name=\"name\"\n                :mode=\"mode\"\n                :as-map=\"false\"\n                :read-allowed=\"false\"\n                :title=\"t('probe.httpGet.headers.label')\"\n                :key-label=\"t('generic.name')\"\n                :value-label=\"t('generic.value')\"\n                :add-label=\"t('generic.add')\"\n                @update:value=\"update\"\n              >\n                <template #title>\n                  <h3>\n                    {{ t('workload.container.healthCheck.httpGet.headers') }}\n                  </h3>\n                </template>\n              </KeyValue>\n            </div>\n          </div>\n        </template>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n\n  .title {\n    margin-bottom: 10px;\n  }\n  :deep() .labeled-select {\n    height: auto;\n  }\n\n</style>\n"]}]}