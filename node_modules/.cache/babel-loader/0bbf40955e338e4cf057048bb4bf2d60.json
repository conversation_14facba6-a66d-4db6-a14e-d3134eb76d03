{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??clonedRuleSet-44.use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownMenu.vue?vue&type=template&id=31521b37&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownMenu.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownMenu.vue?vue&type=template&id=31521b37&ts=true", "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownMenu.vue"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,cAAc,IAAI,eAAe,EAAE,OAAO,IAAI,QAAQ,EAAE,WAAW,IAAI,YAAY,EAAE,UAAU,IAAI,WAAW,EAAE,QAAQ,IAAI,SAAS,EAAE,SAAS,IAAI,UAAU,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,WAAW,IAAI,YAAY,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,eAAe,IAAI,gBAAgB,EAAE,eAAe,IAAI,gBAAgB,EAAE,MAAM,KAAK,CAAA;AAE5Z,MAAM,UAAU,MAAM,CAAC,IAAS,EAAC,MAAW,EAAC,MAAW,EAAC,MAAW,EAAC,KAAU,EAAC,QAAa;IAC3F,OAAO,CAAC,UAAU,EAAE,ECoBpB,YAAA,CA2Cc,MAAA,CAAA,YAAA,CAAA,EAAA;QA1CX,YAAU,EAAE,MAAA,CAAA,iBAAiB;QAC7B,eAAW,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAG,CAAU,EAAA,EAAA,CAAK,MAAA,CAAA,IAAI,CAAA,aAAA,EAAgB,CAAC,CAAA,CAAA;KDnBpD,EAAE;QC6BU,kBAAkB,EAAA,QAAA,CAEzB,GAAsB,EAAA,CAAA;YD7BxB,CAAC,UAAU,CAAC,IAAI,CAAC,EC4BjB,mBAAA,CAsBW,SAAA,EAAA,IAAA,EAAA,WAAA,CArBK,MAAA,CAAA,OAAO,EAAA,CAAb,CAAC,EAAA,EAAA;gBD5BT,OAAO,CAAC,UAAU,EAAE,EAAE,mBAAmB,CAAC,SAAS,EAAE;oBACnD,GAAG,EC4BC,CAAC,CAAC,KAAK;iBD3BZ,EAAE;oBACD,CAAC,CC6BM,CAAC,CAAC,OAAO,CAAA;wBD5Bd,CAAC,CAAC,CAAC,UAAU,EAAE,EC2BnB,YAAA,CAcmB,MAAA,CAAA,gBAAA,CAAA,EAAA;4BDxCX,GAAG,EAAE,CAAC;4BC4BX,OAAK,EAAA,CAAG,CAAa,EAAA,EAAA,CAAK,MAAA,CAAA,IAAI,CAAA,QAAA,EAAW,CAAC,EAAE,CAAC,CAAA;yBD1BzC,EAAE;4BC4BI,MAAM,EAAA,QAAA,CAOzB,GAKiB,EAAA,CAAA;gCDtCD,CC4BE,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,CAAA;oCD3Bf,CAAC,CAAC,CAAC,UAAU,EAAE,EC0BvB,YAAA,CAME,MAAA,CAAA,WAAA,CAAA,EAAA;wCD/BU,GAAG,EAAE,CAAC;wCC2Bf,IAAI,EAAE,CAAC,CAAC,IAAI;wCACZ,GAAG,EAAE,CAAC,CAAC,GAAG;wCACX,KAAK,EAAC,MAAM;wCACZ,KAAK,EAAC,QAAQ;qCDzBL,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;oCAC3C,CAAC,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;6BACtC,CAAC;4BACF,OAAO,EAAE,QAAQ,CCwBZ,GACX,EAAA,CAAA;gCDxBQ,gBAAgB,CCuBb,GACX,GAAA,gBAAA,CAAG,CAAC,CAAC,KAAK,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;6BDvBH,CAAC;4BACF,CAAC,EAAE,CAAC,CAAC,aAAa;yBACnB,EAAE,IAAI,CAAC,0BAA0B,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;wBACnD,CAAC,CAAC,CAAC,UAAU,EAAE,ECsBnB,YAAA,CAEE,MAAA,CAAA,qBAAA,CAAA,EAAA,EAAA,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;iBDvBD,EAAE,EAAE,CAAC,qBAAqB,CAAC,CAAC,CAAA;YAC/B,CAAC,CAAC,EAAE,GAAG,CAAC,oBAAoB,CAAC,CAAC;YAC9B,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;YAC7C,CAAC,CCuBQ,MAAA,CAAA,UAAU,CAAC,MAAA,CAAA,OAAO,CAAA,CAAA;gBDtBzB,CAAC,CAAC,CAAC,UAAU,EAAE,ECqBjB,YAAA,CAKmB,MAAA,CAAA,gBAAA,CAAA,EAAA;oBDzBb,GAAG,EAAE,CAAC;oBCsBV,QAAQ,EAAR,EAAQ;iBDpBL,EAAE;oBACD,OAAO,EAAE,QAAQ,CCoBtB,GAED,EAAA,CAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA;wBDrBQ,gBAAgB,CCmBvB,wCAED,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA;qBDpBO,CAAC,CAAC;oBACH,CAAC,EAAE,CAAC,CAAC,YAAY;oBACjB,EAAE,EAAE,CAAC,CAAC,CAAC;iBACR,CAAC,CAAC;gBACL,CAAC,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;SACtC,CAAC;QACF,OAAO,EAAE,QAAQ,CCvBjB,GAOsB,EAAA,CAAA;YAPtB,YAAA,CAOsB,MAAA,CAAA,mBAAA,CAAA,EAAA,eAAA,CAAA;gBANnB,CAAA,MAAA,CAAA,UAAA,IAAA,EAAA,CAAY,EAAE,IAAI;gBAClB,CAAA,MAAA,CAAA,UAAA,IAAA,EAAA,CAAY,EAAE,IAAI;gBAClB,aAAW,EAAE,MAAA,CAAA,UAAU;gBACvB,YAAU,EAAE,MAAA,CAAA,eAAe;aDyB3B,CAAC,EAAE;gBACF,OAAO,EAAE,QAAQ,CCxBnB,GAA+B,EAAA,CAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA;oBAA/B,mBAAA,CAA+B,GAAA,EAAA,EAA5B,KAAK,EAAC,mBAAmB,EAAA,EAAA,IAAA,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA;iBD0BzB,CAAC,CAAC;gBACH,CAAC,EAAE,CAAC,CAAC,YAAY;gBACjB,EAAE,EAAE,CAAC,CAAC,CAAC;aACR,EAAE,EAAE,CAAC,gBAAgB,EAAE,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;YACtD,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;SAC9C,CAAC;QACF,CAAC,EAAE,CAAC,CAAC,YAAY;QACjB,EAAE,EAAE,CAAC,CAAC,CAAC;KACR,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAA;AACpC,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownMenu.vue.tsx", "sourceRoot": "", "sourcesContent": ["import { createElementVNode as _createElementVNode, normalizeProps as _normalizeProps, withCtx as _withCtx, createVNode as _createVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode } from \"vue\"\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  return (_openBlock(), _createBlock($setup[\"RcDropdown\"], {\n    \"aria-label\": $props.dropdownAriaLabel,\n    \"onUpdate:open\": _cache[0] || (_cache[0] = (e) => $setup.emit('update:open', e))\n  }, {\n    dropdownCollection: _withCtx(() => [\n      (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($props.options, (a) => {\n        return (_openBlock(), _createElementBlock(_Fragment, {\n          key: a.label\n        }, [\n          (!a.divider)\n            ? (_openBlock(), _createBlock($setup[\"RcDropdownItem\"], {\n                key: 0,\n                onClick: (e) => $setup.emit('select', e, a)\n              }, {\n                before: _withCtx(() => [\n                  (a.icon || a.svg)\n                    ? (_openBlock(), _createBlock($setup[\"IconOrSvg\"], {\n                        key: 0,\n                        icon: a.icon,\n                        src: a.svg,\n                        class: \"icon\",\n                        color: \"header\"\n                      }, null, 8 /* PROPS */, [\"icon\", \"src\"]))\n                    : _createCommentVNode(\"v-if\", true)\n                ]),\n                default: _withCtx(() => [\n                  _createTextVNode(\" \" + _toDisplayString(a.label), 1 /* TEXT */)\n                ]),\n                _: 2 /* DYNAMIC */\n              }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]))\n            : (_openBlock(), _createBlock($setup[\"RcDropdownSeparator\"], { key: 1 }))\n        ], 64 /* STABLE_FRAGMENT */))\n      }), 128 /* KEYED_FRAGMENT */)),\n      _cache[3] || (_cache[3] = _createTextVNode()),\n      (!$setup.hasOptions($props.options))\n        ? (_openBlock(), _createBlock($setup[\"RcDropdownItem\"], {\n            key: 0,\n            disabled: \"\"\n          }, {\n            default: _withCtx(() => _cache[2] || (_cache[2] = [\n              _createTextVNode(\"\\n        No actions available\\n      \", -1 /* CACHED */)\n            ])),\n            _: 1 /* STABLE */,\n            __: [2]\n          }))\n        : _createCommentVNode(\"v-if\", true)\n    ]),\n    default: _withCtx(() => [\n      _createVNode($setup[\"RcDropdownTrigger\"], _normalizeProps({\n        [$props.buttonRole || \"\"]: true,\n        [$props.buttonSize || \"\"]: true,\n        \"data-testid\": $props.dataTestid,\n        \"aria-label\": $props.buttonAriaLabel\n      }), {\n        default: _withCtx(() => _cache[1] || (_cache[1] = [\n          _createElementVNode(\"i\", { class: \"icon icon-actions\" }, null, -1 /* CACHED */)\n        ])),\n        _: 1 /* STABLE */,\n        __: [1]\n      }, 16 /* FULL_PROPS */, [\"data-testid\", \"aria-label\"]),\n      _cache[4] || (_cache[4] = _createTextVNode())\n    ]),\n    _: 1 /* STABLE */,\n    __: [4]\n  }, 8 /* PROPS */, [\"aria-label\"]))\n}", "<script setup lang=\"ts\">\nimport {\n  Rc<PERSON><PERSON>down,\n  Rc<PERSON>ropdownItem,\n  RcDropdownSeparator,\n  RcDropdownTrigger\n} from '@components/RcDropdown';\nimport { RcDropdownMenuComponentProps, DropdownOption } from './types';\nimport IconOrSvg from '@shell/components/IconOrSvg';\n\nwithDefaults(defineProps<RcDropdownMenuComponentProps>(), {\n  buttonRole: 'primary',\n  buttonSize: undefined,\n});\n\nconst emit = defineEmits(['update:open', 'select']);\n\nconst hasOptions = (options: DropdownOption[]) => {\n  return options.length !== undefined ? options.length : Object.keys(options).length > 0;\n};\n</script>\n\n<template>\n  <rc-dropdown\n    :aria-label=\"dropdownAriaLabel\"\n    @update:open=\"(e: boolean) => emit('update:open', e)\"\n  >\n    <rc-dropdown-trigger\n      :[buttonRole]=\"true\"\n      :[buttonSize]=\"true\"\n      :data-testid=\"dataTestid\"\n      :aria-label=\"buttonAriaLabel\"\n    >\n      <i class=\"icon icon-actions\" />\n    </rc-dropdown-trigger>\n    <template #dropdownCollection>\n      <template\n        v-for=\"(a) in options\"\n        :key=\"a.label\"\n      >\n        <rc-dropdown-item\n          v-if=\"!a.divider\"\n          @click=\"(e: MouseEvent) => emit('select', e, a)\"\n        >\n          <template #before>\n            <IconOrSvg\n              v-if=\"a.icon || a.svg\"\n              :icon=\"a.icon\"\n              :src=\"a.svg\"\n              class=\"icon\"\n              color=\"header\"\n            />\n          </template>\n          {{ a.label }}\n        </rc-dropdown-item>\n        <rc-dropdown-separator\n          v-else\n        />\n      </template>\n      <rc-dropdown-item\n        v-if=\"!hasOptions(options)\"\n        disabled\n      >\n        No actions available\n      </rc-dropdown-item>\n    </template>\n  </rc-dropdown>\n</template>\n"]}]}