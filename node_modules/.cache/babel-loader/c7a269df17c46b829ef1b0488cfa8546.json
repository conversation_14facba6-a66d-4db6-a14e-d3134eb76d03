{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Reference.vue?vue&type=template&id=57fe6436", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Reference.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Reference.vue"], "names": ["$fetchState", "disabled", "displayLabel", "question", "value", "displayTooltip", "$emit", "showDescription", "mode"], "mappings": ";;;;EAoHI,KAAK,EAAC,KAAK;;qBAEN,KAAK,EAAC,YAAY;qBAelB,KAAK,EAAC,kBAAkB;;;;;EAS7B,KAAK,EAAC,KAAK;;qBAEN,KAAK,EAAC,YAAY;qBAYlB,KAAK,EAAC,kBAAkB;;;;;;;;UAzCvB,gBAAU;qBADlB,oBAyBM,OAzBN,UAyBM;QArBJ,oBAcM,OAdN,UAcM;UAbJ,aAYE;YAXC,eAAa,EAAE,cAAQ;YACvB,UAAQ,EAAE,cAAO;YACjB,QAAQ,EAAEA,gBAAW,CAAC,OAAO,IAAIC,aAAQ;YACzC,KAAK,EAAEC,iBAAY;YACnB,WAAW,EAAEC,aAAQ,CAAC,WAAW;YACjC,QAAQ,EAAEA,aAAQ,CAAC,QAAQ;YAC3B,KAAK,EAAEC,UAAK;YACZ,OAAO,EAAEC,mBAAc;YACvB,6BAA2B,EAAE,6BAAuB;YACpD,wBAAsB,EAAE,yBAAmB;YAC3C,gBAAY,wCAAGL,gBAAW,CAAC,OAAO,IAAIM,UAAK,iBAAiB,MAAM;;;;QAGvE,oBAKM,OALN,UAKM;4CAJD,gBAAU,CAAC,UAAU,CAAC,IAAI;WAAe,qBAAY;6BAAxB,oBAAoE,oBAA1C,gBAAc,oBAAG,sBAAe;;;WAC/EC,oBAAe;6BAA1B,oBAEM,oCADDJ,aAAQ,CAAC,WAAW;;;;qBAI7B,oBAyBM,OAzBN,UAyBM;QArBJ,oBAWM,OAXN,UAWM;UAVJ,aASE;YARC,IAAI,EAAEK,SAAI;YACV,QAAQ,EAAER,gBAAW,CAAC,OAAO,IAAIC,aAAQ;YACzC,KAAK,EAAEC,iBAAY;YACnB,WAAW,EAAEC,aAAQ,CAAC,WAAW;YACjC,QAAQ,EAAEA,aAAQ,CAAC,QAAQ;YAC3B,KAAK,EAAEC,UAAK;YACZ,OAAO,EAAEC,mBAAc;YACvB,gBAAY,wCAAGL,gBAAW,CAAC,OAAO,IAAIM,UAAK,iBAAiB,MAAM;;;;QAGvE,oBAQM,OARN,UAQM;4CAPDH,aAAQ,CAAC,IAAI;WAAe,qBAAY;6BAAxB,oBAAoE,oBAA1C,gBAAc,oBAAG,sBAAe;;;WAClEI,oBAAe;6BAA1B,oBAEM,qCADDJ,aAAQ,CAAC,WAAW;;;oCAEzB,oBAEM,SAFD,KAAK,EAAC,YAAY,IAAC,8DAExB", "sourcesContent": ["<script>\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport ResourceLabeledSelect from '@shell/components/form/ResourceLabeledSelect.vue';\nimport { PaginationParamFilter } from '@shell/types/store/pagination.types';\n\nimport { PVC, STORAGE_CLASS } from '@shell/config/types';\nimport Question from './Question';\n\n// Older versions of rancher document these words as valid types\nconst LEGACY_MAP = {\n  storageclass: STORAGE_CLASS,\n  pvc:          PVC,\n};\n\nexport default {\n  emits: ['update:value'],\n\n  components: { LabeledInput, ResourceLabeledSelect },\n  mixins:     [Question],\n\n  props: {\n    inStore: {\n      type:    String,\n      default: 'cluster',\n    },\n\n    targetNamespace: {\n      type:    String,\n      default: null,\n    },\n  },\n\n  data() {\n    const t = this.question.type;\n\n    let typeName;\n\n    const match = t.match(/^reference\\[(.*)\\]$/);\n\n    if ( match ) {\n      typeName = match?.[1];\n    } else {\n      typeName = LEGACY_MAP[t] || t;\n    }\n\n    let typeSchema;\n\n    if ( typeName ) {\n      typeSchema = this.$store.getters[`${ this.inStore }/schemaFor`](typeName);\n    }\n\n    return {\n      typeName,\n      typeSchema,\n      all:                 [],\n      allResourceSettings: {\n        updateResources: (all) => {\n          // Filter to only include required namespaced resources\n          const resources = this.isNamespaced ? all.filter((r) => r.metadata.namespace === this.targetNamespace) : all;\n\n          return this.mapResourcesToOptions(resources);\n        }\n      },\n      paginateResourceSetting: {\n        updateResources: (resources) => {\n          return this.mapResourcesToOptions(resources);\n        },\n        /**\n          * of type PaginateTypeOverridesFn\n          * @param [LabelSelectPaginationFunctionOptions] opts\n          * @returns LabelSelectPaginationFunctionOptions\n         */\n        requestSettings: (opts) => {\n          // Filter to only include required namespaced resources\n          const filters = this.isNamespaced ? [\n            PaginationParamFilter.createSingleField({ field: 'metadata.namespace', value: this.targetNamespace }),\n          ] : [];\n\n          return {\n            ...opts,\n            filters,\n            groupByNamespace: false,\n            classify:         true,\n          };\n        }\n      },\n    };\n  },\n\n  methods: {\n    mapResourcesToOptions(resources) {\n      return resources.map((r) => {\n        if (r.id) {\n          return {\n            label: r.nameDisplay || r.metadata.name,\n            value: r.metadata.name\n          };\n        } else {\n          return r;\n        }\n      });\n    },\n\n  },\n\n  computed: {\n    isNamespaced() {\n      return !!this.typeSchema?.attributes?.namespaced;\n    },\n  },\n};\n</script>\n\n<template>\n  <div\n    v-if=\"typeSchema\"\n    class=\"row\"\n  >\n    <div class=\"col span-6\">\n      <ResourceLabeledSelect\n        :resource-type=\"typeName\"\n        :in-store=\"inStore\"\n        :disabled=\"$fetchState.pending || disabled\"\n        :label=\"displayLabel\"\n        :placeholder=\"question.description\"\n        :required=\"question.required\"\n        :value=\"value\"\n        :tooltip=\"displayTooltip\"\n        :paginated-resource-settings=\"paginateResourceSetting\"\n        :all-resources-settings=\"allResourceSettings\"\n        @update:value=\"!$fetchState.pending && $emit('update:value', $event)\"\n      />\n    </div>\n    <div class=\"col span-6 mt-10\">\n      {{ typeSchema.attributes.kind }}<span v-if=\"isNamespaced\"> in namespace {{ targetNamespace }}</span>\n      <div v-if=\"showDescription\">\n        {{ question.description }}\n      </div>\n    </div>\n  </div>\n  <div\n    v-else\n    class=\"row\"\n  >\n    <div class=\"col span-6\">\n      <LabeledInput\n        :mode=\"mode\"\n        :disabled=\"$fetchState.pending || disabled\"\n        :label=\"displayLabel\"\n        :placeholder=\"question.description\"\n        :required=\"question.required\"\n        :value=\"value\"\n        :tooltip=\"displayTooltip\"\n        @update:value=\"!$fetchState.pending && $emit('update:value', $event)\"\n      />\n    </div>\n    <div class=\"col span-6 mt-10\">\n      {{ question.type }}<span v-if=\"isNamespaced\"> in namespace {{ targetNamespace }}</span>\n      <div v-if=\"showDescription\">\n        {{ question.description }}\n      </div>\n      <div class=\"text-error\">\n        (You do not have access to list this type)\n      </div>\n    </div>\n  </div>\n</template>\n"]}]}