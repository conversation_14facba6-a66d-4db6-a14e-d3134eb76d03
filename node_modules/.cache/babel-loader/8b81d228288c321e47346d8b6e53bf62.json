{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??clonedRuleSet-44.use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/util/aws.ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/util/aws.ts", "mtime": 1754992537325}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGNvbnN0IE1BTkFHRURfVEVNUExBVEVfUFJFRklYID0gJ3JhbmNoZXItbWFuYWdlZC1sdCc7Ci8vIGF3cyB0YWdzIGFyZSBpbiB0aGUgZm9ybSBbe0tleTogJ2tleTEnLCBWYWx1ZTogJ3ZhbDEnfSwge0tleTogJ2tleTInLCBWYWx1ZTogJ3ZhbDJ9XQovLyByYW5jaGVyIGVrc2NvbmZpZyB0YWdzIGFyZSBpbiB0aGUgZm9ybSB7a2V5MTp2YWwxLCBrZXkyOnZhbDJ9CmV4cG9ydCBmdW5jdGlvbiBwYXJzZVRhZ3MoYXdzVGFncykgewogICAgcmV0dXJuIGF3c1RhZ3MucmVkdWNlKChvdXQsIHRhZykgPT4gewogICAgICAgIG91dFt0YWcuS2V5XSA9IHRhZy5WYWx1ZTsKICAgICAgICByZXR1cm4gb3V0OwogICAgfSwge30pOwp9Cg=="}, {"version": 3, "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/util/aws.ts", "sourceRoot": "", "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/util/aws.ts"], "names": [], "mappings": "AAAA,MAAM,CAAC,MAAM,uBAAuB,GAAG,oBAAoB,CAAC;AAE5D,uFAAuF;AACvF,gEAAgE;AAChE,MAAM,UAAU,SAAS,CAAC,OAAuC;IAC/D,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QACjC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC;QAEzB,OAAO,GAAG,CAAC;IACb,CAAC,EAAE,EAA6B,CAAC,CAAC;AACpC,CAAC", "sourcesContent": ["export const MANAGED_TEMPLATE_PREFIX = 'rancher-managed-lt';\n\n// aws tags are in the form [{Key: 'key1', Value: 'val1'}, {Key: 'key2', Value: 'val2}]\n// rancher eksconfig tags are in the form {key1:val1, key2:val2}\nexport function parseTags(awsTags: {Key: string, Value: string}[]): {[key: string]: string} {\n  return awsTags.reduce((out, tag) => {\n    out[tag.Key] = tag.Value;\n\n    return out;\n  }, {} as {[key: string]: string});\n}\n"]}]}