{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/utils/width.js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/utils/width.js", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:LyoqCiAqIFNldHMgdGhlIHdpZHRoIG9mIGEgRE9NIGVsZW1lbnQuIEFkYXB0ZWQgZnJvbSBbeW91bWlnaHRub3RuZWVkanF1ZXJ5LmNvbV0oaHR0cHM6Ly95b3VtaWdodG5vdG5lZWRqcXVlcnkuY29tLyNzZXRfd2lkdGgpCiAqIEBwYXJhbSB7RWxlbWVudH0gZWwgLSBUaGUgdGFyZ2V0IERPTSBlbGVtZW50CiAqIEBwYXJhbSB7ZnVuY3Rpb24gfCBzdHJpbmcgfCBudW1iZXJ9IHZhbCAtIFRoZSBkZXNpcmVkIHdpZHRoIHJlcHJlc2VudGVkIGFzIGEgTnVtYmVyCiAqLwpleHBvcnQgZnVuY3Rpb24gc2V0V2lkdGgoZWwsIHZhbCkgewogIGlmICghZWwpIHsKICAgIHJldHVybjsKICB9CgogIGlmICh0eXBlb2YgdmFsID09PSAnZnVuY3Rpb24nKSB7CiAgICB2YWwgPSB2YWwoKTsKICB9CgogIGlmICh0eXBlb2YgdmFsID09PSAnc3RyaW5nJykgewogICAgZWwuc3R5bGUud2lkdGggPSB2YWw7CgogICAgcmV0dXJuOwogIH0KCiAgZWwuc3R5bGUud2lkdGggPSBgJHsgdmFsIH1weGA7Cn0KCi8qKgogKiBHZXRzIHRoZSB3aWR0aCBvZiBhIERPTSBlbGVtZW50LiBBZGFwdGVkIGZyb20gW3lvdW1pZ2h0bm90bmVlZGpxdWVyeS5jb21dKGh0dHBzOi8veW91bWlnaHRub3RuZWVkanF1ZXJ5LmNvbS8jZ2V0X3dpZHRoKQogKiBAcGFyYW0ge0VsZW1lbnR9IGVsIC0gVGhlIHRhcmdldCBET00gZWxlbWVudAogKiBAcmV0dXJucyBOdW1iZXIgcmVwcmVzZW50aW5nIHRoZSB3aWR0aCBmb3IgdGhlIHByb3ZpZGVkIGVsZW1lbnQKICovCmV4cG9ydCBmdW5jdGlvbiBnZXRXaWR0aChlbCkgewogIGlmICghZWwgfHwgIWVsLmxlbmd0aCkgewogICAgcmV0dXJuOwogIH0KCiAgaWYgKGVsLmxlbmd0aCkgewogICAgcmV0dXJuIHBhcnNlRmxvYXQoZ2V0Q29tcHV0ZWRTdHlsZShlbFswXSkud2lkdGgucmVwbGFjZSgncHgnLCAnJykpOwogIH0gZWxzZSB7CiAgICByZXR1cm4gcGFyc2VGbG9hdChnZXRDb21wdXRlZFN0eWxlKGVsKS53aWR0aC5yZXBsYWNlKCdweCcsICcnKSk7CiAgfQp9Cg=="}]}