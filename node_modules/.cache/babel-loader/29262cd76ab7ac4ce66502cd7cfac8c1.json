{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??clonedRuleSet-44.use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/LabeledTooltip/LabeledTooltip.vue?vue&type=template&id=7ac56810&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/LabeledTooltip/LabeledTooltip.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/LabeledTooltip/LabeledTooltip.vue?vue&type=template&id=7ac56810&ts=true", "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/LabeledTooltip/LabeledTooltip.vue"], "names": ["status", "hover", "value", "iconClass", "tooltipContent", "isObject"], "mappings": "AAAA,OAAO,EAAE,cAAc,IAAI,eAAe,EAAE,gBAAgB,IAAI,iBAAiB,EAAE,cAAc,IAAI,eAAe,EAAE,SAAS,IAAI,UAAU,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,eAAe,IAAI,gBAAgB,EAAE,eAAe,IAAI,gBAAgB,EAAE,QAAQ,IAAI,SAAS,EAAE,MAAM,KAAK,CAAA;AAE5X,MAAM,UAAU,GAAG;IACjB,GAAG,EAAE,CAAC;ICwEA,KAAK,EAAC,SAAS;IACf,aAAW,EAAC,QAAQ;CDtE3B,CAAA;AACD,MAAM,UAAU,GAAG,ECwEN,KAAK,EAAC,eAAe,EAAA,CAAA;ADtElC,MAAM,UAAU,MAAM,CAAC,IAAS,EAAC,MAAW,EAAC,MAAW,EAAC,MAAW,EAAC,KAAU,EAAC,QAAa;IAC3F,MAAM,wBAAwB,GAAG,iBAAiB,CAAC,eAAe,CAAE,CAAA;IACpE,MAAM,8BAA8B,GAAG,iBAAiB,CAAC,qBAAqB,CAAE,CAAA;IAEhF,OAAO,CAAC,UAAU,EAAE,ECyCpB,mBAAA,CA8BM,KAAA,EAAA;QA7BJ,GAAG,EAAC,WAAW;QACf,KAAK,EAAA,eAAA,CAAA,CAAC,iBAAiB,EAAA,EAAA,CACbA,IAAAA,CAAAA,MAAM,CAAA,EAAA,IAAA,EAAA,SAAA,EAAoBC,IAAAA,CAAAA,KAAK,EAAA,CAAA,CAAA;KDzC1C,EAAE;QACD,CC0CgBA,IAAAA,CAAAA,KAAK,CAAA;YDzCnB,CAAC,CAAC,eAAe,CAAC,CAAC,UAAU,EAAE,EC0C/B,mBAAA,CAME,GAAA,EAAA;gBD/CE,GAAG,EAAE,CAAC;gBC4CP,KAAK,EAAA,eAAA,CAAA,CAAA,EAAA,OAAA,EAAA,CAAYC,IAAAA,CAAAA,KAAK,EAAA,CAAGC,IAAAA,CAAAA,SAAS,CAAA,EAAA,IAAA,EAAA,EAC7B,kBAAkB,CAAA,CAAA;gBACxB,QAAQ,EAAC,GAAG;aD3CX,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,EAAE;gBACxB,CAAC,wBAAwB,ECsCVC,IAAAA,CAAAA,cAAc,CAAA;gBDrC7B,CAAC,8BAA8B,ECsCVC,IAAAA,CAAAA,QAAQ,CAACH,IAAAA,CAAAA,KAAK,CAAA,CAAA,CAAA,CAAIA,IAAAA,CAAAA,KAAK,CAAC,OAAO,CAAA,CAAA,CAAGA,IAAAA,CAAAA,KAAK,CAAA;aDrC7D,CAAC;YACJ,CAAC,CAAC,CAAC,UAAU,EAAE,EC0CjB,mBAAA,CAeW,SAAA,EAAA,EAAA,GAAA,EAAA,CAAA,EAAA,EAAA;gBAdT,mBAAA,CAGE,GAAA,EAAA;oBAFC,KAAK,EAAA,eAAA,CAAA,CAAA,EAAA,OAAA,EAAA,CAAYA,IAAAA,CAAAA,KAAK,EAAA,EACjB,kBAAkB,CAAA,CAAA;iBD1CrB,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,CAAC;gBACvB,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;gBAC7C,CC2CIA,IAAAA,CAAAA,KAAK,CAAA;oBD1CP,CAAC,CAAC,CAAC,UAAU,EAAE,ECyCrB,mBAAA,CASM,KAAA,EATN,UASM,EAAA;wBDjDI,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GC6C/B,mBAAA,CAA6B,KAAA,EAAA,EAAxB,KAAK,EAAC,eAAe,EAAA,EAAA,IAAA,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA;wBD5ClB,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;wBC6CrD,mBAAA,CAEM,KAAA,EAFN,UAEM,EAAA,gBAAA,CADDA,IAAAA,CAAAA,KAAK,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;qBD5CH,CAAC,CAAC;oBACL,CAAC,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;aACtC,EAAE,EAAE,CAAC,qBAAqB,CAAC,CAAC;KAClC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAA;AACpB,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/LabeledTooltip/LabeledTooltip.vue.tsx", "sourceRoot": "", "sourcesContent": ["import { normalizeClass as _normalizeClass, resolveDirective as _resolveDirective, withDirectives as _withDirectives, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, Fragment as _Fragment } from \"vue\"\n\nconst _hoisted_1 = {\n  key: 0,\n  class: \"tooltip\",\n  \"x-placement\": \"bottom\"\n}\nconst _hoisted_2 = { class: \"tooltip-inner\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _directive_clean_tooltip = _resolveDirective(\"clean-tooltip\")!\n  const _directive_stripped_aria_label = _resolveDirective(\"stripped-aria-label\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", {\n    ref: \"container\",\n    class: _normalizeClass([\"labeled-tooltip\", {[_ctx.status]: true, hoverable: _ctx.hover}])\n  }, [\n    (_ctx.hover)\n      ? _withDirectives((_openBlock(), _createElementBlock(\"i\", {\n          key: 0,\n          class: _normalizeClass([{'hover':!_ctx.value, [_ctx.iconClass]: true}, \"icon status-icon\"]),\n          tabindex: \"0\"\n        }, null, 2 /* CLASS */)), [\n          [_directive_clean_tooltip, _ctx.tooltipContent],\n          [_directive_stripped_aria_label, _ctx.isObject(_ctx.value) ? _ctx.value.content : _ctx.value]\n        ])\n      : (_openBlock(), _createElementBlock(_Fragment, { key: 1 }, [\n          _createElementVNode(\"i\", {\n            class: _normalizeClass([{'hover':!_ctx.value}, \"icon status-icon\"])\n          }, null, 2 /* CLASS */),\n          _cache[2] || (_cache[2] = _createTextVNode()),\n          (_ctx.value)\n            ? (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n                _cache[0] || (_cache[0] = _createElementVNode(\"div\", { class: \"tooltip-arrow\" }, null, -1 /* CACHED */)),\n                _cache[1] || (_cache[1] = _createTextVNode()),\n                _createElementVNode(\"div\", _hoisted_2, _toDisplayString(_ctx.value), 1 /* TEXT */)\n              ]))\n            : _createCommentVNode(\"v-if\", true)\n        ], 64 /* STABLE_FRAGMENT */))\n  ], 2 /* CLASS */))\n}", "<script lang=\"ts\">\nimport { defineComponent } from 'vue';\n\nexport default defineComponent({\n  props: {\n    /**\n     * The Labeled Tooltip value.\n     */\n    value: {\n      type:    [String, Object],\n      default: null\n    },\n\n    /**\n     * The status for the Labeled Tooltip. Controls the Labeled Tooltip class.\n     * @values info, success, warning, error\n     */\n    status: {\n      type:    String,\n      default: 'error'\n    },\n\n    /**\n     * Displays the Labeled Tooltip on mouse hover.\n     */\n    hover: {\n      type:    Boolean,\n      default: true\n    }\n  },\n  computed: {\n    iconClass(): string {\n      return this.status === 'error' ? 'icon-warning' : 'icon-info';\n    },\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    tooltipContent(): {[key: string]: any} | string {\n      if (this.isObject(this.value)) {\n        return {\n          ...{ content: this.value.content, popperClass: [`tooltip-${ status }`] }, ...this.value, triggers: ['hover', 'touch', 'focus']\n        };\n      }\n\n      return this.value ? { content: this.value, triggers: ['hover', 'touch', 'focus'] } : '';\n    }\n  },\n  methods: {\n    isObject(value: string | Record<string, unknown>): value is Record<string, unknown> {\n      return typeof value === 'object' && value !== null && !!value.content;\n    }\n  }\n});\n</script>\n\n<template>\n  <div\n    ref=\"container\"\n    class=\"labeled-tooltip\"\n    :class=\"{[status]: true, hoverable: hover}\"\n  >\n    <template v-if=\"hover\">\n      <i\n        v-clean-tooltip=\"tooltipContent\"\n        v-stripped-aria-label=\"isObject(value) ? value.content : value\"\n        :class=\"{'hover':!value, [iconClass]: true}\"\n        class=\"icon status-icon\"\n        tabindex=\"0\"\n      />\n    </template>\n    <template v-else>\n      <i\n        :class=\"{'hover':!value}\"\n        class=\"icon status-icon\"\n      />\n      <div\n        v-if=\"value\"\n        class=\"tooltip\"\n        x-placement=\"bottom\"\n      >\n        <div class=\"tooltip-arrow\" />\n        <div class=\"tooltip-inner\">\n          {{ value }}\n        </div>\n      </div>\n    </template>\n  </div>\n</template>\n\n<style lang='scss'>\n.labeled-tooltip {\n    position: absolute;\n    width: 100%;\n    height: 100%;\n    left: 0;\n    top: 0;\n\n    &.hoverable {\n      height: 0%;\n    }\n\n     .status-icon {\n        position:  absolute;\n        right: 30px;\n        top: $input-padding-lg;\n        z-index: z-index(hoverOverContent);\n     }\n\n    @mixin tooltipColors($color) {\n        .status-icon {\n            color: $color;\n        }\n    }\n\n    &.error {\n        @include tooltipColors(var(--error));\n\n        .status-icon {\n          top: 7px;\n          right: 5px;\n        }\n    }\n\n    &.warning {\n        @include tooltipColors(var(--warning));\n    }\n\n    &.success {\n        @include tooltipColors(var(--success));\n    }\n}\n\n// Ensure code blocks inside tootips don't look awful\n.v-popper__popper.v-popper--theme-tooltip {\n  .v-popper__inner {\n    pre {\n      padding: 2px;\n      vertical-align: middle;\n    }\n  }\n}\n</style>\n"]}]}