{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/KnownHostsEditDialog.vue?vue&type=template&id=73593971&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/KnownHostsEditDialog.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/KnownHostsEditDialog.vue"], "names": ["t"], "mappings": ";;qBAwFM,KAAK,EAAC,wBAAwB;qBAE1B,KAAK,EAAC,OAAO;qBAGZ,KAAK,EAAC,cAAc;qBAClB,KAAK,EAAC,cAAc;qBASpB,KAAK,EAAC,gBAAgB;qBACpB,KAAK,EAAC,6BAA6B;qBAQnC,KAAK,EAAC,4BAA4B;;;;;;;UAhCvC,eAAS;qBADjB,aAoDY;;QAlDV,GAAG,EAAC,qBAAqB;QACzB,aAAW,EAAC,qBAAqB;QACjC,MAAM,EAAC,MAAM;QACZ,UAAU,EAAE,IAAI;QAChB,OAAK,uCAAE,oBAAW;;0BAEnB,CA2CM;UA3CN,oBA2CM,OA3CN,UA2CM;YAxCJ,oBAEK,MAFL,UAEK,mBADAA,MAAC;;YAEN,oBAoCM,OApCN,UAoCM;cAnCJ,oBAQM,OARN,UAQM;gBAPJ,aAME;kBALC,KAAK,EAAE,UAAI;kBACZ,aAAW,EAAC,oCAAoC;kBAC/C,OAAO,EAAE,uBAAiB;kBAC1B,aAAa,EAAE,IAAI;kBACnB,SAAO,EAAE,qBAAY;;;;cAG1B,oBAyBM,OAzBN,UAyBM;gBAxBJ,oBAOM,OAPN,UAOM;kBANJ,aAKE;oBAJA,KAAK,EAAC,oBAAoB;oBAC1B,aAAW,EAAC,sCAAsC;oBACjD,KAAK,EAAEA,MAAC;oBACR,UAAQ,EAAE,qBAAY;;;;gBAG3B,oBAeM,OAfN,UAeM;kBAdJ,oBAMS;oBALP,KAAK,EAAC,oBAAoB;oBAC1B,aAAW,EAAC,mCAAmC;oBAC9C,OAAK,uCAAE,oBAAW;sCAEhBA,MAAC;;kBAEN,oBAMS;oBALP,KAAK,EAAC,kBAAkB;oBACxB,aAAW,EAAC,iCAAiC;oBAC5C,OAAK,uCAAE,oBAAW;sCAEhBA,MAAC", "sourcesContent": ["<script>\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\nimport CodeMirror from '@shell/components/CodeMirror';\nimport FileSelector from '@shell/components/form/FileSelector.vue';\nimport AppModal from '@shell/components/AppModal.vue';\n\nexport default {\n  emits: ['closed'],\n\n  components: {\n    FileSelector,\n    AppModal,\n    CodeMirror,\n  },\n\n  props: {\n    value: {\n      type:     String,\n      required: true\n    },\n\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n  },\n\n  data() {\n    const codeMirrorOptions = {\n      readOnly:        this.isView,\n      gutters:         ['CodeMirror-foldgutter'],\n      mode:            'text/x-properties',\n      lint:            false,\n      lineNumbers:     !this.isView,\n      styleActiveLine: false,\n      tabSize:         2,\n      indentWithTabs:  false,\n      cursorBlinkRate: 530,\n    };\n\n    return {\n      codeMirrorOptions,\n      text:      this.value,\n      showModal: false,\n    };\n  },\n\n  computed: {\n    isView() {\n      return this.mode === _VIEW;\n    }\n  },\n\n  methods: {\n    onTextChange(value) {\n      this.text = value?.trim();\n    },\n\n    showDialog() {\n      this.showModal = true;\n    },\n\n    closeDialog(result) {\n      if (!result) {\n        this.text = this.value;\n      }\n\n      this.showModal = false;\n\n      this.$emit('closed', {\n        success: result,\n        value:   this.text,\n      });\n    },\n  }\n};\n</script>\n\n<template>\n  <app-modal\n    v-if=\"showModal\"\n    ref=\"sshKnownHostsDialog\"\n    data-testid=\"sshKnownHostsDialog\"\n    height=\"auto\"\n    :scrollable=\"true\"\n    @close=\"closeDialog(false)\"\n  >\n    <div\n      class=\"ssh-known-hosts-dialog\"\n    >\n      <h4 class=\"mt-10\">\n        {{ t('secret.ssh.editKnownHosts.title') }}\n      </h4>\n      <div class=\"custom mt-10\">\n        <div class=\"dialog-panel\">\n          <CodeMirror\n            :value=\"text\"\n            data-testid=\"ssh-known-hosts-dialog_code-mirror\"\n            :options=\"codeMirrorOptions\"\n            :showKeyMapBox=\"true\"\n            @onInput=\"onTextChange\"\n          />\n        </div>\n        <div class=\"dialog-actions\">\n          <div class=\"action-pannel file-selector\">\n            <FileSelector\n              class=\"btn role-secondary\"\n              data-testid=\"ssh-known-hosts-dialog_file-selector\"\n              :label=\"t('generic.readFromFile')\"\n              @selected=\"onTextChange\"\n            />\n          </div>\n          <div class=\"action-pannel form-actions\">\n            <button\n              class=\"btn role-secondary\"\n              data-testid=\"ssh-known-hosts-dialog_cancel-btn\"\n              @click=\"closeDialog(false)\"\n            >\n              {{ t('generic.cancel') }}\n            </button>\n            <button\n              class=\"btn role-primary\"\n              data-testid=\"ssh-known-hosts-dialog_save-btn\"\n              @click=\"closeDialog(true)\"\n            >\n              {{ t('generic.save') }}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </app-modal>\n</template>\n\n<style lang=\"scss\" scoped>\n  .ssh-known-hosts-dialog {\n    padding: 15px;\n\n    h4 {\n      font-weight: bold;\n      margin-bottom: 20px;\n    }\n\n    .dialog-panel {\n      display: flex;\n      flex-direction: column;\n      min-height: 100px;\n\n      :deep() .code-mirror {\n        display: flex;\n        flex-direction: column;\n        resize: none;\n\n        .codemirror-container {\n          border: 1px solid var(--border);\n        }\n\n        .CodeMirror,\n        .CodeMirror-gutters {\n          min-height: 400px;\n          max-height: 400px;\n          background-color: var(--yaml-editor-bg);\n        }\n\n        .CodeMirror-gutters {\n          width: 25px;\n        }\n\n        .CodeMirror-linenumber {\n          padding-left: 0;\n        }\n      }\n    }\n\n    .dialog-actions {\n      display: flex;\n      justify-content: space-between;\n\n      .action-pannel {\n        margin-top: 10px;\n      }\n\n      .form-actions {\n        display: flex;\n        justify-content: flex-end;\n\n        > *:not(:last-child) {\n          margin-right: 10px;\n        }\n      }\n    }\n  }\n</style>\n"]}]}