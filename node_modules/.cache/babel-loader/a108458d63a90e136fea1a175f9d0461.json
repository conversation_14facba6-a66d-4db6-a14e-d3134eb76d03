{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Radio.vue?vue&type=template&id=168c4266", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Radio.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Radio.vue"], "names": ["mode", "question", "value", "disabled", "displayLabel", "displayTooltip", "$emit", "showDescription", "displayDescription"], "mappings": ";;qBAaO,KAAK,EAAC,KAAK;qBACT,KAAK,EAAC,YAAY;;;EAerB,KAAK,EAAC,kBAAkB;;;;;;wBAhB5B,oBAoBM,OApBN,UAoBM;IAnBJ,oBAYM,OAZN,UAYM;MAXJ,aAUE;QATA,IAAI,EAAC,mBAAmB;QACvB,IAAI,EAAEA,SAAI;QACV,MAAM,EAAE,KAAK,CAAC,OAAO,CAACC,aAAQ,CAAC,OAAO,IAAIA,aAAQ,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAACA,aAAQ,CAAC,OAAO;QAC3F,OAAO,EAAE,KAAK,CAAC,OAAO,CAACA,aAAQ,CAAC,OAAO,IAAIA,aAAQ,CAAC,OAAO,GAAG,MAAM,CAAC,IAAI,CAACA,aAAQ,CAAC,OAAO;QAC1F,KAAK,EAAEC,UAAK;QACZ,QAAQ,EAAEC,aAAQ;QAClB,KAAK,EAAEC,iBAAY;QACnB,OAAO,EAAEC,mBAAc;QACvB,gBAAY,uCAAEC,UAAK,iBAAiB,MAAM;;;;KAIvCC,oBAAe;uBADvB,oBAKM,OALN,UAKM,mBADDC,uBAAkB", "sourcesContent": ["<script>\nimport RadioGroup from '@components/Form/Radio/RadioGroup.vue';\nimport Question from './Question';\n\nexport default {\n  emits: ['update:value'],\n\n  components: { RadioGroup },\n  mixins:     [Question]\n};\n</script>\n\n<template>\n  <div class=\"row\">\n    <div class=\"col span-6\">\n      <RadioGroup\n        name=\"question.variable\"\n        :mode=\"mode\"\n        :labels=\"Array.isArray(question.options) ? question.options : Object.values(question.options)\"\n        :options=\"Array.isArray(question.options) ? question.options : Object.keys(question.options)\"\n        :value=\"value\"\n        :disabled=\"disabled\"\n        :label=\"displayLabel\"\n        :tooltip=\"displayTooltip\"\n        @update:value=\"$emit('update:value', $event)\"\n      />\n    </div>\n    <div\n      v-if=\"showDescription\"\n      class=\"col span-6 mt-10\"\n    >\n      {{ displayDescription }}\n    </div>\n  </div>\n</template>\n"]}]}