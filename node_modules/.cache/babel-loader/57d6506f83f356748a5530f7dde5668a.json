{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??clonedRuleSet-44.use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Logging.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Logging.vue", "mtime": 1754992537319}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgZGVmaW5lQ29tcG9uZW50IH0gZnJvbSAndnVlJzsKaW1wb3J0IHsgbWFwR2V0dGVycyB9IGZyb20gJ3Z1ZXgnOwppbXBvcnQgeyBfRURJVCB9IGZyb20gJ0BzaGVsbC9jb25maWcvcXVlcnktcGFyYW1zJzsKaW1wb3J0IENoZWNrYm94IGZyb20gJ0Bjb21wb25lbnRzL0Zvcm0vQ2hlY2tib3gvQ2hlY2tib3gudnVlJzsKaW1wb3J0IHsgcmVtb3ZlT2JqZWN0IH0gZnJvbSAnQHNoZWxsL3V0aWxzL2FycmF5JzsKZXhwb3J0IGRlZmF1bHQgZGVmaW5lQ29tcG9uZW50KHsKICAgIG5hbWU6ICdFS1NMb2dnaW5nJywKICAgIGVtaXRzOiBbJ3VwZGF0ZTpsb2dnaW5nVHlwZXMnXSwKICAgIGNvbXBvbmVudHM6IHsgQ2hlY2tib3ggfSwKICAgIHByb3BzOiB7CiAgICAgICAgY29uZmlnOiB7CiAgICAgICAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUKICAgICAgICB9LAogICAgICAgIGxvZ2dpbmdUeXBlczogewogICAgICAgICAgICB0eXBlOiBBcnJheSwKICAgICAgICAgICAgZGVmYXVsdDogKCkgPT4gW10KICAgICAgICB9LAogICAgICAgIG1vZGU6IHsKICAgICAgICAgICAgdHlwZTogU3RyaW5nLAogICAgICAgICAgICBkZWZhdWx0OiBfRURJVAogICAgICAgIH0sCiAgICB9LAogICAgY29tcHV0ZWQ6IHsgLi4ubWFwR2V0dGVycyh7IHQ6ICdpMThuL3QnIH0pIH0sCiAgICBtZXRob2RzOiB7CiAgICAgICAgdHlwZUVuYWJsZWQodHlwZSkgewogICAgICAgICAgICByZXR1cm4gKHRoaXMubG9nZ2luZ1R5cGVzIHx8IFtdKS5pbmNsdWRlcyh0eXBlKTsKICAgICAgICB9LAogICAgICAgIHRvZ2dsZVR5cGUodHlwZSkgewogICAgICAgICAgICBjb25zdCBvdXQgPSBbLi4uKHRoaXMubG9nZ2luZ1R5cGVzIHx8IFtdKV07CiAgICAgICAgICAgIGlmIChvdXQuaW5jbHVkZXModHlwZSkpIHsKICAgICAgICAgICAgICAgIHJlbW92ZU9iamVjdChvdXQsIHR5cGUpOwogICAgICAgICAgICB9CiAgICAgICAgICAgIGVsc2UgewogICAgICAgICAgICAgICAgb3V0LnB1c2godHlwZSk7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgdGhpcy4kZW1pdCgndXBkYXRlOmxvZ2dpbmdUeXBlcycsIG91dCk7CiAgICAgICAgfQogICAgfSwKfSk7Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Logging.vue"], "names": [], "mappings": "AACA,OAAO,EAAE,eAAe,EAAW,MAAO,KAAK,CAAA;AAC/C,OAAO,EAAE,UAAS,EAAE,MAAO,MAAM,CAAA;AAEjC,OAAO,EAAE,KAAI,EAAE,MAAO,4BAA4B,CAAA;AAElD,OAAO,QAAO,MAAO,wCAAwC,CAAA;AAC7D,OAAO,EAAE,YAAW,EAAE,MAAO,oBAAoB,CAAA;AAEjD,eAAe,eAAe,CAAC;IAC7B,IAAI,EAAE,YAAY;IAElB,KAAK,EAAE,CAAC,qBAAqB,CAAC;IAE9B,UAAU,EAAE,EAAE,QAAO,EAAG;IAExB,KAAK,EAAE;QACL,MAAM,EAAE;YACN,IAAI,EAAM,MAA6B;YACvC,QAAQ,EAAE,IAAG;SACd;QAED,YAAY,EAAE;YACZ,IAAI,EAAK,KAAK;YACd,OAAO,EAAE,GAAG,EAAC,CAAE,EAAC;SACjB;QAED,IAAI,EAAE;YACJ,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,KAAI;SACd;KACF;IAED,QAAQ,EAAE,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE,QAAO,EAAG,CAAA,EAAG;IAE5C,OAAO,EAAE;QACP,WAAW,CAAC,IAAY;YACtB,OAAO,CAAC,IAAI,CAAC,YAAW,IAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QACjD,CAAC;QAED,UAAU,CAAC,IAAW;YACpB,MAAM,GAAE,GAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAW,IAAK,EAAE,CAAC,CAAC,CAAA;YAE1C,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAA;gBACtB,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;YACzB,CAAA;iBAAO,CAAA;gBACL,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAChB,CAAA;YAEA,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,GAAG,CAAC,CAAA;QACxC,CAAA;KACD;CACF,CAAC,CAAA", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Logging.vue.tsx", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent, PropType } from 'vue';\nimport { mapGetters } from 'vuex';\nimport { EKSConfig } from '../../types';\nimport { _EDIT } from '@shell/config/query-params';\n\nimport Checkbox from '@components/Form/Checkbox/Checkbox.vue';\nimport { removeObject } from '@shell/utils/array';\n\nexport default defineComponent({\n  name: 'EKSLogging',\n\n  emits: ['update:loggingTypes'],\n\n  components: { Checkbox },\n\n  props: {\n    config: {\n      type:     Object as PropType<EKSConfig>,\n      required: true\n    },\n\n    loggingTypes: {\n      type:    Array,\n      default: () => []\n    },\n\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n  },\n\n  computed: { ...mapGetters({ t: 'i18n/t' }) },\n\n  methods: {\n    typeEnabled(type: string) {\n      return (this.loggingTypes || []).includes(type);\n    },\n\n    toggleType(type:string) {\n      const out = [...(this.loggingTypes || [])];\n\n      if (out.includes(type)) {\n        removeObject(out, type);\n      } else {\n        out.push(type);\n      }\n\n      this.$emit('update:loggingTypes', out);\n    }\n  },\n});\n</script>\n\n<template>\n  <div>\n    <div\n      :style=\"{'justify-content':'space-between'}\"\n      class=\"row mb-10\"\n    >\n      <div class=\"col span-2\">\n        <Checkbox\n          :value=\"typeEnabled('audit')\"\n          :mode=\"mode\"\n          label-key=\"eks.audit.label\"\n          :tooltip=\"t('eks.audit.tooltip')\"\n          @update:value=\"toggleType('audit')\"\n        />\n      </div>\n      <div class=\"col span-2\">\n        <Checkbox\n          :value=\"typeEnabled('api')\"\n          :mode=\"mode\"\n          label-key=\"eks.api.label\"\n          :tooltip=\"t('eks.api.tooltip')\"\n          @update:value=\"toggleType('api')\"\n        />\n      </div>\n\n      <div class=\"col span-2\">\n        <Checkbox\n          :value=\"typeEnabled('scheduler')\"\n          :mode=\"mode\"\n          label-key=\"eks.scheduler.label\"\n          :tooltip=\"t('eks.scheduler.tooltip')\"\n          @update:value=\"toggleType('scheduler')\"\n        />\n      </div>\n\n      <div class=\"col span-2\">\n        <Checkbox\n          :value=\"typeEnabled('controllerManager')\"\n          :mode=\"mode\"\n          label-key=\"eks.controllerManager.label\"\n          :tooltip=\"t('eks.controllerManager.tooltip')\"\n          @update:value=\"toggleType('controllerManager')\"\n        />\n      </div>\n      <div class=\"col span-2\">\n        <Checkbox\n          :value=\"typeEnabled('authenticator')\"\n          :mode=\"mode\"\n          label-key=\"eks.authenticator.label\"\n          :tooltip=\"t('eks.authenticator.tooltip')\"\n          @update:value=\"toggleType('authenticator')\"\n        />\n      </div>\n    </div>\n  </div>\n</template>\n"]}]}