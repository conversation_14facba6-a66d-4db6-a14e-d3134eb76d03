{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/GetCredential.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/GetCredential.vue", "mtime": 1754995886426}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/GetCredential.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,<PERSON><PERSON>,CAAC,CAAC;AAChE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAExC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAErB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/D,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAExB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;IAEtG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC3D,CAAC;MACD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACnC,CAAC,CAAC;;IAEF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE;MAClD,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1B;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC;MACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAElC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACzB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAElC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzE,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAClE,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE1B,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;MAErB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACpF,CAAC,CAAC;;MAEF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACjD,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3F,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC;;MAEH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACtE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC;MACJ;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1D,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,EAAE,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC;;MAEF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACZ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd;;MAEA,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1E;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjC;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACpD;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAEP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC;EACF,CAAC;AACH,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/GetCredential.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport Loading from '@shell/components/Loading';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport { NORMAN, DEFAULT_WORKSPACE } from '@shell/config/types';\nimport CreateEditView from '@shell/mixins/create-edit-view';\nimport CruResource from '@shell/components/CruResource';\nimport NameNsDescription from '@shell/components/form/NameNsDescription';\nimport { Banner } from '@components/Banner';\nimport { CAPI } from '@shell/config/labels-annotations';\nimport { clear } from '@shell/utils/array';\nimport cloneDeep from 'lodash/cloneDeep';\n\nconst _NEW = '_NEW';\nconst _NONE = '_NONE';\n\nexport default {\n  emits: ['update:value', 'credential-created'],\n\n  components: {\n    Loading, LabeledSelect, CruResource, NameNsDescription, Banner\n  },\n\n  mixins: [CreateEditView],\n\n  props: {\n    value: {\n      type:    String,\n      default: null,\n    },\n\n    provider: {\n      type:    String,\n      default: null,\n    },\n\n    cancel: {\n      type:    Function,\n      default: null\n    },\n\n    showingForm: {\n      type:     Boolean,\n      required: true,\n    }\n  },\n\n  async fetch() {\n    this.allCredentials = await this.$store.dispatch('rancher/findAll', { type: NORMAN.CLOUD_CREDENTIAL });\n\n    const field = this.$store.getters['plugins/credentialFieldForDriver'](this.driverName);\n\n    this.newCredential = await this.$store.dispatch('rancher/create', {\n      type:     NORMAN.CLOUD_CREDENTIAL,\n      metadata: {\n        namespace:   DEFAULT_WORKSPACE,\n        annotations: { [CAPI.CREDENTIAL_DRIVER]: this.driverName }\n      },\n      [`${ field }credentialConfig`]: {}\n    });\n\n    if ( this.value ) {\n      this.credentialId = this.value;\n    } else if ( this.filteredCredentials.length === 1 ) {\n      // Auto pick the first credential if there's only one\n      this.credentialId = this.filteredCredentials[0].id;\n    } else if ( !this.filteredCredentials.length ) {\n      this.credentialId = _NEW;\n    }\n  },\n\n  data() {\n    return {\n      allCredentials:                [],\n      nodeComponent:                 null,\n      credentialId:                  this.value || _NONE,\n      newCredential:                 null,\n      credCustomComponentValidation: false,\n      nameRequiredValidation:        false,\n      originalId:                    this.value\n    };\n  },\n\n  computed: {\n    hasCustomCloudCredentialComponent() {\n      const driverName = this.driverName;\n\n      return this.$store.getters['type-map/hasCustomCloudCredentialComponent'](driverName);\n    },\n\n    cloudCredentialComponent() {\n      const driverName = this.driverName;\n\n      return this.$store.getters['type-map/importCloudCredential'](driverName);\n    },\n\n    genericCloudCredentialComponent() {\n      return this.$store.getters['type-map/importCloudCredential']('generic');\n    },\n\n    cloudComponent() {\n      if (this.hasCustomCloudCredentialComponent) {\n        return this.cloudCredentialComponent;\n      }\n\n      return this.genericCloudCredentialComponent;\n    },\n\n    isNone() {\n      return this.credentialId === null || this.credentialId === _NONE;\n    },\n\n    isNew() {\n      return false;\n      // return this.credentialId === _NEW;\n    },\n\n    isPicked() {\n      return !!this.credentialId && !this.isNone && !this.isNew;\n    },\n\n    driverName() {\n      let driver = this.provider;\n\n      // Map providers that share a common credential to one driver\n      driver = this.$store.getters['plugins/credentialDriverFor'](driver);\n\n      return driver;\n    },\n\n    filteredCredentials() {\n      return this.allCredentials.filter((x) => x.provider === this.driverName);\n    },\n\n    options() {\n      const duplicates = {};\n\n      this.filteredCredentials.forEach((cred) => {\n        duplicates[cred.nameDisplay] = duplicates[cred.nameDisplay] === null ? true : null;\n      });\n\n      const out = this.filteredCredentials.map((obj) => ({\n        // if credential name is duplicated we add the id to the label\n        label: duplicates[obj.nameDisplay] ? `${ obj.nameDisplay } (${ obj.id })` : obj.nameDisplay,\n        value: obj.id,\n      }));\n\n      if ( this.originalId && !out.find((x) => x.value === this.originalId) ) {\n        out.unshift({\n          label: `${ this.originalId.replace(/^cattle-global-data:/, '') } (current)`,\n          value: this.originalId\n        });\n      }\n\n      // out.unshift({\n      //   label: this.t('cluster.credential.select.option.new'),\n      //   value: _NEW,\n      // });\n\n      out.unshift({\n        label:    this.t('cluster.credential.select.option.none'),\n        value:    _NONE,\n        disabled: true,\n      });\n\n      return out;\n    },\n\n    validationPassed() {\n      if ( this.credentialId === _NONE ) {\n        return false;\n      }\n\n      if ( this.credentialId === _NEW ) {\n        return this.credCustomComponentValidation && this.nameRequiredValidation;\n      }\n\n      return !!this.credentialId;\n    },\n  },\n\n  watch: {\n    credentialId(val) {\n      if ( val === _NEW || val === _NONE ) {\n        this.$emit('update:value', null);\n      } else {\n        this.$emit('update:value', val);\n      }\n    },\n    'newCredential.name'(newValue) {\n      this.nameRequiredValidation = newValue?.length > 0;\n    }\n  },\n\n  methods: {\n\n    createValidationChanged(passed) {\n      this.credCustomComponentValidation = passed;\n    },\n\n    backToExisting() {\n      this.credentialId = _NONE;\n    },\n    updateCredentialValue(key, value) {\n      this.newCredential.setData(key, value);\n    }\n  },\n};\n</script>\n\n<template>\n  <Loading v-if=\"$fetchState.pending\" />\n  <div v-else>\n    <div>\n      <Banner\n        v-if=\"!credentialId\"\n        label=\"First you need to pick or create the cloud credential that will be used to create the nodes for the cluster...\"\n        color=\"info\"\n      />\n\n      <LabeledSelect\n        v-model:value=\"credentialId\"\n        :label=\"t('cluster.credential.label')\"\n        :options=\"options\"\n        option-key=\"value\"\n        :mode=\"mode\"\n        :selectable=\"option => !option.disabled\"\n        data-testid=\"cluster-prov-select-credential\"\n      />\n    </div>\n\n    <!-- <template\n      v-if=\"isNew && options.length\"\n      #footer-prefix\n    >\n      <button\n        class=\"btn role-secondary\"\n        @click=\"backToExisting()\"\n      >\n        {{ t('cluster.credential.selectExisting.label') }}\n      </button>\n    </template> -->\n  </div>\n\n  <!-- </CruResource> -->\n</template>\n\n<style lang='scss' scoped>\n  .select-credentials {\n    flex-grow: 1; // Do grow when on own\n    &__showingForm {\n      flex-grow: 0; // Don't grow when in rke2 form\n    }\n  }\n</style>\n"]}]}