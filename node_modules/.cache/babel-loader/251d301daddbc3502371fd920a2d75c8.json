{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetIntro.vue?vue&type=template&id=d24df9bc&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetIntro.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetIntro.vue"], "names": ["t"], "mappings": ";;qBA4BO,KAAK,EAAC,WAAW;qBAEf,KAAK,EAAC,OAAO;;;EAKhB,KAAK,EAAC,SAAS;;;;;;wBAPnB,oBAgBM,OAhBN,UAgBM;8BAfJ,oBAAkC,OAA/B,KAAK,EAAC,sBAAsB;;IAC/B,oBAEM,OAFN,UAEM,mBADDA,MAAC;;KAGE,oBAAc;uBADtB,oBAUM,OAVN,UAUM;UANJ,aAKc;YAJX,EAAE,EAAE,kBAAY;YACjB,KAAK,EAAC,oBAAoB;;8BAE1B,CAAqC;gDAAlCA,MAAC", "sourcesContent": ["<script>\nimport { FLEET } from '@shell/config/types';\nimport { NAME } from '@shell/config/product/fleet';\n\nexport default {\n\n  name: 'FleetIntro',\n\n  data() {\n    const gitRepoRoute = {\n      name:   'c-cluster-product-resource-create',\n      params: {\n        product:  NAME,\n        resource: FLEET.GIT_REPO\n      },\n    };\n\n    const gitRepoSchema = this.$store.getters['management/schemaFor'](FLEET.GIT_REPO);\n    const canCreateRepos = gitRepoSchema && gitRepoSchema.resourceMethods.includes('PUT');\n\n    return {\n      gitRepoRoute,\n      canCreateRepos\n    };\n  },\n};\n</script>\n<template>\n  <div class=\"intro-box\">\n    <i class=\"icon icon-repository\" />\n    <div class=\"title\">\n      {{ t('fleet.gitRepo.repo.noRepos') }}\n    </div>\n    <div\n      v-if=\"canCreateRepos\"\n      class=\"actions\"\n    >\n      <router-link\n        :to=\"gitRepoRoute\"\n        class=\"btn role-secondary\"\n      >\n        {{ t('fleet.gitRepo.repo.addRepo') }}\n      </router-link>\n    </div>\n  </div>\n</template>\n<style lang=\"scss\" scoped>\n.intro-box {\n  flex: 0 0 100%;\n  height: calc(100vh - 246px); // 2(48 content header + 20 padding + 55 pageheader)\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-direction: column;\n}\n\n.title {\n  margin-bottom: 15px;\n  font-size: $font-size-h2;\n}\n.icon-repository {\n  font-size: 96px;\n  margin-bottom: 32px;\n}\n</style>\n"]}]}