{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/WorkspaceSwitcher.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/WorkspaceSwitcher.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/WorkspaceSwitcher.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEvE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;EAEtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEvG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnE,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAC9E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC9D;MACF,CAAC;IACH,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC;QACH,CAAC,CAAC;;QAEF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACZ;;MAEA,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClD,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACjB;;MAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEpE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B;IACF,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;IAEzF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACxE,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC;EACH,CAAC;AACH,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/WorkspaceSwitcher.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { LAST_NAMESPACE, WORKSPACE } from '@shell/store/prefs';\nimport { mapState } from 'vuex';\nimport Select from '@shell/components/form/Select';\nimport { WORKSPACE_ANNOTATION } from '@shell/config/labels-annotations';\n\nexport default {\n  name:       'WorkspaceSwitcher',\n  components: { Select },\n\n  computed: {\n    ...mapState(['allWorkspaces', 'workspace', 'allNamespaces', 'defaultNamespace', 'getActiveNamespaces']),\n\n    value: {\n      get() {\n        return this.workspace || this.namespace || this.options[0]?.value;\n      },\n\n      set(value) {\n        if (value !== this.value) {\n          this.$store.commit('updateWorkspace', { value, getters: this.$store.getters });\n          this.$store.dispatch('prefs/set', { key: WORKSPACE, value });\n        }\n      },\n    },\n\n    options() {\n      if (this.allWorkspaces.length) {\n        const out = this.allWorkspaces.map((obj) => {\n          return {\n            label: obj.nameDisplay,\n            value: obj.id,\n          };\n        });\n\n        return out;\n      }\n\n      // If doesn't have workspaces (e.g. no permissions)\n      // Then find the workspaces from the annotation.\n      return this.allNamespaces.filter((item) => {\n        return item.metadata.annotations[WORKSPACE_ANNOTATION] === WORKSPACE;\n      }).map((obj) => {\n        return {\n          label: obj.nameDisplay,\n          value: obj.id,\n        };\n      });\n    },\n  },\n\n  watch: {\n    options(curr, prev) {\n      if (curr.length === 0) {\n        this.value = '';\n      }\n\n      const currentExists = curr.find((item) => item.value === this.value);\n\n      if (curr.length && !currentExists) {\n        this.value = curr[0]?.value;\n      }\n    },\n  },\n\n  created() {\n    // in fleet standard user with just the project owner and global git repo permissions\n    // returns 'default'\n    const initValue = !this.workspace ? this.$store.getters['prefs/get'](LAST_NAMESPACE) : '';\n\n    this.value = (initValue === 'default' || initValue === '') && this.options.length ? this.options[0].value : initValue;\n  },\n\n  data() {\n    return { namespace: this.$store.getters['prefs/get'](LAST_NAMESPACE) };\n  },\n\n  methods: {\n    focus() {\n      this.$refs.select.$refs.search.focus();\n    },\n  },\n};\n</script>\n\n<template>\n  <div\n    class=\"filter\"\n    data-testid=\"workspace-switcher\"\n  >\n    <Select\n      ref=\"select\"\n      v-model:value=\"value\"\n      label=\"label\"\n      :options=\"options\"\n      :clearable=\"false\"\n      :reduce=\"(opt) => opt.value\"\n    />\n    <!--button v-shortkey.once=\"['w']\" class=\"hide\" @shortkey=\"focus()\" /-->\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.filter {\n  min-width: 220px;\n  max-width: 100%;\n  display: inline-block;\n}\n\n.filter.show-masked :deep() .unlabeled-select:not(.masked-dropdown) {\n  position: absolute;\n  left: 0;\n  top: 0;\n  height: 0;\n  opacity: 0;\n  visibility: hidden;\n}\n\n.filter:not(.show-masked) :deep() .unlabeled-select.masked-dropdown {\n  position: absolute;\n  left: 0;\n  top: 0;\n  height: 0;\n  opacity: 0;\n  visibility: hidden;\n}\n\n.filter :deep() .unlabeled-select.has-more .v-select:not(.vs--open) .vs__dropdown-toggle {\n  overflow: hidden;\n}\n\n.filter :deep() .unlabeled-select.has-more .v-select.vs--open .vs__dropdown-toggle {\n  height: max-content;\n  background-color: var(--header-bg);\n}\n\n.filter :deep() .unlabeled-select {\n  background-color: transparent;\n  border: 0;\n}\n\n.filter :deep() .unlabeled-select:not(.focused) {\n  min-height: 0;\n}\n\n.filter :deep() .unlabeled-select:not(.view):hover .vs__dropdown-menu {\n  background: var(--dropdown-bg);\n}\n\n.filter :deep() .unlabeled-select .v-select.inline {\n  margin: 0;\n}\n\n.filter :deep() .unlabeled-select .v-select .vs__selected {\n  margin: $input-padding-sm;\n  user-select: none;\n  color: var(--header-btn-text);\n}\n\n.filter :deep() .unlabeled-select .vs__search::placeholder {\n  color: var(--header-btn-text);\n}\n\n.filter :deep() .unlabeled-select INPUT:hover {\n  background-color: transparent;\n}\n\n.filter :deep() .unlabeled-select .vs__dropdown-toggle {\n  background: rgba(0, 0, 0, 0.05);\n  border-radius: var(--border-radius);\n  border: 1px solid var(--header-btn-bg);\n  color: var(--header-btn-text);\n  height: 40px;\n  max-width: 100%;\n  padding-top: 0;\n}\n\n.filter :deep() .unlabeled-select .vs__deselect:after {\n  color: var(--header-btn-text);\n}\n\n.filter :deep() .unlabeled-select .v-select .vs__actions:after {\n  fill: var(--header-btn-text) !important;\n  color: var(--header-btn-text) !important;\n}\n\n.filter :deep() .unlabeled-select INPUT[type='search'] {\n  padding: 7px;\n}\n</style>\n"]}]}