{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/String.vue?vue&type=template&id=5ff7db36", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/String.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/String.vue"], "names": ["question", "mode", "displayLabel", "value", "disabled", "displayTooltip", "rules", "$emit", "showDescription", "displayDescription"], "mappings": ";;;qBA2BS,KAAK,EAAC,YAAY;;;;;;wBAJzB,oBA0BM;IAzBH,aAAW,gBAAgBA,aAAQ,CAAC,QAAQ;IAC7C,KAAK,EAAC,KAAK;;IAEX,oBAcM,OAdN,UAcM;MAbJ,aAYE;QAXC,IAAI,EAAEC,SAAI;QACV,IAAI,EAAE,kBAAS;QACf,KAAK,EAAEC,iBAAY;QACnB,WAAW,EAAEF,aAAQ,CAAC,OAAO;QAC7B,QAAQ,EAAEA,aAAQ,CAAC,QAAQ;QAC3B,KAAK,EAAEG,UAAK;QACZ,QAAQ,EAAEC,aAAQ;QAClB,OAAO,EAAEC,mBAAc;QACvB,KAAK,EAAEC,UAAK;QACZ,aAAW,kBAAkBN,aAAQ,CAAC,QAAQ;QAC9C,gBAAY,uCAAEO,UAAK,iBAAiB,MAAM;;;;KAIvCC,oBAAe;uBADvB,oBAMM;;UAJH,aAAW,wBAAwBR,aAAQ,CAAC,QAAQ;UACrD,KAAK,EAAC,kBAAkB;4BAErBS,uBAAkB", "sourcesContent": ["<script>\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport Question from './Question';\n\nexport default {\n  emits: ['update:value'],\n\n  components: { LabeledInput },\n  mixins:     [Question],\n\n  computed: {\n    inputType() {\n      if ( ['text', 'password', 'multiline'].includes(this.question.type) ) {\n        return this.question.type;\n      }\n\n      return 'text';\n    }\n  }\n};\n</script>\n\n<template>\n  <div\n    :data-testid=\"`string-row-${question.variable}`\"\n    class=\"row\"\n  >\n    <div class=\"col span-6\">\n      <LabeledInput\n        :mode=\"mode\"\n        :type=\"inputType\"\n        :label=\"displayLabel\"\n        :placeholder=\"question.default\"\n        :required=\"question.required\"\n        :value=\"value\"\n        :disabled=\"disabled\"\n        :tooltip=\"displayTooltip\"\n        :rules=\"rules\"\n        :data-testid=\"`string-input-${question.variable}`\"\n        @update:value=\"$emit('update:value', $event)\"\n      />\n    </div>\n    <div\n      v-if=\"showDescription\"\n      :data-testid=\"`string-description-${question.variable}`\"\n      class=\"col span-6 mt-10\"\n    >\n      {{ displayDescription }}\n    </div>\n  </div>\n</template>\n"]}]}