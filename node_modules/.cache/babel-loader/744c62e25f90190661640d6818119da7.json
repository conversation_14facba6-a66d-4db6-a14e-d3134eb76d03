{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??clonedRuleSet-44.use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcButton/RcButton.vue?vue&type=script&setup=true&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcButton/RcButton.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcButton/RcButton.vue?vue&type=script&setup=true&lang=ts", "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcButton/RcButton.vue"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,IAAI,gBAAgB,EAAE,MAAM,KAAK,CAAA;ACSzD,OAAO,EAAE,QAAQ,EAAE,GAAiB,EAAE,MAAM,KAAK,CAAA;ADJjD,eAAe,aAAa,CAAA,gBAAgB,CAAC;IAC3C,MAAM,EAAE,UAAU;IAClB,KAAK,EAAE;QACL,OAAO,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;QAC3C,SAAS,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;QAC7C,QAAQ,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;QAC5C,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;QACxC,KAAK,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;QACzC,KAAK,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;KAC1C;IACD,KAAK,CAAC,OAAY,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE;QCd1C;;;;;;;WAOE;QAIF,MAAM,WAAW,GAAyD;YACxE,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,cAAc,EAAE;YAC9C,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,gBAAgB,EAAE;YAClD,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,eAAe,EAAE;YAChD,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE;YACxC,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE;SAC3C,CAAA;QAED,MAAM,WAAW,GAAyD;YACxE,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE;SACvC,CAAA;QAED,MAAM,KAAK,GAAG,OAAgD,CAAA;QAE9D,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,EAAE;YAChC,MAAM,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;YAC9D,MAAM,aAAa,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;YAEjE,OAAO;gBACL,GAAG,EAAE,IAAI;gBAET,CAAC,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,SAAS,KAAI,cAAc,CAAC,EAAE,IAAI;gBAE/C,QAAQ,EAAE,aAAa;aACxB,CAAA;QACH,CAAC,CAAC,CAAA;QAEF,MAAM,aAAa,GAAG,GAAG,CAAqB,IAAI,CAAC,CAAA;QAEnD,MAAM,KAAK,GAAG,GAAG,EAAE;;YACjB,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,KAAK,0CAAE,KAAK,EAAE,CAAA;QAC/B,CAAC,CAAA;QAED,QAAY,CAAC,EAAE,KAAK,EAAE,CAAC,CAAA;QDevB,MAAM,YAAY,GAAG,EAAE,WAAW,EAAE,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE,aAAa,EAAE,KAAK,EAAE,CAAA;QAC3F,MAAM,CAAC,cAAc,CAAC,YAAY,EAAE,iBAAiB,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAA;QAC1F,OAAO,YAAY,CAAA;IACnB,CAAC;CAEA,CAAC,CAAA", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcButton/RcButton.vue.tsx", "sourceRoot": "", "sourcesContent": ["import { defineComponent as _defineComponent } from 'vue'\nimport { computed, ref } from 'vue';\nimport { ButtonRoleProps, ButtonSizeProps } from './types';\n\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'R<PERSON><PERSON><PERSON><PERSON>',\n  props: {\n    primary: { type: Boolean, required: false },\n    secondary: { type: Boolean, required: false },\n    tertiary: { type: Boolean, required: false },\n    link: { type: Boolean, required: false },\n    ghost: { type: Boolean, required: false },\n    small: { type: Boolean, required: false }\n  },\n  setup(__props: any, { expose: __expose }) {\n\n/**\n * A button element used for performing actions, such as submitting forms or\n * opening dialogs.\n *\n * Example:\n *\n * <rc-button primary @click=\"doAction\">Perform an Action</rc-button>\n */\nconst buttonRoles: { role: keyof ButtonRoleProps, className: string }[] = [\n  { role: 'primary', className: 'role-primary' },\n  { role: 'secondary', className: 'role-secondary' },\n  { role: 'tertiary', className: 'role-tertiary' },\n  { role: 'link', className: 'role-link' },\n  { role: 'ghost', className: 'role-ghost' },\n];\n\nconst buttonSizes: { size: keyof ButtonSizeProps, className: string }[] = [\n  { size: 'small', className: 'btn-sm' },\n];\n\nconst props = __props;\n\nconst buttonClass = computed(() => {\n  const activeRole = buttonRoles.find(({ role }) => props[role]);\n  const isButtonSmall = buttonSizes.some(({ size }) => props[size]);\n\n  return {\n    btn: true,\n\n    [activeRole?.className || 'role-primary']: true,\n\n    'btn-sm': isButtonSmall,\n  };\n});\n\nconst RcFocusTarget = ref<HTMLElement | null>(null);\n\nconst focus = () => {\n  RcFocusTarget?.value?.focus();\n};\n\n__expose({ focus });\n\nconst __returned__ = { buttonRoles, buttonSizes, props, buttonClass, RcFocusTarget, focus }\nObject.defineProperty(__returned__, '__isScriptSetup', { enumerable: false, value: true })\nreturn __returned__\n}\n\n})", "<script setup lang=\"ts\">\n/**\n * A button element used for performing actions, such as submitting forms or\n * opening dialogs.\n *\n * Example:\n *\n * <rc-button primary @click=\"doAction\">Perform an Action</rc-button>\n */\nimport { computed, ref, defineExpose } from 'vue';\nimport { ButtonRoleProps, ButtonSizeProps } from './types';\n\nconst buttonRoles: { role: keyof ButtonRoleProps, className: string }[] = [\n  { role: 'primary', className: 'role-primary' },\n  { role: 'secondary', className: 'role-secondary' },\n  { role: 'tertiary', className: 'role-tertiary' },\n  { role: 'link', className: 'role-link' },\n  { role: 'ghost', className: 'role-ghost' },\n];\n\nconst buttonSizes: { size: keyof ButtonSizeProps, className: string }[] = [\n  { size: 'small', className: 'btn-sm' },\n];\n\nconst props = defineProps<ButtonRoleProps & ButtonSizeProps>();\n\nconst buttonClass = computed(() => {\n  const activeRole = buttonRoles.find(({ role }) => props[role]);\n  const isButtonSmall = buttonSizes.some(({ size }) => props[size]);\n\n  return {\n    btn: true,\n\n    [activeRole?.className || 'role-primary']: true,\n\n    'btn-sm': isButtonSmall,\n  };\n});\n\nconst RcFocusTarget = ref<HTMLElement | null>(null);\n\nconst focus = () => {\n  RcFocusTarget?.value?.focus();\n};\n\ndefineExpose({ focus });\n</script>\n\n<template>\n  <button\n    ref=\"RcFocusTarget\"\n    role=\"button\"\n    :class=\"{ ...buttonClass, ...($attrs.class || { }) }\"\n  >\n    <slot name=\"before\">\n      <!-- Empty Content -->\n    </slot>\n    <slot>\n      <!-- Empty Content -->\n    </slot>\n    <slot name=\"after\">\n      <!-- Empty Content -->\n    </slot>\n  </button>\n</template>\n\n<style lang=\"scss\" scoped>\nbutton {\n  &.role-link {\n    &:focus, &.focused {\n      @include focus-outline;\n      outline-offset: -2px;\n    }\n\n    &:hover {\n      background-color: var(--accent-btn);\n      box-shadow: none;\n    }\n  }\n\n  &.role-ghost {\n    padding: 0;\n    background-color: transparent;\n\n    &:focus, &.focused {\n      @include focus-outline;\n      outline-offset: 0;\n    }\n\n    &:focus-visible {\n      @include focus-outline;\n      outline-offset: 0;\n    }\n  }\n}</style>\n"]}]}