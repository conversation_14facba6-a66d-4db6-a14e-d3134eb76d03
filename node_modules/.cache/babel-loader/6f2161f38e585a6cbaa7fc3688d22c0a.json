{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DisableAuthProviderModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DisableAuthProviderModal.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCB7IENhcmQgfSBmcm9tICdAY29tcG9uZW50cy9DYXJkJzsKaW1wb3J0IEFwcE1vZGFsIGZyb20gJ0BzaGVsbC9jb21wb25lbnRzL0FwcE1vZGFsLnZ1ZSc7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1Byb21wdFJlbW92ZScsCgogIGVtaXRzOiBbJ2Rpc2FibGUnXSwKCiAgY29tcG9uZW50czogeyBDYXJkLCBBcHBNb2RhbCB9LAogIHByb3BzOiAgICAgIHsKICAgIC8qKgogICAgICogSW5oZXJpdGVkIGdsb2JhbCBpZGVudGlmaWVyIHByZWZpeCBmb3IgdGVzdHMKICAgICAqIERlZmluZSBhIHRlcm0gYmFzZWQgb24gdGhlIHBhcmVudCBjb21wb25lbnQgdG8gYXZvaWQgY29uZmxpY3RzIG9uIG11bHRpcGxlIGNvbXBvbmVudHMKICAgICAqLwogICAgY29tcG9uZW50VGVzdGlkOiB7CiAgICAgIHR5cGU6ICAgIFN0cmluZywKICAgICAgZGVmYXVsdDogJ2Rpc2FibGUtYXV0aC1wcm92aWRlcicKICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4geyBzaG93TW9kYWw6IGZhbHNlIH07CiAgfSwKICBtZXRob2RzOiB7CiAgICBzaG93KCkgewogICAgICB0aGlzLnNob3dNb2RhbCA9IHRydWU7CiAgICB9LAogICAgY2xvc2UoKSB7CiAgICAgIHRoaXMuc2hvd01vZGFsID0gZmFsc2U7CiAgICB9LAogICAgZGlzYWJsZSgpIHsKICAgICAgdGhpcy5zaG93TW9kYWwgPSBmYWxzZTsKICAgICAgdGhpcy4kZW1pdCgnZGlzYWJsZScpOwogICAgfSwKICB9Cn07Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DisableAuthProviderModal.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAErD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;IACV,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAC7C,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACtF,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAC7B,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC;EACH;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DisableAuthProviderModal.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { Card } from '@components/Card';\nimport AppModal from '@shell/components/AppModal.vue';\n\nexport default {\n  name: 'PromptRemove',\n\n  emits: ['disable'],\n\n  components: { Card, AppModal },\n  props:      {\n    /**\n     * Inherited global identifier prefix for tests\n     * Define a term based on the parent component to avoid conflicts on multiple components\n     */\n    componentTestid: {\n      type:    String,\n      default: 'disable-auth-provider'\n    }\n  },\n  data() {\n    return { showModal: false };\n  },\n  methods: {\n    show() {\n      this.showModal = true;\n    },\n    close() {\n      this.showModal = false;\n    },\n    disable() {\n      this.showModal = false;\n      this.$emit('disable');\n    },\n  }\n};\n</script>\n\n<template>\n  <app-modal\n    v-if=\"showModal\"\n    custom-class=\"remove-modal\"\n    name=\"disableAuthProviderModal\"\n    :width=\"400\"\n    height=\"auto\"\n    styles=\"max-height: 100vh;\"\n    @close=\"close\"\n  >\n    <Card\n      class=\"disable-auth-provider\"\n      :show-highlight-border=\"false\"\n    >\n      <template #title>\n        <h4 class=\"text-default-text\">\n          {{ t('promptRemove.title') }}\n        </h4>\n      </template>\n      <template #body>\n        <div class=\"mb-10\">\n          <p v-clean-html=\"t('promptRemove.attemptingToRemoveAuthConfig', null, true)\" />\n        </div>\n      </template>\n      <template #actions>\n        <button\n          class=\"btn role-secondary\"\n          @click=\"close\"\n        >\n          {{ t('generic.cancel') }}\n        </button>\n        <div class=\"spacer\" />\n        <button\n          class=\"btn role-primary bg-error ml-10\"\n          :data-testid=\"componentTestid + '-confirm-button'\"\n          @click=\"disable\"\n        >\n          {{ t('generic.disable') }}\n        </button>\n      </template>\n    </Card>\n  </app-modal>\n</template>\n\n<style lang='scss'>\n  .disable-auth-provider {\n    &.card-container {\n      box-shadow: none;\n    }\n    #confirm {\n      width: 90%;\n      margin-left: 3px;\n    }\n\n    .remove-modal {\n        border-radius: var(--border-radius);\n        overflow: scroll;\n        max-height: 100vh;\n        & ::-webkit-scrollbar-corner {\n          background: rgba(0,0,0,0);\n        }\n    }\n\n    .actions {\n      text-align: right;\n    }\n\n    .card-actions {\n      display: flex;\n\n      .spacer {\n        flex: 1;\n      }\n    }\n  }\n</style>\n"]}]}