{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??clonedRuleSet-44.use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/types/components/resourceLabeledSelect.ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/types/components/resourceLabeledSelect.ts", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:LyoqCiAqIEZvcmNlIGEgc3BlY2lmaWMgbW9kZQogKi8KZXhwb3J0IHZhciBSRVNPVVJDRV9MQUJFTF9TRUxFQ1RfTU9ERTsKKGZ1bmN0aW9uIChSRVNPVVJDRV9MQUJFTF9TRUxFQ1RfTU9ERSkgewogICAgLyoqCiAgICAgKiBGZXRjaCBhbGwgcmVzb3VyY2VzCiAgICAgKi8KICAgIFJFU09VUkNFX0xBQkVMX1NFTEVDVF9NT0RFWyJBTExfUkVTT1VSQ0VTIl0gPSAiQUxMIjsKICAgIC8qKgogICAgICogRGV0ZXJtaW5lIGlmIGFsbCByZXNvdXJjZXMgYXJlIGZldGNoZWQgZ2l2ZW4gc3lzdGVtIHNldHRpbmdzCiAgICAgKi8KICAgIFJFU09VUkNFX0xBQkVMX1NFTEVDVF9NT0RFWyJEWU5BTUlDIl0gPSAiRFlOQU1JQyI7Cn0pKFJFU09VUkNFX0xBQkVMX1NFTEVDVF9NT0RFIHx8IChSRVNPVVJDRV9MQUJFTF9TRUxFQ1RfTU9ERSA9IHt9KSk7Cg=="}, {"version": 3, "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/types/components/resourceLabeledSelect.ts", "sourceRoot": "", "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/types/components/resourceLabeledSelect.ts"], "names": [], "mappings": "AAmCA;;GAEG;AACH,MAAM,CAAN,IAAY,0BASX;AATD,WAAY,0BAA0B;IACpC;;OAEG;IACH,mDAAqB,CAAA;IACrB;;OAEG;IACH,iDAAmB,CAAA;AACrB,CAAC,EATW,0BAA0B,KAA1B,0BAA0B,QASrC", "sourcesContent": ["import { LabelSelectPaginationFunctionOptions } from '@shell/components/form/labeled-select-utils/labeled-select.utils';\nimport { LabelSelectPaginateFn } from '@shell/types/components/labeledSelect';\n\ntype PaginateTypeOverridesFn = (opts: LabelSelectPaginationFunctionOptions) => LabelSelectPaginationFunctionOptions;\n\ninterface SharedSettings {\n  /**\n   * Provide specific LabelSelect options for this mode (paginated / not paginated)\n   */\n  labelSelectOptions?: { [key: string]: any },\n  /**\n   * Map, filter, tweak, etc the resources to show in the LabelSelect\n   */\n  updateResources?: (resources: any[]) => any[]\n}\n\n/**\n * Settings to use when the LabelSelect is paginating\n */\nexport interface ResourceLabeledSelectPaginateSettings extends SharedSettings {\n  /**\n   * Override the convenience function which fetches a page of results\n   */\n  overrideRequest?: LabelSelectPaginateFn,\n  /**\n   * Override the default settings used in the convenience function to fetch a page of results\n   */\n  requestSettings?: PaginateTypeOverridesFn,\n}\n\n/**\n * Settings to use when the LabelSelect is fetching all resources (not paginating)\n */\nexport type ResourceLabeledSelectSettings = SharedSettings\n\n/**\n * Force a specific mode\n */\nexport enum RESOURCE_LABEL_SELECT_MODE {\n  /**\n   * Fetch all resources\n   */\n  ALL_RESOURCES = 'ALL', // eslint-disable-line no-unused-vars\n  /**\n   * Determine if all resources are fetched given system settings\n   */\n  DYNAMIC = 'DYNAMIC', // eslint-disable-line no-unused-vars\n}\n"]}]}