{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??clonedRuleSet-44.use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/ConfigSummary.vue?vue&type=template&id=479b2bdb&scoped=true&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/ConfigSummary.vue", "mtime": 1755002061656}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/ConfigSummary.vue?vue&type=template&id=479b2bdb&scoped=true&ts=true", "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/ConfigSummary.vue"], "names": ["clusterName", "awsRegion", "kubernetesVersion", "networkingMode", "primaryNodeGroup", "totalNodes", "nodeGroups", "estimatedMonthlyCost"], "mappings": "AAAA,OAAO,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,eAAe,IAAI,gBAAgB,EAAE,gBAAgB,IAAI,iBAAiB,EAAE,WAAW,IAAI,YAAY,EAAE,SAAS,IAAI,UAAU,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,eAAe,IAAI,gBAAgB,EAAE,OAAO,IAAI,QAAQ,EAAE,MAAM,KAAK,CAAA;AAEjV,MAAM,UAAU,GAAG,ECuGZ,KAAK,EAAC,gBAAgB,EAAA,CAAA;ADtG7B,MAAM,UAAU,GAAG,ECuGV,KAAK,EAAC,iBAAiB,EAAA,CAAA;ADtGhC,MAAM,UAAU,GAAG,EC4GR,KAAK,EAAC,cAAc,EAAA,CAAA;AD3G/B,MAAM,UAAU,GAAG,ECsIV,KAAK,EAAC,uBAAuB,EAAA,CAAA;ADrItC,MAAM,UAAU,GAAG;IACjB,GAAG,EAAE,CAAC;IC4IA,KAAK,EAAC,cAAc;CD1I3B,CAAA;AACD,MAAM,UAAU,GAAG,EC2IN,KAAK,EAAC,cAAc,EAAA,CAAA;AD1IjC,MAAM,UAAU,GAAG;IACjB,GAAG,EAAE,CAAC;ICsKA,KAAK,EAAC,OAAO;CDpKpB,CAAA;AACD,MAAM,UAAU,GAAG,EC2KV,KAAK,EAAC,oCAAoC,EAAA,CAAA;AD1KnD,MAAM,UAAU,GAAG,ECgLR,KAAK,EAAC,gBAAgB,EAAA,CAAA;AD/KjC,MAAM,WAAW,GAAG,ECgLP,KAAK,EAAC,WAAW,EAAA,CAAA;AD/K9B,MAAM,WAAW,GAAG,ECiLJ,KAAK,EAAC,YAAY,EAAA,CAAA;ADhLlC,MAAM,WAAW,GAAG,ECwLX,KAAK,EAAC,uBAAuB,EAAA,CAAA;ADtLtC,MAAM,UAAU,MAAM,CAAC,IAAS,EAAC,MAAW,EAAC,MAAW,EAAC,MAAW,EAAC,KAAU,EAAC,QAAa;IAC3F,MAAM,qBAAqB,GAAG,iBAAiB,CAAC,YAAY,CAAE,CAAA;IAC9D,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,QAAQ,CAAE,CAAA;IAEtD,OAAO,CAAC,UAAU,EAAE,ECgFpB,mBAAA,CAsHM,KAAA,EAtHN,UAsHM,EAAA;QArHJ,mBAAA,CA+BM,KAAA,EA/BN,UA+BM,EAAA;YD9GJ,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GCgFvB,mBAAA,CAGK,IAAA,EAAA,IAAA,EAAA;gBAFH,mBAAA,CAA+B,GAAA,EAAA,EAA5B,KAAK,EAAC,mBAAmB,EAAA,CAAA;gBD/E5B,gBAAgB,CC+Ee,yCAEjC,CAAA;aDhFC,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;YACpB,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;YCiF7C,mBAAA,CAwBM,KAAA,EAxBN,UAwBM,EAAA;gBAvBJ,YAAA,CAIE,qBAAA,EAAA;oBAHA,IAAI,EAAC,cAAc;oBAClB,KAAK,EAAEA,IAAAA,CAAAA,WAAW;oBACnB,KAAK,EAAC,cAAc;iBD/ErB,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,CAAC;gBAClC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;gBCiF7C,YAAA,CAIE,qBAAA,EAAA;oBAHA,IAAI,EAAC,YAAY;oBAChB,KAAK,EAAEC,IAAAA,CAAAA,SAAS;oBACjB,KAAK,EAAC,cAAc;iBD/ErB,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,CAAC;gBAClC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;gBCiF7C,YAAA,CAIE,qBAAA,EAAA;oBAHA,IAAI,EAAC,oBAAoB;oBACxB,KAAK,EAAEC,IAAAA,CAAAA,iBAAiB;oBACzB,KAAK,EAAC,cAAc;iBD/ErB,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,CAAC;gBAClC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;gBCiF7C,YAAA,CAIE,qBAAA,EAAA;oBAHA,IAAI,EAAC,gBAAgB;oBACpB,KAAK,EAAEC,IAAAA,CAAAA,cAAc;oBACtB,KAAK,EAAC,cAAc;iBD/ErB,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,CAAC;aACnC,CAAC;SACH,CAAC;QACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;QCiF/C,mBAAA,CA6CM,KAAA,EA7CN,UA6CM,EAAA;YD5HJ,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GCgFvB,mBAAA,CAGK,IAAA,EAAA,IAAA,EAAA;gBAFH,mBAAA,CAA6B,GAAA,EAAA,EAA1B,KAAK,EAAC,iBAAiB,EAAA,CAAA;gBD/E1B,gBAAgB,CC+Ea,sCAE/B,CAAA;aDhFC,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;YACpB,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;YAC7C,CCiFQC,IAAAA,CAAAA,gBAAgB,CAAA;gBDhFtB,CAAC,CAAC,CAAC,UAAU,EAAE,EC+EjB,mBAAA,CA6BM,KAAA,EA7BN,UA6BM,EAAA;oBAzBJ,mBAAA,CAwBM,KAAA,EAxBN,UAwBM,EAAA;wBAvBJ,YAAA,CAIE,qBAAA,EAAA;4BAHA,IAAI,EAAC,eAAe;4BACnB,KAAK,EAAEA,IAAAA,CAAAA,gBAAgB,CAAC,YAAY;4BACrC,KAAK,EAAC,cAAc;yBDjFjB,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,CAAC;wBAClC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;wBCmFjD,YAAA,CAIE,qBAAA,EAAA;4BAHA,IAAI,EAAC,WAAW;4BACf,KAAK,EAAA,GAAKA,IAAAA,CAAAA,gBAAgB,CAAC,QAAQ,KAAA;4BACpC,KAAK,EAAC,cAAc;yBDjFjB,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,CAAC;wBAClC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;wBCmFjD,YAAA,CAIE,qBAAA,EAAA;4BAHA,IAAI,EAAC,cAAc;4BAClB,KAAK,EAAA,GAAKC,IAAAA,CAAAA,UAAU,CAAC,GAAG,MAAMA,IAAAA,CAAAA,UAAU,CAAC,GAAG,QAAA;4BAC7C,KAAK,EAAC,cAAc;yBDjFjB,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,CAAC;wBAClC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;wBCmFjD,YAAA,CAIE,qBAAA,EAAA;4BAHA,IAAI,EAAC,cAAc;4BAClB,KAAK,EAAA,GAAKA,IAAAA,CAAAA,UAAU,CAAC,OAAO,QAAA;4BAC7B,KAAK,EAAC,cAAc;yBDjFjB,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,CAAC;qBACnC,CAAC;iBACH,CAAC,CAAC;gBACL,CAAC,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;YACrC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;YAC/C,CCkFQC,IAAAA,CAAAA,UAAU,CAAC,MAAM,GAAA,CAAA,CAAA;gBDjFvB,CAAC,CAAC,CAAC,UAAU,EAAE,ECgFjB,mBAAA,CAOM,KAAA,EAPN,UAOM,EAAA;oBAHJ,YAAA,CAES,iBAAA,EAAA,EAFD,KAAK,EAAC,MAAM,EAAA,EAAA;wBDlFd,OAAO,EAAE,QAAQ,CCmFrB,GAAqD,EAAA,CAAA;4BAArD,mBAAA,CAAqD,GAAA,EAAA,IAAA,EAAA,gBAAA,CAA/CA,IAAAA,CAAAA,UAAU,CAAC,MAAM,CAAA,GAAG,yBAAuB,EAAA,CAAA,CAAA,UAAA,CAAA;yBDjF5C,CAAC;wBACF,CAAC,EAAE,CAAC,CAAC,YAAY;qBAClB,CAAC;iBACH,CAAC,CAAC;gBACL,CAAC,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;SACtC,CAAC;QACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;QCgF/C,mBAAA,CAeM,KAAA,EAfN,UAeM,EAAA;YD7FJ,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GC+EzB,mBAAA,CAGK,IAAA,EAAA,IAAA,EAAA;gBAFH,mBAAA,CAA8B,GAAA,EAAA,EAA3B,KAAK,EAAC,kBAAkB,EAAA,CAAA;gBD9E3B,gBAAgB,CC8Ec,kCAEhC,CAAA;aD/EC,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;YACpB,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;YCgF/C,mBAAA,CAQM,KAAA,EARN,UAQM,EAAA;gBAPJ,mBAAA,CAGM,KAAA,EAHN,WAGM,EAAA;oBDjFJ,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GC+EzB,mBAAA,CAAiD,MAAA,EAAA,EAA3C,KAAK,EAAC,YAAY,EAAA,EAAC,mBAAiB,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA;oBD9E1C,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;oBC+E/C,mBAAA,CAA0D,MAAA,EAA1D,WAA0D,EAAA,gBAAA,CAA9BC,IAAAA,CAAAA,oBAAoB,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;iBD7EjD,CAAC;gBACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;gBAC/C,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GC6EzB,mBAAA,CAEI,GAAA,EAAA,EAFD,KAAK,EAAC,iBAAiB,EAAA,EAAC,8IAE3B,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA;aD9ED,CAAC;SACH,CAAC;QACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;QCgF/C,mBAAA,CAmBM,KAAA,EAnBN,WAmBM,EAAA;YAlBJ,YAAA,CAiBS,iBAAA,EAAA;gBAhBP,KAAK,EAAC,SAAS;gBACf,KAAK,EAAC,cAAc;aD9ErB,EAAE;gBACD,OAAO,EAAE,QAAQ,CC+EjB,GAYM,EAAA,CAAA,MAAA,CAAA,EAAA,CAAA,IAAA,CAAA,MAAA,CAAA,EAAA,CAAA,GAAA;oBAZN,mBAAA,CAYM,KAAA,EAAA,EAZD,KAAK,EAAC,eAAe,EAAA,EAAA;wBACxB,mBAAA,CAAyC,GAAA,EAAA,EAAtC,KAAK,EAAC,6BAA6B,EAAA,CAAA;wBD7EpC,gBAAgB,EAAE;wBC8EpB,mBAAA,CASM,KAAA,EAAA,IAAA,EAAA;4BARJ,mBAAA,CAAsC,IAAA,EAAA,IAAA,EAAlC,+BAA6B,CAAA;4BD5E/B,gBAAgB,EAAE;4BC6EpB,mBAAA,CAA6E,GAAA,EAAA,IAAA,EAA1E,wEAAsE,CAAA;4BD3EvE,gBAAgB,EAAE;4BC4EpB,mBAAA,CAKK,IAAA,EAAA,IAAA,EAAA;gCAJH,mBAAA,CAAmC,IAAA,EAAA,IAAA,EAA/B,4BAA0B,CAAA;gCD1E5B,gBAAgB,EAAE;gCC2EpB,mBAAA,CAA8C,IAAA,EAAA,IAAA,EAA1C,uCAAqC,CAAA;gCDzEvC,gBAAgB,EAAE;gCC0EpB,mBAAA,CAAmC,IAAA,EAAA,IAAA,EAA/B,4BAA0B,CAAA;gCDxE5B,gBAAgB,EAAE;gCCyEpB,mBAAA,CAA8C,IAAA,EAAA,IAAA,EAA1C,uCAAqC,CAAA;6BDvExC,CAAC;yBACH,CAAC;qBACH,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC;iBACpB,CAAC,CAAC;gBACH,CAAC,EAAE,CAAC,CAAC,YAAY;gBACjB,EAAE,EAAE,CAAC,EAAE,CAAC;aACT,CAAC;SACH,CAAC;KACH,CAAC,CAAC,CAAA;AACL,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/ConfigSummary.vue.tsx", "sourceRoot": "", "sourcesContent": ["import { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, withCtx as _withCtx } from \"vue\"\n\nconst _hoisted_1 = { class: \"config-summary\" }\nconst _hoisted_2 = { class: \"summary-section\" }\nconst _hoisted_3 = { class: \"summary-grid\" }\nconst _hoisted_4 = { class: \"summary-section mt-20\" }\nconst _hoisted_5 = {\n  key: 0,\n  class: \"node-summary\"\n}\nconst _hoisted_6 = { class: \"summary-grid\" }\nconst _hoisted_7 = {\n  key: 1,\n  class: \"mt-10\"\n}\nconst _hoisted_8 = { class: \"summary-section mt-20 cost-section\" }\nconst _hoisted_9 = { class: \"cost-breakdown\" }\nconst _hoisted_10 = { class: \"cost-main\" }\nconst _hoisted_11 = { class: \"cost-value\" }\nconst _hoisted_12 = { class: \"summary-section mt-20\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_LabelValue = _resolveComponent(\"LabelValue\")!\n  const _component_Banner = _resolveComponent(\"Banner\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _cache[3] || (_cache[3] = _createElementVNode(\"h3\", null, [\n        _createElementVNode(\"i\", { class: \"icon icon-cluster\" }),\n        _createTextVNode(\"\\n        Cluster Configuration\\n      \")\n      ], -1 /* CACHED */)),\n      _cache[4] || (_cache[4] = _createTextVNode()),\n      _createElementVNode(\"div\", _hoisted_3, [\n        _createVNode(_component_LabelValue, {\n          name: \"Cluster Name\",\n          value: _ctx.clusterName,\n          class: \"summary-item\"\n        }, null, 8 /* PROPS */, [\"value\"]),\n        _cache[0] || (_cache[0] = _createTextVNode()),\n        _createVNode(_component_LabelValue, {\n          name: \"AWS Region\",\n          value: _ctx.awsRegion,\n          class: \"summary-item\"\n        }, null, 8 /* PROPS */, [\"value\"]),\n        _cache[1] || (_cache[1] = _createTextVNode()),\n        _createVNode(_component_LabelValue, {\n          name: \"Kubernetes Version\",\n          value: _ctx.kubernetesVersion,\n          class: \"summary-item\"\n        }, null, 8 /* PROPS */, [\"value\"]),\n        _cache[2] || (_cache[2] = _createTextVNode()),\n        _createVNode(_component_LabelValue, {\n          name: \"Network Access\",\n          value: _ctx.networkingMode,\n          class: \"summary-item\"\n        }, null, 8 /* PROPS */, [\"value\"])\n      ])\n    ]),\n    _cache[18] || (_cache[18] = _createTextVNode()),\n    _createElementVNode(\"div\", _hoisted_4, [\n      _cache[8] || (_cache[8] = _createElementVNode(\"h3\", null, [\n        _createElementVNode(\"i\", { class: \"icon icon-nodes\" }),\n        _createTextVNode(\"\\n        Node Configuration\\n      \")\n      ], -1 /* CACHED */)),\n      _cache[9] || (_cache[9] = _createTextVNode()),\n      (_ctx.primaryNodeGroup)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [\n            _createElementVNode(\"div\", _hoisted_6, [\n              _createVNode(_component_LabelValue, {\n                name: \"Instance Type\",\n                value: _ctx.primaryNodeGroup.instanceType,\n                class: \"summary-item\"\n              }, null, 8 /* PROPS */, [\"value\"]),\n              _cache[5] || (_cache[5] = _createTextVNode()),\n              _createVNode(_component_LabelValue, {\n                name: \"Disk Size\",\n                value: `${_ctx.primaryNodeGroup.diskSize} GB`,\n                class: \"summary-item\"\n              }, null, 8 /* PROPS */, [\"value\"]),\n              _cache[6] || (_cache[6] = _createTextVNode()),\n              _createVNode(_component_LabelValue, {\n                name: \"Auto-scaling\",\n                value: `${_ctx.totalNodes.min} - ${_ctx.totalNodes.max} nodes`,\n                class: \"summary-item\"\n              }, null, 8 /* PROPS */, [\"value\"]),\n              _cache[7] || (_cache[7] = _createTextVNode()),\n              _createVNode(_component_LabelValue, {\n                name: \"Initial Size\",\n                value: `${_ctx.totalNodes.desired} nodes`,\n                class: \"summary-item\"\n              }, null, 8 /* PROPS */, [\"value\"])\n            ])\n          ]))\n        : _createCommentVNode(\"v-if\", true),\n      _cache[10] || (_cache[10] = _createTextVNode()),\n      (_ctx.nodeGroups.length > 1)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [\n            _createVNode(_component_Banner, { color: \"info\" }, {\n              default: _withCtx(() => [\n                _createElementVNode(\"p\", null, _toDisplayString(_ctx.nodeGroups.length) + \" node groups configured\", 1 /* TEXT */)\n              ]),\n              _: 1 /* STABLE */\n            })\n          ]))\n        : _createCommentVNode(\"v-if\", true)\n    ]),\n    _cache[19] || (_cache[19] = _createTextVNode()),\n    _createElementVNode(\"div\", _hoisted_8, [\n      _cache[15] || (_cache[15] = _createElementVNode(\"h3\", null, [\n        _createElementVNode(\"i\", { class: \"icon icon-dollar\" }),\n        _createTextVNode(\"\\n        Estimated Cost\\n      \")\n      ], -1 /* CACHED */)),\n      _cache[16] || (_cache[16] = _createTextVNode()),\n      _createElementVNode(\"div\", _hoisted_9, [\n        _createElementVNode(\"div\", _hoisted_10, [\n          _cache[11] || (_cache[11] = _createElementVNode(\"span\", { class: \"cost-label\" }, \"Monthly estimate:\", -1 /* CACHED */)),\n          _cache[12] || (_cache[12] = _createTextVNode()),\n          _createElementVNode(\"span\", _hoisted_11, _toDisplayString(_ctx.estimatedMonthlyCost), 1 /* TEXT */)\n        ]),\n        _cache[13] || (_cache[13] = _createTextVNode()),\n        _cache[14] || (_cache[14] = _createElementVNode(\"p\", { class: \"cost-disclaimer\" }, \"\\n          * This is a rough estimate based on instance types and does not include data transfer, storage, or other AWS services.\\n        \", -1 /* CACHED */))\n      ])\n    ]),\n    _cache[20] || (_cache[20] = _createTextVNode()),\n    _createElementVNode(\"div\", _hoisted_12, [\n      _createVNode(_component_Banner, {\n        color: \"success\",\n        class: \"ready-banner\"\n      }, {\n        default: _withCtx(() => _cache[17] || (_cache[17] = [\n          _createElementVNode(\"div\", { class: \"ready-content\" }, [\n            _createElementVNode(\"i\", { class: \"icon icon-checkmark icon-2x\" }),\n            _createTextVNode(),\n            _createElementVNode(\"div\", null, [\n              _createElementVNode(\"h4\", null, \"Ready to create your cluster!\"),\n              _createTextVNode(),\n              _createElementVNode(\"p\", null, \"Your cluster will be created with production-ready defaults including:\"),\n              _createTextVNode(),\n              _createElementVNode(\"ul\", null, [\n                _createElementVNode(\"li\", null, \"Automatic security updates\"),\n                _createTextVNode(),\n                _createElementVNode(\"li\", null, \"Network isolation and security groups\"),\n                _createTextVNode(),\n                _createElementVNode(\"li\", null, \"CloudWatch logging enabled\"),\n                _createTextVNode(),\n                _createElementVNode(\"li\", null, \"IAM roles for service accounts (IRSA)\")\n              ])\n            ])\n          ], -1 /* CACHED */)\n        ])),\n        _: 1 /* STABLE */,\n        __: [17]\n      })\n    ])\n  ]))\n}", "<script lang=\"ts\">\nimport { defineComponent, PropType } from 'vue';\nimport { EKSConfig, EKSNodeGroup, NormanCluster } from '../../types';\nimport LabelValue from '@shell/components/LabelValue.vue';\nimport Banner from '@components/Banner/Banner.vue';\n\nexport default defineComponent({\n  name: 'ConfigSummary',\n\n  components: {\n    LabelValue,\n    Banner\n  },\n\n  props: {\n    normanCluster: {\n      type:     Object as PropType<NormanCluster>,\n      required: true\n    },\n    config: {\n      type:     Object as PropType<EKSConfig>,\n      required: true\n    },\n    nodeGroups: {\n      type:    Array as PropType<EKSNodeGroup[]>,\n      default: () => []\n    },\n    region: {\n      type:    String,\n      default: ''\n    }\n  },\n\n  computed: {\n    clusterName(): string {\n      return this.normanCluster?.name || 'Not set';\n    },\n\n    awsRegion(): string {\n      return this.config?.region || this.region || 'Not set';\n    },\n\n    kubernetesVersion(): string {\n      return this.config?.kubernetesVersion || 'Latest stable';\n    },\n\n    totalNodes(): { min: number; max: number; desired: number } {\n      const totals = this.nodeGroups.reduce((acc, group) => {\n        return {\n          min:     acc.min + (group.minSize || 0),\n          max:     acc.max + (group.maxSize || 0),\n          desired: acc.desired + (group.desiredSize || 0)\n        };\n      }, { min: 0, max: 0, desired: 0 });\n\n      return totals;\n    },\n\n    primaryNodeGroup(): EKSNodeGroup | null {\n      return this.nodeGroups[0] || null;\n    },\n\n    estimatedMonthlyCost(): string {\n      // Simple cost estimation\n      const costMap: Record<string, number> = {\n        't3.small':   15,\n        't3.medium':  30,\n        't3.large':   60,\n        't3.xlarge':  120,\n        't4g.small':  12,\n        't4g.medium': 24,\n        't4g.large':  48,\n        'm5.large':   70,\n        'm5.xlarge':  140,\n        'm6i.large':  70,\n        'm6i.xlarge': 140,\n      };\n\n      let totalCost = 0;\n      this.nodeGroups.forEach(group => {\n        const instanceCost = group.instanceType ? costMap[group.instanceType] || 50 : 50;\n        totalCost += instanceCost * (group.desiredSize || 2);\n      });\n\n      // Add EKS control plane cost ($0.10/hour = ~$73/month)\n      totalCost += 73;\n\n      return `$${totalCost}`;\n    },\n\n    networkingMode(): string {\n      if (this.config?.publicAccess && !this.config?.privateAccess) {\n        return 'Public';\n      } else if (!this.config?.publicAccess && this.config?.privateAccess) {\n        return 'Private';\n      } else if (this.config?.publicAccess && this.config?.privateAccess) {\n        return 'Public and Private';\n      }\n      return 'Default (Public)';\n    }\n  }\n});\n</script>\n\n<template>\n  <div class=\"config-summary\">\n    <div class=\"summary-section\">\n      <h3>\n        <i class=\"icon icon-cluster\" />\n        Cluster Configuration\n      </h3>\n      \n      <div class=\"summary-grid\">\n        <LabelValue\n          name=\"Cluster Name\"\n          :value=\"clusterName\"\n          class=\"summary-item\"\n        />\n        \n        <LabelValue\n          name=\"AWS Region\"\n          :value=\"awsRegion\"\n          class=\"summary-item\"\n        />\n        \n        <LabelValue\n          name=\"Kubernetes Version\"\n          :value=\"kubernetesVersion\"\n          class=\"summary-item\"\n        />\n        \n        <LabelValue\n          name=\"Network Access\"\n          :value=\"networkingMode\"\n          class=\"summary-item\"\n        />\n      </div>\n    </div>\n\n    <div class=\"summary-section mt-20\">\n      <h3>\n        <i class=\"icon icon-nodes\" />\n        Node Configuration\n      </h3>\n      \n      <div\n        v-if=\"primaryNodeGroup\"\n        class=\"node-summary\"\n      >\n        <div class=\"summary-grid\">\n          <LabelValue\n            name=\"Instance Type\"\n            :value=\"primaryNodeGroup.instanceType\"\n            class=\"summary-item\"\n          />\n          \n          <LabelValue\n            name=\"Disk Size\"\n            :value=\"`${primaryNodeGroup.diskSize} GB`\"\n            class=\"summary-item\"\n          />\n          \n          <LabelValue\n            name=\"Auto-scaling\"\n            :value=\"`${totalNodes.min} - ${totalNodes.max} nodes`\"\n            class=\"summary-item\"\n          />\n          \n          <LabelValue\n            name=\"Initial Size\"\n            :value=\"`${totalNodes.desired} nodes`\"\n            class=\"summary-item\"\n          />\n        </div>\n      </div>\n\n      <div\n        v-if=\"nodeGroups.length > 1\"\n        class=\"mt-10\"\n      >\n        <Banner color=\"info\">\n          <p>{{ nodeGroups.length }} node groups configured</p>\n        </Banner>\n      </div>\n    </div>\n\n    <div class=\"summary-section mt-20 cost-section\">\n      <h3>\n        <i class=\"icon icon-dollar\" />\n        Estimated Cost\n      </h3>\n      \n      <div class=\"cost-breakdown\">\n        <div class=\"cost-main\">\n          <span class=\"cost-label\">Monthly estimate:</span>\n          <span class=\"cost-value\">{{ estimatedMonthlyCost }}</span>\n        </div>\n        <p class=\"cost-disclaimer\">\n          * This is a rough estimate based on instance types and does not include data transfer, storage, or other AWS services.\n        </p>\n      </div>\n    </div>\n\n    <div class=\"summary-section mt-20\">\n      <Banner\n        color=\"success\"\n        class=\"ready-banner\"\n      >\n        <div class=\"ready-content\">\n          <i class=\"icon icon-checkmark icon-2x\" />\n          <div>\n            <h4>Ready to create your cluster!</h4>\n            <p>Your cluster will be created with production-ready defaults including:</p>\n            <ul>\n              <li>Automatic security updates</li>\n              <li>Network isolation and security groups</li>\n              <li>CloudWatch logging enabled</li>\n              <li>IAM roles for service accounts (IRSA)</li>\n            </ul>\n          </div>\n        </div>\n      </Banner>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.config-summary {\n  .summary-section {\n    padding: 20px;\n    background: var(--body-bg);\n    border: 1px solid var(--border);\n    border-radius: var(--border-radius);\n\n    h3 {\n      margin: 0 0 20px 0;\n      font-size: 18px;\n      font-weight: 600;\n      display: flex;\n      align-items: center;\n      gap: 10px;\n      color: var(--text-default);\n\n      i {\n        color: var(--primary);\n      }\n    }\n  }\n\n  .summary-grid {\n    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n    gap: 20px;\n\n    .summary-item {\n      padding: 10px;\n      background: var(--nav-bg);\n      border-radius: var(--border-radius);\n      \n      ::v-deep .labeled-value {\n        .label {\n          color: var(--text-muted);\n          font-size: 12px;\n          text-transform: uppercase;\n          margin-bottom: 5px;\n        }\n\n        .value {\n          color: var(--text-default);\n          font-size: 16px;\n          font-weight: 500;\n        }\n      }\n    }\n  }\n\n  .cost-section {\n    background: linear-gradient(135deg, var(--body-bg) 0%, var(--nav-bg) 100%);\n  }\n\n  .cost-breakdown {\n    .cost-main {\n      display: flex;\n      align-items: baseline;\n      gap: 15px;\n      margin-bottom: 10px;\n\n      .cost-label {\n        font-size: 16px;\n        color: var(--text-muted);\n      }\n\n      .cost-value {\n        font-size: 32px;\n        font-weight: 600;\n        color: var(--success);\n      }\n    }\n\n    .cost-disclaimer {\n      font-size: 12px;\n      color: var(--text-muted);\n      font-style: italic;\n      margin: 0;\n    }\n  }\n\n  .ready-banner {\n    .ready-content {\n      display: flex;\n      gap: 20px;\n      align-items: flex-start;\n\n      i {\n        color: var(--success);\n        flex-shrink: 0;\n      }\n\n      h4 {\n        margin: 0 0 10px 0;\n        font-size: 18px;\n        font-weight: 600;\n      }\n\n      p {\n        margin: 0 0 10px 0;\n      }\n\n      ul {\n        margin: 0;\n        padding-left: 20px;\n\n        li {\n          margin: 5px 0;\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}