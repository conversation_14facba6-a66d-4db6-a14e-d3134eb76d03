{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/LabelValue.vue?vue&type=template&id=8d435bc0&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/LabelValue.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVuZGVyU2xvdCBhcyBfcmVuZGVyU2xvdCwgdG9EaXNwbGF5U3RyaW5nIGFzIF90b0Rpc3BsYXlTdHJpbmcsIGNyZWF0ZVRleHRWTm9kZSBhcyBfY3JlYXRlVGV4dFZOb2RlLCBjcmVhdGVFbGVtZW50Vk5vZGUgYXMgX2NyZWF0ZUVsZW1lbnRWTm9kZSwgb3BlbkJsb2NrIGFzIF9vcGVuQmxvY2ssIGNyZWF0ZUVsZW1lbnRCbG9jayBhcyBfY3JlYXRlRWxlbWVudEJsb2NrIH0gZnJvbSAidnVlIgoKY29uc3QgX2hvaXN0ZWRfMSA9IHsgY2xhc3M6ICJsYWJlbCIgfQpjb25zdCBfaG9pc3RlZF8yID0geyBjbGFzczogInRleHQtbGFiZWwiIH0KY29uc3QgX2hvaXN0ZWRfMyA9IHsgY2xhc3M6ICJ2YWx1ZSIgfQoKZXhwb3J0IGZ1bmN0aW9uIHJlbmRlcihfY3R4LCBfY2FjaGUsICRwcm9wcywgJHNldHVwLCAkZGF0YSwgJG9wdGlvbnMpIHsKICByZXR1cm4gKF9vcGVuQmxvY2soKSwgX2NyZWF0ZUVsZW1lbnRCbG9jaygiZGl2IiwgX2hvaXN0ZWRfMSwgWwogICAgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfMiwgWwogICAgICBfcmVuZGVyU2xvdChfY3R4LiRzbG90cywgIm5hbWUiLCB7fSwgKCkgPT4gWwogICAgICAgIF9jcmVhdGVUZXh0Vk5vZGUoX3RvRGlzcGxheVN0cmluZygkcHJvcHMubmFtZSksIDEgLyogVEVYVCAqLykKICAgICAgXSwgdHJ1ZSkKICAgIF0pLAogICAgX2NhY2hlWzBdIHx8IChfY2FjaGVbMF0gPSBfY3JlYXRlVGV4dFZOb2RlKCkpLAogICAgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfMywgWwogICAgICBfcmVuZGVyU2xvdChfY3R4LiRzbG90cywgInZhbHVlIiwge30sICgpID0+IFsKICAgICAgICBfY3JlYXRlVGV4dFZOb2RlKF90b0Rpc3BsYXlTdHJpbmcoJHByb3BzLnZhbHVlKSwgMSAvKiBURVhUICovKQogICAgICBdLCB0cnVlKQogICAgXSkKICBdKSkKfQ=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/LabelValue.vue"], "names": [], "mappings": ";;qBAgBO,KAAK,EAAC,OAAO;qBACX,KAAK,EAAC,YAAY;qBAMlB,KAAK,EAAC,OAAO;;;wBAPpB,oBAYM,OAZN,UAYM;IAXJ,oBAIM,OAJN,UAIM;MAHJ,YAEO,yBAFP,CAEO;0CADF,WAAI;;;;IAIX,oBAIM,OAJN,UAIM;MAHJ,YAEO,0BAFP,CAEO;0CADF,YAAK", "sourcesContent": ["<script>\nexport default {\n  props: {\n    name: {\n      type:     String,\n      required: true\n    },\n    value: {\n      type:    [Number, String, undefined],\n      default: ''\n    }\n  }\n};\n</script>\n\n<template>\n  <div class=\"label\">\n    <div class=\"text-label\">\n      <slot name=\"name\">\n        {{ name }}\n      </slot>\n    </div>\n\n    <div class=\"value\">\n      <slot name=\"value\">\n        {{ value }}\n      </slot>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.label {\n  display: flex;\n  flex-direction: column;\n\n  .value {\n    font-size: 14px;\n    line-height: $input-line-height;\n  }\n}\n</style>\n"]}]}