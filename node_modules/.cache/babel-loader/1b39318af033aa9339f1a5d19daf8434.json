{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Int.vue?vue&type=template&id=ad1e35cc", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Int.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVzb2x2ZUNvbXBvbmVudCBhcyBfcmVzb2x2ZUNvbXBvbmVudCwgY3JlYXRlVk5vZGUgYXMgX2NyZWF0ZVZOb2RlLCBjcmVhdGVFbGVtZW50Vk5vZGUgYXMgX2NyZWF0ZUVsZW1lbnRWTm9kZSwgdG9EaXNwbGF5U3RyaW5nIGFzIF90b0Rpc3BsYXlTdHJpbmcsIG9wZW5CbG9jayBhcyBfb3BlbkJsb2NrLCBjcmVhdGVFbGVtZW50QmxvY2sgYXMgX2NyZWF0ZUVsZW1lbnRCbG9jaywgY3JlYXRlQ29tbWVudFZOb2RlIGFzIF9jcmVhdGVDb21tZW50Vk5vZGUsIGNyZWF0ZVRleHRWTm9kZSBhcyBfY3JlYXRlVGV4dFZOb2RlIH0gZnJvbSAidnVlIgoKY29uc3QgX2hvaXN0ZWRfMSA9IFsiZGF0YS10ZXN0aWQiXQpjb25zdCBfaG9pc3RlZF8yID0geyBjbGFzczogImNvbCBzcGFuLTYiIH0KY29uc3QgX2hvaXN0ZWRfMyA9IFsiZGF0YS10ZXN0aWQiXQoKZXhwb3J0IGZ1bmN0aW9uIHJlbmRlcihfY3R4LCBfY2FjaGUsICRwcm9wcywgJHNldHVwLCAkZGF0YSwgJG9wdGlvbnMpIHsKICBjb25zdCBfY29tcG9uZW50X0xhYmVsZWRJbnB1dCA9IF9yZXNvbHZlQ29tcG9uZW50KCJMYWJlbGVkSW5wdXQiKQoKICByZXR1cm4gKF9vcGVuQmxvY2soKSwgX2NyZWF0ZUVsZW1lbnRCbG9jaygiZGl2IiwgewogICAgImRhdGEtdGVzdGlkIjogYGludC1yb3ctJHtfY3R4LnF1ZXN0aW9uLnZhcmlhYmxlfWAsCiAgICBjbGFzczogInJvdyIKICB9LCBbCiAgICBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCBfaG9pc3RlZF8yLCBbCiAgICAgIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X0xhYmVsZWRJbnB1dCwgewogICAgICAgIHR5cGU6ICJ0ZXh0IiwKICAgICAgICBtb2RlOiBfY3R4Lm1vZGUsCiAgICAgICAgbGFiZWw6IF9jdHguZGlzcGxheUxhYmVsLAogICAgICAgIHBsYWNlaG9sZGVyOiBfY3R4LnF1ZXN0aW9uLmRlZmF1bHQsCiAgICAgICAgcmVxdWlyZWQ6IF9jdHgucXVlc3Rpb24ucmVxdWlyZWQsCiAgICAgICAgdmFsdWU6IF9jdHgudmFsdWUsCiAgICAgICAgZGlzYWJsZWQ6IF9jdHguZGlzYWJsZWQsCiAgICAgICAgdG9vbHRpcDogX2N0eC5kaXNwbGF5VG9vbHRpcCwKICAgICAgICBydWxlczogX2N0eC5ydWxlcywKICAgICAgICAiZGF0YS10ZXN0aWQiOiBgaW50LWlucHV0LSR7X2N0eC5xdWVzdGlvbi52YXJpYWJsZX1gLAogICAgICAgICJvblVwZGF0ZTp2YWx1ZSI6IF9jYWNoZVswXSB8fCAoX2NhY2hlWzBdID0gJGV2ZW50ID0+IHtfY3R4LnZhbCA9IHBhcnNlSW50KCRldmVudCwgMTApOyBpZiAoICFpc05hTihfY3R4LnZhbCkgKSB7IF9jdHguJGVtaXQoJ3VwZGF0ZTp2YWx1ZScsIF9jdHgudmFsKSB9fSkKICAgICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgWyJtb2RlIiwgImxhYmVsIiwgInBsYWNlaG9sZGVyIiwgInJlcXVpcmVkIiwgInZhbHVlIiwgImRpc2FibGVkIiwgInRvb2x0aXAiLCAicnVsZXMiLCAiZGF0YS10ZXN0aWQiXSkKICAgIF0pLAogICAgX2NhY2hlWzFdIHx8IChfY2FjaGVbMV0gPSBfY3JlYXRlVGV4dFZOb2RlKCkpLAogICAgKF9jdHguc2hvd0Rlc2NyaXB0aW9uKQogICAgICA/IChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soImRpdiIsIHsKICAgICAgICAgIGtleTogMCwKICAgICAgICAgICJkYXRhLXRlc3RpZCI6IGBpbnQtZGVzY3JpcHRpb24tJHtfY3R4LnF1ZXN0aW9uLnZhcmlhYmxlfWAsCiAgICAgICAgICBjbGFzczogImNvbCBzcGFuLTYgbXQtMTAiCiAgICAgICAgfSwgX3RvRGlzcGxheVN0cmluZyhfY3R4LmRpc3BsYXlEZXNjcmlwdGlvbiksIDkgLyogVEVYVCwgUFJPUFMgKi8sIF9ob2lzdGVkXzMpKQogICAgICA6IF9jcmVhdGVDb21tZW50Vk5vZGUoInYtaWYiLCB0cnVlKQogIF0sIDggLyogUFJPUFMgKi8sIF9ob2lzdGVkXzEpKQp9"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Int.vue"], "names": ["question", "mode", "displayLabel", "value", "disabled", "displayTooltip", "rules", "val", "$emit", "showDescription", "displayDescription"], "mappings": ";;;qBAiBS,KAAK,EAAC,YAAY;;;;;;wBAJzB,oBA0BM;IAzBH,aAAW,aAAaA,aAAQ,CAAC,QAAQ;IAC1C,KAAK,EAAC,KAAK;;IAEX,oBAcM,OAdN,UAcM;MAbJ,aAYE;QAXA,IAAI,EAAC,MAAM;QACV,IAAI,EAAEC,SAAI;QACV,KAAK,EAAEC,iBAAY;QACnB,WAAW,EAAEF,aAAQ,CAAC,OAAO;QAC7B,QAAQ,EAAEA,aAAQ,CAAC,QAAQ;QAC3B,KAAK,EAAEG,UAAK;QACZ,QAAQ,EAAEC,aAAQ;QAClB,OAAO,EAAEC,mBAAc;QACvB,KAAK,EAAEC,UAAK;QACZ,aAAW,eAAeN,aAAQ,CAAC,QAAQ;QAC3C,gBAAY,uCAAEO,QAAG,GAAG,QAAQ,CAAC,MAAM,aAAa,KAAK,CAACA,QAAG,MAAMC,UAAK,iBAAiBD,QAAG;;;;KAIrFE,oBAAe;uBADvB,oBAMM;;UAJH,aAAW,qBAAqBT,aAAQ,CAAC,QAAQ;UAClD,KAAK,EAAC,kBAAkB;4BAErBU,uBAAkB", "sourcesContent": ["<script>\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport Question from './Question';\n\nexport default {\n  emits: ['update:value'],\n\n  components: { LabeledInput },\n  mixins:     [Question]\n};\n</script>\n\n<template>\n  <div\n    :data-testid=\"`int-row-${question.variable}`\"\n    class=\"row\"\n  >\n    <div class=\"col span-6\">\n      <LabeledInput\n        type=\"text\"\n        :mode=\"mode\"\n        :label=\"displayLabel\"\n        :placeholder=\"question.default\"\n        :required=\"question.required\"\n        :value=\"value\"\n        :disabled=\"disabled\"\n        :tooltip=\"displayTooltip\"\n        :rules=\"rules\"\n        :data-testid=\"`int-input-${question.variable}`\"\n        @update:value=\"val = parseInt($event, 10); if ( !isNaN(val) ) { $emit('update:value', val) }\"\n      />\n    </div>\n    <div\n      v-if=\"showDescription\"\n      :data-testid=\"`int-description-${question.variable}`\"\n      class=\"col span-6 mt-10\"\n    >\n      {{ displayDescription }}\n    </div>\n  </div>\n</template>\n"]}]}