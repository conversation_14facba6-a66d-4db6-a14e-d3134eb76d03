{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Import.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Import.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCB7IG1hcEdldHRlcnMgfSBmcm9tICd2dWV4JzsKaW1wb3J0IHsgQ2FyZCB9IGZyb20gJ0Bjb21wb25lbnRzL0NhcmQnOwppbXBvcnQgeyBCYW5uZXIgfSBmcm9tICdAY29tcG9uZW50cy9CYW5uZXInOwppbXBvcnQgTG9hZGluZyBmcm9tICdAc2hlbGwvY29tcG9uZW50cy9Mb2FkaW5nJzsKaW1wb3J0IFlhbWxFZGl0b3IgZnJvbSAnQHNoZWxsL2NvbXBvbmVudHMvWWFtbEVkaXRvcic7CmltcG9ydCBGaWxlU2VsZWN0b3IgZnJvbSAnQHNoZWxsL2NvbXBvbmVudHMvZm9ybS9GaWxlU2VsZWN0b3InOwppbXBvcnQgQXN5bmNCdXR0b24gZnJvbSAnQHNoZWxsL2NvbXBvbmVudHMvQXN5bmNCdXR0b24nOwppbXBvcnQgTGFiZWxlZFNlbGVjdCBmcm9tICdAc2hlbGwvY29tcG9uZW50cy9mb3JtL0xhYmVsZWRTZWxlY3QnOwppbXBvcnQgU29ydGFibGVUYWJsZSBmcm9tICdAc2hlbGwvY29tcG9uZW50cy9Tb3J0YWJsZVRhYmxlJzsKaW1wb3J0IHsgc29ydEJ5IH0gZnJvbSAnQHNoZWxsL3V0aWxzL3NvcnQnOwppbXBvcnQgeyBleGNlcHRpb25Ub0Vycm9yc0FycmF5IH0gZnJvbSAnQHNoZWxsL3V0aWxzL2Vycm9yJzsKaW1wb3J0IHsgTkFNRVNQQUNFIH0gZnJvbSAnQHNoZWxsL2NvbmZpZy90eXBlcyc7CmltcG9ydCB7IE5BTUUgYXMgTkFNRV9DT0wsIFRZUEUsIE5BTUVTUEFDRSBhcyBOQU1FU1BBQ0VfQ09MLCBBR0UgfSBmcm9tICdAc2hlbGwvY29uZmlnL3RhYmxlLWhlYWRlcnMnOwoKZXhwb3J0IGRlZmF1bHQgewogIGVtaXRzOiBbJ2Nsb3NlJywgJ29uUmVhZHlZYW1sRWRpdG9yJ10sCgogIGNvbXBvbmVudHM6IHsKICAgIEFzeW5jQnV0dG9uLAogICAgQmFubmVyLAogICAgQ2FyZCwKICAgIExvYWRpbmcsCiAgICBZYW1sRWRpdG9yLAogICAgRmlsZVNlbGVjdG9yLAogICAgTGFiZWxlZFNlbGVjdCwKICAgIFNvcnRhYmxlVGFibGUKICB9LAoKICBwcm9wczogewogICAgZGVmYXVsdE5hbWVzcGFjZTogewogICAgICB0eXBlOiAgICBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICdkZWZhdWx0JwogICAgfSwKICB9LAoKICBhc3luYyBmZXRjaCgpIHsKICAgIHRoaXMuYWxsTmFtZXNwYWNlcyA9IGF3YWl0IHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdjbHVzdGVyL2ZpbmRBbGwnLCB7IHR5cGU6IE5BTUVTUEFDRSwgb3B0OiB7IHVybDogJ25hbWVzcGFjZXMnIH0gfSk7CiAgfSwKCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGN1cnJlbnRZYW1sOiAgICcnLAogICAgICBhbGxOYW1lc3BhY2VzOiBbXSwKICAgICAgZXJyb3JzOiAgICAgICAgbnVsbCwKICAgICAgcm93czogICAgICAgICAgbnVsbCwKICAgICAgZG9uZTogICAgICAgICAgZmFsc2UsCiAgICB9OwogIH0sCgogIGNvbXB1dGVkOiB7CiAgICAuLi5tYXBHZXR0ZXJzKFsnY3VycmVudENsdXN0ZXInXSksCgogICAgbmFtZXNwYWNlT3B0aW9ucygpIHsKICAgICAgY29uc3Qgb3V0ID0gdGhpcy5hbGxOYW1lc3BhY2VzLm1hcCgob2JqKSA9PiB7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIGxhYmVsOiBvYmoubmFtZSwKICAgICAgICAgIHZhbHVlOiBvYmoubmFtZSwKICAgICAgICB9OwogICAgICB9KTsKCiAgICAgIHJldHVybiBzb3J0Qnkob3V0LCAnbGFiZWwnKTsKICAgIH0sCgogICAgaGVhZGVycygpIHsKICAgICAgcmV0dXJuIFsKICAgICAgICBUWVBFLAogICAgICAgIE5BTUVfQ09MLAogICAgICAgIE5BTUVTUEFDRV9DT0wsCiAgICAgICAgQUdFCiAgICAgIF07CiAgICB9LAogIH0sCgogIG1ldGhvZHM6IHsKICAgIGNsb3NlKCkgewogICAgICB0aGlzLiRlbWl0KCdjbG9zZScpOwogICAgfSwKCiAgICBvbkZpbGVTZWxlY3RlZCh2YWx1ZSkgewogICAgICBjb25zdCBjb21wb25lbnQgPSB0aGlzLiRyZWZzLnlhbWxlZGl0b3I7CgogICAgICBpZiAoY29tcG9uZW50KSB7CiAgICAgICAgdGhpcy5lcnJvcnMgPSBudWxsOwogICAgICAgIGNvbXBvbmVudC51cGRhdGVWYWx1ZSh2YWx1ZSk7CiAgICAgIH0KICAgIH0sCgogICAgYXN5bmMgaW1wb3J0WWFtbChidG5DYikgewogICAgICB0cnkgewogICAgICAgIHRoaXMuZXJyb3JzID0gW107CgogICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IHRoaXMuY3VycmVudENsdXN0ZXIuZG9BY3Rpb24oJ2FwcGx5JywgewogICAgICAgICAgeWFtbDogICAgICAgICAgICAgdGhpcy5jdXJyZW50WWFtbCwKICAgICAgICAgIGRlZmF1bHROYW1lc3BhY2U6IHRoaXMuZGVmYXVsdE5hbWVzcGFjZSwKICAgICAgICB9KTsKCiAgICAgICAgYnRuQ2IodHJ1ZSk7CgogICAgICAgIHRoaXMucm93cyA9IHJlczsKICAgICAgICB0aGlzLmRvbmUgPSB0cnVlOwogICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICB0aGlzLmVycm9ycyA9IGV4Y2VwdGlvblRvRXJyb3JzQXJyYXkoZXJyKTsKICAgICAgICB0aGlzLmRvbmUgPSBmYWxzZTsKICAgICAgICBidG5DYihmYWxzZSk7CiAgICAgIH0KICAgIH0sCgogICAgcm93Q2xpY2soZSkgewogICAgICBpZiAoIGUudGFyZ2V0LnRhZ05hbWUgPT09ICdBJyApIHsKICAgICAgICB0aGlzLmNsb3NlKCk7CiAgICAgIH0KICAgIH0sCgogICAgb25SZWFkeVlhbWxFZGl0b3IoYXJnKSB7CiAgICAgIHRoaXMuJGVtaXQoJ29uUmVhZHlZYW1sRWRpdG9yJywgYXJnKTsKICAgIH0KICB9LAp9Owo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Import.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAErG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAErC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;EACrH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC;MACH,CAAC,CAAC;;MAEF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC;MACJ,CAAC;IACH,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEvC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;QAEhB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACtD,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC;;QAEF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAClB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACtC;EACF,CAAC;AACH,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Import.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapGetters } from 'vuex';\nimport { Card } from '@components/Card';\nimport { Banner } from '@components/Banner';\nimport Loading from '@shell/components/Loading';\nimport YamlEditor from '@shell/components/YamlEditor';\nimport FileSelector from '@shell/components/form/FileSelector';\nimport AsyncButton from '@shell/components/AsyncButton';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport SortableTable from '@shell/components/SortableTable';\nimport { sortBy } from '@shell/utils/sort';\nimport { exceptionToErrorsArray } from '@shell/utils/error';\nimport { NAMESPACE } from '@shell/config/types';\nimport { NAME as NAME_COL, TYPE, NAMESPACE as NAMESPACE_COL, AGE } from '@shell/config/table-headers';\n\nexport default {\n  emits: ['close', 'onReadyYamlEditor'],\n\n  components: {\n    AsyncButton,\n    Banner,\n    Card,\n    Loading,\n    YamlEditor,\n    FileSelector,\n    LabeledSelect,\n    SortableTable\n  },\n\n  props: {\n    defaultNamespace: {\n      type:    String,\n      default: 'default'\n    },\n  },\n\n  async fetch() {\n    this.allNamespaces = await this.$store.dispatch('cluster/findAll', { type: NAMESPACE, opt: { url: 'namespaces' } });\n  },\n\n  data() {\n    return {\n      currentYaml:   '',\n      allNamespaces: [],\n      errors:        null,\n      rows:          null,\n      done:          false,\n    };\n  },\n\n  computed: {\n    ...mapGetters(['currentCluster']),\n\n    namespaceOptions() {\n      const out = this.allNamespaces.map((obj) => {\n        return {\n          label: obj.name,\n          value: obj.name,\n        };\n      });\n\n      return sortBy(out, 'label');\n    },\n\n    headers() {\n      return [\n        TYPE,\n        NAME_COL,\n        NAMESPACE_COL,\n        AGE\n      ];\n    },\n  },\n\n  methods: {\n    close() {\n      this.$emit('close');\n    },\n\n    onFileSelected(value) {\n      const component = this.$refs.yamleditor;\n\n      if (component) {\n        this.errors = null;\n        component.updateValue(value);\n      }\n    },\n\n    async importYaml(btnCb) {\n      try {\n        this.errors = [];\n\n        const res = await this.currentCluster.doAction('apply', {\n          yaml:             this.currentYaml,\n          defaultNamespace: this.defaultNamespace,\n        });\n\n        btnCb(true);\n\n        this.rows = res;\n        this.done = true;\n      } catch (err) {\n        this.errors = exceptionToErrorsArray(err);\n        this.done = false;\n        btnCb(false);\n      }\n    },\n\n    rowClick(e) {\n      if ( e.target.tagName === 'A' ) {\n        this.close();\n      }\n    },\n\n    onReadyYamlEditor(arg) {\n      this.$emit('onReadyYamlEditor', arg);\n    }\n  },\n};\n</script>\n\n<template>\n  <Loading v-if=\"$fetchState.pending\" />\n  <Card\n    v-else\n    :show-highlight-border=\"false\"\n    data-testid=\"import-yaml\"\n    :trigger-focus-trap=\"true\"\n  >\n    <template #title>\n      <div style=\"display: block; width: 100%;\">\n        <template v-if=\"done\">\n          <h4 data-testid=\"import-yaml-success\">\n            {{ t('import.success', {count: rows.length}) }}\n          </h4>\n        </template>\n        <template v-else>\n          <h4 v-t=\"'import.title'\" />\n          <div class=\"row\">\n            <div class=\"col span-6\">\n              <FileSelector\n                role=\"button\"\n                :aria-label=\"t('generic.readFromFileArea', { area: t('import.title') })\"\n                class=\"btn role-secondary pull-left\"\n                :label=\"t('generic.readFromFile')\"\n                @selected=\"onFileSelected\"\n              />\n            </div>\n            <div class=\"col span-6\">\n              <LabeledSelect\n                :value=\"defaultNamespace\"\n                :options=\"namespaceOptions\"\n                label-key=\"import.defaultNamespace.label\"\n                mode=\"edit\"\n                @update:value=\"newValue => defaultNamespace = newValue\"\n              />\n            </div>\n          </div>\n        </template>\n      </div>\n    </template>\n    <template #body>\n      <template v-if=\"done\">\n        <div class=\"results\">\n          <SortableTable\n            :rows=\"rows\"\n            :headers=\"headers\"\n            mode=\"view\"\n            key-field=\"_key\"\n            :search=\"false\"\n            :paging=\"true\"\n            :row-actions=\"false\"\n            :table-actions=\"false\"\n            :sub-rows-description=\"false\"\n            @rowClick=\"rowClick\"\n          />\n        </div>\n      </template>\n      <YamlEditor\n        v-else\n        ref=\"yamleditor\"\n        v-model:value=\"currentYaml\"\n        class=\"yaml-editor\"\n        @onReady=\"onReadyYamlEditor\"\n      />\n      <Banner\n        v-for=\"(err, i) in errors\"\n        :key=\"i\"\n        color=\"error\"\n        :label=\"err\"\n      />\n    </template>\n    <template #actions>\n      <div\n        v-if=\"done\"\n        class=\"text-center\"\n        style=\"width: 100%\"\n      >\n        <button\n          :aria-label=\"t('generic.close')\"\n          role=\"button\"\n          type=\"button\"\n          class=\"btn role-primary\"\n          data-testid=\"import-yaml-close\"\n          @click=\"close\"\n        >\n          {{ t('generic.close') }}\n        </button>\n      </div>\n      <div\n        v-else\n        class=\"text-center\"\n        style=\"width: 100%\"\n      >\n        <button\n          :aria-label=\"t('generic.cancel')\"\n          role=\"button\"\n          type=\"button\"\n          class=\"btn role-secondary mr-10\"\n          data-testid=\"import-yaml-cancel\"\n          @click=\"close\"\n        >\n          {{ t('generic.cancel') }}\n        </button>\n        <AsyncButton\n          v-if=\"!done\"\n          mode=\"import\"\n          :disabled=\"!currentYaml.length\"\n          data-testid=\"import-yaml-import-action\"\n          :aria-label=\"t('import.title')\"\n          @click=\"importYaml\"\n        />\n      </div>\n    </template>\n  </Card>\n</template>\n\n<style lang='scss' scoped>\n  $min: 50vh;\n  $max: 50vh;\n\n  .yaml-editor {\n    flex: 1;\n    min-height: $min;\n    max-height: $max;\n\n    :deep() .code-mirror {\n      .CodeMirror {\n        position: initial;\n      }\n\n      .CodeMirror,\n      .CodeMirror-scroll,\n      .CodeMirror-gutters {\n        min-height: $min;\n        max-height: $max;\n      }\n    }\n  }\n</style>\n"]}]}