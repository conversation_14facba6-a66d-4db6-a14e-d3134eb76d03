{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ValueFromResource.vue?vue&type=template&id=a56e893e&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ValueFromResource.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVzb2x2ZUNvbXBvbmVudCBhcyBfcmVzb2x2ZUNvbXBvbmVudCwgY3JlYXRlVk5vZGUgYXMgX2NyZWF0ZVZOb2RlLCBjcmVhdGVFbGVtZW50Vk5vZGUgYXMgX2NyZWF0ZUVsZW1lbnRWTm9kZSwgb3BlbkJsb2NrIGFzIF9vcGVuQmxvY2ssIGNyZWF0ZUVsZW1lbnRCbG9jayBhcyBfY3JlYXRlRWxlbWVudEJsb2NrLCBjcmVhdGVDb21tZW50Vk5vZGUgYXMgX2NyZWF0ZUNvbW1lbnRWTm9kZSwgbm9ybWFsaXplQ2xhc3MgYXMgX25vcm1hbGl6ZUNsYXNzLCBjcmVhdGVUZXh0Vk5vZGUgYXMgX2NyZWF0ZVRleHRWTm9kZSwgRnJhZ21lbnQgYXMgX0ZyYWdtZW50LCB0b0Rpc3BsYXlTdHJpbmcgYXMgX3RvRGlzcGxheVN0cmluZywgd2l0aE1vZGlmaWVycyBhcyBfd2l0aE1vZGlmaWVycyB9IGZyb20gInZ1ZSIKCmNvbnN0IF9ob2lzdGVkXzEgPSB7IGNsYXNzOiAidmFyLXJvdyIgfQpjb25zdCBfaG9pc3RlZF8yID0geyBjbGFzczogInR5cGUiIH0KY29uc3QgX2hvaXN0ZWRfMyA9IHsgY2xhc3M6ICJuYW1lIiB9CmNvbnN0IF9ob2lzdGVkXzQgPSB7CiAga2V5OiAwLAogIGNsYXNzOiAic2luZ2xlLXZhbHVlIgp9CmNvbnN0IF9ob2lzdGVkXzUgPSB7IGtleTogMCB9CmNvbnN0IF9ob2lzdGVkXzYgPSB7CiAga2V5OiAzLAogIGNsYXNzOiAic2luZ2xlLXZhbHVlIgp9CmNvbnN0IF9ob2lzdGVkXzcgPSB7IGNsYXNzOiAicmVtb3ZlIiB9CgpleHBvcnQgZnVuY3Rpb24gcmVuZGVyKF9jdHgsIF9jYWNoZSwgJHByb3BzLCAkc2V0dXAsICRkYXRhLCAkb3B0aW9ucykgewogIGNvbnN0IF9jb21wb25lbnRfTGFiZWxlZFNlbGVjdCA9IF9yZXNvbHZlQ29tcG9uZW50KCJMYWJlbGVkU2VsZWN0IikKICBjb25zdCBfY29tcG9uZW50X0xhYmVsZWRJbnB1dCA9IF9yZXNvbHZlQ29tcG9uZW50KCJMYWJlbGVkSW5wdXQiKQoKICByZXR1cm4gKF9vcGVuQmxvY2soKSwgX2NyZWF0ZUVsZW1lbnRCbG9jaygiZGl2IiwgX2hvaXN0ZWRfMSwgWwogICAgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfMiwgWwogICAgICBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9MYWJlbGVkU2VsZWN0LCB7CiAgICAgICAgdmFsdWU6ICRkYXRhLnR5cGUsCiAgICAgICAgIm9uVXBkYXRlOnZhbHVlIjogWwogICAgICAgICAgX2NhY2hlWzBdIHx8IChfY2FjaGVbMF0gPSAkZXZlbnQgPT4gKCgkZGF0YS50eXBlKSA9ICRldmVudCkpLAogICAgICAgICAgJG9wdGlvbnMudXBkYXRlUm93CiAgICAgICAgXSwKICAgICAgICBtb2RlOiAkcHJvcHMubW9kZSwKICAgICAgICBtdWx0aXBsZTogZmFsc2UsCiAgICAgICAgb3B0aW9uczogJGRhdGEudHlwZU9wdHMsCiAgICAgICAgIm9wdGlvbi1sYWJlbCI6ICJsYWJlbCIsCiAgICAgICAgc2VhcmNoYWJsZTogZmFsc2UsCiAgICAgICAgcmVkdWNlOiBlPT5lLnZhbHVlLAogICAgICAgIGxhYmVsOiBfY3R4LnQoJ3dvcmtsb2FkLmNvbnRhaW5lci5jb21tYW5kLmZyb21SZXNvdXJjZS50eXBlJykKICAgICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgWyJ2YWx1ZSIsICJtb2RlIiwgIm9wdGlvbnMiLCAicmVkdWNlIiwgImxhYmVsIiwgIm9uVXBkYXRlOnZhbHVlIl0pCiAgICBdKSwKICAgIF9jYWNoZVsxMV0gfHwgKF9jYWNoZVsxMV0gPSBfY3JlYXRlVGV4dFZOb2RlKCkpLAogICAgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfMywgWwogICAgICBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9MYWJlbGVkSW5wdXQsIHsKICAgICAgICB2YWx1ZTogJGRhdGEubmFtZSwKICAgICAgICAib25VcGRhdGU6dmFsdWUiOiBbCiAgICAgICAgICBfY2FjaGVbMV0gfHwgKF9jYWNoZVsxXSA9ICRldmVudCA9PiAoKCRkYXRhLm5hbWUpID0gJGV2ZW50KSksCiAgICAgICAgICAkb3B0aW9ucy51cGRhdGVSb3cKICAgICAgICBdLAogICAgICAgIGxhYmVsOiAkb3B0aW9ucy5uYW1lTGFiZWwsCiAgICAgICAgcGxhY2Vob2xkZXI6IF9jdHgudCgnd29ya2xvYWQuY29udGFpbmVyLmNvbW1hbmQuZnJvbVJlc291cmNlLm5hbWUucGxhY2Vob2xkZXInKSwKICAgICAgICBtb2RlOiAkcHJvcHMubW9kZQogICAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBbInZhbHVlIiwgImxhYmVsIiwgInBsYWNlaG9sZGVyIiwgIm1vZGUiLCAib25VcGRhdGU6dmFsdWUiXSkKICAgIF0pLAogICAgX2NhY2hlWzEyXSB8fCAoX2NhY2hlWzEyXSA9IF9jcmVhdGVUZXh0Vk5vZGUoKSksCiAgICAoJGRhdGEudHlwZT09PSdzaW1wbGUnKQogICAgICA/IChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soImRpdiIsIF9ob2lzdGVkXzQsIFsKICAgICAgICAgIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X0xhYmVsZWRJbnB1dCwgewogICAgICAgICAgICB2YWx1ZTogJGRhdGEudmFsU3RyLAogICAgICAgICAgICAib25VcGRhdGU6dmFsdWUiOiBbCiAgICAgICAgICAgICAgX2NhY2hlWzJdIHx8IChfY2FjaGVbMl0gPSAkZXZlbnQgPT4gKCgkZGF0YS52YWxTdHIpID0gJGV2ZW50KSksCiAgICAgICAgICAgICAgJG9wdGlvbnMudXBkYXRlUm93CiAgICAgICAgICAgIF0sCiAgICAgICAgICAgIGxhYmVsOiBfY3R4LnQoJ3dvcmtsb2FkLmNvbnRhaW5lci5jb21tYW5kLmZyb21SZXNvdXJjZS52YWx1ZS5sYWJlbCcpLAogICAgICAgICAgICBwbGFjZWhvbGRlcjogX2N0eC50KCd3b3JrbG9hZC5jb250YWluZXIuY29tbWFuZC5mcm9tUmVzb3VyY2UudmFsdWUucGxhY2Vob2xkZXInKSwKICAgICAgICAgICAgbW9kZTogJHByb3BzLm1vZGUKICAgICAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIFsidmFsdWUiLCAibGFiZWwiLCAicGxhY2Vob2xkZXIiLCAibW9kZSIsICJvblVwZGF0ZTp2YWx1ZSJdKQogICAgICAgIF0pKQogICAgICA6ICgkb3B0aW9ucy5uZWVkc1NvdXJjZSkKICAgICAgICA/IChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soX0ZyYWdtZW50LCB7IGtleTogMSB9LCBbCiAgICAgICAgICAgIF9jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIHsKICAgICAgICAgICAgICBjbGFzczogX25vcm1hbGl6ZUNsYXNzKHsnc2luZ2xlLXZhbHVlJzogJGRhdGEudHlwZSA9PT0gJ2NvbmZpZ01hcFJlZicgfHwgJGRhdGEudHlwZSA9PT0gJ3NlY3JldFJlZid9KQogICAgICAgICAgICB9LCBbCiAgICAgICAgICAgICAgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfTGFiZWxlZFNlbGVjdCwgewogICAgICAgICAgICAgICAgdmFsdWU6ICRkYXRhLnJlZmVyZW5jZWQsCiAgICAgICAgICAgICAgICAib25VcGRhdGU6dmFsdWUiOiBfY2FjaGVbM10gfHwgKF9jYWNoZVszXSA9ICRldmVudCA9PiAoKCRkYXRhLnJlZmVyZW5jZWQpID0gJGV2ZW50KSksCiAgICAgICAgICAgICAgICBvcHRpb25zOiAkb3B0aW9ucy5zb3VyY2VPcHRpb25zLAogICAgICAgICAgICAgICAgbXVsdGlwbGU6IGZhbHNlLAogICAgICAgICAgICAgICAgImdldC1vcHRpb24tbGFiZWwiOiBvcHQ9PiRvcHRpb25zLmdldChvcHQsICdtZXRhZGF0YS5uYW1lJykgfHwgb3B0LAogICAgICAgICAgICAgICAgImdldC1vcHRpb24ta2V5Ijogb3B0PT5vcHQuaWR8fCBvcHQsCiAgICAgICAgICAgICAgICBtb2RlOiAkcHJvcHMubW9kZSwKICAgICAgICAgICAgICAgIGxhYmVsOiAkb3B0aW9ucy5zb3VyY2VMYWJlbCwKICAgICAgICAgICAgICAgIGxvYWRpbmc6ICRwcm9wcy5sb2FkaW5nCiAgICAgICAgICAgICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgWyJ2YWx1ZSIsICJvcHRpb25zIiwgImdldC1vcHRpb24tbGFiZWwiLCAiZ2V0LW9wdGlvbi1rZXkiLCAibW9kZSIsICJsYWJlbCIsICJsb2FkaW5nIl0pCiAgICAgICAgICAgIF0sIDIgLyogQ0xBU1MgKi8pLAogICAgICAgICAgICBfY2FjaGVbOV0gfHwgKF9jYWNoZVs5XSA9IF9jcmVhdGVUZXh0Vk5vZGUoKSksCiAgICAgICAgICAgICgkZGF0YS50eXBlIT09J3NlY3JldFJlZicgJiYgJGRhdGEudHlwZSE9PSAnY29uZmlnTWFwUmVmJykKICAgICAgICAgICAgICA/IChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soImRpdiIsIF9ob2lzdGVkXzUsIFsKICAgICAgICAgICAgICAgICAgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfTGFiZWxlZFNlbGVjdCwgewogICAgICAgICAgICAgICAgICAgIHZhbHVlOiAkZGF0YS5rZXksCiAgICAgICAgICAgICAgICAgICAgIm9uVXBkYXRlOnZhbHVlIjogWwogICAgICAgICAgICAgICAgICAgICAgX2NhY2hlWzRdIHx8IChfY2FjaGVbNF0gPSAkZXZlbnQgPT4gKCgkZGF0YS5rZXkpID0gJGV2ZW50KSksCiAgICAgICAgICAgICAgICAgICAgICAkb3B0aW9ucy51cGRhdGVSb3cKICAgICAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgICAgIG11bHRpcGxlOiBmYWxzZSwKICAgICAgICAgICAgICAgICAgICBvcHRpb25zOiAkZGF0YS5rZXlzLAogICAgICAgICAgICAgICAgICAgIG1vZGU6ICRwcm9wcy5tb2RlLAogICAgICAgICAgICAgICAgICAgICJvcHRpb24tbGFiZWwiOiAibGFiZWwiLAogICAgICAgICAgICAgICAgICAgIGxhYmVsOiBfY3R4LnQoJ3dvcmtsb2FkLmNvbnRhaW5lci5jb21tYW5kLmZyb21SZXNvdXJjZS5rZXkubGFiZWwnKSwKICAgICAgICAgICAgICAgICAgICBsb2FkaW5nOiAkcHJvcHMubG9hZGluZwogICAgICAgICAgICAgICAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBbInZhbHVlIiwgIm9wdGlvbnMiLCAibW9kZSIsICJsYWJlbCIsICJsb2FkaW5nIiwgIm9uVXBkYXRlOnZhbHVlIl0pCiAgICAgICAgICAgICAgICBdKSkKICAgICAgICAgICAgICA6IF9jcmVhdGVDb21tZW50Vk5vZGUoInYtaWYiLCB0cnVlKQogICAgICAgICAgXSwgNjQgLyogU1RBQkxFX0ZSQUdNRU5UICovKSkKICAgICAgICA6ICgkZGF0YS50eXBlPT09J3Jlc291cmNlRmllbGRSZWYnKQogICAgICAgICAgPyAoX29wZW5CbG9jaygpLCBfY3JlYXRlRWxlbWVudEJsb2NrKF9GcmFnbWVudCwgeyBrZXk6IDIgfSwgWwogICAgICAgICAgICAgIF9jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIG51bGwsIFsKICAgICAgICAgICAgICAgIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X0xhYmVsZWRJbnB1dCwgewogICAgICAgICAgICAgICAgICB2YWx1ZTogJGRhdGEucmVmTmFtZSwKICAgICAgICAgICAgICAgICAgIm9uVXBkYXRlOnZhbHVlIjogWwogICAgICAgICAgICAgICAgICAgIF9jYWNoZVs1XSB8fCAoX2NhY2hlWzVdID0gJGV2ZW50ID0+ICgoJGRhdGEucmVmTmFtZSkgPSAkZXZlbnQpKSwKICAgICAgICAgICAgICAgICAgICAkb3B0aW9ucy51cGRhdGVSb3cKICAgICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgICAgbGFiZWw6IF9jdHgudCgnd29ya2xvYWQuY29udGFpbmVyLmNvbW1hbmQuZnJvbVJlc291cmNlLmNvbnRhaW5lck5hbWUnKSwKICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI6IF9jdHgudCgnd29ya2xvYWQuY29udGFpbmVyLmNvbW1hbmQuZnJvbVJlc291cmNlLnNvdXJjZS5wbGFjZWhvbGRlcicpLAogICAgICAgICAgICAgICAgICBtb2RlOiAkcHJvcHMubW9kZQogICAgICAgICAgICAgICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgWyJ2YWx1ZSIsICJsYWJlbCIsICJwbGFjZWhvbGRlciIsICJtb2RlIiwgIm9uVXBkYXRlOnZhbHVlIl0pCiAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgX2NhY2hlWzEwXSB8fCAoX2NhY2hlWzEwXSA9IF9jcmVhdGVUZXh0Vk5vZGUoKSksCiAgICAgICAgICAgICAgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgbnVsbCwgWwogICAgICAgICAgICAgICAgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfTGFiZWxlZFNlbGVjdCwgewogICAgICAgICAgICAgICAgICB2YWx1ZTogJGRhdGEua2V5LAogICAgICAgICAgICAgICAgICAib25VcGRhdGU6dmFsdWUiOiBbCiAgICAgICAgICAgICAgICAgICAgX2NhY2hlWzZdIHx8IChfY2FjaGVbNl0gPSAkZXZlbnQgPT4gKCgkZGF0YS5rZXkpID0gJGV2ZW50KSksCiAgICAgICAgICAgICAgICAgICAgJG9wdGlvbnMudXBkYXRlUm93CiAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgIGxhYmVsOiBfY3R4LnQoJ3dvcmtsb2FkLmNvbnRhaW5lci5jb21tYW5kLmZyb21SZXNvdXJjZS5rZXkubGFiZWwnKSwKICAgICAgICAgICAgICAgICAgbXVsdGlwbGU6IGZhbHNlLAogICAgICAgICAgICAgICAgICBvcHRpb25zOiAkZGF0YS5yZXNvdXJjZUtleU9wdHMsCiAgICAgICAgICAgICAgICAgIG1vZGU6ICRwcm9wcy5tb2RlLAogICAgICAgICAgICAgICAgICBzZWFyY2hhYmxlOiBmYWxzZSwKICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI6IF9jdHgudCgnd29ya2xvYWQuY29udGFpbmVyLmNvbW1hbmQuZnJvbVJlc291cmNlLmtleS5wbGFjZWhvbGRlcicsIG51bGwsIHRydWUpCiAgICAgICAgICAgICAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBbInZhbHVlIiwgImxhYmVsIiwgIm9wdGlvbnMiLCAibW9kZSIsICJwbGFjZWhvbGRlciIsICJvblVwZGF0ZTp2YWx1ZSJdKQogICAgICAgICAgICAgIF0pCiAgICAgICAgICAgIF0sIDY0IC8qIFNUQUJMRV9GUkFHTUVOVCAqLykpCiAgICAgICAgICA6IChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soImRpdiIsIF9ob2lzdGVkXzYsIFsKICAgICAgICAgICAgICBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9MYWJlbGVkSW5wdXQsIHsKICAgICAgICAgICAgICAgIHZhbHVlOiAkZGF0YS5maWVsZFBhdGgsCiAgICAgICAgICAgICAgICAib25VcGRhdGU6dmFsdWUiOiBbCiAgICAgICAgICAgICAgICAgIF9jYWNoZVs3XSB8fCAoX2NhY2hlWzddID0gJGV2ZW50ID0+ICgoJGRhdGEuZmllbGRQYXRoKSA9ICRldmVudCkpLAogICAgICAgICAgICAgICAgICAkb3B0aW9ucy51cGRhdGVSb3cKICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcjogX2N0eC50KCd3b3JrbG9hZC5jb250YWluZXIuY29tbWFuZC5mcm9tUmVzb3VyY2Uua2V5LnBsYWNlaG9sZGVyJywgbnVsbCwgdHJ1ZSksCiAgICAgICAgICAgICAgICBsYWJlbDogX2N0eC50KCd3b3JrbG9hZC5jb250YWluZXIuY29tbWFuZC5mcm9tUmVzb3VyY2Uua2V5LmxhYmVsJyksCiAgICAgICAgICAgICAgICBtb2RlOiAkcHJvcHMubW9kZQogICAgICAgICAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIFsidmFsdWUiLCAicGxhY2Vob2xkZXIiLCAibGFiZWwiLCAibW9kZSIsICJvblVwZGF0ZTp2YWx1ZSJdKQogICAgICAgICAgICBdKSksCiAgICBfY2FjaGVbMTNdIHx8IChfY2FjaGVbMTNdID0gX2NyZWF0ZVRleHRWTm9kZSgpKSwKICAgIF9jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIF9ob2lzdGVkXzcsIFsKICAgICAgKCEkb3B0aW9ucy5pc1ZpZXcpCiAgICAgICAgPyAoX29wZW5CbG9jaygpLCBfY3JlYXRlRWxlbWVudEJsb2NrKCJidXR0b24iLCB7CiAgICAgICAgICAgIGtleTogMCwKICAgICAgICAgICAgdHlwZTogImJ1dHRvbiIsCiAgICAgICAgICAgIGNsYXNzOiAiYnRuIHJvbGUtbGluayIsCiAgICAgICAgICAgIG9uQ2xpY2s6IF9jYWNoZVs4XSB8fCAoX2NhY2hlWzhdID0gX3dpdGhNb2RpZmllcnMoJGV2ZW50ID0+IChfY3R4LiRlbWl0KCdyZW1vdmUnKSksIFsic3RvcCJdKSkKICAgICAgICAgIH0sIF90b0Rpc3BsYXlTdHJpbmcoX2N0eC50KCdnZW5lcmljLnJlbW92ZScpKSwgMSAvKiBURVhUICovKSkKICAgICAgICA6IF9jcmVhdGVDb21tZW50Vk5vZGUoInYtaWYiLCB0cnVlKQogICAgXSkKICBdKSkKfQ=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ValueFromResource.vue"], "names": ["t", "$emit"], "mappings": ";;qBAuQO,KAAK,EAAC,SAAS;qBACb,KAAK,EAAC,MAAM;qBAcZ,KAAK,EAAC,MAAM;;;EAYf,KAAK,EAAC,cAAc;;;;;EA+Df,KAAK,EAAC,cAAc;;qBAUtB,KAAK,EAAC,QAAQ;;;;;;wBApGrB,oBA8GM,OA9GN,UA8GM;IA7GJ,oBAYM,OAZN,UAYM;MAXJ,aAUE;QATQ,KAAK,EAAE,UAAI;;gDAAJ,UAAI;UAQJ,kBAAS;;QAPvB,IAAI,EAAE,WAAI;QACV,QAAQ,EAAE,KAAK;QACf,OAAO,EAAE,cAAQ;QAClB,cAAY,EAAC,OAAO;QACnB,UAAU,EAAE,KAAK;QACjB,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK;QAClB,KAAK,EAAEA,MAAC;;;;IAKb,oBAQM,OARN,UAQM;MAPJ,aAME;QALQ,KAAK,EAAE,UAAI;;gDAAJ,UAAI;UAIJ,kBAAS;;QAHvB,KAAK,EAAE,kBAAS;QAChB,WAAW,EAAEA,MAAC;QACd,IAAI,EAAE,WAAI;;;;KAMP,UAAI;uBADZ,oBAWM,OAXN,UAWM;UAPJ,aAME;YALQ,KAAK,EAAE,YAAM;;oDAAN,YAAM;cAIN,kBAAS;;YAHvB,KAAK,EAAEA,MAAC;YACR,WAAW,EAAEA,MAAC;YACd,IAAI,EAAE,WAAI;;;SAKM,oBAAW;yBAAhC,oBAyBW;YAxBT,oBAWM;cAXA,KAAK,mCAAmB,UAAI,uBAAuB,UAAI;;cAC3D,aASE;gBARQ,KAAK,EAAE,gBAAU;wEAAV,gBAAU;gBACxB,OAAO,EAAE,sBAAa;gBACtB,QAAQ,EAAE,KAAK;gBACf,kBAAgB,EAAE,GAAG,EAAE,YAAG,CAAC,GAAG,sBAAsB,GAAG;gBACvD,gBAAc,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,GAAG;gBACjC,IAAI,EAAE,WAAI;gBACV,KAAK,EAAE,oBAAW;gBAClB,OAAO,EAAE,cAAO;;;;aAGV,UAAI,kBAAkB,UAAI;+BAArC,oBAWM;kBAVJ,aASE;oBARQ,KAAK,EAAE,SAAG;;4DAAH,SAAG;sBAOH,kBAAS;;oBANvB,QAAQ,EAAE,KAAK;oBACf,OAAO,EAAE,UAAI;oBACb,IAAI,EAAE,WAAI;oBACX,cAAY,EAAC,OAAO;oBACnB,KAAK,EAAEA,MAAC;oBACR,OAAO,EAAE,cAAO;;;;;WAMF,UAAI;2BAAzB,oBAsBW;cArBT,oBAQM;gBAPJ,aAME;kBALQ,KAAK,EAAE,aAAO;;0DAAP,aAAO;oBAIP,kBAAS;;kBAHvB,KAAK,EAAEA,MAAC;kBACR,WAAW,EAAEA,MAAC;kBACd,IAAI,EAAE,WAAI;;;;cAIf,oBAWM;gBAVJ,aASE;kBARQ,KAAK,EAAE,SAAG;;0DAAH,SAAG;oBAOH,kBAAS;;kBANvB,KAAK,EAAEA,MAAC;kBACR,QAAQ,EAAE,KAAK;kBACf,OAAO,EAAE,qBAAe;kBACxB,IAAI,EAAE,WAAI;kBACV,UAAU,EAAE,KAAK;kBACjB,WAAW,EAAEA,MAAC;;;;2BAOnB,oBAQM,OARN,UAQM;cAPJ,aAME;gBALQ,KAAK,EAAE,eAAS;;wDAAT,eAAS;kBAIT,kBAAS;;gBAHvB,WAAW,EAAEA,MAAC;gBACd,KAAK,EAAEA,MAAC;gBACR,IAAI,EAAE,WAAI;;;;IAKjB,oBASM,OATN,UASM;QAPK,eAAM;yBADf,oBAOS;;YALP,IAAI,EAAC,QAAQ;YACb,KAAK,EAAC,eAAe;YACpB,OAAK,sDAAOC,UAAK;8BAEfD,MAAC", "sourcesContent": ["<script>\nimport { CONFIG_MAP, SECRET, NAMESPACE } from '@shell/config/types';\nimport { get } from '@shell/utils/object';\nimport { _VIEW } from '@shell/config/query-params';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport { LabeledInput } from '@components/Form/LabeledInput';\n\nexport default {\n  emits: ['update:value', 'remove'],\n\n  components: {\n    LabeledSelect,\n    LabeledInput\n  },\n\n  props: {\n    mode: {\n      type:    String,\n      default: 'create'\n    },\n    value: {\n      type:    Object,\n      default: () => {\n        return { valueFrom: {} };\n      }\n    },\n    allConfigMaps: {\n      type:    Array,\n      default: () => []\n    },\n    allSecrets: {\n      type:    Array,\n      default: () => []\n    },\n    // filter resource options by namespace(s) selected in top nav\n    namespaced: {\n      type:    Boolean,\n      default: true\n    },\n    loading: {\n      default: false,\n      type:    Boolean\n    },\n  },\n\n  data() {\n    const typeOpts = [\n      { value: 'simple', label: 'Key/Value Pair' },\n      { value: 'resourceFieldRef', label: 'Resource' },\n      { value: 'configMapKeyRef', label: 'ConfigMap Key' },\n      { value: 'secretKeyRef', label: 'Secret key' },\n      { value: 'fieldRef', label: 'Pod Field' },\n      { value: 'secretRef', label: 'Secret' },\n      { value: 'configMapRef', label: 'ConfigMap' },\n    ];\n\n    const resourceKeyOpts = ['limits.cpu', 'limits.ephemeral-storage', 'limits.memory', 'requests.cpu', 'requests.ephemeral-storage', 'requests.memory'];\n    let type;\n\n    if (this.value.secretRef) {\n      type = 'secretRef';\n    } else if (this.value.configMapRef) {\n      type = 'configMapRef';\n    } else if (this.value.value) {\n      type = 'simple';\n    } else if (this.value.valueFrom) {\n      type = Object.keys((this.value.valueFrom))[0] || 'simple';\n    }\n\n    let refName;\n    let name;\n    let fieldPath;\n    let referenced;\n    let key;\n    let valStr;\n    const keys = [];\n\n    switch (type) {\n    case 'resourceFieldRef':\n      name = this.value.name;\n      refName = this.value.valueFrom[type].containerName;\n      key = this.value.valueFrom[type].resource || '';\n      break;\n    case 'configMapKeyRef':\n      name = this.value.name;\n      key = this.value.valueFrom[type].key || '';\n      refName = this.value.valueFrom[type].name;\n      referenced = this.allConfigMaps.filter((resource) => {\n        return resource.metadata.name === refName;\n      })[0];\n      if (referenced && referenced.data) {\n        keys.push(...Object.keys(referenced.data));\n      }\n      break;\n    case 'secretRef':\n    case 'configMapRef':\n      name = this.value.prefix;\n      refName = this.value[type].name;\n      break;\n    case 'secretKeyRef':\n      name = this.value.name;\n      key = this.value.valueFrom[type].key || '';\n      refName = this.value.valueFrom[type].name;\n      referenced = this.allSecrets.filter((resource) => {\n        return resource.metadata.name === refName;\n      })[0];\n      if (referenced && referenced.data) {\n        keys.push(...Object.keys(referenced.data));\n      }\n      break;\n    case 'fieldRef':\n      fieldPath = get(this.value.valueFrom, `${ type }.fieldPath`) || '';\n      name = this.value.name;\n      break;\n    default:\n      name = this.value.name;\n      valStr = this.value.value;\n      break;\n    }\n\n    return {\n      typeOpts, type, refName, referenced: refName, secrets: this.allSecrets, keys, key, fieldPath, name, resourceKeyOpts, valStr\n    };\n  },\n  computed: {\n    isView() {\n      return this.mode === _VIEW;\n    },\n\n    namespaces() {\n      if (this.namespaced) {\n        const map = this.$store.getters.namespaces();\n\n        return Object.keys(map).filter((key) => map[key]);\n      } else {\n        const inStore = this.$store.getters['currentStore'](NAMESPACE);\n\n        return this.$store.getters[`${ inStore }/all`](NAMESPACE);\n      }\n    },\n\n    sourceOptions() {\n      if (this.type === 'configMapKeyRef' || this.type === 'configMapRef') {\n        return this.allConfigMaps.filter((map) => this.namespaces.includes(map?.metadata?.namespace));\n      } else if (this.type === 'secretRef' || this.type === 'secretKeyRef') {\n        return this.allSecrets.filter((secret) => this.namespaces.includes(secret?.metadata?.namespace));\n      } else {\n        return [];\n      }\n    },\n\n    needsSource() {\n      return this.type !== 'simple' && this.type !== 'resourceFieldRef' && this.type !== 'fieldRef' && !!this.type;\n    },\n\n    sourceLabel() {\n      let out;\n      const { type } = this;\n\n      if (!type) {\n        return;\n      }\n\n      switch (type) {\n      case 'secretKeyRef':\n      case 'secretRef':\n        out = 'workload.container.command.fromResource.secret';\n        break;\n      case 'configMapKeyRef':\n      case 'configMapRef':\n        out = 'workload.container.command.fromResource.configMap';\n        break;\n      default:\n        out = 'workload.container.command.fromResource.source.label';\n      }\n\n      return this.t(out);\n    },\n\n    nameLabel() {\n      if (this.type === 'configMapRef' || this.type === 'secretRef') {\n        return this.t('workload.container.command.fromResource.prefix');\n      } else {\n        return this.t('workload.container.command.fromResource.name.label');\n      }\n    },\n\n    extraColumn() {\n      return ['resourceFieldRef', 'configMapKeyRef', 'secretKeyRef'].includes(this.type);\n    },\n  },\n\n  watch: {\n    type() {\n      this.referenced = null;\n      this.key = '';\n      this.refName = '';\n      this.keys = [];\n      this.key = '';\n      this.valStr = '';\n      this.fieldPath = '';\n    },\n\n    referenced(neu, old) {\n      if (neu) {\n        if ((neu.type === SECRET || neu.type === CONFIG_MAP) && neu.data) {\n          this.keys = Object.keys(neu.data);\n        }\n        this.refName = neu?.metadata?.name;\n      }\n      this.updateRow();\n    },\n  },\n\n  methods: {\n    updateRow() {\n      if (!this.name?.length && !this.refName?.length) {\n        if (this.type !== 'fieldRef') {\n          this.$emit('update:value', null);\n\n          return;\n        }\n      }\n      let out = { name: this.name || this.refName };\n\n      switch (this.type) {\n      case 'configMapKeyRef':\n      case 'secretKeyRef':\n        out.valueFrom = {\n          [this.type]: {\n            key: this.key, name: this.refName, optional: false\n          }\n        };\n        break;\n      case 'resourceFieldRef':\n        out.valueFrom = {\n          [this.type]: {\n            containerName: this.refName, divisor: 1, resource: this.key\n          }\n        };\n        break;\n      case 'fieldRef':\n        if (!this.fieldPath || !this.fieldPath.length) {\n          out = null; break;\n        }\n        out.valueFrom = { [this.type]: { apiVersion: 'v1', fieldPath: this.fieldPath } };\n        break;\n      case 'simple':\n        out.value = this.valStr;\n        break;\n      default:\n        delete out.name;\n        out.prefix = this.name;\n        out[this.type] = { name: this.refName, optional: false };\n      }\n      this.$emit('update:value', out);\n    },\n    get\n  }\n};\n</script>\n\n<template>\n  <div class=\"var-row\">\n    <div class=\"type\">\n      <LabeledSelect\n        v-model:value=\"type\"\n        :mode=\"mode\"\n        :multiple=\"false\"\n        :options=\"typeOpts\"\n        option-label=\"label\"\n        :searchable=\"false\"\n        :reduce=\"e=>e.value\"\n        :label=\"t('workload.container.command.fromResource.type')\"\n        @update:value=\"updateRow\"\n      />\n    </div>\n\n    <div class=\"name\">\n      <LabeledInput\n        v-model:value=\"name\"\n        :label=\"nameLabel\"\n        :placeholder=\"t('workload.container.command.fromResource.name.placeholder')\"\n        :mode=\"mode\"\n        @update:value=\"updateRow\"\n      />\n    </div>\n\n    <div\n      v-if=\"type==='simple'\"\n      class=\"single-value\"\n    >\n      <LabeledInput\n        v-model:value=\"valStr\"\n        :label=\"t('workload.container.command.fromResource.value.label')\"\n        :placeholder=\"t('workload.container.command.fromResource.value.placeholder')\"\n        :mode=\"mode\"\n        @update:value=\"updateRow\"\n      />\n    </div>\n\n    <template v-else-if=\"needsSource\">\n      <div :class=\"{'single-value': type === 'configMapRef' || type === 'secretRef'}\">\n        <LabeledSelect\n          v-model:value=\"referenced\"\n          :options=\"sourceOptions\"\n          :multiple=\"false\"\n          :get-option-label=\"opt=>get(opt, 'metadata.name') || opt\"\n          :get-option-key=\"opt=>opt.id|| opt\"\n          :mode=\"mode\"\n          :label=\"sourceLabel\"\n          :loading=\"loading\"\n        />\n      </div>\n      <div v-if=\"type!=='secretRef' && type!== 'configMapRef'\">\n        <LabeledSelect\n          v-model:value=\"key\"\n          :multiple=\"false\"\n          :options=\"keys\"\n          :mode=\"mode\"\n          option-label=\"label\"\n          :label=\"t('workload.container.command.fromResource.key.label')\"\n          :loading=\"loading\"\n          @update:value=\"updateRow\"\n        />\n      </div>\n    </template>\n\n    <template v-else-if=\"type==='resourceFieldRef'\">\n      <div>\n        <LabeledInput\n          v-model:value=\"refName\"\n          :label=\"t('workload.container.command.fromResource.containerName')\"\n          :placeholder=\"t('workload.container.command.fromResource.source.placeholder')\"\n          :mode=\"mode\"\n          @update:value=\"updateRow\"\n        />\n      </div>\n      <div>\n        <LabeledSelect\n          v-model:value=\"key\"\n          :label=\"t('workload.container.command.fromResource.key.label')\"\n          :multiple=\"false\"\n          :options=\"resourceKeyOpts\"\n          :mode=\"mode\"\n          :searchable=\"false\"\n          :placeholder=\"t('workload.container.command.fromResource.key.placeholder', null, true)\"\n          @update:value=\"updateRow\"\n        />\n      </div>\n    </template>\n\n    <template v-else>\n      <div class=\"single-value\">\n        <LabeledInput\n          v-model:value=\"fieldPath\"\n          :placeholder=\"t('workload.container.command.fromResource.key.placeholder', null, true)\"\n          :label=\"t('workload.container.command.fromResource.key.label')\"\n          :mode=\"mode\"\n          @update:value=\"updateRow\"\n        />\n      </div>\n    </template>\n    <div class=\"remove\">\n      <button\n        v-if=\"!isView\"\n        type=\"button\"\n        class=\"btn role-link\"\n        @click.stop=\"$emit('remove')\"\n      >\n        {{ t('generic.remove') }}\n      </button>\n    </div>\n  </div>\n</template>\n\n<style lang='scss' scoped>\n.var-row{\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr 1fr 100px;\n  grid-column-gap: 20px;\n  margin-bottom: 10px;\n  align-items: center;\n\n  .single-value {\n    grid-column: span 2;\n  }\n\n  .remove BUTTON {\n    padding: 0px;\n  }\n}\n\n</style>\n"]}]}