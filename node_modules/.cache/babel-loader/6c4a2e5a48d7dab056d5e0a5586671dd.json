{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Conditions.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Conditions.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Conditions.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX;IACF;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL;UACE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC;MACH,CAAC;IACH,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACzD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEhC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;EACH;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Conditions.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport SortableTable from '@shell/components/SortableTable';\n\nexport default {\n  components: { SortableTable },\n  props:      {\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  computed: {\n    headers() {\n      return [\n        {\n          name:        'condition',\n          labelKey:    'tableHeaders.condition',\n          value:       'condition',\n          width:       150,\n          sort:        'condition',\n          dashIfEmpty: true,\n        },\n        {\n          name:        'status',\n          labelKey:    'tableHeaders.status',\n          value:       'status',\n          width:       75,\n          sort:        'status',\n          dashIfEmpty: true,\n        },\n        {\n          name:          'time',\n          labelKey:      'tableHeaders.updated',\n          value:         'time',\n          sort:          'time',\n          formatter:     'LiveDate',\n          formatterOpts: { addSuffix: true },\n          width:         125,\n          dashIfEmpty:   true,\n        },\n        {\n          name:        'message',\n          labelKey:    'tableHeaders.message',\n          value:       'message',\n          sort:        ['message'],\n          dashIfEmpty: true,\n        },\n      ];\n    },\n\n    rows() {\n      return (this.value.status?.conditions || []).map((cond) => {\n        let message = cond.message || '';\n\n        if ( cond.reason ) {\n          message = `[${ cond.reason }] ${ message }`.trim();\n        }\n\n        return {\n          condition: cond.type || 'Unknown',\n          status:    cond.status || 'Unknown',\n          error:     cond.error,\n          time:      cond.lastProbeTime || cond.lastUpdateTime || cond.lastTransitionTime,\n          message,\n        };\n      });\n    },\n  }\n};\n</script>\n\n<template>\n  <SortableTable\n    :headers=\"headers\"\n    :rows=\"rows\"\n    key-field=\"condition\"\n    default-sort-by=\"condition\"\n    :table-actions=\"false\"\n    :row-actions=\"false\"\n    :search=\"false\"\n  >\n    <template #cell:condition=\"{row}\">\n      <span :class=\"{'text-error': row.error}\">{{ row.condition }}</span>\n    </template>\n\n    <template #cell:status=\"{row}\">\n      <span :class=\"{'text-error': row.error}\">{{ row.status }}</span>\n    </template>\n  </SortableTable>\n</template>\n"]}]}