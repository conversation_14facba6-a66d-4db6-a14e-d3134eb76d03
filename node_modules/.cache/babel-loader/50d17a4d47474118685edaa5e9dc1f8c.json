{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/RuleSelector.vue?vue&type=template&id=c5ef2e9a&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/RuleSelector.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/RuleSelector.vue"], "names": ["$emit"], "mappings": ";;qBAwIa,KAAK,EAAC,KAAK;qBAMX,KAAK,EAAC,UAAU;qBAQhB,KAAK,EAAC,OAAO;;;;;;;wBA1CxB,oBAoDM;IAnDJ,KAAK,mBAAC,eAAe,IACX,WAAI;;IAEd,aA+CY;MA9CT,KAAK,EAAE,YAAK;MACZ,MAAM,EAAE,KAAK;MACb,aAAW,EAAE,IAAI;MACjB,WAAS,EAAE,eAAQ;MACnB,mBAAiB,EAAE,qBAAe;MAClC,IAAI,EAAE,WAAI;MACV,gBAAY,uCAAEA,UAAK,UAAU,MAAM;;MAEnB,gBAAc,WAC7B,CAWM;QAXN,oBAWM,SAXD,KAAK,EAAC,KAAK;UACd,oBAEM,SAFD,KAAK,EAAC,KAAK,IAAC,+BAEjB;;UACA,oBAEM,SAFD,KAAK,EAAC,UAAU,IAAC,oCAEtB;;UACA,oBAEM,SAFD,KAAK,EAAC,OAAO,IAAC,iCAEnB;;UACA,oBAAO;;;MAGM,OAAO,WACtB,CAKM,AANkB,KAAK;QAC7B,oBAKM,OALN,UAKM;UAJJ,aAGE;YAFQ,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG;0CAAnB,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG;YACjC,IAAI,EAAE,WAAI;;;;QAGf,oBAOM,OAPN,UAOM;UANJ,aAKE;YAJC,IAAI,EAAE,WAAI;YACV,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ;YAC/B,OAAO,EAAE,qBAAe;YACxB,gBAAY,aAAE,wBAAe,CAAC,KAAK,EAAE,MAAM;;;;QAGhD,oBAOM,OAPN,UAOM;UANJ,aAKE;YAJC,QAAQ,EAAE,wBAAe,CAAC,KAAK;YAC/B,KAAK,EAAE,iBAAQ,CAAC,KAAK;YACrB,IAAI,EAAE,WAAI;YACV,gBAAY,aAAE,qBAAY,CAAC,KAAK,EAAE,MAAM", "sourcesContent": ["<script>\nimport { _VIEW } from '@shell/config/query-params';\nimport ArrayList from '@shell/components/form/ArrayList';\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport Select from '@shell/components/form/Select';\n\nconst OPERATOR_VALUES = {\n  IS_SET:      'Exists',\n  IS_NOT_SET:  'DoesNotExist',\n  IN_LIST:     'In',\n  NOT_IN_LIST: 'NotIn'\n};\n\nexport default {\n  emits: ['update:value', 'input'],\n\n  components: {\n    ArrayList,\n    LabeledInput,\n    Select\n  },\n\n  props: {\n    value: {\n      type:    Array,\n      default: null\n    },\n    mode: {\n      type:    String,\n      default: _VIEW\n    },\n    addLabel: {\n      type:     String,\n      required: true\n    }\n  },\n\n  data() {\n    return {\n      operatorOptions: [\n        {\n          label: 'is set',\n          value: OPERATOR_VALUES.IS_SET,\n        },\n        {\n          label: 'is not set',\n          value: OPERATOR_VALUES.IS_NOT_SET,\n        },\n        {\n          label: 'in list',\n          value: OPERATOR_VALUES.IN_LIST,\n        },\n        {\n          label: 'not in list',\n          value: OPERATOR_VALUES.NOT_IN_LIST,\n        }\n      ],\n      optionsWithValueDisabled: [\n        OPERATOR_VALUES.IS_SET,\n        OPERATOR_VALUES.IS_NOT_SET\n      ],\n      defaultAddValue: {\n        key:      '',\n        operator: OPERATOR_VALUES.IS_SET,\n      }\n    };\n  },\n\n  computed: {\n    localValue: {\n      get() {\n        return this.value;\n      },\n      set(localValue) {\n        this.$emit('update:value', localValue);\n      }\n    }\n  },\n\n  methods: {\n    onOperatorInput(scope, operator) {\n      scope.row.value.operator = operator;\n      if (this.optionsWithValueDisabled.includes(operator)) {\n        if (scope.row.value.values) {\n          delete scope.row.value.values;\n        }\n      } else {\n        scope.row.value.values = scope.row.value.values || [];\n      }\n      scope.queueUpdate();\n    },\n\n    isValueDisabled(scope) {\n      return this.optionsWithValueDisabled.includes(scope.row.value.operator);\n    },\n    getValue(scope) {\n      return scope.row.value.values?.join(',') || '';\n    },\n    onValueInput(scope, rawValue) {\n      scope.row.value.values = rawValue.split(',')\n        .map((entry) => entry.trim());\n      scope.queueUpdate();\n    }\n  },\n};\n</script>\n\n<template>\n  <div\n    class=\"rule-selector\"\n    :class=\"{[mode]: true}\"\n  >\n    <ArrayList\n      :value=\"value\"\n      :protip=\"false\"\n      :show-header=\"true\"\n      :add-label=\"addLabel\"\n      :default-add-value=\"defaultAddValue\"\n      :mode=\"mode\"\n      @update:value=\"$emit('input', $event)\"\n    >\n      <template v-slot:column-headers>\n        <div class=\"box\">\n          <div class=\"key\">\n            Key\n          </div>\n          <div class=\"operator\">\n            Operator\n          </div>\n          <div class=\"value\">\n            Value\n          </div>\n          <div />\n        </div>\n      </template>\n      <template v-slot:columns=\"scope\">\n        <div class=\"key\">\n          <LabeledInput\n            v-model:value=\"scope.row.value.key\"\n            :mode=\"mode\"\n          />\n        </div>\n        <div class=\"operator\">\n          <Select\n            :mode=\"mode\"\n            :value=\"scope.row.value.operator\"\n            :options=\"operatorOptions\"\n            @update:value=\"onOperatorInput(scope, $event)\"\n          />\n        </div>\n        <div class=\"value\">\n          <LabeledInput\n            :disabled=\"isValueDisabled(scope)\"\n            :value=\"getValue(scope)\"\n            :mode=\"mode\"\n            @update:value=\"onValueInput(scope, $event)\"\n          />\n        </div>\n      </template>\n    </ArrayList>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.rule-selector {\n  &:not(.view) table {\n    table-layout: initial;\n  }\n\n   :deep() .box {\n    display: grid;\n    grid-template-columns: 25% 25% 25% 15%;\n    column-gap: 1.75%;\n    align-items: center;\n    margin-bottom: 10px;\n\n    .key,\n    .value,\n    .operator {\n      height: 100%;\n    }\n  }\n}\n</style>\n"]}]}