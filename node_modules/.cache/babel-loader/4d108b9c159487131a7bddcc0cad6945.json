{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??clonedRuleSet-44.use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/utils/pagination-wrapper.ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/utils/pagination-wrapper.ts", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/utils/pagination-wrapper.ts", "sourceRoot": "", "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/utils/pagination-wrapper.ts"], "names": [], "mappings": "AAAA,OAAO,eAAe,MAAM,+BAA+B,CAAC;AAU5D;;;;;;;;;GASG;AACH,MAAM,iBAAiB;IASnB,YAAY,EACV,MAAM,EACN,UAAU,EACV,QAAQ,GAKT;QACC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC,SAAS,CAAC,EAAE,WAAW,EAAE,MAAM,CAAC,OAAO,EAAE,EAAE,UAAU,CAAC,CAAC;QACxF,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,IAGb;;QACC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,qBAAsB,IAAI,CAAC,UAAU,CAAC,KAAM,IAAK,MAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,0CAAE,EAAG,iBAAkB,MAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,0CAAE,OAAQ,iBAAiB,CAAC,CAAC;QACvK,CAAC;QACD,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;QAClD,MAAM,GAAG,GAAuB;YAC9B,SAAS,EAAE,IAAI;YACf,UAAU;SACX,CAAC;QAEF,MAAM,GAAG,GAAc,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAI,IAAI,CAAC,UAAU,CAAC,KAAM,WAAW,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,MAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,0CAAE,EAAE,EAAE,CAAC,CAAC;QAEtI,IAAI,UAAU,EAAE,CAAC;YACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACzC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAI,IAAI,CAAC,UAAU,CAAC,KAAM,SAAS,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7F,CAAC;QACH,CAAC;QAED,OAAO,GAAG,CAAC;IACb,CAAC;CACJ;AAED,eAAe,iBAAiB,CAAC", "sourcesContent": ["import paginationUtils from '@shell/utils/pagination-utils';\nimport { PaginationArgs, PaginationResourceContext, StorePagination } from '@shell/types/store/pagination.types';\nimport { VuexStore } from '@shell/types/store/vuex';\nimport { ActionFindPageArgs } from '@shell/types/store/dashboard-store.types';\n\ninterface Result<T> {\n  data: Array<T>\n  pagination: StorePagination\n}\n\n/**\n * This is a helper class that will assist in fetching a resource\n * - Handle if the resource can be fetched by the new pagination api\n * - Make a request to get a page (including classify)\n * - Provide updates when the resource changes\n *\n * This is designed to work in places where we don't/can't store the resource in the store\n * - There already exists a resource we don't want to overwrite\n * - We're transient and want something nicer than just cluster/request\n */\nclass PaginationWrapper<T = any> {\n    private $store: VuexStore;\n    private enabledFor: PaginationResourceContext;\n\n    // Blocked on https://github.com/rancher/rancher/issues/40773 / https://github.com/rancher/dashboard/issues/12734\n    private onUpdate: (out: Result<T>) => void;\n\n    public isEnabled: boolean;\n\n    constructor({\n      $store,\n      enabledFor,\n      onUpdate,\n    }: {\n        $store: VuexStore,\n        onUpdate: (res: Result<T>) => void,\n        enabledFor: PaginationResourceContext,\n    }) {\n      this.$store = $store;\n      this.isEnabled = paginationUtils.isEnabled({ rootGetters: $store.getters }, enabledFor);\n      this.enabledFor = enabledFor;\n      this.onUpdate = onUpdate;\n    }\n\n    async request(args: {\n        pagination: PaginationArgs,\n        classify?: boolean,\n    }): Promise<Result<T>> {\n      if (!this.isEnabled) {\n        throw new Error(`Wrapper for type '${ this.enabledFor.store }/${ this.enabledFor.resource?.id }' in context '${ this.enabledFor.resource?.context }' not supported`);\n      }\n      const { pagination, classify: doClassify } = args;\n      const opt: ActionFindPageArgs = {\n        transient: true,\n        pagination\n      };\n\n      const out: Result<T> = await this.$store.dispatch(`${ this.enabledFor.store }/findPage`, { opt, type: this.enabledFor.resource?.id });\n\n      if (doClassify) {\n        for (let i = 0; i < out.data.length; i++) {\n          out.data[i] = await this.$store.dispatch(`${ this.enabledFor.store }/create`, out.data[i]);\n        }\n      }\n\n      return out;\n    }\n}\n\nexport default PaginationWrapper;\n"]}]}