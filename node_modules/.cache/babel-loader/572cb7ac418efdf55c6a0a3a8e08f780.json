{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ModalWithCard.vue?vue&type=template&id=4c410e60&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ModalWithCard.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ModalWithCard.vue"], "names": ["$attrs", "$emit"], "mappings": ";;qBA0EY,KAAK,EAAC,mBAAmB;qBAsBtB,KAAK,EAAC,QAAQ;;;;;;;;wBArC3B,aAqDY,sBArDZ,YAqDY;IApDT,IAAI,EAAE,WAAI;IACV,KAAK,EAAE,YAAK;IACZ,gBAAc,EAAE,KAAK;IACrB,MAAM,EAAE,aAAM;KACPA,WAAM;IACd,KAAK,EAAC,OAAO;IACb,aAAW,EAAC,WAAW;IACtB,OAAK,uCAAEC,UAAK,WAAW,MAAM;;sBAE9B,CA0CO;MA1CP,aA0CO;QAzCL,KAAK,EAAC,OAAO;QACZ,uBAAqB,EAAE,KAAK;;QAElB,KAAK,WACd,CAEK;UAFL,oBAEK,MAFL,UAEK;YADH,YAAqB;;;QAId,IAAI,WACb,CAAuB;UAAvB,YAAuB;;6BAEvB,oBASM,6BARgB,aAAM,GAAlB,GAAG,EAAC,GAAG;kCADjB,oBASM,SAPH,GAAG,EAAE,GAAG;cAET,aAIE;gBAHA,KAAK,EAAC,QAAQ;gBACd,KAAK,EAAC,OAAO;gBACZ,KAAK,EAAE,GAAG;;;;;QAKN,OAAO,WAChB,CAcO;UAdP,YAcO,2BAdP,CAcO;YAbL,oBAYM,OAZN,UAYM;cAXJ,oBAKS;gBAJP,KAAK,EAAC,0BAA0B;gBAC/B,OAAK,yDAAU,uCAAI;kCAEjB,gBAAS;;cAGd,aAGE;gBAFC,IAAI,EAAE,eAAQ;gBACd,OAAK,uCAAEA,UAAK,WAAW,MAAM", "sourcesContent": ["<script>\nimport { Card } from '@components/Card';\nimport { Banner } from '@components/Banner';\nimport AsyncButton from '@shell/components/AsyncButton';\nimport AppModal from '@shell/components/AppModal.vue';\n\nexport default {\n  name: 'ModalWithCard',\n\n  emits: ['close', 'finish'],\n\n  components: {\n    Card, Banner, AsyncButton, AppModal\n  },\n\n  props: {\n    name: {\n      type:     String,\n      required: true\n    },\n\n    closeText: {\n      type:    String,\n      default: 'Close'\n    },\n\n    saveText: {\n      type:    String,\n      default: 'create'\n    },\n\n    width: {\n      type:    [String, Number],\n      default: '50%'\n    },\n\n    height: {\n      type:    [String, Number],\n      default: 'auto'\n    },\n\n    errors: {\n      type:    Array,\n      default: () => {\n        return [];\n      }\n    }\n  },\n\n  methods: {\n    hide() {\n      this.$emit('close');\n    },\n  }\n};\n\n</script>\n\n<template>\n  <app-modal\n    :name=\"name\"\n    :width=\"width\"\n    :click-to-close=\"false\"\n    :height=\"height\"\n    v-bind=\"$attrs\"\n    class=\"modal\"\n    data-testid=\"mvc__card\"\n    @close=\"$emit('finish', $event)\"\n  >\n    <Card\n      class=\"modal\"\n      :show-highlight-border=\"false\"\n    >\n      <template #title>\n        <h4 class=\"text-default-text\">\n          <slot name=\"title\" />\n        </h4>\n      </template>\n\n      <template #body>\n        <slot name=\"content\" />\n\n        <div\n          v-for=\"(err,idx) in errors\"\n          :key=\"idx\"\n        >\n          <Banner\n            class=\"banner\"\n            color=\"error\"\n            :label=\"err\"\n          />\n        </div>\n      </template>\n\n      <template #actions>\n        <slot name=\"footer\">\n          <div class=\"footer\">\n            <button\n              class=\"btn role-secondary mr-20\"\n              @click.prevent=\"hide\"\n            >\n              {{ closeText }}\n            </button>\n\n            <AsyncButton\n              :mode=\"saveText\"\n              @click=\"$emit('finish', $event)\"\n            />\n          </div>\n        </slot>\n      </template>\n    </Card>\n  </app-modal>\n</template>\n\n<style lang=\"scss\" scoped>\n.footer {\n  width: 100%;\n  display: flex;\n  justify-content: center;\n}\n\n.banner {\n  margin-bottom: 0px;\n}\n</style>\n\n<style lang=\"scss\">\n.modal {\n  border-radius: var(--border-radius);\n  max-height: 100vh;\n\n  &.card-container {\n    box-shadow: none;\n  }\n}\n</style>\n"]}]}