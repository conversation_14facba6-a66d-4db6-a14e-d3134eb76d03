{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Probe.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Probe.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCB7IF9FRElULCBfVklFVyB9IGZyb20gJ0BzaGVsbC9jb25maWcvcXVlcnktcGFyYW1zJzsKaW1wb3J0IHsgY2xvbmUgfSBmcm9tICdAc2hlbGwvdXRpbHMvb2JqZWN0JzsKaW1wb3J0IFVuaXRJbnB1dCBmcm9tICdAc2hlbGwvY29tcG9uZW50cy9mb3JtL1VuaXRJbnB1dCc7CmltcG9ydCB7IExhYmVsZWRJbnB1dCB9IGZyb20gJ0Bjb21wb25lbnRzL0Zvcm0vTGFiZWxlZElucHV0JzsKaW1wb3J0IExhYmVsZWRTZWxlY3QgZnJvbSAnQHNoZWxsL2NvbXBvbmVudHMvZm9ybS9MYWJlbGVkU2VsZWN0JzsKaW1wb3J0IFNoZWxsSW5wdXQgZnJvbSAnQHNoZWxsL2NvbXBvbmVudHMvZm9ybS9TaGVsbElucHV0JzsKaW1wb3J0IEtleVZhbHVlIGZyb20gJ0BzaGVsbC9jb21wb25lbnRzL2Zvcm0vS2V5VmFsdWUnOwoKY29uc3QgS0lORFMgPSBbCiAgJ25vbmUnLAogICdIVFRQJywKICAnSFRUUFMnLAogICd0Y3AnLAogICdleGVjJywKXTsKCmV4cG9ydCBkZWZhdWx0IHsKICBlbWl0czogWyd1cGRhdGU6dmFsdWUnXSwKCiAgY29tcG9uZW50czogewogICAgTGFiZWxlZElucHV0LCBMYWJlbGVkU2VsZWN0LCBVbml0SW5wdXQsIFNoZWxsSW5wdXQsIEtleVZhbHVlLAogIH0sCgogIHByb3BzOiB7CiAgICB2YWx1ZTogewogICAgICB0eXBlOiAgICBbT2JqZWN0LCBudWxsXSwKICAgICAgZGVmYXVsdDogbnVsbCwKICAgIH0sCiAgICBtb2RlOiB7CiAgICAgIHR5cGU6ICAgIFN0cmluZywKICAgICAgZGVmYXVsdDogX0VESVQsCiAgICB9LAoKICAgIGxhYmVsOiB7CiAgICAgIHR5cGU6ICAgIFN0cmluZywKICAgICAgZGVmYXVsdDogJ1Byb2JlJywKICAgIH0sCgogICAgZGVzY3JpcHRpb246IHsKICAgICAgdHlwZTogICAgU3RyaW5nLAogICAgICBkZWZhdWx0OiAnJywKICAgIH0sCiAgfSwKCiAgZGF0YSgpIHsKICAgIGxldCBraW5kID0gJ25vbmUnOwogICAgbGV0IHByb2JlID0gbnVsbDsKICAgIGxldCBleGVjID0gbnVsbDsKICAgIGxldCBodHRwR2V0ID0gbnVsbDsKICAgIGxldCB0Y3BTb2NrZXQgPSBudWxsOwoKICAgIGlmICggdGhpcy52YWx1ZSApIHsKICAgICAgcHJvYmUgPSBjbG9uZSh0aGlzLnZhbHVlKTsKCiAgICAgIGlmICggcHJvYmUuZXhlYyApIHsKICAgICAgICBraW5kID0gJ2V4ZWMnOwogICAgICB9IGVsc2UgaWYgKCBwcm9iZS5odHRwR2V0ICkgewogICAgICAgIGlmICggKHByb2JlLmh0dHBHZXQuc2NoZW1lIHx8ICcnKS50b0xvd2VyQ2FzZSgpID09PSAnaHR0cHMnICkgewogICAgICAgICAga2luZCA9ICdIVFRQUyc7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGtpbmQgPSAnSFRUUCc7CiAgICAgICAgfQogICAgICB9IGVsc2UgaWYgKCBwcm9iZS50Y3BTb2NrZXQgKSB7CiAgICAgICAga2luZCA9ICd0Y3AnOwogICAgICB9CiAgICB9IGVsc2UgewogICAgICBwcm9iZSA9IHsKICAgICAgICBmYWlsdXJlVGhyZXNob2xkOiAgICAzLAogICAgICAgIHN1Y2Nlc3NUaHJlc2hvbGQ6ICAgIDEsCiAgICAgICAgaW5pdGlhbERlbGF5U2Vjb25kczogMCwKICAgICAgICB0aW1lb3V0U2Vjb25kczogICAgICAxLAogICAgICAgIHBlcmlvZFNlY29uZHM6ICAgICAgIDEwLAogICAgICAgIGV4ZWM6ICAgICAgICAgICAgICAgIG51bGwsCiAgICAgICAgaHR0cEdldDogICAgICAgICAgICAgbnVsbCwKICAgICAgICB0Y3BTb2NrZXQ6ICAgICAgICAgICBudWxsLAogICAgICB9OwogICAgfQoKICAgIGV4ZWMgPSBwcm9iZS5leGVjIHx8IHt9OwogICAgaHR0cEdldCA9IHByb2JlLmh0dHBHZXQgfHwge307CiAgICB0Y3BTb2NrZXQgPSBwcm9iZS50Y3BTb2NrZXQgfHwge307CgogICAgcmV0dXJuIHsKICAgICAgcHJvYmUsIGtpbmQsIGV4ZWMsIGh0dHBHZXQsIHRjcFNvY2tldAogICAgfTsKICB9LAoKICBjb21wdXRlZDogewogICAgaXNWaWV3KCkgewogICAgICByZXR1cm4gdGhpcy5tb2RlID09PSBfVklFVzsKICAgIH0sCgogICAgaXNOb25lKCkgewogICAgICByZXR1cm4gdGhpcy5raW5kID09PSAnbm9uZSc7CiAgICB9LAoKICAgIGtpbmRPcHRpb25zKCkgewogICAgICByZXR1cm4gS0lORFMubWFwKChrKSA9PiB7CiAgICAgICAgcmV0dXJuIHsgbGFiZWw6IHRoaXMudChgd29ya2xvYWQuY29udGFpbmVyLmhlYWx0aENoZWNrLmtpbmQuJHsgayB9YCksIHZhbHVlOiBrIH07CiAgICAgIH0pOwogICAgfQogIH0sCgogIHdhdGNoOiB7CiAgICBraW5kKCkgewogICAgICB0aGlzLnVwZGF0ZSgpOwogICAgfQogIH0sCgogIG1ldGhvZHM6IHsKICAgIHVwZGF0ZSgpIHsKICAgICAgY29uc3QgcHJvYmUgPSB0aGlzLnByb2JlOwoKICAgICAgaWYgKCB0aGlzLmlzTm9uZSApIHsKICAgICAgICB0aGlzLiRlbWl0KCd1cGRhdGU6dmFsdWUnLCBudWxsKTsKCiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICBzd2l0Y2ggKCB0aGlzLmtpbmQgKSB7CiAgICAgIGNhc2UgJ0hUVFAnOgogICAgICBjYXNlICdIVFRQUyc6CiAgICAgICAgdGhpcy5odHRwR2V0LnNjaGVtZSA9IHRoaXMua2luZDsKICAgICAgICBwcm9iZS5odHRwR2V0ID0gdGhpcy5odHRwR2V0OwogICAgICAgIHByb2JlLnRjcFNvY2tldCA9IG51bGw7CiAgICAgICAgcHJvYmUuZXhlYyA9IG51bGw7CiAgICAgICAgYnJlYWs7CiAgICAgIGNhc2UgJ3RjcCc6CiAgICAgICAgcHJvYmUuaHR0cEdldCA9IG51bGw7CiAgICAgICAgcHJvYmUudGNwU29ja2V0ID0gdGhpcy50Y3BTb2NrZXQ7CiAgICAgICAgcHJvYmUuZXhlYyA9IG51bGw7CiAgICAgICAgYnJlYWs7CiAgICAgIGNhc2UgJ2V4ZWMnOgogICAgICAgIHByb2JlLmh0dHBHZXQgPSBudWxsOwogICAgICAgIHByb2JlLnRjcFNvY2tldCA9IG51bGw7CiAgICAgICAgcHJvYmUuZXhlYyA9IHRoaXMuZXhlYzsKICAgICAgICBicmVhazsKICAgICAgfQoKICAgICAgdGhpcy4kZW1pdCgndXBkYXRlOnZhbHVlJywgcHJvYmUpOwogICAgfQogIH0sCn07Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Probe.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEtD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACR,CAAC;;AAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9D,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACb,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEpB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEzB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QAChB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QAC1B,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UAC5D,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf;MACF,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QAC5B,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd;IACF,EAAE,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC;IACH;;IAEA,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;IAEjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAClF,CAAC,CAAC;IACJ;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAExB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEhC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC;MACP;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC;EACF,CAAC;AACH,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Probe.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\nimport { clone } from '@shell/utils/object';\nimport UnitInput from '@shell/components/form/UnitInput';\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport ShellInput from '@shell/components/form/ShellInput';\nimport KeyValue from '@shell/components/form/KeyValue';\n\nconst KINDS = [\n  'none',\n  'HTTP',\n  'HTTPS',\n  'tcp',\n  'exec',\n];\n\nexport default {\n  emits: ['update:value'],\n\n  components: {\n    LabeledInput, LabeledSelect, UnitInput, ShellInput, KeyValue,\n  },\n\n  props: {\n    value: {\n      type:    [Object, null],\n      default: null,\n    },\n    mode: {\n      type:    String,\n      default: _EDIT,\n    },\n\n    label: {\n      type:    String,\n      default: 'Probe',\n    },\n\n    description: {\n      type:    String,\n      default: '',\n    },\n  },\n\n  data() {\n    let kind = 'none';\n    let probe = null;\n    let exec = null;\n    let httpGet = null;\n    let tcpSocket = null;\n\n    if ( this.value ) {\n      probe = clone(this.value);\n\n      if ( probe.exec ) {\n        kind = 'exec';\n      } else if ( probe.httpGet ) {\n        if ( (probe.httpGet.scheme || '').toLowerCase() === 'https' ) {\n          kind = 'HTTPS';\n        } else {\n          kind = 'HTTP';\n        }\n      } else if ( probe.tcpSocket ) {\n        kind = 'tcp';\n      }\n    } else {\n      probe = {\n        failureThreshold:    3,\n        successThreshold:    1,\n        initialDelaySeconds: 0,\n        timeoutSeconds:      1,\n        periodSeconds:       10,\n        exec:                null,\n        httpGet:             null,\n        tcpSocket:           null,\n      };\n    }\n\n    exec = probe.exec || {};\n    httpGet = probe.httpGet || {};\n    tcpSocket = probe.tcpSocket || {};\n\n    return {\n      probe, kind, exec, httpGet, tcpSocket\n    };\n  },\n\n  computed: {\n    isView() {\n      return this.mode === _VIEW;\n    },\n\n    isNone() {\n      return this.kind === 'none';\n    },\n\n    kindOptions() {\n      return KINDS.map((k) => {\n        return { label: this.t(`workload.container.healthCheck.kind.${ k }`), value: k };\n      });\n    }\n  },\n\n  watch: {\n    kind() {\n      this.update();\n    }\n  },\n\n  methods: {\n    update() {\n      const probe = this.probe;\n\n      if ( this.isNone ) {\n        this.$emit('update:value', null);\n\n        return;\n      }\n\n      switch ( this.kind ) {\n      case 'HTTP':\n      case 'HTTPS':\n        this.httpGet.scheme = this.kind;\n        probe.httpGet = this.httpGet;\n        probe.tcpSocket = null;\n        probe.exec = null;\n        break;\n      case 'tcp':\n        probe.httpGet = null;\n        probe.tcpSocket = this.tcpSocket;\n        probe.exec = null;\n        break;\n      case 'exec':\n        probe.httpGet = null;\n        probe.tcpSocket = null;\n        probe.exec = this.exec;\n        break;\n      }\n\n      this.$emit('update:value', probe);\n    }\n  },\n};\n</script>\n\n<template>\n  <div>\n    <div class=\"title clearfix\">\n      <h3>\n        {{ label }}\n        <i\n          v-if=\"description\"\n          v-clean-tooltip=\"description\"\n          class=\"icon icon-info\"\n        />\n      </h3>\n    </div>\n    <div class=\"row\">\n      <div\n        data-testid=\"input-probe-kind\"\n        class=\"col span-11-of-23\"\n      >\n        <LabeledSelect\n          v-model:value=\"kind\"\n          :mode=\"mode\"\n          :label=\"t('probe.type.label')\"\n          :options=\"kindOptions\"\n          :placeholder=\"t('probe.type.placeholder')\"\n          @update:value=\"update\"\n        />\n\n        <div\n          v-if=\"kind && kind!=='none'\"\n          class=\"spacer-small\"\n        />\n\n        <!-- HTTP/HTTPS -->\n        <div\n          v-if=\"kind === 'HTTP' || kind === 'HTTPS'\"\n          data-testid=\"input-probe-port\"\n        >\n          <LabeledInput\n            v-model:value.number=\"httpGet.port\"\n            type=\"number\"\n            min=\"1\"\n            max=\"65535\"\n            :mode=\"mode\"\n            :label=\"t('probe.httpGet.port.label')\"\n            :placeholder=\"t('probe.httpGet.port.placeholder')\"\n            @update:value=\"update\"\n          />\n\n          <div class=\"spacer-small\" />\n\n          <div data-testid=\"input-probe-path\">\n            <LabeledInput\n              v-model:value=\"httpGet.path\"\n              :mode=\"mode\"\n              :label=\"t('probe.httpGet.path.label')\"\n              :placeholder=\"t('probe.httpGet.path.placeholder')\"\n              @update:value=\"update\"\n            />\n          </div>\n        </div>\n\n        <!-- TCP -->\n        <div\n          v-if=\"kind === 'tcp'\"\n          data-testid=\"input-probe-socket\"\n        >\n          <LabeledInput\n            v-model:value.number=\"tcpSocket.port\"\n            type=\"number\"\n            min=\"1\"\n            max=\"65535\"\n            :mode=\"mode\"\n            :label=\"t('probe.httpGet.port.label')\"\n            :placeholder=\"t('probe.httpGet.port.placeholderDeux')\"\n            @update:value=\"update\"\n          />\n          <div class=\"spacer-small\" />\n        </div>\n\n        <!-- Exec -->\n        <div\n          v-if=\"kind === 'exec'\"\n          data-testid=\"input-probe-command\"\n        >\n          <div class=\"col span-12\">\n            <ShellInput\n              v-model:value=\"exec.command\"\n              :label=\"t('probe.command.label')\"\n              :placeholder=\"t('probe.command.placeholder')\"\n              @update:value=\"update\"\n            />\n          </div>\n          <div class=\"spacer-small\" />\n        </div>\n      </div>\n\n      <div class=\"col span-1-of-13\">\n        <hr\n          v-if=\"kind && kind!=='none'\"\n          :style=\"{'position':'relative', 'margin':'0px'}\"\n          class=\"vertical\"\n        >\n      </div>\n\n      <!-- none -->\n      <div\n        v-if=\"!isNone\"\n        class=\"col span-11-of-23\"\n      >\n        <div class=\"row\">\n          <div\n            data-testid=\"input-probe-periodSeconds\"\n            class=\"col span-4\"\n          >\n            <UnitInput\n              v-model:value=\"probe.periodSeconds\"\n              :mode=\"mode\"\n              :label=\"t('probe.checkInterval.label')\"\n              min=\"1\"\n              :suffix=\"t('suffix.sec')\"\n              :placeholder=\"t('probe.checkInterval.placeholder')\"\n              @update:value=\"update\"\n            />\n          </div>\n          <div\n            data-testid=\"input-probe-initialDelaySeconds\"\n            class=\"col span-4\"\n          >\n            <UnitInput\n              v-model:value=\"probe.initialDelaySeconds\"\n              :mode=\"mode\"\n              :suffix=\"t('suffix.sec')\"\n              :label=\"t('probe.initialDelay.label')\"\n              min=\"0\"\n              :placeholder=\"t('probe.initialDelay.placeholder')\"\n              @update:value=\"update\"\n            />\n          </div>\n          <div\n            data-testid=\"input-probe-timeoutSeconds\"\n            class=\"col span-4\"\n          >\n            <UnitInput\n              v-model:value=\"probe.timeoutSeconds\"\n              min=\"0\"\n              :mode=\"mode\"\n              :suffix=\"t('suffix.sec')\"\n              :label=\"t('probe.timeout.label')\"\n              :placeholder=\"t('probe.timeout.placeholder')\"\n              @update:value=\"update\"\n            />\n          </div>\n        </div>\n\n        <div class=\"spacer-small\" />\n\n        <div class=\"row\">\n          <div\n            data-testid=\"input-probe-successThreshold\"\n            class=\"col span-6\"\n          >\n            <LabeledInput\n              v-model:value.number=\"probe.successThreshold\"\n              type=\"number\"\n              min=\"1\"\n              :mode=\"mode\"\n              :label=\"t('probe.successThreshold.label')\"\n              :placeholder=\"t('probe.successThreshold.placeholder')\"\n              @update:value=\"update\"\n            />\n          </div>\n          <div\n            data-testid=\"input-probe-failureThreshold\"\n            class=\"col span-6\"\n          >\n            <LabeledInput\n              v-model:value.number=\"probe.failureThreshold\"\n              type=\"number\"\n              min=\"1\"\n              :mode=\"mode\"\n              :label=\"t('probe.failureThreshold.label')\"\n              :placeholder=\"t('probe.failureThreshold.placeholder')\"\n              @update:value=\"update\"\n            />\n          </div>\n        </div>\n\n        <template v-if=\"kind === 'HTTP' || kind === 'HTTPS'\">\n          <div class=\"spacer-small\" />\n\n          <div class=\"row\">\n            <div class=\"col span-12\">\n              <KeyValue\n                v-model:value=\"httpGet.httpHeaders\"\n                data-testid=\"input-probe-http-headers\"\n                key-name=\"name\"\n                :mode=\"mode\"\n                :as-map=\"false\"\n                :read-allowed=\"false\"\n                :title=\"t('probe.httpGet.headers.label')\"\n                :key-label=\"t('generic.name')\"\n                :value-label=\"t('generic.value')\"\n                :add-label=\"t('generic.add')\"\n                @update:value=\"update\"\n              >\n                <template #title>\n                  <h3>\n                    {{ t('workload.container.healthCheck.httpGet.headers') }}\n                  </h3>\n                </template>\n              </KeyValue>\n            </div>\n          </div>\n        </template>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n\n  .title {\n    margin-bottom: 10px;\n  }\n  :deep() .labeled-select {\n    height: auto;\n  }\n\n</style>\n"]}]}