{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/ForceDirectedTreeChart/index.vue?vue&type=template&id=1b75a374", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/ForceDirectedTreeChart/index.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/ForceDirectedTreeChart/index.vue"], "names": ["t"], "mappings": ";;;EAkWM,KAAK,EAAC,iBAAiB;EACvB,aAAW,EAAC,eAAe;;;;EAKzB,KAAK,EAAC,mBAAmB;;qBAatB,KAAK,EAAC,qBAAqB;qBACzB,KAAK,EAAC,WAAW;qBAUR,KAAK,EAAC,sBAAsB;;;;qBAgBlC,KAAK,EAAC,cAAc;;;EAapB,KAAK,EAAC,cAAc;EACpB,OAAO,EAAC,GAAG;;;;;;;wBA9DzB,oBA2EM;IA1EJ,oBAyEM,OAzEN,UAyEM;MArEJ,iDAAiC;;QAExB,yCAAmC;yBAD5C,oBAWM,OAXN,UAWM;4BAPJ,oBAEI,4BADCA,MAAC;wBADM,0BAAoB;;;4BAGhC,oBAEI,4BADCA,MAAC;uBADK,0BAAoB,KAAK,yCAAmC;;;sCAGvE,oBAA0C,OAAvC,KAAK,EAAC,8BAA8B;;;;MAEzC,mDAAmC;;kCACnC,oBAAiB,SAAZ,EAAE,EAAC,MAAM;;MACd,iCAAiB;;MACjB,oBAoDM,OApDN,UAoDM;QAnDJ,oBAkDM,OAlDN,UAkDM;UAjDJ,oBAgDQ;+BA/CN,oBA8CK,6BA7CiB,cAAQ,GAApB,IAAI,EAAE,CAAC;oCADjB,oBA8CK,QA5CF,GAAG,EAAE,CAAC;iBAGC,IAAI,CAAC,IAAI;mCADjB,oBAKK;;sBAHF,KAAK,mCAAmB,IAAI,CAAC,IAAI;;sBAElC,oBAAiE,QAAjE,UAAiE,mBAA3BA,MAAC,CAAC,IAAI,CAAC,QAAQ,KAAI,GAAC;;;;gBAE5D,uCAAuB;;iBACb,IAAI,CAAC,IAAI;mCAAnB,oBASK;uBARS,IAAI,CAAC,QAAQ,CAAC,cAAc;yCAAxC,oBAMO;4BALL,aAIc;8BAHX,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,cAAc;;gDAEjC,CAAyB;kEAAtB,IAAI,CAAC,QAAQ,CAAC,KAAK;;;;;yCAG1B,oBAA6C,qCAA7B,IAAI,CAAC,QAAQ,CAAC,KAAK;;qBAIxB,IAAI,CAAC,IAAI;qCADtB,oBAWK;wBAZL,6CAA6B;wBAC7B,oBAWK,MAXL,UAWK;0BAPH,oBAMO;4BALL,aAIE;8BAHC,KAAK,QAAQ,IAAI,CAAC,QAAQ,CAAC,UAAU;8BACrC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU;8BAChC,KAAK,EAAC,aAAa;;;;;;;gBAIzB,8CAA8B;;iBAEtB,IAAI,CAAC,IAAI;mCADjB,oBAMK,MANL,WAMK;sBADH,oBAAuB,4BAAjB,IAAI,CAAC,KAAK;;mCAGlB,oBAEK;sBAHL,yCAAyB;sBACzB,oBAEK,6BADA,IAAI,CAAC,KAAK", "sourcesContent": ["<script>\nimport * as d3 from 'd3';\nimport { STATES } from '@shell/plugins/dashboard-store/resource-class';\nimport { BadgeState } from '@components/BadgeState';\nimport { getChartIcon } from './chartIcons.js';\n\nexport default {\n  name:       'ForceDirectedTreeChart',\n  components: { BadgeState },\n  props:      {\n    data: {\n      type:     [Array, Object],\n      required: true\n    },\n    fdcConfig: {\n      type:     Object,\n      required: true\n    }\n  },\n  data() {\n    return {\n      dataWatcher:                         undefined,\n      parsedInfo:                          undefined,\n      root:                                undefined,\n      allNodesData:                        undefined,\n      allLinks:                            undefined,\n      rootNode:                            undefined,\n      node:                                undefined,\n      link:                                undefined,\n      svg:                                 undefined,\n      zoom:                                undefined,\n      simulation:                          undefined,\n      isChartFirstRendered:                false,\n      isChartFirstRenderAnimationFinished: false,\n      moreInfo:                            {}\n    };\n  },\n  methods: {\n    watcherFunction(newValue) {\n      if (newValue.length) {\n        if (!this.isChartFirstRendered) {\n          this.parsedInfo = this.fdcConfig.parseData(this.data);\n\n          // set details info and set active state for node\n          this.setDetailsInfo(this.parsedInfo, false);\n          this.parsedInfo.active = true;\n\n          // render and update chart\n          this.renderChart();\n          this.updateChart(true, true);\n          this.isChartFirstRendered = true;\n\n          // here we just look for changes in the status of the nodes and update them accordingly\n        } else {\n          const parsedInfo = this.fdcConfig.parseData(this.data);\n          const flattenedData = this.flatten(parsedInfo);\n          let hasStatusChange = false;\n\n          flattenedData.forEach((item) => {\n            const index = this.allNodesData.findIndex((nodeData) => item.matchingId === nodeData.data.matchingId);\n\n            // apply status change to each node\n            if (index > -1 && this.allNodesData[index].data.state !== item.state) {\n              this.allNodesData[index].data.state = item.state;\n              this.allNodesData[index].data.stateLabel = item.stateLabel;\n              this.allNodesData[index].data.stateColor = item.stateColor;\n              hasStatusChange = true;\n\n              // if node is selected (active), update details info\n              if (this.allNodesData[index].data.active) {\n                this.setDetailsInfo(this.allNodesData[index].data, false);\n              }\n            }\n          });\n\n          if (hasStatusChange) {\n            this.updateChart(false, false);\n          }\n        }\n      }\n    },\n    renderChart() {\n      this.zoom = d3.zoom().scaleExtent([1 / 8, 16]).on('zoom', this.zoomed);\n      const transform = d3.zoomIdentity.scale(1).translate(0, 0);\n\n      this.rootNode = this.svg.append('g')\n        .attr('class', 'root-node');\n\n      this.svg.call(this.zoom);\n      this.svg.call(this.zoom.transform, transform);\n\n      this.simulation = d3.forceSimulation()\n        .force('charge', d3.forceManyBody().strength(this.fdcConfig.simulationParams.fdcStrength).distanceMax(this.fdcConfig.simulationParams.fdcDistanceMax))\n        .force('collision', d3.forceCollide(this.fdcConfig.simulationParams.fdcForceCollide))\n        .force('center', d3.forceCenter( this.fdcConfig.chartWidth / 2, this.fdcConfig.chartHeight / 2 ))\n        .alphaDecay(this.fdcConfig.simulationParams.fdcAlphaDecay)\n        .on('tick', this.ticked)\n        .on('end', () => {\n          if (!this.isChartFirstRenderAnimationFinished) {\n            this.zoomFit();\n            this.isChartFirstRenderAnimationFinished = true;\n          }\n        });\n    },\n    updateChart(isStartingData, isSettingNodesAndLinks) {\n      if (isStartingData) {\n        this.root = d3.hierarchy(this.parsedInfo);\n      }\n\n      if (isSettingNodesAndLinks) {\n        this.allNodesData = this.flatten(this.root);\n        this.allLinks = this.root.links();\n      }\n\n      this.link = this.rootNode\n        .selectAll('.link')\n        .data(this.allLinks, (d) => {\n          return d.target.id;\n        });\n\n      this.link.exit().remove();\n\n      const linkEnter = this.link\n        .enter()\n        .append('line')\n        .attr('class', 'link')\n        .style('opacity', '0.2')\n        .style('stroke-width', 4);\n\n      this.link = linkEnter.merge(this.link);\n\n      this.node = this.rootNode\n        .selectAll('.node')\n        .data(this.allNodesData, (d) => {\n          return d.id;\n        })\n        // this is where we define which prop changes with any data update (status color)\n        .attr('class', this.mainNodeClass);\n\n      this.node.exit().remove();\n\n      // define the node styling and function\n      const nodeEnter = this.node\n        .enter()\n        .append('g')\n        .attr('class', this.mainNodeClass)\n        .style('opacity', 1)\n        .on('click', (ev, d) => {\n          this.setDetailsInfo(d.data, true);\n        })\n        .call(d3.drag()\n          .on('start', this.dragStarted)\n          .on('drag', this.dragging)\n          .on('end', this.dragEnded));\n\n      // draw status circle (inherits color from main node)\n      nodeEnter.append('circle')\n        .attr('r', this.setNodeRadius);\n\n      nodeEnter.append('circle')\n        .attr('r', (d) => {\n          return this.setNodeRadius(d) - 5;\n        })\n        .attr('class', 'node-hover-layer');\n\n      nodeEnter.append('svg').html((d) => {\n        const icon = this.fdcConfig.fetchNodeIcon(d);\n\n        return getChartIcon(icon);\n      })\n        .attr('x', this.nodeImagePosition)\n        .attr('y', this.nodeImagePosition)\n        .attr('height', this.nodeImageSize)\n        .attr('width', this.nodeImageSize);\n\n      this.node = nodeEnter.merge(this.node);\n\n      this.simulation.nodes(this.allNodesData);\n      this.simulation.force('link', d3.forceLink()\n        .id((d) => {\n          return d.id;\n        })\n        .distance(100)\n        .links(this.allLinks)\n      );\n    },\n    mainNodeClass(d) {\n      const lowerCaseStatus = d.data?.state ? d.data.state.toLowerCase() : 'unkown_status';\n      const defaultClassArray = ['node'];\n\n      if (STATES[lowerCaseStatus] && STATES[lowerCaseStatus].color) {\n        defaultClassArray.push(`node-${ STATES[lowerCaseStatus].color }`);\n      } else {\n        defaultClassArray.push(`node-default-fill`);\n      }\n\n      // node active (clicked)\n      if (d.data?.active) {\n        defaultClassArray.push('active');\n      }\n\n      // here we extend the node classes (different chart types)\n      const extendedClassArray = this.fdcConfig.extendNodeClass(d).concat(defaultClassArray);\n\n      return extendedClassArray.join(' ');\n    },\n    setNodeRadius(d) {\n      const { radius } = this.fdcConfig.nodeDimensions(d);\n\n      return radius;\n    },\n    nodeImageSize(d) {\n      const { size } = this.fdcConfig.nodeDimensions(d);\n\n      return size;\n    },\n    nodeImagePosition(d) {\n      const { position } = this.fdcConfig.nodeDimensions(d);\n\n      return position;\n    },\n    setDetailsInfo(data, toUpdate) {\n      // get the data to be displayed on info box, per each different chart\n      this.moreInfo = Object.assign([], this.fdcConfig.infoDetails(data));\n\n      // update to the chart is needed when active state changes\n      if (toUpdate) {\n        this.allNodesData.forEach((item, i) => {\n          if (item.data.matchingId === data.matchingId) {\n            this.allNodesData[i].data.active = true;\n          } else {\n            this.allNodesData[i].data.active = false;\n          }\n        });\n\n        this.updateChart(false, false);\n      }\n    },\n    zoomFit() {\n      const rootNode = d3.select('.root-node');\n\n      if (!rootNode?.node()) {\n        return;\n      }\n\n      const paddingBuffer = 30;\n      const chartDimentions = rootNode.node().getBoundingClientRect();\n      const chartCoordinates = rootNode.node().getBBox();\n      const parent = rootNode.node().parentElement;\n      const fullWidth = parent.clientWidth;\n      const fullHeight = parent.clientHeight;\n      const width = chartDimentions.width;\n      const height = chartDimentions.height;\n      const midX = chartCoordinates.x + width / 2;\n      const midY = chartCoordinates.y + height / 2;\n\n      if (width === 0 || height === 0) {\n        return;\n      } // nothing to fit\n\n      const scale = 1 / Math.max(width / (fullWidth - paddingBuffer), height / (fullHeight - paddingBuffer));\n      const translate = [fullWidth / 2 - scale * midX, fullHeight / 2 - scale * midY];\n\n      const transform = d3.zoomIdentity\n        .translate(translate[0], translate[1])\n        .scale(scale);\n\n      // this update the cached zoom state!!!!! very important so that any transforms from user interaction keep this base!\n      this.svg.call(this.zoom.transform, transform);\n    },\n    ticked() {\n      this.link\n        .attr('x1', (d) => {\n          return d.source.x;\n        })\n        .attr('y1', (d) => {\n          return d.source.y;\n        })\n        .attr('x2', (d) => {\n          return d.target.x;\n        })\n        .attr('y2', (d) => {\n          return d.target.y;\n        });\n\n      this.node\n        .attr('transform', (d) => {\n          return `translate(${ d.x }, ${ d.y })`;\n        });\n    },\n    dragStarted(ev, d) {\n      if (!ev.active) {\n        this.simulation.alphaTarget(0.3).restart();\n      }\n      d.fx = d.x;\n      d.fy = d.y;\n    },\n    dragging(ev, d) {\n      d.fx = ev.x;\n      d.fy = ev.y;\n    },\n    dragEnded(ev, d) {\n      if (!ev.active) {\n        this.simulation.alphaTarget(0);\n      }\n      d.fx = undefined;\n      d.fy = undefined;\n    },\n    zoomed(ev) {\n      this.rootNode.attr('transform', ev.transform);\n    },\n    flatten(root) {\n      const nodes = [];\n      let i = 0;\n\n      function recurse(node) {\n        if (node.children) {\n          node.children.forEach(recurse);\n        }\n        if (!node.id) {\n          node.id = ++i;\n        } else {\n          ++i;\n        }\n        nodes.push(node);\n      }\n      recurse(root);\n\n      return nodes;\n    }\n  },\n  mounted() {\n    // start by appending SVG to define height of chart area\n    this.svg = d3.select('#tree').append('svg')\n      .attr('viewBox', `0 0 ${ this.fdcConfig.chartWidth } ${ this.fdcConfig.chartHeight }`)\n      .attr('preserveAspectRatio', 'none');\n\n    // set watcher for the chart data\n    this.dataWatcher = this.$watch(this.fdcConfig.watcherProp, function(newValue) {\n      this.watcherFunction(newValue);\n    }, {\n      deep:      true,\n      immediate: true\n    });\n  },\n  unmounted() {\n    this.dataWatcher();\n  },\n};\n</script>\n\n<template>\n  <div>\n    <div\n      class=\"chart-container\"\n      data-testid=\"gitrepo_graph\"\n    >\n      <!-- loading status container -->\n      <div\n        v-if=\"!isChartFirstRenderAnimationFinished\"\n        class=\"loading-container\"\n      >\n        <p v-show=\"!isChartFirstRendered\">\n          {{ t('fleet.fdc.loadingChart') }}\n        </p>\n        <p v-show=\"isChartFirstRendered && !isChartFirstRenderAnimationFinished\">\n          {{ t('fleet.fdc.renderingChart') }}\n        </p>\n        <i class=\"mt-10 icon-spinner icon-spin\" />\n      </div>\n      <!-- main div for svg container -->\n      <div id=\"tree\" />\n      <!-- info box -->\n      <div class=\"more-info-container\">\n        <div class=\"more-info\">\n          <table>\n            <tr\n              v-for=\"(item, i) in moreInfo\"\n              :key=\"i\"\n            >\n              <td\n                v-if=\"item.type !== 'single-error'\"\n                :class=\"{'align-middle': item.type === 'state-badge'}\"\n              >\n                <span class=\"more-info-item-label\">{{ t(item.labelKey) }}:</span>\n              </td>\n              <!-- title template -->\n              <td v-if=\"item.type === 'title-link'\">\n                <span v-if=\"item.valueObj.detailLocation\">\n                  <router-link\n                    :to=\"item.valueObj.detailLocation\"\n                  >\n                    {{ item.valueObj.label }}\n                  </router-link>\n                </span>\n                <span v-else>{{ item.valueObj.label }}</span>\n              </td>\n              <!-- state-badge template -->\n              <td\n                v-else-if=\"item.type === 'state-badge'\"\n                class=\"align-middle\"\n              >\n                <span>\n                  <BadgeState\n                    :color=\"`bg-${item.valueObj.stateColor}`\"\n                    :label=\"item.valueObj.stateLabel\"\n                    class=\"state-bagde\"\n                  />\n                </span>\n              </td>\n              <!-- single-error template -->\n              <td\n                v-if=\"item.type === 'single-error'\"\n                class=\"single-error\"\n                colspan=\"2\"\n              >\n                <p>{{ item.value }}</p>\n              </td>\n              <!-- default template -->\n              <td v-else>\n                {{ item.value }}\n              </td>\n            </tr>\n          </table>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\">\n.chart-container {\n  display: flex;\n  background-color: var(--body-bg);\n  position: relative;\n  border: 1px solid var(--border);\n  border-radius: var(--border-radius);\n  min-height: 100px;\n\n  .loading-container {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    border-radius: var(--border-radius);\n    background-color: var(--body-bg);\n    z-index: 2;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    flex-direction: column;\n\n    i {\n      font-size: 24px;\n    }\n  }\n\n  #tree {\n    width: 70%;\n    height: fit-content;\n\n    svg {\n      margin-top: 3px;\n    }\n\n    .link {\n      stroke: var(--darker);\n    }\n\n    .node {\n      cursor: pointer;\n\n      &.active {\n        .node-hover-layer {\n          display: block;\n        }\n      }\n\n      &.repo.active > circle {\n        transform: scale(1.2);\n      }\n\n      &.bundle.active > circle {\n        transform: scale(1.35);\n      }\n\n      &.bundle-deployment.active > circle {\n        transform: scale(1.6);\n      }\n\n      &.node-default-fill > circle,\n      &.repo > circle {\n        fill: var(--muted);\n      }\n      &:not(.repo).node-success > circle {\n        fill: var(--success);\n      }\n      &:not(.repo).node-info > circle {\n        fill: var(--info);\n      }\n      &:not(.repo).node-warning > circle {\n        fill: var(--warning);\n      }\n      &:not(.repo).node-error > circle {\n        fill: var(--error);\n      }\n\n      .node-hover-layer {\n        stroke: var(--body-bg);\n        stroke-width: 2;\n        display: none;\n      }\n    }\n  }\n\n  .more-info-container {\n    width: 30%;\n    position: relative;\n    border-left: 1px solid var(--border);\n    background-color: var(--body-bg);\n    border-top-right-radius: var(--border-radius);\n    border-bottom-right-radius: var(--border-radius);\n    overflow: hidden;\n\n    .more-info {\n      position: absolute;\n      top: 0;\n      left: 0;\n      right:0;\n      bottom:0;\n      width: 100%;\n      padding: 20px;\n      border-top-right-radius: var(--border-radius);\n      border-bottom-right-radius: var(--border-radius);\n      overflow-y: auto;\n\n      table {\n        td {\n          vertical-align: top;\n          padding-bottom: 10px;\n\n          &.align-middle {\n            vertical-align: middle;\n          }\n        }\n\n        .more-info-item-label {\n          color: var(--darker);\n          margin-right: 8px;\n        }\n\n        .single-error {\n          color: var(--error);\n        }\n\n        p {\n          line-height: 1.5em;\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}