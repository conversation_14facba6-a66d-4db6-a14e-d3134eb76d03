{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SortableTable/sorting.js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SortableTable/sorting.js", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}