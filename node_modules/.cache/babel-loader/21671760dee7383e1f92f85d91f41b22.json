{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??clonedRuleSet-44.use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/NodeGroup.vue?vue&type=template&id=151d0a74&scoped=true&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/NodeGroup.vue", "mtime": 1754992537319}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/NodeGroup.vue?vue&type=template&id=151d0a74&scoped=true&ts=true", "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/NodeGroup.vue"], "names": ["t", "nodegroupName", "mode", "poolIsUnprovisioned", "rules", "$emit", "displayNodeRole", "defaultNodeRoleOption", "ec2Roles", "loadingRoles", "desiredSize", "minSize", "maxSize", "minMaxDesiredErrors", "labels", "tags", "clusterWillUpgrade", "nodeCanUpgrade", "version", "willUpgrade", "originalNodeVersion", "clusterVersion", "<PERSON><PERSON><PERSON><PERSON>", "selectedLaunchTemplate", "launchTemplateOptions", "loadingLaunchTemplates", "hasUserLaunchTemplate", "launchTemplate", "launchTemplateVersionOptions", "imageId", "requestSpotInstances", "templateValue", "instanceTypeOptions", "loadingInstanceTypes", "instanceType", "diskSize", "loadingSelectedVersion", "gpu", "hasRancherLaunchTemplate", "spotInstanceTypes", "spotInstanceTypeOptions", "userData", "userDataPlaceholder", "loadingSshKeyPairs", "ec2SshKey", "sshKeyPairs", "resourceTags"], "mappings": "AAAA,OAAO,EAAE,eAAe,IAAI,gBAAgB,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,gBAAgB,IAAI,iBAAiB,EAAE,WAAW,IAAI,YAAY,EAAE,eAAe,IAAI,gBAAgB,EAAE,SAAS,IAAI,UAAU,EAAE,WAAW,IAAI,YAAY,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,OAAO,IAAI,QAAQ,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,MAAM,KAAK,CAAA;AAE9W,MAAM,UAAU,GAAG,EC+hBV,KAAK,EAAC,WAAW,EAAA,CAAA;AD9hB1B,MAAM,UAAU,GAAG,EC+hBR,KAAK,EAAC,YAAY,EAAA,CAAA;AD9hB7B,MAAM,UAAU,GAAG,EC2iBR,KAAK,EAAC,YAAY,EAAA,CAAA;AD1iB7B,MAAM,UAAU,GAAG,ECwjBV,KAAK,EAAC,WAAW,EAAA,CAAA;ADvjB1B,MAAM,UAAU,GAAG,ECwjBR,KAAK,EAAC,YAAY,EAAA,CAAA;ADvjB7B,MAAM,UAAU,GAAG,ECikBR,KAAK,EAAC,YAAY,EAAA,CAAA;ADhkB7B,MAAM,UAAU,GAAG,EC0kBR,KAAK,EAAC,YAAY,EAAA,CAAA;ADzkB7B,MAAM,UAAU,GAAG,ECylBV,KAAK,EAAC,WAAW,EAAA,CAAA;ADxlB1B,MAAM,UAAU,GAAG,ECylBR,KAAK,EAAC,kBAAkB,EAAA,CAAA;ADxlBnC,MAAM,WAAW,GAAG,ECwmBT,KAAK,EAAC,kBAAkB,EAAA,CAAA;ADvmBnC,MAAM,WAAW,GAAG,ECkoBX,KAAK,EAAC,WAAW,EAAA,CAAA;ADjoB1B,MAAM,WAAW,GAAG,ECkoBT,KAAK,EAAC,4BAA4B,EAAA,CAAA;ADjoB7C,MAAM,WAAW,GAAG,ECipBT,KAAK,EAAC,YAAY,EAAA,CAAA;ADhpB7B,MAAM,WAAW,GAAG,EC6pBT,KAAK,EAAC,YAAY,EAAA,CAAA;AD5pB7B,MAAM,WAAW,GAAG,EC4qBX,KAAK,EAAC,WAAW,EAAA,CAAA;AD3qB1B,MAAM,WAAW,GAAG,EC4qBT,KAAK,EAAC,YAAY,EAAA,CAAA;AD3qB7B,MAAM,WAAW,GAAG,ECqrBT,KAAK,EAAC,YAAY,EAAA,CAAA;ADprB7B,MAAM,WAAW,GAAG,ECosBT,KAAK,EAAC,YAAY,EAAA,CAAA;ADnsB7B,MAAM,WAAW,GAAG,ECwtBX,KAAK,EAAC,WAAW,EAAA,CAAA;ADvtB1B,MAAM,WAAW,GAAG,ECwtBT,KAAK,EAAC,YAAY,EAAA,CAAA;ADvtB7B,MAAM,WAAW,GAAG,ECkuBT,KAAK,EAAC,YAAY,EAAA,CAAA;ADjuB7B,MAAM,WAAW,GAAG;IAClB,GAAG,EAAE,CAAC;IC4uBF,KAAK,EAAC,WAAW;CD1uBtB,CAAA;AACD,MAAM,WAAW,GAAG,EC4uBZ,KAAK,EAAC,YAAY,EAAA,CAAA;AD3uB1B,MAAM,WAAW,GAAG,ECyvBX,KAAK,EAAC,WAAW,EAAA,CAAA;ADxvB1B,MAAM,WAAW,GAAG,ECyvBT,KAAK,EAAC,sBAAsB,EAAA,CAAA;ADxvBvC,MAAM,WAAW,GAAG,EC0wBT,KAAK,EAAC,YAAY,EAAA,CAAA;ADzwB7B,MAAM,WAAW,GAAG,ECwxBX,GAAG,EAAC,OAAO,EAAA,CAAA;ADvxBpB,MAAM,WAAW,GAAG,ECwxBT,KAAK,EAAC,mBAAmB,EAAA,CAAA;ADtxBpC,MAAM,UAAU,MAAM,CAAC,IAAS,EAAC,MAAW,EAAC,MAAW,EAAC,MAAW,EAAC,KAAU,EAAC,QAAa;IAC3F,MAAM,uBAAuB,GAAG,iBAAiB,CAAC,cAAc,CAAE,CAAA;IAClE,MAAM,wBAAwB,GAAG,iBAAiB,CAAC,eAAe,CAAE,CAAA;IACpE,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,QAAQ,CAAE,CAAA;IACtD,MAAM,mBAAmB,GAAG,iBAAiB,CAAC,UAAU,CAAE,CAAA;IAC1D,MAAM,mBAAmB,GAAG,iBAAiB,CAAC,UAAU,CAAE,CAAA;IAC1D,MAAM,oBAAoB,GAAG,iBAAiB,CAAC,WAAW,CAAE,CAAA;IAC5D,MAAM,uBAAuB,GAAG,iBAAiB,CAAC,cAAc,CAAE,CAAA;IAElE,OAAO,CAAC,UAAU,EAAE,ECofpB,mBAAA,CA2SM,KAAA,EAAA,IAAA,EAAA;QA1SJ,mBAAA,CAA+C,IAAA,EAAA,IAAA,EAAA,gBAAA,CAAxCA,IAAAA,CAAAA,CAAC,CAAA,6BAAA,CAAA,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;QDnfR,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;QCof/C,mBAAA,CA2BM,KAAA,EA3BN,UA2BM,EAAA;YA1BJ,mBAAA,CAWM,KAAA,EAXN,UAWM,EAAA;gBAVJ,YAAA,CASE,uBAAA,EAAA;oBARC,KAAK,EAAEC,IAAAA,CAAAA,aAAa;oBACrB,WAAS,EAAC,2BAA2B;oBACpC,IAAI,EAAEC,IAAAA,CAAAA,IAAI;oBACV,QAAQ,EAAA,CAAGC,IAAAA,CAAAA,mBAAmB;oBAC9B,KAAK,EAAEC,IAAAA,CAAAA,KAAK,CAAC,aAAa;oBAC3B,aAAW,EAAC,oBAAoB;oBAChC,QAAQ,EAAR,EAAQ;oBACP,gBAAY,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEC,IAAAA,CAAAA,KAAK,CAAA,sBAAA,EAAyB,MAAM,CAAA,CAAA,CAAA;iBDlfpD,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;aAChE,CAAC;YACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;YCof/C,mBAAA,CAYM,KAAA,EAZN,UAYM,EAAA;gBAXJ,YAAA,CAUE,wBAAA,EAAA;oBATQ,KAAK,EAAEC,IAAAA,CAAAA,eAAe;oBDlf9B,gBAAgB,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAW,EAAE,EAAE,CAAC,CAAC,CCkf/CA,IAAAA,CAAAA,eAAe,CAAA,GAAA,MAAA,CAAA,CAAA;oBAC7B,IAAI,EAAEJ,IAAAA,CAAAA,IAAI;oBACX,aAAW,EAAC,cAAc;oBAC1B,WAAS,EAAC,+BAA+B;oBACxC,OAAO,EAAA,CAAGK,IAAAA,CAAAA,qBAAqB,EAAA,GAAKC,IAAAA,CAAAA,QAAQ,CAAA;oBAC7C,cAAY,EAAC,UAAU;oBACvB,YAAU,EAAC,KAAK;oBACf,QAAQ,EAAA,CAAGL,IAAAA,CAAAA,mBAAmB;oBAC9B,OAAO,EAAEM,IAAAA,CAAAA,YAAY;iBDjfvB,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;aAC7E,CAAC;SACH,CAAC;QACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;QCkf/C,mBAAA,CA+BM,KAAA,EA/BN,UA+BM,EAAA;YA9BJ,mBAAA,CASM,KAAA,EATN,UASM,EAAA;gBARJ,YAAA,CAOE,uBAAA,EAAA;oBANA,IAAI,EAAC,QAAQ;oBACZ,KAAK,EAAEC,IAAAA,CAAAA,WAAW;oBACnB,WAAS,EAAC,kCAAkC;oBAC3C,IAAI,EAAER,IAAAA,CAAAA,IAAI;oBACV,KAAK,EAAEE,IAAAA,CAAAA,KAAK,CAAC,WAAW;oBACxB,gBAAY,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEC,IAAAA,CAAAA,KAAK,CAAA,oBAAA,EAAuB,QAAQ,CAAC,MAAM,CAAA,CAAA,CAAA,CAAA;iBDhf3D,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;aACpD,CAAC;YACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;YCif/C,mBAAA,CASM,KAAA,EATN,UASM,EAAA;gBARJ,YAAA,CAOE,uBAAA,EAAA;oBANA,IAAI,EAAC,QAAQ;oBACZ,KAAK,EAAEM,IAAAA,CAAAA,OAAO;oBACf,WAAS,EAAC,8BAA8B;oBACvC,IAAI,EAAET,IAAAA,CAAAA,IAAI;oBACV,KAAK,EAAEE,IAAAA,CAAAA,KAAK,CAAC,OAAO;oBACpB,gBAAY,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEC,IAAAA,CAAAA,KAAK,CAAA,gBAAA,EAAmB,QAAQ,CAAC,MAAM,CAAA,CAAA,CAAA,CAAA;iBD/evD,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;aACpD,CAAC;YACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;YCgf/C,mBAAA,CASM,KAAA,EATN,UASM,EAAA;gBARJ,YAAA,CAOE,uBAAA,EAAA;oBANA,IAAI,EAAC,QAAQ;oBACZ,KAAK,EAAEO,IAAAA,CAAAA,OAAO;oBACf,WAAS,EAAC,8BAA8B;oBACvC,IAAI,EAAEV,IAAAA,CAAAA,IAAI;oBACV,KAAK,EAAEE,IAAAA,CAAAA,KAAK,CAAC,OAAO;oBACpB,gBAAY,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEC,IAAAA,CAAAA,KAAK,CAAA,gBAAA,EAAmB,QAAQ,CAAC,MAAM,CAAA,CAAA,CAAA,CAAA;iBD9evD,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;aACpD,CAAC;SACH,CAAC;QACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;QAC/C,CAAC,CAAC,CC+eQQ,IAAAA,CAAAA,mBAAmB,CAAA;YD9e3B,CAAC,CAAC,CAAC,UAAU,EAAE,EC6ejB,YAAA,CAIE,iBAAA,EAAA;gBDhfI,GAAG,EAAE,CAAC;gBC8eV,KAAK,EAAC,OAAO;gBACZ,KAAK,EAAEA,IAAAA,CAAAA,mBAAmB;aD5exB,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;YACrC,CAAC,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;QACrC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;QC4e/C,mBAAA,CAgCM,KAAA,EAhCN,UAgCM,EAAA;YA/BJ,mBAAA,CAeM,KAAA,EAfN,UAeM,EAAA;gBAdJ,YAAA,CAaW,mBAAA,EAAA;oBAZR,IAAI,EAAEX,IAAAA,CAAAA,IAAI;oBACV,KAAK,EAAEF,IAAAA,CAAAA,CAAC,CAAA,kCAAA,CAAA;oBACR,cAAY,EAAE,KAAK;oBACnB,KAAK,EAAEc,IAAAA,CAAAA,MAAM;oBACb,QAAM,EAAE,IAAI;oBACZ,gBAAY,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAET,IAAAA,CAAAA,KAAK,CAAA,eAAA,EAAkB,MAAM,CAAA,CAAA,CAAA;iBD1e7C,EAAE;oBC4eU,KAAK,EAAA,QAAA,CACd,GAEK,EAAA,CAAA;wBAFL,mBAAA,CAEK,IAAA,EAAA,IAAA,EAAA,gBAAA,CADAL,IAAAA,CAAAA,CAAC,CAAA,kCAAA,CAAA,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;qBD3eP,CAAC;oBACF,CAAC,EAAE,CAAC,CAAC,YAAY;iBAClB,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;aAC9C,CAAC;YACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;YC4e/C,mBAAA,CAcM,KAAA,EAdN,WAcM,EAAA;gBAbJ,YAAA,CAYW,mBAAA,EAAA;oBAXR,IAAI,EAAEE,IAAAA,CAAAA,IAAI;oBACV,KAAK,EAAEF,IAAAA,CAAAA,CAAC,CAAA,gCAAA,CAAA;oBACR,cAAY,EAAE,KAAK;oBACnB,QAAM,EAAE,IAAI;oBACZ,KAAK,EAAEe,IAAAA,CAAAA,IAAI;oBACZ,aAAW,EAAC,yBAAyB;oBACpC,gBAAY,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEV,IAAAA,CAAAA,KAAK,CAAA,aAAA,EAAgB,MAAM,CAAA,CAAA,CAAA;iBD1e3C,EAAE;oBC4eU,KAAK,EAAA,QAAA,CACd,GAAkD,EAAA,CAAA;wBAAlD,mBAAA,CAAkD,IAAA,EAAA,IAAA,EAAA,gBAAA,CAA3CL,IAAAA,CAAAA,CAAC,CAAA,gCAAA,CAAA,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;qBD1eT,CAAC;oBACF,CAAC,EAAE,CAAC,CAAC,YAAY;iBAClB,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;aAC9C,CAAC;SACH,CAAC;QACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;QAC/C,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GCyezB,mBAAA,CAGC,IAAA,EAAA;YAFC,KAAK,EAAC,OAAO;YACb,IAAI,EAAC,MAAM;SDxeZ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;QAC1B,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;QCye/C,mBAAA,CAAkD,IAAA,EAAA,IAAA,EAAA,gBAAA,CAA3CA,IAAAA,CAAAA,CAAC,CAAA,gCAAA,CAAA,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;QDveR,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;QAC/C,CCweQgB,IAAAA,CAAAA,kBAAkB,IAAA,CAAKb,IAAAA,CAAAA,mBAAmB,CAAA;YDvehD,CAAC,CAAC,CAAC,UAAU,EAAE,ECsejB,YAAA,CAKE,iBAAA,EAAA;gBD1eI,GAAG,EAAE,CAAC;gBCueV,KAAK,EAAC,MAAM;gBACZ,WAAS,EAAC,qDAAqD;gBAC/D,aAAW,EAAC,4BAA4B;aDrerC,CAAC,CAAC;YACL,CAAC,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;QACrC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;QCqe/C,mBAAA,CAyCM,KAAA,EAzCN,WAyCM,EAAA;YAxCJ,mBAAA,CAeM,KAAA,EAfN,WAeM,EAAA;gBDlfJ,CAAC,CCqeQc,IAAAA,CAAAA,cAAc,CAAA;oBDperB,CAAC,CAAC,CAAC,UAAU,EAAE,ECmejB,YAAA,CAME,uBAAA,EAAA;wBDxeI,GAAG,EAAE,CAAC;wBCoeV,WAAS,EAAC,wCAAwC;wBACjD,QAAQ,EAAE,IAAI;wBACd,KAAK,EAAEC,IAAAA,CAAAA,OAAO;wBACf,aAAW,EAAC,qBAAqB;qBDle9B,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;oBACrC,CAAC,CAAC,CAAC,UAAU,EAAE,ECmejB,YAAA,CAME,mBAAA,EAAA;wBDxeI,GAAG,EAAE,CAAC;wBCoeF,KAAK,EAAEC,IAAAA,CAAAA,WAAW;wBDletB,gBAAgB,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAW,EAAE,EAAE,CAAC,CAAC,CCkenDA,IAAAA,CAAAA,WAAW,CAAA,GAAA,MAAA,CAAA,CAAA;wBACzB,KAAK,EAAEnB,IAAAA,CAAAA,CAAC,CAAA,0CAAA,EAAA,EAAA,IAAA,EAAoDoB,IAAAA,CAAAA,mBAAmB,EAAA,EAAA,EAAMC,IAAAA,CAAAA,cAAc,EAAA,CAAA;wBACpG,aAAW,EAAC,8BAA8B;wBACzC,QAAQ,EAAEC,IAAAA,CAAAA,MAAM;qBDjed,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC;aAC7D,CAAC;YACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;YCke/C,mBAAA,CAYM,KAAA,EAZN,WAYM,EAAA;gBAXJ,YAAA,CAUE,wBAAA,EAAA;oBATQ,KAAK,EAAEC,IAAAA,CAAAA,sBAAsB;oBDherC,gBAAgB,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAW,EAAE,EAAE,CAAC,CAAC,CCge/CA,IAAAA,CAAAA,sBAAsB,CAAA,GAAA,MAAA,CAAA,CAAA;oBACpC,IAAI,EAAErB,IAAAA,CAAAA,IAAI;oBACX,WAAS,EAAC,qCAAqC;oBAC9C,OAAO,EAAEsB,IAAAA,CAAAA,qBAAqB;oBAC/B,cAAY,EAAC,oBAAoB;oBACjC,YAAU,EAAC,kBAAkB;oBAC5B,QAAQ,EAAA,CAAGrB,IAAAA,CAAAA,mBAAmB;oBAC9B,OAAO,EAAEsB,IAAAA,CAAAA,sBAAsB;oBAChC,aAAW,EAAC,8BAA8B;iBD/d3C,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;aAC7E,CAAC;YACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;YCge/C,mBAAA,CAUM,KAAA,EAVN,WAUM,EAAA;gBDxeJ,CCgeQC,IAAAA,CAAAA,qBAAqB,CAAA;oBD/d3B,CAAC,CAAC,CAAC,UAAU,EAAE,EC8djB,YAAA,CAQE,wBAAA,EAAA;wBDreI,GAAG,EAAE,CAAC;wBC+dT,KAAK,EAAEC,IAAAA,CAAAA,cAAc,CAAC,OAAO;wBAC7B,IAAI,EAAEzB,IAAAA,CAAAA,IAAI;wBACX,WAAS,EAAC,uCAAuC;wBAChD,OAAO,EAAE0B,IAAAA,CAAAA,4BAA4B;wBACtC,aAAW,EAAC,sCAAsC;wBACjD,gBAAY,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEvB,IAAAA,CAAAA,KAAK,CAAA,uBAAA,EAAA,EAAA,GAA8BsB,IAAAA,CAAAA,cAAc,EAAA,OAAA,EAAW,MAAM,EAAA,CAAA,CAAA,CAAA;qBD7d9E,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;oBACxD,CAAC,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;aACtC,CAAC;SACH,CAAC;QACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;QC6d/C,YAAA,CAGE,iBAAA,EAAA;YAFA,KAAK,EAAC,MAAM;YACZ,WAAS,EAAC,gCAAgC;SD3d3C,CAAC;QACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;QC4d/C,mBAAA,CAyCM,KAAA,EAzCN,WAyCM,EAAA;YAxCJ,mBAAA,CASM,KAAA,EATN,WASM,EAAA;gBARJ,YAAA,CAOE,uBAAA,EAAA;oBANA,WAAS,EAAC,8BAA8B;oBACvC,IAAI,EAAEzB,IAAAA,CAAAA,IAAI;oBACV,KAAK,EAAE2B,IAAAA,CAAAA,OAAO;oBACd,QAAQ,EAAEH,IAAAA,CAAAA,qBAAqB;oBAChC,aAAW,EAAC,oBAAoB;oBAC/B,gBAAY,EAAA,MAAA,CAAA,EAAA,CAAA,IAAA,CAAA,MAAA,CAAA,EAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAErB,IAAAA,CAAAA,KAAK,CAAA,gBAAA,EAAmB,MAAM,CAAA,CAAA,CAAA;iBD1d9C,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;aACvD,CAAC;YACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;YC2d/C,mBAAA,CAcM,KAAA,EAdN,WAcM,EAAA;gBAbJ,YAAA,CAYE,wBAAA,EAAA;oBAXC,QAAQ,EAAA,CAAGyB,IAAAA,CAAAA,oBAAoB,IAAA,CAAKC,IAAAA,CAAAA,aAAa,CAAA,cAAA,CAAA;oBACjD,IAAI,EAAE7B,IAAAA,CAAAA,IAAI;oBACX,WAAS,EAAC,mCAAmC;oBAC5C,OAAO,EAAE8B,IAAAA,CAAAA,mBAAmB;oBAC5B,OAAO,EAAEC,IAAAA,CAAAA,oBAAoB;oBAC7B,KAAK,EAAEC,IAAAA,CAAAA,YAAY;oBACnB,QAAQ,EAAA,CAAA,CAAIH,IAAAA,CAAAA,aAAa,CAAA,cAAA,CAAA,IAAoBD,IAAAA,CAAAA,oBAAoB;oBACjE,OAAO,EAAA,CAAGA,IAAAA,CAAAA,oBAAoB,IAAA,CAAKC,IAAAA,CAAAA,aAAa,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA,CAAoB/B,IAAAA,CAAAA,CAAC,CAAA,qCAAA,CAAA,CAAA,CAAA,CAAA,EAAA;oBACrE,KAAK,EAAA,CAAG8B,IAAAA,CAAAA,oBAAoB,CAAA,CAAA,CAAG1B,IAAAA,CAAAA,KAAK,CAAC,YAAY,CAAA,CAAA,CAAA,EAAA;oBAClD,aAAW,EAAC,4BAA4B;oBACvC,gBAAY,EAAA,MAAA,CAAA,EAAA,CAAA,IAAA,CAAA,MAAA,CAAA,EAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEC,IAAAA,CAAAA,KAAK,CAAA,qBAAA,EAAwB,MAAM,CAAA,CAAA,CAAA;iBDzdnD,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;aAC7G,CAAC;YACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;YC2d/C,mBAAA,CAaM,KAAA,EAbN,WAaM,EAAA;gBAZJ,YAAA,CAWE,oBAAA,EAAA;oBAVC,QAAQ,EAAA,CAAG0B,IAAAA,CAAAA,aAAa,CAAA,UAAA,CAAA;oBACzB,WAAS,EAAC,+BAA+B;oBACxC,IAAI,EAAE7B,IAAAA,CAAAA,IAAI;oBACV,KAAK,EAAEiC,IAAAA,CAAAA,QAAQ;oBAChB,MAAM,EAAC,IAAI;oBACV,OAAO,EAAEC,IAAAA,CAAAA,sBAAsB;oBAC/B,QAAQ,EAAA,CAAA,CAAIL,IAAAA,CAAAA,aAAa,CAAA,UAAA,CAAA,IAAgBK,IAAAA,CAAAA,sBAAsB;oBAC/D,KAAK,EAAEhC,IAAAA,CAAAA,KAAK,CAAC,QAAQ;oBACtB,aAAW,EAAC,oBAAoB;oBAC/B,gBAAY,EAAA,MAAA,CAAA,EAAA,CAAA,IAAA,CAAA,MAAA,CAAA,EAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEC,IAAAA,CAAAA,KAAK,CAAA,iBAAA,EAAoB,MAAM,CAAA,CAAA,CAAA;iBDzd/C,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;aACvF,CAAC;SACH,CAAC;QACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;QAC/C,CC0dQyB,IAAAA,CAAAA,oBAAoB,IAAIJ,IAAAA,CAAAA,qBAAqB,CAAA;YDzdnD,CAAC,CAAC,CAAC,UAAU,EAAE,ECwdjB,YAAA,CAKE,iBAAA,EAAA;gBD5dI,GAAG,EAAE,CAAC;gBCydV,KAAK,EAAC,SAAS;gBACd,KAAK,EAAE1B,IAAAA,CAAAA,CAAC,CAAA,6CAAA,CAAA;gBACT,aAAW,EAAC,0BAA0B;aDvdnC,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;YACrC,CAAC,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;QACrC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;QCud/C,mBAAA,CAqBM,KAAA,EArBN,WAqBM,EAAA;YApBJ,mBAAA,CAUM,KAAA,EAVN,WAUM,EAAA;gBATJ,YAAA,CAQE,mBAAA,EAAA;oBAPC,IAAI,EAAEE,IAAAA,CAAAA,IAAI;oBACX,WAAS,EAAC,0BAA0B;oBACnC,KAAK,EAAEmC,IAAAA,CAAAA,GAAG;oBACV,QAAQ,EAAA,CAAA,CAAIN,IAAAA,CAAAA,aAAa,CAAA,SAAA,CAAA,IAAeO,IAAAA,CAAAA,wBAAwB;oBAChE,OAAO,EAAEP,IAAAA,CAAAA,aAAa,CAAA,SAAA,CAAA,CAAA,CAAA,CAAc/B,IAAAA,CAAAA,CAAC,CAAA,4BAAA,CAAA,CAAA,CAAA,CAAA,EAAA;oBACtC,aAAW,EAAC,eAAe;oBAC1B,gBAAY,EAAA,MAAA,CAAA,EAAA,CAAA,IAAA,CAAA,MAAA,CAAA,EAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEK,IAAAA,CAAAA,KAAK,CAAA,YAAA,EAAe,MAAM,CAAA,CAAA,CAAA;iBDrd1C,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;aAClE,CAAC;YACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;YCsd/C,mBAAA,CAQM,KAAA,EARN,WAQM,EAAA;gBAPJ,YAAA,CAME,mBAAA,EAAA;oBALC,KAAK,EAAEyB,IAAAA,CAAAA,oBAAoB;oBAC3B,IAAI,EAAE5B,IAAAA,CAAAA,IAAI;oBACX,WAAS,EAAC,2CAA2C;oBACpD,QAAQ,EAAEoC,IAAAA,CAAAA,wBAAwB;oBAClC,gBAAY,EAAA,MAAA,CAAA,EAAA,CAAA,IAAA,CAAA,MAAA,CAAA,EAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEjC,IAAAA,CAAAA,KAAK,CAAA,6BAAA,EAAgC,MAAM,CAAA,CAAA,CAAA;iBDpd3D,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;aACvD,CAAC;SACH,CAAC;QACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;QAC/C,CCqdQyB,IAAAA,CAAAA,oBAAoB,IAAA,CAAKC,IAAAA,CAAAA,aAAa,CAAA,cAAA,CAAA,CAAA;YDpd5C,CAAC,CAAC,CAAC,UAAU,EAAE,ECmdjB,mBAAA,CAkBM,KAAA,EAlBN,WAkBM,EAAA;gBAdJ,mBAAA,CAaM,KAAA,EAbN,WAaM,EAAA;oBAVJ,YAAA,CASE,wBAAA,EAAA;wBARC,IAAI,EAAE7B,IAAAA,CAAAA,IAAI;wBACV,KAAK,EAAEqC,IAAAA,CAAAA,iBAAiB;wBACzB,WAAS,EAAC,wCAAwC;wBACjD,OAAO,EAAEC,IAAAA,CAAAA,uBAAuB;wBAChC,QAAQ,EAAE,IAAI;wBACd,OAAO,EAAEJ,IAAAA,CAAAA,sBAAsB,IAAIH,IAAAA,CAAAA,oBAAoB;wBACxD,aAAW,EAAC,iCAAiC;wBAC5C,gBAAY,EAAA,MAAA,CAAA,EAAA,CAAA,IAAA,CAAA,MAAA,CAAA,EAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAE5B,IAAAA,CAAAA,KAAK,CAAA,0BAAA,EAA6B,MAAM,CAAA,CAAA,CAAA;qBDvdpD,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;iBACjE,CAAC;aACH,CAAC,CAAC;YACL,CAAC,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;QACrC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;QCud/C,mBAAA,CAiCM,KAAA,EAjCN,WAiCM,EAAA;YAhCJ,mBAAA,CAiBM,KAAA,EAjBN,WAiBM,EAAA;gBAhBJ,YAAA,CASE,uBAAA,EAAA;oBARA,WAAS,EAAC,+BAA+B;oBACxC,IAAI,EAAEH,IAAAA,CAAAA,IAAI;oBACX,IAAI,EAAC,WAAW;oBACf,KAAK,EAAEuC,IAAAA,CAAAA,QAAQ;oBACf,QAAQ,EAAEf,IAAAA,CAAAA,qBAAqB;oBAC/B,WAAW,EAAEgB,IAAAA,CAAAA,mBAAmB;oBAChC,WAAS,EAAE1C,IAAAA,CAAAA,CAAC,CAAA,iCAAA,EAAA,EAAA,EAAA,IAAA,CAAA;oBACZ,gBAAY,EAAA,MAAA,CAAA,EAAA,CAAA,IAAA,CAAA,MAAA,CAAA,EAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEK,IAAAA,CAAAA,KAAK,CAAA,iBAAA,EAAoB,MAAM,CAAA,CAAA,CAAA;iBDrd/C,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;gBAClF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;gBCsd/C,YAAA,CAKE,uBAAA,EAAA;oBAJC,IAAI,EAAEH,IAAAA,CAAAA,IAAI;oBACV,KAAK,EAAEF,IAAAA,CAAAA,CAAC,CAAA,sBAAA,CAAA;oBACT,KAAK,EAAC,qBAAqB;oBAC1B,UAAQ,EAAA,MAAA,CAAA,EAAA,CAAA,IAAA,CAAA,MAAA,CAAA,EAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEK,IAAAA,CAAAA,KAAK,CAAA,iBAAA,EAAoB,MAAM,CAAA,CAAA,CAAA;iBDpd3C,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;aAC3C,CAAC;YACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;YCqd/C,mBAAA,CAaM,KAAA,EAbN,WAaM,EAAA;gBAZJ,YAAA,CAWE,wBAAA,EAAA;oBAVC,OAAO,EAAEsC,IAAAA,CAAAA,kBAAkB;oBAC3B,KAAK,EAAEC,IAAAA,CAAAA,SAAS;oBAChB,OAAO,EAAEC,IAAAA,CAAAA,WAAW;oBACrB,WAAS,EAAC,gCAAgC;oBACzC,IAAI,EAAE3C,IAAAA,CAAAA,IAAI;oBACV,QAAQ,EAAEwB,IAAAA,CAAAA,qBAAqB;oBAC/B,QAAQ,EAAE,IAAI;oBACd,UAAU,EAAE,IAAI;oBACjB,aAAW,EAAC,8BAA8B;oBACzC,gBAAY,EAAA,MAAA,CAAA,EAAA,CAAA,IAAA,CAAA,MAAA,CAAA,EAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAErB,IAAAA,CAAAA,KAAK,CAAA,kBAAA,EAAqB,MAAM,CAAA,CAAA,CAAA;iBDndhD,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;aAC7E,CAAC;SACH,CAAC;QACF,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC;QCod/C,mBAAA,CAkBM,KAAA,EAlBN,WAkBM,EAAA;YAjBJ,mBAAA,CAgBM,KAAA,EAhBN,WAgBM,EAAA;gBAfJ,YAAA,CAcW,mBAAA,EAAA;oBAbR,IAAI,EAAEH,IAAAA,CAAAA,IAAI;oBACX,WAAS,EAAC,mCAAmC;oBAC5C,KAAK,EAAE4C,IAAAA,CAAAA,YAAY;oBACnB,QAAQ,EAAEpB,IAAAA,CAAAA,qBAAqB;oBAC/B,cAAY,EAAE,KAAK;oBACnB,QAAM,EAAE,IAAI;oBACZ,gBAAY,EAAA,MAAA,CAAA,EAAA,CAAA,IAAA,CAAA,MAAA,CAAA,EAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAErB,IAAAA,CAAAA,KAAK,CAAA,qBAAA,EAAwB,MAAM,CAAA,CAAA,CAAA;iBDldnD,EAAE;oBCodU,KAAK,EAAA,QAAA,CACd,GAEK,EAAA,CAAA;wBAFL,mBAAA,CAEK,IAAA,EAAA,IAAA,EAAA,gBAAA,CADAL,IAAAA,CAAAA,CAAC,CAAA,mCAAA,CAAA,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;qBDndP,CAAC;oBACF,CAAC,EAAE,CAAC,CAAC,YAAY;iBAClB,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;aACjD,CAAC;SACH,CAAC;KACH,CAAC,CAAC,CAAA;AACL,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/NodeGroup.vue.tsx", "sourceRoot": "", "sourcesContent": ["import { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createTextVNode as _createTextVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, withCtx as _withCtx, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"row mb-10\" }\nconst _hoisted_2 = { class: \"col span-6\" }\nconst _hoisted_3 = { class: \"col span-6\" }\nconst _hoisted_4 = { class: \"row mb-10\" }\nconst _hoisted_5 = { class: \"col span-4\" }\nconst _hoisted_6 = { class: \"col span-4\" }\nconst _hoisted_7 = { class: \"col span-4\" }\nconst _hoisted_8 = { class: \"row mb-10\" }\nconst _hoisted_9 = { class: \"col span-6 mt-20\" }\nconst _hoisted_10 = { class: \"col span-6 mt-20\" }\nconst _hoisted_11 = { class: \"row mb-10\" }\nconst _hoisted_12 = { class: \"col span-4 upgrade-version\" }\nconst _hoisted_13 = { class: \"col span-4\" }\nconst _hoisted_14 = { class: \"col span-4\" }\nconst _hoisted_15 = { class: \"row mb-10\" }\nconst _hoisted_16 = { class: \"col span-4\" }\nconst _hoisted_17 = { class: \"col span-4\" }\nconst _hoisted_18 = { class: \"col span-4\" }\nconst _hoisted_19 = { class: \"row mb-10\" }\nconst _hoisted_20 = { class: \"col span-4\" }\nconst _hoisted_21 = { class: \"col span-4\" }\nconst _hoisted_22 = {\n  key: 3,\n  class: \"row mb-10\"\n}\nconst _hoisted_23 = { class: \"col span-6\" }\nconst _hoisted_24 = { class: \"row mb-15\" }\nconst _hoisted_25 = { class: \"col span-6 user-data\" }\nconst _hoisted_26 = { class: \"col span-6\" }\nconst _hoisted_27 = { row: \"mb-10\" }\nconst _hoisted_28 = { class: \"col span-12 mt-20\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_LabeledInput = _resolveComponent(\"LabeledInput\")!\n  const _component_LabeledSelect = _resolveComponent(\"LabeledSelect\")!\n  const _component_Banner = _resolveComponent(\"Banner\")!\n  const _component_KeyValue = _resolveComponent(\"KeyValue\")!\n  const _component_Checkbox = _resolveComponent(\"Checkbox\")!\n  const _component_UnitInput = _resolveComponent(\"UnitInput\")!\n  const _component_FileSelector = _resolveComponent(\"FileSelector\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", null, [\n    _createElementVNode(\"h3\", null, _toDisplayString(_ctx.t('eks.nodeGroups.groupDetails')), 1 /* TEXT */),\n    _cache[31] || (_cache[31] = _createTextVNode()),\n    _createElementVNode(\"div\", _hoisted_1, [\n      _createElementVNode(\"div\", _hoisted_2, [\n        _createVNode(_component_LabeledInput, {\n          value: _ctx.nodegroupName,\n          \"label-key\": \"eks.nodeGroups.name.label\",\n          mode: _ctx.mode,\n          disabled: !_ctx.poolIsUnprovisioned,\n          rules: _ctx.rules.nodegroupName,\n          \"data-testid\": \"eks-nodegroup-name\",\n          required: \"\",\n          \"onUpdate:value\": _cache[0] || (_cache[0] = ($event: any) => (_ctx.$emit('update:nodegroupName', $event)))\n        }, null, 8 /* PROPS */, [\"value\", \"mode\", \"disabled\", \"rules\"])\n      ]),\n      _cache[20] || (_cache[20] = _createTextVNode()),\n      _createElementVNode(\"div\", _hoisted_3, [\n        _createVNode(_component_LabeledSelect, {\n          value: _ctx.displayNodeRole,\n          \"onUpdate:value\": _cache[1] || (_cache[1] = ($event: any) => ((_ctx.displayNodeRole) = $event)),\n          mode: _ctx.mode,\n          \"data-testid\": \"eks-noderole\",\n          \"label-key\": \"eks.nodeGroups.nodeRole.label\",\n          options: [_ctx.defaultNodeRoleOption, ..._ctx.ec2Roles],\n          \"option-label\": \"RoleName\",\n          \"option-key\": \"Arn\",\n          disabled: !_ctx.poolIsUnprovisioned,\n          loading: _ctx.loadingRoles\n        }, null, 8 /* PROPS */, [\"value\", \"mode\", \"options\", \"disabled\", \"loading\"])\n      ])\n    ]),\n    _cache[32] || (_cache[32] = _createTextVNode()),\n    _createElementVNode(\"div\", _hoisted_4, [\n      _createElementVNode(\"div\", _hoisted_5, [\n        _createVNode(_component_LabeledInput, {\n          type: \"number\",\n          value: _ctx.desiredSize,\n          \"label-key\": \"eks.nodeGroups.desiredSize.label\",\n          mode: _ctx.mode,\n          rules: _ctx.rules.desiredSize,\n          \"onUpdate:value\": _cache[2] || (_cache[2] = ($event: any) => (_ctx.$emit('update:desiredSize', parseInt($event))))\n        }, null, 8 /* PROPS */, [\"value\", \"mode\", \"rules\"])\n      ]),\n      _cache[21] || (_cache[21] = _createTextVNode()),\n      _createElementVNode(\"div\", _hoisted_6, [\n        _createVNode(_component_LabeledInput, {\n          type: \"number\",\n          value: _ctx.minSize,\n          \"label-key\": \"eks.nodeGroups.minSize.label\",\n          mode: _ctx.mode,\n          rules: _ctx.rules.minSize,\n          \"onUpdate:value\": _cache[3] || (_cache[3] = ($event: any) => (_ctx.$emit('update:minSize', parseInt($event))))\n        }, null, 8 /* PROPS */, [\"value\", \"mode\", \"rules\"])\n      ]),\n      _cache[22] || (_cache[22] = _createTextVNode()),\n      _createElementVNode(\"div\", _hoisted_7, [\n        _createVNode(_component_LabeledInput, {\n          type: \"number\",\n          value: _ctx.maxSize,\n          \"label-key\": \"eks.nodeGroups.maxSize.label\",\n          mode: _ctx.mode,\n          rules: _ctx.rules.maxSize,\n          \"onUpdate:value\": _cache[4] || (_cache[4] = ($event: any) => (_ctx.$emit('update:maxSize', parseInt($event))))\n        }, null, 8 /* PROPS */, [\"value\", \"mode\", \"rules\"])\n      ])\n    ]),\n    _cache[33] || (_cache[33] = _createTextVNode()),\n    (!!_ctx.minMaxDesiredErrors)\n      ? (_openBlock(), _createBlock(_component_Banner, {\n          key: 0,\n          color: \"error\",\n          label: _ctx.minMaxDesiredErrors\n        }, null, 8 /* PROPS */, [\"label\"]))\n      : _createCommentVNode(\"v-if\", true),\n    _cache[34] || (_cache[34] = _createTextVNode()),\n    _createElementVNode(\"div\", _hoisted_8, [\n      _createElementVNode(\"div\", _hoisted_9, [\n        _createVNode(_component_KeyValue, {\n          mode: _ctx.mode,\n          title: _ctx.t('eks.nodeGroups.groupLabels.label'),\n          \"read-allowed\": false,\n          value: _ctx.labels,\n          \"as-map\": true,\n          \"onUpdate:value\": _cache[5] || (_cache[5] = ($event: any) => (_ctx.$emit('update:labels', $event)))\n        }, {\n          title: _withCtx(() => [\n            _createElementVNode(\"h4\", null, _toDisplayString(_ctx.t('eks.nodeGroups.groupLabels.label')), 1 /* TEXT */)\n          ]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"mode\", \"title\", \"value\"])\n      ]),\n      _cache[23] || (_cache[23] = _createTextVNode()),\n      _createElementVNode(\"div\", _hoisted_10, [\n        _createVNode(_component_KeyValue, {\n          mode: _ctx.mode,\n          title: _ctx.t('eks.nodeGroups.groupTags.label'),\n          \"read-allowed\": false,\n          \"as-map\": true,\n          value: _ctx.tags,\n          \"data-testid\": \"eks-resource-tags-input\",\n          \"onUpdate:value\": _cache[6] || (_cache[6] = ($event: any) => (_ctx.$emit('update:tags', $event)))\n        }, {\n          title: _withCtx(() => [\n            _createElementVNode(\"h4\", null, _toDisplayString(_ctx.t('eks.nodeGroups.groupTags.label')), 1 /* TEXT */)\n          ]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"mode\", \"title\", \"value\"])\n      ])\n    ]),\n    _cache[35] || (_cache[35] = _createTextVNode()),\n    _cache[36] || (_cache[36] = _createElementVNode(\"hr\", {\n      class: \"mb-20\",\n      role: \"none\"\n    }, null, -1 /* CACHED */)),\n    _cache[37] || (_cache[37] = _createTextVNode()),\n    _createElementVNode(\"h3\", null, _toDisplayString(_ctx.t('eks.nodeGroups.templateDetails')), 1 /* TEXT */),\n    _cache[38] || (_cache[38] = _createTextVNode()),\n    (_ctx.clusterWillUpgrade && !_ctx.poolIsUnprovisioned)\n      ? (_openBlock(), _createBlock(_component_Banner, {\n          key: 1,\n          color: \"info\",\n          \"label-key\": \"eks.nodeGroups.kubernetesVersion.clusterWillUpgrade\",\n          \"data-testid\": \"eks-version-upgrade-banner\"\n        }))\n      : _createCommentVNode(\"v-if\", true),\n    _cache[39] || (_cache[39] = _createTextVNode()),\n    _createElementVNode(\"div\", _hoisted_11, [\n      _createElementVNode(\"div\", _hoisted_12, [\n        (!_ctx.nodeCanUpgrade)\n          ? (_openBlock(), _createBlock(_component_LabeledInput, {\n              key: 0,\n              \"label-key\": \"eks.nodeGroups.kubernetesVersion.label\",\n              disabled: true,\n              value: _ctx.version,\n              \"data-testid\": \"eks-version-display\"\n            }, null, 8 /* PROPS */, [\"value\"]))\n          : (_openBlock(), _createBlock(_component_Checkbox, {\n              key: 1,\n              value: _ctx.willUpgrade,\n              \"onUpdate:value\": _cache[7] || (_cache[7] = ($event: any) => ((_ctx.willUpgrade) = $event)),\n              label: _ctx.t('eks.nodeGroups.kubernetesVersion.upgrade', {from: _ctx.originalNodeVersion, to: _ctx.clusterVersion}),\n              \"data-testid\": \"eks-version-upgrade-checkbox\",\n              disabled: _ctx.isView\n            }, null, 8 /* PROPS */, [\"value\", \"label\", \"disabled\"]))\n      ]),\n      _cache[24] || (_cache[24] = _createTextVNode()),\n      _createElementVNode(\"div\", _hoisted_13, [\n        _createVNode(_component_LabeledSelect, {\n          value: _ctx.selectedLaunchTemplate,\n          \"onUpdate:value\": _cache[8] || (_cache[8] = ($event: any) => ((_ctx.selectedLaunchTemplate) = $event)),\n          mode: _ctx.mode,\n          \"label-key\": \"eks.nodeGroups.launchTemplate.label\",\n          options: _ctx.launchTemplateOptions,\n          \"option-label\": \"LaunchTemplateName\",\n          \"option-key\": \"LaunchTemplateId\",\n          disabled: !_ctx.poolIsUnprovisioned,\n          loading: _ctx.loadingLaunchTemplates,\n          \"data-testid\": \"eks-launch-template-dropdown\"\n        }, null, 8 /* PROPS */, [\"value\", \"mode\", \"options\", \"disabled\", \"loading\"])\n      ]),\n      _cache[25] || (_cache[25] = _createTextVNode()),\n      _createElementVNode(\"div\", _hoisted_14, [\n        (_ctx.hasUserLaunchTemplate)\n          ? (_openBlock(), _createBlock(_component_LabeledSelect, {\n              key: 0,\n              value: _ctx.launchTemplate.version,\n              mode: _ctx.mode,\n              \"label-key\": \"eks.nodeGroups.launchTemplate.version\",\n              options: _ctx.launchTemplateVersionOptions,\n              \"data-testid\": \"eks-launch-template-version-dropdown\",\n              \"onUpdate:value\": _cache[9] || (_cache[9] = ($event: any) => (_ctx.$emit('update:launchTemplate', {..._ctx.launchTemplate, version: $event})))\n            }, null, 8 /* PROPS */, [\"value\", \"mode\", \"options\"]))\n          : _createCommentVNode(\"v-if\", true)\n      ])\n    ]),\n    _cache[40] || (_cache[40] = _createTextVNode()),\n    _createVNode(_component_Banner, {\n      color: \"info\",\n      \"label-key\": \"eks.nodeGroups.imageId.tooltip\"\n    }),\n    _cache[41] || (_cache[41] = _createTextVNode()),\n    _createElementVNode(\"div\", _hoisted_15, [\n      _createElementVNode(\"div\", _hoisted_16, [\n        _createVNode(_component_LabeledInput, {\n          \"label-key\": \"eks.nodeGroups.imageId.label\",\n          mode: _ctx.mode,\n          value: _ctx.imageId,\n          disabled: _ctx.hasUserLaunchTemplate,\n          \"data-testid\": \"eks-image-id-input\",\n          \"onUpdate:value\": _cache[10] || (_cache[10] = ($event: any) => (_ctx.$emit('update:imageId', $event)))\n        }, null, 8 /* PROPS */, [\"mode\", \"value\", \"disabled\"])\n      ]),\n      _cache[26] || (_cache[26] = _createTextVNode()),\n      _createElementVNode(\"div\", _hoisted_17, [\n        _createVNode(_component_LabeledSelect, {\n          required: !_ctx.requestSpotInstances && !_ctx.templateValue('instanceType'),\n          mode: _ctx.mode,\n          \"label-key\": \"eks.nodeGroups.instanceType.label\",\n          options: _ctx.instanceTypeOptions,\n          loading: _ctx.loadingInstanceTypes,\n          value: _ctx.instanceType,\n          disabled: !!_ctx.templateValue('instanceType') || _ctx.requestSpotInstances,\n          tooltip: (_ctx.requestSpotInstances && !_ctx.templateValue('instanceType')) ? _ctx.t('eks.nodeGroups.instanceType.tooltip'): '',\n          rules: !_ctx.requestSpotInstances ? _ctx.rules.instanceType : [],\n          \"data-testid\": \"eks-instance-type-dropdown\",\n          \"onUpdate:value\": _cache[11] || (_cache[11] = ($event: any) => (_ctx.$emit('update:instanceType', $event)))\n        }, null, 8 /* PROPS */, [\"required\", \"mode\", \"options\", \"loading\", \"value\", \"disabled\", \"tooltip\", \"rules\"])\n      ]),\n      _cache[27] || (_cache[27] = _createTextVNode()),\n      _createElementVNode(\"div\", _hoisted_18, [\n        _createVNode(_component_UnitInput, {\n          required: !_ctx.templateValue('diskSize'),\n          \"label-key\": \"eks.nodeGroups.diskSize.label\",\n          mode: _ctx.mode,\n          value: _ctx.diskSize,\n          suffix: \"GB\",\n          loading: _ctx.loadingSelectedVersion,\n          disabled: !!_ctx.templateValue('diskSize') || _ctx.loadingSelectedVersion,\n          rules: _ctx.rules.diskSize,\n          \"data-testid\": \"eks-disksize-input\",\n          \"onUpdate:value\": _cache[12] || (_cache[12] = ($event: any) => (_ctx.$emit('update:diskSize', $event)))\n        }, null, 8 /* PROPS */, [\"required\", \"mode\", \"value\", \"loading\", \"disabled\", \"rules\"])\n      ])\n    ]),\n    _cache[42] || (_cache[42] = _createTextVNode()),\n    (_ctx.requestSpotInstances && _ctx.hasUserLaunchTemplate)\n      ? (_openBlock(), _createBlock(_component_Banner, {\n          key: 2,\n          color: \"warning\",\n          label: _ctx.t('eks.nodeGroups.requestSpotInstances.warning'),\n          \"data-testid\": \"eks-spot-instance-banner\"\n        }, null, 8 /* PROPS */, [\"label\"]))\n      : _createCommentVNode(\"v-if\", true),\n    _cache[43] || (_cache[43] = _createTextVNode()),\n    _createElementVNode(\"div\", _hoisted_19, [\n      _createElementVNode(\"div\", _hoisted_20, [\n        _createVNode(_component_Checkbox, {\n          mode: _ctx.mode,\n          \"label-key\": \"eks.nodeGroups.gpu.label\",\n          value: _ctx.gpu,\n          disabled: !!_ctx.templateValue('imageId') || _ctx.hasRancherLaunchTemplate,\n          tooltip: _ctx.templateValue('imageId') ? _ctx.t('eks.nodeGroups.gpu.tooltip') : '',\n          \"data-testid\": \"eks-gpu-input\",\n          \"onUpdate:value\": _cache[13] || (_cache[13] = ($event: any) => (_ctx.$emit('update:gpu', $event)))\n        }, null, 8 /* PROPS */, [\"mode\", \"value\", \"disabled\", \"tooltip\"])\n      ]),\n      _cache[28] || (_cache[28] = _createTextVNode()),\n      _createElementVNode(\"div\", _hoisted_21, [\n        _createVNode(_component_Checkbox, {\n          value: _ctx.requestSpotInstances,\n          mode: _ctx.mode,\n          \"label-key\": \"eks.nodeGroups.requestSpotInstances.label\",\n          disabled: _ctx.hasRancherLaunchTemplate,\n          \"onUpdate:value\": _cache[14] || (_cache[14] = ($event: any) => (_ctx.$emit('update:requestSpotInstances', $event)))\n        }, null, 8 /* PROPS */, [\"value\", \"mode\", \"disabled\"])\n      ])\n    ]),\n    _cache[44] || (_cache[44] = _createTextVNode()),\n    (_ctx.requestSpotInstances && !_ctx.templateValue('instanceType'))\n      ? (_openBlock(), _createElementBlock(\"div\", _hoisted_22, [\n          _createElementVNode(\"div\", _hoisted_23, [\n            _createVNode(_component_LabeledSelect, {\n              mode: _ctx.mode,\n              value: _ctx.spotInstanceTypes,\n              \"label-key\": \"eks.nodeGroups.spotInstanceTypes.label\",\n              options: _ctx.spotInstanceTypeOptions,\n              multiple: true,\n              loading: _ctx.loadingSelectedVersion || _ctx.loadingInstanceTypes,\n              \"data-testid\": \"eks-spot-instance-type-dropdown\",\n              \"onUpdate:value\": _cache[15] || (_cache[15] = ($event: any) => (_ctx.$emit('update:spotInstanceTypes', $event)))\n            }, null, 8 /* PROPS */, [\"mode\", \"value\", \"options\", \"loading\"])\n          ])\n        ]))\n      : _createCommentVNode(\"v-if\", true),\n    _cache[45] || (_cache[45] = _createTextVNode()),\n    _createElementVNode(\"div\", _hoisted_24, [\n      _createElementVNode(\"div\", _hoisted_25, [\n        _createVNode(_component_LabeledInput, {\n          \"label-key\": \"eks.nodeGroups.userData.label\",\n          mode: _ctx.mode,\n          type: \"multiline\",\n          value: _ctx.userData,\n          disabled: _ctx.hasUserLaunchTemplate,\n          placeholder: _ctx.userDataPlaceholder,\n          \"sub-label\": _ctx.t('eks.nodeGroups.userData.tooltip', {}, true),\n          \"onUpdate:value\": _cache[16] || (_cache[16] = ($event: any) => (_ctx.$emit('update:userData', $event)))\n        }, null, 8 /* PROPS */, [\"mode\", \"value\", \"disabled\", \"placeholder\", \"sub-label\"]),\n        _cache[29] || (_cache[29] = _createTextVNode()),\n        _createVNode(_component_FileSelector, {\n          mode: _ctx.mode,\n          label: _ctx.t('generic.readFromFile'),\n          class: \"role-tertiary mt-20\",\n          onSelected: _cache[17] || (_cache[17] = ($event: any) => (_ctx.$emit('update:userData', $event)))\n        }, null, 8 /* PROPS */, [\"mode\", \"label\"])\n      ]),\n      _cache[30] || (_cache[30] = _createTextVNode()),\n      _createElementVNode(\"div\", _hoisted_26, [\n        _createVNode(_component_LabeledSelect, {\n          loading: _ctx.loadingSshKeyPairs,\n          value: _ctx.ec2SshKey,\n          options: _ctx.sshKeyPairs,\n          \"label-key\": \"eks.nodeGroups.ec2SshKey.label\",\n          mode: _ctx.mode,\n          disabled: _ctx.hasUserLaunchTemplate,\n          taggable: true,\n          searchable: true,\n          \"data-testid\": \"eks-nodegroup-ec2-key-select\",\n          \"onUpdate:value\": _cache[18] || (_cache[18] = ($event: any) => (_ctx.$emit('update:ec2SshKey', $event)))\n        }, null, 8 /* PROPS */, [\"loading\", \"value\", \"options\", \"mode\", \"disabled\"])\n      ])\n    ]),\n    _cache[46] || (_cache[46] = _createTextVNode()),\n    _createElementVNode(\"div\", _hoisted_27, [\n      _createElementVNode(\"div\", _hoisted_28, [\n        _createVNode(_component_KeyValue, {\n          mode: _ctx.mode,\n          \"label-key\": \"eks.nodeGroups.resourceTags.label\",\n          value: _ctx.resourceTags,\n          disabled: _ctx.hasUserLaunchTemplate,\n          \"read-allowed\": false,\n          \"as-map\": true,\n          \"onUpdate:value\": _cache[19] || (_cache[19] = ($event: any) => (_ctx.$emit('update:resourceTags', $event)))\n        }, {\n          title: _withCtx(() => [\n            _createElementVNode(\"h4\", null, _toDisplayString(_ctx.t('eks.nodeGroups.resourceTags.label')), 1 /* TEXT */)\n          ]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"mode\", \"value\", \"disabled\"])\n      ])\n    ])\n  ]))\n}", "<script lang=\"ts\">\nimport { defineComponent, PropType } from 'vue';\nimport { mapGetters, Store } from 'vuex';\nimport debounce from 'lodash/debounce';\n\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\nimport { randomStr } from '@shell/utils/string';\nimport { isEmpty } from '@shell/utils/object';\n\nimport LabeledSelect from '@shell/components/form/LabeledSelect.vue';\nimport LabeledInput from '@components/Form/LabeledInput/LabeledInput.vue';\nimport Checkbox from '@components/Form/Checkbox/Checkbox.vue';\nimport KeyValue from '@shell/components/form/KeyValue.vue';\nimport Banner from '@components/Banner/Banner.vue';\nimport UnitInput from '@shell/components/form/UnitInput.vue';\nimport FileSelector from '@shell/components/form/FileSelector.vue';\n\nimport { MANAGED_TEMPLATE_PREFIX, parseTags } from '../../util/aws';\nimport { AWS } from '../../types';\nimport { DEFAULT_NODE_GROUP_CONFIG } from './CruEKS.vue';\n\n// map between fields in rancher eksConfig and amazon launch templates\nconst launchTemplateFieldMapping: {[key: string]: string} = {\n  imageId:      'ImageId',\n  userData:     'UserData',\n  instanceType: 'InstanceType',\n  ec2SshKey:    '',\n  resourceTags: 'TagSpecifications',\n  diskSize:     'BlockDeviceMappings'\n};\n\nconst DEFAULT_USER_DATA =\n`MIME-Version: 1.0\nContent-Type: multipart/mixed; boundary=\"==MYBOUNDARY==\"\n\n--==MYBOUNDARY==\nContent-Type: text/x-shellscript; charset=\"us-ascii\"\n\n#!/bin/bash\necho \"Running custom user data script\"\n\n--==MYBOUNDARY==--\\\\`;\n\nexport default defineComponent({\n  name: 'EKSNodePool',\n\n  emits: ['update:instanceType', 'update:spotInstanceTypes', 'update:ec2SshKey', 'update:launchTemplate', 'update:nodeRole', 'update:nodeRole', 'update:version', 'update:poolIsUpgrading', 'error', 'update:resourceTags', 'update:diskSize', 'update:nodegroupName', 'update:desiredSize', 'update:minSize', 'update:maxSize', 'update:labels', 'update:tags', 'update:imageId', 'update:gpu', 'update:requestSpotInstances', 'update:userData', 'update:ec2SshKey'],\n\n  components: {\n    LabeledInput,\n    LabeledSelect,\n    KeyValue,\n    Banner,\n    Checkbox,\n    UnitInput,\n    FileSelector\n  },\n\n  props: {\n    nodeRole: {\n      type:    String,\n      default: ''\n    },\n    resourceTags: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    requestSpotInstances: {\n      type:    Boolean,\n      default: false\n    },\n    spotInstanceTypes: {\n      type:    Array,\n      default: () => []\n    },\n    labels: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    tags: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    gpu: {\n      type:    Boolean,\n      default: false\n    },\n    userData: {\n      type:    String,\n      default: ''\n    },\n    instanceType: {\n      type:    String,\n      default: ''\n    },\n    imageId: {\n      type:    [String, null],\n      default: ''\n    },\n    desiredSize: {\n      type:    [Number, String],\n      default: null\n    },\n    minSize: {\n      type:    [Number, String],\n      default: null\n    },\n    maxSize: {\n      type:    [Number, String],\n      default: null\n    },\n    diskSize: {\n      type:    Number,\n      default: null\n    },\n    ec2SshKey: {\n      type:    String,\n      default: ''\n    },\n    nodegroupName: {\n      type:    String,\n      default: ''\n    },\n    region: {\n      type:    String,\n      default: ''\n    },\n    amazonCredentialSecret: {\n      type:    String,\n      default: ''\n    },\n\n    launchTemplate: {\n      type:    Object,\n      default: () => {}\n    },\n\n    version: {\n      type:    String,\n      default: ''\n    },\n\n    clusterVersion: {\n      type:    String,\n      default: ''\n    },\n\n    originalClusterVersion: {\n      type:    String,\n      default: ''\n    },\n\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n\n    ec2Roles: {\n      type:    Array as PropType<AWS.IamRole[]>,\n      default: () => []\n    },\n\n    isNewOrUnprovisioned: {\n      type:    Boolean,\n      default: true\n    },\n\n    poolIsNew: {\n      type:    Boolean,\n      default: false\n    },\n\n    instanceTypeOptions: {\n      type:    Array,\n      default: () => []\n    },\n\n    spotInstanceTypeOptions: {\n      type:    Array,\n      default: () => []\n    },\n\n    launchTemplates: {\n      type:    Array as PropType<AWS.LaunchTemplate[]>,\n      default: () => []\n    },\n\n    sshKeyPairs: {\n      type:    Array as PropType<string[]>,\n      default: () => []\n    },\n\n    normanCluster: {\n      type:    Object,\n      default: null\n    },\n\n    poolIsUpgrading: {\n      type:    Boolean,\n      default: false\n    },\n\n    loadingInstanceTypes: {\n      type:    Boolean,\n      default: false\n    },\n\n    loadingRoles: {\n      type:    Boolean,\n      default: false\n    },\n\n    loadingLaunchTemplates: {\n      type:    Boolean,\n      default: false\n    },\n\n    loadingSshKeyPairs: {\n      type:    Boolean,\n      default: false\n    },\n\n    rules: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  created() {\n    this.debouncedSetValuesFromTemplate = debounce(this.setValuesFromTemplate, 500);\n  },\n\n  data() {\n    const store = this.$store as Store<any>;\n    const t = store.getters['i18n/t'];\n\n    return {\n      originalNodeVersion:   this.version,\n      defaultTemplateOption: { LaunchTemplateName: t('eks.defaultCreateOne') } as AWS.LaunchTemplate,\n\n      defaultNodeRoleOption:          { RoleName: t('eks.defaultCreateOne') },\n      loadingSelectedVersion:         false,\n      // once a specific lt has been selected, an additional query is made to get full information on every version of it\n      selectedLaunchTemplateInfo:     {} as AWS.LaunchTemplateDetail,\n      debouncedSetValuesFromTemplate: null as Function | null,\n      // the keyvalue component needs to be re-rendered if the value prop is updated by parent component when as-map=true\n      // TODO nb file an issue\n      resourceTagKey:                 randomStr()\n    };\n  },\n\n  watch: {\n    selectedLaunchTemplate: {\n      handler(neu) {\n        if (neu && neu.LaunchTemplateId && this.amazonCredentialSecret) {\n          this.fetchLaunchTemplateVersionInfo(this.selectedLaunchTemplate);\n        }\n      },\n      immediate: true\n    },\n\n    amazonCredentialSecret: {\n      handler() {\n        this.fetchLaunchTemplateVersionInfo(this.selectedLaunchTemplate);\n      },\n      immediate: true\n    },\n\n    'selectedVersionData'(neu = {}, old = {}) {\n      this.loadingSelectedVersion = true;\n      if (this.debouncedSetValuesFromTemplate) {\n        this.debouncedSetValuesFromTemplate(neu, old);\n      }\n    },\n\n    'requestSpotInstances'(neu) {\n      if (neu && !this.templateValue('instanceType')) {\n        this.$emit('update:instanceType', null);\n      } else {\n        this.$emit('update:spotInstanceTypes', null);\n      }\n    },\n\n    sshKeyPairs: {\n      handler(neu) {\n        if (!neu.includes(this.ec2SshKey)) {\n          this.$emit('update:ec2SshKey', '');\n        }\n      },\n      deep: true\n    }\n  },\n\n  computed: {\n    ...mapGetters({ t: 'i18n/t' }),\n\n    rancherTemplate() {\n      const eksStatus = this.normanCluster?.eksStatus || {};\n\n      return eksStatus.managedLaunchTemplateID;\n    },\n\n    hasRancherLaunchTemplate() {\n      const eksStatus = this.normanCluster?.eksStatus || {};\n      const nodegroupName = this.nodegroupName;\n      const nodeGroupTemplateVersion = (eksStatus?.managedLaunchTemplateVersions || {})[nodegroupName];\n\n      return isEmpty(this.launchTemplate) && !isEmpty(eksStatus.managedLaunchTemplateID) && !isEmpty(nodeGroupTemplateVersion);\n    },\n\n    hasUserLaunchTemplate() {\n      const { launchTemplate = {} } = this;\n\n      return !!launchTemplate?.id && !!launchTemplate?.version;\n    },\n\n    hasNoLaunchTemplate() {\n      return !this.hasRancherLaunchTemplate && !this.hasUserLaunchTemplate;\n    },\n\n    launchTemplateOptions(): AWS.LaunchTemplate[] {\n      return [this.defaultTemplateOption, ...this.launchTemplates.filter((template) => !(template?.LaunchTemplateName || '').startsWith(MANAGED_TEMPLATE_PREFIX))];\n    },\n\n    selectedLaunchTemplate: {\n      get(): AWS.LaunchTemplate {\n        if (this.hasRancherLaunchTemplate) {\n          return { LaunchTemplateId: this.rancherTemplate, LaunchTemplateName: this.t('eks.nodeGroups.launchTemplate.rancherManaged', { name: this.rancherTemplate }) };\n        }\n        const id = this.launchTemplate?.id;\n\n        return this.launchTemplateOptions.find((lt: AWS.LaunchTemplate) => lt.LaunchTemplateId && lt.LaunchTemplateId === id) || this.defaultTemplateOption;\n      },\n      set(neu: AWS.LaunchTemplate) {\n        if (neu.LaunchTemplateName === this.defaultTemplateOption.LaunchTemplateName) {\n          this.$emit('update:launchTemplate', {});\n\n          return;\n        }\n        const name = neu.LaunchTemplateName;\n        const id = neu.LaunchTemplateId;\n        const version = neu.DefaultVersionNumber;\n\n        this.$emit('update:launchTemplate', {\n          name, id, version\n        });\n      }\n    },\n\n    launchTemplateVersionOptions(): number[] {\n      if (this.selectedLaunchTemplateInfo && this.selectedLaunchTemplateInfo?.LaunchTemplateVersions) {\n        return this.selectedLaunchTemplateInfo.LaunchTemplateVersions.map((version) => version.VersionNumber).sort();\n      }\n\n      return [];\n    },\n\n    selectedVersionInfo(): AWS.LaunchTemplateVersion | null {\n      return (this.selectedLaunchTemplateInfo?.LaunchTemplateVersions || []).find((v: any) => v.VersionNumber === this.launchTemplate?.version) || null;\n    },\n\n    selectedVersionData(): AWS.LaunchTemplateVersionData | undefined {\n      return this.selectedVersionInfo?.LaunchTemplateData;\n    },\n\n    displayNodeRole: {\n      get() {\n        const arn = this.nodeRole;\n\n        if (!arn) {\n          return this.defaultNodeRoleOption;\n        }\n\n        return this.ec2Roles.find((role: AWS.IamRole) => role.Arn === arn) ;\n      },\n      set(neu: AWS.IamRole) {\n        if (neu.Arn) {\n          this.$emit('update:nodeRole', neu.Arn);\n        } else {\n          this.$emit('update:nodeRole', '');\n        }\n      }\n    },\n\n    userDataPlaceholder() {\n      return DEFAULT_USER_DATA;\n    },\n\n    poolIsUnprovisioned() {\n      return this.isNewOrUnprovisioned || this.poolIsNew;\n    },\n\n    clusterWillUpgrade() {\n      return this.clusterVersion !== this.originalClusterVersion;\n    },\n\n    nodeCanUpgrade() {\n      return !this.clusterWillUpgrade && this.originalNodeVersion !== this.clusterVersion && !this.poolIsNew;\n    },\n\n    willUpgrade: {\n      get() {\n        return this.nodeCanUpgrade && this.version === this.clusterVersion;\n      },\n      set(neu: boolean) {\n        if (neu) {\n          this.$emit('update:version', this.clusterVersion);\n          this.$emit('update:poolIsUpgrading', true);\n        } else {\n          this.$emit('update:version', this.originalNodeVersion);\n          this.$emit('update:poolIsUpgrading', false);\n        }\n      }\n    },\n\n    minMaxDesiredErrors() {\n      const errs = (this.rules?.minMaxDesired || []).reduce((errs: string[], rule: Function) => {\n        const err = rule({\n          minSize: this.minSize, maxSize: this.maxSize, desiredSize: this.desiredSize\n        });\n\n        if (err) {\n          errs.push(err);\n        }\n\n        return errs;\n      }, [] as string[]);\n\n      return errs.length ? errs.join(' ') : null;\n    },\n\n    isView() {\n      return this.mode === _VIEW;\n    }\n  },\n\n  methods: {\n    async fetchLaunchTemplateVersionInfo(launchTemplate: AWS.LaunchTemplate) {\n      const { region, amazonCredentialSecret } = this;\n\n      if (!region || !amazonCredentialSecret || this.isView) {\n        return;\n      }\n      const store = this.$store as Store<any>;\n      const ec2Client = await store.dispatch('aws/ec2', { region, cloudCredentialId: amazonCredentialSecret });\n\n      try {\n        if (launchTemplate.LaunchTemplateName !== this.defaultTemplateOption.LaunchTemplateName) {\n          if (launchTemplate.LaunchTemplateId) {\n            this.selectedLaunchTemplateInfo = await ec2Client.describeLaunchTemplateVersions({ LaunchTemplateId: launchTemplate.LaunchTemplateId });\n          } else {\n            this.selectedLaunchTemplateInfo = await ec2Client.describeLaunchTemplateVersions({ LaunchTemplateName: launchTemplate.LaunchTemplateName });\n          }\n        }\n      } catch (err) {\n        this.$emit('error', err);\n      }\n    },\n\n    setValuesFromTemplate(neu = {} as AWS.LaunchTemplateVersionData, old = {} as AWS.LaunchTemplateVersionData) {\n      Object.keys(launchTemplateFieldMapping).forEach((rancherKey: string) => {\n        const awsKey = launchTemplateFieldMapping[rancherKey];\n\n        if (awsKey === 'TagSpecifications') {\n          const { TagSpecifications } = neu;\n\n          if (TagSpecifications) {\n            const tags = {} as {[key:string]: string};\n\n            TagSpecifications.forEach((tag: {Tags?: {Key: string, Value: string}[], ResourceType?: string}) => {\n              if (tag.ResourceType === 'instance' && tag.Tags && tag.Tags.length) {\n                Object.assign(tags, parseTags(tag.Tags));\n              }\n            });\n            this.$emit('update:resourceTags', tags);\n          } else {\n            this.$emit('update:resourceTags', { ...DEFAULT_NODE_GROUP_CONFIG.resourceTags });\n          }\n        } else if (awsKey === 'BlockDeviceMappings') {\n          const { BlockDeviceMappings } = neu;\n\n          if (BlockDeviceMappings && BlockDeviceMappings.length) {\n            const size = BlockDeviceMappings[0]?.Ebs?.VolumeSize;\n\n            this.$emit('update:diskSize', size);\n          } else {\n            this.$emit('update:diskSize', DEFAULT_NODE_GROUP_CONFIG.diskSize);\n          }\n        } else if (this.templateValue(rancherKey)) {\n          this.$emit(`update:${ rancherKey }`, this.templateValue(rancherKey));\n        } else {\n          this.$emit(`update:${ rancherKey }`, DEFAULT_NODE_GROUP_CONFIG[rancherKey as keyof typeof DEFAULT_NODE_GROUP_CONFIG]);\n        }\n      });\n\n      this.$nextTick(() => {\n        this.resourceTagKey = randomStr();\n        this.loadingSelectedVersion = false;\n      });\n    },\n\n    templateValue(field: string): string | null | AWS.TagSpecification | AWS.TagSpecification[] | AWS.BlockDeviceMapping[] {\n      if (this.hasNoLaunchTemplate) {\n        return null;\n      }\n\n      const launchTemplateKey = launchTemplateFieldMapping[field] as keyof AWS.LaunchTemplateVersionData;\n\n      if (!launchTemplateKey) {\n        return null;\n      }\n      const launchTemplateVal = this.selectedVersionData?.[launchTemplateKey];\n\n      if (launchTemplateVal !== undefined && (!(typeof launchTemplateVal === 'object') || !isEmpty(launchTemplateVal))) {\n        if (field === 'diskSize') {\n          const blockMapping = launchTemplateVal[0] as AWS.BlockDeviceMapping;\n\n          return blockMapping?.Ebs?.VolumeSize || null;\n        }\n        if (field === 'resourceTags') {\n          const tags = (launchTemplateVal || []) as AWS.TagSpecification[];\n\n          return tags.filter((tag: AWS.TagSpecification) => tag.ResourceType === 'instance')[0];\n        }\n\n        return launchTemplateVal;\n      }\n\n      return null;\n    },\n  },\n});\n</script>\n\n<template>\n  <div>\n    <h3>{{ t('eks.nodeGroups.groupDetails') }}</h3>\n    <div class=\"row mb-10\">\n      <div class=\"col span-6\">\n        <LabeledInput\n          :value=\"nodegroupName\"\n          label-key=\"eks.nodeGroups.name.label\"\n          :mode=\"mode\"\n          :disabled=\"!poolIsUnprovisioned\"\n          :rules=\"rules.nodegroupName\"\n          data-testid=\"eks-nodegroup-name\"\n          required\n          @update:value=\"$emit('update:nodegroupName', $event)\"\n        />\n      </div>\n\n      <div class=\"col span-6\">\n        <LabeledSelect\n          v-model:value=\"displayNodeRole\"\n          :mode=\"mode\"\n          data-testid=\"eks-noderole\"\n          label-key=\"eks.nodeGroups.nodeRole.label\"\n          :options=\"[defaultNodeRoleOption, ...ec2Roles]\"\n          option-label=\"RoleName\"\n          option-key=\"Arn\"\n          :disabled=\"!poolIsUnprovisioned\"\n          :loading=\"loadingRoles\"\n        />\n      </div>\n    </div>\n    <div class=\"row mb-10\">\n      <div class=\"col span-4\">\n        <LabeledInput\n          type=\"number\"\n          :value=\"desiredSize\"\n          label-key=\"eks.nodeGroups.desiredSize.label\"\n          :mode=\"mode\"\n          :rules=\"rules.desiredSize\"\n          @update:value=\"$emit('update:desiredSize', parseInt($event))\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <LabeledInput\n          type=\"number\"\n          :value=\"minSize\"\n          label-key=\"eks.nodeGroups.minSize.label\"\n          :mode=\"mode\"\n          :rules=\"rules.minSize\"\n          @update:value=\"$emit('update:minSize', parseInt($event))\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <LabeledInput\n          type=\"number\"\n          :value=\"maxSize\"\n          label-key=\"eks.nodeGroups.maxSize.label\"\n          :mode=\"mode\"\n          :rules=\"rules.maxSize\"\n          @update:value=\"$emit('update:maxSize', parseInt($event))\"\n        />\n      </div>\n    </div>\n    <Banner\n      v-if=\"!!minMaxDesiredErrors\"\n      color=\"error\"\n      :label=\"minMaxDesiredErrors\"\n    />\n    <div class=\"row mb-10\">\n      <div class=\"col span-6 mt-20\">\n        <KeyValue\n          :mode=\"mode\"\n          :title=\"t('eks.nodeGroups.groupLabels.label')\"\n          :read-allowed=\"false\"\n          :value=\"labels\"\n          :as-map=\"true\"\n          @update:value=\"$emit('update:labels', $event)\"\n        >\n          <template #title>\n            <h4>\n              {{ t('eks.nodeGroups.groupLabels.label') }}\n            </h4>\n          </template>\n        </KeyValue>\n      </div>\n      <div class=\"col span-6 mt-20\">\n        <KeyValue\n          :mode=\"mode\"\n          :title=\"t('eks.nodeGroups.groupTags.label')\"\n          :read-allowed=\"false\"\n          :as-map=\"true\"\n          :value=\"tags\"\n          data-testid=\"eks-resource-tags-input\"\n          @update:value=\"$emit('update:tags', $event)\"\n        >\n          <template #title>\n            <h4>{{ t('eks.nodeGroups.groupTags.label') }}</h4>\n          </template>\n        </KeyValue>\n      </div>\n    </div>\n    <hr\n      class=\"mb-20\"\n      role=\"none\"\n    >\n    <h3>{{ t('eks.nodeGroups.templateDetails') }}</h3>\n    <Banner\n      v-if=\"clusterWillUpgrade && !poolIsUnprovisioned\"\n      color=\"info\"\n      label-key=\"eks.nodeGroups.kubernetesVersion.clusterWillUpgrade\"\n      data-testid=\"eks-version-upgrade-banner\"\n    />\n    <div class=\"row mb-10\">\n      <div class=\"col span-4 upgrade-version\">\n        <LabeledInput\n          v-if=\"!nodeCanUpgrade\"\n          label-key=\"eks.nodeGroups.kubernetesVersion.label\"\n          :disabled=\"true\"\n          :value=\"version\"\n          data-testid=\"eks-version-display\"\n        />\n        <Checkbox\n          v-else\n          v-model:value=\"willUpgrade\"\n          :label=\"t('eks.nodeGroups.kubernetesVersion.upgrade', {from: originalNodeVersion, to: clusterVersion})\"\n          data-testid=\"eks-version-upgrade-checkbox\"\n          :disabled=\"isView\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <LabeledSelect\n          v-model:value=\"selectedLaunchTemplate\"\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.launchTemplate.label\"\n          :options=\"launchTemplateOptions\"\n          option-label=\"LaunchTemplateName\"\n          option-key=\"LaunchTemplateId\"\n          :disabled=\"!poolIsUnprovisioned\"\n          :loading=\"loadingLaunchTemplates\"\n          data-testid=\"eks-launch-template-dropdown\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <LabeledSelect\n          v-if=\"hasUserLaunchTemplate\"\n          :value=\"launchTemplate.version\"\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.launchTemplate.version\"\n          :options=\"launchTemplateVersionOptions\"\n          data-testid=\"eks-launch-template-version-dropdown\"\n          @update:value=\"$emit('update:launchTemplate', {...launchTemplate, version: $event})\"\n        />\n      </div>\n    </div>\n    <Banner\n      color=\"info\"\n      label-key=\"eks.nodeGroups.imageId.tooltip\"\n    />\n    <div class=\"row mb-10\">\n      <div class=\"col span-4\">\n        <LabeledInput\n          label-key=\"eks.nodeGroups.imageId.label\"\n          :mode=\"mode\"\n          :value=\"imageId\"\n          :disabled=\"hasUserLaunchTemplate\"\n          data-testid=\"eks-image-id-input\"\n          @update:value=\"$emit('update:imageId', $event)\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <LabeledSelect\n          :required=\"!requestSpotInstances && !templateValue('instanceType')\"\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.instanceType.label\"\n          :options=\"instanceTypeOptions\"\n          :loading=\"loadingInstanceTypes\"\n          :value=\"instanceType\"\n          :disabled=\"!!templateValue('instanceType') || requestSpotInstances\"\n          :tooltip=\"(requestSpotInstances && !templateValue('instanceType')) ? t('eks.nodeGroups.instanceType.tooltip'): ''\"\n          :rules=\"!requestSpotInstances ? rules.instanceType : []\"\n          data-testid=\"eks-instance-type-dropdown\"\n          @update:value=\"$emit('update:instanceType', $event)\"\n        />\n      </div>\n\n      <div class=\"col span-4\">\n        <UnitInput\n          :required=\"!templateValue('diskSize')\"\n          label-key=\"eks.nodeGroups.diskSize.label\"\n          :mode=\"mode\"\n          :value=\"diskSize\"\n          suffix=\"GB\"\n          :loading=\"loadingSelectedVersion\"\n          :disabled=\"!!templateValue('diskSize') || loadingSelectedVersion\"\n          :rules=\"rules.diskSize\"\n          data-testid=\"eks-disksize-input\"\n          @update:value=\"$emit('update:diskSize', $event)\"\n        />\n      </div>\n    </div>\n    <Banner\n      v-if=\"requestSpotInstances && hasUserLaunchTemplate\"\n      color=\"warning\"\n      :label=\"t('eks.nodeGroups.requestSpotInstances.warning')\"\n      data-testid=\"eks-spot-instance-banner\"\n    />\n    <div class=\"row mb-10\">\n      <div class=\"col span-4\">\n        <Checkbox\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.gpu.label\"\n          :value=\"gpu\"\n          :disabled=\"!!templateValue('imageId') || hasRancherLaunchTemplate\"\n          :tooltip=\"templateValue('imageId') ? t('eks.nodeGroups.gpu.tooltip') : ''\"\n          data-testid=\"eks-gpu-input\"\n          @update:value=\"$emit('update:gpu', $event)\"\n        />\n      </div>\n      <div class=\"col span-4\">\n        <Checkbox\n          :value=\"requestSpotInstances\"\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.requestSpotInstances.label\"\n          :disabled=\"hasRancherLaunchTemplate\"\n          @update:value=\"$emit('update:requestSpotInstances', $event)\"\n        />\n      </div>\n    </div>\n    <div\n      v-if=\"requestSpotInstances && !templateValue('instanceType')\"\n      class=\"row mb-10\"\n    >\n      <div\n        class=\"col span-6\"\n      >\n        <LabeledSelect\n          :mode=\"mode\"\n          :value=\"spotInstanceTypes\"\n          label-key=\"eks.nodeGroups.spotInstanceTypes.label\"\n          :options=\"spotInstanceTypeOptions\"\n          :multiple=\"true\"\n          :loading=\"loadingSelectedVersion || loadingInstanceTypes\"\n          data-testid=\"eks-spot-instance-type-dropdown\"\n          @update:value=\"$emit('update:spotInstanceTypes', $event)\"\n        />\n      </div>\n    </div>\n    <div class=\"row mb-15\">\n      <div class=\"col span-6 user-data\">\n        <LabeledInput\n          label-key=\"eks.nodeGroups.userData.label\"\n          :mode=\"mode\"\n          type=\"multiline\"\n          :value=\"userData\"\n          :disabled=\"hasUserLaunchTemplate\"\n          :placeholder=\"userDataPlaceholder\"\n          :sub-label=\"t('eks.nodeGroups.userData.tooltip', {}, true)\"\n          @update:value=\"$emit('update:userData', $event)\"\n        />\n        <FileSelector\n          :mode=\"mode\"\n          :label=\"t('generic.readFromFile')\"\n          class=\"role-tertiary mt-20\"\n          @selected=\"$emit('update:userData', $event)\"\n        />\n      </div>\n      <div class=\"col span-6\">\n        <LabeledSelect\n          :loading=\"loadingSshKeyPairs\"\n          :value=\"ec2SshKey\"\n          :options=\"sshKeyPairs\"\n          label-key=\"eks.nodeGroups.ec2SshKey.label\"\n          :mode=\"mode\"\n          :disabled=\"hasUserLaunchTemplate\"\n          :taggable=\"true\"\n          :searchable=\"true\"\n          data-testid=\"eks-nodegroup-ec2-key-select\"\n          @update:value=\"$emit('update:ec2SshKey', $event)\"\n        />\n      </div>\n    </div>\n    <div row=\"mb-10\">\n      <div class=\"col span-12 mt-20\">\n        <KeyValue\n          :mode=\"mode\"\n          label-key=\"eks.nodeGroups.resourceTags.label\"\n          :value=\"resourceTags\"\n          :disabled=\"hasUserLaunchTemplate\"\n          :read-allowed=\"false\"\n          :as-map=\"true\"\n          @update:value=\"$emit('update:resourceTags', $event)\"\n        >\n          <template #title>\n            <h4>\n              {{ t('eks.nodeGroups.resourceTags.label') }}\n            </h4>\n          </template>\n        </KeyValue>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.user-data{\n  &>button{\n    float: right;\n  }\n}\n\n.upgrade-version {\n  display: flex;\n  align-items: center;\n}\n</style>\n"]}]}