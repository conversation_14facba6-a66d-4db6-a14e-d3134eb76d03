{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/HookOption.vue?vue&type=template&id=348dcaa0&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/HookOption.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVzb2x2ZUNvbXBvbmVudCBhcyBfcmVzb2x2ZUNvbXBvbmVudCwgY3JlYXRlVk5vZGUgYXMgX2NyZWF0ZVZOb2RlLCBjcmVhdGVFbGVtZW50Vk5vZGUgYXMgX2NyZWF0ZUVsZW1lbnRWTm9kZSwgdG9EaXNwbGF5U3RyaW5nIGFzIF90b0Rpc3BsYXlTdHJpbmcsIGNyZWF0ZVRleHRWTm9kZSBhcyBfY3JlYXRlVGV4dFZOb2RlLCBvcGVuQmxvY2sgYXMgX29wZW5CbG9jaywgY3JlYXRlRWxlbWVudEJsb2NrIGFzIF9jcmVhdGVFbGVtZW50QmxvY2ssIGNyZWF0ZUNvbW1lbnRWTm9kZSBhcyBfY3JlYXRlQ29tbWVudFZOb2RlLCByZW5kZXJMaXN0IGFzIF9yZW5kZXJMaXN0LCBGcmFnbWVudCBhcyBfRnJhZ21lbnQsIHdpdGhNb2RpZmllcnMgYXMgX3dpdGhNb2RpZmllcnMgfSBmcm9tICJ2dWUiCgpjb25zdCBfaG9pc3RlZF8xID0geyBjbGFzczogInJvdyBtYi0xMCIgfQpjb25zdCBfaG9pc3RlZF8yID0gewogIGtleTogMCwKICBjbGFzczogIm1iLTIwIHNpbmdsZS12YWx1ZSIKfQpjb25zdCBfaG9pc3RlZF8zID0geyBjbGFzczogInZhci1yb3ciIH0KY29uc3QgX2hvaXN0ZWRfNCA9IHsgY2xhc3M6ICJyZW1vdmUiIH0KY29uc3QgX2hvaXN0ZWRfNSA9IFsiZGlzYWJsZWQiLCAib25DbGljayJdCmNvbnN0IF9ob2lzdGVkXzYgPSBbImRpc2FibGVkIl0KCmV4cG9ydCBmdW5jdGlvbiByZW5kZXIoX2N0eCwgX2NhY2hlLCAkcHJvcHMsICRzZXR1cCwgJGRhdGEsICRvcHRpb25zKSB7CiAgY29uc3QgX2NvbXBvbmVudF9SYWRpb0dyb3VwID0gX3Jlc29sdmVDb21wb25lbnQoIlJhZGlvR3JvdXAiKQogIGNvbnN0IF9jb21wb25lbnRfU2hlbGxJbnB1dCA9IF9yZXNvbHZlQ29tcG9uZW50KCJTaGVsbElucHV0IikKICBjb25zdCBfY29tcG9uZW50X0xhYmVsZWRJbnB1dCA9IF9yZXNvbHZlQ29tcG9uZW50KCJMYWJlbGVkSW5wdXQiKQogIGNvbnN0IF9jb21wb25lbnRfTGFiZWxlZFNlbGVjdCA9IF9yZXNvbHZlQ29tcG9uZW50KCJMYWJlbGVkU2VsZWN0IikKICBjb25zdCBfY29tcG9uZW50X3QgPSBfcmVzb2x2ZUNvbXBvbmVudCgidCIpCgogIHJldHVybiAoX29wZW5CbG9jaygpLCBfY3JlYXRlRWxlbWVudEJsb2NrKCJkaXYiLCBudWxsLCBbCiAgICBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCBfaG9pc3RlZF8xLCBbCiAgICAgIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X1JhZGlvR3JvdXAsIHsKICAgICAgICB2YWx1ZTogJGRhdGEuc2VsZWN0SG9vaywKICAgICAgICAib25VcGRhdGU6dmFsdWUiOiBbCiAgICAgICAgICBfY2FjaGVbMF0gfHwgKF9jYWNoZVswXSA9ICRldmVudCA9PiAoKCRkYXRhLnNlbGVjdEhvb2spID0gJGV2ZW50KSksCiAgICAgICAgICAkb3B0aW9ucy51cGRhdGUKICAgICAgICBdLAogICAgICAgIG5hbWU6ICJzZWxlY3RIb29rIiwKICAgICAgICBvcHRpb25zOiBbJ25vbmUnLCAnZXhlYycsICdodHRwR2V0J10sCiAgICAgICAgbGFiZWxzOiBbCiAgICAgICAgICBfY3R4LnQoJ2dlbmVyaWMubm9uZScpLAogICAgICAgICAgX2N0eC50KCd3b3JrbG9hZC5jb250YWluZXIubGlmZWN5Y2xlSG9vay5leGVjLmFkZCcpLAogICAgICAgICAgX2N0eC50KCd3b3JrbG9hZC5jb250YWluZXIubGlmZWN5Y2xlSG9vay5odHRwR2V0LmFkZCcpLAogICAgICAgIF0sCiAgICAgICAgbW9kZTogJHByb3BzLm1vZGUKICAgICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgWyJ2YWx1ZSIsICJsYWJlbHMiLCAibW9kZSIsICJvblVwZGF0ZTp2YWx1ZSJdKQogICAgXSksCiAgICBfY2FjaGVbMTddIHx8IChfY2FjaGVbMTddID0gX2NyZWF0ZVRleHRWTm9kZSgpKSwKICAgICgkZGF0YS5zZWxlY3RIb29rID09PSAnZXhlYycpCiAgICAgID8gKF9vcGVuQmxvY2soKSwgX2NyZWF0ZUVsZW1lbnRCbG9jaygiZGl2IiwgX2hvaXN0ZWRfMiwgWwogICAgICAgICAgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiaDQiLCBudWxsLCBfdG9EaXNwbGF5U3RyaW5nKF9jdHgudCgnd29ya2xvYWQuY29udGFpbmVyLmxpZmVjeWNsZUhvb2suZXhlYy50aXRsZScpKSwgMSAvKiBURVhUICovKSwKICAgICAgICAgIF9jYWNoZVs3XSB8fCAoX2NhY2hlWzddID0gX2NyZWF0ZVRleHRWTm9kZSgpKSwKICAgICAgICAgIF9jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIG51bGwsIFsKICAgICAgICAgICAgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfU2hlbGxJbnB1dCwgewogICAgICAgICAgICAgIHZhbHVlOiAkcHJvcHMudmFsdWUuZXhlYy5jb21tYW5kLAogICAgICAgICAgICAgICJvblVwZGF0ZTp2YWx1ZSI6IF9jYWNoZVsxXSB8fCAoX2NhY2hlWzFdID0gJGV2ZW50ID0+ICgoJHByb3BzLnZhbHVlLmV4ZWMuY29tbWFuZCkgPSAkZXZlbnQpKSwKICAgICAgICAgICAgICBtb2RlOiAkcHJvcHMubW9kZSwKICAgICAgICAgICAgICBsYWJlbDogX2N0eC50KCd3b3JrbG9hZC5jb250YWluZXIubGlmZWN5Y2xlSG9vay5leGVjLmNvbW1hbmQubGFiZWwnKSwKICAgICAgICAgICAgICBwbGFjZWhvbGRlcjogX2N0eC50KCd3b3JrbG9hZC5jb250YWluZXIubGlmZWN5Y2xlSG9vay5leGVjLmNvbW1hbmQucGxhY2Vob2xkZXInLCBudWxsLCB0cnVlKSwKICAgICAgICAgICAgICByZXF1aXJlZDogIiIKICAgICAgICAgICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgWyJ2YWx1ZSIsICJtb2RlIiwgImxhYmVsIiwgInBsYWNlaG9sZGVyIl0pCiAgICAgICAgICBdKQogICAgICAgIF0pKQogICAgICA6IF9jcmVhdGVDb21tZW50Vk5vZGUoInYtaWYiLCB0cnVlKSwKICAgIF9jYWNoZVsxOF0gfHwgKF9jYWNoZVsxOF0gPSBfY3JlYXRlVGV4dFZOb2RlKCkpLAogICAgKCRkYXRhLnNlbGVjdEhvb2sgPT09ICdodHRwR2V0JykKICAgICAgPyAoX29wZW5CbG9jaygpLCBfY3JlYXRlRWxlbWVudEJsb2NrKF9GcmFnbWVudCwgeyBrZXk6IDEgfSwgWwogICAgICAgICAgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiaDQiLCBudWxsLCBfdG9EaXNwbGF5U3RyaW5nKF9jdHgudCgnd29ya2xvYWQuY29udGFpbmVyLmxpZmVjeWNsZUhvb2suaHR0cEdldC50aXRsZScpKSwgMSAvKiBURVhUICovKSwKICAgICAgICAgIF9jYWNoZVsxM10gfHwgKF9jYWNoZVsxM10gPSBfY3JlYXRlVGV4dFZOb2RlKCkpLAogICAgICAgICAgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfMywgWwogICAgICAgICAgICBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9MYWJlbGVkSW5wdXQsIHsKICAgICAgICAgICAgICB2YWx1ZTogJHByb3BzLnZhbHVlLmh0dHBHZXQuaG9zdCwKICAgICAgICAgICAgICAib25VcGRhdGU6dmFsdWUiOiBbCiAgICAgICAgICAgICAgICBfY2FjaGVbMl0gfHwgKF9jYWNoZVsyXSA9ICRldmVudCA9PiAoKCRwcm9wcy52YWx1ZS5odHRwR2V0Lmhvc3QpID0gJGV2ZW50KSksCiAgICAgICAgICAgICAgICAkb3B0aW9ucy51cGRhdGUKICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgIGxhYmVsOiBfY3R4LnQoJ3dvcmtsb2FkLmNvbnRhaW5lci5saWZlY3ljbGVIb29rLmh0dHBHZXQuaG9zdC5sYWJlbCcpLAogICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiBfY3R4LnQoJ3dvcmtsb2FkLmNvbnRhaW5lci5saWZlY3ljbGVIb29rLmh0dHBHZXQuaG9zdC5wbGFjZWhvbGRlcicpLAogICAgICAgICAgICAgIG1vZGU6ICRwcm9wcy5tb2RlCiAgICAgICAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIFsidmFsdWUiLCAibGFiZWwiLCAicGxhY2Vob2xkZXIiLCAibW9kZSIsICJvblVwZGF0ZTp2YWx1ZSJdKSwKICAgICAgICAgICAgX2NhY2hlWzhdIHx8IChfY2FjaGVbOF0gPSBfY3JlYXRlVGV4dFZOb2RlKCkpLAogICAgICAgICAgICBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9MYWJlbGVkSW5wdXQsIHsKICAgICAgICAgICAgICB2YWx1ZTogJHByb3BzLnZhbHVlLmh0dHBHZXQucGF0aCwKICAgICAgICAgICAgICAib25VcGRhdGU6dmFsdWUiOiBbCiAgICAgICAgICAgICAgICBfY2FjaGVbM10gfHwgKF9jYWNoZVszXSA9ICRldmVudCA9PiAoKCRwcm9wcy52YWx1ZS5odHRwR2V0LnBhdGgpID0gJGV2ZW50KSksCiAgICAgICAgICAgICAgICAkb3B0aW9ucy51cGRhdGUKICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgIGxhYmVsOiBfY3R4LnQoJ3dvcmtsb2FkLmNvbnRhaW5lci5saWZlY3ljbGVIb29rLmh0dHBHZXQucGF0aC5sYWJlbCcpLAogICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiBfY3R4LnQoJ3dvcmtsb2FkLmNvbnRhaW5lci5saWZlY3ljbGVIb29rLmh0dHBHZXQucGF0aC5wbGFjZWhvbGRlcicpLAogICAgICAgICAgICAgIG1vZGU6ICRwcm9wcy5tb2RlCiAgICAgICAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIFsidmFsdWUiLCAibGFiZWwiLCAicGxhY2Vob2xkZXIiLCAibW9kZSIsICJvblVwZGF0ZTp2YWx1ZSJdKSwKICAgICAgICAgICAgX2NhY2hlWzldIHx8IChfY2FjaGVbOV0gPSBfY3JlYXRlVGV4dFZOb2RlKCkpLAogICAgICAgICAgICBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9MYWJlbGVkSW5wdXQsIHsKICAgICAgICAgICAgICB2YWx1ZTogJHByb3BzLnZhbHVlLmh0dHBHZXQucG9ydCwKICAgICAgICAgICAgICAib25VcGRhdGU6dmFsdWUiOiBbCiAgICAgICAgICAgICAgICBfY2FjaGVbNF0gfHwgKF9jYWNoZVs0XSA9ICRldmVudCA9PiAoKCRwcm9wcy52YWx1ZS5odHRwR2V0LnBvcnQpID0gJGV2ZW50KSksCiAgICAgICAgICAgICAgICAkb3B0aW9ucy51cGRhdGUKICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgIHZhbHVlTW9kaWZpZXJzOiB7IG51bWJlcjogdHJ1ZSB9LAogICAgICAgICAgICAgIHR5cGU6ICJudW1iZXIiLAogICAgICAgICAgICAgIGxhYmVsOiBfY3R4LnQoJ3dvcmtsb2FkLmNvbnRhaW5lci5saWZlY3ljbGVIb29rLmh0dHBHZXQucG9ydC5sYWJlbCcpLAogICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiBfY3R4LnQoJ3dvcmtsb2FkLmNvbnRhaW5lci5saWZlY3ljbGVIb29rLmh0dHBHZXQucG9ydC5wbGFjZWhvbGRlcicpLAogICAgICAgICAgICAgIG1vZGU6ICRwcm9wcy5tb2RlLAogICAgICAgICAgICAgIHJlcXVpcmVkOiAiIgogICAgICAgICAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBbInZhbHVlIiwgImxhYmVsIiwgInBsYWNlaG9sZGVyIiwgIm1vZGUiLCAib25VcGRhdGU6dmFsdWUiXSksCiAgICAgICAgICAgIF9jYWNoZVsxMF0gfHwgKF9jYWNoZVsxMF0gPSBfY3JlYXRlVGV4dFZOb2RlKCkpLAogICAgICAgICAgICBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9MYWJlbGVkU2VsZWN0LCB7CiAgICAgICAgICAgICAgdmFsdWU6ICRwcm9wcy52YWx1ZS5odHRwR2V0LnNjaGVtZSwKICAgICAgICAgICAgICAib25VcGRhdGU6dmFsdWUiOiBbCiAgICAgICAgICAgICAgICBfY2FjaGVbNV0gfHwgKF9jYWNoZVs1XSA9ICRldmVudCA9PiAoKCRwcm9wcy52YWx1ZS5odHRwR2V0LnNjaGVtZSkgPSAkZXZlbnQpKSwKICAgICAgICAgICAgICAgICRvcHRpb25zLnVwZGF0ZQogICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgbGFiZWw6IF9jdHgudCgnd29ya2xvYWQuY29udGFpbmVyLmxpZmVjeWNsZUhvb2suaHR0cEdldC5zY2hlbWUubGFiZWwnKSwKICAgICAgICAgICAgICBwbGFjZWhvbGRlcjogX2N0eC50KCd3b3JrbG9hZC5jb250YWluZXIubGlmZWN5Y2xlSG9vay5odHRwR2V0LnNjaGVtZS5wbGFjZWhvbGRlcicpLAogICAgICAgICAgICAgIG9wdGlvbnM6ICRkYXRhLnNjaGVtZU9wdGlvbnMsCiAgICAgICAgICAgICAgbW9kZTogJHByb3BzLm1vZGUKICAgICAgICAgICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgWyJ2YWx1ZSIsICJsYWJlbCIsICJwbGFjZWhvbGRlciIsICJvcHRpb25zIiwgIm1vZGUiLCAib25VcGRhdGU6dmFsdWUiXSkKICAgICAgICAgIF0pLAogICAgICAgICAgX2NhY2hlWzE0XSB8fCAoX2NhY2hlWzE0XSA9IF9jcmVhdGVUZXh0Vk5vZGUoKSksCiAgICAgICAgICBfY3JlYXRlRWxlbWVudFZOb2RlKCJoNCIsIG51bGwsIF90b0Rpc3BsYXlTdHJpbmcoX2N0eC50KCd3b3JrbG9hZC5jb250YWluZXIubGlmZWN5Y2xlSG9vay5odHRwSGVhZGVycy50aXRsZScpKSwgMSAvKiBURVhUICovKSwKICAgICAgICAgIF9jYWNoZVsxNV0gfHwgKF9jYWNoZVsxNV0gPSBfY3JlYXRlVGV4dFZOb2RlKCkpLAogICAgICAgICAgKF9vcGVuQmxvY2sodHJ1ZSksIF9jcmVhdGVFbGVtZW50QmxvY2soX0ZyYWdtZW50LCBudWxsLCBfcmVuZGVyTGlzdCgkcHJvcHMudmFsdWUuaHR0cEdldC5odHRwSGVhZGVycywgKGhlYWRlciwgaW5kZXgpID0+IHsKICAgICAgICAgICAgcmV0dXJuIChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soImRpdiIsIHsKICAgICAgICAgICAgICBrZXk6IGluZGV4LAogICAgICAgICAgICAgIGNsYXNzOiAidmFyLXJvdyIsCiAgICAgICAgICAgICAgImRhdGEtdGVzdGlkIjogImhvb2tvcHRpb24taGVhZGVyLXJvdyIKICAgICAgICAgICAgfSwgWwogICAgICAgICAgICAgIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X0xhYmVsZWRJbnB1dCwgewogICAgICAgICAgICAgICAgdmFsdWU6ICRwcm9wcy52YWx1ZS5odHRwR2V0Lmh0dHBIZWFkZXJzW2luZGV4XS5uYW1lLAogICAgICAgICAgICAgICAgIm9uVXBkYXRlOnZhbHVlIjogWyRldmVudCA9PiAoKCRwcm9wcy52YWx1ZS5odHRwR2V0Lmh0dHBIZWFkZXJzW2luZGV4XS5uYW1lKSA9ICRldmVudCksICRvcHRpb25zLnVwZGF0ZV0sCiAgICAgICAgICAgICAgICBsYWJlbDogX2N0eC50KCd3b3JrbG9hZC5jb250YWluZXIubGlmZWN5Y2xlSG9vay5odHRwSGVhZGVycy5uYW1lLmxhYmVsJyksCiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcjogX2N0eC50KCd3b3JrbG9hZC5jb250YWluZXIubGlmZWN5Y2xlSG9vay5odHRwSGVhZGVycy5uYW1lLnBsYWNlaG9sZGVyJyksCiAgICAgICAgICAgICAgICBjbGFzczogInNpbmdsZS12YWx1ZSIsCiAgICAgICAgICAgICAgICBtb2RlOiAkcHJvcHMubW9kZSwKICAgICAgICAgICAgICAgIHJlcXVpcmVkOiAiIgogICAgICAgICAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIFsidmFsdWUiLCAib25VcGRhdGU6dmFsdWUiLCAibGFiZWwiLCAicGxhY2Vob2xkZXIiLCAibW9kZSJdKSwKICAgICAgICAgICAgICBfY2FjaGVbMTFdIHx8IChfY2FjaGVbMTFdID0gX2NyZWF0ZVRleHRWTm9kZSgpKSwKICAgICAgICAgICAgICBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9MYWJlbGVkSW5wdXQsIHsKICAgICAgICAgICAgICAgIHZhbHVlOiAkcHJvcHMudmFsdWUuaHR0cEdldC5odHRwSGVhZGVyc1tpbmRleF0udmFsdWUsCiAgICAgICAgICAgICAgICAib25VcGRhdGU6dmFsdWUiOiBbJGV2ZW50ID0+ICgoJHByb3BzLnZhbHVlLmh0dHBHZXQuaHR0cEhlYWRlcnNbaW5kZXhdLnZhbHVlKSA9ICRldmVudCksICRvcHRpb25zLnVwZGF0ZV0sCiAgICAgICAgICAgICAgICBsYWJlbDogX2N0eC50KCd3b3JrbG9hZC5jb250YWluZXIubGlmZWN5Y2xlSG9vay5odHRwSGVhZGVycy52YWx1ZS5sYWJlbCcpLAogICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI6IF9jdHgudCgnd29ya2xvYWQuY29udGFpbmVyLmxpZmVjeWNsZUhvb2suaHR0cEhlYWRlcnMudmFsdWUucGxhY2Vob2xkZXInKSwKICAgICAgICAgICAgICAgIGNsYXNzOiAic2luZ2xlLXZhbHVlIiwKICAgICAgICAgICAgICAgIG1vZGU6ICRwcm9wcy5tb2RlCiAgICAgICAgICAgICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgWyJ2YWx1ZSIsICJvblVwZGF0ZTp2YWx1ZSIsICJsYWJlbCIsICJwbGFjZWhvbGRlciIsICJtb2RlIl0pLAogICAgICAgICAgICAgIF9jYWNoZVsxMl0gfHwgKF9jYWNoZVsxMl0gPSBfY3JlYXRlVGV4dFZOb2RlKCkpLAogICAgICAgICAgICAgIF9jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIF9ob2lzdGVkXzQsIFsKICAgICAgICAgICAgICAgICghJG9wdGlvbnMuaXNWaWV3KQogICAgICAgICAgICAgICAgICA/IChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soImJ1dHRvbiIsIHsKICAgICAgICAgICAgICAgICAgICAgIGtleTogMCwKICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICJidXR0b24iLAogICAgICAgICAgICAgICAgICAgICAgY2xhc3M6ICJidG4gcm9sZS1saW5rIG1sMCIsCiAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZDogJHByb3BzLm1vZGU9PT0ndmlldycsCiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrOiBfd2l0aE1vZGlmaWVycygkZXZlbnQgPT4gKCRvcHRpb25zLnJlbW92ZUhlYWRlcihpbmRleCkpLCBbInN0b3AiXSkKICAgICAgICAgICAgICAgICAgICB9LCBbCiAgICAgICAgICAgICAgICAgICAgICBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF90LCB7IGs6ICJnZW5lcmljLnJlbW92ZSIgfSkKICAgICAgICAgICAgICAgICAgICBdLCA4IC8qIFBST1BTICovLCBfaG9pc3RlZF81KSkKICAgICAgICAgICAgICAgICAgOiBfY3JlYXRlQ29tbWVudFZOb2RlKCJ2LWlmIiwgdHJ1ZSkKICAgICAgICAgICAgICBdKQogICAgICAgICAgICBdKSkKICAgICAgICAgIH0pLCAxMjggLyogS0VZRURfRlJBR01FTlQgKi8pKSwKICAgICAgICAgIF9jYWNoZVsxNl0gfHwgKF9jYWNoZVsxNl0gPSBfY3JlYXRlVGV4dFZOb2RlKCkpLAogICAgICAgICAgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgbnVsbCwgWwogICAgICAgICAgICAoISRvcHRpb25zLmlzVmlldykKICAgICAgICAgICAgICA/IChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soImJ1dHRvbiIsIHsKICAgICAgICAgICAgICAgICAga2V5OiAwLAogICAgICAgICAgICAgICAgICB0eXBlOiAiYnV0dG9uIiwKICAgICAgICAgICAgICAgICAgY2xhc3M6ICJidG4gcm9sZS1saW5rIG1iLTIwIiwKICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ6ICRwcm9wcy5tb2RlID09PSAndmlldycsCiAgICAgICAgICAgICAgICAgICJkYXRhLXRlc3RpZCI6ICJob29rb3B0aW9uLWFkZC1oZWFkZXItYnV0dG9uIiwKICAgICAgICAgICAgICAgICAgb25DbGljazogX2NhY2hlWzZdIHx8IChfY2FjaGVbNl0gPSBfd2l0aE1vZGlmaWVycygoLi4uYXJncykgPT4gKCRvcHRpb25zLmFkZEhlYWRlciAmJiAkb3B0aW9ucy5hZGRIZWFkZXIoLi4uYXJncykpLCBbInN0b3AiXSkpCiAgICAgICAgICAgICAgICB9LCAiXG4gICAgICAgICAgQWRkIEhlYWRlclxuICAgICAgICAiLCA4IC8qIFBST1BTICovLCBfaG9pc3RlZF82KSkKICAgICAgICAgICAgICA6IF9jcmVhdGVDb21tZW50Vk5vZGUoInYtaWYiLCB0cnVlKQogICAgICAgICAgXSkKICAgICAgICBdLCA2NCAvKiBTVEFCTEVfRlJBR01FTlQgKi8pKQogICAgICA6IF9jcmVhdGVDb21tZW50Vk5vZGUoInYtaWYiLCB0cnVlKQogIF0pKQp9"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/HookOption.vue"], "names": ["t"], "mappings": ";;qBAuHS,KAAK,EAAC,WAAW;;;EAgBf,KAAK,EAAC,oBAAoB;;qBAgB1B,KAAK,EAAC,SAAS;qBA0Db,KAAK,EAAC,QAAQ;;;;;;;;;;;wBA3FzB,oBAqHM;IApHJ,oBAaM,OAbN,UAaM;MAZJ,aAWE;QAVQ,KAAK,EAAE,gBAAU;;gDAAV,gBAAU;UASV,eAAM;;QARrB,IAAI,EAAC,YAAY;QAChB,OAAO,EAAE,2BAA2B;QACpC,MAAM;UAAcA,MAAC;UAA4BA,MAAC;UAAyDA,MAAC;;QAK5G,IAAI,EAAE,WAAI;;;;KAKC,gBAAU;uBACxB,oBAWM,OAXN,UAWM;UAVJ,oBAA+D,6BAAxDA,MAAC;;UACR,oBAQM;YAPJ,aAME;cALQ,KAAK,EAAE,YAAK,CAAC,IAAI,CAAC,OAAO;sEAAlB,YAAK,CAAC,IAAI,CAAC,OAAO;cAChC,IAAI,EAAE,WAAI;cACV,KAAK,EAAEA,MAAC;cACR,WAAW,EAAEA,MAAC;cACf,QAAQ,EAAR,EAAQ;;;;;;KAMA,gBAAU;uBAA1B,oBAqFW;UApFT,oBAAkE,6BAA3DA,MAAC;;UACR,oBAgCM,OAhCN,UAgCM;YA/BJ,aAME;cALQ,KAAK,EAAE,YAAK,CAAC,OAAO,CAAC,IAAI;;sDAAlB,YAAK,CAAC,OAAO,CAAC,IAAI;gBAIlB,eAAM;;cAHpB,KAAK,EAAEA,MAAC;cACR,WAAW,EAAEA,MAAC;cACd,IAAI,EAAE,WAAI;;;YAGb,aAME;cALQ,KAAK,EAAE,YAAK,CAAC,OAAO,CAAC,IAAI;;sDAAlB,YAAK,CAAC,OAAO,CAAC,IAAI;gBAIlB,eAAM;;cAHpB,KAAK,EAAEA,MAAC;cACR,WAAW,EAAEA,MAAC;cACd,IAAI,EAAE,WAAI;;;YAGb,aAQE;cAPQ,KAAK,EAAS,YAAK,CAAC,OAAO,CAAC,IAAI;;sDAAlB,YAAK,CAAC,OAAO,CAAC,IAAI;gBAMzB,eAAM;;8BANrB,gBAAyC;cACzC,IAAI,EAAC,QAAQ;cACZ,KAAK,EAAEA,MAAC;cACR,WAAW,EAAEA,MAAC;cACd,IAAI,EAAE,WAAI;cACX,QAAQ,EAAR,EAAQ;;;YAGV,aAOE;cANQ,KAAK,EAAE,YAAK,CAAC,OAAO,CAAC,MAAM;;sDAApB,YAAK,CAAC,OAAO,CAAC,MAAM;gBAKpB,eAAM;;cAJpB,KAAK,EAAEA,MAAC;cACR,WAAW,EAAEA,MAAC;cACd,OAAO,EAAE,mBAAa;cACtB,IAAI,EAAE,WAAI;;;;UAKf,oBAAsE,6BAA/DA,MAAC;;6BACR,oBAkCM,6BAjCsB,YAAK,CAAC,OAAO,CAAC,WAAW,GAA3C,MAAM,EAAE,KAAK;kCADvB,oBAkCM;cAhCH,GAAG,EAAE,KAAK;cACX,KAAK,EAAC,SAAS;cACf,aAAW,EAAC,uBAAuB;;cAEnC,aAQE;gBAPQ,KAAK,EAAE,YAAK,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI;+CAArC,YAAK,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,aAMrC,eAAM;gBALpB,KAAK,EAAEA,MAAC;gBACR,WAAW,EAAEA,MAAC;gBACf,KAAK,EAAC,cAAc;gBACnB,IAAI,EAAE,WAAI;gBACX,QAAQ,EAAR,EAAQ;;;cAGV,aAOE;gBANQ,KAAK,EAAE,YAAK,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK;+CAAtC,YAAK,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,aAKtC,eAAM;gBAJpB,KAAK,EAAEA,MAAC;gBACR,WAAW,EAAEA,MAAC;gBACf,KAAK,EAAC,cAAc;gBACnB,IAAI,EAAE,WAAI;;;cAGb,oBAUM,OAVN,UAUM;kBARK,eAAM;mCADf,oBAQS;;sBANP,IAAI,EAAC,QAAQ;sBACb,KAAK,EAAC,mBAAmB;sBACxB,QAAQ,EAAE,WAAI;sBACd,OAAK,4BAAO,qBAAY,CAAC,KAAK;;sBAE/B,aAAwB,gBAArB,CAAC,EAAC,gBAAgB;;;;;;;UAK3B,oBAWM;cATK,eAAM;+BADf,oBASS;;kBAPP,IAAI,EAAC,QAAQ;kBACb,KAAK,EAAC,qBAAqB;kBAC1B,QAAQ,EAAE,WAAI;kBACf,aAAW,EAAC,8BAA8B;kBACzC,OAAK,yDAAO,iDAAS;mBACvB,kCAED", "sourcesContent": ["<script>\nimport debounce from 'lodash/debounce';\nimport { RadioGroup } from '@components/Form/Radio';\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport ShellInput from '@shell/components/form/ShellInput';\nimport { _VIEW } from '@shell/config/query-params';\nimport { isEmpty } from '@shell/utils/object';\n\nexport default {\n  emits: ['update:value'],\n\n  props: {\n    mode: {\n      type:     String,\n      required: true,\n    },\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  components: {\n    RadioGroup, LabeledInput, LabeledSelect, ShellInput\n  },\n\n  data() {\n    const selectHook = null;\n\n    const defaultExec = { exec: { command: [] } };\n    const defaultHttpGet = {\n      httpGet: {\n        host:        '',\n        path:        '',\n        port:        null,\n        scheme:      '',\n        httpHeaders: null\n      }\n    };\n\n    return {\n      selectHook,\n      defaultExec,\n      defaultHttpGet,\n      schemeOptions: ['HTTP', 'HTTPS']\n    };\n  },\n\n  computed: {\n    isView() {\n      return this.mode === _VIEW;\n    },\n  },\n\n  created() {\n    if (this.value) {\n      this.selectHook = Object.keys(this.value)[0];\n    }\n\n    if (isEmpty(this.value)) {\n      this.selectHook = 'none';\n    }\n\n    this.queueUpdate = debounce(this.update, 500);\n  },\n\n  methods: {\n    addHeader() {\n      const header = { name: '', value: '' };\n\n      if (!this.value.httpGet.httpHeaders) {\n        this.value.httpGet['httpHeaders'] = [];\n      }\n\n      this.value.httpGet.httpHeaders.push(header);\n    },\n\n    removeHeader(index) {\n      this.value.httpGet.httpHeaders.splice(index, 1);\n    },\n\n    update() {\n      const { ...leftovers } = this.value;\n\n      switch (this.selectHook) {\n      case 'none':\n        this.deleteLeftovers(leftovers);\n        break;\n      case 'exec':\n        this.deleteLeftovers(leftovers);\n        Object.assign(this.value, this.defaultExec);\n        break;\n      case 'httpGet':\n        this.deleteLeftovers(leftovers);\n        Object.assign(this.value, this.defaultHttpGet);\n        break;\n      default:\n        break;\n      }\n\n      this.$emit('update:value', this.value);\n    },\n\n    deleteLeftovers(leftovers) {\n      if (leftovers) {\n        for (const obj in leftovers) {\n          delete this.value[obj];\n        }\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <div>\n    <div class=\"row mb-10\">\n      <RadioGroup\n        v-model:value=\"selectHook\"\n        name=\"selectHook\"\n        :options=\"['none', 'exec', 'httpGet']\"\n        :labels=\"[\n          t('generic.none'),\n          t('workload.container.lifecycleHook.exec.add'),\n          t('workload.container.lifecycleHook.httpGet.add'),\n        ]\"\n        :mode=\"mode\"\n        @update:value=\"update\"\n      />\n    </div>\n\n    <template v-if=\"selectHook === 'exec'\">\n      <div class=\"mb-20 single-value\">\n        <h4>{{ t('workload.container.lifecycleHook.exec.title') }}</h4>\n        <div>\n          <ShellInput\n            v-model:value=\"value.exec.command\"\n            :mode=\"mode\"\n            :label=\"t('workload.container.lifecycleHook.exec.command.label')\"\n            :placeholder=\"t('workload.container.lifecycleHook.exec.command.placeholder', null, true)\"\n            required\n          />\n        </div>\n      </div>\n    </template>\n\n    <template v-if=\"selectHook === 'httpGet'\">\n      <h4>{{ t('workload.container.lifecycleHook.httpGet.title') }}</h4>\n      <div class=\"var-row\">\n        <LabeledInput\n          v-model:value=\"value.httpGet.host\"\n          :label=\"t('workload.container.lifecycleHook.httpGet.host.label')\"\n          :placeholder=\"t('workload.container.lifecycleHook.httpGet.host.placeholder')\"\n          :mode=\"mode\"\n          @update:value=\"update\"\n        />\n        <LabeledInput\n          v-model:value=\"value.httpGet.path\"\n          :label=\"t('workload.container.lifecycleHook.httpGet.path.label')\"\n          :placeholder=\"t('workload.container.lifecycleHook.httpGet.path.placeholder')\"\n          :mode=\"mode\"\n          @update:value=\"update\"\n        />\n        <LabeledInput\n          v-model:value.number=\"value.httpGet.port\"\n          type=\"number\"\n          :label=\"t('workload.container.lifecycleHook.httpGet.port.label')\"\n          :placeholder=\"t('workload.container.lifecycleHook.httpGet.port.placeholder')\"\n          :mode=\"mode\"\n          required\n          @update:value=\"update\"\n        />\n        <LabeledSelect\n          v-model:value=\"value.httpGet.scheme\"\n          :label=\"t('workload.container.lifecycleHook.httpGet.scheme.label')\"\n          :placeholder=\"t('workload.container.lifecycleHook.httpGet.scheme.placeholder')\"\n          :options=\"schemeOptions\"\n          :mode=\"mode\"\n          @update:value=\"update\"\n        />\n      </div>\n\n      <h4>{{ t('workload.container.lifecycleHook.httpHeaders.title') }}</h4>\n      <div\n        v-for=\"(header, index) in value.httpGet.httpHeaders\"\n        :key=\"index\"\n        class=\"var-row\"\n        data-testid=\"hookoption-header-row\"\n      >\n        <LabeledInput\n          v-model:value=\"value.httpGet.httpHeaders[index].name\"\n          :label=\"t('workload.container.lifecycleHook.httpHeaders.name.label')\"\n          :placeholder=\"t('workload.container.lifecycleHook.httpHeaders.name.placeholder')\"\n          class=\"single-value\"\n          :mode=\"mode\"\n          required\n          @update:value=\"update\"\n        />\n        <LabeledInput\n          v-model:value=\"value.httpGet.httpHeaders[index].value\"\n          :label=\"t('workload.container.lifecycleHook.httpHeaders.value.label')\"\n          :placeholder=\"t('workload.container.lifecycleHook.httpHeaders.value.placeholder')\"\n          class=\"single-value\"\n          :mode=\"mode\"\n          @update:value=\"update\"\n        />\n        <div class=\"remove\">\n          <button\n            v-if=\"!isView\"\n            type=\"button\"\n            class=\"btn role-link ml0\"\n            :disabled=\"mode==='view'\"\n            @click.stop=\"removeHeader(index)\"\n          >\n            <t k=\"generic.remove\" />\n          </button>\n        </div>\n      </div>\n\n      <div>\n        <button\n          v-if=\"!isView\"\n          type=\"button\"\n          class=\"btn role-link mb-20\"\n          :disabled=\"mode === 'view'\"\n          data-testid=\"hookoption-add-header-button\"\n          @click.stop=\"addHeader\"\n        >\n          Add Header\n        </button>\n      </div>\n    </template>\n  </div>\n</template>\n\n<style lang='scss' scoped>\n.var-row{\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr 1fr 100px;\n  grid-column-gap: 20px;\n  margin-bottom: 20px;\n  align-items: center;\n\n  .single-value {\n    grid-column: span 2;\n  }\n\n  .labeled-select {\n    min-height: $input-height;\n  }\n  .remove BUTTON {\n    padding: 0px;\n  }\n}\n</style>\n"]}]}