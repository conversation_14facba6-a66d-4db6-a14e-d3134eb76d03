{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/utils/svg-filter.js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/utils/svg-filter.js", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}