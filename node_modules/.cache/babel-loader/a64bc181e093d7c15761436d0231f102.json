{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/CloudCredential.vue?vue&type=template&id=774c6697", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/CloudCredential.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/CloudCredential.vue"], "names": ["mode", "$fetchState", "disabled", "displayLabel", "question", "value", "displayTooltip", "$emit", "showDescription"], "mappings": ";;qBAiCO,KAAK,EAAC,KAAK;qBACT,KAAK,EAAC,YAAY;qBAalB,KAAK,EAAC,kBAAkB;;;;;;wBAd/B,oBAmBM,OAnBN,UAmBM;IAlBJ,oBAYM,OAZN,UAYM;MAXJ,aAUE;QATC,IAAI,EAAEA,SAAI;QACV,OAAO,EAAE,gBAAO;QAChB,QAAQ,EAAEC,gBAAW,CAAC,OAAO,IAAIC,aAAQ;QACzC,KAAK,EAAEC,iBAAY;QACnB,WAAW,EAAEC,aAAQ,CAAC,WAAW;QACjC,QAAQ,EAAEA,aAAQ,CAAC,QAAQ;QAC3B,KAAK,EAAEC,UAAK;QACZ,OAAO,EAAEC,mBAAc;QACvB,gBAAY,wCAAGL,gBAAW,CAAC,OAAO,IAAIM,UAAK,iBAAiB,MAAM;;;;IAGvE,oBAIM,OAJN,UAIM;OAHOC,oBAAe;yBAA1B,oBAEM,oCADDJ,aAAQ,CAAC,WAAW", "sourcesContent": ["<script>\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport { NORMAN } from '@shell/config/types';\nimport Question from './Question';\n\nexport default {\n  emits: ['update:value'],\n\n  components: { LabeledSelect },\n  mixins:     [Question],\n\n  async fetch() {\n    this.all = await this.$store.dispatch('rancher/findAll', { type: NORMAN.CLOUD_CREDENTIAL });\n  },\n\n  data() {\n    return { all: [] };\n  },\n\n  computed: {\n    options() {\n      return this.all.map((x) => {\n        return {\n          label: x.nameDisplay || x.name || x.metadata.name,\n          value: x.id\n        };\n      });\n    }\n  },\n};\n</script>\n\n<template>\n  <div class=\"row\">\n    <div class=\"col span-6\">\n      <LabeledSelect\n        :mode=\"mode\"\n        :options=\"options\"\n        :disabled=\"$fetchState.pending || disabled\"\n        :label=\"displayLabel\"\n        :placeholder=\"question.description\"\n        :required=\"question.required\"\n        :value=\"value\"\n        :tooltip=\"displayTooltip\"\n        @update:value=\"!$fetchState.pending && $emit('update:value', $event)\"\n      />\n    </div>\n    <div class=\"col span-6 mt-10\">\n      <div v-if=\"showDescription\">\n        {{ question.description }}\n      </div>\n    </div>\n  </div>\n</template>\n"]}]}