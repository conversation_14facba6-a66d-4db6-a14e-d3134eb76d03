{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetStatus.vue?vue&type=template&id=0abf3d2c&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetStatus.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetStatus.vue"], "names": [], "mappings": ";;qBA8IO,KAAK,EAAC,cAAc;qBAClB,KAAK,EAAC,OAAO;qBAGb,KAAK,EAAC,oBAAoB;qBACxB,KAAK,EAAC,QAAQ;qBACZ,KAAK,EAAC,OAAO;qBAoBT,KAAK,EAAC,YAAY;qBAIhB,KAAK,EAAC,uBAAuB;qBASM,KAAK,EAAC,YAAY;;;;;;;wBAvCtE,oBA6DM,OA7DN,UA6DM;IA5DJ,oBAEM,OAFN,UAEM,mBADD,aAAI,CAAC,KAAK;;IAEf,oBAwDM,OAxDN,UAwDM;MAvDJ,oBAyCM,OAzCN,UAyCM;QAxCJ,oBAEM,OAFN,UAEM,mBADD,YAAK;;QAEV,oBAoCM;UAnCJ,KAAK,EAAC,oBAAoB;UAC1B,QAAQ,EAAC,GAAG;UACX,MAAI,uCAAE,iBAAQ;UACd,OAAK,uCAAE,iBAAQ;+DACA,iBAAQ;;UAExB,aA4Ba;YA3BX,GAAG,EAAC,SAAS;YACb,SAAS,EAAC,QAAQ;YAClB,MAAM,EAAC,KAAK;YACX,QAAQ,EAAE,EAAE;YACZ,KAAK,EAAE,kBAAkB;YACzB,IAAI,EAAE,KAAK;YACX,SAAS,EAAE,KAAK;YACjB,cAAY,EAAC,uBAAuB;;YAKzB,MAAM,WACf,CAYM;cAZN,oBAYM,OAZN,UAYM;gBAXJ,oBAUK;kBATH,KAAK,EAAC,wBAAwB;kBAC7B,OAAK,sDAAO,iBAAQ;;qCAErB,oBAKK,6BAJkB,aAAM,GAAnB,GAAG,EAAE,GAAG;0CADlB,oBAKK,QAHF,GAAG,EAAE,GAAG;sBAET,oBAA4B,+BAAnB,GAAG,CAAC,KAAK;sBAAU,oBAA+C,QAA/C,UAA+C,mBAAnB,GAAG,CAAC,KAAK;;;;;;8BAbzE,CAEM;cAFN,oBAEM,OAFN,UAEM;kDADD,aAAI,CAAC,UAAU,IAAG,KAAG,oBAAG,aAAI,CAAC,KAAK,IAAG,GAAC,oBAAG,YAAK,IAAG,SAAO;0CAAA,oBAA2C,OAAxC,KAAK,EAAC,+BAA+B;;;;;;;;;;qCAoB3G,oBAYM;QAVH,KAAK,0CAA0B,eAAM,CAAC,MAAM;;2BAE7C,oBAOE,6BANuB,eAAM,GAArB,KAAK,EAAE,GAAG;gDADpB,oBAOE;YALC,GAAG,EAAE,GAAG;YAER,mBAAiB,EAAE,KAAK,CAAC,KAAK;YAC9B,KAAK,mCAAmB,KAAK,CAAC,KAAK;YACnC,KAAK,kBAAE,KAAK,CAAC,KAAK", "sourcesContent": ["<script>\nimport { sortBy } from '@shell/utils/sort';\nimport { get } from '@shell/utils/object';\nimport { stateSort, STATES_ENUM } from '@shell/plugins/dashboard-store/resource-class';\n\nexport default {\n\n  name: 'FleetStatus',\n\n  props: {\n    values: {\n      type:     Array,\n      required: true,\n    },\n    colorKey: {\n      type:    String,\n      default: 'color',\n    },\n    labelKey: {\n      type:    String,\n      default: 'label',\n    },\n    valueKey: {\n      type:    String,\n      default: 'value',\n    },\n    min: {\n      type:    Number,\n      default: 0\n    },\n    max: {\n      type:    Number,\n      default: null,\n    },\n    minPercent: {\n      type:    Number,\n      default: 5,\n    },\n    showZeros: {\n      type:    Boolean,\n      default: false,\n    },\n\n    title: {\n      type:    String,\n      default: 'Resources'\n    }\n  },\n\n  computed: {\n    meta() {\n      return {\n        total:      this.values.map((x) => x.value).reduce((a, b) => a + b, 0),\n        readyCount: this.values.filter((x) => x.status === STATES_ENUM.SUCCESS || x.status === STATES_ENUM.READY).map((x) => x.value).reduce((a, b) => a + b, 0)\n      };\n    },\n\n    pieces() {\n      let out = [...this.values].reduce((prev, obj) => {\n        const color = get(obj, this.colorKey);\n        const label = get(obj, this.labelKey);\n        const value = get(obj, this.valueKey);\n\n        if ( obj[this.valueKey] === 0 && !this.showZeros) {\n          return prev;\n        }\n\n        prev.push({\n          color,\n          label,\n          value,\n          sort: stateSort(color),\n        });\n\n        return prev;\n      }, []);\n\n      const minPercent = this.minPercent || 0;\n      const min = this.min || 0;\n      let max = this.max;\n      let sum = 0;\n\n      if ( !this.max ) {\n        max = 100;\n        if ( out.length ) {\n          max = out.map((x) => x.value).reduce((a, b) => a + b);\n        }\n      }\n\n      out = this.values.map((obj) => {\n        if (obj.value === 0 ) {\n          obj.percent = 0;\n\n          return obj;\n        }\n        const percent = Math.max(minPercent, toPercent(obj.value, min, max));\n\n        obj.percent = percent;\n        sum += percent;\n\n        return obj;\n      });\n\n      // If the sum is bigger than 100%, take it out of the biggest piece\n      if ( sum > 100 ) {\n        sortBy(out, 'percent', true)[0].percent -= sum - 100;\n      }\n\n      out = this.values.map((obj) => {\n        obj.style = `width: ${ obj.percent }%; background: var(--${ obj.color })`;\n\n        return obj;\n      });\n\n      return [...out].filter((obj) => obj.percent);\n    },\n  },\n\n  methods: {\n    showMenu(show) {\n      if (this.$refs.popover) {\n        if (show) {\n          this.$refs.popover.show();\n        } else {\n          this.$refs.popover.hide();\n        }\n      }\n    },\n  }\n};\n\nfunction toPercent(value, min, max) {\n  value = Math.max(min, Math.min(max, value));\n  let per = value / (max - min) * 100; // Percent 0-100\n\n  per = Math.floor(per * 100) / 100; // Round to 2 decimal places\n\n  return per;\n}\n\n</script>\n<template>\n  <div class=\"fleet-status\">\n    <div class=\"count\">\n      {{ meta.total }}\n    </div>\n    <div class=\"progress-container\">\n      <div class=\"header\">\n        <div class=\"title\">\n          {{ title }}\n        </div>\n        <div\n          class=\"resources-dropdown\"\n          tabindex=\"0\"\n          @blur=\"showMenu(false)\"\n          @click=\"showMenu(true)\"\n          @focus.capture=\"showMenu(true)\"\n        >\n          <v-dropdown\n            ref=\"popover\"\n            placement=\"bottom\"\n            offset=\"-10\"\n            :triggers=\"[]\"\n            :delay=\"{show: 0, hide: 0}\"\n            :flip=\"false\"\n            :container=\"false\"\n            popper-class=\"fleet-summary-tooltip\"\n          >\n            <div class=\"meta-title\">\n              {{ meta.readyCount }} / {{ meta.total }} {{ title }} ready <i class=\"icon toggle icon-chevron-down\" />\n            </div>\n            <template #popper>\n              <div class=\"resources-status-list\">\n                <ul\n                  class=\"list-unstyled dropdown\"\n                  @click.stop=\"showMenu(false)\"\n                >\n                  <li\n                    v-for=\"(val, idx) in values\"\n                    :key=\"idx\"\n                  >\n                    <span>{{ val.label }}</span><span class=\"list-count\">{{ val.count }}</span>\n                  </li>\n                </ul>\n              </div>\n            </template>\n          </v-dropdown>\n        </div>\n      </div>\n      <div\n        v-trim-whitespace\n        :class=\"{progress: true, multi: pieces.length > 1}\"\n      >\n        <div\n          v-for=\"(piece, idx) of pieces\"\n          :key=\"idx\"\n          v-trim-whitespace\n          :primary-color-var=\"piece.color\"\n          :class=\"{'piece': true, [piece.color]: true}\"\n          :style=\"piece.style\"\n        />\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  $progress-divider-width: 1px;\n  $progress-border-radius: 90px;\n  $progress-height:        10px;\n  $progress-width:         100%;\n\n  .fleet-status {\n    display: flex;\n    width: 100%;\n    border: 1px solid var(--border);\n    border-radius: 10px\n  }\n\n  .header {\n    display: flex;\n    margin-bottom: 15px;\n    justify-content: space-between;\n  }\n\n  .progress-container {\n    width: 100%;\n    padding: 15px;\n\n  }\n\n  .count {\n    padding: 15px;\n    background-color: var(--tabbed-container-bg);\n    border-radius: 10px 0 0 10px;\n    display: flex;\n    align-items: center;\n    min-width: 60px;\n    justify-content: center;\n    font-size: $font-size-h2\n  }\n\n  .progress {\n    display: block;\n    border-radius: $progress-border-radius;\n    background-color: var(--progress-bg);\n    height: $progress-height;\n    width: $progress-width;\n\n    .piece {\n      display: inline-block;\n      vertical-align: top;\n      height: $progress-height;\n      border-radius: 0;\n      border-right: $progress-divider-width solid var(--progress-divider);\n      vertical-align: top;\n\n      &:first-child {\n        border-top-left-radius: $progress-border-radius;\n        border-bottom-left-radius: $progress-border-radius;\n      }\n\n      &:last-child {\n        border-top-right-radius: $progress-border-radius;\n        border-bottom-right-radius: $progress-border-radius;\n        border-right: 0;\n      }\n    }\n  }\n\n  .piece.bg-success:only-child {\n    opacity: 0.5;\n  }\n\n  .meta-title {\n    display: flex;\n    align-items: center;\n\n    &:hover {\n      cursor: pointer;\n      color: var(--link);\n    }\n\n    .icon {\n      margin: 4px 0 0 5px;\n      opacity: 0.3;\n    }\n  }\n\n  .resources-dropdown {\n    li {\n        display: flex;\n        justify-content: space-between;\n        margin: 10px 5px;\n    }\n\n    .list-count {\n        margin-left: 30px;\n    }\n }\n</style>\n"]}]}