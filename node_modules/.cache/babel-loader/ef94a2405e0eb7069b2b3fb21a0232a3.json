{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Boolean.vue?vue&type=template&id=e36a995a", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Boolean.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Boolean.vue"], "names": ["question", "mode", "displayLabel", "value", "disabled", "displayTooltip", "$emit", "showDescription", "displayDescription"], "mappings": ";;;qBAiBS,KAAK,EAAC,YAAY;;;;;;wBAJzB,oBAsBM;IArBH,aAAW,iBAAiBA,aAAQ,CAAC,QAAQ;IAC9C,KAAK,EAAC,KAAK;;IAEX,oBAUM,OAVN,UAUM;MATJ,aAQE;QAPC,IAAI,EAAEC,SAAI;QACV,KAAK,EAAEC,iBAAY;QACnB,KAAK,EAAEC,UAAK;QACZ,QAAQ,EAAEC,aAAQ;QAClB,OAAO,EAAEC,mBAAc;QACvB,aAAW,mBAAmBL,aAAQ,CAAC,QAAQ;QAC/C,gBAAY,uCAAEM,UAAK,iBAAiB,MAAM;;;;KAIvCC,oBAAe;uBADvB,oBAMM;;UAJH,aAAW,yBAAyBP,aAAQ,CAAC,QAAQ;UACtD,KAAK,EAAC,kBAAkB;4BAErBQ,uBAAkB", "sourcesContent": ["<script>\nimport { Checkbox } from '@components/Form/Checkbox';\nimport Question from './Question';\n\nexport default {\n  emits: ['update:value'],\n\n  components: { Checkbox },\n  mixins:     [Question]\n};\n</script>\n\n<template>\n  <div\n    :data-testid=\"`boolean-row-${question.variable}`\"\n    class=\"row\"\n  >\n    <div class=\"col span-6\">\n      <Checkbox\n        :mode=\"mode\"\n        :label=\"displayLabel\"\n        :value=\"value\"\n        :disabled=\"disabled\"\n        :tooltip=\"displayTooltip\"\n        :data-testid=\"`boolean-input-${question.variable}`\"\n        @update:value=\"$emit('update:value', $event)\"\n      />\n    </div>\n    <div\n      v-if=\"showDescription\"\n      :data-testid=\"`boolean-description-${question.variable}`\"\n      class=\"col span-6 mt-10\"\n    >\n      {{ displayDescription }}\n    </div>\n  </div>\n</template>\n"]}]}