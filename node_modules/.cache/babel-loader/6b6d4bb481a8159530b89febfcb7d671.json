{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Reference.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Reference.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Reference.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE3E,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEjC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC/D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;AACnB,CAAC;;AAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE5B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE5C,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,EAAE,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;IAC/B;;IAEA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEd,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3E;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;UAE5G,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C;MACF,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC;QACD,CAAC,CAAC;UACA,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACnD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC/C,CAAC;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACvG,EAAE,EAAE,CAAC,CAAC;;UAEN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC;QACH;MACF,CAAC;IACH,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC;QACH,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACV;MACF,CAAC,CAAC;IACJ,CAAC;;EAEH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC;EACH,CAAC;AACH,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Reference.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport ResourceLabeledSelect from '@shell/components/form/ResourceLabeledSelect.vue';\nimport { PaginationParamFilter } from '@shell/types/store/pagination.types';\n\nimport { PVC, STORAGE_CLASS } from '@shell/config/types';\nimport Question from './Question';\n\n// Older versions of rancher document these words as valid types\nconst LEGACY_MAP = {\n  storageclass: STORAGE_CLASS,\n  pvc:          PVC,\n};\n\nexport default {\n  emits: ['update:value'],\n\n  components: { LabeledInput, ResourceLabeledSelect },\n  mixins:     [Question],\n\n  props: {\n    inStore: {\n      type:    String,\n      default: 'cluster',\n    },\n\n    targetNamespace: {\n      type:    String,\n      default: null,\n    },\n  },\n\n  data() {\n    const t = this.question.type;\n\n    let typeName;\n\n    const match = t.match(/^reference\\[(.*)\\]$/);\n\n    if ( match ) {\n      typeName = match?.[1];\n    } else {\n      typeName = LEGACY_MAP[t] || t;\n    }\n\n    let typeSchema;\n\n    if ( typeName ) {\n      typeSchema = this.$store.getters[`${ this.inStore }/schemaFor`](typeName);\n    }\n\n    return {\n      typeName,\n      typeSchema,\n      all:                 [],\n      allResourceSettings: {\n        updateResources: (all) => {\n          // Filter to only include required namespaced resources\n          const resources = this.isNamespaced ? all.filter((r) => r.metadata.namespace === this.targetNamespace) : all;\n\n          return this.mapResourcesToOptions(resources);\n        }\n      },\n      paginateResourceSetting: {\n        updateResources: (resources) => {\n          return this.mapResourcesToOptions(resources);\n        },\n        /**\n          * of type PaginateTypeOverridesFn\n          * @param [LabelSelectPaginationFunctionOptions] opts\n          * @returns LabelSelectPaginationFunctionOptions\n         */\n        requestSettings: (opts) => {\n          // Filter to only include required namespaced resources\n          const filters = this.isNamespaced ? [\n            PaginationParamFilter.createSingleField({ field: 'metadata.namespace', value: this.targetNamespace }),\n          ] : [];\n\n          return {\n            ...opts,\n            filters,\n            groupByNamespace: false,\n            classify:         true,\n          };\n        }\n      },\n    };\n  },\n\n  methods: {\n    mapResourcesToOptions(resources) {\n      return resources.map((r) => {\n        if (r.id) {\n          return {\n            label: r.nameDisplay || r.metadata.name,\n            value: r.metadata.name\n          };\n        } else {\n          return r;\n        }\n      });\n    },\n\n  },\n\n  computed: {\n    isNamespaced() {\n      return !!this.typeSchema?.attributes?.namespaced;\n    },\n  },\n};\n</script>\n\n<template>\n  <div\n    v-if=\"typeSchema\"\n    class=\"row\"\n  >\n    <div class=\"col span-6\">\n      <ResourceLabeledSelect\n        :resource-type=\"typeName\"\n        :in-store=\"inStore\"\n        :disabled=\"$fetchState.pending || disabled\"\n        :label=\"displayLabel\"\n        :placeholder=\"question.description\"\n        :required=\"question.required\"\n        :value=\"value\"\n        :tooltip=\"displayTooltip\"\n        :paginated-resource-settings=\"paginateResourceSetting\"\n        :all-resources-settings=\"allResourceSettings\"\n        @update:value=\"!$fetchState.pending && $emit('update:value', $event)\"\n      />\n    </div>\n    <div class=\"col span-6 mt-10\">\n      {{ typeSchema.attributes.kind }}<span v-if=\"isNamespaced\"> in namespace {{ targetNamespace }}</span>\n      <div v-if=\"showDescription\">\n        {{ question.description }}\n      </div>\n    </div>\n  </div>\n  <div\n    v-else\n    class=\"row\"\n  >\n    <div class=\"col span-6\">\n      <LabeledInput\n        :mode=\"mode\"\n        :disabled=\"$fetchState.pending || disabled\"\n        :label=\"displayLabel\"\n        :placeholder=\"question.description\"\n        :required=\"question.required\"\n        :value=\"value\"\n        :tooltip=\"displayTooltip\"\n        @update:value=\"!$fetchState.pending && $emit('update:value', $event)\"\n      />\n    </div>\n    <div class=\"col span-6 mt-10\">\n      {{ question.type }}<span v-if=\"isNamespaced\"> in namespace {{ targetNamespace }}</span>\n      <div v-if=\"showDescription\">\n        {{ question.description }}\n      </div>\n      <div class=\"text-error\">\n        (You do not have access to list this type)\n      </div>\n    </div>\n  </div>\n</template>\n"]}]}