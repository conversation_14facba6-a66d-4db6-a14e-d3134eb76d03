{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Float.vue?vue&type=template&id=5aef8f72", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Float.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Float.vue"], "names": ["question", "mode", "displayLabel", "value", "disabled", "displayTooltip", "rules", "val", "$emit", "showDescription"], "mappings": ";;;qBAiBS,KAAK,EAAC,YAAY;;;;;;wBAJzB,oBA0BM;IAzBH,aAAW,eAAeA,aAAQ,CAAC,QAAQ;IAC5C,KAAK,EAAC,KAAK;;IAEX,oBAcM,OAdN,UAcM;MAbJ,aAYE;QAXA,IAAI,EAAC,MAAM;QACV,IAAI,EAAEC,SAAI;QACV,KAAK,EAAEC,iBAAY;QACnB,WAAW,EAAEF,aAAQ,CAAC,OAAO;QAC7B,QAAQ,EAAEA,aAAQ,CAAC,QAAQ;QAC3B,KAAK,EAAEG,UAAK;QACZ,QAAQ,EAAEC,aAAQ;QAClB,OAAO,EAAEC,mBAAc;QACvB,KAAK,EAAEC,UAAK;QACZ,aAAW,iBAAiBN,aAAQ,CAAC,QAAQ;QAC7C,gBAAY,uCAAEO,QAAG,GAAG,UAAU,CAAC,MAAM,SAAS,KAAK,CAACA,QAAG,MAAMC,UAAK,iBAAiBD,QAAG;;;;KAInFE,oBAAe;uBADvB,oBAMM;;UAJH,aAAW,uBAAuBT,aAAQ,CAAC,QAAQ;UACpD,KAAK,EAAC,kBAAkB;4BAErBA,aAAQ,CAAC,WAAW", "sourcesContent": ["<script>\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport Question from './Question';\n\nexport default {\n  emits: ['update:value'],\n\n  components: { LabeledInput },\n  mixins:     [Question]\n};\n</script>\n\n<template>\n  <div\n    :data-testid=\"`float-row-${question.variable}`\"\n    class=\"row\"\n  >\n    <div class=\"col span-6\">\n      <LabeledInput\n        type=\"text\"\n        :mode=\"mode\"\n        :label=\"displayLabel\"\n        :placeholder=\"question.default\"\n        :required=\"question.required\"\n        :value=\"value\"\n        :disabled=\"disabled\"\n        :tooltip=\"displayTooltip\"\n        :rules=\"rules\"\n        :data-testid=\"`float-input-${question.variable}`\"\n        @update:value=\"val = parseFloat($event); if ( !isNaN(val) ) { $emit('update:value', val) }\"\n      />\n    </div>\n    <div\n      v-if=\"showDescription\"\n      :data-testid=\"`float-description-${question.variable}`\"\n      class=\"col span-6 mt-10\"\n    >\n      {{ question.description }}\n    </div>\n  </div>\n</template>\n"]}]}