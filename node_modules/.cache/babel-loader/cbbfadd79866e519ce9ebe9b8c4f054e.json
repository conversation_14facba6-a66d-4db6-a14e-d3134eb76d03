{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SortableTable/actions.js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SortableTable/actions.js", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}