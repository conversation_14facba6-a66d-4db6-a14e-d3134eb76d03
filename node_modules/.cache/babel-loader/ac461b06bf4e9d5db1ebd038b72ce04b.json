{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Array.vue?vue&type=template&id=091b7c78", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Array.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVzb2x2ZUNvbXBvbmVudCBhcyBfcmVzb2x2ZUNvbXBvbmVudCwgY3JlYXRlVk5vZGUgYXMgX2NyZWF0ZVZOb2RlLCBjcmVhdGVFbGVtZW50Vk5vZGUgYXMgX2NyZWF0ZUVsZW1lbnRWTm9kZSwgdG9EaXNwbGF5U3RyaW5nIGFzIF90b0Rpc3BsYXlTdHJpbmcsIG9wZW5CbG9jayBhcyBfb3BlbkJsb2NrLCBjcmVhdGVFbGVtZW50QmxvY2sgYXMgX2NyZWF0ZUVsZW1lbnRCbG9jaywgY3JlYXRlQ29tbWVudFZOb2RlIGFzIF9jcmVhdGVDb21tZW50Vk5vZGUsIGNyZWF0ZVRleHRWTm9kZSBhcyBfY3JlYXRlVGV4dFZOb2RlIH0gZnJvbSAidnVlIgoKY29uc3QgX2hvaXN0ZWRfMSA9IHsgY2xhc3M6ICJyb3ciIH0KY29uc3QgX2hvaXN0ZWRfMiA9IHsgY2xhc3M6ICJjb2wgc3Bhbi02IiB9CmNvbnN0IF9ob2lzdGVkXzMgPSB7CiAga2V5OiAwLAogIGNsYXNzOiAiY29sIHNwYW4tNiBtdC0xMCIKfQoKZXhwb3J0IGZ1bmN0aW9uIHJlbmRlcihfY3R4LCBfY2FjaGUsICRwcm9wcywgJHNldHVwLCAkZGF0YSwgJG9wdGlvbnMpIHsKICBjb25zdCBfY29tcG9uZW50X0FycmF5TGlzdCA9IF9yZXNvbHZlQ29tcG9uZW50KCJBcnJheUxpc3QiKQoKICByZXR1cm4gKF9vcGVuQmxvY2soKSwgX2NyZWF0ZUVsZW1lbnRCbG9jaygiZGl2IiwgX2hvaXN0ZWRfMSwgWwogICAgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfMiwgWwogICAgICBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9BcnJheUxpc3QsIHsKICAgICAgICB2YWx1ZTogX2N0eC52YWx1ZSwKICAgICAgICB0aXRsZTogX2N0eC5xdWVzdGlvbi5sYWJlbCwKICAgICAgICBtb2RlOiBfY3R4Lm1vZGUsCiAgICAgICAgZGlzYWJsZWQ6IF9jdHguZGlzYWJsZWQsCiAgICAgICAgcHJvdGlwOiBfY3R4LmRpc3BsYXlUb29sdGlwLAogICAgICAgICJvblVwZGF0ZTp2YWx1ZSI6ICRvcHRpb25zLnVwZGF0ZQogICAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBbInZhbHVlIiwgInRpdGxlIiwgIm1vZGUiLCAiZGlzYWJsZWQiLCAicHJvdGlwIiwgIm9uVXBkYXRlOnZhbHVlIl0pCiAgICBdKSwKICAgIF9jYWNoZVswXSB8fCAoX2NhY2hlWzBdID0gX2NyZWF0ZVRleHRWTm9kZSgpKSwKICAgIChfY3R4LnNob3dEZXNjcmlwdGlvbikKICAgICAgPyAoX29wZW5CbG9jaygpLCBfY3JlYXRlRWxlbWVudEJsb2NrKCJkaXYiLCBfaG9pc3RlZF8zLCBfdG9EaXNwbGF5U3RyaW5nKF9jdHgucXVlc3Rpb24uZGVzY3JpcHRpb24pLCAxIC8qIFRFWFQgKi8pKQogICAgICA6IF9jcmVhdGVDb21tZW50Vk5vZGUoInYtaWYiLCB0cnVlKQogIF0pKQp9"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Array.vue"], "names": ["value", "question", "mode", "disabled", "displayTooltip", "showDescription"], "mappings": ";;qBAmBO,KAAK,EAAC,KAAK;qBACT,KAAK,EAAC,YAAY;;;EAYrB,KAAK,EAAC,kBAAkB;;;;;;wBAb5B,oBAiBM,OAjBN,UAiBM;IAhBJ,oBASM,OATN,UASM;MARJ,aAOE;QANC,KAAK,EAAEA,UAAK;QACZ,KAAK,EAAEC,aAAQ,CAAC,KAAK;QACrB,IAAI,EAAEC,SAAI;QACV,QAAQ,EAAEC,aAAQ;QAClB,MAAM,EAAEC,mBAAc;QACtB,gBAAY,EAAE,eAAM;;;;KAIjBC,oBAAe;uBADvB,oBAKM,OALN,UAKM,mBADDJ,aAAQ,CAAC,WAAW", "sourcesContent": ["<script>\nimport ArrayList from '@shell/components/form/ArrayList';\nimport Question from './Question';\n\nexport default {\n  emits: ['update:value'],\n\n  components: { ArrayList },\n  mixins:     [Question],\n\n  methods: {\n    update(val) {\n      this.$emit('update:value', val);\n    }\n  }\n};\n</script>\n\n<template>\n  <div class=\"row\">\n    <div class=\"col span-6\">\n      <ArrayList\n        :value=\"value\"\n        :title=\"question.label\"\n        :mode=\"mode\"\n        :disabled=\"disabled\"\n        :protip=\"displayTooltip\"\n        @update:value=\"update\"\n      />\n    </div>\n    <div\n      v-if=\"showDescription\"\n      class=\"col span-6 mt-10\"\n    >\n      {{ question.description }}\n    </div>\n  </div>\n</template>\n"]}]}