{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??clonedRuleSet-44.use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Card/Card.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Card/Card.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Card/Card.vue"], "names": [], "mappings": "AACA,OAAO,EAAE,eAAe,EAAW,MAAO,KAAK,CAAA;AAC/C,OAAO,EAAE,sBAAqB,EAAE,MAAO,8BAA8B,CAAA;AAErE,eAAe,eAAe,CAAC;IAE7B,IAAI,EAAG,MAAM;IACb,KAAK,EAAE;QACL;;WAEE;QACF,KAAK,EAAE;YACL,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,EAAC;SACX;QACD;;WAEE;QACF,OAAO,EAAE;YACP,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,EAAC;SACX;QACD;;WAEE;QACF,YAAY,EAAE;YACZ,IAAI,EAAK,QAAiD;YAC1D,OAAO,EAAE,GAAS,EAAC,GAAI,CAAA;SACxB;QACD;;WAEE;QACF,UAAU,EAAE;YACV,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,IAAG;SACb;QACD;;WAEE;QACF,mBAAmB,EAAE;YACnB,IAAI,EAAK,OAAO;YAChB,OAAO,EAAE,IAAG;SACb;QACD;;WAEE;QACF,WAAW,EAAE;YACX,IAAI,EAAK,OAAO;YAChB,OAAO,EAAE,IAAG;SACb;QACD,MAAM,EAAE;YACN,IAAI,EAAK,OAAO;YAChB,OAAO,EAAE,KAAK;SACf;QACD,gBAAgB,EAAE;YAChB,IAAI,EAAK,OAAO;YAChB,OAAO,EAAE,KAAK;SACf;KACF;IACD,KAAK,CAAC,KAAK;QACT,IAAI,KAAK,CAAC,gBAAgB,EAAE,CAAA;YAC1B,sBAAsB,CAAC,oCAAoC,EAAE;gBAC3D,6DAA4D;gBAC5D,+CAA8C;gBAC9C,0EAAyE;gBACzE,+DAA8D;gBAC9D,oEAAmE;gBACnE,iBAAiB,EAAE,KAAK;gBACxB,iBAAiB,EAAE,IAAI;aACxB,CAAC,CAAA;QACJ,CAAA;IACF,CAAA;CACD,CAAC,CAAA", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/Card/Card.vue.tsx", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent, PropType } from 'vue';\nimport { useBasicSetupFocusTrap } from '@shell/composables/focusTrap';\n\nexport default defineComponent({\n\n  name:  'Card',\n  props: {\n    /**\n     * The card's title.\n     */\n    title: {\n      type:    String,\n      default: ''\n    },\n    /**\n     * The text content for the card's body.\n     */\n    content: {\n      type:    String,\n      default: ''\n    },\n    /**\n     * The function to invoke when the default action button is clicked.\n     */\n    buttonAction: {\n      type:    Function as PropType<(event: MouseEvent) => void>,\n      default: (): void => { }\n    },\n    /**\n     * The text for the default action button.\n     */\n    buttonText: {\n      type:    String,\n      default: 'go'\n    },\n    /**\n     * Toggles the card's highlight-border class.\n     */\n    showHighlightBorder: {\n      type:    Boolean,\n      default: true\n    },\n    /**\n     * Toggles the card's Actions section.\n     */\n    showActions: {\n      type:    Boolean,\n      default: true\n    },\n    sticky: {\n      type:    Boolean,\n      default: false,\n    },\n    triggerFocusTrap: {\n      type:    Boolean,\n      default: false,\n    },\n  },\n  setup(props) {\n    if (props.triggerFocusTrap) {\n      useBasicSetupFocusTrap('#focus-trap-card-container-element', {\n        // needs to be false because of import YAML modal from header\n        // where the YAML editor itself is a focus trap\n        // and we can't have it superseed the \"escape key\" to blur that UI element\n        // In this case the focus trap moves the focus out of the modal\n        // correctly once it closes because of the \"onBeforeUnmount\" trigger\n        escapeDeactivates: false,\n        allowOutsideClick: true,\n      });\n    }\n  }\n});\n</script>\n\n<template>\n  <div\n    id=\"focus-trap-card-container-element\"\n    class=\"card-container\"\n    :class=\"{'highlight-border': showHighlightBorder, 'card-sticky': sticky}\"\n    data-testid=\"card\"\n  >\n    <div class=\"card-wrap\">\n      <div\n        class=\"card-title\"\n        data-testid=\"card-title-slot\"\n      >\n        <slot name=\"title\">\n          {{ title }}\n        </slot>\n      </div>\n      <hr>\n      <div\n        class=\"card-body\"\n        data-testid=\"card-body-slot\"\n      >\n        <slot name=\"body\">\n          {{ content }}\n        </slot>\n      </div>\n      <div\n        v-if=\"showActions\"\n        class=\"card-actions\"\n        data-testid=\"card-actions-slot\"\n      >\n        <slot name=\"actions\">\n          <button\n            class=\"btn role-primary\"\n            @click=\"buttonAction\"\n          >\n            {{ buttonText }}\n          </button>\n        </slot>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang='scss'>\n .card-container {\n  &.highlight-border {\n    border-left: 5px solid var(--primary);\n  }\n  border-radius: var(--border-radius);\n  display: flex;\n  flex-basis: 40%;\n  margin: 10px;\n  min-height: 100px;\n  padding: 10px;\n  box-shadow: 0 0 20px var(--shadow);\n  &:not(.top) {\n    align-items: top;\n    flex-direction: row;\n    justify-content: start;\n  }\n  .card-wrap {\n    width: 100%;\n  }\n   & .card-body {\n    color: var(--input-label);\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n   }\n   & .card-actions {\n     align-self: end;\n     display: flex;\n     padding-top: 20px;\n   }\n   & .card-title {\n    align-items: center;\n    display: flex;\n    width: 100%;\n     h5 {\n       margin: 0;\n     }\n    .flex-right {\n      margin-left: auto;\n    }\n   }\n\n  // Sticky mode will stick header and footer to top and bottom with content in the middle scrolling\n   &.card-sticky {\n      // display: flex;\n      // flex-direction: column;\n      overflow: hidden;\n\n    .card-wrap {\n      display: flex;\n      flex-direction: column;\n\n      .card-body {\n        justify-content: flex-start;\n        overflow: auto;\n      }\n\n      > * {\n        flex: 0;\n      }\n\n      .card-body {\n        flex: 1;\n      }\n    }\n   }\n }\n</style>\n"]}]}