{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Circle.vue?vue&type=template&id=d1de6198&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Circle.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgY3JlYXRlRWxlbWVudFZOb2RlIGFzIF9jcmVhdGVFbGVtZW50Vk5vZGUsIGNyZWF0ZVRleHRWTm9kZSBhcyBfY3JlYXRlVGV4dFZOb2RlLCB0b0Rpc3BsYXlTdHJpbmcgYXMgX3RvRGlzcGxheVN0cmluZywgb3BlbkJsb2NrIGFzIF9vcGVuQmxvY2ssIGNyZWF0ZUVsZW1lbnRCbG9jayBhcyBfY3JlYXRlRWxlbWVudEJsb2NrLCBjcmVhdGVDb21tZW50Vk5vZGUgYXMgX2NyZWF0ZUNvbW1lbnRWTm9kZSB9IGZyb20gInZ1ZSIKCmNvbnN0IF9ob2lzdGVkXzEgPSBbInZpZXdCb3giXQpjb25zdCBfaG9pc3RlZF8yID0gWyJ0cmFuc2Zvcm0iXQpjb25zdCBfaG9pc3RlZF8zID0gWyJpZCJdCmNvbnN0IF9ob2lzdGVkXzQgPSBbInN0b3AtY29sb3IiXQpjb25zdCBfaG9pc3RlZF81ID0gWyJzdG9wLWNvbG9yIl0KY29uc3QgX2hvaXN0ZWRfNiA9IFsiaWQiXQpjb25zdCBfaG9pc3RlZF83ID0gWyJzdG9wLWNvbG9yIl0KY29uc3QgX2hvaXN0ZWRfOCA9IFsic3RvcC1jb2xvciJdCmNvbnN0IF9ob2lzdGVkXzkgPSBbInIiLCAiY3kiLCAiY3giLCAic3Ryb2tlLXdpZHRoIiwgInN0cm9rZSJdCmNvbnN0IF9ob2lzdGVkXzEwID0gWyJyIiwgImN5IiwgImN4IiwgInN0cm9rZS13aWR0aCIsICJzdHJva2UiLCAic3Ryb2tlLWRhc2hhcnJheSIsICJzdHJva2UtZGFzaG9mZnNldCJdCmNvbnN0IF9ob2lzdGVkXzExID0gWyJ4IiwgInkiLCAiZmlsbCJdCgpleHBvcnQgZnVuY3Rpb24gcmVuZGVyKF9jdHgsIF9jYWNoZSwgJHByb3BzLCAkc2V0dXAsICRkYXRhLCAkb3B0aW9ucykgewogIHJldHVybiAoX29wZW5CbG9jaygpLCBfY3JlYXRlRWxlbWVudEJsb2NrKCJzdmciLCB7CiAgICBjbGFzczogImNpcmNsZSIsCiAgICB3aWR0aDogIjEwMCUiLAogICAgaGVpZ2h0OiAiMTAwJSIsCiAgICB2aWV3Qm94OiAkb3B0aW9ucy52aWV3Qm94CiAgfSwgWwogICAgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZyIsIHsgdHJhbnNmb3JtOiAkb3B0aW9ucy50cmFuc2Zvcm0gfSwgWwogICAgICBfY3JlYXRlRWxlbWVudFZOb2RlKCJkZWZzIiwgbnVsbCwgWwogICAgICAgIF9jcmVhdGVFbGVtZW50Vk5vZGUoImxpbmVhckdyYWRpZW50IiwgewogICAgICAgICAgaWQ6ICRvcHRpb25zLnByaW1hcnlTdHJva2VDb2xvcklkLAogICAgICAgICAgeDE6ICIwJSIsCiAgICAgICAgICB5MTogIjAlIiwKICAgICAgICAgIHgyOiAiMTAwJSIsCiAgICAgICAgICB5MjogIjAlIgogICAgICAgIH0sIFsKICAgICAgICAgIF9jcmVhdGVFbGVtZW50Vk5vZGUoInN0b3AiLCB7CiAgICAgICAgICAgIG9mZnNldDogIjUwJSIsCiAgICAgICAgICAgICJzdG9wLWNvbG9yIjogJHByb3BzLnByaW1hcnlTdHJva2VHcmFkaWVudENvbG9yIHx8ICRwcm9wcy5wcmltYXJ5U3Ryb2tlQ29sb3IKICAgICAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIF9ob2lzdGVkXzQpLAogICAgICAgICAgX2NhY2hlWzBdIHx8IChfY2FjaGVbMF0gPSBfY3JlYXRlVGV4dFZOb2RlKCkpLAogICAgICAgICAgX2NyZWF0ZUVsZW1lbnRWTm9kZSgic3RvcCIsIHsKICAgICAgICAgICAgb2Zmc2V0OiAiMTAwJSIsCiAgICAgICAgICAgICJzdG9wLWNvbG9yIjogJHByb3BzLnByaW1hcnlTdHJva2VDb2xvcgogICAgICAgICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgX2hvaXN0ZWRfNSkKICAgICAgICBdLCA4IC8qIFBST1BTICovLCBfaG9pc3RlZF8zKSwKICAgICAgICBfY2FjaGVbMl0gfHwgKF9jYWNoZVsyXSA9IF9jcmVhdGVUZXh0Vk5vZGUoKSksCiAgICAgICAgX2NyZWF0ZUVsZW1lbnRWTm9kZSgibGluZWFyR3JhZGllbnQiLCB7CiAgICAgICAgICBpZDogJG9wdGlvbnMuc2Vjb25kYXJ5U3Ryb2tlQ29sb3JJZCwKICAgICAgICAgIHgxOiAiMCUiLAogICAgICAgICAgeTE6ICIwJSIsCiAgICAgICAgICB4MjogIjEwMCUiLAogICAgICAgICAgeTI6ICIwJSIKICAgICAgICB9LCBbCiAgICAgICAgICBfY3JlYXRlRWxlbWVudFZOb2RlKCJzdG9wIiwgewogICAgICAgICAgICBvZmZzZXQ6ICI1MCUiLAogICAgICAgICAgICAic3RvcC1jb2xvciI6ICRwcm9wcy5zZWNvbmRhcnlTdHJva2VHcmFkaWVudENvbG9yIHx8ICRwcm9wcy5zZWNvbmRhcnlTdHJva2VDb2xvcgogICAgICAgICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgX2hvaXN0ZWRfNyksCiAgICAgICAgICBfY2FjaGVbMV0gfHwgKF9jYWNoZVsxXSA9IF9jcmVhdGVUZXh0Vk5vZGUoKSksCiAgICAgICAgICBfY3JlYXRlRWxlbWVudFZOb2RlKCJzdG9wIiwgewogICAgICAgICAgICBvZmZzZXQ6ICIxMDAlIiwKICAgICAgICAgICAgInN0b3AtY29sb3IiOiAkcHJvcHMuc2Vjb25kYXJ5U3Ryb2tlQ29sb3IKICAgICAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIF9ob2lzdGVkXzgpCiAgICAgICAgXSwgOCAvKiBQUk9QUyAqLywgX2hvaXN0ZWRfNikKICAgICAgXSksCiAgICAgIF9jYWNoZVszXSB8fCAoX2NhY2hlWzNdID0gX2NyZWF0ZVRleHRWTm9kZSgpKSwKICAgICAgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiY2lyY2xlIiwgewogICAgICAgIHI6ICRvcHRpb25zLnJhZGl1cywKICAgICAgICBjeTogJG9wdGlvbnMuY2VudGVyLAogICAgICAgIGN4OiAkb3B0aW9ucy5jZW50ZXIsCiAgICAgICAgInN0cm9rZS13aWR0aCI6ICRwcm9wcy5zdHJva2VXaWR0aCwKICAgICAgICBzdHJva2U6IGB1cmwoIyR7JG9wdGlvbnMuc2Vjb25kYXJ5U3Ryb2tlQ29sb3JJZH0pYCwKICAgICAgICBmaWxsOiAibm9uZSIKICAgICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgX2hvaXN0ZWRfOSksCiAgICAgIF9jYWNoZVs0XSB8fCAoX2NhY2hlWzRdID0gX2NyZWF0ZVRleHRWTm9kZSgpKSwKICAgICAgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiY2lyY2xlIiwgewogICAgICAgIHI6ICRvcHRpb25zLnJhZGl1cywKICAgICAgICBjeTogJG9wdGlvbnMuY2VudGVyLAogICAgICAgIGN4OiAkb3B0aW9ucy5jZW50ZXIsCiAgICAgICAgInN0cm9rZS13aWR0aCI6ICRwcm9wcy5zdHJva2VXaWR0aCwKICAgICAgICBzdHJva2U6IGB1cmwoIyR7JG9wdGlvbnMucHJpbWFyeVN0cm9rZUNvbG9ySWR9KWAsCiAgICAgICAgInN0cm9rZS1kYXNoYXJyYXkiOiAkb3B0aW9ucy5jaXJjdW1mZXJlbmNlLAogICAgICAgICJzdHJva2UtZGFzaG9mZnNldCI6ICRvcHRpb25zLmNpcmN1bWZlcmVuY2UgKiAoMSAtICRwcm9wcy5wZXJjZW50YWdlKSwKICAgICAgICBmaWxsOiAibm9uZSIKICAgICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgX2hvaXN0ZWRfMTApCiAgICBdLCA4IC8qIFBST1BTICovLCBfaG9pc3RlZF8yKSwKICAgIF9jYWNoZVs1XSB8fCAoX2NhY2hlWzVdID0gX2NyZWF0ZVRleHRWTm9kZSgpKSwKICAgICgkcHJvcHMuc2hvd1RleHQpCiAgICAgID8gKF9vcGVuQmxvY2soKSwgX2NyZWF0ZUVsZW1lbnRCbG9jaygidGV4dCIsIHsKICAgICAgICAgIGtleTogMCwKICAgICAgICAgIHg6ICRvcHRpb25zLmNlbnRlciwKICAgICAgICAgIHk6ICRvcHRpb25zLmNlbnRlciwKICAgICAgICAgIHN0eWxlOiB7ImZvbnQtc2l6ZSI6IjI1IiwiZG9taW5hbnQtYmFzZWxpbmUiOiJtaWRkbGUiLCJ0ZXh0LWFuY2hvciI6Im1pZGRsZSJ9LAogICAgICAgICAgZmlsbDogYHVybCgjJHskb3B0aW9ucy5wcmltYXJ5U3Ryb2tlQ29sb3JJZH0pYAogICAgICAgIH0sIF90b0Rpc3BsYXlTdHJpbmcoJG9wdGlvbnMucGFyc2VQZXJjZW50YWdlKSArICIlXG4gICAgIiwgOSAvKiBURVhULCBQUk9QUyAqLywgX2hvaXN0ZWRfMTEpKQogICAgICA6IF9jcmVhdGVDb21tZW50Vk5vZGUoInYtaWYiLCB0cnVlKQogIF0sIDggLyogUFJPUFMgKi8sIF9ob2lzdGVkXzEpKQp9"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Circle.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;wBAsFE,oBAsEM;IArEJ,KAAK,EAAC,QAAQ;IACd,KAAK,EAAC,MAAM;IACZ,MAAM,EAAC,MAAM;IACZ,OAAO,EAAE,gBAAO;;IAEjB,oBAqDI,OArDA,SAAS,EAAE,kBAAS;MACtB,oBAiCO;QAhCL,oBAeiB;UAdd,EAAE,EAAE,6BAAoB;UACzB,EAAE,EAAC,IAAI;UACP,EAAE,EAAC,IAAI;UACP,EAAE,EAAC,MAAM;UACT,EAAE,EAAC,IAAI;;UAEP,oBAGE;YAFA,MAAM,EAAC,KAAK;YACX,YAAU,EAAE,iCAA0B,IAAI,yBAAkB;;;UAE/D,oBAGE;YAFA,MAAM,EAAC,MAAM;YACZ,YAAU,EAAE,yBAAkB;;;;QAGnC,oBAeiB;UAdd,EAAE,EAAE,+BAAsB;UAC3B,EAAE,EAAC,IAAI;UACP,EAAE,EAAC,IAAI;UACP,EAAE,EAAC,MAAM;UACT,EAAE,EAAC,IAAI;;UAEP,oBAGE;YAFA,MAAM,EAAC,KAAK;YACX,YAAU,EAAE,mCAA4B,IAAI,2BAAoB;;;UAEnE,oBAGE;YAFA,MAAM,EAAC,MAAM;YACZ,YAAU,EAAE,2BAAoB;;;;;MAIvC,oBAOE;QANC,CAAC,EAAE,eAAM;QACT,EAAE,EAAE,eAAM;QACV,EAAE,EAAE,eAAM;QACV,cAAY,EAAE,kBAAW;QACzB,MAAM,UAAU,+BAAsB;QACvC,IAAI,EAAC,MAAM;;;MAEb,oBASE;QARC,CAAC,EAAE,eAAM;QACT,EAAE,EAAE,eAAM;QACV,EAAE,EAAE,eAAM;QACV,cAAY,EAAE,kBAAW;QACzB,MAAM,UAAU,6BAAoB;QACpC,kBAAgB,EAAE,sBAAa;QAC/B,mBAAiB,EAAE,sBAAa,QAAQ,iBAAU;QACnD,IAAI,EAAC,MAAM;;;;KAKP,eAAQ;uBADhB,oBAQO;;UANJ,CAAC,EAAE,eAAM;UACT,CAAC,EAAE,eAAM;UACV,KAAsE,EAAtE,sEAAsE;UACrE,IAAI,UAAU,6BAAoB;4BAEhC,wBAAe,IAAG,SACvB", "sourcesContent": ["<script>\nlet id = 0;\n\nexport default {\n  props: {\n    percentage: {\n      type:    Number,\n      default: 0.75\n    },\n    strokeWidth: {\n      type:    Number,\n      default: 22\n    },\n    primaryStrokeColor: {\n      type:     String,\n      required: true\n    },\n    primaryStrokeGradientColor: {\n      type:    String,\n      default: null\n    },\n    secondaryStrokeColor: {\n      type:     String,\n      required: true\n    },\n    secondaryStrokeGradientColor: {\n      type:    String,\n      default: null\n    },\n    rotate: {\n      type:    Number,\n      default: 90\n    },\n    showText: {\n      type:    Boolean,\n      default: false\n    },\n  },\n  data() {\n    return { id: id++ };\n  },\n  computed: {\n    viewportSize() {\n      return 100;\n    },\n    radius() {\n      const outerRadius = this.viewportSize / 2;\n      const halfStrokeWidth = this.strokeWidth / 2;\n\n      return outerRadius - halfStrokeWidth;\n    },\n    center() {\n      return this.viewportSize / 2;\n    },\n    viewBox() {\n      return `0 0 ${ this.viewportSize } ${ this.viewportSize }`;\n    },\n    circumference() {\n      return 2 * Math.PI * this.radius;\n    },\n    transform() {\n      return `rotate(${ this.rotate }, ${ this.center }, ${ this.center })`;\n    },\n    strokeDasharray() {\n      // This needs to be the circumference of the circle in order to allow the path to be filled\n      return this.circumference;\n    },\n    strokeDashoffset() {\n      // This needs to be the percentage of the circumference that we won't show as it will hide that portion of the path\n      return this.circumference * (1 - this.percentage);\n    },\n    primaryStrokeColorId() {\n      return `primary-${ id }`;\n    },\n    secondaryStrokeColorId() {\n      return `secondary-${ id }`;\n    },\n    parsePercentage() {\n      return parseInt(this.percentage * 100) || 0;\n    },\n  }\n};\n\n</script>\n\n<template>\n  <svg\n    class=\"circle\"\n    width=\"100%\"\n    height=\"100%\"\n    :viewBox=\"viewBox\"\n  >\n    <g :transform=\"transform\">\n      <defs>\n        <linearGradient\n          :id=\"primaryStrokeColorId\"\n          x1=\"0%\"\n          y1=\"0%\"\n          x2=\"100%\"\n          y2=\"0%\"\n        >\n          <stop\n            offset=\"50%\"\n            :stop-color=\"primaryStrokeGradientColor || primaryStrokeColor\"\n          />\n          <stop\n            offset=\"100%\"\n            :stop-color=\"primaryStrokeColor\"\n          />\n        </linearGradient>\n        <linearGradient\n          :id=\"secondaryStrokeColorId\"\n          x1=\"0%\"\n          y1=\"0%\"\n          x2=\"100%\"\n          y2=\"0%\"\n        >\n          <stop\n            offset=\"50%\"\n            :stop-color=\"secondaryStrokeGradientColor || secondaryStrokeColor\"\n          />\n          <stop\n            offset=\"100%\"\n            :stop-color=\"secondaryStrokeColor\"\n          />\n        </linearGradient>\n      </defs>\n      <circle\n        :r=\"radius\"\n        :cy=\"center\"\n        :cx=\"center\"\n        :stroke-width=\"strokeWidth\"\n        :stroke=\"`url(#${secondaryStrokeColorId})`\"\n        fill=\"none\"\n      />\n      <circle\n        :r=\"radius\"\n        :cy=\"center\"\n        :cx=\"center\"\n        :stroke-width=\"strokeWidth\"\n        :stroke=\"`url(#${primaryStrokeColorId})`\"\n        :stroke-dasharray=\"circumference\"\n        :stroke-dashoffset=\"circumference * (1 - percentage)\"\n        fill=\"none\"\n      />\n    </g>\n\n    <text\n      v-if=\"showText\"\n      :x=\"center\"\n      :y=\"center\"\n      style=\"font-size: 25; dominant-baseline:  middle; text-anchor:middle;\"\n      :fill=\"`url(#${primaryStrokeColorId})`\"\n    >\n      {{ parsePercentage }}%\n    </text>\n  </svg>\n</template>\n\n<style lang=\"scss\" scoped>\nsvg.text {\n  fill: red\n}\n</style>\n"]}]}