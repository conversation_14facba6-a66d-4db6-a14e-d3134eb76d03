{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Yaml.vue?vue&type=template&id=4a649a0c", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Yaml.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Yaml.vue"], "names": ["question", "displayLabel", "displayTooltip", "mode", "disabled", "value", "$emit", "showDescription", "displayDescription"], "mappings": ";;;qBAqBS,KAAK,EAAC,YAAY;;;EAMjB,KAAK,EAAC,wBAAwB;;;;;;;;wBAVtC,oBA6BM;IA5BH,aAAW,cAAcA,aAAQ,CAAC,QAAQ;IAC3C,KAAK,EAAC,KAAK;;IAEX,oBAiBM,OAjBN,UAiBM;MAhBJ,oBAOK;0CANAC,iBAAY,IAAG,GAClB;SACQC,mBAAc;2CADtB,oBAIE,KAJF,UAIE;yCAFiBA,mBAAc;;;;;MAInC,aAOE;QANA,KAAK,EAAC,kBAAkB;QACvB,aAAW,EAAEC,SAAI,KAAK,UAAI;QAC1B,QAAQ,EAAEC,aAAQ;QAClB,KAAK,EAAEC,UAAK;QACZ,aAAW,gBAAgBL,aAAQ,CAAC,QAAQ;QAC5C,gBAAY,uCAAEM,UAAK,iBAAiB,MAAM;;;;KAIvCC,oBAAe;uBADvB,oBAMM;;UAJH,aAAW,sBAAsBP,aAAQ,CAAC,QAAQ;UACnD,KAAK,EAAC,kBAAkB;4BAErBQ,uBAAkB", "sourcesContent": ["<script>\nimport YamlEditor from '@shell/components/YamlEditor';\nimport Question from './Question';\nimport { _VIEW } from '@shell/config/query-params';\n\nexport default {\n  emits: ['update:value'],\n\n  components: { YamlEditor },\n  mixins:     [Question],\n  data() {\n    return { VIEW: _VIEW };\n  }\n};\n</script>\n\n<template>\n  <div\n    :data-testid=\"`yaml-row-${question.variable}`\"\n    class=\"row\"\n  >\n    <div class=\"col span-6\">\n      <h3>\n        {{ displayLabel }}\n        <i\n          v-if=\"displayTooltip\"\n          v-clean-tooltip=\"displayTooltip\"\n          class=\"icon icon-info icon-lg\"\n        />\n      </h3>\n      <YamlEditor\n        class=\"yaml-editor mb-6\"\n        :editor-mode=\"mode === VIEW ? 'VIEW_CODE' : 'EDIT_CODE'\"\n        :disabled=\"disabled\"\n        :value=\"value\"\n        :data-testid=\"`yaml-input-${question.variable}`\"\n        @update:value=\"$emit('update:value', $event)\"\n      />\n    </div>\n    <div\n      v-if=\"showDescription\"\n      :data-testid=\"`yaml-description-${question.variable}`\"\n      class=\"col span-6 mt-10\"\n    >\n      {{ displayDescription }}\n    </div>\n  </div>\n</template>\n"]}]}