{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Group.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Group.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Group.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;EAEpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAElD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAChC,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACxC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;QACzD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEhC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACjC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;;UAEvD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/D,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MACnB;IACF;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC3G,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAErD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC;MACJ;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAElB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE1C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACjE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;QAC7B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;;QAEb,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEvE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC;;QAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEhC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B;MACF;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB;;MAEA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;UACtJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3I,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE9C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACjF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb;QACF;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE9B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5E,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACrB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;cAE/C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;gBACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrC;YACF;UACF;QACF,CAAC,CAAC;MACJ;IACF;EACF;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Group.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport Type from '@shell/components/nav/Type';\nexport default {\n  name: 'Group',\n\n  components: { Type },\n\n  emits: ['expand', 'close'],\n\n  props: {\n    depth: {\n      type:    Number,\n      default: 0,\n    },\n\n    idPrefix: {\n      type:     String,\n      required: true,\n    },\n\n    group: {\n      type:     Object,\n      required: true,\n    },\n\n    childrenKey: {\n      type:    String,\n      default: 'children',\n    },\n\n    canCollapse: {\n      type:    Boolean,\n      default: true,\n    },\n\n    showHeader: {\n      type:    Boolean,\n      default: true,\n    },\n\n    fixedOpen: {\n      type:    Boolean,\n      default: false,\n    }\n  },\n\n  data() {\n    const id = (this.idPrefix || '') + this.group.name;\n\n    return { id, expanded: false };\n  },\n\n  computed: {\n    isGroupActive() {\n      return this.isOverview || (this.hasActiveRoute() && this.isExpanded && this.showHeader);\n    },\n\n    hasChildren() {\n      return this.group.children?.length > 0;\n    },\n\n    hasOverview() {\n      return this.group.children?.[0]?.overview;\n    },\n\n    onlyHasOverview() {\n      return this.group.children && this.group.children.length === 1 && this.hasOverview;\n    },\n\n    isOverview() {\n      if (this.group.children && this.group.children.length > 0) {\n        const grp = this.group.children[0];\n        const overviewRoute = grp?.route;\n\n        if (overviewRoute && grp.overview) {\n          const route = this.$router.resolve(overviewRoute || {});\n\n          return this.$route.fullPath.split('#')[0] === route?.fullPath;\n        }\n      }\n\n      return false;\n    },\n\n    isExpanded: {\n      get() {\n        return this.fixedOpen || this.group.isRoot || !!this.expanded;\n      },\n      set(v) {\n        this.expanded = v;\n      }\n    }\n  },\n\n  methods: {\n    expandGroup() {\n      this.isExpanded = true;\n      this.$emit('expand', this.group);\n    },\n\n    groupSelected() {\n      // Don't auto-select first group entry if we're already expanded and contain the currently-selected nav item\n      if (this.hasActiveRoute() && this.isExpanded) {\n        return;\n      } else {\n        // Remove all active class if click on group header and not active route\n        const headerEl = document.querySelectorAll('.header');\n\n        headerEl.forEach((el) => {\n          el.classList.remove('active');\n        });\n      }\n      this.expandGroup();\n\n      const items = this.group[this.childrenKey];\n\n      // Navigate to one of the child items (by default the first child)\n      if (items && items.length > 0) {\n        let index = 0;\n\n        // If there is a default type, use it\n        if (this.group.defaultType) {\n          const found = items.findIndex((i) => i.name === this.group.defaultType);\n\n          index = (found === -1) ? 0 : found;\n        }\n\n        const route = items[index].route;\n\n        if (route) {\n          this.$router.replace(route);\n        }\n      }\n    },\n\n    selectType() {\n      this.groupSelected();\n      this.close();\n    },\n\n    close() {\n      this.$emit('close');\n    },\n\n    // User clicked on the expander icon, so toggle the expansion so the user can see inside the group\n    peek($event) {\n      // Add active class to the current header if click on chevron icon\n      $event.target.parentElement.classList.remove('active');\n      if (this.hasActiveRoute() && this.isExpanded) {\n        $event.target.parentElement.classList.add('active');\n      }\n      this.isExpanded = !this.isExpanded;\n      $event.stopPropagation();\n    },\n\n    hasActiveRoute(items) {\n      if (!items) {\n        items = this.group;\n      }\n\n      for (const item of items.children) {\n        if (item.children && this.hasActiveRoute(item)) {\n          return true;\n        } else if (item.route) {\n          const navLevels = ['cluster', 'product', 'resource'];\n          const matchesNavLevel = navLevels.filter((param) => !this.$route.params[param] || this.$route.params[param] !== item.route.params[param]).length === 0;\n          const withoutHash = this.$route.hash ? this.$route.fullPath.slice(0, this.$route.fullPath.indexOf(this.$route.hash)) : this.$route.fullPath;\n          const withoutQuery = withoutHash.split('?')[0];\n\n          if (matchesNavLevel || this.$router.resolve(item.route).fullPath === withoutQuery) {\n            return true;\n          }\n        }\n      }\n\n      return false;\n    },\n\n    syncNav() {\n      const refs = this.$refs.groups;\n\n      if (refs) {\n        // Only expand one group - so after the first has been expanded, no more will\n        let canExpand = true;\n\n        refs.forEach((grp) => {\n          if (!grp.group.isRoot) {\n            if (canExpand) {\n              const isActive = this.hasActiveRoute(grp.group);\n\n              if (isActive) {\n                grp.isExpanded = true;\n                canExpand = false;\n                this.$nextTick(() => grp.syncNav());\n              }\n            }\n          }\n        });\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <div\n    class=\"accordion\"\n    :class=\"{[`depth-${depth}`]: true, 'expanded': isExpanded, 'has-children': hasChildren, 'group-highlight': isGroupActive}\"\n  >\n    <div\n      v-if=\"showHeader\"\n      class=\"header\"\n      :class=\"{'active': isOverview, 'noHover': !canCollapse}\"\n      role=\"button\"\n      tabindex=\"0\"\n      :aria-label=\"group.labelDisplay || group.label || ''\"\n      @click=\"groupSelected()\"\n      @keyup.enter=\"groupSelected()\"\n      @keyup.space=\"groupSelected()\"\n    >\n      <slot name=\"header\">\n        <router-link\n          v-if=\"hasOverview\"\n          :to=\"group.children[0].route\"\n          :exact=\"group.children[0].exact\"\n          :tabindex=\"-1\"\n        >\n          <h6>\n            <span v-clean-html=\"group.labelDisplay || group.label\" />\n          </h6>\n        </router-link>\n        <h6\n          v-else\n        >\n          <span v-clean-html=\"group.labelDisplay || group.label\" />\n        </h6>\n      </slot>\n      <i\n        v-if=\"!onlyHasOverview && canCollapse\"\n        class=\"icon toggle toggle-accordion\"\n        :class=\"{'icon-chevron-right': !isExpanded, 'icon-chevron-down': isExpanded}\"\n        role=\"button\"\n        tabindex=\"0\"\n        :aria-label=\"t('nav.ariaLabel.collapseExpand')\"\n        @click=\"peek($event, true)\"\n        @keyup.enter=\"peek($event, true)\"\n        @keyup.space=\"peek($event, true)\"\n      />\n    </div>\n    <ul\n      v-if=\"isExpanded\"\n      class=\"list-unstyled body\"\n      v-bind=\"$attrs\"\n    >\n      <template\n        v-for=\"(child, idx) in group[childrenKey]\"\n        :key=\"idx\"\n      >\n        <li\n          v-if=\"child.divider\"\n          :key=\"idx\"\n        >\n          <hr>\n        </li>\n        <!-- <div v-else-if=\"child[childrenKey] && hideGroup(child[childrenKey])\" :key=\"child.name\">\n          HIDDEN\n        </div> -->\n        <li\n          v-else-if=\"child[childrenKey]\"\n          :key=\"child.name\"\n        >\n          <Group\n            ref=\"groups\"\n            :key=\"id+'_'+child.name+'_children'\"\n            :id-prefix=\"id+'_'\"\n            :depth=\"depth + 1\"\n            :children-key=\"childrenKey\"\n            :can-collapse=\"canCollapse\"\n            :group=\"child\"\n            :fixed-open=\"fixedOpen\"\n            @selected=\"groupSelected($event)\"\n            @expand=\"expandGroup($event)\"\n            @close=\"close($event)\"\n          />\n        </li>\n        <Type\n          v-else-if=\"!child.overview || group.name === 'starred'\"\n          :key=\"id+'_' + child.name + '_type'\"\n          :is-root=\"depth == 0 && !showHeader\"\n          :type=\"child\"\n          :depth=\"depth\"\n          @selected=\"selectType($event)\"\n        />\n      </template>\n    </ul>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .header {\n    position: relative;\n    cursor: pointer;\n    color: var(--body-text);\n    height: 33px;\n    outline: none;\n\n    H6 {\n      color: var(--body-text);\n      user-select: none;\n      text-transform: none;\n      font-size: 14px;\n    }\n\n    > A {\n      display: block;\n      box-sizing:border-box;\n      height: 100%;\n      &:hover{\n        text-decoration: none;\n      }\n      &:focus{\n        outline:none;\n      }\n      > H6 {\n        text-transform: none;\n        padding: 8px 0 8px 16px;\n      }\n    }\n  }\n\n  .accordion {\n    .header {\n      &:focus-visible {\n        h6 span {\n          @include focus-outline;\n          outline-offset: 2px;\n        }\n      }\n      .toggle-accordion:focus-visible {\n        @include focus-outline;\n        outline-offset: -6px;\n      }\n\n      &.active {\n        color: var(--primary-hover-text);\n        background-color: var(--primary-hover-bg);\n\n        h6 {\n          padding: 8px 0 8px 16px;\n          font-weight: bold;\n          color: var(--primary-hover-text);\n        }\n\n        &:hover {\n          background-color: var(--primary-hover-bg);\n        }\n      }\n      &:hover:not(.active) {\n        background-color: var(--nav-hover);\n      }\n    }\n  }\n\n  .accordion {\n    &.depth-0 {\n      > .header {\n\n        &.noHover {\n          cursor: default;\n        }\n\n        > H6 {\n          text-transform: none;\n          padding: 8px 0 8px 16px;\n        }\n\n        > I {\n          position: absolute;\n          right: 0;\n          top: 0;\n          padding: 10px 10px 9px 7px;\n          user-select: none;\n        }\n      }\n\n      > .body {\n        margin-left: 0;\n      }\n\n      &.group-highlight {\n        background: var(--nav-active);\n      }\n    }\n\n    &.depth-1 {\n      > .header {\n        padding-left: 20px;\n        > H6 {\n          line-height: 18px;\n          padding: 8px 0 7px 5px !important;\n        }\n        > I {\n          padding: 10px 7px 9px 7px !important;\n        }\n      }\n\n      &:deep() .type-link > .label {\n        padding-left: 10px;\n      }\n    }\n\n    &:not(.depth-0) {\n      > .header {\n        > H6 {\n          // Child groups that aren't linked themselves\n          display: inline-block;\n          padding: 5px 0 5px 5px;\n        }\n\n        > I {\n          position: absolute;\n          right: 0;\n          top: 0;\n          padding: 6px 8px 6px 8px;\n        }\n      }\n    }\n  }\n\n  .body :deep() > .child.router-link-active,\n  .header :deep() > .child.router-link-exact-active {\n    padding: 0;\n\n    A, A I {\n      color: var(--primary-hover-text);\n    }\n\n    A {\n      color: var(--primary-hover-text);\n      background-color: var(--primary-hover-bg);\n      font-weight: bold;\n    }\n  }\n\n  .body :deep() > .child {\n    A {\n      border-left: solid 5px transparent;\n      line-height: 16px;\n      font-size: 14px;\n      padding-left: 24px;\n      display: flex;\n      justify-content: space-between;\n    }\n\n    A:focus {\n      outline: none;\n    }\n\n    &.root {\n      background: transparent;\n      A {\n        padding-left: 14px;\n      }\n    }\n  }\n</style>\n"]}]}