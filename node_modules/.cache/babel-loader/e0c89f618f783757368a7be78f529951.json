{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??clonedRuleSet-44.use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/LabeledTooltip/LabeledTooltip.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/LabeledTooltip/LabeledTooltip.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgZGVmaW5lQ29tcG9uZW50IH0gZnJvbSAndnVlJzsKZXhwb3J0IGRlZmF1bHQgZGVmaW5lQ29tcG9uZW50KHsKICAgIHByb3BzOiB7CiAgICAgICAgLyoqCiAgICAgICAgICogVGhlIExhYmVsZWQgVG9vbHRpcCB2YWx1ZS4KICAgICAgICAgKi8KICAgICAgICB2YWx1ZTogewogICAgICAgICAgICB0eXBlOiBbU3RyaW5nLCBPYmplY3RdLAogICAgICAgICAgICBkZWZhdWx0OiBudWxsCiAgICAgICAgfSwKICAgICAgICAvKioKICAgICAgICAgKiBUaGUgc3RhdHVzIGZvciB0aGUgTGFiZWxlZCBUb29sdGlwLiBDb250cm9scyB0aGUgTGFiZWxlZCBUb29sdGlwIGNsYXNzLgogICAgICAgICAqIEB2YWx1ZXMgaW5mbywgc3VjY2Vzcywgd2FybmluZywgZXJyb3IKICAgICAgICAgKi8KICAgICAgICBzdGF0dXM6IHsKICAgICAgICAgICAgdHlwZTogU3RyaW5nLAogICAgICAgICAgICBkZWZhdWx0OiAnZXJyb3InCiAgICAgICAgfSwKICAgICAgICAvKioKICAgICAgICAgKiBEaXNwbGF5cyB0aGUgTGFiZWxlZCBUb29sdGlwIG9uIG1vdXNlIGhvdmVyLgogICAgICAgICAqLwogICAgICAgIGhvdmVyOiB7CiAgICAgICAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgICAgICAgIGRlZmF1bHQ6IHRydWUKICAgICAgICB9CiAgICB9LAogICAgY29tcHV0ZWQ6IHsKICAgICAgICBpY29uQ2xhc3MoKSB7CiAgICAgICAgICAgIHJldHVybiB0aGlzLnN0YXR1cyA9PT0gJ2Vycm9yJyA/ICdpY29uLXdhcm5pbmcnIDogJ2ljb24taW5mbyc7CiAgICAgICAgfSwKICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L25vLWV4cGxpY2l0LWFueQogICAgICAgIHRvb2x0aXBDb250ZW50KCkgewogICAgICAgICAgICBpZiAodGhpcy5pc09iamVjdCh0aGlzLnZhbHVlKSkgewogICAgICAgICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgICAgICAgICAuLi57IGNvbnRlbnQ6IHRoaXMudmFsdWUuY29udGVudCwgcG9wcGVyQ2xhc3M6IFtgdG9vbHRpcC0ke3N0YXR1c31gXSB9LCAuLi50aGlzLnZhbHVlLCB0cmlnZ2VyczogWydob3ZlcicsICd0b3VjaCcsICdmb2N1cyddCiAgICAgICAgICAgICAgICB9OwogICAgICAgICAgICB9CiAgICAgICAgICAgIHJldHVybiB0aGlzLnZhbHVlID8geyBjb250ZW50OiB0aGlzLnZhbHVlLCB0cmlnZ2VyczogWydob3ZlcicsICd0b3VjaCcsICdmb2N1cyddIH0gOiAnJzsKICAgICAgICB9CiAgICB9LAogICAgbWV0aG9kczogewogICAgICAgIGlzT2JqZWN0KHZhbHVlKSB7CiAgICAgICAgICAgIHJldHVybiB0eXBlb2YgdmFsdWUgPT09ICdvYmplY3QnICYmIHZhbHVlICE9PSBudWxsICYmICEhdmFsdWUuY29udGVudDsKICAgICAgICB9CiAgICB9Cn0pOwo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/LabeledTooltip/LabeledTooltip.vue"], "names": [], "mappings": "AACA,OAAO,EAAE,eAAc,EAAE,MAAO,KAAK,CAAA;AAErC,eAAe,eAAe,CAAC;IAC7B,KAAK,EAAE;QACL;;WAEE;QACF,KAAK,EAAE;YACL,IAAI,EAAK,CAAC,MAAM,EAAE,MAAM,CAAC;YACzB,OAAO,EAAE,IAAG;SACb;QAED;;;WAGE;QACF,MAAM,EAAE;YACN,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,OAAM;SAChB;QAED;;WAEE;QACF,KAAK,EAAE;YACL,IAAI,EAAK,OAAO;YAChB,OAAO,EAAE,IAAG;SACd;KACD;IACD,QAAQ,EAAE;QACR,SAAS;YACP,OAAO,IAAI,CAAC,MAAK,KAAM,OAAM,CAAE,CAAA,CAAE,cAAa,CAAE,CAAA,CAAE,WAAW,CAAA;QAC/D,CAAC;QACD,8DAA6D;QAC7D,cAAc;YACZ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAA;gBAC7B,OAAO;oBACL,GAAG,EAAE,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,WAAW,EAAE,CAAC,WAAY,MAAO,EAAE,CAAA,EAAG,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAA;iBAC9H,CAAA;YACH,CAAA;YAEA,OAAO,IAAI,CAAC,KAAI,CAAE,CAAA,CAAE,EAAE,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAA,EAAE,CAAE,CAAA,CAAE,EAAE,CAAA;QACzF,CAAA;KACD;IACD,OAAO,EAAE;QACP,QAAQ,CAAC,KAAuC;YAC9C,OAAO,OAAO,KAAI,KAAM,QAAO,IAAK,KAAI,KAAM,IAAG,IAAK,CAAC,CAAC,KAAK,CAAC,OAAO,CAAA;QACvE,CAAA;KACF;CACD,CAAC,CAAA", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/LabeledTooltip/LabeledTooltip.vue.tsx", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent } from 'vue';\n\nexport default defineComponent({\n  props: {\n    /**\n     * The Labeled Tooltip value.\n     */\n    value: {\n      type:    [String, Object],\n      default: null\n    },\n\n    /**\n     * The status for the Labeled Tooltip. Controls the Labeled Tooltip class.\n     * @values info, success, warning, error\n     */\n    status: {\n      type:    String,\n      default: 'error'\n    },\n\n    /**\n     * Displays the Labeled Tooltip on mouse hover.\n     */\n    hover: {\n      type:    Boolean,\n      default: true\n    }\n  },\n  computed: {\n    iconClass(): string {\n      return this.status === 'error' ? 'icon-warning' : 'icon-info';\n    },\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    tooltipContent(): {[key: string]: any} | string {\n      if (this.isObject(this.value)) {\n        return {\n          ...{ content: this.value.content, popperClass: [`tooltip-${ status }`] }, ...this.value, triggers: ['hover', 'touch', 'focus']\n        };\n      }\n\n      return this.value ? { content: this.value, triggers: ['hover', 'touch', 'focus'] } : '';\n    }\n  },\n  methods: {\n    isObject(value: string | Record<string, unknown>): value is Record<string, unknown> {\n      return typeof value === 'object' && value !== null && !!value.content;\n    }\n  }\n});\n</script>\n\n<template>\n  <div\n    ref=\"container\"\n    class=\"labeled-tooltip\"\n    :class=\"{[status]: true, hoverable: hover}\"\n  >\n    <template v-if=\"hover\">\n      <i\n        v-clean-tooltip=\"tooltipContent\"\n        v-stripped-aria-label=\"isObject(value) ? value.content : value\"\n        :class=\"{'hover':!value, [iconClass]: true}\"\n        class=\"icon status-icon\"\n        tabindex=\"0\"\n      />\n    </template>\n    <template v-else>\n      <i\n        :class=\"{'hover':!value}\"\n        class=\"icon status-icon\"\n      />\n      <div\n        v-if=\"value\"\n        class=\"tooltip\"\n        x-placement=\"bottom\"\n      >\n        <div class=\"tooltip-arrow\" />\n        <div class=\"tooltip-inner\">\n          {{ value }}\n        </div>\n      </div>\n    </template>\n  </div>\n</template>\n\n<style lang='scss'>\n.labeled-tooltip {\n    position: absolute;\n    width: 100%;\n    height: 100%;\n    left: 0;\n    top: 0;\n\n    &.hoverable {\n      height: 0%;\n    }\n\n     .status-icon {\n        position:  absolute;\n        right: 30px;\n        top: $input-padding-lg;\n        z-index: z-index(hoverOverContent);\n     }\n\n    @mixin tooltipColors($color) {\n        .status-icon {\n            color: $color;\n        }\n    }\n\n    &.error {\n        @include tooltipColors(var(--error));\n\n        .status-icon {\n          top: 7px;\n          right: 5px;\n        }\n    }\n\n    &.warning {\n        @include tooltipColors(var(--warning));\n    }\n\n    &.success {\n        @include tooltipColors(var(--success));\n    }\n}\n\n// Ensure code blocks inside tootips don't look awful\n.v-popper__popper.v-popper--theme-tooltip {\n  .v-popper__inner {\n    pre {\n      padding: 2px;\n      vertical-align: middle;\n    }\n  }\n}\n</style>\n"]}]}