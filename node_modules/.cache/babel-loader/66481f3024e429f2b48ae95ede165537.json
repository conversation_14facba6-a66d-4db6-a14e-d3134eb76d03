{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??clonedRuleSet-44.use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/TopLevelMenu.helper.ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/TopLevelMenu.helper.ts", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/TopLevelMenu.helper.ts", "sourceRoot": "", "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/TopLevelMenu.helper.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACvD,OAAO,EAAmB,qBAAqB,EAAkB,MAAM,qCAAqC,CAAC;AAE7G,OAAO,EAAE,wBAAwB,EAAE,4BAA4B,EAAE,wBAAwB,EAAE,MAAM,sBAAsB,CAAC;AACxH,OAAO,iBAAiB,MAAM,iCAAiC,CAAC;AAChE,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/C,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAgC3C;;;;;GAKG;AACH,MAAM,YAAY,GAA0B;IAC1C;QACE,GAAG,EAAI,KAAK;QACZ,KAAK,EAAE,eAAe;KACvB;IACD;QACE,GAAG,EAAI,KAAK;QACZ,KAAK,EAAE,kBAAkB;KAC1B;IACD;QACE,GAAG,EAAI,IAAI;QACX,KAAK,EAAE,kBAAkB;KAC1B;CACF,CAAC;AAkCF,MAAM,OAAgB,sBAAsB;IAkC1C,YAAY,EAAE,MAAM,EAErB;QAhCC;;;;;;;;;;;;UAYE;QACK,mBAAc,GAA+B,EAAE,CAAC;QAEvD;;;;;;;;;;;;UAYE;QACK,mBAAc,GAA+B,EAAE,CAAC;QAKrD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAExF,mEAAmE;QACnE,MAAM,EAAE,cAAc,GAAG,EAAE,EAAE,cAAc,GAAG,EAAE,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;QAE/F,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAC;QAC5C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAC;IAC9C,CAAC;IAES,gBAAgB,CAAC,WAAwB,EAAE,WAAwB;QAC3E,OAAO;YACL,EAAE,EAAe,WAAW,CAAC,EAAE;YAC/B,KAAK,EAAY,WAAW,CAAC,WAAW;YACxC,KAAK,EAAY,WAAW,CAAC,OAAO,EAAE,6BAA6B;YACnE,eAAe,EAAE,WAAW,CAAC,gBAAgB;YAC7C,KAAK,EAAY,WAAW,CAAC,KAAK;YAClC,SAAS,EAAQ,WAAW,CAAC,SAAS;YACtC,OAAO,EAAU,WAAW,CAAC,OAAO;YACpC,MAAM,EAAW,WAAW,CAAC,MAAM;YACnC,WAAW,EAAM,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,WAAW,KAAI,WAAW,CAAC,WAAW;YACpE,GAAG,EAAc,GAAG,EAAE,CAAC,WAAW,CAAC,GAAG,EAAE;YACxC,KAAK,EAAY,GAAG,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE;YAC1C,YAAY,EAAK,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,WAAW,CAAC,EAAE,EAAE,EAAE;SACrF,CAAC;IACJ,CAAC;IAES,aAAa;QACrB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,iBAAiB,EAAE,EAAE,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IACxH,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,4BAA6B,SAAQ,sBAAsB;IAStE,YAAY,EAAE,MAAM,EAEnB;QACC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;QAElB,IAAI,CAAC,oBAAoB,GAAG,wBAAwB,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;QAEvF,IAAI,CAAC,qBAAqB,GAAG,IAAI,iBAAiB,CAAC;YACjD,MAAM;YACN,QAAQ,EAAE,GAAG,EAAE;gBACb,0EAA0E;gBAC1E,sGAAsG;gBACtG,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;oBACd,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzB,CAAC;YACH,CAAC;YACD,UAAU,EAAE;gBACV,KAAK,EAAK,YAAY;gBACtB,QAAQ,EAAE;oBACR,EAAE,EAAO,UAAU,CAAC,OAAO;oBAC3B,OAAO,EAAE,UAAU;iBACpB;aACF;SACF,CAAC,CAAC;QACH,IAAI,CAAC,qBAAqB,GAAG,IAAI,iBAAiB,CAAC;YACjD,MAAM;YACN,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE;gBAChB,0EAA0E;gBAC1E,sGAAsG;gBACtG,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;oBACd,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzB,CAAC;YACH,CAAC;YACD,UAAU,EAAE;gBACV,KAAK,EAAK,YAAY;gBACtB,QAAQ,EAAE;oBACR,EAAE,EAAO,UAAU,CAAC,OAAO;oBAC3B,OAAO,EAAE,UAAU;iBACpB;aACF;SACF,CAAC,CAAC;QACH,IAAI,CAAC,kBAAkB,GAAG,IAAI,iBAAiB,CAAC;YAC9C,MAAM;YACN,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE;gBAChB,0EAA0E;gBAC1E,sGAAsG;gBACtG,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;oBACd,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzB,CAAC;YACH,CAAC;YACD,UAAU,EAAE;gBACV,KAAK,EAAK,YAAY;gBACtB,QAAQ,EAAE;oBACR,EAAE,EAAO,IAAI,CAAC,eAAe;oBAC7B,OAAO,EAAE,UAAU;iBACpB;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAED,iCAAiC;IACjC,KAAK,CAAC,MAAM,CAAC,IAAgB;QAC3B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,6GAA6G;YAC7G,aAAa;YACb,OAAO;QACT,CAAC;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,MAAM,QAAQ,GAAG;YACf,MAAM,EAAK,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;YAClC,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;SACnC,CAAC;QAEF,MAAM,GAAG,GAGL,MAAM,OAAO,CAAC,QAAQ,CAAQ,CAAC;QACnC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;QAC7E,MAAM,oBAAoB,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAqC,EAAE,WAAwB,EAAE,EAAE;YACnH,IAAI,WAAW,CAAC,aAAa,EAAE,CAAC;gBAC9B,GAAG,CAAC,WAAW,CAAC,aAAa,CAAC,GAAG,WAAW,CAAC;YAC/C,CAAC;YAED,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAsC,CAAC,CAAC;QAE3C,MAAM,kBAAkB,GAAG,GAAG,CAAC,SAAS;aACrC,MAAM,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC,oBAAoB,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;aAC/D,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,oBAAoB,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAClG,MAAM,eAAe,GAAG,GAAG,CAAC,MAAM;aAC/B,MAAM,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC,oBAAoB,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;aAC/D,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,oBAAoB,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAElG,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;QAC/B,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;QAE/B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,CAAC;QAC7C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,kBAAkB,CAAC,CAAC;QAEhD,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAEO,eAAe,CAAC,EACtB,SAAS,EACT,UAAU,EACV,YAAY,EACZ,iBAAiB,EACjB,aAAa,EACb,aAAa,GAQd;QACC,MAAM,OAAO,GAAsB,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAElE,IAAI,SAAS,EAAE,CAAC;YACd,IAAI,aAAa,EAAE,CAAC;gBAClB,oCAAoC;gBACpC,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CACrD,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;oBACrB,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI;iBAClD,CAAC,CAAC,CACJ,CAAC,CAAC;YACL,CAAC;YAED,IAAI,aAAa,EAAE,CAAC;gBAClB,uDAAuD;gBACvD,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,qBAAqB,CAAC,iBAAiB,CAAC;oBAC5E,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;iBACtC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC;QACH,CAAC;QAED,IAAI,UAAU,IAAI,iBAAiB,EAAE,CAAC;YACpC,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,CAAC;gBACnD,KAAK,EAAE,kBAAkB,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU;aAC3D,CAAC,CAAC,CAAC;QACN,CAAC;QAED,IAAI,YAAY,EAAE,CAAC;YACjB,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;QACzF,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY,CAAC,IAAgB;;QACzC,IAAI,CAAA,MAAA,IAAI,CAAC,SAAS,0CAAE,MAAM,IAAG,CAAC,EAAE,CAAC;YAC/B,yDAAyD;YACzD,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC7B,CAAC;QAED,OAAO,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;YACxC,UAAU,EAAE;gBACV,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC;oBAC5B,SAAS,EAAM,IAAI,CAAC,SAAS;oBAC7B,aAAa,EAAE,IAAI;iBACpB,CAAC;gBACF,IAAI,EAAkB,CAAC;gBACvB,IAAI,EAAkB,YAAY;gBAClC,oBAAoB,EAAE,EAAE;aACzB;YACD,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY,CAAC,IAAgB;QACzC,OAAO,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;YACxC,UAAU,EAAE;gBACV,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC;oBAC5B,UAAU,EAAS,IAAI,CAAC,UAAU;oBAClC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU;oBACpC,SAAS,EAAU,IAAI,CAAC,SAAS;oBACjC,aAAa,EAAM,CAAC,IAAI,CAAC,UAAU;iBACpC,CAAC;gBACF,IAAI,EAAkB,CAAC;gBACvB,QAAQ,EAAc,IAAI,CAAC,WAAW;gBACtC,IAAI,EAAkB,YAAY;gBAClC,oBAAoB,EAAE,EAAE;aACzB;YACD,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,SAAwB,EAAE,MAAqB;QAC7E,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;YACrC,UAAU,EAAE;gBAEV,OAAO,EAAE;oBACP,qBAAqB,CAAC,oBAAoB,CACxC,CAAC,GAAG,SAAS,EAAE,GAAG,MAAM,CAAC;yBACtB,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;wBACrB,KAAK,EAAE,oBAAoB,EAAE,KAAK,EAAE,WAAW,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI;qBAC9E,CAAC,CAAC,CACN;iBACF;gBAED,IAAI,EAAkB,CAAC;gBACvB,IAAI,EAAkB,EAAE;gBACxB,oBAAoB,EAAE,EAAE;aACzB;YACD,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,wBAAyB,SAAQ,sBAAsB;IAClE,YAAY,EAAE,MAAM,EAEnB;QACC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;QAElB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,MAAM,CAAC,QAAQ,CAAC,oBAAoB,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,IAAgB;QAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACvC,MAAM,kBAAkB,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QACjE,MAAM,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAEzD,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;QAC/B,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;QAE/B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,CAAC;QAC7C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,kBAAkB,CAAC,CAAC;QAEhD,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAED;;;;;;;OAOG;IACK,cAAc;QACpB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,6GAA6G;YAC7G,aAAa;YACb,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACtE,MAAM,YAAY,GAAG,wBAAwB,CAAC,4BAA4B,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3G,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACjF,MAAM,oBAAoB,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAQ,EAAE,WAAwB,EAAE,EAAE;;YACtF,IAAI,MAAA,WAAW,CAAC,IAAI,0CAAE,EAAE,EAAE,CAAC;gBACzB,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC;YACzC,CAAC;YAED,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;QAEP,OAAO,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,GAAQ,EAAE,WAAwB,EAAE,EAAE;YACxE,uFAAuF;YACvF,oGAAoG;YACpG,wCAAwC;YACxC,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC;gBAC1C,OAAO,GAAG,CAAC;YACb,CAAC;YAED,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,oBAAoB,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAEnF,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAED;;;;;;;;;;OAUG;IACK,gBAAgB,CAAC,QAA+B,EAAE,IAAgB;QACxE,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC;QACtC,MAAM,iBAAiB,GAAG,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC;QAEjD,MAAM,MAAM,GAAG,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;QACnD,IAAI,YAAY,GAAuB,IAAI,CAAC;QAE5C,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE;;YACrC,oDAAoD;YACpD,IAAI,MAAM,EAAE,CAAC;gBACX,IAAI,CAAC,CAAA,MAAA,CAAC,CAAC,KAAK,0CAAE,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAA,EAAE,CAAC;oBAC7C,OAAO,KAAK,CAAC;gBACf,CAAC;YACH,CAAC;iBAAM,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC;gBACpB,wCAAwC;gBACxC,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,EAAE,KAAK,OAAO,EAAE,CAAC;gBACtC,4FAA4F;gBAC5F,YAAY,GAAG,CAAC,CAAC;gBAEjB,OAAO,KAAK,CAAC;YACf,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC,CAAC;QAEzD,8FAA8F;QAC9F,IAAI,YAAY,EAAE,CAAC;YACjB,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAC/B,CAAC;QAED,IAAI,MAAM,EAAE,CAAC;YACX,gCAAgC;YAChC,0CAA0C;YAE1C,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,+BAA+B;QAC/B,6BAA6B;QAE7B,IAAI,MAAM,CAAC,MAAM,IAAI,iBAAiB,EAAE,CAAC;YACvC,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;QAC5C,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;;;;;OAUG;IACK,WAAW,CAAC,QAA+B,EAAE,IAAgB;QACnE,IAAI,YAAY,GAAG,IAAI,CAAC;QACxB,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE;YACrC,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;gBACd,qCAAqC;gBACrC,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,CAAC,CAAC,EAAE,KAAK,OAAO,EAAE,CAAC;gBACrB,mEAAmE;gBACnE,YAAY,GAAG,CAAC,CAAC;gBAEjB,OAAO,KAAK,CAAC;YACf,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC,CAAC;QAEzD,8FAA8F;QAC9F,IAAI,YAAY,EAAE,CAAC;YACjB,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAC/B,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;CACF", "sourcesContent": ["import { CAPI, MANAGEMENT } from '@shell/config/types';\nimport { PaginationParam, PaginationParamFilter, PaginationSort } from '@shell/types/store/pagination.types';\nimport { VuexStore } from '@shell/types/store/vuex';\nimport { filterHiddenLocalCluster, filterOnlyKubernetesClusters, paginationFilterClusters } from '@shell/utils/cluster';\nimport PaginationWrapper from '@shell/utils/pagination-wrapper';\nimport { allHash } from '@shell/utils/promise';\nimport { sortBy } from '@shell/utils/sort';\nimport { LocationAsRelativeRaw } from 'vue-router';\n\ninterface TopLevelMenuCluster {\n  id: string,\n  label: string,\n  ready: boolean\n  providerNavLogo: string,\n  badge: string,\n  iconColor: string,\n  isLocal: boolean,\n  pinned: boolean,\n  description: string,\n  pin: () => void,\n  unpin: () => void,\n  clusterRoute: LocationAsRelativeRaw,\n}\n\ninterface UpdateArgs {\n  searchTerm: string,\n  pinnedIds: string[],\n  unPinnedMax?: number,\n}\n\ntype MgmtCluster = {\n  [key: string]: any\n}\n\ntype ProvCluster = {\n  [key: string]: any\n}\n\n/**\n * Order of v1 mgmt clusters\n * 1. local cluster - https://github.com/rancher/dashboard/issues/10975\n * 2. working clusters\n * 3. name\n */\nconst DEFAULT_SORT: Array<PaginationSort> = [\n  {\n    asc:   false,\n    field: 'spec.internal',\n  },\n  {\n    asc:   false,\n    field: 'status.connected'\n  },\n  {\n    asc:   true,\n    field: 'spec.displayName',\n  },\n];\n\nexport interface TopLevelMenuHelper {\n  /**\n  * Filter mgmt clusters by\n  * 1. If harvester or not (filterOnlyKubernetesClusters)\n  * 2. If local or not (filterHiddenLocalCluster)\n  * 3. Is pinned\n  *\n  * Sort By\n  * 1. is local cluster (appears at top)\n  * 2. ready\n  * 3. name\n  */\n  clustersPinned: Array<TopLevelMenuCluster>;\n\n  /**\n  * Filter mgmt clusters by\n  * 1. If harvester or not (filterOnlyKubernetesClusters)\n  * 2. If local or not (filterHiddenLocalCluster)\n  * 3.\n  * a) if search term, filter on it\n  * b) if no search term, filter on pinned\n  *\n  * Sort By\n  * 1. is local cluster (appears at top)\n  * 2. ready\n  * 3. name\n  */\n  clustersOthers: Array<TopLevelMenuCluster>;\n\n  update: (args: UpdateArgs) => Promise<void>\n}\n\nexport abstract class BaseTopLevelMenuHelper {\n  protected $store: VuexStore;\n  protected hasProvCluster: boolean;\n\n  /**\n  * Filter mgmt clusters by\n  * 1. If harvester or not (filterOnlyKubernetesClusters)\n  * 2. If local or not (filterHiddenLocalCluster)\n  * 3. Is pinned\n  *\n  * Why aren't we filtering these by search term? Because we don't show pinned when filtering on search term\n  *\n  * Sort By\n  * 1. is local cluster (appears at top)\n  * 2. ready\n  * 3. name\n  */\n  public clustersPinned: Array<TopLevelMenuCluster> = [];\n\n  /**\n  * Filter mgmt clusters by\n  * 1. If harvester or not (filterOnlyKubernetesClusters)\n  * 2. If local or not (filterHiddenLocalCluster)\n  * 3.\n  * a) if search term, filter on it\n  * b) if no search term, filter on pinned\n  *\n  * Sort By\n  * 1. is local cluster (appears at top)\n  * 2. ready\n  * 3. name\n  */\n  public clustersOthers: Array<TopLevelMenuCluster> = [];\n\n  constructor({ $store }: {\n    $store: VuexStore,\n}) {\n    this.$store = $store;\n\n    this.hasProvCluster = this.$store.getters[`management/schemaFor`](CAPI.RANCHER_CLUSTER);\n\n    // Reduce flicker when component is recreated on a different layout\n    const { clustersPinned = [], clustersOthers = [] } = this.$store.getters['sideNavCache'] || {};\n\n    this.clustersPinned.push(...clustersPinned);\n    this.clustersOthers.push(...clustersOthers);\n  }\n\n  protected convertToCluster(mgmtCluster: MgmtCluster, provCluster: ProvCluster): TopLevelMenuCluster {\n    return {\n      id:              mgmtCluster.id,\n      label:           mgmtCluster.nameDisplay,\n      ready:           mgmtCluster.isReady, // && !provCluster?.hasError,\n      providerNavLogo: mgmtCluster.providerMenuLogo,\n      badge:           mgmtCluster.badge,\n      iconColor:       mgmtCluster.iconColor,\n      isLocal:         mgmtCluster.isLocal,\n      pinned:          mgmtCluster.pinned,\n      description:     provCluster?.description || mgmtCluster.description,\n      pin:             () => mgmtCluster.pin(),\n      unpin:           () => mgmtCluster.unpin(),\n      clusterRoute:    { name: 'c-cluster-explorer', params: { cluster: mgmtCluster.id } }\n    };\n  }\n\n  protected cacheClusters() {\n    this.$store.dispatch('setSideNavCache', { clustersPinned: this.clustersPinned, clustersOthers: this.clustersOthers });\n  }\n}\n\n/**\n * Helper designed to supply paginated results for the top level menu cluster resources\n */\nexport class TopLevelMenuHelperPagination extends BaseTopLevelMenuHelper implements TopLevelMenuHelper {\n  private args?: UpdateArgs;\n\n  private clustersPinnedWrapper: PaginationWrapper;\n  private clustersOthersWrapper: PaginationWrapper;\n  private provClusterWrapper: PaginationWrapper;\n\n  private commonClusterFilters: PaginationParam[];\n\n  constructor({ $store }: {\n      $store: VuexStore,\n  }) {\n    super({ $store });\n\n    this.commonClusterFilters = paginationFilterClusters({ getters: this.$store.getters });\n\n    this.clustersPinnedWrapper = new PaginationWrapper({\n      $store,\n      onUpdate: () => {\n        // trigger on websocket update (only need 1 trigger for this cluster type)\n        // https://github.com/rancher/rancher/issues/40773 / https://github.com/rancher/dashboard/issues/12734\n        if (this.args) {\n          this.update(this.args);\n        }\n      },\n      enabledFor: {\n        store:    'management',\n        resource: {\n          id:      MANAGEMENT.CLUSTER,\n          context: 'side-bar',\n        }\n      }\n    });\n    this.clustersOthersWrapper = new PaginationWrapper({\n      $store,\n      onUpdate: (res) => {\n        // trigger on websocket update (only need 1 trigger for this cluster type)\n        // https://github.com/rancher/rancher/issues/40773 / https://github.com/rancher/dashboard/issues/12734\n        if (this.args) {\n          this.update(this.args);\n        }\n      },\n      enabledFor: {\n        store:    'management',\n        resource: {\n          id:      MANAGEMENT.CLUSTER,\n          context: 'side-bar',\n        }\n      }\n    });\n    this.provClusterWrapper = new PaginationWrapper({\n      $store,\n      onUpdate: (res) => {\n        // trigger on websocket update (only need 1 trigger for this cluster type)\n        // https://github.com/rancher/rancher/issues/40773 / https://github.com/rancher/dashboard/issues/12734\n        if (this.args) {\n          this.update(this.args);\n        }\n      },\n      enabledFor: {\n        store:    'management',\n        resource: {\n          id:      CAPI.RANCHER_CLUSTER,\n          context: 'side-bar',\n        }\n      }\n    });\n  }\n\n  // ---------- requests ----------\n  async update(args: UpdateArgs) {\n    if (!this.hasProvCluster) {\n      // We're filtering out mgmt clusters without prov clusters, so if the user can't see any prov clusters at all\n      // exit early\n      return;\n    }\n\n    this.args = args;\n    const promises = {\n      pinned:    this.updatePinned(args),\n      notPinned: this.updateOthers(args)\n    };\n\n    const res: {\n      pinned: MgmtCluster[],\n      notPinned: MgmtCluster[]\n    } = await allHash(promises) as any;\n    const provClusters = await this.updateProvCluster(res.notPinned, res.pinned);\n    const provClustersByMgmtId = provClusters.reduce((res: { [mgmtId: string]: ProvCluster}, provCluster: ProvCluster) => {\n      if (provCluster.mgmtClusterId) {\n        res[provCluster.mgmtClusterId] = provCluster;\n      }\n\n      return res;\n    }, {} as { [mgmtId: string]: ProvCluster});\n\n    const _clustersNotPinned = res.notPinned\n      .filter((mgmtCluster) => !!provClustersByMgmtId[mgmtCluster.id])\n      .map((mgmtCluster) => this.convertToCluster(mgmtCluster, provClustersByMgmtId[mgmtCluster.id]));\n    const _clustersPinned = res.pinned\n      .filter((mgmtCluster) => !!provClustersByMgmtId[mgmtCluster.id])\n      .map((mgmtCluster) => this.convertToCluster(mgmtCluster, provClustersByMgmtId[mgmtCluster.id]));\n\n    this.clustersPinned.length = 0;\n    this.clustersOthers.length = 0;\n\n    this.clustersPinned.push(..._clustersPinned);\n    this.clustersOthers.push(..._clustersNotPinned);\n\n    this.cacheClusters();\n  }\n\n  private constructParams({\n    pinnedIds,\n    searchTerm,\n    includeLocal,\n    includeSearchTerm,\n    includePinned,\n    excludePinned,\n  }: {\n    pinnedIds?: string[],\n    searchTerm?: string,\n    includeLocal?: boolean,\n    includeSearchTerm?: boolean,\n    includePinned?: boolean,\n    excludePinned?: boolean,\n  }): PaginationParam[] {\n    const filters: PaginationParam[] = [...this.commonClusterFilters];\n\n    if (pinnedIds) {\n      if (includePinned) {\n        // cluster id is 1 OR 2 OR 3 OR 4...\n        filters.push(PaginationParamFilter.createMultipleFields(\n          pinnedIds.map((id) => ({\n            field: 'id', value: id, equals: true, exact: true\n          }))\n        ));\n      }\n\n      if (excludePinned) {\n        // cluster id is NOT 1 AND NOT 2 AND NOT 3 AND NOT 4...\n        filters.push(...pinnedIds.map((id) => PaginationParamFilter.createSingleField({\n          field: 'id', equals: false, value: id\n        })));\n      }\n    }\n\n    if (searchTerm && includeSearchTerm) {\n      filters.push(PaginationParamFilter.createSingleField({\n        field: 'spec.displayName', exact: false, value: searchTerm\n      }));\n    }\n\n    if (includeLocal) {\n      filters.push(PaginationParamFilter.createSingleField({ field: 'id', value: 'local' }));\n    }\n\n    return filters;\n  }\n\n  /**\n   * See `clustersPinned` description for details\n   */\n  private async updatePinned(args: UpdateArgs): Promise<MgmtCluster[]> {\n    if (args.pinnedIds?.length < 1) {\n      // Return early, otherwise we're fetching all clusters...\n      return Promise.resolve([]);\n    }\n\n    return this.clustersPinnedWrapper.request({\n      pagination: {\n        filters: this.constructParams({\n          pinnedIds:     args.pinnedIds,\n          includePinned: true,\n        }),\n        page:                 1,\n        sort:                 DEFAULT_SORT,\n        projectsOrNamespaces: []\n      },\n      classify: true,\n    }).then((r) => r.data);\n  }\n\n  /**\n   * See `clustersOthers` description for details\n   */\n  private async updateOthers(args: UpdateArgs): Promise<MgmtCluster[]> {\n    return this.clustersOthersWrapper.request({\n      pagination: {\n        filters: this.constructParams({\n          searchTerm:        args.searchTerm,\n          includeSearchTerm: !!args.searchTerm,\n          pinnedIds:         args.pinnedIds,\n          excludePinned:     !args.searchTerm,\n        }),\n        page:                 1,\n        pageSize:             args.unPinnedMax,\n        sort:                 DEFAULT_SORT,\n        projectsOrNamespaces: []\n      },\n      classify: true,\n    }).then((r) => r.data);\n  }\n\n  /**\n   * Find all provisioning clusters associated with the displayed mgmt clusters\n   */\n  private async updateProvCluster(notPinned: MgmtCluster[], pinned: MgmtCluster[]): Promise<ProvCluster[]> {\n    return this.provClusterWrapper.request({\n      pagination: {\n\n        filters: [\n          PaginationParamFilter.createMultipleFields(\n            [...notPinned, ...pinned]\n              .map((mgmtCluster) => ({\n                field: 'status.clusterName', value: mgmtCluster.id, equals: true, exact: true\n              }))\n          )\n        ],\n\n        page:                 1,\n        sort:                 [],\n        projectsOrNamespaces: []\n      },\n      classify: true,\n    }).then((r) => r.data);\n  }\n}\n\n/**\n * Helper designed to supply non-paginated results for the top level menu cluster resources\n */\nexport class TopLevelMenuHelperLegacy extends BaseTopLevelMenuHelper implements TopLevelMenuHelper {\n  constructor({ $store }: {\n    $store: VuexStore,\n  }) {\n    super({ $store });\n\n    if (this.hasProvCluster) {\n      $store.dispatch('management/findAll', { type: CAPI.RANCHER_CLUSTER });\n    }\n  }\n\n  async update(args: UpdateArgs) {\n    const clusters = this.updateClusters();\n    const _clustersNotPinned = this.clustersFiltered(clusters, args);\n    const _clustersPinned = this.pinFiltered(clusters, args);\n\n    this.clustersPinned.length = 0;\n    this.clustersOthers.length = 0;\n\n    this.clustersPinned.push(..._clustersPinned);\n    this.clustersOthers.push(..._clustersNotPinned);\n\n    this.cacheClusters();\n  }\n\n  /**\n   * Filter mgmt clusters by\n   * 1. Harvester type 1 (filterOnlyKubernetesClusters)\n   * 2. Harvester type 2 (filterHiddenLocalCluster)\n   * 3. There's a matching prov cluster\n   *\n   * Convert remaining clusters to special format\n   */\n  private updateClusters(): TopLevelMenuCluster[] {\n    if (!this.hasProvCluster) {\n      // We're filtering out mgmt clusters without prov clusters, so if the user can't see any prov clusters at all\n      // exit early\n      return [];\n    }\n\n    const all = this.$store.getters['management/all'](MANAGEMENT.CLUSTER);\n    const mgmtClusters = filterHiddenLocalCluster(filterOnlyKubernetesClusters(all, this.$store), this.$store);\n    const provClusters = this.$store.getters['management/all'](CAPI.RANCHER_CLUSTER);\n    const provClustersByMgmtId = provClusters.reduce((res: any, provCluster: ProvCluster) => {\n      if (provCluster.mgmt?.id) {\n        res[provCluster.mgmt.id] = provCluster;\n      }\n\n      return res;\n    }, {});\n\n    return (mgmtClusters || []).reduce((res: any, mgmtCluster: MgmtCluster) => {\n      // Filter to only show mgmt clusters that exist for the available provisioning clusters\n      // Addresses issue where a mgmt cluster can take some time to get cleaned up after the corresponding\n      // provisioning cluster has been deleted\n      if (!provClustersByMgmtId[mgmtCluster.id]) {\n        return res;\n      }\n\n      res.push(this.convertToCluster(mgmtCluster, provClustersByMgmtId[mgmtCluster.id]));\n\n      return res;\n    }, []);\n  }\n\n  /**\n   * Filter clusters by\n   * 1. Not pinned\n   * 2. Includes search term\n   *\n   * Sort remaining clusters\n   *\n   * Reduce number of clusters if too many too show\n   *\n   * Important! This is used to show unpinned clusters OR results of search\n   */\n  private clustersFiltered(clusters: TopLevelMenuCluster[], args: UpdateArgs): TopLevelMenuCluster[] {\n    const clusterFilter = args.searchTerm;\n    const maxClustersToShow = args.unPinnedMax || 10;\n\n    const search = (clusterFilter || '').toLowerCase();\n    let localCluster: MgmtCluster | null = null;\n\n    const filtered = clusters.filter((c) => {\n      // If we're searching we don't care if pinned or not\n      if (search) {\n        if (!c.label?.toLowerCase().includes(search)) {\n          return false;\n        }\n      } else if (c.pinned) {\n        // Not searching, not pinned, don't care\n        return false;\n      }\n\n      if (!localCluster && c.id === 'local') {\n        // Local cluster is a special case, we're inserting it at top so don't include in the middle\n        localCluster = c;\n\n        return false;\n      }\n\n      return true;\n    });\n\n    const sorted = sortBy(filtered, ['ready:desc', 'label']);\n\n    // put local cluster on top of list always - https://github.com/rancher/dashboard/issues/10975\n    if (localCluster) {\n      sorted.unshift(localCluster);\n    }\n\n    if (search) {\n      // this.showPinClusters = false;\n      // this.searchActive = !sorted.length > 0;\n\n      return sorted;\n    }\n    // this.showPinClusters = true;\n    // this.searchActive = false;\n\n    if (sorted.length >= maxClustersToShow) {\n      return sorted.slice(0, maxClustersToShow);\n    }\n\n    return sorted;\n  }\n\n  /**\n   * Filter clusters by\n   * 1. Not pinned\n   * 2. Includes search term\n   *\n   * Sort remaining clusters\n   *\n   * Reduce number of clusters if too many too show\n   *\n   * Important! This is hidden if there's a filter (user searching)\n   */\n  private pinFiltered(clusters: TopLevelMenuCluster[], args: UpdateArgs): TopLevelMenuCluster[] {\n    let localCluster = null;\n    const filtered = clusters.filter((c) => {\n      if (!c.pinned) {\n        // We only care about pinned clusters\n        return false;\n      }\n\n      if (c.id === 'local') {\n        // Special case, we're going to add this at the start so filter out\n        localCluster = c;\n\n        return false;\n      }\n\n      return true;\n    });\n\n    const sorted = sortBy(filtered, ['ready:desc', 'label']);\n\n    // put local cluster on top of list always - https://github.com/rancher/dashboard/issues/10975\n    if (localCluster) {\n      sorted.unshift(localCluster);\n    }\n\n    return sorted;\n  }\n}\n"]}]}