{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/CloudCredential.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/CloudCredential.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCBMYWJlbGVkU2VsZWN0IGZyb20gJ0BzaGVsbC9jb21wb25lbnRzL2Zvcm0vTGFiZWxlZFNlbGVjdCc7CmltcG9ydCB7IE5PUk1BTiB9IGZyb20gJ0BzaGVsbC9jb25maWcvdHlwZXMnOwppbXBvcnQgUXVlc3Rpb24gZnJvbSAnLi9RdWVzdGlvbic7CgpleHBvcnQgZGVmYXVsdCB7CiAgZW1pdHM6IFsndXBkYXRlOnZhbHVlJ10sCgogIGNvbXBvbmVudHM6IHsgTGFiZWxlZFNlbGVjdCB9LAogIG1peGluczogICAgIFtRdWVzdGlvbl0sCgogIGFzeW5jIGZldGNoKCkgewogICAgdGhpcy5hbGwgPSBhd2FpdCB0aGlzLiRzdG9yZS5kaXNwYXRjaCgncmFuY2hlci9maW5kQWxsJywgeyB0eXBlOiBOT1JNQU4uQ0xPVURfQ1JFREVOVElBTCB9KTsKICB9LAoKICBkYXRhKCkgewogICAgcmV0dXJuIHsgYWxsOiBbXSB9OwogIH0sCgogIGNvbXB1dGVkOiB7CiAgICBvcHRpb25zKCkgewogICAgICByZXR1cm4gdGhpcy5hbGwubWFwKCh4KSA9PiB7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIGxhYmVsOiB4Lm5hbWVEaXNwbGF5IHx8IHgubmFtZSB8fCB4Lm1ldGFkYXRhLm5hbWUsCiAgICAgICAgICB2YWx1ZTogeC5pZAogICAgICAgIH07CiAgICAgIH0pOwogICAgfQogIH0sCn07Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/CloudCredential.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7F,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EACpB,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACZ,CAAC;MACH,CAAC,CAAC;IACJ;EACF,CAAC;AACH,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/CloudCredential.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport { NORMAN } from '@shell/config/types';\nimport Question from './Question';\n\nexport default {\n  emits: ['update:value'],\n\n  components: { LabeledSelect },\n  mixins:     [Question],\n\n  async fetch() {\n    this.all = await this.$store.dispatch('rancher/findAll', { type: NORMAN.CLOUD_CREDENTIAL });\n  },\n\n  data() {\n    return { all: [] };\n  },\n\n  computed: {\n    options() {\n      return this.all.map((x) => {\n        return {\n          label: x.nameDisplay || x.name || x.metadata.name,\n          value: x.id\n        };\n      });\n    }\n  },\n};\n</script>\n\n<template>\n  <div class=\"row\">\n    <div class=\"col span-6\">\n      <LabeledSelect\n        :mode=\"mode\"\n        :options=\"options\"\n        :disabled=\"$fetchState.pending || disabled\"\n        :label=\"displayLabel\"\n        :placeholder=\"question.description\"\n        :required=\"question.required\"\n        :value=\"value\"\n        :tooltip=\"displayTooltip\"\n        @update:value=\"!$fetchState.pending && $emit('update:value', $event)\"\n      />\n    </div>\n    <div class=\"col span-6 mt-10\">\n      <div v-if=\"showDescription\">\n        {{ question.description }}\n      </div>\n    </div>\n  </div>\n</template>\n"]}]}