{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??clonedRuleSet-44.use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Networking.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Networking.vue", "mtime": 1754992537319}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgX0NSRUFURSwgX0VESVQsIF9WSUVXIH0gZnJvbSAnQHNoZWxsL2NvbmZpZy9xdWVyeS1wYXJhbXMnOwppbXBvcnQgeyBkZWZpbmVDb21wb25lbnQgfSBmcm9tICd2dWUnOwppbXBvcnQgeyBtYXBHZXR0ZXJzIH0gZnJvbSAndnVleCc7CmltcG9ydCBMYWJlbGVkU2VsZWN0IGZyb20gJ0BzaGVsbC9jb21wb25lbnRzL2Zvcm0vTGFiZWxlZFNlbGVjdC52dWUnOwppbXBvcnQgQ2hlY2tib3ggZnJvbSAnQGNvbXBvbmVudHMvRm9ybS9DaGVja2JveC9DaGVja2JveC52dWUnOwppbXBvcnQgQXJyYXlMaXN0IGZyb20gJ0BzaGVsbC9jb21wb25lbnRzL2Zvcm0vQXJyYXlMaXN0LnZ1ZSc7CmltcG9ydCBCYW5uZXIgZnJvbSAnQGNvbXBvbmVudHMvQmFubmVyL0Jhbm5lci52dWUnOwppbXBvcnQgUmFkaW9Hcm91cCBmcm9tICdAY29tcG9uZW50cy9Gb3JtL1JhZGlvL1JhZGlvR3JvdXAudnVlJzsKZXhwb3J0IGRlZmF1bHQgZGVmaW5lQ29tcG9uZW50KHsKICAgIG5hbWU6ICdFS1NOZXR3b3JraW5nJywKICAgIGVtaXRzOiBbJ3VwZGF0ZTpzdWJuZXRzJywgJ3VwZGF0ZTpzZWN1cml0eUdyb3VwcycsICdlcnJvcicsICd1cGRhdGU6cHVibGljQWNjZXNzJywgJ3VwZGF0ZTpwcml2YXRlQWNjZXNzJywgJ3VwZGF0ZTpwdWJsaWNBY2Nlc3NTb3VyY2VzJ10sCiAgICBjb21wb25lbnRzOiB7CiAgICAgICAgTGFiZWxlZFNlbGVjdCwKICAgICAgICBBcnJheUxpc3QsCiAgICAgICAgQ2hlY2tib3gsCiAgICAgICAgUmFkaW9Hcm91cCwKICAgICAgICBCYW5uZXIKICAgIH0sCiAgICBwcm9wczogewogICAgICAgIG1vZGU6IHsKICAgICAgICAgICAgdHlwZTogU3RyaW5nLAogICAgICAgICAgICBkZWZhdWx0OiBfRURJVAogICAgICAgIH0sCiAgICAgICAgcmVnaW9uOiB7CiAgICAgICAgICAgIHR5cGU6IFN0cmluZywKICAgICAgICAgICAgZGVmYXVsdDogJycKICAgICAgICB9LAogICAgICAgIGFtYXpvbkNyZWRlbnRpYWxTZWNyZXQ6IHsKICAgICAgICAgICAgdHlwZTogU3RyaW5nLAogICAgICAgICAgICBkZWZhdWx0OiAnJwogICAgICAgIH0sCiAgICAgICAgc3VibmV0czogewogICAgICAgICAgICB0eXBlOiBBcnJheSwKICAgICAgICAgICAgZGVmYXVsdDogKCkgPT4gW10KICAgICAgICB9LAogICAgICAgIHNlY3VyaXR5R3JvdXBzOiB7CiAgICAgICAgICAgIHR5cGU6IEFycmF5LAogICAgICAgICAgICBkZWZhdWx0OiAoKSA9PiBbXQogICAgICAgIH0sCiAgICAgICAgcHVibGljQWNjZXNzOiB7CiAgICAgICAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgICAgICAgIGRlZmF1bHQ6IGZhbHNlCiAgICAgICAgfSwKICAgICAgICBwcml2YXRlQWNjZXNzOiB7CiAgICAgICAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgICAgICAgIGRlZmF1bHQ6IGZhbHNlCiAgICAgICAgfSwKICAgICAgICBwdWJsaWNBY2Nlc3NTb3VyY2VzOiB7CiAgICAgICAgICAgIHR5cGU6IEFycmF5LAogICAgICAgICAgICBkZWZhdWx0OiAoKSA9PiBbXQogICAgICAgIH0sCiAgICAgICAgc3RhdHVzU3VibmV0czogewogICAgICAgICAgICB0eXBlOiBBcnJheSwKICAgICAgICAgICAgZGVmYXVsdDogKCkgPT4gW10KICAgICAgICB9LAogICAgICAgIHJ1bGVzOiB7CiAgICAgICAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgICAgICAgZGVmYXVsdDogKCkgPT4geyB9CiAgICAgICAgfQogICAgfSwKICAgIHdhdGNoOiB7CiAgICAgICAgYW1hem9uQ3JlZGVudGlhbFNlY3JldDogewogICAgICAgICAgICBoYW5kbGVyKG5ldSkgewogICAgICAgICAgICAgICAgaWYgKG5ldSAmJiAhdGhpcy5pc1ZpZXcpIHsKICAgICAgICAgICAgICAgICAgICB0aGlzLmZldGNoVnBjcygpOwogICAgICAgICAgICAgICAgICAgIHRoaXMuZmV0Y2hTZWN1cml0eUdyb3VwcygpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9LAogICAgICAgICAgICBpbW1lZGlhdGU6IHRydWUKICAgICAgICB9LAogICAgICAgIHJlZ2lvbjogewogICAgICAgICAgICBoYW5kbGVyKG5ldSkgewogICAgICAgICAgICAgICAgaWYgKG5ldSAmJiAhdGhpcy5pc1ZpZXcpIHsKICAgICAgICAgICAgICAgICAgICB0aGlzLmZldGNoVnBjcygpOwogICAgICAgICAgICAgICAgICAgIHRoaXMuZmV0Y2hTZWN1cml0eUdyb3VwcygpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9LAogICAgICAgICAgICBpbW1lZGlhdGU6IHRydWUKICAgICAgICB9LAogICAgICAgICdjaG9vc2VTdWJuZXQnKG5ldSkgewogICAgICAgICAgICBpZiAoIW5ldSkgewogICAgICAgICAgICAgICAgdGhpcy4kZW1pdCgndXBkYXRlOnN1Ym5ldHMnLCBbXSk7CiAgICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIHNlbGVjdGVkVnBjKG5ldSwgb2xkKSB7CiAgICAgICAgICAgIGlmICghIW9sZCkgewogICAgICAgICAgICAgICAgdGhpcy4kZW1pdCgndXBkYXRlOnNlY3VyaXR5R3JvdXBzJywgW10pOwogICAgICAgICAgICB9CiAgICAgICAgfQogICAgfSwKICAgIGRhdGEoKSB7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgbG9hZGluZ1ZwY3M6IGZhbHNlLAogICAgICAgICAgICBsb2FkaW5nU2VjdXJpdHlHcm91cHM6IGZhbHNlLAogICAgICAgICAgICB2cGNJbmZvOiBbXSwKICAgICAgICAgICAgc3VibmV0SW5mbzogW10sCiAgICAgICAgICAgIHNlY3VyaXR5R3JvdXBJbmZvOiB7fSwKICAgICAgICAgICAgY2hvb3NlU3VibmV0OiAhIXRoaXMuc3VibmV0cyAmJiAhIXRoaXMuc3VibmV0cy5sZW5ndGgKICAgICAgICB9OwogICAgfSwKICAgIGNvbXB1dGVkOiB7CiAgICAgICAgLi4ubWFwR2V0dGVycyh7IHQ6ICdpMThuL3QnIH0pLAogICAgICAgIC8vIG1hcCBzdWJuZXRzIHRvIFZQQ3MKICAgICAgICAvLyB7W3ZwYyBpZF06IFtzdWJuZXRzXX0KICAgICAgICB2cGNPcHRpb25zKCkgewogICAgICAgICAgICBjb25zdCBvdXQgPSBbXTsKICAgICAgICAgICAgY29uc3QgdnBjcyA9IHRoaXMudnBjSW5mbyB8fCBbXTsKICAgICAgICAgICAgY29uc3Qgc3VibmV0cyA9IHRoaXMuc3VibmV0SW5mbyB8fCBbXTsKICAgICAgICAgICAgY29uc3QgbWFwcGVkU3VibmV0cyA9IHt9OwogICAgICAgICAgICBzdWJuZXRzLmZvckVhY2goKHMpID0+IHsKICAgICAgICAgICAgICAgIGlmICghbWFwcGVkU3VibmV0c1tzLlZwY0lkXSkgewogICAgICAgICAgICAgICAgICAgIG1hcHBlZFN1Ym5ldHNbcy5WcGNJZF0gPSBbc107CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBlbHNlIHsKICAgICAgICAgICAgICAgICAgICBtYXBwZWRTdWJuZXRzW3MuVnBjSWRdLnB1c2gocyk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pOwogICAgICAgICAgICB2cGNzLmZvckVhY2goKHYpID0+IHsKICAgICAgICAgICAgICAgIHZhciBfYTsKICAgICAgICAgICAgICAgIGNvbnN0IHsgVnBjSWQgPSAnJywgVGFncyA9IFtdIH0gPSB2OwogICAgICAgICAgICAgICAgY29uc3QgbmFtZVRhZyA9IChfYSA9IFRhZ3MuZmluZCgodCkgPT4gewogICAgICAgICAgICAgICAgICAgIHJldHVybiB0LktleSA9PT0gJ05hbWUnOwogICAgICAgICAgICAgICAgfSkpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5WYWx1ZTsKICAgICAgICAgICAgICAgIGNvbnN0IGZvcm1PcHRpb24gPSB7CiAgICAgICAgICAgICAgICAgICAga2V5OiBWcGNJZCwgbGFiZWw6IGAke25hbWVUYWd9ICgke1ZwY0lkfSlgLCBraW5kOiAnZ3JvdXAnCiAgICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgICAgb3V0LnB1c2goZm9ybU9wdGlvbik7CiAgICAgICAgICAgICAgICBpZiAobWFwcGVkU3VibmV0c1tWcGNJZF0pIHsKICAgICAgICAgICAgICAgICAgICBtYXBwZWRTdWJuZXRzW1ZwY0lkXS5mb3JFYWNoKChzKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgIHZhciBfYTsKICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgeyBTdWJuZXRJZCwgVGFncyA9IFtdIH0gPSBzOwogICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBuYW1lVGFnID0gKF9hID0gVGFncy5maW5kKCh0KSA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gdC5LZXkgPT09ICdOYW1lJzsKICAgICAgICAgICAgICAgICAgICAgICAgfSkpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5WYWx1ZTsKICAgICAgICAgICAgICAgICAgICAgICAgY29uc3Qgc3VibmV0Rm9ybU9wdGlvbiA9IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleTogU3VibmV0SWQsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogYCR7bmFtZVRhZ30gKCR7U3VibmV0SWR9KWAsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBfaXNTdWJuZXQ6IHRydWUsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZDogISF0aGlzLnNlbGVjdGVkVnBjICYmIFZwY0lkICE9PSB0aGlzLnNlbGVjdGVkVnBjCiAgICAgICAgICAgICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgICAgICAgICAgIG91dC5wdXNoKHN1Ym5ldEZvcm1PcHRpb24pOwogICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KTsKICAgICAgICAgICAgcmV0dXJuIG91dDsKICAgICAgICB9LAogICAgICAgIHNlY3VyaXR5R3JvdXBPcHRpb25zKCkgewogICAgICAgICAgICB2YXIgX2E7CiAgICAgICAgICAgIGNvbnN0IGFsbEdyb3VwcyA9ICgoX2EgPSB0aGlzLnNlY3VyaXR5R3JvdXBJbmZvKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuU2VjdXJpdHlHcm91cHMpIHx8IFtdOwogICAgICAgICAgICByZXR1cm4gYWxsR3JvdXBzLnJlZHVjZSgob3B0cywgc2cpID0+IHsKICAgICAgICAgICAgICAgIGlmIChzZy5WcGNJZCAhPT0gdGhpcy5zZWxlY3RlZFZwYykgewogICAgICAgICAgICAgICAgICAgIHJldHVybiBvcHRzOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgb3B0cy5wdXNoKHsKICAgICAgICAgICAgICAgICAgICBsYWJlbDogYCR7c2cuR3JvdXBOYW1lfSAoJHtzZy5Hcm91cElkfSlgLAogICAgICAgICAgICAgICAgICAgIHZhbHVlOiBzZy5Hcm91cElkCiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIHJldHVybiBvcHRzOwogICAgICAgICAgICB9LCBbXSk7CiAgICAgICAgfSwKICAgICAgICBkaXNwbGF5U3VibmV0czogewogICAgICAgICAgICBnZXQoKSB7CiAgICAgICAgICAgICAgICBjb25zdCBzdWJuZXRzID0gdGhpcy5jaG9vc2VTdWJuZXQgPyB0aGlzLnN1Ym5ldHMgOiB0aGlzLnN0YXR1c1N1Ym5ldHM7CiAgICAgICAgICAgICAgICAvLyB2cGNPcHRpb25zIHdpbGwgYmUgZW1wdHkgaW4gJ3ZpZXcgY29uZmlnJyBtb2RlLCB3aGVyZSBhd3MgQVBJIHJlcXVlc3RzIGFyZSBub3QgbWFkZQogICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMudnBjT3B0aW9ucy5sZW5ndGggPyB0aGlzLnZwY09wdGlvbnMuZmlsdGVyKChvcHRpb24pID0+IHN1Ym5ldHMuaW5jbHVkZXMob3B0aW9uLmtleSkpIDogc3VibmV0czsKICAgICAgICAgICAgfSwKICAgICAgICAgICAgc2V0KG5ldSkgewogICAgICAgICAgICAgICAgdGhpcy4kZW1pdCgndXBkYXRlOnN1Ym5ldHMnLCBuZXUubWFwKChzKSA9PiBzLmtleSkpOwogICAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICBzZWxlY3RlZFZwYygpIHsKICAgICAgICAgICAgdmFyIF9hOwogICAgICAgICAgICBpZiAoIXRoaXMuY2hvb3NlU3VibmV0KSB7CiAgICAgICAgICAgICAgICByZXR1cm4gbnVsbDsKICAgICAgICAgICAgfQogICAgICAgICAgICByZXR1cm4gKF9hID0gKHRoaXMuc3VibmV0SW5mbyB8fCBbXSkuZmluZCgocykgPT4gdGhpcy5zdWJuZXRzLmluY2x1ZGVzKHMuU3VibmV0SWQpKSkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLlZwY0lkOwogICAgICAgIH0sCiAgICAgICAgaXNOZXcoKSB7CiAgICAgICAgICAgIHJldHVybiB0aGlzLm1vZGUgPT09IF9DUkVBVEU7CiAgICAgICAgfSwKICAgICAgICBpc1ZpZXcoKSB7CiAgICAgICAgICAgIHJldHVybiB0aGlzLm1vZGUgPT09IF9WSUVXOwogICAgICAgIH0KICAgIH0sCiAgICBtZXRob2RzOiB7CiAgICAgICAgYXN5bmMgZmV0Y2hWcGNzKCkgewogICAgICAgICAgICB0aGlzLmxvYWRpbmdWcGNzID0gdHJ1ZTsKICAgICAgICAgICAgY29uc3QgeyByZWdpb24sIGFtYXpvbkNyZWRlbnRpYWxTZWNyZXQgfSA9IHRoaXM7CiAgICAgICAgICAgIGlmICghcmVnaW9uIHx8ICFhbWF6b25DcmVkZW50aWFsU2VjcmV0KSB7CiAgICAgICAgICAgICAgICByZXR1cm47CiAgICAgICAgICAgIH0KICAgICAgICAgICAgY29uc3QgZWMyQ2xpZW50ID0gYXdhaXQgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ2F3cy9lYzInLCB7IHJlZ2lvbiwgY2xvdWRDcmVkZW50aWFsSWQ6IGFtYXpvbkNyZWRlbnRpYWxTZWNyZXQgfSk7CiAgICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgICAgICB0aGlzLnZwY0luZm8gPSBhd2FpdCB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnYXdzL2RlcGFnaW5hdGVMaXN0JywgeyBjbGllbnQ6IGVjMkNsaWVudCwgY21kOiAnZGVzY3JpYmVWcGNzJyB9KTsKICAgICAgICAgICAgICAgIHRoaXMuc3VibmV0SW5mbyA9IGF3YWl0IHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdhd3MvZGVwYWdpbmF0ZUxpc3QnLCB7IGNsaWVudDogZWMyQ2xpZW50LCBjbWQ6ICdkZXNjcmliZVN1Ym5ldHMnIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICAgIGNhdGNoIChlcnIpIHsKICAgICAgICAgICAgICAgIHRoaXMuJGVtaXQoJ2Vycm9yJywgZXJyKTsKICAgICAgICAgICAgfQogICAgICAgICAgICB0aGlzLmxvYWRpbmdWcGNzID0gZmFsc2U7CiAgICAgICAgfSwKICAgICAgICBhc3luYyBmZXRjaFNlY3VyaXR5R3JvdXBzKCkgewogICAgICAgICAgICB0aGlzLmxvYWRpbmdTZWN1cml0eUdyb3VwcyA9IHRydWU7CiAgICAgICAgICAgIGNvbnN0IHsgcmVnaW9uLCBhbWF6b25DcmVkZW50aWFsU2VjcmV0IH0gPSB0aGlzOwogICAgICAgICAgICBpZiAoIXJlZ2lvbiB8fCAhYW1hem9uQ3JlZGVudGlhbFNlY3JldCkgewogICAgICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgICB9CiAgICAgICAgICAgIGNvbnN0IGVjMkNsaWVudCA9IGF3YWl0IHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdhd3MvZWMyJywgeyByZWdpb24sIGNsb3VkQ3JlZGVudGlhbElkOiBhbWF6b25DcmVkZW50aWFsU2VjcmV0IH0pOwogICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgICAgdGhpcy5zZWN1cml0eUdyb3VwSW5mbyA9IGF3YWl0IGVjMkNsaWVudC5kZXNjcmliZVNlY3VyaXR5R3JvdXBzKHt9KTsKICAgICAgICAgICAgfQogICAgICAgICAgICBjYXRjaCAoZXJyKSB7CiAgICAgICAgICAgICAgICB0aGlzLiRlbWl0KCdlcnJvcicsIGVycik7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgdGhpcy5sb2FkaW5nU2VjdXJpdHlHcm91cHMgPSBmYWxzZTsKICAgICAgICB9CiAgICB9Cn0pOwo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Networking.vue"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAI,EAAE,MAAO,4BAA4B,CAAA;AAClE,OAAO,EAAY,eAAc,EAAE,MAAO,KAAK,CAAA;AAC/C,OAAO,EAAS,UAAS,EAAE,MAAO,MAAM,CAAA;AACxC,OAAO,aAAY,MAAO,0CAA0C,CAAA;AACpE,OAAO,QAAO,MAAO,wCAAwC,CAAA;AAC7D,OAAO,SAAQ,MAAO,sCAAsC,CAAA;AAC5D,OAAO,MAAK,MAAO,+BAA+B,CAAA;AAElD,OAAO,UAAS,MAAO,uCAAuC,CAAA;AAI9D,eAAe,eAAe,CAAC;IAC7B,IAAI,EAAE,eAAe;IAErB,KAAK,EAAE,CAAC,gBAAgB,EAAE,uBAAuB,EAAE,OAAO,EAAE,qBAAqB,EAAE,sBAAsB,EAAE,4BAA4B,CAAC;IAExI,UAAU,EAAE;QACV,aAAa;QACb,SAAS;QACT,QAAQ;QACR,UAAU;QACV,MAAK;KACN;IAED,KAAK,EAAE;QACL,IAAI,EAAE;YACJ,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,KAAI;SACd;QAED,MAAM,EAAE;YACN,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,EAAC;SACX;QAED,sBAAsB,EAAE;YACtB,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,EAAC;SACX;QAED,OAAO,EAAE;YACP,IAAI,EAAK,KAA2B;YACpC,OAAO,EAAE,GAAG,EAAC,CAAE,EAAC;SACjB;QAED,cAAc,EAAE;YACd,IAAI,EAAK,KAA2B;YACpC,OAAO,EAAE,GAAG,EAAC,CAAE,EAAC;SACjB;QAED,YAAY,EAAE;YACZ,IAAI,EAAK,OAAO;YAChB,OAAO,EAAE,KAAI;SACd;QAED,aAAa,EAAE;YACb,IAAI,EAAK,OAAO;YAChB,OAAO,EAAE,KAAI;SACd;QAED,mBAAmB,EAAE;YACnB,IAAI,EAAK,KAAK;YACd,OAAO,EAAE,GAAG,EAAC,CAAE,EAAC;SACjB;QAED,aAAa,EAAE;YACb,IAAI,EAAK,KAA2B;YACpC,OAAO,EAAE,GAAG,EAAC,CAAE,EAAC;SACjB;QAED,KAAK,EAAE;YACL,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,GAAG,EAAC,GAAG,CAAA;SAClB;KACD;IAED,KAAK,EAAE;QACL,sBAAsB,EAAE;YACtB,OAAO,CAAC,GAAG;gBACT,IAAI,GAAE,IAAK,CAAC,IAAI,CAAC,MAAM,EAAE,CAAA;oBACvB,IAAI,CAAC,SAAS,EAAE,CAAA;oBAChB,IAAI,CAAC,mBAAmB,EAAE,CAAA;gBAC5B,CAAA;YACF,CAAC;YACD,SAAS,EAAE,IAAG;SACf;QACD,MAAM,EAAE;YACN,OAAO,CAAC,GAAE;gBACR,IAAI,GAAE,IAAK,CAAC,IAAI,CAAC,MAAM,EAAE,CAAA;oBACvB,IAAI,CAAC,SAAS,EAAE,CAAA;oBAChB,IAAI,CAAC,mBAAmB,EAAE,CAAA;gBAC5B,CAAA;YACF,CAAC;YACD,SAAS,EAAE,IAAG;SACf;QAED,cAAc,CAAC,GAAY;YACzB,IAAI,CAAC,GAAG,EAAE,CAAA;gBACR,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAA;YAClC,CAAA;QACF,CAAC;QAED,WAAW,CAAC,GAAW,EAAE,GAAW;YAClC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAA;gBACT,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAA;YACzC,CAAA;QACF,CAAA;KAED;IAED,IAAI;QACF,OAAO;YACL,WAAW,EAAY,KAAK;YAC5B,qBAAqB,EAAE,KAAK;YAC5B,OAAO,EAAgB,EAAe;YACtC,UAAU,EAAa,EAAkB;YACzC,iBAAiB,EAAM,EAA2C;YAClE,YAAY,EAAW,CAAC,CAAC,IAAI,CAAC,OAAM,IAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAK;SAC9D,CAAA;IACH,CAAC;IAED,QAAQ,EAAE;QACR,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE,QAAO,EAAG,CAAC;QAC9B,sBAAqB;QACrB,wBAAuB;QACvB,UAAU;YACR,MAAM,GAAG,GAAmE,EAAE,CAAA;YAC9E,MAAM,IAAI,GAAc,IAAI,CAAC,OAAM,IAAK,EAAE,CAAA;YAC1C,MAAM,OAAO,GAAiB,IAAI,CAAC,UAAS,IAAK,EAAE,CAAA;YACnD,MAAM,aAAa,GAAiC,EAAE,CAAA;YAEtD,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAC;gBACnB,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAA;oBAC3B,aAAa,CAAC,CAAC,CAAC,KAAK,CAAA,GAAI,CAAC,CAAC,CAAC,CAAA;gBAC9B,CAAA;qBAAO,CAAA;oBACL,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;gBAChC,CAAA;YACF,CAAC,CAAC,CAAA;YACF,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAC;;gBAChB,MAAM,EAAE,KAAI,GAAI,EAAE,EAAE,IAAG,GAAI,EAAC,EAAE,GAAI,CAAC,CAAA;gBACnC,MAAM,OAAM,GAAI,MAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAC;oBAC7B,OAAO,CAAC,CAAC,GAAE,KAAM,MAAM,CAAA;gBACzB,CAAC,CAAC,0CAAE,KAAK,CAAA;gBAET,MAAM,UAAS,GAAI;oBACjB,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,GAAI,OAAQ,KAAM,KAAM,GAAG,EAAE,IAAI,EAAE,OAAM;iBAC7D,CAAA;gBAED,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;gBACpB,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE,CAAA;oBACxB,aAAa,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAC;;wBAChC,MAAM,EAAE,QAAQ,EAAE,IAAG,GAAI,EAAC,EAAE,GAAI,CAAC,CAAA;wBACjC,MAAM,OAAM,GAAI,MAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAC;4BAC7B,OAAO,CAAC,CAAC,GAAE,KAAM,MAAM,CAAA;wBACzB,CAAC,CAAC,0CAAE,KAAK,CAAA;wBAET,MAAM,gBAAe,GAAI;4BACvB,GAAG,EAAQ,QAAQ;4BACnB,KAAK,EAAM,GAAI,OAAQ,KAAM,QAAS,GAAG;4BACzC,SAAS,EAAE,IAAI;4BACf,QAAQ,EAAG,CAAC,CAAC,IAAI,CAAC,WAAU,IAAK,KAAI,KAAM,IAAI,CAAC,WAAU;yBAC3D,CAAA;wBAED,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;oBAC5B,CAAC,CAAC,CAAA;gBACJ,CAAA;YACF,CAAC,CAAC,CAAA;YAEF,OAAO,GAAG,CAAA;QACZ,CAAC;QAED,oBAAoB;;YAClB,MAAM,SAAQ,GAAI,CAAA,MAAA,IAAI,CAAC,iBAAiB,0CAAE,cAAa,KAAK,EAAE,CAAA;YAE9D,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,EAAC;gBAClC,IAAI,EAAE,CAAC,KAAI,KAAM,IAAI,CAAC,WAAW,EAAE,CAAA;oBACjC,OAAO,IAAI,CAAA;gBACb,CAAA;gBACA,IAAI,CAAC,IAAI,CAAC;oBACR,KAAK,EAAE,GAAI,EAAE,CAAC,SAAU,KAAM,EAAE,CAAC,OAAQ,GAAG;oBAC5C,KAAK,EAAE,EAAE,CAAC,OAAM;iBACjB,CAAC,CAAA;gBAEF,OAAO,IAAI,CAAA;YACb,CAAC,EAAE,EAAsC,CAAC,CAAA;QAC5C,CAAC;QAED,cAAc,EAAE;YACd,GAAG;gBACD,MAAM,OAAO,GAAa,IAAI,CAAC,YAAW,CAAE,CAAA,CAAE,IAAI,CAAC,OAAM,CAAE,CAAA,CAAE,IAAI,CAAC,aAAa,CAAA;gBAE/E,sFAAqF;gBACrF,OAAO,IAAI,CAAC,UAAU,CAAC,MAAK,CAAE,CAAA,CAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAC,CAAE,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA,CAAE,CAAA,CAAE,OAAO,CAAA;YAC5G,CAAC;YACD,GAAG,CAAC,GAAmE;gBACrE,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAC,CAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;YACrD,CAAA;SACD;QAED,WAAW;;YACT,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAA;gBACtB,OAAO,IAAI,CAAA;YACb,CAAA;YAEA,OAAO,MAAA,CAAC,IAAI,CAAC,UAAS,IAAK,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAC,CAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,0CAAE,KAAK,CAAA;QACtF,CAAC;QAED,KAAK;YACH,OAAO,IAAI,CAAC,IAAG,KAAM,OAAO,CAAA;QAC9B,CAAC;QAED,MAAM;YACJ,OAAO,IAAI,CAAC,IAAG,KAAM,KAAK,CAAA;QAC5B,CAAA;KACD;IAED,OAAO,EAAE;QACP,KAAI,CAAE,SAAS;YACb,IAAI,CAAC,WAAU,GAAI,IAAI,CAAA;YACvB,MAAM,EAAE,MAAM,EAAE,sBAAqB,EAAE,GAAI,IAAI,CAAA;YAE/C,IAAI,CAAC,MAAK,IAAK,CAAC,sBAAsB,EAAE,CAAA;gBACtC,OAAM;YACR,CAAA;YACA,MAAM,SAAQ,GAAI,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,iBAAiB,EAAE,sBAAqB,EAAG,CAAC,CAAA;YAE9G,IAAI,CAAA;gBACF,IAAI,CAAC,OAAM,GAAI,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,oBAAoB,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,cAAa,EAAG,CAAC,CAAA;gBAC3G,IAAI,CAAC,UAAS,GAAI,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,oBAAoB,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,iBAAgB,EAAG,CAAC,CAAA;YACnH,CAAA;YAAE,OAAO,GAAG,EAAE,CAAA;gBACZ,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAA;YAC1B,CAAA;YACA,IAAI,CAAC,WAAU,GAAI,KAAK,CAAA;QAC1B,CAAC;QAED,KAAI,CAAE,mBAAmB;YACvB,IAAI,CAAC,qBAAoB,GAAI,IAAI,CAAA;YACjC,MAAM,EAAE,MAAM,EAAE,sBAAqB,EAAE,GAAI,IAAI,CAAA;YAE/C,IAAI,CAAC,MAAK,IAAK,CAAC,sBAAsB,EAAE,CAAA;gBACtC,OAAM;YACR,CAAA;YACA,MAAM,SAAQ,GAAI,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,iBAAiB,EAAE,sBAAqB,EAAG,CAAC,CAAA;YAE9G,IAAI,CAAA;gBACF,IAAI,CAAC,iBAAgB,GAAI,MAAM,SAAS,CAAC,sBAAsB,CAAC,EAAG,CAAC,CAAA;YACtE,CAAA;YAAE,OAAO,GAAG,EAAE,CAAA;gBACZ,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAA;YAC1B,CAAA;YACA,IAAI,CAAC,qBAAoB,GAAI,KAAK,CAAA;QACpC,CAAA;KACF;CACD,CAAC,CAAA", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Networking.vue.tsx", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { _CREATE, _EDIT, _VIEW } from '@shell/config/query-params';\nimport { PropType, defineComponent } from 'vue';\nimport { Store, mapGetters } from 'vuex';\nimport LabeledSelect from '@shell/components/form/LabeledSelect.vue';\nimport Checkbox from '@components/Form/Checkbox/Checkbox.vue';\nimport ArrayList from '@shell/components/form/ArrayList.vue';\nimport Banner from '@components/Banner/Banner.vue';\n\nimport RadioGroup from '@components/Form/Radio/RadioGroup.vue';\n\nimport { AWS } from '../../types';\n\nexport default defineComponent({\n  name: 'EKSNetworking',\n\n  emits: ['update:subnets', 'update:securityGroups', 'error', 'update:publicAccess', 'update:privateAccess', 'update:publicAccessSources'],\n\n  components: {\n    LabeledSelect,\n    ArrayList,\n    Checkbox,\n    RadioGroup,\n    Banner\n  },\n\n  props: {\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n\n    region: {\n      type:    String,\n      default: ''\n    },\n\n    amazonCredentialSecret: {\n      type:    String,\n      default: ''\n    },\n\n    subnets: {\n      type:    Array as PropType<string[]>,\n      default: () => []\n    },\n\n    securityGroups: {\n      type:    Array as PropType<string[]>,\n      default: () => []\n    },\n\n    publicAccess: {\n      type:    Boolean,\n      default: false\n    },\n\n    privateAccess: {\n      type:    Boolean,\n      default: false\n    },\n\n    publicAccessSources: {\n      type:    Array,\n      default: () => []\n    },\n\n    statusSubnets: {\n      type:    Array as PropType<string[]>,\n      default: () => []\n    },\n\n    rules: {\n      type:    Object,\n      default: () => {}\n    }\n  },\n\n  watch: {\n    amazonCredentialSecret: {\n      handler(neu) {\n        if (neu && !this.isView) {\n          this.fetchVpcs();\n          this.fetchSecurityGroups();\n        }\n      },\n      immediate: true\n    },\n    region: {\n      handler(neu ) {\n        if (neu && !this.isView) {\n          this.fetchVpcs();\n          this.fetchSecurityGroups();\n        }\n      },\n      immediate: true\n    },\n\n    'chooseSubnet'(neu: boolean) {\n      if (!neu) {\n        this.$emit('update:subnets', []);\n      }\n    },\n\n    selectedVpc(neu: string, old: string) {\n      if (!!old) {\n        this.$emit('update:securityGroups', []);\n      }\n    }\n\n  },\n\n  data() {\n    return {\n      loadingVpcs:           false,\n      loadingSecurityGroups: false,\n      vpcInfo:               [] as AWS.VPC[],\n      subnetInfo:            [] as AWS.Subnet[],\n      securityGroupInfo:     {} as {SecurityGroups: AWS.SecurityGroup[]},\n      chooseSubnet:          !!this.subnets && !!this.subnets.length\n    };\n  },\n\n  computed: {\n    ...mapGetters({ t: 'i18n/t' }),\n    // map subnets to VPCs\n    // {[vpc id]: [subnets]}\n    vpcOptions() {\n      const out: {key:string, label:string, _isSubnet?:boolean, kind?:string}[] = [];\n      const vpcs: AWS.VPC[] = this.vpcInfo || [];\n      const subnets: AWS.Subnet[] = this.subnetInfo || [];\n      const mappedSubnets: {[key:string]: AWS.Subnet[]} = {};\n\n      subnets.forEach((s) => {\n        if (!mappedSubnets[s.VpcId]) {\n          mappedSubnets[s.VpcId] = [s];\n        } else {\n          mappedSubnets[s.VpcId].push(s);\n        }\n      });\n      vpcs.forEach((v) => {\n        const { VpcId = '', Tags = [] } = v;\n        const nameTag = Tags.find((t) => {\n          return t.Key === 'Name';\n        })?.Value;\n\n        const formOption = {\n          key: VpcId, label: `${ nameTag } (${ VpcId })`, kind: 'group'\n        };\n\n        out.push(formOption);\n        if (mappedSubnets[VpcId]) {\n          mappedSubnets[VpcId].forEach((s) => {\n            const { SubnetId, Tags = [] } = s;\n            const nameTag = Tags.find((t) => {\n              return t.Key === 'Name';\n            })?.Value;\n\n            const subnetFormOption = {\n              key:       SubnetId,\n              label:     `${ nameTag } (${ SubnetId })`,\n              _isSubnet: true,\n              disabled:  !!this.selectedVpc && VpcId !== this.selectedVpc\n            };\n\n            out.push(subnetFormOption);\n          });\n        }\n      });\n\n      return out;\n    },\n\n    securityGroupOptions() {\n      const allGroups = this.securityGroupInfo?.SecurityGroups || [];\n\n      return allGroups.reduce((opts, sg) => {\n        if (sg.VpcId !== this.selectedVpc) {\n          return opts;\n        }\n        opts.push({\n          label: `${ sg.GroupName } (${ sg.GroupId })`,\n          value: sg.GroupId\n        });\n\n        return opts;\n      }, [] as {label: string, value: string}[]);\n    },\n\n    displaySubnets: {\n      get(): {key:string, label:string, _isSubnet?:boolean, kind?:string}[] | string[] {\n        const subnets: string[] = this.chooseSubnet ? this.subnets : this.statusSubnets;\n\n        // vpcOptions will be empty in 'view config' mode, where aws API requests are not made\n        return this.vpcOptions.length ? this.vpcOptions.filter((option) => subnets.includes(option.key)) : subnets;\n      },\n      set(neu: {key:string, label:string, _isSubnet?:boolean, kind?:string}[]) {\n        this.$emit('update:subnets', neu.map((s) => s.key));\n      }\n    },\n\n    selectedVpc() {\n      if (!this.chooseSubnet) {\n        return null;\n      }\n\n      return (this.subnetInfo || []).find((s) => this.subnets.includes(s.SubnetId))?.VpcId;\n    },\n\n    isNew(): boolean {\n      return this.mode === _CREATE;\n    },\n\n    isView():boolean {\n      return this.mode === _VIEW;\n    }\n  },\n\n  methods: {\n    async fetchVpcs() {\n      this.loadingVpcs = true;\n      const { region, amazonCredentialSecret } = this;\n\n      if (!region || !amazonCredentialSecret) {\n        return;\n      }\n      const ec2Client = await this.$store.dispatch('aws/ec2', { region, cloudCredentialId: amazonCredentialSecret });\n\n      try {\n        this.vpcInfo = await this.$store.dispatch('aws/depaginateList', { client: ec2Client, cmd: 'describeVpcs' });\n        this.subnetInfo = await this.$store.dispatch('aws/depaginateList', { client: ec2Client, cmd: 'describeSubnets' });\n      } catch (err) {\n        this.$emit('error', err);\n      }\n      this.loadingVpcs = false;\n    },\n\n    async fetchSecurityGroups() {\n      this.loadingSecurityGroups = true;\n      const { region, amazonCredentialSecret } = this;\n\n      if (!region || !amazonCredentialSecret) {\n        return;\n      }\n      const ec2Client = await this.$store.dispatch('aws/ec2', { region, cloudCredentialId: amazonCredentialSecret });\n\n      try {\n        this.securityGroupInfo = await ec2Client.describeSecurityGroups({ });\n      } catch (err) {\n        this.$emit('error', err);\n      }\n      this.loadingSecurityGroups = false;\n    }\n  }\n});\n</script>\n\n<template>\n  <div>\n    <Banner\n      color=\"info\"\n      label-key=\"eks.publicAccess.tooltip\"\n    />\n    <div class=\"row mb-10\">\n      <div class=\"col span-6\">\n        <Checkbox\n          :value=\"publicAccess\"\n          :mode=\"mode\"\n          label-key=\"eks.publicAccess.label\"\n          @update:value=\"$emit('update:publicAccess', $event)\"\n        />\n        <Checkbox\n          :value=\"privateAccess\"\n          :mode=\"mode\"\n          label-key=\"eks.privateAccess.label\"\n          @update:value=\"$emit('update:privateAccess', $event)\"\n        />\n      </div>\n    </div>\n    <div class=\"row mb-10\">\n      <div class=\"col span-6\">\n        <ArrayList\n          :value=\"publicAccessSources\"\n          :mode=\"mode\"\n          :disabled=\"!publicAccess\"\n          :add-allowed=\"publicAccess\"\n          :add-label=\"t('eks.publicAccessSources.addEndpoint')\"\n          data-testid=\"eks-public-access-sources\"\n          @update:value=\"$emit('update:publicAccessSources', $event)\"\n        >\n          <template #title>\n            {{ t('eks.publicAccessSources.label') }}\n          </template>\n        </ArrayList>\n      </div>\n    </div>\n    <div class=\"row mb-10 mt-20\">\n      <div\n        v-if=\"isNew\"\n        class=\"col span-6\"\n      >\n        <RadioGroup\n          v-model:value=\"chooseSubnet\"\n          name=\"subnet-mode\"\n          :mode=\"mode\"\n          :options=\"[{label: t('eks.subnets.default'), value: false},{label: t('eks.subnets.useCustom'), value: true}]\"\n          label-key=\"eks.subnets.title\"\n          :disabled=\"!isNew\"\n        />\n      </div>\n    </div>\n    <div\n      class=\"row mb-10\"\n    >\n      <div\n        v-if=\"chooseSubnet || !isNew\"\n        class=\"col span-6\"\n      >\n        <LabeledSelect\n          v-model:value=\"displaySubnets\"\n          :disabled=\"!isNew\"\n          :mode=\"mode\"\n          label-key=\"eks.vpcSubnet.label\"\n          :options=\"vpcOptions\"\n          :loading=\"loadingVpcs\"\n          option-key=\"key\"\n          :multiple=\"true\"\n          :rules=\"rules && rules.subnets\"\n          data-testid=\"eks-subnets-dropdown\"\n        >\n          <template #option=\"option\">\n            <span :class=\"{'pl-30': option._isSubnet}\">{{ option.label }}</span>\n          </template>\n        </LabeledSelect>\n      </div>\n      <div\n        v-if=\"chooseSubnet\"\n        class=\"col span-6\"\n      >\n        <LabeledSelect\n          :mode=\"mode\"\n          :disabled=\"!isNew\"\n          label-key=\"eks.securityGroups.label\"\n          :tooltip=\"t('eks.securityGroups.tooltip')\"\n          :options=\"securityGroupOptions\"\n          :multiple=\"true\"\n          :value=\"securityGroups\"\n          :loading=\"loadingSecurityGroups\"\n          data-testid=\"eks-security-groups-dropdown\"\n          @update:value=\"$emit('update:securityGroups', $event)\"\n        />\n      </div>\n    </div>\n  </div>\n</template>\n"]}]}