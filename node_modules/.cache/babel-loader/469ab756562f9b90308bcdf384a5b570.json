{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/IconOrSvg.vue?vue&type=template&id=4d8ba295&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/IconOrSvg.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgbm9ybWFsaXplQ2xhc3MgYXMgX25vcm1hbGl6ZUNsYXNzLCBvcGVuQmxvY2sgYXMgX29wZW5CbG9jaywgY3JlYXRlRWxlbWVudEJsb2NrIGFzIF9jcmVhdGVFbGVtZW50QmxvY2ssIGNyZWF0ZUNvbW1lbnRWTm9kZSBhcyBfY3JlYXRlQ29tbWVudFZOb2RlIH0gZnJvbSAidnVlIgoKY29uc3QgX2hvaXN0ZWRfMSA9IFsic3JjIl0KY29uc3QgX2hvaXN0ZWRfMiA9IHsKICBrZXk6IDIsCiAgY2xhc3M6ICJpY29uIGljb24tZXh0ZW5zaW9uIgp9CgpleHBvcnQgZnVuY3Rpb24gcmVuZGVyKF9jdHgsIF9jYWNoZSwgJHByb3BzLCAkc2V0dXAsICRkYXRhLCAkb3B0aW9ucykgewogIHJldHVybiAoJHByb3BzLnNyYykKICAgID8gKF9vcGVuQmxvY2soKSwgX2NyZWF0ZUVsZW1lbnRCbG9jaygiaW1nIiwgewogICAgICAgIGtleTogMCwKICAgICAgICBzcmM6ICRwcm9wcy5zcmMsCiAgICAgICAgY2xhc3M6IF9ub3JtYWxpemVDbGFzcyhbInN2Zy1pY29uIiwgJGRhdGEuY2xhc3NOYW1lXSkKICAgICAgfSwgbnVsbCwgMTAgLyogQ0xBU1MsIFBST1BTICovLCBfaG9pc3RlZF8xKSkKICAgIDogKCRwcm9wcy5pY29uKQogICAgICA/IChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soImkiLCB7CiAgICAgICAgICBrZXk6IDEsCiAgICAgICAgICBjbGFzczogX25vcm1hbGl6ZUNsYXNzKFsiaWNvbiBncm91cC1pY29uIiwgJHByb3BzLmljb25dKQogICAgICAgIH0sIG51bGwsIDIgLyogQ0xBU1MgKi8pKQogICAgICA6IChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soImkiLCBfaG9pc3RlZF8yKSkKfQ=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/IconOrSvg.vue"], "names": [], "mappings": ";;;;;EA8KI,KAAK,EAAC,qBAAqB;;;;UAZrB,UAAG;qBADX,oBAKC;;QAHE,GAAG,EAAE,UAAG;QACT,KAAK,mBAAC,UAAU,EACR,eAAS;;OAGN,WAAI;uBADjB,oBAIE;;UAFA,KAAK,mBAAC,iBAAiB,EACf,WAAI;;uBAEd,oBAGE,KAHF,UAGE", "sourcesContent": ["<script>\n\n/**\n * This component renders the icon in the top level menu.\n * Icon can either be via a font via the 'icon' property or an svg via the 'src' property\n *\n * The trickiness here is that we want the icon to be the correct color - both normally and when hovered\n * For a font icon, this is easy, since we just set the color css property\n * For an svg icon included with the <img> tag this is harder - there is no way to apply css to\n * the svg brought in this way - the workaround is to apply a css filter - in order to do this we\n * need to generate the css filter for the required color - the code for that is in the 'svg-filter' utility\n *\n * We cache filters and css for given colors, so we only generate them once.\n *\n * This makes the code here look complex - but we are essentially generating the css filters\n * and then injecting custom css into the document so that any icons included via svg will\n * show with the desired colors for the theme.\n */\nimport { Solver } from '@shell/utils/svg-filter';\nimport { colorToRgb, mapStandardColors, normalizeHex } from '@shell/utils/color';\n\nconst filterCache = {};\nconst cssCache = {};\n\nconst colors = {\n  header: {\n    color: '--header-btn-text',\n    hover: '--header-btn-text-hover'\n  },\n  primary: {\n    color: '--link',\n    hover: '--primary-hover-text'\n  }\n};\n\nexport default {\n  name:  'IconOrSvg',\n  props: {\n    src: {\n      type:    String,\n      default: () => undefined,\n    },\n    icon: {\n      type:    String,\n      default: () => undefined,\n    },\n    color: {\n      type:    String,\n      default: () => 'primary',\n    }\n  },\n\n  data() {\n    return { className: '' };\n  },\n\n  created() {\n    if (this.src) {\n      this.setColor();\n    }\n  },\n\n  methods: {\n    setColor() {\n      const currTheme = this.$store.getters['prefs/theme'];\n      let uiColor, hoverColor;\n\n      // grab css vars values based on the actual stylesheets, depending on the theme applied\n      // use for loops to minimize computation\n      for (let i = 0; i < Object.keys(document.styleSheets).length; i++) {\n        let found = false;\n        const stylesheet = document.styleSheets[i];\n\n        if (stylesheet && stylesheet.cssRules) {\n          for (let x = 0; x < Object.keys(stylesheet.cssRules).length; x++) {\n            const cssRules = stylesheet.cssRules[x];\n\n            if (cssRules.selectorText && ((currTheme === 'light' && (cssRules.selectorText.includes('body') || cssRules.selectorText.includes('BODY')) &&\n              cssRules.selectorText.includes('.theme-light') && cssRules.style.cssText.includes('--link:')) ||\n              (currTheme === 'dark' && cssRules.selectorText.includes('.theme-dark')))) {\n              // grab the colors to be used on the icon from the css rules\n              uiColor = mapStandardColors(cssRules.style.getPropertyValue(colors[this.color].color).trim());\n              hoverColor = mapStandardColors(cssRules.style.getPropertyValue(colors[this.color].hover).trim());\n\n              // normalize hex colors (#xxx to #xxxxxx)\n              uiColor = normalizeHex(uiColor);\n              hoverColor = normalizeHex(hoverColor);\n\n              found = true;\n              break;\n            }\n          }\n        }\n        if (found) {\n          break;\n        } else {\n          continue;\n        }\n      }\n\n      const uiColorRGB = colorToRgb(uiColor);\n      const hoverColorRGB = colorToRgb(hoverColor);\n      const uiColorStr = `${ uiColorRGB.r }-${ uiColorRGB.g }-${ uiColorRGB.b }`;\n      const hoverColorStr = `${ hoverColorRGB.r }-${ hoverColorRGB.g }-${ hoverColorRGB.b }`;\n\n      const className = `svg-icon-${ uiColorStr }-${ hoverColorStr }`;\n\n      if (!cssCache[className]) {\n        let hoverFilter = filterCache[hoverColor];\n\n        if (!hoverFilter) {\n          const solver = new Solver(hoverColorRGB);\n          const res = solver.solve();\n\n          hoverFilter = res?.filter;\n          filterCache[hoverColor] = hoverFilter;\n        }\n\n        let mainFilter = filterCache[uiColor];\n\n        if (!mainFilter) {\n          const solver = new Solver(uiColorRGB);\n          const res = solver.solve();\n\n          mainFilter = res?.filter;\n          filterCache[uiColor] = mainFilter;\n        }\n\n        // Add stylesheet (added as global styles)\n        const styles = `\n          img.${ className } {\n            ${ mainFilter };\n          }\n          img.${ className }:hover {\n            ${ hoverFilter };\n          }\n          button:hover > img.${ className } {\n            ${ hoverFilter };\n          }\n          li:hover > img.${ className } {\n            ${ hoverFilter };\n          }\n          a.option:hover > img.${ className } {\n            ${ hoverFilter };\n          }      `;\n\n        const styleSheet = document.createElement('style');\n\n        styleSheet.innerText = styles;\n        document.head.appendChild(styleSheet);\n\n        cssCache[className] = true;\n      }\n\n      this['className'] = className;\n    }\n  }\n};\n</script>\n\n<template>\n  <img\n    v-if=\"src\"\n    :src=\"src\"\n    class=\"svg-icon\"\n    :class=\"className\"\n  >\n  <i\n    v-else-if=\"icon\"\n    class=\"icon group-icon\"\n    :class=\"icon\"\n  />\n  <i\n    v-else\n    class=\"icon icon-extension\"\n  />\n</template>\n\n<style lang=\"scss\" scoped>\n  .svg-icon {\n    height: 24px;\n    width: 24px;\n  }\n</style>\n"]}]}