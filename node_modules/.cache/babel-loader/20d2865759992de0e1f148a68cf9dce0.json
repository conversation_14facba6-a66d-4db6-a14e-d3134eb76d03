{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??clonedRuleSet-44.use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/index.vue?vue&type=script&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/index.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgZGVmaW5lQ29tcG9uZW50IH0gZnJvbSAndnVlJzsKaW1wb3J0IEtub3duSG9zdHNFZGl0RGlhbG9nIGZyb20gJy4vS25vd25Ib3N0c0VkaXREaWFsb2cudnVlJzsKaW1wb3J0IHsgX0VESVQsIF9WSUVXIH0gZnJvbSAnQHNoZWxsL2NvbmZpZy9xdWVyeS1wYXJhbXMnOwpleHBvcnQgZGVmYXVsdCBkZWZpbmVDb21wb25lbnQoewogICAgbmFtZTogJ1NTSEtub3duSG9zdHMnLAogICAgZW1pdHM6IFsndXBkYXRlOnZhbHVlJ10sCiAgICBwcm9wczogewogICAgICAgIHZhbHVlOiB7CiAgICAgICAgICAgIHR5cGU6IFN0cmluZywKICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUKICAgICAgICB9LAogICAgICAgIG1vZGU6IHsKICAgICAgICAgICAgdHlwZTogU3RyaW5nLAogICAgICAgICAgICBkZWZhdWx0OiBfRURJVAogICAgICAgIH0sCiAgICB9LAogICAgY29tcG9uZW50czogeyBLbm93bkhvc3RzRWRpdERpYWxvZyB9LAogICAgY29tcHV0ZWQ6IHsKICAgICAgICBpc1ZpZXdNb2RlKCkgewogICAgICAgICAgICByZXR1cm4gdGhpcy5tb2RlID09PSBfVklFVzsKICAgICAgICB9LAogICAgICAgIC8vIFRoZSBudW1iZXIgb2YgZW50cmllcyAtIGV4Y2x1ZGUgZW1wdHkgbGluZXMgYW5kIGNvbW1lbnRzCiAgICAgICAgZW50cmllcygpIHsKICAgICAgICAgICAgcmV0dXJuIHRoaXMudmFsdWUuc3BsaXQoJ1xuJykuZmlsdGVyKChsaW5lKSA9PiAhIWxpbmUudHJpbSgpLmxlbmd0aCAmJiAhbGluZS5zdGFydHNXaXRoKCcjJykpLmxlbmd0aDsKICAgICAgICB9LAogICAgICAgIHN1bW1hcnkoKSB7CiAgICAgICAgICAgIHJldHVybiB0aGlzLnQoJ3NlY3JldC5zc2guZWRpdEtub3duSG9zdHMuZW50cmllcycsIHsgZW50cmllczogdGhpcy5lbnRyaWVzIH0pOwogICAgICAgIH0KICAgIH0sCiAgICBtZXRob2RzOiB7CiAgICAgICAgb3BlbkRpYWxvZygpIHsKICAgICAgICAgICAgdmFyIF9hOwogICAgICAgICAgICAoX2EgPSB0aGlzLiRyZWZzLmJ1dHRvbikgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmJsdXIoKTsKICAgICAgICAgICAgdGhpcy4kcmVmcy5lZGl0RGlhbG9nLnNob3dEaWFsb2coKTsKICAgICAgICB9LAogICAgICAgIGRpYWxvZ0Nsb3NlZChyZXN1bHQpIHsKICAgICAgICAgICAgaWYgKHJlc3VsdC5zdWNjZXNzKSB7CiAgICAgICAgICAgICAgICB0aGlzLiRlbWl0KCd1cGRhdGU6dmFsdWUnLCByZXN1bHQudmFsdWUpOwogICAgICAgICAgICB9CiAgICAgICAgfQogICAgfQp9KTsK"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/index.vue"], "names": [], "mappings": "AACA,OAAO,EAAE,eAAc,EAAE,MAAO,KAAK,CAAA;AACrC,OAAO,oBAAmB,MAAO,4BAA4B,CAAA;AAC7D,OAAO,EAAE,KAAK,EAAE,KAAI,EAAE,MAAO,4BAA4B,CAAA;AAEzD,eAAe,eAAe,CAAC;IAC7B,IAAI,EAAE,eAAe;IAErB,KAAK,EAAE,CAAC,cAAc,CAAC;IAEvB,KAAK,EAAE;QACL,KAAK,EAAE;YACL,IAAI,EAAM,MAAM;YAChB,QAAQ,EAAE,IAAG;SACd;QAED,IAAI,EAAE;YACJ,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,KAAI;SACd;KACF;IAED,UAAU,EAAE,EAAE,oBAAmB,EAAG;IAEpC,QAAQ,EAAE;QACR,UAAU;YACR,OAAO,IAAI,CAAC,IAAG,KAAM,KAAK,CAAA;QAC5B,CAAC;QAED,2DAA0D;QAC1D,OAAO;YACL,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,IAAY,EAAE,EAAC,CAAE,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAK,IAAK,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAA;QAC9G,CAAC;QAED,OAAO;YACL,OAAO,IAAI,CAAC,CAAC,CAAC,mCAAmC,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,OAAM,EAAG,CAAC,CAAA;QAC/E,CAAA;KACD;IAED,OAAO,EAAE;QACP,UAAU;;YACR,MAAC,IAAI,CAAC,KAAK,CAAC,MAA2B,0CAAE,IAAI,EAAE,CAAA;YAC9C,IAAI,CAAC,KAAK,CAAC,UAAkB,CAAC,UAAU,EAAE,CAAA;QAC7C,CAAC;QAED,YAAY,CAAC,MAAW;YACtB,IAAI,MAAM,CAAC,OAAO,EAAE,CAAA;gBAClB,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,MAAM,CAAC,KAAK,CAAC,CAAA;YAC1C,CAAA;QACF,CAAA;KACF;CACD,CAAC,CAAA", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/index.vue.tsx", "sourceRoot": "", "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent } from 'vue';\nimport KnownHostsEditDialog from './KnownHostsEditDialog.vue';\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\n\nexport default defineComponent({\n  name: 'SSHKnownHosts',\n\n  emits: ['update:value'],\n\n  props: {\n    value: {\n      type:     String,\n      required: true\n    },\n\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n  },\n\n  components: { KnownHostsEditDialog },\n\n  computed: {\n    isViewMode() {\n      return this.mode === _VIEW;\n    },\n\n    // The number of entries - exclude empty lines and comments\n    entries() {\n      return this.value.split('\\n').filter((line: string) => !!line.trim().length && !line.startsWith('#')).length;\n    },\n\n    summary() {\n      return this.t('secret.ssh.editKnownHosts.entries', { entries: this.entries });\n    }\n  },\n\n  methods: {\n    openDialog() {\n      (this.$refs.button as HTMLInputElement)?.blur();\n      (this.$refs.editDialog as any).showDialog();\n    },\n\n    dialogClosed(result: any) {\n      if (result.success) {\n        this.$emit('update:value', result.value);\n      }\n    }\n  }\n});\n</script>\n<template>\n  <div\n    class=\"input-known-ssh-hosts labeled-input\"\n    data-testid=\"input-known-ssh-hosts\"\n  >\n    <label>{{ t('secret.ssh.knownHosts') }}</label>\n    <div\n      class=\"hosts-input\"\n      data-testid=\"input-known-ssh-hosts_summary\"\n    >\n      {{ summary }}\n    </div>\n    <template v-if=\"!isViewMode\">\n      <button\n        ref=\"button\"\n        data-testid=\"input-known-ssh-hosts_open-dialog\"\n        class=\"show-dialog-btn btn\"\n        @click=\"openDialog\"\n      >\n        <i class=\"icon icon-edit\" />\n      </button>\n\n      <KnownHostsEditDialog\n        ref=\"editDialog\"\n        :value=\"value\"\n        :mode=\"mode\"\n        @closed=\"dialogClosed\"\n      />\n    </template>\n  </div>\n</template>\n<style lang=\"scss\" scoped>\n  .input-known-ssh-hosts {\n    display: flex;\n    justify-content: space-between;\n\n    .hosts-input {\n      cursor: default;\n      line-height: calc(18px + 1px);\n      padding: 18px 0 0 0;\n    }\n\n    .show-dialog-btn {\n      display: contents;\n      background-color: transparent;\n    }\n  }\n</style>\n"]}]}