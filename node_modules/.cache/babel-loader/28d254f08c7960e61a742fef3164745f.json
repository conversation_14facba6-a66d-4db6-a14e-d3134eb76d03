{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ModalWithCard.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ModalWithCard.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCB7IENhcmQgfSBmcm9tICdAY29tcG9uZW50cy9DYXJkJzsKaW1wb3J0IHsgQmFubmVyIH0gZnJvbSAnQGNvbXBvbmVudHMvQmFubmVyJzsKaW1wb3J0IEFzeW5jQnV0dG9uIGZyb20gJ0BzaGVsbC9jb21wb25lbnRzL0FzeW5jQnV0dG9uJzsKaW1wb3J0IEFwcE1vZGFsIGZyb20gJ0BzaGVsbC9jb21wb25lbnRzL0FwcE1vZGFsLnZ1ZSc7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ01vZGFsV2l0aENhcmQnLAoKICBlbWl0czogWydjbG9zZScsICdmaW5pc2gnXSwKCiAgY29tcG9uZW50czogewogICAgQ2FyZCwgQmFubmVyLCBBc3luY0J1dHRvbiwgQXBwTW9kYWwKICB9LAoKICBwcm9wczogewogICAgbmFtZTogewogICAgICB0eXBlOiAgICAgU3RyaW5nLAogICAgICByZXF1aXJlZDogdHJ1ZQogICAgfSwKCiAgICBjbG9zZVRleHQ6IHsKICAgICAgdHlwZTogICAgU3RyaW5nLAogICAgICBkZWZhdWx0OiAnQ2xvc2UnCiAgICB9LAoKICAgIHNhdmVUZXh0OiB7CiAgICAgIHR5cGU6ICAgIFN0cmluZywKICAgICAgZGVmYXVsdDogJ2NyZWF0ZScKICAgIH0sCgogICAgd2lkdGg6IHsKICAgICAgdHlwZTogICAgW1N0cmluZywgTnVtYmVyXSwKICAgICAgZGVmYXVsdDogJzUwJScKICAgIH0sCgogICAgaGVpZ2h0OiB7CiAgICAgIHR5cGU6ICAgIFtTdHJpbmcsIE51bWJlcl0sCiAgICAgIGRlZmF1bHQ6ICdhdXRvJwogICAgfSwKCiAgICBlcnJvcnM6IHsKICAgICAgdHlwZTogICAgQXJyYXksCiAgICAgIGRlZmF1bHQ6ICgpID0+IHsKICAgICAgICByZXR1cm4gW107CiAgICAgIH0KICAgIH0KICB9LAoKICBtZXRob2RzOiB7CiAgICBoaWRlKCkgewogICAgICB0aGlzLiRlbWl0KCdjbG9zZScpOwogICAgfSwKICB9Cn07Cgo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ModalWithCard.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAErD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAErB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpC,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX;IACF;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;EACH;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ModalWithCard.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { Card } from '@components/Card';\nimport { Banner } from '@components/Banner';\nimport AsyncButton from '@shell/components/AsyncButton';\nimport AppModal from '@shell/components/AppModal.vue';\n\nexport default {\n  name: 'ModalWithCard',\n\n  emits: ['close', 'finish'],\n\n  components: {\n    Card, Banner, AsyncButton, AppModal\n  },\n\n  props: {\n    name: {\n      type:     String,\n      required: true\n    },\n\n    closeText: {\n      type:    String,\n      default: 'Close'\n    },\n\n    saveText: {\n      type:    String,\n      default: 'create'\n    },\n\n    width: {\n      type:    [String, Number],\n      default: '50%'\n    },\n\n    height: {\n      type:    [String, Number],\n      default: 'auto'\n    },\n\n    errors: {\n      type:    Array,\n      default: () => {\n        return [];\n      }\n    }\n  },\n\n  methods: {\n    hide() {\n      this.$emit('close');\n    },\n  }\n};\n\n</script>\n\n<template>\n  <app-modal\n    :name=\"name\"\n    :width=\"width\"\n    :click-to-close=\"false\"\n    :height=\"height\"\n    v-bind=\"$attrs\"\n    class=\"modal\"\n    data-testid=\"mvc__card\"\n    @close=\"$emit('finish', $event)\"\n  >\n    <Card\n      class=\"modal\"\n      :show-highlight-border=\"false\"\n    >\n      <template #title>\n        <h4 class=\"text-default-text\">\n          <slot name=\"title\" />\n        </h4>\n      </template>\n\n      <template #body>\n        <slot name=\"content\" />\n\n        <div\n          v-for=\"(err,idx) in errors\"\n          :key=\"idx\"\n        >\n          <Banner\n            class=\"banner\"\n            color=\"error\"\n            :label=\"err\"\n          />\n        </div>\n      </template>\n\n      <template #actions>\n        <slot name=\"footer\">\n          <div class=\"footer\">\n            <button\n              class=\"btn role-secondary mr-20\"\n              @click.prevent=\"hide\"\n            >\n              {{ closeText }}\n            </button>\n\n            <AsyncButton\n              :mode=\"saveText\"\n              @click=\"$emit('finish', $event)\"\n            />\n          </div>\n        </slot>\n      </template>\n    </Card>\n  </app-modal>\n</template>\n\n<style lang=\"scss\" scoped>\n.footer {\n  width: 100%;\n  display: flex;\n  justify-content: center;\n}\n\n.banner {\n  margin-bottom: 0px;\n}\n</style>\n\n<style lang=\"scss\">\n.modal {\n  border-radius: var(--border-radius);\n  max-height: 100vh;\n\n  &.card-container {\n    box-shadow: none;\n  }\n}\n</style>\n"]}]}