{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SortableTable/THead.vue?vue&type=template&id=06369a08&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SortableTable/THead.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SortableTable/THead.vue"], "names": ["t"], "mappings": ";;;;;qBAmQY,KAAK,EAAC,SAAS;;;EAKb,KAAK,EAAC,YAAY;;;;EAOpB,KAAK,EAAC,MAAM;EACZ,aAAW,EAAC,MAAM;;qBAKhB,KAAK,EAAC,gCAAgC;qBAElC,KAAK,EAAC,YAAY;;;;;EAqB1B,GAAG,EAAC,eAAe;EACnB,KAAK,EAAC,qBAAqB;;;;EAkBvB,KAAK,EAAC,wBAAwB;;sBAExB,KAAK,EAAC,4BAA4B;sBAavC,KAAK,EAAC,kCAAkC;;;;;;;;;wBAlHrD,oBA6IQ;IA5IN,oBA2IK;MA3IA,KAAK,8BAAc,cAAO,iBAAiB,8BAAqB;;OAE3D,mBAAY;yBADpB,oBAYK;;YAVF,KAAK,EAAE,iBAAU;;YAElB,aAOE;cANQ,KAAK,EAAE,cAAK;sEAAL,cAAK;cACpB,KAAK,EAAC,OAAO;cACb,aAAW,EAAC,iCAAiC;cAC5C,aAAa,EAAE,wBAAe;cAC9B,QAAQ,EAAE,aAAM,IAAI,gBAAS;cAC7B,iBAAe,EAAEA,MAAC;;;;;OAIf,sBAAe;yBADvB,oBAGE;;YADC,KAAK,EAAE,kBAAW;;;;yBAErB,oBAuDK,6BAtDa,cAAO,GAAf,GAAG;8CADb,oBAuDK;UApDF,GAAG,EAAE,GAAG,CAAC,IAAI;UACb,KAAK,EAAE,GAAG,CAAC,KAAK;UAChB,KAAK,EAAE,GAAG,CAAC,KAAK;UAChB,KAAK,+BAAc,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,UAAU,KAAK,GAAG,CAAC,UAAU,GAE1D,6BAA6B;UADlC,QAAQ,EAAE,GAAG,CAAC,IAAI;UAElB,WAAS,EAAE,iBAAQ,CAAC,GAAG;UACvB,OAAK,4BAAU,mBAAU,CAAC,MAAM,EAAE,GAAG;UACrC,OAAK;iCAAQ,mBAAU,CAAC,MAAM,EAAE,GAAG;iCACtB,mBAAU,CAAC,MAAM,EAAE,GAAG;;;UAEpC,oBAwCM;YAvCJ,KAAK,mBAAC,wBAAwB,sBACF,2BAAoB,KAAK,GAAG,CAAC,QAAQ;;2CAEjE,oBAWM,OAXN,UAWM;8BAPJ,oBAAqC;wCAAjB,eAAQ,CAAC,GAAG;;;eAExB,GAAG,CAAC,QAAQ;iCADpB,oBAKO,QALP,UAKO,mBADF,GAAG,CAAC,QAAQ;;;yCARA,gBAAO,CAAC,GAAG;;;aAYtB,GAAG,CAAC,IAAI;+BADhB,oBAuBM,OAvBN,UAuBM;kCAlBJ,oBAIE,KAJF,UAIE;6BAHQ,2BAAoB,KAAK,GAAG,CAAC,QAAQ;+CAC5BA,MAAC;;;kBAGpB,oBAYO,QAZP,UAYO;8CAXL,oBAAgD,OAA7C,KAAK,EAAC,oCAAoC;;qBAErC,kBAAS,CAAC,GAAG,MAAM,iBAAU;uCADrC,oBAIE;;0BAFA,KAAK,EAAC,mCAAmC;0BACxC,GAAG,EAAEA,MAAC;;;;qBAGD,kBAAS,CAAC,GAAG,KAAK,iBAAU;uCADpC,oBAIE;;0BAFA,KAAK,EAAC,iCAAiC;0BACtC,GAAG,EAAEA,MAAC;;;;;;;;oBAhDN,2BAAoB,KAAK,2BAAoB,IAAI,GAAG,CAAC,YAAY;;;;OAuDpE,iBAAU,IAAI,2BAAoB,IAAI,uBAAgB,CAAC,MAAM;yBADrE,oBA4DK;;YA1DF,KAAK,EAAE,sBAAe;;YAEvB,oBAuDM,OAvDN,WAuDM;cAnDJ,oBAQS;gBAPP,eAAa,EAAC,MAAM;gBACpB,eAAa,EAAC,OAAO;gBACrB,IAAI,EAAC,QAAQ;gBACb,KAAK,EAAC,gDAAgD;gBACrD,OAAK,0CAAE,yEAAqB;;gBAE7B,oBAA+B,OAA5B,KAAK,EAAC,mBAAmB;;;8BAE9B,oBAyCM;gBAvCJ,KAAK,EAAC,yBAAyB;gBAC9B,KAAK,kBAAE,2BAAqB;;iBAGrB,uBAAc;mCADtB,oBAgBM,OAhBN,WAgBM;sBAZJ,oBAA6F,QAA7F,WAA6F,mBAAjDA,MAAC,yCAAwC,GAAC;;sBACtF,aAUE;wBATQ,KAAK,EAAE,iBAAQ;gFAAR,iBAAQ;wBACvB,KAAK,EAAC,+BAA+B;wBACpC,SAAS,EAAE,IAAI;wBACf,OAAO,EAAE,mBAAY;wBACrB,QAAQ,EAAE,KAAK;wBACf,UAAU,EAAE,KAAK;wBAClB,IAAI,EAAC,MAAM;wBACV,QAAQ,EAAE,KAAK;wBACf,QAAQ,EAAE,KAAK;;;;;gBAGpB,oBAEI,KAFJ,WAEI,mBADCA,MAAC,sCAAqC,iBAC3C;;gBACA,oBAeK;qCAdH,oBAaK,6BAZoB,uBAAgB,GAA/B,GAAG,EAAE,KAAK;0DADpB,oBAaK;sBAVF,GAAG,EAAE,KAAK;sBACV,KAAK,gCAAgB,GAAG,CAAC,gBAAgB;;sCAE1C,aAME;wBAJQ,KAAK,EAAE,GAAG,CAAC,YAAY;uDAAhB,GAAG,CAAC,YAAY,wBAGhB,6BAAoB,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK;wBAFrD,KAAK,EAAC,wBAAwB;wBAC7B,KAAK,EAAE,GAAG,CAAC,KAAK;;kCAHR,GAAG,CAAC,gBAAgB;;;+BALvB,GAAG,CAAC,aAAa;;;;;yBA3BrB,gCAA0B;;;;WA4C3B,iBAAU;2BADvB,oBAGE;;cADC,KAAK,EAAE,sBAAe", "sourcesContent": ["<script>\nimport { Checkbox } from '@components/Form/Checkbox';\nimport { SOME, NONE } from './selection';\nimport { AUTO, CENTER, fitOnScreen } from '@shell/utils/position';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\n\nexport default {\n  emits: ['update-cols-options', 'on-toggle-all', 'group-value-change', 'on-sort-change', 'col-visibility-change'],\n\n  components: { Checkbox, LabeledSelect },\n  props:      {\n    columns: {\n      type:     Array,\n      required: true\n    },\n    sortBy: {\n      type:     String,\n      required: true\n    },\n    defaultSortBy: {\n      type:    String,\n      default: ''\n    },\n    group: {\n      type:    String,\n      default: ''\n    },\n    groupOptions: {\n      type:    Array,\n      default: () => []\n    },\n    descending: {\n      type:     Boolean,\n      required: true\n    },\n    hasAdvancedFiltering: {\n      type:     Boolean,\n      required: false\n    },\n    tableColsOptions: {\n      type:    Array,\n      default: () => [],\n    },\n    tableActions: {\n      type:     Boolean,\n      required: true,\n    },\n    rowActions: {\n      type:     Boolean,\n      required: true,\n    },\n    howMuchSelected: {\n      type:     String,\n      required: true,\n    },\n    checkWidth: {\n      type:    Number,\n      default: 30,\n    },\n    rowActionsWidth: {\n      type:     Number,\n      required: true\n    },\n    subExpandColumn: {\n      type:    Boolean,\n      default: false,\n    },\n    expandWidth: {\n      type:    Number,\n      default: 30,\n    },\n    labelFor: {\n      type:     Function,\n      required: true,\n    },\n    noRows: {\n      type:    Boolean,\n      default: true,\n    },\n    noResults: {\n      type:    Boolean,\n      default: true,\n    },\n    loading: {\n      type:     Boolean,\n      required: false,\n    },\n  },\n\n  data() {\n    return {\n      tableColsOptionsVisibility: false,\n      tableColsMenuPosition:      null\n    };\n  },\n\n  watch: {\n    advancedFilteringValues() {\n      // passing different dummy args to make sure update is triggered\n      this.watcherUpdateLiveAndDelayed(true, false);\n    },\n    tableColsOptionsVisibility(neu) {\n      if (neu) {\n        // check if user clicked outside the table cols options box\n        window.addEventListener('click', this.onClickOutside);\n\n        // update filtering options and toggable cols every time dropdown is open\n        this.$emit('update-cols-options');\n      } else {\n        // unregister click event\n        window.removeEventListener('click', this.onClickOutside);\n      }\n    }\n  },\n  computed: {\n    isAll: {\n      get() {\n        return this.howMuchSelected !== NONE;\n      },\n\n      set(value) {\n        this.$emit('on-toggle-all', value);\n      }\n    },\n    hasAdvGrouping() {\n      return this.group?.length && this.groupOptions?.length;\n    },\n    advGroup: {\n      get() {\n        return this.group || this.advGroup;\n      },\n\n      set(val) {\n        this.$emit('group-value-change', val);\n      }\n    },\n\n    isIndeterminate() {\n      return this.howMuchSelected === SOME;\n    },\n    hasColumnWithSubLabel() {\n      return this.columns.some((col) => col.subLabel);\n    }\n  },\n\n  methods: {\n    changeSort(e, col) {\n      if ( !col.sort ) {\n        return;\n      }\n\n      let desc = false;\n\n      if ( this.sortBy === col.name ) {\n        desc = !this.descending;\n      }\n\n      this.$emit('on-sort-change', col.name, desc);\n    },\n\n    isCurrent(col) {\n      return col.name === this.sortBy;\n    },\n\n    ariaSort(col) {\n      if (this.isCurrent(col)) {\n        return this.descending ? this.t('generic.descending') : this.t('generic.ascending');\n      }\n\n      return this.t('generic.none');\n    },\n\n    tableColsOptionsClick(ev) {\n      // set menu position\n      const menu = document.querySelector('.table-options-container');\n      const elem = document.querySelector('.table-options-btn');\n\n      this.tableColsMenuPosition = fitOnScreen(menu, ev || elem, {\n        overlapX:  true,\n        fudgeX:    326,\n        fudgeY:    -22,\n        positionX: CENTER,\n        positionY: AUTO,\n      });\n\n      // toggle visibility\n      this.tableColsOptionsVisibility = !this.tableColsOptionsVisibility;\n    },\n\n    onClickOutside(event) {\n      const tableOpts = this.$refs['table-options'];\n\n      if (!tableOpts || tableOpts.contains(event.target)) {\n        return;\n      }\n      this.tableColsOptionsVisibility = false;\n    },\n\n    tableOptionsCheckbox(value, label) {\n      this.$emit('col-visibility-change', {\n        label,\n        value\n      });\n    },\n\n    tooltip(col) {\n      if (!col.tooltip) {\n        return null;\n      }\n\n      const exists = this.$store.getters['i18n/exists'];\n\n      return exists(col.tooltip) ? this.t(col.tooltip) : col.tooltip;\n    },\n  }\n\n};\n</script>\n\n<template>\n  <thead>\n    <tr :class=\"{'loading': loading, 'top-aligned': hasColumnWithSubLabel}\">\n      <th\n        v-if=\"tableActions\"\n        :width=\"checkWidth\"\n      >\n        <Checkbox\n          v-model:value=\"isAll\"\n          class=\"check\"\n          data-testid=\"sortable-table_check_select_all\"\n          :indeterminate=\"isIndeterminate\"\n          :disabled=\"noRows || noResults\"\n          :alternate-label=\"t('sortableTable.genericGroupCheckbox')\"\n        />\n      </th>\n      <th\n        v-if=\"subExpandColumn\"\n        :width=\"expandWidth\"\n      />\n      <th\n        v-for=\"(col) in columns\"\n        v-show=\"!hasAdvancedFiltering || (hasAdvancedFiltering && col.isColVisible)\"\n        :key=\"col.name\"\n        :align=\"col.align || 'left'\"\n        :width=\"col.width\"\n        :class=\"{ sortable: col.sort, [col.breakpoint]: !!col.breakpoint}\"\n        :tabindex=\"col.sort ? 0 : -1\"\n        class=\"sortable-table-head-element\"\n        :aria-sort=\"ariaSort(col)\"\n        @click.prevent=\"changeSort($event, col)\"\n        @keyup.enter=\"changeSort($event, col)\"\n        @keyup.space=\"changeSort($event, col)\"\n      >\n        <div\n          class=\"table-header-container\"\n          :class=\"{ 'not-filterable': hasAdvancedFiltering && !col.isFilter }\"\n        >\n          <div\n            v-clean-tooltip=\"tooltip(col)\"\n            class=\"content\"\n          >\n            <span v-clean-html=\"labelFor(col)\" />\n            <span\n              v-if=\"col.subLabel\"\n              class=\"text-muted\"\n            >\n              {{ col.subLabel }}\n            </span>\n          </div>\n          <div\n            v-if=\"col.sort\"\n            class=\"sort\"\n            aria-hidden=\"true\"\n          >\n            <i\n              v-show=\"hasAdvancedFiltering && !col.isFilter\"\n              v-clean-tooltip=\"t('sortableTable.tableHeader.noFilter')\"\n              class=\"icon icon-info not-filter-icon\"\n            />\n            <span class=\"icon-stack\">\n              <i class=\"icon icon-sort icon-stack-1x faded\" />\n              <i\n                v-if=\"isCurrent(col) && !descending\"\n                class=\"icon icon-sort-down icon-stack-1x\"\n                :alt=\"t('sortableTable.alt.sortingIconDesc')\"\n              />\n              <i\n                v-if=\"isCurrent(col) && descending\"\n                class=\"icon icon-sort-up icon-stack-1x\"\n                :alt=\"t('sortableTable.alt.sortingIconAsc')\"\n              />\n            </span>\n          </div>\n        </div>\n      </th>\n      <th\n        v-if=\"rowActions && hasAdvancedFiltering && tableColsOptions.length\"\n        :width=\"rowActionsWidth\"\n      >\n        <div\n          ref=\"table-options\"\n          class=\"table-options-group\"\n        >\n          <button\n            aria-haspopup=\"true\"\n            aria-expanded=\"false\"\n            type=\"button\"\n            class=\"btn btn-sm role-multi-action table-options-btn\"\n            @click=\"tableColsOptionsClick\"\n          >\n            <i class=\"icon icon-actions\" />\n          </button>\n          <div\n            v-show=\"tableColsOptionsVisibility\"\n            class=\"table-options-container\"\n            :style=\"tableColsMenuPosition\"\n          >\n            <div\n              v-if=\"hasAdvGrouping\"\n              class=\"table-options-grouping\"\n            >\n              <span class=\"table-options-col-subtitle\">{{ t('sortableTable.tableHeader.groupBy') }}:</span>\n              <LabeledSelect\n                v-model:value=\"advGroup\"\n                class=\"table-options-grouping-select\"\n                :clearable=\"true\"\n                :options=\"groupOptions\"\n                :disabled=\"false\"\n                :searchable=\"false\"\n                mode=\"edit\"\n                :multiple=\"false\"\n                :taggable=\"false\"\n              />\n            </div>\n            <p class=\"table-options-col-subtitle mb-20\">\n              {{ t('sortableTable.tableHeader.show') }}:\n            </p>\n            <ul>\n              <li\n                v-for=\"(col, index) in tableColsOptions\"\n                v-show=\"col.isTableOption\"\n                :key=\"index\"\n                :class=\"{ 'visible': !col.preventColToggle }\"\n              >\n                <Checkbox\n                  v-show=\"!col.preventColToggle\"\n                  v-model:value=\"col.isColVisible\"\n                  class=\"table-options-checkbox\"\n                  :label=\"col.label\"\n                  @update:value=\"tableOptionsCheckbox($event, col.label)\"\n                />\n              </li>\n            </ul>\n          </div>\n        </div>\n      </th>\n      <th\n        v-else-if=\"rowActions\"\n        :width=\"rowActionsWidth\"\n      />\n    </tr>\n  </thead>\n</template>\n\n  <style lang=\"scss\" scoped>\n    .table-options-group {\n\n      .table-options-btn.role-multi-action {\n        background-color: transparent;\n        border: none;\n        font-size: 18px;\n        &:hover, &:focus {\n          background-color: var(--accent-btn);\n          box-shadow: none;\n        }\n      }\n      .table-options-container {\n        width: 350px;\n        border: 1px solid var(--primary);\n        background-color: var(--body-bg);\n        padding: 20px;\n        z-index: 1;\n\n        .table-options-grouping {\n          display: flex;\n          align-items: center;\n          margin-bottom: 20px;\n\n          span {\n            white-space: nowrap;\n            margin-right: 10px;\n          }\n        }\n\n        ul {\n          list-style: none;\n          margin: 0;\n          padding: 0;\n          max-height: 200px;\n          overflow-y: auto;\n\n          li {\n            margin: 0;\n            padding: 0;\n\n            &.visible {\n              margin: 0 0 10px 0;\n            }\n          }\n        }\n      }\n    }\n\n    .sortable > SPAN {\n      cursor: pointer;\n      user-select: none;\n      white-space: nowrap;\n      &:hover,\n      &:active {\n        text-decoration: underline;\n        color: var(--body-text);\n      }\n    }\n\n    .top-aligned th {\n      vertical-align: top;\n      padding-top: 10px;\n    }\n\n    thead {\n      tr {\n        background-color: var(--sortable-table-header-bg);\n        color: var(--body-text);\n        text-align: left;\n        border-bottom: 1px solid var(--sortable-table-top-divider);\n      }\n    }\n\n    th {\n      padding: 8px 5px;\n      font-weight: normal;\n      border: 0;\n      color: var(--body-text);\n\n      &.sortable-table-head-element:focus-visible {\n        @include focus-outline;\n        outline-offset: -4px;\n      }\n\n      .table-header-container {\n        display: inline-flex;\n\n        .content {\n          display: flex;\n          flex-direction: column;\n        }\n\n        &.not-filterable {\n          margin-top: -2px;\n\n          .icon-stack {\n            margin-top: -2px;\n          }\n        }\n\n        .not-filter-icon {\n          font-size: 16px;\n          color: var(--primary);\n          vertical-align: super;\n        }\n      }\n\n      &:first-child {\n        padding-left: 10px;\n      }\n\n      &:last-child {\n        padding-right: 10px;\n      }\n\n      &:not(.sortable) > SPAN {\n        display: block;\n        margin-bottom: 2px;\n      }\n\n      & A {\n        color: var(--body-text);\n      }\n\n      // Aligns with COLUMN_BREAKPOINTS\n      @media only screen and (max-width: map-get($breakpoints, '--viewport-4')) {\n        // HIDE column on sizes below 480px\n        &.tablet, &.laptop, &.desktop {\n          display: none;\n        }\n      }\n      @media only screen and (max-width: map-get($breakpoints, '--viewport-9')) {\n        // HIDE column on sizes below 992px\n        &.laptop, &.desktop {\n          display: none;\n        }\n      }\n      @media only screen and (max-width: map-get($breakpoints, '--viewport-12')) {\n        // HIDE column on sizes below 1281px\n        &.desktop {\n          display: none;\n        }\n      }\n    }\n\n    .icon-stack {\n      width: 12px;\n    }\n\n    .icon-sort {\n      &.faded {\n        opacity: .3;\n      }\n    }\n  </style>\n  <style lang=\"scss\">\n    .table-options-checkbox .checkbox-custom {\n      min-width: 14px;\n    }\n    .table-options-checkbox .checkbox-label {\n      color: var(--body-text);\n    }\n  </style>\n"]}]}