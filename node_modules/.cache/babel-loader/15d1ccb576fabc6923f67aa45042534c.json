{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CruResourceFooter.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CruResourceFooter.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CruResourceFooter.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAElD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAErC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAChD,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;IACV,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAC7C,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACtF,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACjC,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;IAE9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAC3B,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC;EACH,CAAC;AACH,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CruResourceFooter.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapGetters } from 'vuex';\n\nimport AsyncButton from '@shell/components/AsyncButton';\nimport ResourceCancelModal from '@shell/components/ResourceCancelModal';\nimport { _VIEW } from '@shell/config/query-params';\n\nexport default {\n  emits: ['cancel-confirmed', 'finish'],\n\n  components: { AsyncButton, ResourceCancelModal },\n  props:      {\n    mode: {\n      type:    String,\n      default: 'create',\n    },\n\n    isForm: {\n      type:    Boolean,\n      default: true,\n    },\n\n    // Override the set of labels shown on the button from the default save/create.\n    finishButtonMode: {\n      type:    String,\n      default: null,\n    },\n\n    confirmCancelRequired: {\n      type:    Boolean,\n      default: false,\n    },\n\n    confirmBackRequired: {\n      type:    Boolean,\n      default: true,\n    },\n\n    showCancel: {\n      type:    Boolean,\n      default: true\n    },\n\n    /**\n     * Inherited global identifier prefix for tests\n     * Define a term based on the parent component to avoid conflicts on multiple components\n     */\n    componentTestid: {\n      type:    String,\n      default: 'form-footer'\n    }\n  },\n\n  data() {\n    return { isCancelModal: false };\n  },\n\n  computed: {\n    ...mapGetters({ t: 'i18n/t' }),\n\n    isView() {\n      return this.mode === _VIEW;\n    },\n  },\n\n  methods: {\n    checkCancel(isCancel) {\n      if (isCancel) {\n        this.isCancelModal = true;\n      } else {\n        this.isCancelModal = false;\n      }\n      this.$refs.cancelModal.show();\n    },\n\n    confirmCancel(isCancel) {\n      this.$emit('cancel-confirmed', isCancel);\n    },\n  },\n};\n</script>\n\n<template>\n  <div class=\"cru-resource-footer\">\n    <slot name=\"footer-prefix\" />\n    <slot name=\"cancel\">\n      <button\n        v-if=\"!isView && showCancel\"\n        id=\"cru-cancel\"\n        :data-testid=\"componentTestid + '-cancel'\"\n        type=\"button\"\n        class=\"btn role-secondary\"\n        @click=\"confirmCancelRequired ? checkCancel(true) : $emit('cancel-confirmed', true)\"\n      >\n        <t k=\"generic.cancel\" />\n      </button>\n    </slot>\n    <slot :checkCancel=\"checkCancel\">\n      <AsyncButton\n        v-if=\"!isView\"\n        :data-testid=\"componentTestid + '-create'\"\n        :mode=\"finishButtonMode || mode\"\n        @click=\"$emit('finish', $event)\"\n      />\n    </slot>\n    <ResourceCancelModal\n      ref=\"cancelModal\"\n      :is-cancel-modal=\"isCancelModal\"\n      :is-form=\"isForm\"\n      @confirm-cancel=\"confirmCancel($event)\"\n    />\n  </div>\n</template>\n\n<style lang=\"scss\">\n.cru-resource-footer {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 20px;\n  z-index: z-index('cruFooter');\n\n  .btn {\n    margin-left: 20px;\n  }\n}\n\n</style>\n"]}]}