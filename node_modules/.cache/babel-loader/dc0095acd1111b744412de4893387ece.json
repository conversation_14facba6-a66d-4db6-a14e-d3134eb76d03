{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Enum.vue?vue&type=template&id=2b9a1134", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Enum.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Enum.vue"], "names": ["mode", "displayLabel", "question", "value", "disabled", "displayTooltip", "$emit", "showDescription", "displayDescription"], "mappings": ";;qBA6BO,KAAK,EAAC,KAAK;qBACT,KAAK,EAAC,YAAY;;;EAiBrB,KAAK,EAAC,kBAAkB;;;;;;wBAlB5B,oBAsBM,OAtBN,UAsBM;IArBJ,oBAcM,OAdN,UAcM;MAbJ,aAYE;QAXC,IAAI,EAAEA,SAAI;QACV,KAAK,EAAEC,iBAAY;QACnB,OAAO,EAAE,gBAAO;QAChB,WAAW,EAAEC,aAAQ,CAAC,WAAW;QACjC,QAAQ,EAAEA,aAAQ,CAAC,QAAQ;QAC3B,QAAQ,EAAEA,aAAQ,CAAC,QAAQ;QAC3B,KAAK,EAAEC,UAAK;QACZ,QAAQ,EAAEC,aAAQ;QAClB,OAAO,EAAEC,mBAAc;QACvB,UAAU,EAAEH,aAAQ,CAAC,UAAU;QAC/B,gBAAY,uCAAEI,UAAK,iBAAiB,MAAM;;;;KAIvCC,oBAAe;uBADvB,oBAKM,OALN,UAKM,mBADDC,uBAAkB", "sourcesContent": ["<script>\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport Question from './Question';\n\nexport default {\n  emits: ['update:value'],\n\n  components: { LabeledSelect },\n  mixins:     [Question],\n  computed:   {\n    options() {\n      const options = this.question.options;\n\n      if (Array.isArray(options)) {\n        return options;\n      }\n\n      return Object.entries(options).map(([key, value]) => {\n        return {\n          value: key,\n          label: value,\n        };\n      });\n    }\n  }\n};\n</script>\n\n<template>\n  <div class=\"row\">\n    <div class=\"col span-6\">\n      <LabeledSelect\n        :mode=\"mode\"\n        :label=\"displayLabel\"\n        :options=\"options\"\n        :placeholder=\"question.description\"\n        :required=\"question.required\"\n        :multiple=\"question.multiple\"\n        :value=\"value\"\n        :disabled=\"disabled\"\n        :tooltip=\"displayTooltip\"\n        :searchable=\"question.searchable\"\n        @update:value=\"$emit('update:value', $event)\"\n      />\n    </div>\n    <div\n      v-if=\"showDescription\"\n      class=\"col span-6 mt-10\"\n    >\n      {{ displayDescription }}\n    </div>\n  </div>\n</template>\n"]}]}