{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/TopLevelMenu.vue?vue&type=template&id=4367c1c6&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/TopLevelMenu.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/TopLevelMenu.vue"], "names": ["t", "isRancherInHarvester"], "mappings": ";;;qBAgea,KAAK,EAAC,OAAO;;;qBAuBX,KAAK,EAAC,gBAAgB;qBAUxB,KAAK,EAAC,MAAM;;EAYP,KAAK,EAAC,eAAe;EACrB,KAAK,EAAC,4BAA4B;EAClC,MAAM,EAAC,IAAI;EACX,OAAO,EAAC,WAAW;EACnB,KAAK,EAAC,IAAI;;qBAKP,KAAK,EAAC,WAAW;;;EAQxB,KAAK,EAAC,iBAAiB;;sBAElB,KAAK,EAAC,uBAAuB;;;EAK9B,KAAK,EAAC,sBAAsB;;sBAK9B,KAAK,EAAC,QAAQ;;;;EAwEd,KAAK,EAAC,gBAAgB;;;;sBA4BhB,KAAK,EAAC,cAAc;;;EAKlB,KAAK,EAAC,aAAa;;;sBAsBrB,KAAK,EAAC,cAAc;;;EAKlB,KAAK,EAAC,aAAa;;;;EAazB,KAAK,EAAC,gBAAgB;;sBAOrB,KAAK,EAAC,cAAc;;;sBA2BjB,KAAK,EAAC,cAAc;;;EAKlB,KAAK,EAAC,aAAa;;;sBAuBrB,KAAK,EAAC,cAAc;;;EAKlB,KAAK,EAAC,aAAa;;;;EAiB3B,aAAW,EAAC,2BAA2B;EACvC,KAAK,EAAC,eAAe;;sBA0BtB,KAAK,EAAC,UAAU;sBAGf,KAAK,EAAC,gBAAgB;sBAyBd,KAAK,EAAC,aAAa;sBAQ3B,KAAK,EAAC,gBAAgB;sBAkC5B,KAAK,EAAC,QAAQ;;;;;;;;;;;wBAlatB,oBAicM;IAhcJ,gCAAgB;;KAER,WAAK;uBADb,oBAIE;;UAFA,KAAK,EAAC,iBAAiB;UACtB,OAAK,uCAAE,aAAI;;;;IAEd,aAyba,eAzbD,IAAI,EAAC,MAAM;wBAErB,CAsbM;QAtbN,oBAsbM;UArbJ,aAAW,EAAC,WAAW;UACvB,KAAK,mBAAC,WAAW,gBACK,WAAK,gBAAgB,WAAK;UAChD,QAAQ,EAAC,IAAI;UACb,IAAI,EAAC,YAAY;UAChB,YAAU,EAAEA,MAAC;;UAEd,sCAAsB;;UACtB,oBA8BM,OA9BN,UA8BM;YA7BJ,oBAqBM;cApBJ,aAAW,EAAC,gBAAgB;cAC3B,YAAU,EAAEA,MAAC;cACd,IAAI,EAAC,QAAQ;cACb,QAAQ,EAAC,GAAG;cACZ,KAAK,EAAC,MAAM;cACX,OAAK;+DAAQ,eAAM;+DACN,eAAM;;cACnB,OAAK,uCAAE,eAAM;;6BAEd,oBAUkE;gBAThE,KAAK,EAAC,WAAW;gBACjB,KAAK,EAAC,4BAA4B;gBAClC,MAAM,EAAC,IAAI;gBACX,OAAO,EAAC,WAAW;gBACnB,KAAK,EAAC,IAAI;gBACT,GAAG,EAAEA,MAAC;;gBACR,oBAGC;kBAFA,CAAC,EAAC,eAAe;kBACjB,IAAI,EAAC,MAAM;;gBACX,oBAA0D,UAApD,CAAC,EAAC,+CAA+C;;;;YAE3D,oBAMM,OANN,UAMM;cALJ,aAIE;gBAHA,aAAW,EAAC,sBAAsB;gBACjC,GAAG,EAAEA,MAAC;gBACP,WAAS,EAAC,kBAAkB;;;;;UAKlC,kCAAkB;;UAClB,oBA2WM,OA3WN,UA2WM;YA1WJ,oBA4DM;cA3DJ,oCAAoB;;cACpB,oBAsBM;gBAtBA,OAAK,uCAAE,aAAI;;gBACf,aAoBc;kBAnBZ,KAAK,EAAC,8BAA8B;kBACnC,EAAE,EAAE,gBAAgB;kBACrB,IAAI,EAAC,MAAM;kBACV,YAAU,EAAEA,MAAC;;oCAEd,CAUwD;mDAVxD,oBAUwD,OAVxD,UAUwD;sBAHvD,oBAGC;wBAFA,CAAC,EAAC,eAAe;wBACjB,IAAI,EAAC,MAAM;;sBACX,oBAAgD,UAA1C,CAAC,EAAC,qCAAqC;;iDAT5B,yBAAgB,CAACA,MAAC;;;oBAUrC,oBAEM,OAFN,UAEM,mBADDA,MAAC;;;;;;;cAIV,mCAAmB;;eAEX,0BAAiB;iCADzB,oBAiCM,OAjCN,UAiCM;oBA7BJ,oBAOM,OAPN,WAOM;sBANJ,oBAAqC,+BAA5B,2BAAkB;uCAAU,GACrC,oBAAGA,MAAC,2BAA0B,GAC9B;uBACQ,mBAAa;yCADrB,oBAGE,KAHF,WAGE;;;;oBAGJ,oBAmBM,OAnBN,WAmBM;sCAhBJ,oBAMC;wBALC,GAAG,EAAC,eAAe;qFACV,mBAAa;wBACrB,WAAW,EAAEA,MAAC;wBACd,QAAQ,GAAG,WAAK;wBAChB,YAAU,EAAEA,MAAC;;sCAHL,mBAAa;;;sBAKxB,oBAGE;wBAFA,KAAK,mBAAC,4BAA4B,YAChB,mBAAa;;;uBAGzB,mBAAa;yCADrB,oBAIE;;4BAFA,KAAK,EAAC,iBAAiB;4BACtB,OAAK,uCAAE,mBAAa;;;;;;;;YAM7B,yCAAyB;;aACT,gBAAO,CAAC,MAAM;+BAA9B,oBAqCW;8CApCT,oBAAwB,SAAnB,KAAK,EAAC,UAAU;;kBACrB,oBAcM;qBAZIC,yBAAoB;uCAD5B,oBAYI;;0BAVF,KAAK,EAAC,QAAQ;0BACd,QAAQ,EAAC,GAAG;0BACX,OAAK,uCAAE,6BAAoB;;sDAE5B,oBAEE,OADA,KAAK,EAAC,8BAA8B;;0BAEtC,oBAEM,8BADDD,MAAC;;;;;qCAIV,oBAmBM,6BAlBa,eAAM,CAAC,OAAO,GAAvB,CAAC,EAAE,CAAC;0CADd,oBAmBM;sBAjBH,GAAG,EAAE,CAAC;sBACN,OAAK,uCAAE,aAAI;;sBAEZ,aAac;wBAZZ,KAAK,mBAAC,QAAQ,uBAEe,CAAC,CAAC,YAAY;wBAD1C,EAAE,EAAE,CAAC,CAAC,EAAE;wBAET,IAAI,EAAC,MAAM;wBACV,YAAU,KAAKA,MAAC,uCAAuC,CAAC,CAAC,KAAK;;0CAE/D,CAIE;0BAJF,aAIE;4BAHA,KAAK,EAAC,UAAU;4BACf,IAAI,EAAE,CAAC,CAAC,IAAI;4BACZ,GAAG,EAAE,CAAC,CAAC,GAAG;;;0BAEb,oBAAwB,8BAAhB,CAAC,CAAC,KAAK;;;;;;;;;;YAKrB,qCAAqB;;eACH,yBAAgB;+BAAlC,oBA8LW;kBA7LT,oBA0KM;oBAzKJ,GAAG,EAAC,aAAa;oBACjB,KAAK,EAAC,UAAU;oBACf,KAAK,kBAAE,6BAAoB;;oBAE5B,wCAAwB;;qBAEhB,wBAAe,IAAI,oBAAW,CAAC,MAAM;uCAD7C,oBA+EM,OA/EN,WA+EM;6CA3EJ,oBAoEM,6BAnEiB,eAAM,CAAC,WAAW,GAA/B,CAAC,EAAE,KAAK;kDADlB,oBAoEM;8BAlEH,GAAG,EAAE,KAAK;8BACV,aAAW,0BAA0B,KAAK;8BAC1C,OAAK,yCAAE,aAAI;;+BAGJ,CAAC,CAAC,KAAK;iEADf,oBAkCS;;oCA/BN,aAAW,0BAA0B,CAAC,CAAC,EAAE;oCAC1C,KAAK,mBAAC,yBAAyB,uBACF,CAAC,CAAC,YAAY;oCAC1C,EAAE,EAAE,CAAC,CAAC,YAAY;oCACnB,IAAI,EAAC,QAAQ;oCACZ,YAAU,KAAKA,MAAC,8BAA8B,CAAC,CAAC,KAAK;oCACrD,OAAK,4BAAU,yBAAgB,CAAC,MAAM,EAAE,CAAC;oCACzC,UAAQ,0CAAE,qEAAmB;;oDAE9B,aAKE;sCAHC,OAAO,EAAE,CAAC;sCACV,aAAW,EAAE,gBAAU;sCACxB,KAAK,EAAC,uBAAuB;;iEAHZ,yBAAgB,CAAC,CAAC;;;mEAKrC,oBAWM,OAXN,WAWM;sCAPJ,oBAAoB,4BAAd,CAAC,CAAC,KAAK;;uCAEL,CAAC,CAAC,WAAW;yDADrB,oBAKI,KALJ,WAKI,mBADC,CAAC,CAAC,WAAW;;;iEARD,yBAAgB,CAAC,CAAC;;;oCAWrC,aAGE;sCAFC,OAAO,EAAE,CAAC;sCACV,WAAS,EAAE,WAAK;;;;;sCA9BF,mCAAmC;;wCAAzC,IAAI,EAAf,IAAqD;;;iDAiCvD,oBA0BO;;oCAxBL,KAAK,EAAC,kCAAkC;oCACvC,aAAW,mCAAmC,CAAC,CAAC,EAAE;;oDAEnD,aAIE;sCAFC,OAAO,EAAE,CAAC;sCACX,KAAK,EAAC,uBAAuB;;iEAFZ,yBAAgB,CAAC,CAAC;;;mEAIrC,oBAWM,OAXN,WAWM;sCAPJ,oBAAoB,4BAAd,CAAC,CAAC,KAAK;;uCAEL,CAAC,CAAC,WAAW;yDADrB,oBAKI,KALJ,WAKI,mBADC,CAAC,CAAC,WAAW;;;iEARD,yBAAgB,CAAC,CAAC;;;oCAWrC,aAGE;sCAFC,OAAO,EAAE,CAAC;sCACV,WAAS,EAAE,WAAK;;;;;;2BAKf,yBAAgB,CAAC,MAAM;6CAD/B,oBAKM,OALN,WAKM;gCADJ,oBAAI;;;;;;oBAIR,+CAA+B;;oBAC/B,oBAwEM,OAxEN,WAwEM;yCAvEJ,oBAsEM,6BArEiB,eAAM,CAAC,gBAAgB,GAApC,CAAC,EAAE,KAAK;8CADlB,oBAsEM;0BApEH,GAAG,EAAE,KAAK;0BACV,aAAW,4BAA4B,KAAK;0BAC5C,OAAK,yCAAE,aAAI;;2BAGJ,CAAC,CAAC,KAAK;6DADf,oBAmCS;;gCAhCN,aAAW,mBAAmB,CAAC,CAAC,EAAE;gCACnC,KAAK,mBAAC,yBAAyB,uBACF,CAAC,CAAC,YAAY;gCAC1C,EAAE,EAAE,CAAC,CAAC,YAAY;gCACnB,IAAI,EAAC,QAAQ;gCACZ,YAAU,KAAKA,MAAC,8BAA8B,CAAC,CAAC,KAAK;gCACrD,OAAK,aAAE,yBAAgB,CAAC,MAAM,EAAE,CAAC;gCACjC,UAAQ,4CAAE,qEAAmB;;gDAE9B,aAKE;kCAHC,OAAO,EAAE,CAAC;kCACV,aAAW,EAAE,gBAAU;kCACxB,KAAK,EAAC,uBAAuB;;6DAHZ,yBAAgB,CAAC,CAAC;;;+DAKrC,oBAWM,OAXN,WAWM;kCAPJ,oBAAoB,4BAAd,CAAC,CAAC,KAAK;;mCAEL,CAAC,CAAC,WAAW;qDADrB,oBAKI,KALJ,WAKI,mBADC,CAAC,CAAC,WAAW;;;6DARD,yBAAgB,CAAC,CAAC;;;gCAWrC,aAIE;kCAHC,KAAK,8BAAc,CAAC,CAAC,MAAM;kCAC3B,WAAS,EAAE,WAAK;kCAChB,OAAO,EAAE,CAAC;;;;;kCA/BI,mCAAmC;;oCAAzC,IAAI,EAAf,IAAqD;;;6CAkCvD,oBA2BO;;gCAzBL,KAAK,EAAC,kCAAkC;gCACvC,aAAW,4BAA4B,CAAC,CAAC,EAAE;;gDAE5C,aAIE;kCAFC,OAAO,EAAE,CAAC;kCACX,KAAK,EAAC,uBAAuB;;6DAFZ,yBAAgB,CAAC,CAAC;;;+DAIrC,oBAWM,OAXN,WAWM;kCAPJ,oBAAoB,4BAAd,CAAC,CAAC,KAAK;;mCAEL,CAAC,CAAC,WAAW;qDADrB,oBAKI,KALJ,WAKI,mBADC,CAAC,CAAC,WAAW;;;6DARD,yBAAgB,CAAC,CAAC;;;gCAWrC,aAIE;kCAHC,KAAK,8BAAc,CAAC,CAAC,MAAM;kCAC3B,WAAS,EAAE,WAAK;kCAChB,OAAO,EAAE,CAAC;;;;;;;oBAMnB,4CAA4B;;qBAEpB,yBAAgB,CAAC,MAAM,UAAU,qBAAY;uCADrD,oBAMM,OANN,WAMM,mBADDA,MAAC;;;;kBAIR,yCAAyB;;mBAEjB,yBAAgB,GAAG,uBAAiB;qCAD5C,aAec;;wBAbZ,KAAK,EAAC,cAAc;wBACnB,EAAE;yBAA0E,kBAAY;;;;wBAKzF,IAAI,EAAC,MAAM;wBACV,YAAU,EAAEA,MAAC;;0CAEd,CAGO;0BAHP,oBAGO;8DAFF,WAAK,GAAGA,MAAC,yBAAyBA,MAAC,mCAAkC,GACxE;wDAAA,oBAAqC,OAAlC,KAAK,EAAC,yBAAyB;;;;;;;;;YAKxC,2CAA2B;;YAC3B,oBAiEM,OAjEN,WAiEM;eAhEY,yBAAgB,CAAC,MAAM;iCAAvC,oBA8BW;oBA7BT,oBAOM,OAPN,WAOM;kDAJJ,oBAAI;;sBACJ,oBAEO,+BADFA,MAAC;;;uCAGR,oBAoBM,6BAnBa,eAAM,CAAC,gBAAgB,GAAhC,CAAC,EAAE,CAAC;4CADd,oBAoBM;wBAlBH,GAAG,EAAE,CAAC;wBACN,OAAK,yCAAE,aAAI;;wBAEZ,aAcc;0BAbZ,KAAK,mBAAC,QAAQ,uBACe,CAAC,CAAC,YAAY;0BAC1C,EAAE,EAAE,CAAC,CAAC,EAAE;0BACT,IAAI,EAAC,MAAM;0BACV,YAAU,KAAKA,MAAC,uCAAuC,CAAC,CAAC,KAAK;;4CAE/D,CAKE;4CALF,aAKE;8BAHA,KAAK,EAAC,UAAU;8BACf,IAAI,EAAE,CAAC,CAAC,IAAI;8BACZ,GAAG,EAAE,CAAC,CAAC,GAAG;;yDAHM,yBAAgB,CAAC,CAAC,CAAC,KAAK;;;4BAK3C,oBAA8C,QAA9C,WAA8C,mBAAjB,CAAC,CAAC,KAAK;;;;;;;;;;cAK1C,gDAAgC;;eAChB,0BAAiB,CAAC,MAAM;iCAAxC,oBA8BW;oBA7BT,oBAOM,OAPN,WAOM;kDAJJ,oBAAI;;sBACJ,oBAEO,+BADFA,MAAC;;;uCAGR,oBAoBM,6BAnBa,eAAM,CAAC,iBAAiB,GAAjC,CAAC,EAAE,CAAC;4CADd,oBAoBM;wBAlBH,GAAG,EAAE,CAAC;wBACN,OAAK,yCAAE,aAAI;;wBAEZ,aAcc;0BAbZ,KAAK,mBAAC,QAAQ,uBACe,CAAC,CAAC,YAAY;0BAC1C,EAAE,EAAE,CAAC,CAAC,EAAE;0BACT,IAAI,EAAC,MAAM;0BACV,YAAU,KAAKA,MAAC,wCAAwC,CAAC,CAAC,KAAK;;4CAEhE,CAKE;4CALF,aAKE;8BAHA,KAAK,EAAC,UAAU;8BACf,IAAI,EAAE,CAAC,CAAC,IAAI;8BACZ,GAAG,EAAE,CAAC,CAAC,GAAG;;yDAHM,yBAAgB,CAAC,CAAC,CAAC,KAAK;;;4BAK3C,oBAAwB,8BAAhB,CAAC,CAAC,KAAK;;;;;;;;;;;;UAOzB,+BAAe;;UACf,oBA6BM,OA7BN,WA6BM;aAzBI,wBAAe;+BADvB,oBAYM;;kBAVJ,KAAK,EAAC,SAAS;kBACd,OAAK,yCAAE,aAAI;;kBAEZ,aAMc;oBALX,EAAE,EAAE,iBAAiB;oBACtB,IAAI,EAAC,MAAM;oBACV,YAAU,EAAEA,MAAC;;sCAEd,CAAoC;wDAAjCA,MAAC,6BAAiB,mBAAU;;;;;;;YAGnC,oBAYM;cAXJ,KAAK,mBAAC,SAAS,oBACW,uBAAc;cACvC,OAAK,yCAAE,aAAI;;cAEZ,aAMc;gBALX,EAAE,EAAE,iBAAiB;gBACtB,IAAI,EAAC,MAAM;gBACV,YAAU,EAAEA,MAAC;;kCAEd,CAAe;oDAAZ,kBAAS", "sourcesContent": ["<script>\nimport BrandImage from '@shell/components/BrandImage';\nimport ClusterIconMenu from '@shell/components/ClusterIconMenu';\nimport IconOrSvg from '../IconOrSvg';\nimport { BLANK_CLUSTER } from '@shell/store/store-types.js';\nimport { mapGetters } from 'vuex';\nimport { CAPI, COUNT, MANAGEMENT } from '@shell/config/types';\nimport { MENU_MAX_CLUSTERS, PINNED_CLUSTERS } from '@shell/store/prefs';\nimport { sortBy } from '@shell/utils/sort';\nimport { ucFirst } from '@shell/utils/string';\nimport { KEY } from '@shell/utils/platform';\nimport { getVersionInfo } from '@shell/utils/version';\nimport { SETTING } from '@shell/config/settings';\nimport { getProductFromRoute } from '@shell/utils/router';\nimport { isRancherPrime } from '@shell/config/version';\nimport Pinned from '@shell/components/nav/Pinned';\nimport { TopLevelMenuHelperPagination, TopLevelMenuHelperLegacy } from '@shell/components/nav/TopLevelMenu.helper';\nimport { debounce } from 'lodash';\nimport { sameContents } from '@shell/utils/array';\n\nexport default {\n  components: {\n    BrandImage,\n    ClusterIconMenu,\n    IconOrSvg,\n    Pinned\n  },\n\n  data() {\n    const { displayVersion, fullVersion } = getVersionInfo(this.$store);\n    const hasProvCluster = this.$store.getters[`management/schemaFor`](CAPI.RANCHER_CLUSTER);\n\n    const canPagination = this.$store.getters[`management/paginationEnabled`]({\n      id:      MANAGEMENT.CLUSTER,\n      context: 'side-bar',\n    }) && this.$store.getters[`management/paginationEnabled`]({\n      id:      CAPI.RANCHER_CLUSTER,\n      context: 'side-bar',\n    });\n    const helper = canPagination ? new TopLevelMenuHelperPagination({ $store: this.$store }) : new TopLevelMenuHelperLegacy({ $store: this.$store });\n    const provClusters = !canPagination && hasProvCluster ? this.$store.getters[`management/all`](CAPI.RANCHER_CLUSTER) : [];\n    const mgmtClusters = !canPagination ? this.$store.getters[`management/all`](MANAGEMENT.CLUSTER) : [];\n\n    if (!canPagination) {\n      // Reduce the impact of the initial load, but only if we're not making a request\n      const args = {\n        pinnedIds:   this.pinnedIds,\n        searchTerm:  this.search,\n        unPinnedMax: this.maxClustersToShow\n      };\n\n      helper.update(args);\n    }\n\n    return {\n      shown:             false,\n      displayVersion,\n      fullVersion,\n      clusterFilter:     '',\n      hasProvCluster,\n      maxClustersToShow: MENU_MAX_CLUSTERS,\n      emptyCluster:      BLANK_CLUSTER,\n      routeCombo:        false,\n\n      canPagination,\n      helper,\n      debouncedHelperUpdateSlow:   debounce((...args) => this.helper.update(...args), 1000),\n      debouncedHelperUpdateMedium: debounce((...args) => this.helper.update(...args), 750),\n      debouncedHelperUpdateQuick:  debounce((...args) => this.helper.update(...args), 200),\n      provClusters,\n      mgmtClusters,\n    };\n  },\n\n  computed: {\n    ...mapGetters(['clusterId']),\n    ...mapGetters(['clusterReady', 'isRancher', 'currentCluster', 'currentProduct', 'isRancherInHarvester']),\n    ...mapGetters({ features: 'features/get' }),\n\n    pinnedIds() {\n      return this.$store.getters['prefs/get'](PINNED_CLUSTERS);\n    },\n\n    showClusterSearch() {\n      return this.allClustersCount > this.maxClustersToShow;\n    },\n\n    allClustersCount() {\n      const counts = this.$store.getters[`management/all`](COUNT)?.[0]?.counts || {};\n      const count = counts[MANAGEMENT.CLUSTER] || {};\n\n      return count?.summary.count;\n    },\n\n    // New\n    search() {\n      return (this.clusterFilter || '').toLowerCase();\n    },\n\n    // New\n    showPinClusters() {\n      return !this.clusterFilter;\n    },\n\n    // New\n    searchActive() {\n      return !!this.search;\n    },\n\n    /**\n     * Only Clusters that are pinned\n     *\n     * (see description of helper.clustersPinned for more details)\n     */\n    pinFiltered() {\n      return this.hasProvCluster ? this.helper.clustersPinned : [];\n    },\n\n    /**\n     * Used to shown unpinned clusters OR results of text search\n     *\n     * (see description of helper.clustersOthers for more details)\n     */\n    clustersFiltered() {\n      return this.hasProvCluster ? this.helper.clustersOthers : [];\n    },\n\n    pinnedClustersHeight() {\n      const pinCount = this.pinFiltered.length;\n      const height = pinCount > 2 ? (pinCount * 43) : 90;\n\n      return `min-height: ${ height }px`;\n    },\n    clusterFilterCount() {\n      return this.clusterFilter ? this.clustersFiltered.length : this.allClustersCount;\n    },\n\n    multiClusterApps() {\n      const options = this.options;\n\n      return options.filter((opt) => {\n        const filterApps = (opt.inStore === 'management' || opt.isMultiClusterApp) && opt.category !== 'configuration' && opt.category !== 'legacy';\n\n        if (this.isRancherInHarvester) {\n          return filterApps && opt.category !== 'hci';\n        } else {\n          // We expect the location of Virtualization Management to remain the same when rancher-manage-support is not enabled\n          return filterApps;\n        }\n      });\n    },\n\n    configurationApps() {\n      const options = this.options;\n\n      return options.filter((opt) => opt.category === 'configuration');\n    },\n\n    hciApps() {\n      const options = this.options;\n\n      return options.filter((opt) => this.isRancherInHarvester && opt.category === 'hci');\n    },\n\n    options() {\n      const cluster = this.clusterId || this.$store.getters['defaultClusterId'];\n\n      // TODO plugin routes\n      const entries = this.$store.getters['type-map/activeProducts']?.map((p) => {\n        // Try product-specific index first\n        const to = p.to || {\n          name:   `c-cluster-${ p.name }`,\n          params: { cluster }\n        };\n\n        const matched = this.$router.getRoutes().filter((route) => route.name === to.name);\n\n        if ( !matched.length ) {\n          to.name = 'c-cluster-product';\n          to.params.product = p.name;\n        }\n\n        return {\n          label:             this.$store.getters['i18n/withFallback'](`product.\"${ p.name }\"`, null, ucFirst(p.name)),\n          icon:              `icon-${ p.icon || 'copy' }`,\n          svg:               p.svg,\n          value:             p.name,\n          removable:         p.removable !== false,\n          inStore:           p.inStore || 'cluster',\n          weight:            p.weight || 1,\n          category:          p.category || 'none',\n          to,\n          isMultiClusterApp: p.isMultiClusterApp,\n        };\n      });\n\n      return sortBy(entries, ['weight']);\n    },\n\n    canEditSettings() {\n      return (this.$store.getters['management/schemaFor'](MANAGEMENT.SETTING)?.resourceMethods || []).includes('PUT');\n    },\n\n    hasSupport() {\n      return isRancherPrime() || this.$store.getters['management/byId'](MANAGEMENT.SETTING, SETTING.SUPPORTED )?.value === 'true';\n    },\n\n    isCurrRouteClusterExplorer() {\n      return this.$route?.name?.startsWith('c-cluster');\n    },\n\n    productFromRoute() {\n      return getProductFromRoute(this.$route);\n    },\n\n    aboutText() {\n      // If a version number (starts with 'v') then use that\n      if (this.displayVersion.startsWith('v')) {\n        // Don't show the '.0' for a minor release (e.g. 2.8.0, 2.9.0 etc)\n        return !this.displayVersion.endsWith('.0') ? this.displayVersion : this.displayVersion.substr(0, this.displayVersion.length - 2);\n      }\n\n      // Default fallback to 'About'\n      return this.t('about.title');\n    },\n\n    largeAboutText() {\n      return this.aboutText.length > 6;\n    },\n\n    appBar() {\n      let activeFound = false;\n\n      // order is important for the object keys here\n      // since we want to check last pinFiltered and clustersFiltered\n      const appBar = {\n        hciApps:           this.hciApps,\n        multiClusterApps:  this.multiClusterApps,\n        configurationApps: this.configurationApps,\n        pinFiltered:       this.pinFiltered,\n        clustersFiltered:  this.clustersFiltered,\n      };\n\n      Object.keys(appBar).forEach((menuSection) => {\n        const menuSectionItems = appBar[menuSection];\n        const isClusterCheck = menuSection === 'pinFiltered' || menuSection === 'clustersFiltered';\n\n        // need to reset active state on other menu items\n        menuSectionItems.forEach((item) => {\n          item.isMenuActive = false;\n\n          if (!activeFound && this.checkActiveRoute(item, isClusterCheck)) {\n            activeFound = true;\n            item.isMenuActive = true;\n          }\n        });\n      });\n\n      return appBar;\n    }\n  },\n\n  // See https://github.com/rancher/dashboard/issues/12831 for outstanding performance related work\n  watch: {\n    $route() {\n      this.shown = false;\n    },\n\n    // Before SSP world all of these changes were kicked off given Vue change detection to properties in a computed method.\n    // Changes could come from two scenarios\n    // 1. Changes made by the user (pin / search). Could be tens per second\n    // 2. Changes made by rancher to clusters (state, label, etc change). Could be hundreds a second\n    // They can be restricted to help the churn caused from above\n    // 1. When SSP enabled reduce http spam\n    // 2. When SSP is disabled (legacy) reduce fn churn (this was a known performance customer issue)\n\n    pinnedIds: {\n      immediate: true,\n      handler(neu, old) {\n        if (sameContents(neu, old)) {\n          return;\n        }\n\n        // Low throughput (user click). Changes should be shown quickly\n        this.updateClusters(neu, 'quick');\n      }\n    },\n\n    search() {\n      // Medium throughput. Changes should be shown quickly, unless we want to reduce http spam in SSP world\n      this.updateClusters(this.pinnedIds, this.canPagination ? 'medium' : 'quick');\n    },\n\n    provClusters: {\n      handler(neu, old) {\n        // Potentially incredibly high throughput. Changes should be at least limited (slow if state change, quick if added/removed). Shouldn't get here if SSP\n        this.updateClusters(this.pinnedIds, neu?.length === old?.length ? 'slow' : 'quick');\n      },\n      deep:      true,\n      immediate: true,\n    },\n\n    mgmtClusters: {\n      handler(neu, old) {\n        // Potentially incredibly high throughput. Changes should be at least limited (slow if state change, quick if added/removed). Shouldn't get here if SSP\n        this.updateClusters(this.pinnedIds, neu?.length === old?.length ? 'slow' : 'quick');\n      },\n      deep:      true,\n      immediate: true,\n    },\n\n  },\n\n  mounted() {\n    document.addEventListener('keyup', this.handler);\n  },\n\n  beforeUnmount() {\n    document.removeEventListener('keyup', this.handler);\n  },\n\n  methods: {\n    checkActiveRoute(obj, isClusterRoute) {\n      // for Cluster links in main nav: check if route is a cluster explorer one + check if route cluster matches cluster obj id + check if curr product matches route product\n      if (isClusterRoute) {\n        return this.isCurrRouteClusterExplorer && this.$route?.params?.cluster === obj?.id && this.productFromRoute === this.currentProduct?.name;\n      }\n\n      // for remaining main nav items, check if curr product matches route product is enough\n      return this.productFromRoute === obj?.value;\n    },\n\n    handleKeyComboClick() {\n      this.routeCombo = !this.routeCombo;\n    },\n\n    clusterMenuClick(ev, cluster) {\n      if (this.routeCombo) {\n        ev.preventDefault();\n\n        if (this.isCurrRouteClusterExplorer && this.productFromRoute === this.currentProduct?.name) {\n          const clusterRoute = {\n            name:   this.$route.name,\n            params: { ...this.$route.params },\n            query:  { ...this.$route.query }\n          };\n\n          clusterRoute.params.cluster = cluster.id;\n\n          return this.$router.push(clusterRoute);\n        }\n      }\n\n      return this.$router.push(cluster.clusterRoute);\n    },\n\n    handler(e) {\n      if (e.keyCode === KEY.ESCAPE ) {\n        this.hide();\n      }\n    },\n\n    hide() {\n      this.shown = false;\n      if (this.clustersFiltered === 0) {\n        this.clusterFilter = '';\n      }\n    },\n\n    toggle() {\n      this.shown = !this.shown;\n    },\n\n    async goToHarvesterCluster() {\n      const localCluster = this.$store.getters['management/all'](CAPI.RANCHER_CLUSTER).find((C) => C.id === 'fleet-local/local');\n\n      try {\n        await localCluster.goToHarvesterCluster();\n      } catch {\n      }\n    },\n\n    getTooltipConfig(item, showWhenClosed = false) {\n      if (!item) {\n        return;\n      }\n\n      let contentText = '';\n      let content;\n      let popperClass = '';\n\n      // this is the normal tooltip scenario where we are just passing a string\n      if (typeof item === 'string') {\n        contentText = item;\n        content = this.shown ? null : contentText;\n\n      // if key combo is pressed, then we update the tooltip as well\n      } else if (this.routeCombo &&\n        typeof item === 'object' &&\n        !Array.isArray(item) &&\n        item !== null &&\n        item.ready) {\n        contentText = this.t('nav.keyComboTooltip');\n\n        if (showWhenClosed) {\n          content = !this.shown ? contentText : null;\n        } else {\n          content = this.shown ? contentText : null;\n        }\n\n      // this is scenario where we show a tooltip when we are on the expanded menu to show full description\n      } else {\n        contentText = item.label;\n        // this adds a class to the tooltip container so that we can control the max width\n        popperClass = 'menu-description-tooltip';\n\n        if (item.description) {\n          contentText += `<br><br>${ item.description }`;\n        }\n\n        if (showWhenClosed) {\n          content = !this.shown ? contentText : null;\n        } else {\n          content = this.shown ? contentText : null;\n\n          // this adds a class to adjust tooltip position so it doesn't overlap the cluster pinning action\n          popperClass += ' description-tooltip-pos-adjustment';\n        }\n      }\n\n      return {\n        content,\n        placement:     'right',\n        popperOptions: { modifiers: { preventOverflow: { enabled: false }, hide: { enabled: false } } },\n        popperClass\n      };\n    },\n\n    updateClusters(pinnedIds, speed = 'slow' | 'medium' | 'quick') {\n      const args = {\n        pinnedIds,\n        searchTerm:  this.search,\n        unPinnedMax: this.maxClustersToShow\n      };\n\n      switch (speed) {\n      case 'slow':\n        this.debouncedHelperUpdateSlow(args);\n        break;\n      case 'medium':\n        this.debouncedHelperUpdateMedium(args);\n        break;\n      case 'quick':\n        this.debouncedHelperUpdateQuick(args);\n        break;\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <div>\n    <!-- Overlay -->\n    <div\n      v-if=\"shown\"\n      class=\"side-menu-glass\"\n      @click=\"hide()\"\n    />\n    <transition name=\"fade\">\n      <!-- Side menu -->\n      <div\n        data-testid=\"side-menu\"\n        class=\"side-menu\"\n        :class=\"{'menu-open': shown, 'menu-close':!shown}\"\n        tabindex=\"-1\"\n        role=\"navigation\"\n        :aria-label=\"t('nav.ariaLabel.topLevelMenu')\"\n      >\n        <!-- Logo and name -->\n        <div class=\"title\">\n          <div\n            data-testid=\"top-level-menu\"\n            :aria-label=\"t('nav.expandCollapseAppBar')\"\n            role=\"button\"\n            tabindex=\"0\"\n            class=\"menu\"\n            @keyup.enter=\"toggle()\"\n            @keyup.space=\"toggle()\"\n            @click=\"toggle()\"\n          >\n            <svg\n              class=\"menu-icon\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n              height=\"24\"\n              viewBox=\"0 0 24 24\"\n              width=\"24\"\n              :alt=\"t('nav.alt.mainMenuIcon')\"\n            ><path\n              d=\"M0 0h24v24H0z\"\n              fill=\"none\"\n            /><path d=\"M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z\" /></svg>\n          </div>\n          <div class=\"side-menu-logo\">\n            <BrandImage\n              data-testid=\"side-menu__brand-img\"\n              :alt=\"t('nav.alt.mainMenuRancherLogo')\"\n              file-name=\"rancher-logo.svg\"\n            />\n          </div>\n        </div>\n\n        <!-- Menu body -->\n        <div class=\"body\">\n          <div>\n            <!-- Home button -->\n            <div @click=\"hide()\">\n              <router-link\n                class=\"option cluster selector home\"\n                :to=\"{ name: 'home' }\"\n                role=\"link\"\n                :aria-label=\"t('nav.ariaLabel.homePage')\"\n              >\n                <svg\n                  v-clean-tooltip=\"getTooltipConfig(t('nav.home'))\"\n                  class=\"top-menu-icon\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  width=\"24\"\n                ><path\n                  d=\"M0 0h24v24H0z\"\n                  fill=\"none\"\n                /><path d=\"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z\" /></svg>\n                <div class=\"home-text\">\n                  {{ t('nav.home') }}\n                </div>\n              </router-link>\n            </div>\n            <!-- Search bar -->\n            <div\n              v-if=\"showClusterSearch\"\n              class=\"clusters-search\"\n            >\n              <div class=\"clusters-search-count\">\n                <span>{{ clusterFilterCount }}</span>\n                {{ t('nav.search.clusters') }}\n                <i\n                  v-if=\"clusterFilter\"\n                  class=\"icon icon-filter_alt\"\n                />\n              </div>\n\n              <div\n                class=\"search\"\n              >\n                <input\n                  ref=\"clusterFilter\"\n                  v-model=\"clusterFilter\"\n                  :placeholder=\"t('nav.search.placeholder')\"\n                  :tabindex=\"!shown ? -1 : 0\"\n                  :aria-label=\"t('nav.search.ariaLabel')\"\n                >\n                <i\n                  class=\"magnifier icon icon-search\"\n                  :class=\"{ active: clusterFilter }\"\n                />\n                <i\n                  v-if=\"clusterFilter\"\n                  class=\"icon icon-close\"\n                  @click=\"clusterFilter=''\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <!-- Harvester extras -->\n          <template v-if=\"hciApps.length\">\n            <div class=\"category\" />\n            <div>\n              <a\n                v-if=\"isRancherInHarvester\"\n                class=\"option\"\n                tabindex=\"0\"\n                @click=\"goToHarvesterCluster()\"\n              >\n                <i\n                  class=\"icon icon-dashboard app-icon\"\n                />\n                <div>\n                  {{ t('nav.harvesterDashboard') }}\n                </div>\n              </a>\n            </div>\n            <div\n              v-for=\"(a, i) in appBar.hciApps\"\n              :key=\"i\"\n              @click=\"hide()\"\n            >\n              <router-link\n                class=\"option\"\n                :to=\"a.to\"\n                :class=\"{'active-menu-link': a.isMenuActive }\"\n                role=\"link\"\n                :aria-label=\"`${t('nav.ariaLabel.harvesterCluster')} ${ a.label }`\"\n              >\n                <IconOrSvg\n                  class=\"app-icon\"\n                  :icon=\"a.icon\"\n                  :src=\"a.svg\"\n                />\n                <div>{{ a.label }}</div>\n              </router-link>\n            </div>\n          </template>\n\n          <!-- Cluster menu -->\n          <template v-if=\"!!allClustersCount\">\n            <div\n              ref=\"clusterList\"\n              class=\"clusters\"\n              :style=\"pinnedClustersHeight\"\n            >\n              <!-- Pinned Clusters -->\n              <div\n                v-if=\"showPinClusters && pinFiltered.length\"\n                class=\"clustersPinned\"\n              >\n                <div\n                  v-for=\"(c, index) in appBar.pinFiltered\"\n                  :key=\"index\"\n                  :data-testid=\"`pinned-ready-cluster-${index}`\"\n                  @click=\"hide()\"\n                >\n                  <button\n                    v-if=\"c.ready\"\n                    v-shortkey.push=\"{windows: ['alt'], mac: ['option']}\"\n                    :data-testid=\"`pinned-menu-cluster-${ c.id }`\"\n                    class=\"cluster selector option\"\n                    :class=\"{'active-menu-link': c.isMenuActive }\"\n                    :to=\"c.clusterRoute\"\n                    role=\"button\"\n                    :aria-label=\"`${t('nav.ariaLabel.cluster')} ${ c.label }`\"\n                    @click.prevent=\"clusterMenuClick($event, c)\"\n                    @shortkey=\"handleKeyComboClick\"\n                  >\n                    <ClusterIconMenu\n                      v-clean-tooltip=\"getTooltipConfig(c, true)\"\n                      :cluster=\"c\"\n                      :route-combo=\"routeCombo\"\n                      class=\"rancher-provider-icon\"\n                    />\n                    <div\n                      v-clean-tooltip=\"getTooltipConfig(c)\"\n                      class=\"cluster-name\"\n                    >\n                      <p>{{ c.label }}</p>\n                      <p\n                        v-if=\"c.description\"\n                        class=\"description\"\n                      >\n                        {{ c.description }}\n                      </p>\n                    </div>\n                    <Pinned\n                      :cluster=\"c\"\n                      :tab-order=\"shown ? 0 : -1\"\n                    />\n                  </button>\n                  <span\n                    v-else\n                    class=\"option cluster selector disabled\"\n                    :data-testid=\"`pinned-menu-cluster-disabled-${ c.id }`\"\n                  >\n                    <ClusterIconMenu\n                      v-clean-tooltip=\"getTooltipConfig(c, true)\"\n                      :cluster=\"c\"\n                      class=\"rancher-provider-icon\"\n                    />\n                    <div\n                      v-clean-tooltip=\"getTooltipConfig(c)\"\n                      class=\"cluster-name\"\n                    >\n                      <p>{{ c.label }}</p>\n                      <p\n                        v-if=\"c.description\"\n                        class=\"description\"\n                      >\n                        {{ c.description }}\n                      </p>\n                    </div>\n                    <Pinned\n                      :cluster=\"c\"\n                      :tab-order=\"shown ? 0 : -1\"\n                    />\n                  </span>\n                </div>\n                <div\n                  v-if=\"clustersFiltered.length > 0\"\n                  class=\"category-title\"\n                >\n                  <hr>\n                </div>\n              </div>\n\n              <!-- Clusters Search result -->\n              <div class=\"clustersList\">\n                <div\n                  v-for=\"(c, index) in appBar.clustersFiltered\"\n                  :key=\"index\"\n                  :data-testid=\"`top-level-menu-cluster-${index}`\"\n                  @click=\"hide()\"\n                >\n                  <button\n                    v-if=\"c.ready\"\n                    v-shortkey.push=\"{windows: ['alt'], mac: ['option']}\"\n                    :data-testid=\"`menu-cluster-${ c.id }`\"\n                    class=\"cluster selector option\"\n                    :class=\"{'active-menu-link': c.isMenuActive }\"\n                    :to=\"c.clusterRoute\"\n                    role=\"button\"\n                    :aria-label=\"`${t('nav.ariaLabel.cluster')} ${ c.label }`\"\n                    @click=\"clusterMenuClick($event, c)\"\n                    @shortkey=\"handleKeyComboClick\"\n                  >\n                    <ClusterIconMenu\n                      v-clean-tooltip=\"getTooltipConfig(c, true)\"\n                      :cluster=\"c\"\n                      :route-combo=\"routeCombo\"\n                      class=\"rancher-provider-icon\"\n                    />\n                    <div\n                      v-clean-tooltip=\"getTooltipConfig(c)\"\n                      class=\"cluster-name\"\n                    >\n                      <p>{{ c.label }}</p>\n                      <p\n                        v-if=\"c.description\"\n                        class=\"description\"\n                      >\n                        {{ c.description }}\n                      </p>\n                    </div>\n                    <Pinned\n                      :class=\"{'showPin': c.pinned}\"\n                      :tab-order=\"shown ? 0 : -1\"\n                      :cluster=\"c\"\n                    />\n                  </button>\n                  <span\n                    v-else\n                    class=\"option cluster selector disabled\"\n                    :data-testid=\"`menu-cluster-disabled-${ c.id }`\"\n                  >\n                    <ClusterIconMenu\n                      v-clean-tooltip=\"getTooltipConfig(c, true)\"\n                      :cluster=\"c\"\n                      class=\"rancher-provider-icon\"\n                    />\n                    <div\n                      v-clean-tooltip=\"getTooltipConfig(c)\"\n                      class=\"cluster-name\"\n                    >\n                      <p>{{ c.label }}</p>\n                      <p\n                        v-if=\"c.description\"\n                        class=\"description\"\n                      >\n                        {{ c.description }}\n                      </p>\n                    </div>\n                    <Pinned\n                      :class=\"{'showPin': c.pinned}\"\n                      :tab-order=\"shown ? 0 : -1\"\n                      :cluster=\"c\"\n                    />\n                  </span>\n                </div>\n              </div>\n\n              <!-- No clusters message -->\n              <div\n                v-if=\"clustersFiltered.length === 0 && searchActive\"\n                data-testid=\"top-level-menu-no-results\"\n                class=\"none-matching\"\n              >\n                {{ t('nav.search.noResults') }}\n              </div>\n            </div>\n\n            <!-- See all clusters -->\n            <router-link\n              v-if=\"allClustersCount > maxClustersToShow\"\n              class=\"clusters-all\"\n              :to=\"{name: 'c-cluster-product-resource', params: {\n                cluster: emptyCluster,\n                product: 'manager',\n                resource: 'provisioning.cattle.io.cluster'\n              } }\"\n              role=\"link\"\n              :aria-label=\"t('nav.ariaLabel.seeAll')\"\n            >\n              <span>\n                {{ shown ? t('nav.seeAllClusters') : t('nav.seeAllClustersCollapsed') }}\n                <i class=\"icon icon-chevron-right\" />\n              </span>\n            </router-link>\n          </template>\n\n          <!-- MULTI CLUSTER APPS -->\n          <div class=\"category\">\n            <template v-if=\"multiClusterApps.length\">\n              <div\n                class=\"category-title\"\n              >\n                <hr>\n                <span>\n                  {{ t('nav.categories.multiCluster') }}\n                </span>\n              </div>\n              <div\n                v-for=\"(a, i) in appBar.multiClusterApps\"\n                :key=\"i\"\n                @click=\"hide()\"\n              >\n                <router-link\n                  class=\"option\"\n                  :class=\"{'active-menu-link': a.isMenuActive }\"\n                  :to=\"a.to\"\n                  role=\"link\"\n                  :aria-label=\"`${t('nav.ariaLabel.multiClusterApps')} ${ a.label }`\"\n                >\n                  <IconOrSvg\n                    v-clean-tooltip=\"getTooltipConfig(a.label)\"\n                    class=\"app-icon\"\n                    :icon=\"a.icon\"\n                    :src=\"a.svg\"\n                  />\n                  <span class=\"option-link\">{{ a.label }}</span>\n                </router-link>\n              </div>\n            </template>\n\n            <!-- Configuration apps menu -->\n            <template v-if=\"configurationApps.length\">\n              <div\n                class=\"category-title\"\n              >\n                <hr>\n                <span>\n                  {{ t('nav.categories.configuration') }}\n                </span>\n              </div>\n              <div\n                v-for=\"(a, i) in appBar.configurationApps\"\n                :key=\"i\"\n                @click=\"hide()\"\n              >\n                <router-link\n                  class=\"option\"\n                  :class=\"{'active-menu-link': a.isMenuActive }\"\n                  :to=\"a.to\"\n                  role=\"link\"\n                  :aria-label=\"`${t('nav.ariaLabel.configurationApps')} ${ a.label }`\"\n                >\n                  <IconOrSvg\n                    v-clean-tooltip=\"getTooltipConfig(a.label)\"\n                    class=\"app-icon\"\n                    :icon=\"a.icon\"\n                    :src=\"a.svg\"\n                  />\n                  <div>{{ a.label }}</div>\n                </router-link>\n              </div>\n            </template>\n          </div>\n        </div>\n\n        <!-- Footer -->\n        <div\n          class=\"footer\"\n        >\n          <div\n            v-if=\"canEditSettings\"\n            class=\"support\"\n            @click=\"hide()\"\n          >\n            <router-link\n              :to=\"{name: 'support'}\"\n              role=\"link\"\n              :aria-label=\"t('nav.ariaLabel.support')\"\n            >\n              {{ t('nav.support', {hasSupport}) }}\n            </router-link>\n          </div>\n          <div\n            class=\"version\"\n            :class=\"{'version-small': largeAboutText}\"\n            @click=\"hide()\"\n          >\n            <router-link\n              :to=\"{ name: 'about' }\"\n              role=\"link\"\n              :aria-label=\"t('nav.ariaLabel.about')\"\n            >\n              {{ aboutText }}\n            </router-link>\n          </div>\n        </div>\n      </div>\n    </transition>\n  </div>\n</template>\n\n<style lang=\"scss\">\n  .menu-description-tooltip {\n    max-width: 200px;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n  }\n\n  .description-tooltip-pos-adjustment {\n    // needs !important so that we can\n    // offset the tooltip a bit so it doesn't\n    // overlap the pin icon and cause bad UX\n    left: 48px !important;\n  }\n\n  .localeSelector, .footer-tooltip {\n    z-index: 1000;\n  }\n\n  .localeSelector {\n    .v-popper__inner {\n      padding: 10px 0;\n    }\n\n    .v-popper__arrow-container {\n      display: none;\n    }\n\n    .v-popper:focus {\n      outline: 0;\n    }\n  }\n\n  .theme-dark .cluster-name .description {\n    color: var(--input-label) !important;\n  }\n  .theme-dark .body .option  {\n    &:hover .cluster-name .description,\n    &.router-link-active .cluster-name .description,\n    &.active-menu-link .cluster-name .description {\n      color: var(--side-menu-desc) !important;\n  }\n  }\n</style>\n\n<style lang=\"scss\" scoped>\n  $clear-search-size: 20px;\n  $icon-size: 25px;\n  $option-padding: 9px;\n  $option-padding-left: 14px;\n  $option-height: $icon-size + $option-padding + $option-padding;\n\n  .side-menu {\n    .menu {\n      position: absolute;\n      width: $app-bar-collapsed-width;\n      height: 54px;\n      top: 0;\n      grid-area: menu;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n\n      &:focus-visible {\n        outline: none;\n\n        .menu-icon {\n          @include focus-outline;\n          outline-offset: 4px;  // Ensure there is space around the menu icon for the focus indication\n        }\n      }\n\n      .menu-icon {\n        width: 25px;\n        height: 25px;\n        fill: var(--header-btn-text);\n      }\n    }\n\n    position: absolute;\n    top: 0;\n    left: 0px;\n    bottom: 0;\n    width: $app-bar-collapsed-width;\n    background-color: var(--topmenu-bg);\n    z-index: 100;\n    border-right: 1px solid var(--topmost-border);\n    display: flex;\n    flex-direction: column;\n    padding: 0;\n    overflow: hidden;\n    transition: width 250ms;\n\n    &:focus, &:focus-visible {\n      outline: 0;\n    }\n\n    .option:focus-visible {\n      outline: 0;\n    }\n\n    &.menu-open {\n      width: 300px;\n      box-shadow: 3px 1px 3px var(--shadow);\n\n      // because of accessibility, we force pin action to be visible on menu open\n      .pin {\n        display: block !important;\n\n        &:focus-visible {\n          @include focus-outline;\n          outline-offset: 4px;\n        }\n      }\n    }\n\n    .title {\n      display: flex;\n      height: 55px;\n      flex: 0 0 55px;\n      width: 100%;\n      justify-content: flex-start;\n      align-items: center;\n\n      .menu {\n        display: flex;\n        justify-content: center;\n      }\n      .menu-icon {\n        width: 25px;\n        height: 25px;\n      }\n    }\n    .home {\n      svg {\n        width: 25px;\n        height: 25px;\n        margin-left: 9px;\n      }\n    }\n    .home-text {\n      margin-left: $option-padding-left - 7;\n    }\n    .body {\n      flex: 1;\n      display: flex;\n      flex-direction: column;\n      margin: 10px 0;\n      width: 300px;\n      overflow: auto;\n\n      .option {\n        align-items: center;\n        cursor: pointer;\n        display: flex;\n        color: var(--link);\n        font-size: 14px;\n        height: $option-height;\n        white-space: nowrap;\n        background-color: transparent;\n        width: 100%;\n        border-radius: 0;\n        border: none;\n\n        .cluster-badge-logo-text {\n          color: var(--default-active-text);\n          font-weight: 500;\n        }\n\n        .pin {\n          font-size: 16px;\n          margin-left: auto;\n          display: none;\n          color: var(--body-text);\n          &.showPin {\n            display: block;\n          }\n        }\n\n        .cluster-name {\n          line-height: normal;\n\n          & > p {\n            width: 182px;\n            white-space: nowrap;\n            overflow: hidden;\n            text-overflow: ellipsis;\n            text-align: left;\n\n            &.description {\n              font-size: 12px;\n              padding-right: 8px;\n              color: var(--darker);\n            }\n          }\n        }\n\n        &:hover {\n          text-decoration: none;\n\n          .pin {\n            display: block;\n            color: var(--darker-text);\n          }\n        }\n        &.disabled {\n          background: transparent;\n          cursor: not-allowed;\n\n          .rancher-provider-icon,\n          .cluster-name p {\n            filter: grayscale(1);\n            color: var(--muted) !important;\n          }\n\n          .pin {\n            cursor: pointer;\n          }\n        }\n\n        &:focus {\n          outline: 0;\n          box-shadow: none;\n        }\n\n        > i, > img {\n          display: block;\n          font-size: $icon-size;\n          margin-right: 14px;\n          &:not(.pin){\n            width: 42px;\n          }\n        }\n\n        .rancher-provider-icon,\n        svg {\n          margin-right: 16px;\n          fill: var(--link);\n        }\n\n        .top-menu-icon {\n          outline-offset: 4px;\n        }\n\n        &.router-link-active, &.active-menu-link {\n          &:focus-visible {\n            .top-menu-icon, .app-icon {\n              @include focus-outline;\n            }\n          }\n\n          &:focus-visible .rancher-provider-icon {\n            @include focus-outline;\n            outline-offset: -4px;\n          }\n\n          background: var(--primary-hover-bg);\n          color: var(--primary-hover-text);\n\n          svg {\n            fill: var(--primary-hover-text);\n          }\n\n          i {\n            color: var(--primary-hover-text);\n          }\n\n          div .description {\n            color: var(--default);\n          }\n        }\n\n        &:focus-visible {\n          .top-menu-icon, .rancher-provider-icon, .app-icon {\n            @include focus-outline;\n          }\n        }\n\n        &:hover {\n          color: var(--primary-hover-text);\n          background: var(--primary-hover-bg);\n          > div {\n            color: var(--primary-hover-text);\n\n            .description {\n              color: var(--default);\n            }\n          }\n          svg {\n            fill: var(--primary-hover-text);\n          }\n          div {\n            color: var(--primary-hover-text);\n          }\n          &.disabled {\n            background: transparent;\n            color: var(--muted);\n\n            > .pin {\n              color:var(--default-text);\n              display: block;\n            }\n          }\n        }\n      }\n\n      .option, .option-disabled {\n        padding: $option-padding 0 $option-padding $option-padding-left;\n      }\n\n      .search {\n        position: relative;\n        > input {\n          background-color: transparent;\n          padding-right: 35px;\n          padding-left: 25px;\n          height: 32px;\n        }\n        > .magnifier {\n          position: absolute;\n          top: 12px;\n          left: 8px;\n          width: 12px;\n          height: 12px;\n          font-size: 12px;\n          opacity: 0.4;\n\n          &.active {\n            opacity: 1;\n\n            &:hover {\n              color: var(--body-text);\n            }\n          }\n        }\n        > i {\n          position: absolute;\n          font-size: 12px;\n          top: 12px;\n          right: 8px;\n          opacity: 0.7;\n          cursor: pointer;\n          &:hover {\n            color: var(--disabled-bg);\n          }\n        }\n      }\n\n      .clusters-all {\n        display: flex;\n        flex-direction: row-reverse;\n        margin-right: 16px;\n        margin-top: 10px;\n\n        &:focus-visible {\n          outline: none;\n        }\n\n        span {\n          display: flex;\n          align-items: center;\n        }\n\n        &:focus-visible span {\n          @include focus-outline;\n          outline-offset: 4px;\n        }\n      }\n\n      .clusters {\n        overflow-y: auto;\n\n         a, span {\n          margin: 0;\n         }\n\n        &-search {\n          display: flex;\n          align-items: center;\n          gap: 14px;\n          margin: 16px 0;\n          height: 42px;\n\n          .search {\n            transition: all 0.25s ease-in-out;\n            transition-delay: 2s;\n            width: 72%;\n            height: 36px;\n\n            input {\n              height: 100%;\n            }\n          }\n\n          &-count {\n            position: relative;\n            display: flex;\n            flex-direction: column;\n            width: 42px;\n            height: 42px;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            color: var(--default-active-text);\n            margin-left: $option-padding-left;\n            border-radius: 5px;\n            font-size: 10px;\n            font-weight: bold;\n\n            span {\n              font-size: 14px;\n            }\n\n            .router-link-active {\n              &:hover {\n                text-decoration: none;\n              }\n            }\n\n            i {\n              font-size: 12px;\n              position: absolute;\n              right: -3px;\n              top: 2px;\n            }\n          }\n        }\n      }\n\n      .none-matching {\n        width: 100%;\n        text-align: center;\n        padding: 8px\n      }\n\n      .clustersPinned {\n        .category {\n          &-title {\n            margin: 8px 0;\n            margin-left: 16px;\n            hr {\n              margin: 0;\n              width: 94%;\n              transition: all 0.25s ease-in-out;\n              max-width: 100%;\n            }\n          }\n        }\n        .pin {\n          display: block;\n        }\n      }\n\n      .category {\n        display: flex;\n        flex-direction: column;\n        place-content: flex-end;\n        flex: 1;\n\n        &-title {\n          display: flex;\n          flex-direction: row;\n          align-items: flex-start;\n          align-items: center;\n          margin: 15px 0;\n          margin-left: 16px;\n          font-size: 14px;\n          text-transform: uppercase;\n\n          span {\n            transition: all 0.25s ease-in-out;\n            display: flex;\n            max-height: 16px;\n          }\n\n          hr {\n            margin: 0;\n            max-width: 50px;\n            width: 0;\n            transition: all 0.25s ease-in-out;\n          }\n        }\n\n         i {\n            padding-left: $option-padding-left - 5;\n          }\n      }\n    }\n\n    &.menu-open {\n      .option {\n        &.router-link-active, &.active-menu-link {\n          &:focus-visible {\n            @include focus-outline;\n            border-radius: 0;\n            outline-offset: -4px;\n\n            .top-menu-icon, .app-icon, .rancher-provider-icon {\n              outline: none;\n              border-radius: 0;\n            }\n          }\n        }\n\n        &:focus-visible {\n          @include focus-outline;\n          outline-offset: -4px;\n\n          .top-menu-icon, .app-icon, .rancher-provider-icon {\n            outline: none;\n            border-radius: 0;\n          }\n        }\n      }\n    }\n\n    &.menu-close {\n      .side-menu-logo  {\n        opacity: 0;\n      }\n      .category {\n        &-title {\n          span {\n            opacity: 0;\n          }\n\n          hr {\n            width: 40px;\n          }\n        }\n      }\n      .clusters-all {\n        flex-direction: row;\n        margin-left: $option-padding-left + 2;\n\n        span {\n          i {\n            display: none;\n          }\n        }\n      }\n\n      .clustersPinned {\n        .category {\n          &-title {\n            hr {\n              width: 40px;\n            }\n          }\n        }\n      }\n\n      .footer {\n        margin: 20px 10px;\n        width: 50px;\n\n        .support {\n          display: none;\n        }\n\n        .version{\n          text-align: center;\n\n          &.version-small {\n            font-size: 12px;\n          }\n        }\n      }\n    }\n\n    .footer {\n      margin: 20px;\n      width: 240px;\n      display: flex;\n      flex: 0;\n      flex-direction: row;\n      > * {\n        flex: 1;\n        color: var(--link);\n\n        &:first-child {\n          text-align: left;\n        }\n        &:last-child {\n          text-align: right;\n        }\n        text-align: center;\n      }\n\n      .support a:focus-visible {\n        @include focus-outline;\n        outline-offset: 4px;\n      }\n\n      .version {\n        cursor: pointer;\n\n        a:focus-visible {\n          @include focus-outline;\n          outline-offset: 4px;\n        }\n      }\n    }\n  }\n\n  .side-menu-glass {\n    position: absolute;\n    top: 0;\n    left: 0px;\n    bottom: 0;\n    width: 100vw;\n    z-index: 99;\n    opacity: 1;\n  }\n\n  .side-menu-logo {\n    align-items: center;\n    display: flex;\n    transform: translateX($app-bar-collapsed-width);\n    opacity: 1;\n    max-width: 200px;\n    width: 100%;\n    justify-content: center;\n    transition: all 0.5s;\n    overflow: hidden;\n    & IMG {\n      object-fit: contain;\n      height: 21px;\n      max-width: 200px;\n    }\n  }\n\n  .fade-enter-active, .fade-leave-active {\n    transition: all 0.25s;\n    transition-timing-function: ease;\n  }\n\n  .fade-leave-active {\n    transition: all 0.25s;\n  }\n\n  .fade-leave-to {\n    left: -300px;\n  }\n\n  .fade-enter {\n    left: -300px;\n  }\n\n  .locale-chooser {\n    cursor: pointer;\n  }\n\n  .localeSelector {\n    :deep() .v-popper__inner {\n      padding: 50px 0;\n    }\n\n    :deep() .v-popper__arrow-container {\n      display: none;\n    }\n\n    :deep() .v-popper:focus {\n      outline: 0;\n    }\n\n    li {\n      padding: 8px 20px;\n\n      &:hover {\n        background-color: var(--primary-hover-bg);\n        color: var(--primary-hover-text);\n        text-decoration: none;\n      }\n    }\n  }\n</style>\n"]}]}