{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??clonedRuleSet-44.use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownItem.vue?vue&type=template&id=c902f22a&scoped=true&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownItem.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVuZGVyU2xvdCBhcyBfcmVuZGVyU2xvdCwgY3JlYXRlQ29tbWVudFZOb2RlIGFzIF9jcmVhdGVDb21tZW50Vk5vZGUsIGNyZWF0ZVRleHRWTm9kZSBhcyBfY3JlYXRlVGV4dFZOb2RlLCB3aXRoTW9kaWZpZXJzIGFzIF93aXRoTW9kaWZpZXJzLCB3aXRoS2V5cyBhcyBfd2l0aEtleXMsIG9wZW5CbG9jayBhcyBfb3BlbkJsb2NrLCBjcmVhdGVFbGVtZW50QmxvY2sgYXMgX2NyZWF0ZUVsZW1lbnRCbG9jayB9IGZyb20gInZ1ZSI7CmNvbnN0IF9ob2lzdGVkXzEgPSBbImRpc2FibGVkIiwgImFyaWEtZGlzYWJsZWQiLCAib25LZXlkb3duIl07CmV4cG9ydCBmdW5jdGlvbiByZW5kZXIoX2N0eCwgX2NhY2hlLCAkcHJvcHMsICRzZXR1cCwgJGRhdGEsICRvcHRpb25zKSB7CiAgICByZXR1cm4gKF9vcGVuQmxvY2soKSwgX2NyZWF0ZUVsZW1lbnRCbG9jaygiZGl2IiwgewogICAgICAgIHJlZjogImRyb3Bkb3duTWVudUl0ZW0iLAogICAgICAgICJkcm9wZG93bi1tZW51LWl0ZW0iOiAiIiwKICAgICAgICB0YWJpbmRleDogIi0xIiwKICAgICAgICByb2xlOiAibWVudWl0ZW0iLAogICAgICAgIGRpc2FibGVkOiAkcHJvcHMuZGlzYWJsZWQgfHwgbnVsbCwKICAgICAgICAiYXJpYS1kaXNhYmxlZCI6ICRwcm9wcy5kaXNhYmxlZCB8fCBmYWxzZSwKICAgICAgICBvbkNsaWNrOiBfd2l0aE1vZGlmaWVycygkc2V0dXAuaGFuZGxlQ2xpY2ssIFsic3RvcCJdKSwKICAgICAgICBvbktleWRvd246IFsKICAgICAgICAgICAgX3dpdGhLZXlzKCRzZXR1cC5oYW5kbGVBY3RpdmF0ZSwgWyJlbnRlciIsICJzcGFjZSJdKSwKICAgICAgICAgICAgX3dpdGhLZXlzKF93aXRoTW9kaWZpZXJzKCRzZXR1cC5oYW5kbGVLZXlkb3duLCBbInN0b3AiXSksIFsidXAiLCAiZG93biJdKQogICAgICAgIF0KICAgIH0sIFsKICAgICAgICBfcmVuZGVyU2xvdChfY3R4LiRzbG90cywgImJlZm9yZSIsIHt9LCAoKSA9PiBbCiAgICAgICAgICAgIF9jcmVhdGVDb21tZW50Vk5vZGUoIkVtcHR5IHNsb3QgY29udGVudCIpCiAgICAgICAgXSwgdHJ1ZSksCiAgICAgICAgX2NhY2hlWzBdIHx8IChfY2FjaGVbMF0gPSBfY3JlYXRlVGV4dFZOb2RlKCkpLAogICAgICAgIF9yZW5kZXJTbG90KF9jdHguJHNsb3RzLCAiZGVmYXVsdCIsIHt9LCAoKSA9PiBbCiAgICAgICAgICAgIF9jcmVhdGVDb21tZW50Vk5vZGUoIkVtcHR5IHNsb3QgY29udGVudCIpCiAgICAgICAgXSwgdHJ1ZSkKICAgIF0sIDQwIC8qIFBST1BTLCBORUVEX0hZRFJBVElPTiAqLywgX2hvaXN0ZWRfMSkpOwp9Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownItem.vue?vue&type=template&id=c902f22a&scoped=true&ts=true", "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownItem.vue"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,IAAI,WAAW,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,eAAe,IAAI,gBAAgB,EAAE,aAAa,IAAI,cAAc,EAAE,QAAQ,IAAI,SAAS,EAAE,SAAS,IAAI,UAAU,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,MAAM,KAAK,CAAA;AAE3P,MAAM,UAAU,GAAG,CAAC,UAAU,EAAE,eAAe,EAAE,WAAW,CAAC,CAAA;AAE7D,MAAM,UAAU,MAAM,CAAC,IAAS,EAAC,MAAW,EAAC,MAAW,EAAC,MAAW,EAAC,KAAU,EAAC,QAAa;IAC3F,OAAO,CAAC,UAAU,EAAE,ECuEpB,mBAAA,CAiBM,KAAA,EAAA;QAhBJ,GAAG,EAAC,kBAAkB;QACtB,oBAAkB,EAAlB,EAAkB;QAClB,QAAQ,EAAC,IAAI;QACb,IAAI,EAAC,UAAU;QACd,QAAQ,EAAE,MAAA,CAAA,QAAQ,IAAA,IAAA;QAClB,eAAa,EAAE,MAAA,CAAA,QAAQ,IAAA,KAAA;QACvB,OAAK,EAAA,cAAA,CAAO,MAAA,CAAA,WAAW,EAAA,CAAA,MAAA,CAAA,CAAA;QACvB,SAAO,EAAA;YDtEN,SAAS,CCsEW,MAAA,CAAA,cAAc,EAAA,CAAA,OAAA,EAAA,OAAA,CAAA,CAAA;YDrElC,SAAS,CAAC,cAAc,CCsEH,MAAA,CAAA,aAAa,EAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,IAAA,EAAA,MAAA,CAAA,CAAA;SDrEnC;KACF,EAAE;QCsED,WAAA,CAEO,IAAA,CAAA,MAAA,EAAA,QAAA,EAAA,EAAA,EAFP,GAEO,EAAA,CAAA;YADL,mBAAA,CAAA,oBAAA,CAAyB;SDpE1B,EAAE,IAAI,CAAC;QACR,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;QCqE7C,WAAA,CAEO,IAAA,CAAA,MAAA,EAAA,SAAA,EAAA,EAAA,EAFP,GAEO,EAAA,CAAA;YADL,mBAAA,CAAA,oBAAA,CAAyB;SDnE1B,EAAE,IAAI,CAAC;KACT,EAAE,EAAE,CAAC,2BAA2B,EAAE,UAAU,CAAC,CAAC,CAAA;AACjD,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownItem.vue.tsx", "sourceRoot": "", "sourcesContent": ["import { renderSlot as _renderSlot, createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, withModifiers as _withModifiers, withKeys as _withKeys, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = [\"disabled\", \"aria-disabled\", \"onKeydown\"]\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  return (_openBlock(), _createElementBlock(\"div\", {\n    ref: \"dropdownMenuItem\",\n    \"dropdown-menu-item\": \"\",\n    tabindex: \"-1\",\n    role: \"menuitem\",\n    disabled: $props.disabled || null,\n    \"aria-disabled\": $props.disabled || false,\n    onClick: _withModifiers($setup.handleClick, [\"stop\"]),\n    onKeydown: [\n      _withKeys($setup.handleActivate, [\"enter\",\"space\"]),\n      _withKeys(_withModifiers($setup.handleKeydown, [\"stop\"]), [\"up\",\"down\"])\n    ]\n  }, [\n    _renderSlot(_ctx.$slots, \"before\", {}, () => [\n      _createCommentVNode(\"Empty slot content\")\n    ], true),\n    _cache[0] || (_cache[0] = _createTextVNode()),\n    _renderSlot(_ctx.$slots, \"default\", {}, () => [\n      _createCommentVNode(\"Empty slot content\")\n    ], true)\n  ], 40 /* PROPS, NEED_HYDRATION */, _hoisted_1))\n}", "<script setup lang=\"ts\">\n/**\n * An item for a dropdown menu. Used in conjunction with RcDropdown.\n */\nimport { inject } from 'vue';\nimport { DropdownContext, defaultContext } from './types';\n\nconst props = defineProps({ disabled: Boolean });\nconst emits = defineEmits(['click']);\n\nconst { close, dropdownItems } = inject<DropdownContext>('dropdownContext') || defaultContext;\n\n/**\n * Handles keydown events to navigate between dropdown items.\n * @param {KeyboardEvent} e - The keydown event.\n */\nconst handleKeydown = (e: KeyboardEvent) => {\n  const activeItem = document.activeElement;\n\n  const activeIndex = dropdownItems.value.indexOf(activeItem || new HTMLElement());\n\n  if (activeIndex < 0) {\n    return;\n  }\n\n  const shouldAdvance = e.key === 'ArrowDown';\n\n  const newIndex = findNewIndex(shouldAdvance, activeIndex, dropdownItems.value);\n\n  if (dropdownItems.value[newIndex] instanceof HTMLElement) {\n    dropdownItems.value[newIndex].focus();\n  }\n};\n\n/**\n * Finds the new index for the dropdown item based on the key pressed.\n * @param shouldAdvance - Whether to advance to the next or previous item.\n * @param activeIndex - Current active index.\n * @param itemsArr - Array of dropdown items.\n * @returns The new index.\n */\nconst findNewIndex = (shouldAdvance: boolean, activeIndex: number, itemsArr: Element[]) => {\n  const newIndex = shouldAdvance ? activeIndex + 1 : activeIndex - 1;\n\n  if (newIndex > itemsArr.length - 1) {\n    return 0;\n  }\n\n  if (newIndex < 0) {\n    return itemsArr.length - 1;\n  }\n\n  return newIndex;\n};\n\nconst handleClick = (e: MouseEvent) => {\n  if (props.disabled) {\n    return;\n  }\n\n  emits('click', e);\n  close();\n};\n\n/**\n * Handles keydown events to activate the dropdown item.\n * @param e - The keydown event.\n */\nconst handleActivate = (e: KeyboardEvent) => {\n  if (e?.target instanceof HTMLElement) {\n    e?.target?.click();\n  }\n};\n</script>\n\n<template>\n  <div\n    ref=\"dropdownMenuItem\"\n    dropdown-menu-item\n    tabindex=\"-1\"\n    role=\"menuitem\"\n    :disabled=\"disabled || null\"\n    :aria-disabled=\"disabled || false\"\n    @click.stop=\"handleClick\"\n    @keydown.enter.space=\"handleActivate\"\n    @keydown.up.down.stop=\"handleKeydown\"\n  >\n    <slot name=\"before\">\n      <!--Empty slot content-->\n    </slot>\n    <slot name=\"default\">\n      <!--Empty slot content-->\n    </slot>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  [dropdown-menu-item] {\n    display: flex;\n    gap: 8px;\n    align-items: center;\n    padding: 9px 8px;\n    margin: 0 9px;\n    border-radius: 4px;\n\n    &:hover {\n      cursor: pointer;\n      background-color: var(--dropdown-hover-bg);\n    }\n    &:focus-visible, &:focus {\n      @include focus-outline;\n      outline-offset: 0;\n    }\n    &[disabled] {\n      color: var(--disabled-text);\n      &:hover {\n        cursor: not-allowed;\n      }\n    }\n  }\n</style>\n"]}]}