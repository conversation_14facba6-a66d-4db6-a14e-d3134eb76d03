{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/NamespaceRow.vue?vue&type=template&id=2b81bebc&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/NamespaceRow.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/NamespaceRow.vue"], "names": ["typeOption"], "mappings": ";;;;EA+KI,KAAK,EAAC,KAAK;;qBASN,KAAK,EAAC,6BAA6B;;;;;;;;UAVlCA,eAAU;qBADlB,oBA8BM,OA9BN,UA8BM;QA1BJ,aAME;UALA,KAAK,EAAC,OAAO;UACZ,IAAI,EAAE,WAAI;UACV,KAAK,EAAE,WAAI;UACX,QAAQ,EAAE,IAAI;UACd,OAAO,EAAE,YAAK;;;QAEjB,oBAQM,OARN,UAQM;0BAPJ,aAME;YAJA,KAAK,EAAC,gBAAgB;YACrB,UAAU,EAAE,uBAAc;YAC1B,MAAM,EAAE,eAAM;YACd,aAAW,EAAE,oBAAoB;;uCAJjB,gBAAO;;;;QAO5B,aASE;UARC,KAAK,EAAE,YAAK,CAAC,KAAK,CAAC,WAAI;UACvB,IAAI,EAAE,WAAI;UACV,WAAW,EAAEA,eAAU,CAAC,WAAW;UACnC,SAAS,EAAEA,eAAU,CAAC,SAAS;UAC/B,gBAAc,EAAEA,eAAU,CAAC,aAAa;UACxC,WAAS,EAAEA,eAAU,CAAC,QAAQ;UAC9B,iBAAe,EAAE,IAAI;UACrB,gBAAY,EAAE,eAAM", "sourcesContent": ["<script>\nimport Select from '@shell/components/form/Select';\nimport UnitInput from '@shell/components/form/UnitInput';\nimport PercentageBar from '@shell/components/PercentageBar';\nimport { formatSi, parseSi } from '@shell/utils/units';\nimport { ROW_COMPUTED } from './shared';\n\nexport default {\n  emits: ['update:value'],\n\n  components: {\n    Select, PercentageBar, UnitInput\n  },\n\n  props: {\n    mode: {\n      type:     String,\n      required: true,\n    },\n    types: {\n      type:    Array,\n      default: () => []\n    },\n    type: {\n      type:     String,\n      required: true\n    },\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    namespace: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    projectResourceQuotaLimits: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    namespaceResourceQuotaLimits: {\n      type:    Array,\n      default: () => {\n        return [];\n      }\n    },\n    defaultResourceQuotaLimits: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  mounted() {\n    // We want to update the value first so that the value will be rounded to the project limit.\n    // This is relevant when switching projects. If the value is 1200 and the project that it was\n    // switched to only has capacity for 800 more this will force the value to be set to 800.\n    if (this.value?.limit?.[this.type]) {\n      this.update(this.value.limit[this.type]);\n    }\n\n    if (!this.value?.limit?.[this.type]) {\n      this.update(this.defaultResourceQuotaLimits[this.type]);\n    }\n  },\n\n  computed: {\n    ...ROW_COMPUTED,\n    limitValue() {\n      return parseSi(this.projectResourceQuotaLimits[this.type]);\n    },\n    siOptions() {\n      return {\n        maxExponent: this.typeOption.inputExponent,\n        minExponent: this.typeOption.inputExponent,\n        increment:   this.typeOption.increment,\n        suffix:      this.typeOption.increment === 1024 ? 'i' : ''\n      };\n    },\n    namespaceLimits() {\n      return this.namespaceResourceQuotaLimits\n        .filter((resourceQuota) => resourceQuota[this.type] && resourceQuota.id !== this.namespace.id)\n        .map((resourceQuota) => parseSi(resourceQuota[this.type], this.siOptions));\n    },\n    namespaceContribution() {\n      return this.namespaceLimits.reduce((sum, limit) => sum + limit, 0);\n    },\n    totalContribution() {\n      return this.namespaceContribution + parseSi(this.value.limit[this.type] || '0', this.siOptions);\n    },\n    percentageUsed() {\n      return Math.min(this.totalContribution * 100 / this.projectLimit, 100);\n    },\n    projectLimit() {\n      return parseSi(this.projectResourceQuotaLimits[this.type] || 0, this.siOptions);\n    },\n    max() {\n      return this.projectLimit - this.namespaceContribution;\n    },\n    availableResourceQuotas() {\n      return formatSi(this.projectLimit - this.totalContribution, { ...this.siOptions, addSuffixSpace: false });\n    },\n    slices() {\n      const out = [];\n\n      this.namespaceLimits.forEach((limit, i) => {\n        const lastValue = i > 0 ? this.namespaceLimits[i - 1] : 0;\n        const sliceTotal = lastValue + limit;\n\n        out.push(sliceTotal * 100 / this.projectLimit);\n      });\n\n      return out;\n    },\n    tooltip() {\n      const t = this.$store.getters['i18n/t'];\n      const out = [\n        {\n          label: t('resourceQuota.tooltip.reserved'),\n          value: formatSi(this.namespaceContribution, { ...this.siOptions, addSuffixSpace: false }),\n        },\n        {\n          label: t('resourceQuota.tooltip.namespace'),\n          value: this.value.limit[this.type]\n        },\n        {\n          label: t('resourceQuota.tooltip.available'),\n          value: this.availableResourceQuotas\n        },\n        {\n          label: t('resourceQuota.tooltip.max'),\n          value: this.projectResourceQuotaLimits[this.type]\n        }\n      ];\n\n      let formattedTooltip = '<div class=\"quota-percentage-tooltip\">';\n\n      (out || []).forEach((v) => {\n        formattedTooltip += `\n        <div style='margin-top: 5px; display: flex; justify-content: space-between;'>\n          ${ v.label }\n          <span style='margin-left: 20px;'>${ v.value }</span>\n        </div>`;\n      });\n      formattedTooltip += '</div>';\n\n      return formattedTooltip;\n    },\n\n  },\n\n  methods: {\n    update(newValue) {\n      const parsedNewValue = parseSi(newValue, this.siOptions) || 0;\n      const min = Math.max(parsedNewValue, 0);\n      const max = Math.min(min, this.max);\n      const value = formatSi(max, {\n        ...this.siOptions,\n        addSuffixSpace: false\n      });\n\n      this.$emit('update:value', this.type, value);\n    }\n  }\n};\n</script>\n<template>\n  <div\n    v-if=\"typeOption\"\n    class=\"row\"\n  >\n    <Select\n      class=\"mr-10\"\n      :mode=\"mode\"\n      :value=\"type\"\n      :disabled=\"true\"\n      :options=\"types\"\n    />\n    <div class=\"resource-availability mr-10\">\n      <PercentageBar\n        v-clean-tooltip=\"tooltip\"\n        class=\"percentage-bar\"\n        :modelValue=\"percentageUsed\"\n        :slices=\"slices\"\n        :color-stops=\"{'100': '--primary'}\"\n      />\n    </div>\n    <UnitInput\n      :value=\"value.limit[type]\"\n      :mode=\"mode\"\n      :placeholder=\"typeOption.placeholder\"\n      :increment=\"typeOption.increment\"\n      :input-exponent=\"typeOption.inputExponent\"\n      :base-unit=\"typeOption.baseUnit\"\n      :output-modifier=\"true\"\n      @update:value=\"update\"\n    />\n  </div>\n</template>\n\n<style lang='scss' scoped>\n  .resource-availability {\n    align-self: center;\n  }\n  .row {\n    display: flex;\n    flex-direction: row;\n    justify-content: space-evenly;\n\n    & > * {\n      width: 100%;\n    }\n  }\n</style>\n"]}]}