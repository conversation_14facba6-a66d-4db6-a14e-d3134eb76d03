{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Jump.vue?vue&type=template&id=65ab091d&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Jump.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Jump.vue"], "names": ["t", "$emit"], "mappings": ";;;EA0EM,EAAE,EAAC,iCAAiC;EACpC,MAAM,EAAN,EAAM;;;qBAcH,KAAK,EAAC,SAAS;;;;;wBAjBtB,oBAsCM;IArCJ,oBAKI,KALJ,UAKI,mBADCA,MAAC;;oBAEN,oBASC;MARC,GAAG,EAAC,OAAO;mEACF,WAAK;MACb,WAAW,EAAEA,MAAC;MACf,KAAK,EAAC,QAAQ;MACd,IAAI,EAAC,SAAS;MACb,YAAU,EAAEA,MAAC;MACd,kBAAgB,EAAC,iCAAiC;MACjD,OAAK,iDAAMC,UAAK;;oBANR,WAAK;;;IAQhB,oBAoBM,OApBN,UAoBM;yBAnBJ,oBAkBM,6BAjBQ,YAAM,GAAX,CAAC;8BADV,oBAkBM;UAhBH,GAAG,EAAE,CAAC,CAAC,IAAI;UACZ,KAAK,EAAC,SAAS;;YAGN,CAAC,CAAC,MAAM;6BADjB,aAYQ;gBAVL,GAAG,EAAE,CAAC,CAAC,IAAI;gBACZ,WAAS,EAAC,EAAE;gBACX,KAAK,EAAE,CAAC;gBACR,cAAY,EAAE,KAAK;gBACnB,YAAU,EAAE,IAAI;gBAChB,OAAK,uCAAEA,UAAK;;gBAEF,SAAS,WAClB,CAAsB;kBAAtB,oBAAsB,6BAAf,CAAC,CAAC,KAAK", "sourcesContent": ["<script>\nimport debounce from 'lodash/debounce';\nimport Group from '@shell/components/nav/Group';\nimport { isMac } from '@shell/utils/platform';\nimport { BOTH, TYPE_MODES } from '@shell/store/type-map';\nimport { COUNT } from '@shell/config/types';\n\nexport default {\n  emits: ['closeSearch'],\n\n  components: { Group },\n\n  data() {\n    return {\n      isMac,\n      value:  '',\n      groups: null,\n    };\n  },\n\n  watch: {\n    value() {\n      this.queueUpdate();\n    },\n  },\n\n  mounted() {\n    this.updateMatches();\n    this.queueUpdate = debounce(this.updateMatches, 250);\n\n    this.$refs.input.focus();\n  },\n\n  methods: {\n    updateMatches() {\n      const clusterId = this.$store.getters['clusterId'];\n      const productId = this.$store.getters['productId'];\n      const product = this.$store.getters['currentProduct'];\n\n      const allTypesByMode = this.$store.getters['type-map/allTypes'](productId, [TYPE_MODES.ALL]) || {};\n      const allTypes = allTypesByMode[TYPE_MODES.ALL];\n      const out = this.$store.getters['type-map/getTree'](productId, TYPE_MODES.ALL, allTypes, clusterId, BOTH, null, this.value);\n\n      // Suplement the output with count info. Usualy the `Type` component would handle this individualy... but scales real bad so give it\n      // some help\n      const counts = this.$store.getters[`${ product.inStore }/all`](COUNT)?.[0]?.counts || {};\n\n      out.forEach((o) => {\n        o.children?.forEach((t) => {\n          const count = counts[t.name];\n\n          t.count = count ? count.summary.count || 0 : null;\n          t.byNamespace = count ? count.namespaces : {};\n          t.revision = count ? count.revision : null;\n        });\n      });\n\n      this.groups = out;\n\n      // Hide top-level groups with no children (or one child that is an overview)\n      this.groups.forEach((g) => {\n        const isRoot = g.isRoot || g.name === 'Root';\n        const hidden = isRoot || g.children?.length === 0 || (g.children?.length === 1 && g.children[0].overview);\n\n        g.hidden = !!hidden;\n      });\n    }\n  },\n};\n</script>\n\n<template>\n  <div>\n    <p\n      id=\"describe-filter-resource-search\"\n      hidden\n    >\n      {{ t('nav.resourceSearch.filteringDescription') }}\n    </p>\n    <input\n      ref=\"input\"\n      v-model=\"value\"\n      :placeholder=\"t('nav.resourceSearch.placeholder')\"\n      class=\"search\"\n      role=\"textbox\"\n      :aria-label=\"t('nav.resourceSearch.label')\"\n      aria-describedby=\"describe-filter-resource-search\"\n      @keyup.esc=\"$emit('closeSearch')\"\n    >\n    <div class=\"results\">\n      <div\n        v-for=\"g in groups\"\n        :key=\"g.name\"\n        class=\"package\"\n      >\n        <Group\n          v-if=\"!g.hidden\"\n          :key=\"g.name\"\n          id-prefix=\"\"\n          :group=\"g\"\n          :can-collapse=\"false\"\n          :fixed-open=\"true\"\n          @close=\"$emit('closeSearch')\"\n        >\n          <template #accordion>\n            <h6>{{ g.label }}</h6>\n          </template>\n        </Group>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .search, .search:hover {\n    position: relative;\n    background-color: var(--dropdown-bg);\n    border-radius: 0;\n    box-shadow: none;\n  }\n\n  .search:focus-visible {\n    outline-offset: -2px;\n  }\n\n  .results {\n    margin-top: -1px;\n    overflow-y: auto;\n    padding: 10px;\n    color: var(--dropdown-text);\n    background-color: var(--dropdown-bg);\n    border: 1px solid var(--dropdown-border);\n    height: 75vh;\n  }\n</style>\n"]}]}