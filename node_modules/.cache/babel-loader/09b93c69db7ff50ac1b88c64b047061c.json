{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Favorite.vue?vue&type=template&id=40c3f40b&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Favorite.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgd2l0aE1vZGlmaWVycyBhcyBfd2l0aE1vZGlmaWVycywgd2l0aEtleXMgYXMgX3dpdGhLZXlzLCBub3JtYWxpemVDbGFzcyBhcyBfbm9ybWFsaXplQ2xhc3MsIG9wZW5CbG9jayBhcyBfb3BlbkJsb2NrLCBjcmVhdGVFbGVtZW50QmxvY2sgYXMgX2NyZWF0ZUVsZW1lbnRCbG9jayB9IGZyb20gInZ1ZSIKCmNvbnN0IF9ob2lzdGVkXzEgPSBbImFyaWEtcHJlc3NlZCIsICJhcmlhLWxhYmVsIl0KCmV4cG9ydCBmdW5jdGlvbiByZW5kZXIoX2N0eCwgX2NhY2hlLCAkcHJvcHMsICRzZXR1cCwgJGRhdGEsICRvcHRpb25zKSB7CiAgcmV0dXJuIChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soImkiLCB7CiAgICB0YWJpbmRleDogMCwKICAgICJhcmlhLXByZXNzZWQiOiAhISRvcHRpb25zLmlzRmF2b3JpdGUsCiAgICBjbGFzczogX25vcm1hbGl6ZUNsYXNzKFsiZmF2b3JpdGUgaWNvbiIsIHsnaWNvbi1zdGFyLW9wZW4nOiAhJG9wdGlvbnMuaXNGYXZvcml0ZSwgJ2ljb24tc3Rhcic6ICRvcHRpb25zLmlzRmF2b3JpdGV9XSksCiAgICAiYXJpYS1yb2xlIjogImJ1dHRvbiIsCiAgICAiYXJpYS1sYWJlbCI6ICRvcHRpb25zLmFyaWFMYWJlbCwKICAgIG9uQ2xpY2s6IF9jYWNoZVswXSB8fCAoX2NhY2hlWzBdID0gX3dpdGhNb2RpZmllcnMoKC4uLmFyZ3MpID0+ICgkb3B0aW9ucy50b2dnbGUgJiYgJG9wdGlvbnMudG9nZ2xlKC4uLmFyZ3MpKSwgWyJzdG9wIiwicHJldmVudCJdKSksCiAgICBvbktleWRvd246IFsKICAgICAgX2NhY2hlWzFdIHx8IChfY2FjaGVbMV0gPSBfd2l0aEtleXMoX3dpdGhNb2RpZmllcnMoKC4uLmFyZ3MpID0+ICgkb3B0aW9ucy50b2dnbGUgJiYgJG9wdGlvbnMudG9nZ2xlKC4uLmFyZ3MpKSwgWyJwcmV2ZW50Il0pLCBbImVudGVyIl0pKSwKICAgICAgX2NhY2hlWzJdIHx8IChfY2FjaGVbMl0gPSBfd2l0aEtleXMoX3dpdGhNb2RpZmllcnMoKC4uLmFyZ3MpID0+ICgkb3B0aW9ucy50b2dnbGUgJiYgJG9wdGlvbnMudG9nZ2xlKC4uLmFyZ3MpKSwgWyJwcmV2ZW50Il0pLCBbInNwYWNlIl0pKQogICAgXQogIH0sIG51bGwsIDQyIC8qIENMQVNTLCBQUk9QUywgTkVFRF9IWURSQVRJT04gKi8sIF9ob2lzdGVkXzEpKQp9"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Favorite.vue"], "names": [], "mappings": ";;;;;wBA+BE,oBAUE;IATC,QAAQ,EAAE,CAAC;IACX,cAAY,IAAI,mBAAU;IAC3B,KAAK,mBAAC,eAAe,sBACO,mBAAU,eAAe,mBAAU;IAC/D,WAAS,EAAC,QAAQ;IACjB,YAAU,EAAE,kBAAS;IACrB,OAAK,yDAAe,2CAAM;IAC1B,SAAO;uEAAgB,2CAAM;uEACN,2CAAM", "sourcesContent": ["<script>\nexport default {\n  props: {\n    resource: {\n      type:     String,\n      required: true,\n    }\n  },\n\n  computed: {\n    isFavorite() {\n      return this.$store.getters['type-map/isFavorite'](this.resource);\n    },\n    ariaLabel() {\n      return this.t(`resourceDetail.masthead.ariaLabel.${ this.isFavorite ? 'unfavoriteAction' : 'favoriteAction' }`, { resource: this.resource });\n    }\n  },\n\n  methods: {\n    toggle() {\n      if ( this.isFavorite ) {\n        this.$store.dispatch('type-map/removeFavorite', this.resource);\n      } else {\n        this.$store.dispatch('type-map/addFavorite', this.resource);\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <i\n    :tabindex=\"0\"\n    :aria-pressed=\"!!isFavorite\"\n    class=\"favorite icon\"\n    :class=\"{'icon-star-open': !isFavorite, 'icon-star': isFavorite}\"\n    aria-role=\"button\"\n    :aria-label=\"ariaLabel\"\n    @click.stop.prevent=\"toggle\"\n    @keydown.enter.prevent=\"toggle\"\n    @keydown.space.prevent=\"toggle\"\n  />\n</template>\n\n<style lang=\"scss\" scoped>\n  .favorite {\n    position: relative;\n    cursor: pointer;\n    font-size: 20px;\n    transform: ease-in-out-all 1s;\n\n    &.icon-star-open {\n      color: var(--muted);\n    }\n\n    &.icon-star-closed {\n      color: var(--body-text);\n    }\n  }\n</style>\n"]}]}