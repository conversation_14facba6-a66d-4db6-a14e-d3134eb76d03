{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AllowedPrincipals.vue?vue&type=template&id=b07a81ae", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AllowedPrincipals.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgdG9EaXNwbGF5U3RyaW5nIGFzIF90b0Rpc3BsYXlTdHJpbmcsIGNyZWF0ZUVsZW1lbnRWTm9kZSBhcyBfY3JlYXRlRWxlbWVudFZOb2RlLCByZXNvbHZlQ29tcG9uZW50IGFzIF9yZXNvbHZlQ29tcG9uZW50LCBjcmVhdGVWTm9kZSBhcyBfY3JlYXRlVk5vZGUsIG9wZW5CbG9jayBhcyBfb3BlbkJsb2NrLCBjcmVhdGVFbGVtZW50QmxvY2sgYXMgX2NyZWF0ZUVsZW1lbnRCbG9jaywgY3JlYXRlQ29tbWVudFZOb2RlIGFzIF9jcmVhdGVDb21tZW50Vk5vZGUsIGNyZWF0ZVRleHRWTm9kZSBhcyBfY3JlYXRlVGV4dFZOb2RlLCB3aXRoQ3R4IGFzIF93aXRoQ3R4LCBjcmVhdGVTbG90cyBhcyBfY3JlYXRlU2xvdHMsIGNyZWF0ZUJsb2NrIGFzIF9jcmVhdGVCbG9jayB9IGZyb20gInZ1ZSIKCmNvbnN0IF9ob2lzdGVkXzEgPSB7IGNsYXNzOiAicm93IiB9CmNvbnN0IF9ob2lzdGVkXzIgPSB7IGNsYXNzOiAiY29sIHNwYW4tNiIgfQpjb25zdCBfaG9pc3RlZF8zID0geyBjbGFzczogImNvbCBzcGFuLTYiIH0KY29uc3QgX2hvaXN0ZWRfNCA9IHsga2V5OiAwIH0KY29uc3QgX2hvaXN0ZWRfNSA9IHsKICB0eXBlOiAiYnV0dG9uIiwKICBkaXNhYmxlZDogIiIsCiAgY2xhc3M6ICJidG4gcm9sZS1saW5rIGJnLXRyYW5zcGFyZW50Igp9CgpleHBvcnQgZnVuY3Rpb24gcmVuZGVyKF9jdHgsIF9jYWNoZSwgJHByb3BzLCAkc2V0dXAsICRkYXRhLCAkb3B0aW9ucykgewogIGNvbnN0IF9jb21wb25lbnRfUmFkaW9Hcm91cCA9IF9yZXNvbHZlQ29tcG9uZW50KCJSYWRpb0dyb3VwIikKICBjb25zdCBfY29tcG9uZW50X3QgPSBfcmVzb2x2ZUNvbXBvbmVudCgidCIpCiAgY29uc3QgX2NvbXBvbmVudF9QcmluY2lwYWwgPSBfcmVzb2x2ZUNvbXBvbmVudCgiUHJpbmNpcGFsIikKICBjb25zdCBfY29tcG9uZW50X1NlbGVjdFByaW5jaXBhbCA9IF9yZXNvbHZlQ29tcG9uZW50KCJTZWxlY3RQcmluY2lwYWwiKQogIGNvbnN0IF9jb21wb25lbnRfQXJyYXlMaXN0ID0gX3Jlc29sdmVDb21wb25lbnQoIkFycmF5TGlzdCIpCgogIHJldHVybiAoX29wZW5CbG9jaygpLCBfY3JlYXRlRWxlbWVudEJsb2NrKCJkaXYiLCBudWxsLCBbCiAgICBfY3JlYXRlRWxlbWVudFZOb2RlKCJoMyIsIG51bGwsIF90b0Rpc3BsYXlTdHJpbmcoX2N0eC50KCdhdXRoQ29uZmlnLmFjY2Vzc01vZGUubGFiZWwnLCB7cHJvdmlkZXI6ICRwcm9wcy5hdXRoQ29uZmlnLm5hbWVEaXNwbGF5fSkpLCAxIC8qIFRFWFQgKi8pLAogICAgX2NhY2hlWzZdIHx8IChfY2FjaGVbNl0gPSBfY3JlYXRlVGV4dFZOb2RlKCkpLAogICAgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfMSwgWwogICAgICBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCBfaG9pc3RlZF8yLCBbCiAgICAgICAgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfUmFkaW9Hcm91cCwgewogICAgICAgICAgdmFsdWU6ICRwcm9wcy5hdXRoQ29uZmlnLmFjY2Vzc01vZGUsCiAgICAgICAgICAib25VcGRhdGU6dmFsdWUiOiBfY2FjaGVbMF0gfHwgKF9jYWNoZVswXSA9ICRldmVudCA9PiAoKCRwcm9wcy5hdXRoQ29uZmlnLmFjY2Vzc01vZGUpID0gJGV2ZW50KSksCiAgICAgICAgICBuYW1lOiAiYWNjZXNzTW9kZSIsCiAgICAgICAgICBtb2RlOiAkcHJvcHMubW9kZSwKICAgICAgICAgIG9wdGlvbnM6ICRvcHRpb25zLmFjY2Vzc01vZGVPcHRpb25zCiAgICAgICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgWyJ2YWx1ZSIsICJtb2RlIiwgIm9wdGlvbnMiXSkKICAgICAgXSksCiAgICAgIF9jYWNoZVs1XSB8fCAoX2NhY2hlWzVdID0gX2NyZWF0ZVRleHRWTm9kZSgpKSwKICAgICAgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfMywgWwogICAgICAgICgkb3B0aW9ucy5hY2Nlc3NNb2RlIT09J3VucmVzdHJpY3RlZCcpCiAgICAgICAgICA/IChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soImg0IiwgX2hvaXN0ZWRfNCwgWwogICAgICAgICAgICAgIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X3QsIHsKICAgICAgICAgICAgICAgIGs6ICJhdXRoQ29uZmlnLmFsbG93ZWRQcmluY2lwYWxJZHMudGl0bGUiLAogICAgICAgICAgICAgICAgcmF3OiB0cnVlCiAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgXSkpCiAgICAgICAgICA6IF9jcmVhdGVDb21tZW50Vk5vZGUoInYtaWYiLCB0cnVlKSwKICAgICAgICBfY2FjaGVbNF0gfHwgKF9jYWNoZVs0XSA9IF9jcmVhdGVUZXh0Vk5vZGUoKSksCiAgICAgICAgKCRvcHRpb25zLmFjY2Vzc01vZGUhPT0ndW5yZXN0cmljdGVkJykKICAgICAgICAgID8gKF9vcGVuQmxvY2soKSwgX2NyZWF0ZUJsb2NrKF9jb21wb25lbnRfQXJyYXlMaXN0LCB7CiAgICAgICAgICAgICAga2V5OiAiYWxsb3dlZFByaW5jaXBhbElkcyIsCiAgICAgICAgICAgICAgdmFsdWU6ICRwcm9wcy5hdXRoQ29uZmlnLmFsbG93ZWRQcmluY2lwYWxJZHMsCiAgICAgICAgICAgICAgIm9uVXBkYXRlOnZhbHVlIjogX2NhY2hlWzFdIHx8IChfY2FjaGVbMV0gPSAkZXZlbnQgPT4gKCgkcHJvcHMuYXV0aENvbmZpZy5hbGxvd2VkUHJpbmNpcGFsSWRzKSA9ICRldmVudCkpLAogICAgICAgICAgICAgICJ0aXRsZS1rZXkiOiAiYXV0aENvbmZpZy5hbGxvd2VkUHJpbmNpcGFsSWRzLmxhYmVsIiwKICAgICAgICAgICAgICBtb2RlOiAkcHJvcHMubW9kZSwKICAgICAgICAgICAgICBwcm90aXA6IGZhbHNlCiAgICAgICAgICAgIH0sIF9jcmVhdGVTbG90cyh7CiAgICAgICAgICAgICAgdmFsdWU6IF93aXRoQ3R4KCh7cm93fSkgPT4gWwogICAgICAgICAgICAgICAgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfUHJpbmNpcGFsLCB7CiAgICAgICAgICAgICAgICAgIHZhbHVlOiByb3cudmFsdWUKICAgICAgICAgICAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIFsidmFsdWUiXSkKICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICBhZGQ6IF93aXRoQ3R4KCgpID0+IFsKICAgICAgICAgICAgICAgIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X1NlbGVjdFByaW5jaXBhbCwgewogICAgICAgICAgICAgICAgICBtb2RlOiAkcHJvcHMubW9kZSwKICAgICAgICAgICAgICAgICAgb25BZGQ6ICRvcHRpb25zLmFkZFByaW5jaXBhbAogICAgICAgICAgICAgICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgWyJtb2RlIiwgIm9uQWRkIl0pCiAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgXzogMiAvKiBEWU5BTUlDICovCiAgICAgICAgICAgIH0sIFsKICAgICAgICAgICAgICAoJHByb3BzLmF1dGhDb25maWcuYWxsb3dlZFByaW5jaXBhbElkcy5sZW5ndGggPD0gMSkKICAgICAgICAgICAgICAgID8gewogICAgICAgICAgICAgICAgICAgIG5hbWU6ICJyZW1vdmUtYnV0dG9uIiwKICAgICAgICAgICAgICAgICAgICBmbjogX3dpdGhDdHgoKCkgPT4gWwogICAgICAgICAgICAgICAgICAgICAgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiYnV0dG9uIiwgX2hvaXN0ZWRfNSwgX3RvRGlzcGxheVN0cmluZyhfY3R4LnQoJ2dlbmVyaWMucmVtb3ZlJykpLCAxIC8qIFRFWFQgKi8pCiAgICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgICAga2V5OiAiMCIKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgOiB1bmRlZmluZWQKICAgICAgICAgICAgXSksIDEwMzIgLyogUFJPUFMsIERZTkFNSUNfU0xPVFMgKi8sIFsidmFsdWUiLCAibW9kZSJdKSkKICAgICAgICAgIDogX2NyZWF0ZUNvbW1lbnRWTm9kZSgidi1pZiIsIHRydWUpCiAgICAgIF0pCiAgICBdKQogIF0pKQp9"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AllowedPrincipals.vue"], "names": ["t"], "mappings": ";;qBAoES,KAAK,EAAC,KAAK;qBACT,KAAK,EAAC,YAAY;qBAQlB,KAAK,EAAC,YAAY;;;EA0Bf,IAAI,EAAC,QAAQ;EACb,QAAQ,EAAR,EAAQ;EACR,KAAK,EAAC,8BAA8B;;;;;;;;;;wBAxChD,oBAuDM;IAtDJ,oBAAmF,6BAA5EA,MAAC,2CAA2C,iBAAU,CAAC,WAAW;;IAEzE,oBAmDM,OAnDN,UAmDM;MAlDJ,oBAOM,OAPN,UAOM;QANJ,aAKE;UAJQ,KAAK,EAAE,iBAAU,CAAC,UAAU;kEAArB,iBAAU,CAAC,UAAU;UACpC,IAAI,EAAC,YAAY;UAChB,IAAI,EAAE,WAAI;UACV,OAAO,EAAE,0BAAiB;;;;MAG/B,oBAyCM,OAzCN,UAyCM;SAxCM,mBAAU;2BAApB,oBAKK;cAJH,aAGE;gBAFA,CAAC,EAAC,sCAAsC;gBACvC,GAAG,EAAE,IAAI;;;;;SAIN,mBAAU;2BADlB,aAiCY;cA/BV,GAAG,EAAC,qBAAqB;cACjB,KAAK,EAAE,iBAAU,CAAC,mBAAmB;sEAA9B,iBAAU,CAAC,mBAAmB;cAC7C,WAAS,EAAC,sCAAsC;cAC/C,IAAI,EAAE,WAAI;cACV,MAAM,EAAE,KAAK;;cAEH,KAAK,WACd,CAEE,CAHe,GAAG;gBACpB,aAEE;kBADC,KAAK,EAAE,GAAG,CAAC,KAAK;;;cAiBV,GAAG,WACZ,CAGE;gBAHF,aAGE;kBAFC,IAAI,EAAE,WAAI;kBACV,KAAG,EAAE,qBAAY;;;;;eAfd,iBAAU,CAAC,mBAAmB,CAAC,MAAM;;0BAC1C,eAAa;iCAEd,CAMS;sBANT,oBAMS,UANT,UAMS,mBADJA,MAAC", "sourcesContent": ["<script>\nimport { RadioGroup } from '@components/Form/Radio';\nimport ArrayList from '@shell/components/form/ArrayList';\nimport Principal from '@shell/components/auth/Principal';\nimport SelectPrincipal from '@shell/components/auth/SelectPrincipal';\nimport { _EDIT } from '@shell/config/query-params';\nimport uniq from 'lodash/uniq';\n\nexport default {\n  components: {\n    SelectPrincipal,\n    ArrayList,\n    RadioGroup,\n    Principal,\n  },\n\n  props: {\n    provider: {\n      type:     String,\n      required: true,\n    },\n\n    authConfig: {\n      type:     Object,\n      required: true,\n    },\n\n    mode: {\n      type:    String,\n      default: _EDIT,\n    },\n  },\n\n  computed: {\n    accessModeOptions() {\n      return ['unrestricted', 'restricted', 'required'].map((k) => {\n        return {\n          label: this.t(`authConfig.accessMode.${ k }`, { provider: this.authConfig.nameDisplay }),\n          value: k,\n        };\n      });\n    },\n\n    accessMode() {\n      return this.authConfig?.accessMode;\n    }\n  },\n\n  created() {\n    if ( !this.authConfig.accessMode ) {\n      this.authConfig['accessMode'] = 'restricted';\n    } if (!this.authConfig.allowedPrincipalIds) {\n      this.authConfig['allowedPrincipalIds'] = [];\n    }\n  },\n\n  methods: {\n    addPrincipal(id) {\n      this.authConfig.allowedPrincipalIds = uniq([...this.authConfig.allowedPrincipalIds, id]);\n    },\n  }\n};\n</script>\n\n<template>\n  <div>\n    <h3>{{ t('authConfig.accessMode.label', {provider: authConfig.nameDisplay}) }}</h3>\n\n    <div class=\"row\">\n      <div class=\"col span-6\">\n        <RadioGroup\n          v-model:value=\"authConfig.accessMode\"\n          name=\"accessMode\"\n          :mode=\"mode\"\n          :options=\"accessModeOptions\"\n        />\n      </div>\n      <div class=\"col span-6\">\n        <h4 v-if=\"accessMode!=='unrestricted'\">\n          <t\n            k=\"authConfig.allowedPrincipalIds.title\"\n            :raw=\"true\"\n          />\n        </h4>\n        <ArrayList\n          v-if=\"accessMode!=='unrestricted'\"\n          key=\"allowedPrincipalIds\"\n          v-model:value=\"authConfig.allowedPrincipalIds\"\n          title-key=\"authConfig.allowedPrincipalIds.label\"\n          :mode=\"mode\"\n          :protip=\"false\"\n        >\n          <template #value=\"{row}\">\n            <Principal\n              :value=\"row.value\"\n            />\n          </template>\n\n          <template\n            v-if=\"authConfig.allowedPrincipalIds.length <= 1\"\n            #remove-button\n          >\n            <button\n              type=\"button\"\n              disabled\n              class=\"btn role-link bg-transparent\"\n            >\n              {{ t('generic.remove') }}\n            </button>\n          </template>\n\n          <template #add>\n            <SelectPrincipal\n              :mode=\"mode\"\n              @add=\"addPrincipal\"\n            />\n          </template>\n        </ArrayList>\n      </div>\n    </div>\n  </div>\n</template>\n"]}]}