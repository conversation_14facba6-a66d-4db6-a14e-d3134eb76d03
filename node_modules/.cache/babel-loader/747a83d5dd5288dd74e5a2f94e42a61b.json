{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SortableTable/selection.js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SortableTable/selection.js", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}