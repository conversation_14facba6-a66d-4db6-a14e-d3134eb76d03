{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/HookOption.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/HookOption.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/HookOption.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX;IACF;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpD,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IAC7C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClB;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9C;;IAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC/C,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;;MAEtC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACxC;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACjD,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC;MACP;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB;MACF;IACF;EACF;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/HookOption.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport debounce from 'lodash/debounce';\nimport { RadioGroup } from '@components/Form/Radio';\nimport { LabeledInput } from '@components/Form/LabeledInput';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport ShellInput from '@shell/components/form/ShellInput';\nimport { _VIEW } from '@shell/config/query-params';\nimport { isEmpty } from '@shell/utils/object';\n\nexport default {\n  emits: ['update:value'],\n\n  props: {\n    mode: {\n      type:     String,\n      required: true,\n    },\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  components: {\n    RadioGroup, LabeledInput, LabeledSelect, ShellInput\n  },\n\n  data() {\n    const selectHook = null;\n\n    const defaultExec = { exec: { command: [] } };\n    const defaultHttpGet = {\n      httpGet: {\n        host:        '',\n        path:        '',\n        port:        null,\n        scheme:      '',\n        httpHeaders: null\n      }\n    };\n\n    return {\n      selectHook,\n      defaultExec,\n      defaultHttpGet,\n      schemeOptions: ['HTTP', 'HTTPS']\n    };\n  },\n\n  computed: {\n    isView() {\n      return this.mode === _VIEW;\n    },\n  },\n\n  created() {\n    if (this.value) {\n      this.selectHook = Object.keys(this.value)[0];\n    }\n\n    if (isEmpty(this.value)) {\n      this.selectHook = 'none';\n    }\n\n    this.queueUpdate = debounce(this.update, 500);\n  },\n\n  methods: {\n    addHeader() {\n      const header = { name: '', value: '' };\n\n      if (!this.value.httpGet.httpHeaders) {\n        this.value.httpGet['httpHeaders'] = [];\n      }\n\n      this.value.httpGet.httpHeaders.push(header);\n    },\n\n    removeHeader(index) {\n      this.value.httpGet.httpHeaders.splice(index, 1);\n    },\n\n    update() {\n      const { ...leftovers } = this.value;\n\n      switch (this.selectHook) {\n      case 'none':\n        this.deleteLeftovers(leftovers);\n        break;\n      case 'exec':\n        this.deleteLeftovers(leftovers);\n        Object.assign(this.value, this.defaultExec);\n        break;\n      case 'httpGet':\n        this.deleteLeftovers(leftovers);\n        Object.assign(this.value, this.defaultHttpGet);\n        break;\n      default:\n        break;\n      }\n\n      this.$emit('update:value', this.value);\n    },\n\n    deleteLeftovers(leftovers) {\n      if (leftovers) {\n        for (const obj in leftovers) {\n          delete this.value[obj];\n        }\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <div>\n    <div class=\"row mb-10\">\n      <RadioGroup\n        v-model:value=\"selectHook\"\n        name=\"selectHook\"\n        :options=\"['none', 'exec', 'httpGet']\"\n        :labels=\"[\n          t('generic.none'),\n          t('workload.container.lifecycleHook.exec.add'),\n          t('workload.container.lifecycleHook.httpGet.add'),\n        ]\"\n        :mode=\"mode\"\n        @update:value=\"update\"\n      />\n    </div>\n\n    <template v-if=\"selectHook === 'exec'\">\n      <div class=\"mb-20 single-value\">\n        <h4>{{ t('workload.container.lifecycleHook.exec.title') }}</h4>\n        <div>\n          <ShellInput\n            v-model:value=\"value.exec.command\"\n            :mode=\"mode\"\n            :label=\"t('workload.container.lifecycleHook.exec.command.label')\"\n            :placeholder=\"t('workload.container.lifecycleHook.exec.command.placeholder', null, true)\"\n            required\n          />\n        </div>\n      </div>\n    </template>\n\n    <template v-if=\"selectHook === 'httpGet'\">\n      <h4>{{ t('workload.container.lifecycleHook.httpGet.title') }}</h4>\n      <div class=\"var-row\">\n        <LabeledInput\n          v-model:value=\"value.httpGet.host\"\n          :label=\"t('workload.container.lifecycleHook.httpGet.host.label')\"\n          :placeholder=\"t('workload.container.lifecycleHook.httpGet.host.placeholder')\"\n          :mode=\"mode\"\n          @update:value=\"update\"\n        />\n        <LabeledInput\n          v-model:value=\"value.httpGet.path\"\n          :label=\"t('workload.container.lifecycleHook.httpGet.path.label')\"\n          :placeholder=\"t('workload.container.lifecycleHook.httpGet.path.placeholder')\"\n          :mode=\"mode\"\n          @update:value=\"update\"\n        />\n        <LabeledInput\n          v-model:value.number=\"value.httpGet.port\"\n          type=\"number\"\n          :label=\"t('workload.container.lifecycleHook.httpGet.port.label')\"\n          :placeholder=\"t('workload.container.lifecycleHook.httpGet.port.placeholder')\"\n          :mode=\"mode\"\n          required\n          @update:value=\"update\"\n        />\n        <LabeledSelect\n          v-model:value=\"value.httpGet.scheme\"\n          :label=\"t('workload.container.lifecycleHook.httpGet.scheme.label')\"\n          :placeholder=\"t('workload.container.lifecycleHook.httpGet.scheme.placeholder')\"\n          :options=\"schemeOptions\"\n          :mode=\"mode\"\n          @update:value=\"update\"\n        />\n      </div>\n\n      <h4>{{ t('workload.container.lifecycleHook.httpHeaders.title') }}</h4>\n      <div\n        v-for=\"(header, index) in value.httpGet.httpHeaders\"\n        :key=\"index\"\n        class=\"var-row\"\n        data-testid=\"hookoption-header-row\"\n      >\n        <LabeledInput\n          v-model:value=\"value.httpGet.httpHeaders[index].name\"\n          :label=\"t('workload.container.lifecycleHook.httpHeaders.name.label')\"\n          :placeholder=\"t('workload.container.lifecycleHook.httpHeaders.name.placeholder')\"\n          class=\"single-value\"\n          :mode=\"mode\"\n          required\n          @update:value=\"update\"\n        />\n        <LabeledInput\n          v-model:value=\"value.httpGet.httpHeaders[index].value\"\n          :label=\"t('workload.container.lifecycleHook.httpHeaders.value.label')\"\n          :placeholder=\"t('workload.container.lifecycleHook.httpHeaders.value.placeholder')\"\n          class=\"single-value\"\n          :mode=\"mode\"\n          @update:value=\"update\"\n        />\n        <div class=\"remove\">\n          <button\n            v-if=\"!isView\"\n            type=\"button\"\n            class=\"btn role-link ml0\"\n            :disabled=\"mode==='view'\"\n            @click.stop=\"removeHeader(index)\"\n          >\n            <t k=\"generic.remove\" />\n          </button>\n        </div>\n      </div>\n\n      <div>\n        <button\n          v-if=\"!isView\"\n          type=\"button\"\n          class=\"btn role-link mb-20\"\n          :disabled=\"mode === 'view'\"\n          data-testid=\"hookoption-add-header-button\"\n          @click.stop=\"addHeader\"\n        >\n          Add Header\n        </button>\n      </div>\n    </template>\n  </div>\n</template>\n\n<style lang='scss' scoped>\n.var-row{\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr 1fr 100px;\n  grid-column-gap: 20px;\n  margin-bottom: 20px;\n  align-items: center;\n\n  .single-value {\n    grid-column: span 2;\n  }\n\n  .labeled-select {\n    min-height: $input-height;\n  }\n  .remove BUTTON {\n    padding: 0px;\n  }\n}\n</style>\n"]}]}