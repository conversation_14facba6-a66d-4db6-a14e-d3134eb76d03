{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/NamespaceFilter.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/NamespaceFilter.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/NamespaceFilter.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE3D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEpI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;EAEb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACvG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3D,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC/B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IAC/J,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEtB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACpC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAClD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAE/F,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACpB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;YAC3D;UACF;QACF;;QAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrH;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC;;MAEF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAClB;;MAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;;QAEX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACV,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEN,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QACtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACzE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACpC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UAC9E,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEhD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrE;MACF,CAAC,CAAC;;MAEF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACZ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;QACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb;;MAEA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChF,CAAC,CAAC;;MAEF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;MACzB,CAAC;IACH,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE9D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACxC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEhC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACpE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEzG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC9E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACzH,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEhC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC3E,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAElH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC;MACJ;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7C,CAAC,CAAC,EAAE,EAAE;UACJ;YACE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC;UACD;YACE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC;UACD;YACE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC;UACD;YACE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC;UACD;YACE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC;QACH,CAAC;;QAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACZ;;MAEA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE9C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAClE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACpC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC;;QAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxI,CAAC,CAAC;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC9B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C;;QAEA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAClC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC1C,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UAClB;;UAEA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE1C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACV,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAClD;UACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB;;QAEA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC9B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEhC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAC3D,CAAC,CAAC;;UAEF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;UAEpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC;;QAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEzC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAClB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC;;UAEF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B;MACF,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACrC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACL,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cAC9D,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC/D,CAAC;UACH,CAAC;QACH,CAAC;MACH;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC;MACJ;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvG,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE5B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAErB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEtC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAE9C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACjB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd;;QAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;UAClB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpE;;QAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACnC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACV;;QAEA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;QAEP,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC3B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC3C,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACvC,CAAC,CAAC,CAAC;YACH,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC;IACH;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACrI,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACd;KACA,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAClH;KACA,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACrG;KACA,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MAC3B;IACF;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC3B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACpD,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACtC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QACpE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACnB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7F,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9D,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;;UAEd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;;UAE/C,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;UAChE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;UACpB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;UAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;;UAEd,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YAClC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;YAErC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;YAClF,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACjB;;YAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACR,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;gBACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBAClC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;kBACxB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChC;cACF,EAAE,CAAC,CAAC,CAAC,EAAE;gBACL,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChC;YACF;;YAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrD;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C;MACF,CAAC,CAAC;IACJ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1D,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd;IACF,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC1D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClB;QACA,CAAC,CAAC,CAAC,CAAC,CAAC;MACP;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEtE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACzB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE5D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACrC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACrB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAErC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B;QACF;MACF,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE9B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE7B,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACzC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB;QACF;;QAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B;MACF;IACF,CAAC;IACD,CAAC,CAAC,CAAC,EAAE;MACH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC1B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEjD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE7B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACzC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B;QACF;;QAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B;MACF;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACjB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR;;MAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACvD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;YAErD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAC1B;QACF,CAAC,CAAC;MACJ;;MAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE3D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACvF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAC3B,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE5B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnG,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/G,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3E;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC;EACH;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/NamespaceFilter.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { mapGetters } from 'vuex';\nimport { NAMESPACE_FILTERS, ALL_NAMESPACES } from '@shell/store/prefs';\nimport { NAMESPACE, MANAGEMENT } from '@shell/config/types';\nimport { sortBy } from '@shell/utils/sort';\nimport { isArray, addObjects, findBy, filterBy } from '@shell/utils/array';\nimport {\n  NAMESPACE_FILTER_ALL_USER as ALL_USER,\n  NAMESPACE_FILTER_ALL as ALL,\n  NAMESPACE_FILTER_ALL_SYSTEM as ALL_SYSTEM,\n  NAMESPACE_FILTER_ALL_ORPHANS as ALL_ORPHANS,\n  NAMESPACE_FILTER_NAMESPACED_YES as NAMESPACED_YES,\n  NAMESPACE_FILTER_NAMESPACED_NO as NAMESPACED_NO,\n  createNamespaceFilterKey,\n  NAMESPACE_FILTER_KINDS,\n  NAMESPACE_FILTER_NS_FULL_PREFIX,\n  NAMESPACE_FILTER_P_FULL_PREFIX,\n} from '@shell/utils/namespace-filter';\nimport { KEY } from '@shell/utils/platform';\nimport pAndNFiltering from '@shell/plugins/steve/projectAndNamespaceFiltering.utils';\nimport { SETTING } from '@shell/config/settings';\nimport paginationUtils from '@shell/utils/pagination-utils';\n\nconst forcedNamespaceValidTypes = [NAMESPACE_FILTER_KINDS.DIVIDER, NAMESPACE_FILTER_KINDS.PROJECT, NAMESPACE_FILTER_KINDS.NAMESPACE];\n\nexport default {\n\n  data() {\n    return {\n      isOpen:              false,\n      filter:              '',\n      hidden:              0,\n      total:               0,\n      activeElement:       null,\n      cachedFiltered:      [],\n      NAMESPACE_FILTER_KINDS,\n      namespaceFilterMode: undefined,\n    };\n  },\n\n  async fetch() {\n    // Determine if filtering by specific namespaces/projects is required\n    // This is done once and up front\n    // - it doesn't need to be re-active\n    // - added it as a computed caused massive amounts of churn around the `filtered` watcher\n    await this.$store.dispatch('management/find', { type: MANAGEMENT.SETTING, id: SETTING.UI_PERFORMANCE });\n    this.namespaceFilterMode = this.calcNamespaceFilterMode();\n  },\n\n  computed: {\n    ...mapGetters(['currentProduct']),\n\n    hasFilter() {\n      return this.filter.length > 0;\n    },\n\n    paginatedListFilterMode() {\n      return this.$store.getters[`${ this.currentProduct.inStore }/paginationEnabled`](this.$route.params?.resource) ? paginationUtils.validNsProjectFilters : null;\n    },\n\n    filtered() {\n      let out = this.options;\n\n      out = out.filter((item) => {\n        // Filter out anything not applicable to singleton selection\n        if (this.namespaceFilterMode?.length) {\n          // We always show dividers, projects and namespaces\n          if (!forcedNamespaceValidTypes.includes(item.kind)) {\n            const validCustomType = this.namespaceFilterMode.find((prefix) => item.kind.startsWith(prefix));\n\n            if (!validCustomType) {\n              // Hide any invalid option that's not selected\n              return this.value.findIndex((v) => v.id === item.id) >= 0;\n            }\n          }\n        }\n\n        // Filter by the current filter\n        if (this.hasFilter) {\n          return item.kind !== NAMESPACE_FILTER_KINDS.SPECIAL && item.label.toLowerCase().includes(this.filter.toLowerCase());\n        }\n\n        return true;\n      });\n\n      if (out?.[0]?.kind === NAMESPACE_FILTER_KINDS.DIVIDER) {\n        out.splice(0, 1);\n      }\n\n      const mapped = this.value.reduce((m, v) => {\n        m[v.id] = v;\n\n        return m;\n      }, {});\n\n      // Mark all of the selected options\n      out.forEach((i) => {\n        i.selected = !!mapped[i.id] || (i.id === ALL && this.value && this.value.length === 0);\n        i.elementId = (i.id || '').replace('://', '_');\n        i.enabled = true;\n        // Are we in restricted resource type mode, if so is this an allowed type?\n        if (this.namespaceFilterMode?.length) {\n          const isLastSelected = i.selected && (i.id === ALL || this.value.length === 1);\n          const kindAllowed = this.namespaceFilterMode.find((f) => f === i.kind);\n          const isNotInProjectGroup = i.id === ALL_ORPHANS;\n\n          i.enabled = (!isLastSelected && kindAllowed) && !isNotInProjectGroup;\n        } else if (this.paginatedListFilterMode?.length) {\n          i.enabled = !!i.id && paginationUtils.validateNsProjectFilter(i.id);\n        }\n      });\n\n      return out;\n    },\n\n    tooltip() {\n      if (this.isOpen || (this.total + this.hidden) === 0) {\n        return null;\n      }\n\n      let tooltip = '<div class=\"ns-filter-tooltip\">';\n\n      (this.value || []).forEach((v) => {\n        tooltip += `<div class=\"ns-filter-tooltip-item\"><div>${ v.label }</div></div>`;\n      });\n\n      tooltip += '</div>';\n\n      return {\n        content:   tooltip,\n        placement: 'bottom',\n        delay:     { show: 500 }\n      };\n    },\n\n    key() {\n      return createNamespaceFilterKey(this.$store.getters['clusterId'], this.currentProduct);\n    },\n\n    options() {\n      const t = this.$store.getters['i18n/t'];\n      let out = [];\n      const inStore = this.$store.getters['currentStore'](NAMESPACE);\n\n      const params = { ...this.$route.params };\n      const resource = params.resource;\n\n      // Sometimes, different pages may have different namespaces to filter\n      const notFilterNamespaces = this.$store.getters[`type-map/optionsFor`](resource).notFilterNamespace || [];\n\n      // TODO: Add return info\n      if (this.currentProduct?.customNamespaceFilter && this.currentProduct?.inStore) {\n        // Sometimes the component can show before the 'currentProduct' has caught up, so access the product via the getter rather\n        // than caching it in the `fetch`\n\n        // The namespace display on the list and edit pages should be the same as in the namespaceFilter component\n        if (this.$store.getters[`${ this.currentProduct.inStore }/filterNamespace`]) {\n          const allNamespaces = this.$store.getters[`${ this.currentProduct.inStore }/filterNamespace`](notFilterNamespaces);\n\n          this.$store.commit('changeAllNamespaces', allNamespaces);\n        }\n\n        return this.$store.getters[`${ this.currentProduct.inStore }/namespaceFilterOptions`]({\n          addNamespace,\n          divider,\n          notFilterNamespaces\n        });\n      }\n\n      // TODO: Add return info\n      if (!this.currentProduct?.hideSystemResources) {\n        out = [\n          {\n            id:    ALL,\n            kind:  NAMESPACE_FILTER_KINDS.SPECIAL,\n            label: t('nav.ns.all'),\n          },\n          {\n            id:    ALL_USER,\n            kind:  NAMESPACE_FILTER_KINDS.SPECIAL,\n            label: t('nav.ns.user'),\n          },\n          {\n            id:    ALL_SYSTEM,\n            kind:  NAMESPACE_FILTER_KINDS.SPECIAL,\n            label: t('nav.ns.system'),\n          },\n          {\n            id:    NAMESPACED_YES,\n            kind:  NAMESPACE_FILTER_KINDS.SPECIAL,\n            label: t('nav.ns.namespaced'),\n          },\n          {\n            id:    NAMESPACED_NO,\n            kind:  NAMESPACE_FILTER_KINDS.SPECIAL,\n            label: t('nav.ns.clusterLevel'),\n          },\n        ];\n\n        divider(out);\n      }\n\n      if (!inStore) {\n        return out;\n      }\n\n      let namespaces = sortBy(\n        this.$store.getters[`${ inStore }/all`](NAMESPACE),\n        ['nameDisplay']\n      );\n\n      namespaces = this.filterNamespaces(namespaces);\n\n      // isRancher = mgmt schemas are loaded and there's a project schema\n      if (this.$store.getters['isRancher']) {\n        const cluster = this.$store.getters['currentCluster'];\n        let projects = this.$store.getters['management/all'](\n          MANAGEMENT.PROJECT\n        );\n\n        projects = projects.filter((p) => {\n          return this.currentProduct?.hideSystemResources ? !p.isSystem && p.spec.clusterName === cluster.id : p.spec.clusterName === cluster.id;\n        });\n        projects = sortBy(filterBy(projects, 'spec.clusterName', cluster.id), [\n          'nameDisplay',\n        ]);\n        const projectsById = {};\n        const namespacesByProject = {};\n        let firstProject = true;\n\n        namespacesByProject[null] = []; // For namespaces not in a project\n        for (const project of projects) {\n          projectsById[project.metadata.name] = project;\n        }\n\n        for (const namespace of namespaces) {\n          let projectId = namespace.projectId;\n\n          if (!projectId || !projectsById[projectId]) {\n            // If there's a projectId but that project doesn't exist, treat it like no project\n            projectId = null;\n          }\n\n          let entry = namespacesByProject[projectId];\n\n          if (!entry) {\n            entry = [];\n            namespacesByProject[namespace.projectId] = entry;\n          }\n          entry.push(namespace);\n        }\n\n        for (const project of projects) {\n          const id = project.metadata.name;\n\n          if (firstProject) {\n            firstProject = false;\n          } else {\n            divider(out);\n          }\n\n          out.push({\n            id:    `${ NAMESPACE_FILTER_P_FULL_PREFIX }${ id }`,\n            kind:  NAMESPACE_FILTER_KINDS.PROJECT,\n            label: t('nav.ns.project', { name: project.nameDisplay }),\n          });\n\n          const forThisProject = namespacesByProject[id] || [];\n\n          addNamespace(out, forThisProject);\n        }\n\n        const orphans = namespacesByProject[null];\n\n        if (orphans.length) {\n          if (!firstProject) {\n            divider(out);\n          }\n\n          out.push({\n            id:       ALL_ORPHANS,\n            kind:     NAMESPACE_FILTER_KINDS.PROJECT,\n            label:    t('nav.ns.orphan'),\n            disabled: true,\n          });\n\n          addNamespace(out, orphans);\n        }\n      } else {\n        addNamespace(out, namespaces);\n      }\n\n      return out;\n\n      function addNamespace(out, namespaces) {\n        if (!isArray(namespaces)) {\n          namespaces = [namespaces];\n        }\n\n        addObjects(\n          out,\n          namespaces.map((namespace) => {\n            return {\n              id:    `${ NAMESPACE_FILTER_NS_FULL_PREFIX }${ namespace.id }`,\n              kind:  NAMESPACE_FILTER_KINDS.NAMESPACE,\n              label: t('nav.ns.namespace', { name: namespace.nameDisplay }),\n            };\n          })\n        );\n      }\n\n      function divider(out) {\n        out.push({\n          kind:     NAMESPACE_FILTER_KINDS.DIVIDER,\n          label:    `Divider ${ out.length }`,\n          disabled: true,\n        });\n      }\n    },\n\n    isSingleSpecial() {\n      return this.value && this.value.length === 1 && this.value[0].kind === NAMESPACE_FILTER_KINDS.SPECIAL;\n    },\n\n    value: {\n      get() {\n        // Use last picked filter from user preferences\n        const prefs = this.$store.getters['prefs/get'](NAMESPACE_FILTERS);\n        const values = prefs && prefs[this.key] ? prefs[this.key] : this.defaultOption();\n        const options = this.options;\n\n        // Remove values that are not valid options\n        const filters = values\n          .map((value) => {\n            return findBy(options, 'id', value);\n          })\n          .filter((x) => !!x);\n\n        return filters;\n      },\n\n      set(neu) {\n        const old = (this.value || []).slice();\n\n        neu = neu.filter((x) => !!x.id);\n\n        const last = neu[neu.length - 1];\n        const lastIsSpecial = last?.kind === NAMESPACE_FILTER_KINDS.SPECIAL;\n        const hadUser = !!old.find((x) => x.id === ALL_USER);\n        const hadAll = !!old.find((x) => x.id === ALL);\n\n        if (lastIsSpecial) {\n          neu = [last];\n        }\n\n        if (neu.length > 1) {\n          neu = neu.filter((x) => x.kind !== NAMESPACE_FILTER_KINDS.SPECIAL);\n        }\n\n        if (neu.find((x) => x.id === 'all')) {\n          neu = [];\n        }\n\n        let ids;\n\n        // If there was something selected and you remove it, go back to user by default\n        // Unless it was user or all\n        if (neu.length === 0 && !hadUser && !hadAll) {\n          ids = this.defaultOption();\n        } else {\n          ids = neu.map((x) => x.id);\n        }\n\n        this.$nextTick(() => {\n          this.$store.dispatch('switchNamespaces', {\n            ids,\n            key: this.key\n          });\n        });\n      },\n    }\n  },\n\n  beforeUnmount() {\n    this.removeCloseKeyHandler();\n  },\n\n  mounted() {\n    this.layout();\n  },\n\n  watch: {\n    value(neu) {\n      this.layout();\n    },\n\n    /**\n     * When there are thousands of entries certain actions (drop down opened, selection changed, etc) take a long time to complete (upwards\n     * of 5 seconds)\n     *\n     * This is caused by churn of the filtered and options computed properties causing multiple renders for each action.\n     *\n     * To break this multiple-render per cycle behaviour detatch `filtered` from the value used in `v-for`.\n     *\n     */\n    filtered(neu) {\n      if (!!neu) {\n        this.cachedFiltered = neu;\n      }\n    }\n  },\n\n  methods: {\n    filterNamespaces(namespaces) {\n      if (this.$store.getters['prefs/get'](ALL_NAMESPACES)) {\n        // If all namespaces options are turned on in the user preferences,\n        // return all namespaces including system namespaces and RBAC\n        // management namespaces.\n        return namespaces;\n      }\n\n      return namespaces.filter((namespace) => {\n        // Otherwise only filter out obscure namespaces, such as namespaces\n        // that Rancher uses to manage RBAC for projects, which should not be\n        // edited or deleted by Rancher users.\n        return !namespace.isObscure;\n      });\n    },\n    // Layout the content in the dropdown box to best use the space to show the selection\n    layout() {\n      this.$nextTick(() => {\n        // One we have re-rendered, see what we can fit in the control to show the selected namespaces\n        if (this.$refs.values) {\n          const container = this.$refs.values;\n          const overflow = container.scrollWidth > container.offsetWidth;\n          let hidden = 0;\n\n          const dropdown = this.$refs.dropdown;\n          // Remove padding and dropdown arrow size\n          const maxWidth = dropdown.offsetWidth - 20 - 24;\n\n          // If we are overflowing, then allow some space for the +N indicator\n          const itemCount = this.$refs.value ? this.$refs.value.length : 0;\n          let currentWidth = 0;\n          let show = true;\n\n          this.total = 0;\n\n          for (let i = 0; i < itemCount; i++) {\n            const item = this.$refs.value[i];\n            let itemWidth = item.offsetWidth + 10;\n\n            // If this is the first item and we have overflow then add on some space for the +N\n            if (i === 0 && overflow) {\n              itemWidth += 40;\n            }\n\n            currentWidth += itemWidth;\n\n            if (show) {\n              if (i === 0) {\n                // Can't even fit the first item in\n                if (itemWidth > maxWidth) {\n                  show = false;\n                  this.total = this.value.length;\n                }\n              } else {\n                show = currentWidth < maxWidth;\n              }\n            }\n\n            hidden += show ? 0 : 1;\n            item.style.visibility = show ? 'visible' : 'hidden';\n          }\n\n          this.hidden = this.total > 0 ? 0 : hidden;\n        }\n      });\n    },\n    addCloseKeyHandler() {\n      document.addEventListener('keyup', this.closeKeyHandler);\n    },\n    removeCloseKeyHandler() {\n      document.removeEventListener('keyup', this.closeKeyHandler);\n    },\n    closeKeyHandler(e) {\n      if (e.keyCode === KEY.ESCAPE ) {\n        this.close();\n      }\n    },\n    // Keyboard support\n    itemKeyHandler(e, opt) {\n      if (e.keyCode === KEY.DOWN ) {\n        e.preventDefault();\n        e.stopPropagation();\n        this.down();\n      } else if (e.keyCode === KEY.UP ) {\n        e.preventDefault();\n        e.stopPropagation();\n        this.up();\n      } else if (e.keyCode === KEY.SPACE || e.keyCode === KEY.CR) {\n        if (this.namespaceFilterMode && !opt.enabled) {\n          return;\n        }\n        e.preventDefault();\n        e.stopPropagation();\n        this.selectOption(opt);\n        e.target.focus();\n      }\n    },\n    inputKeyHandler(e) {\n      switch (e.keyCode) {\n      case KEY.DOWN:\n        e.preventDefault();\n        e.stopPropagation();\n        this.down(true);\n        break;\n      case KEY.TAB:\n        // Tab out of the input box\n        this.close();\n        e.target.blur();\n        break;\n      case KEY.CR:\n        if (this.filtered.length === 1) {\n          this.selectOption(this.filtered[0]);\n          this.filter = '';\n        }\n        break;\n      }\n    },\n    mouseOver(event) {\n      const el = event?.path?.find((e) => e.classList.contains('ns-option'));\n\n      this.activeElement = el;\n    },\n    setActiveElement(el) {\n      if (!el?.focus) {\n        return;\n      }\n\n      el.focus();\n      this.activeElement = null;\n    },\n    down(input) {\n      const exising = this.activeElement || document.activeElement;\n\n      // Focus the first element in the list\n      if (input || !exising) {\n        if (this.$refs.options) {\n          const c = this.$refs.options.children;\n\n          if (c && c.length > 0) {\n            this.setActiveElement(c[0]);\n          }\n        }\n      } else {\n        let next = exising.nextSibling;\n\n        if (next?.children?.length) {\n          const item = next.children[0];\n\n          // Skip over dividers (assumes we don't have two dividers next to each other)\n          if (item.classList.contains('ns-divider')) {\n            next = next.nextSibling;\n          }\n        }\n\n        if (next?.focus) {\n          this.setActiveElement(next);\n        }\n      }\n    },\n    up() {\n      if (document.activeElement) {\n        let prev = document.activeElement.previousSibling;\n\n        if (prev?.children?.length) {\n          const item = prev.children[0];\n\n          if (item.classList.contains('ns-divider')) {\n            prev = prev.previousSibling;\n          }\n        }\n\n        if (prev?.focus) {\n          this.setActiveElement(prev);\n        }\n      }\n    },\n    toggle() {\n      if (this.isOpen) {\n        this.close();\n      } else {\n        this.open();\n      }\n    },\n    open() {\n      this.isOpen = true;\n      this.$nextTick(() => {\n        this.focusFilter();\n      });\n      this.addCloseKeyHandler();\n      this.layout();\n    },\n    focusFilter() {\n      this.$refs.filter.focus();\n    },\n    close() {\n      this.isOpen = false;\n      this.activeElement = null;\n      this.removeCloseKeyHandler();\n      this.layout();\n    },\n    clear() {\n      this.value = [];\n    },\n    selectOption(option) {\n      // Ignore click for a divider\n      if (option.kind === NAMESPACE_FILTER_KINDS.DIVIDER) {\n        return;\n      }\n\n      const current = this.value;\n\n      // Remove invalid\n      if (!!this.namespaceFilterMode?.length) {\n        this.value.forEach((v) => {\n          if (!this.namespaceFilterMode.find((f) => f === v.kind)) {\n            const index = current.findIndex((c) => c.id === v.id);\n\n            current.splice(index, 1);\n          }\n        });\n      }\n\n      const exists = current.findIndex((v) => v.id === option.id);\n\n      // Remove if it exists (or always add if in singleton mode - we've reset the list above)\n      if (exists !== -1) {\n        current.splice(exists, 1);\n      } else {\n        current.push(option);\n      }\n\n      this.value = current;\n\n      if (document.activeElement) {\n        document.activeElement.blur();\n      }\n    },\n    handleValueMouseDown(ns, event) {\n      this.removeOption(ns, event);\n\n      if (this.value.length === 0) {\n        this.open();\n      }\n    },\n\n    removeOption(ns, event) {\n      this.selectOption(ns);\n      event.preventDefault();\n      event.stopPropagation();\n    },\n\n    defaultOption() {\n      // Note - This is one place where a default ns/project filter value is provided (ALL_USER)\n      // There's also..\n      // - dashboard root store `loadCluster` --> when `updateNamespaces` is dispatched\n      // - harvester root store `loadCluster` --> when `updateNamespaces` is dispatched (can be discarded)\n      // Due to this, we can't really set a nicer default when forced ns/project filtering is on (ALL_USER is invalid)\n      if (this.currentProduct?.customNamespaceFilter) {\n        return [];\n      }\n\n      return [ALL_USER];\n    },\n\n    calcNamespaceFilterMode() {\n      if (pAndNFiltering.isEnabled(this.$store.getters)) {\n        return [NAMESPACE_FILTER_KINDS.NAMESPACE, NAMESPACE_FILTER_KINDS.PROJECT];\n      }\n\n      return null;\n    },\n  }\n};\n</script>\n\n<template>\n  <div\n    v-if=\"!$fetchState.pending\"\n    class=\"ns-filter\"\n    data-testid=\"namespaces-filter\"\n    tabindex=\"0\"\n    @mousedown.prevent\n    @focus=\"open()\"\n  >\n    <div\n      v-if=\"isOpen\"\n      class=\"ns-glass\"\n      @click=\"close()\"\n    />\n\n    <!-- Select Dropdown control -->\n    <div\n      ref=\"dropdown\"\n      class=\"ns-dropdown\"\n      data-testid=\"namespaces-dropdown\"\n      :class=\"{ 'ns-open': isOpen }\"\n      @click=\"toggle()\"\n    >\n      <!-- No filters found or available -->\n      <div\n        v-if=\"value.length === 0\"\n        ref=\"values\"\n        data-testid=\"namespaces-values-none\"\n        class=\"ns-values\"\n      >\n        {{ t('nav.ns.all') }}\n      </div>\n\n      <!-- Filtered by set with custom label E.g. \"All namespaces\" -->\n      <div\n        v-else-if=\"isSingleSpecial\"\n        ref=\"values\"\n        data-testid=\"namespaces-values-label\"\n        class=\"ns-values\"\n      >\n        {{ value[0].label }}\n      </div>\n\n      <!-- All the selected namespaces -->\n      <div\n        v-else\n        ref=\"values\"\n        v-clean-tooltip=\"tooltip\"\n        data-testid=\"namespaces-values\"\n        class=\"ns-values\"\n      >\n        <div\n          v-if=\"total\"\n          ref=\"total\"\n          data-testid=\"namespaces-values-total\"\n          class=\"ns-value ns-abs\"\n        >\n          {{ t('namespaceFilter.selected.label', { total }) }}\n        </div>\n        <div\n          v-for=\"(ns, j) in value\"\n          ref=\"value\"\n          :key=\"ns.id\"\n          :data-testid=\"`namespaces-value-${j}`\"\n          class=\"ns-value\"\n        >\n          <div>{{ ns.label }}</div>\n          <!-- block user from removing the last selection if ns forced filtering is on -->\n          <i\n            v-if=\"!namespaceFilterMode || value.length > 1\"\n            class=\"icon icon-close\"\n            :data-testid=\"`namespaces-values-close-${j}`\"\n            @click=\"removeOption(ns, $event)\"\n            @mousedown=\"handleValueMouseDown(ns, $event)\"\n          />\n        </div>\n      </div>\n\n      <!-- Inform user if more namespaces are selected -->\n      <div\n        v-if=\"hidden > 0\"\n        ref=\"more\"\n        v-clean-tooltip=\"tooltip\"\n        class=\"ns-more\"\n      >\n        {{ t('namespaceFilter.more', { more: hidden }) }}\n      </div>\n      <i\n        v-if=\"!isOpen\"\n        class=\"icon icon-chevron-down\"\n      />\n      <i\n        v-else\n        class=\"icon icon-chevron-up\"\n      />\n    </div>\n    <button\n      v-shortkey.once=\"['n']\"\n      class=\"hide\"\n      @shortkey=\"open()\"\n    />\n\n    <!-- Dropdown menu -->\n    <div\n      v-if=\"isOpen\"\n      class=\"ns-dropdown-menu\"\n      data-testid=\"namespaces-menu\"\n    >\n      <div class=\"ns-controls\">\n        <div class=\"ns-input\">\n          <input\n            ref=\"filter\"\n            v-model=\"filter\"\n            tabindex=\"0\"\n            class=\"ns-filter-input\"\n            @click=\"focusFilter\"\n            @keydown=\"inputKeyHandler($event)\"\n          >\n          <i\n            v-if=\"hasFilter\"\n            class=\"ns-filter-clear icon icon-close\"\n            @click=\"filter = ''\"\n          />\n        </div>\n        <div\n          v-if=\"namespaceFilterMode\"\n          class=\"ns-singleton-info\"\n        >\n          <i\n            v-clean-tooltip=\"t('resourceList.nsFilterToolTip')\"\n            class=\"icon icon-info\"\n          />\n        </div>\n        <div\n          v-else\n          class=\"ns-clear\"\n        >\n          <i\n            class=\"icon icon-close\"\n            @click=\"clear()\"\n          />\n        </div>\n      </div>\n      <div class=\"ns-divider mt-0\" />\n      <div\n        ref=\"options\"\n        class=\"ns-options\"\n        role=\"list\"\n      >\n        <div\n          v-for=\"(opt, i) in cachedFiltered\"\n          :id=\"opt.elementId\"\n          :key=\"opt.id\"\n          tabindex=\"0\"\n          class=\"ns-option\"\n          :disabled=\"opt.enabled ? null : true\"\n          :class=\"{\n            'ns-selected': opt.selected,\n            'ns-single-match': cachedFiltered.length === 1 && !opt.selected,\n          }\"\n          :data-testid=\"`namespaces-option-${i}`\"\n          @click=\"opt.enabled && selectOption(opt)\"\n          @mouseover=\"opt.enabled && mouseOver($event)\"\n          @keydown=\"itemKeyHandler($event, opt)\"\n        >\n          <div\n            v-if=\"opt.kind === NAMESPACE_FILTER_KINDS.DIVIDER\"\n            class=\"ns-divider\"\n          />\n          <div\n            v-else\n            class=\"ns-item\"\n          >\n            <i\n              v-if=\"opt.kind === NAMESPACE_FILTER_KINDS.NAMESPACE\"\n              class=\"icon icon-folder\"\n            />\n            <div>{{ opt.label }}</div>\n            <i\n              v-if=\"opt.selected\"\n              class=\"icon icon-checkmark\"\n            />\n          </div>\n        </div>\n        <div\n          v-if=\"cachedFiltered.length === 0\"\n          class=\"ns-none\"\n          data-testid=\"namespaces-option-none\"\n        >\n          {{ t('namespaceFilter.noMatchingOptions') }}\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  $ns_dropdown_size: 24px;\n\n  .ns-abs {\n    position: absolute;\n  }\n\n  .ns-filter {\n    width: 280px;\n    display: inline-block;\n\n    .ns-glass {\n      top: 0;\n      bottom: 0;\n      left: 0;\n      right: 0;\n      opacity: 0;\n      position: fixed;\n\n      z-index: z-index('overContent');\n    }\n\n    .ns-controls {\n      align-items: center;\n      display: flex;\n    }\n\n    .ns-clear {\n      &:hover {\n        color: var(--primary);\n        cursor: pointer;\n      }\n    }\n\n    .ns-singleton-info, .ns-clear {\n      align-items: center;\n      display: flex;\n      > i {\n        padding-right: 5px;\n      }\n    }\n\n    .ns-input {\n      flex: 1;\n      padding: 5px;\n      position: relative;\n    }\n\n    .ns-filter-input {\n      height: 24px;\n    }\n\n    .ns-filter-clear {\n      cursor: pointer;\n      position: absolute;\n      right: 10px;\n      top: 5px;\n      line-height: 24px;\n      text-align: center;\n      width: 24px;\n    }\n\n    .ns-dropdown-menu {\n      background-color: var(--header-bg);\n      border: 1px solid var(--primary-border);\n      border-bottom-left-radius: var(--border-radius);\n      border-bottom-right-radius: var(--border-radius);\n      color: var(--header-btn-text);\n      margin-top: -1px;\n      padding-bottom: 10px;\n      position: relative;\n      z-index: z-index('dropdownOverlay');\n\n      .ns-options {\n        max-height: 50vh;\n        overflow-y: auto;\n\n        .ns-none {\n          color: var(--muted);\n          padding: 0 10px;\n        }\n      }\n\n      .ns-divider {\n        border-top: 1px solid var(--border);\n        cursor: default;\n        margin-top: 10px;\n        padding-bottom: 10px;\n      }\n\n      .ns-option {\n\n        &[disabled] {\n          cursor: default;\n        }\n\n        &:not([disabled]) {\n          &:focus {\n            background-color: var(--dropdown-hover-bg);\n            color: var(--dropdown-hover-text);\n          }\n          .ns-item {\n             &:hover, &:focus {\n              background-color: var(--dropdown-hover-bg);\n              color: var(--dropdown-hover-text);\n              cursor: pointer;\n\n              > i {\n                color: var(--dropdown-hover-text);\n              }\n            }\n          }\n\n          &.ns-selected {\n            &:hover,&:focus {\n              .ns-item {\n                > * {\n                  background-color: var(--dropdown-hover-bg);\n                  color: var(--dropdown-hover-text);\n                }\n              }\n            }\n          }\n\n          &.ns-selected:not(:hover) {\n            .ns-item {\n              > * {\n                color: var(--primary);\n              }\n            }\n\n            &:focus {\n              .ns-item {\n                > * {\n                  color: var(--dropdown-hover-text);\n                }\n              }\n            }\n          }\n        }\n\n        .ns-item {\n          align-items: center;\n          display: flex;\n          height: 24px;\n          line-height: 24px;\n          padding: 0 10px;\n\n          > i {\n            color: var(--muted);\n            margin: 0 5px;\n          }\n\n          > div {\n            flex: 1;\n            overflow: hidden;\n            text-overflow: ellipsis;\n            white-space: nowrap;\n          }\n\n        }\n\n        &.ns-single-match {\n          .ns-item {\n            background-color: var(--dropdown-hover-bg);\n            > * {\n              color: var(--dropdown-hover-text);\n            }\n          }\n        }\n      }\n    }\n\n    .ns-dropdown {\n      align-items: center;\n      display: flex;\n      border: 1px solid var(--header-border);\n      border-radius: var(--border-radius);\n      color: var(--header-btn-text);\n      cursor: pointer;\n      height: 40px;\n      padding: 0 10px;\n      position: relative;\n      z-index: z-index('dropdownOverlay');\n\n      &.ns-open {\n        border-bottom-left-radius: 0;\n        border-bottom-right-radius: 0;\n        border-color: var(--primary-border);\n      }\n\n      > .ns-values {\n        flex: 1;\n      }\n\n      &:hover {\n        > i {\n          color: var(--primary);\n        }\n      }\n\n      > i {\n        height: $ns_dropdown_size;\n        width: $ns_dropdown_size;\n        cursor: pointer;\n        text-align: center;\n        line-height: $ns_dropdown_size;\n      }\n\n      .ns-more {\n        border: 1px solid var(--header-border);\n        border-radius: 5px;\n        padding: 2px 8px;\n        margin-left: 4px;\n      }\n\n      .ns-values {\n        display: flex;\n        overflow: hidden;\n\n        .ns-value {\n          align-items: center;\n          background-color: rgba(0,0,0,.05);\n          border: 1px solid var(--header-border);\n          border-radius: 5px;\n          color: var(--tag-text);\n          display: flex;\n          line-height: 20px;\n          padding: 2px 5px;\n          white-space: nowrap;\n\n          > i {\n            margin-left: 5px;\n\n            &:hover {\n              color: var(--primary);\n            };\n          }\n\n          // Spacing between tags\n          &:not(:last-child) {\n            margin-right: 5px;\n          }\n        }\n      }\n    }\n  }\n</style>\n<style lang=\"scss\">\n  .v-popper__popper {\n    .ns-filter-tooltip {\n      background-color: var(--body-bg);\n      margin: -6px;\n      padding: 6px;\n\n      .ns-filter-tooltip-item {\n        > div {\n          background-color: rgba(0,0,0,.05);\n          border: 1px solid var(--header-border);\n          border-radius: 5px;\n          color: var(--tag-text);\n          display: inline-block;\n          line-height: 20px;\n          padding: 2px 5px;\n          white-space: nowrap;\n          margin: 4px 0;\n        }\n      }\n    }\n  }\n</style>\n"]}]}