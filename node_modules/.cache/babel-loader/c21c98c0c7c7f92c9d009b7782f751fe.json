{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??clonedRuleSet-44.use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/index.vue?vue&type=template&id=f9b48e2a&scoped=true&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/index.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/index.vue?vue&type=template&id=f9b48e2a&scoped=true&ts=true", "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/index.vue"], "names": ["t", "summary", "isViewMode", "value", "mode", "dialogClosed"], "mappings": "AAAA,OAAO,EAAE,eAAe,IAAI,gBAAgB,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,gBAAgB,IAAI,iBAAiB,EAAE,WAAW,IAAI,YAAY,EAAE,eAAe,IAAI,gBAAgB,EAAE,QAAQ,IAAI,SAAS,EAAE,SAAS,IAAI,UAAU,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,MAAM,KAAK,CAAA;AAEnV,MAAM,UAAU,GAAG;ICqDf,KAAK,EAAC,qCAAqC;IAC3C,aAAW,EAAC,uBAAuB;CDnDtC,CAAA;AACD,MAAM,UAAU,GAAG;ICsDb,KAAK,EAAC,aAAa;IACnB,aAAW,EAAC,+BAA+B;CDpDhD,CAAA;AAED,MAAM,UAAU,MAAM,CAAC,IAAS,EAAC,MAAW,EAAC,MAAW,EAAC,MAAW,EAAC,KAAU,EAAC,QAAa;IAC3F,MAAM,+BAA+B,GAAG,iBAAiB,CAAC,sBAAsB,CAAE,CAAA;IAElF,OAAO,CAAC,UAAU,EAAE,ECwCpB,mBAAA,CA4BM,KAAA,EA5BN,UA4BM,EAAA;QAxBJ,mBAAA,CAA+C,OAAA,EAAA,IAAA,EAAA,gBAAA,CAArCA,IAAAA,CAAAA,CAAC,CAAA,uBAAA,CAAA,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;QD1CX,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;QC2C7C,mBAAA,CAKM,KAAA,EALN,UAKM,EAAA,gBAAA,CADDC,IAAAA,CAAAA,OAAO,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;QD7CZ,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;QAC7C,CAAC,CC8CgBC,IAAAA,CAAAA,UAAU,CAAA;YD7CzB,CAAC,CAAC,CAAC,UAAU,EAAE,EC6CjB,mBAAA,CAgBW,SAAA,EAAA,EAAA,GAAA,EAAA,CAAA,EAAA,EAAA;gBAfT,mBAAA,CAOS,QAAA,EAAA;oBANP,GAAG,EAAC,QAAQ;oBACZ,aAAW,EAAC,mCAAmC;oBAC/C,KAAK,EAAC,qBAAqB;oBAC1B,OAAK,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA;wBD5Cd,YAAY;wBACZ,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC,CC2CG,IAAA,CAAA,UAAA,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAA,CAAU,CAAA,CAAA;iBD1Cf,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG;oBC4C/B,mBAAA,CAA4B,GAAA,EAAA,EAAzB,KAAK,EAAC,gBAAgB,EAAA,EAAA,IAAA,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA;iBD1CtB,CAAC,EAAE,GAAG,CAAC,gBAAgB,CAAC;gBACzB,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;gBC4CjD,YAAA,CAKE,+BAAA,EAAA;oBAJA,GAAG,EAAC,YAAY;oBACf,KAAK,EAAEC,IAAAA,CAAAA,KAAK;oBACZ,IAAI,EAAEC,IAAAA,CAAAA,IAAI;oBACV,QAAM,EAAEC,IAAAA,CAAAA,YAAY;iBD1ClB,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;aACvD,EAAE,EAAE,CAAC,qBAAqB,CAAC,CAAC;YAC/B,CAAC,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;KACtC,CAAC,CAAC,CAAA;AACL,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/SSHKnownHosts/index.vue.tsx", "sourceRoot": "", "sourcesContent": ["import { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createTextVNode as _createTextVNode, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode } from \"vue\"\n\nconst _hoisted_1 = {\n  class: \"input-known-ssh-hosts labeled-input\",\n  \"data-testid\": \"input-known-ssh-hosts\"\n}\nconst _hoisted_2 = {\n  class: \"hosts-input\",\n  \"data-testid\": \"input-known-ssh-hosts_summary\"\n}\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_KnownHostsEditDialog = _resolveComponent(\"KnownHostsEditDialog\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"label\", null, _toDisplayString(_ctx.t('secret.ssh.knownHosts')), 1 /* TEXT */),\n    _cache[3] || (_cache[3] = _createTextVNode()),\n    _createElementVNode(\"div\", _hoisted_2, _toDisplayString(_ctx.summary), 1 /* TEXT */),\n    _cache[4] || (_cache[4] = _createTextVNode()),\n    (!_ctx.isViewMode)\n      ? (_openBlock(), _createElementBlock(_Fragment, { key: 0 }, [\n          _createElementVNode(\"button\", {\n            ref: \"button\",\n            \"data-testid\": \"input-known-ssh-hosts_open-dialog\",\n            class: \"show-dialog-btn btn\",\n            onClick: _cache[0] || (_cache[0] = \n//@ts-ignore\n(...args) => (_ctx.openDialog && _ctx.openDialog(...args)))\n          }, _cache[1] || (_cache[1] = [\n            _createElementVNode(\"i\", { class: \"icon icon-edit\" }, null, -1 /* CACHED */)\n          ]), 512 /* NEED_PATCH */),\n          _cache[2] || (_cache[2] = _createTextVNode()),\n          _createVNode(_component_KnownHostsEditDialog, {\n            ref: \"editDialog\",\n            value: _ctx.value,\n            mode: _ctx.mode,\n            onClosed: _ctx.dialogClosed\n          }, null, 8 /* PROPS */, [\"value\", \"mode\", \"onClosed\"])\n        ], 64 /* STABLE_FRAGMENT */))\n      : _createCommentVNode(\"v-if\", true)\n  ]))\n}", "<script lang=\"ts\">\nimport { defineComponent } from 'vue';\nimport KnownHostsEditDialog from './KnownHostsEditDialog.vue';\nimport { _EDIT, _VIEW } from '@shell/config/query-params';\n\nexport default defineComponent({\n  name: 'SSHKnownHosts',\n\n  emits: ['update:value'],\n\n  props: {\n    value: {\n      type:     String,\n      required: true\n    },\n\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n  },\n\n  components: { KnownHostsEditDialog },\n\n  computed: {\n    isViewMode() {\n      return this.mode === _VIEW;\n    },\n\n    // The number of entries - exclude empty lines and comments\n    entries() {\n      return this.value.split('\\n').filter((line: string) => !!line.trim().length && !line.startsWith('#')).length;\n    },\n\n    summary() {\n      return this.t('secret.ssh.editKnownHosts.entries', { entries: this.entries });\n    }\n  },\n\n  methods: {\n    openDialog() {\n      (this.$refs.button as HTMLInputElement)?.blur();\n      (this.$refs.editDialog as any).showDialog();\n    },\n\n    dialogClosed(result: any) {\n      if (result.success) {\n        this.$emit('update:value', result.value);\n      }\n    }\n  }\n});\n</script>\n<template>\n  <div\n    class=\"input-known-ssh-hosts labeled-input\"\n    data-testid=\"input-known-ssh-hosts\"\n  >\n    <label>{{ t('secret.ssh.knownHosts') }}</label>\n    <div\n      class=\"hosts-input\"\n      data-testid=\"input-known-ssh-hosts_summary\"\n    >\n      {{ summary }}\n    </div>\n    <template v-if=\"!isViewMode\">\n      <button\n        ref=\"button\"\n        data-testid=\"input-known-ssh-hosts_open-dialog\"\n        class=\"show-dialog-btn btn\"\n        @click=\"openDialog\"\n      >\n        <i class=\"icon icon-edit\" />\n      </button>\n\n      <KnownHostsEditDialog\n        ref=\"editDialog\"\n        :value=\"value\"\n        :mode=\"mode\"\n        @closed=\"dialogClosed\"\n      />\n    </template>\n  </div>\n</template>\n<style lang=\"scss\" scoped>\n  .input-known-ssh-hosts {\n    display: flex;\n    justify-content: space-between;\n\n    .hosts-input {\n      cursor: default;\n      line-height: calc(18px + 1px);\n      padding: 18px 0 0 0;\n    }\n\n    .show-dialog-btn {\n      display: contents;\n      background-color: transparent;\n    }\n  }\n</style>\n"]}]}