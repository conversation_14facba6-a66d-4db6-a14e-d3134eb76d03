{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CruResourceFooter.vue?vue&type=template&id=22ef885c", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CruResourceFooter.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVuZGVyU2xvdCBhcyBfcmVuZGVyU2xvdCwgcmVzb2x2ZUNvbXBvbmVudCBhcyBfcmVzb2x2ZUNvbXBvbmVudCwgY3JlYXRlVk5vZGUgYXMgX2NyZWF0ZVZOb2RlLCBvcGVuQmxvY2sgYXMgX29wZW5CbG9jaywgY3JlYXRlRWxlbWVudEJsb2NrIGFzIF9jcmVhdGVFbGVtZW50QmxvY2ssIGNyZWF0ZUNvbW1lbnRWTm9kZSBhcyBfY3JlYXRlQ29tbWVudFZOb2RlLCBjcmVhdGVCbG9jayBhcyBfY3JlYXRlQmxvY2ssIGNyZWF0ZVRleHRWTm9kZSBhcyBfY3JlYXRlVGV4dFZOb2RlIH0gZnJvbSAidnVlIgoKY29uc3QgX2hvaXN0ZWRfMSA9IHsgY2xhc3M6ICJjcnUtcmVzb3VyY2UtZm9vdGVyIiB9CmNvbnN0IF9ob2lzdGVkXzIgPSBbImRhdGEtdGVzdGlkIl0KCmV4cG9ydCBmdW5jdGlvbiByZW5kZXIoX2N0eCwgX2NhY2hlLCAkcHJvcHMsICRzZXR1cCwgJGRhdGEsICRvcHRpb25zKSB7CiAgY29uc3QgX2NvbXBvbmVudF90ID0gX3Jlc29sdmVDb21wb25lbnQoInQiKQogIGNvbnN0IF9jb21wb25lbnRfQXN5bmNCdXR0b24gPSBfcmVzb2x2ZUNvbXBvbmVudCgiQXN5bmNCdXR0b24iKQogIGNvbnN0IF9jb21wb25lbnRfUmVzb3VyY2VDYW5jZWxNb2RhbCA9IF9yZXNvbHZlQ29tcG9uZW50KCJSZXNvdXJjZUNhbmNlbE1vZGFsIikKCiAgcmV0dXJuIChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soImRpdiIsIF9ob2lzdGVkXzEsIFsKICAgIF9yZW5kZXJTbG90KF9jdHguJHNsb3RzLCAiZm9vdGVyLXByZWZpeCIpLAogICAgX2NhY2hlWzNdIHx8IChfY2FjaGVbM10gPSBfY3JlYXRlVGV4dFZOb2RlKCkpLAogICAgX3JlbmRlclNsb3QoX2N0eC4kc2xvdHMsICJjYW5jZWwiLCB7fSwgKCkgPT4gWwogICAgICAoISRvcHRpb25zLmlzVmlldyAmJiAkcHJvcHMuc2hvd0NhbmNlbCkKICAgICAgICA/IChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soImJ1dHRvbiIsIHsKICAgICAgICAgICAga2V5OiAwLAogICAgICAgICAgICBpZDogImNydS1jYW5jZWwiLAogICAgICAgICAgICAiZGF0YS10ZXN0aWQiOiAkcHJvcHMuY29tcG9uZW50VGVzdGlkICsgJy1jYW5jZWwnLAogICAgICAgICAgICB0eXBlOiAiYnV0dG9uIiwKICAgICAgICAgICAgY2xhc3M6ICJidG4gcm9sZS1zZWNvbmRhcnkiLAogICAgICAgICAgICBvbkNsaWNrOiBfY2FjaGVbMF0gfHwgKF9jYWNoZVswXSA9ICRldmVudCA9PiAoJHByb3BzLmNvbmZpcm1DYW5jZWxSZXF1aXJlZCA/ICRvcHRpb25zLmNoZWNrQ2FuY2VsKHRydWUpIDogX2N0eC4kZW1pdCgnY2FuY2VsLWNvbmZpcm1lZCcsIHRydWUpKSkKICAgICAgICAgIH0sIFsKICAgICAgICAgICAgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfdCwgeyBrOiAiZ2VuZXJpYy5jYW5jZWwiIH0pCiAgICAgICAgICBdLCA4IC8qIFBST1BTICovLCBfaG9pc3RlZF8yKSkKICAgICAgICA6IF9jcmVhdGVDb21tZW50Vk5vZGUoInYtaWYiLCB0cnVlKQogICAgXSksCiAgICBfY2FjaGVbNF0gfHwgKF9jYWNoZVs0XSA9IF9jcmVhdGVUZXh0Vk5vZGUoKSksCiAgICBfcmVuZGVyU2xvdChfY3R4LiRzbG90cywgImRlZmF1bHQiLCB7IGNoZWNrQ2FuY2VsOiAkb3B0aW9ucy5jaGVja0NhbmNlbCB9LCAoKSA9PiBbCiAgICAgICghJG9wdGlvbnMuaXNWaWV3KQogICAgICAgID8gKF9vcGVuQmxvY2soKSwgX2NyZWF0ZUJsb2NrKF9jb21wb25lbnRfQXN5bmNCdXR0b24sIHsKICAgICAgICAgICAga2V5OiAwLAogICAgICAgICAgICAiZGF0YS10ZXN0aWQiOiAkcHJvcHMuY29tcG9uZW50VGVzdGlkICsgJy1jcmVhdGUnLAogICAgICAgICAgICBtb2RlOiAkcHJvcHMuZmluaXNoQnV0dG9uTW9kZSB8fCAkcHJvcHMubW9kZSwKICAgICAgICAgICAgb25DbGljazogX2NhY2hlWzFdIHx8IChfY2FjaGVbMV0gPSAkZXZlbnQgPT4gKF9jdHguJGVtaXQoJ2ZpbmlzaCcsICRldmVudCkpKQogICAgICAgICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgWyJkYXRhLXRlc3RpZCIsICJtb2RlIl0pKQogICAgICAgIDogX2NyZWF0ZUNvbW1lbnRWTm9kZSgidi1pZiIsIHRydWUpCiAgICBdKSwKICAgIF9jYWNoZVs1XSB8fCAoX2NhY2hlWzVdID0gX2NyZWF0ZVRleHRWTm9kZSgpKSwKICAgIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X1Jlc291cmNlQ2FuY2VsTW9kYWwsIHsKICAgICAgcmVmOiAiY2FuY2VsTW9kYWwiLAogICAgICAiaXMtY2FuY2VsLW1vZGFsIjogJGRhdGEuaXNDYW5jZWxNb2RhbCwKICAgICAgImlzLWZvcm0iOiAkcHJvcHMuaXNGb3JtLAogICAgICBvbkNvbmZpcm1DYW5jZWw6IF9jYWNoZVsyXSB8fCAoX2NhY2hlWzJdID0gJGV2ZW50ID0+ICgkb3B0aW9ucy5jb25maXJtQ2FuY2VsKCRldmVudCkpKQogICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgWyJpcy1jYW5jZWwtbW9kYWwiLCAiaXMtZm9ybSJdKQogIF0pKQp9"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/CruResourceFooter.vue"], "names": ["$emit"], "mappings": ";;qBAmFO,KAAK,EAAC,qBAAqB;;;;;;;;wBAAhC,oBA4BM,OA5BN,UA4BM;IA3BJ,YAA6B;;IAC7B,YAWO,2BAXP,CAWO;QATI,eAAM,IAAI,iBAAU;yBAD7B,oBASS;;YAPP,EAAE,EAAC,YAAY;YACd,aAAW,EAAE,sBAAe;YAC7B,IAAI,EAAC,QAAQ;YACb,KAAK,EAAC,oBAAoB;YACzB,OAAK,uCAAE,4BAAqB,GAAG,oBAAW,SAASA,UAAK;;YAEzD,aAAwB,gBAArB,CAAC,EAAC,gBAAgB;;;;;IAGzB,YAOO,0BAPA,WAAW,EAAE,oBAAW,IAA/B,CAOO;QALI,eAAM;yBADf,aAKE;;YAHC,aAAW,EAAE,sBAAe;YAC5B,IAAI,EAAE,uBAAgB,IAAI,WAAI;YAC9B,OAAK,uCAAEA,UAAK,WAAW,MAAM;;;;;IAGlC,aAKE;MAJA,GAAG,EAAC,aAAa;MAChB,iBAAe,EAAE,mBAAa;MAC9B,SAAO,EAAE,aAAM;MACf,eAAc,uCAAE,sBAAa,CAAC,MAAM", "sourcesContent": ["<script>\nimport { mapGetters } from 'vuex';\n\nimport AsyncButton from '@shell/components/AsyncButton';\nimport ResourceCancelModal from '@shell/components/ResourceCancelModal';\nimport { _VIEW } from '@shell/config/query-params';\n\nexport default {\n  emits: ['cancel-confirmed', 'finish'],\n\n  components: { AsyncButton, ResourceCancelModal },\n  props:      {\n    mode: {\n      type:    String,\n      default: 'create',\n    },\n\n    isForm: {\n      type:    Boolean,\n      default: true,\n    },\n\n    // Override the set of labels shown on the button from the default save/create.\n    finishButtonMode: {\n      type:    String,\n      default: null,\n    },\n\n    confirmCancelRequired: {\n      type:    Boolean,\n      default: false,\n    },\n\n    confirmBackRequired: {\n      type:    Boolean,\n      default: true,\n    },\n\n    showCancel: {\n      type:    Boolean,\n      default: true\n    },\n\n    /**\n     * Inherited global identifier prefix for tests\n     * Define a term based on the parent component to avoid conflicts on multiple components\n     */\n    componentTestid: {\n      type:    String,\n      default: 'form-footer'\n    }\n  },\n\n  data() {\n    return { isCancelModal: false };\n  },\n\n  computed: {\n    ...mapGetters({ t: 'i18n/t' }),\n\n    isView() {\n      return this.mode === _VIEW;\n    },\n  },\n\n  methods: {\n    checkCancel(isCancel) {\n      if (isCancel) {\n        this.isCancelModal = true;\n      } else {\n        this.isCancelModal = false;\n      }\n      this.$refs.cancelModal.show();\n    },\n\n    confirmCancel(isCancel) {\n      this.$emit('cancel-confirmed', isCancel);\n    },\n  },\n};\n</script>\n\n<template>\n  <div class=\"cru-resource-footer\">\n    <slot name=\"footer-prefix\" />\n    <slot name=\"cancel\">\n      <button\n        v-if=\"!isView && showCancel\"\n        id=\"cru-cancel\"\n        :data-testid=\"componentTestid + '-cancel'\"\n        type=\"button\"\n        class=\"btn role-secondary\"\n        @click=\"confirmCancelRequired ? checkCancel(true) : $emit('cancel-confirmed', true)\"\n      >\n        <t k=\"generic.cancel\" />\n      </button>\n    </slot>\n    <slot :checkCancel=\"checkCancel\">\n      <AsyncButton\n        v-if=\"!isView\"\n        :data-testid=\"componentTestid + '-create'\"\n        :mode=\"finishButtonMode || mode\"\n        @click=\"$emit('finish', $event)\"\n      />\n    </slot>\n    <ResourceCancelModal\n      ref=\"cancelModal\"\n      :is-cancel-modal=\"isCancelModal\"\n      :is-form=\"isForm\"\n      @confirm-cancel=\"confirmCancel($event)\"\n    />\n  </div>\n</template>\n\n<style lang=\"scss\">\n.cru-resource-footer {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 20px;\n  z-index: z-index('cruFooter');\n\n  .btn {\n    margin-left: 20px;\n  }\n}\n\n</style>\n"]}]}