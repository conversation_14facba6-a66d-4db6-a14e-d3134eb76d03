{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DetailTop.vue?vue&type=template&id=0388315e", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DetailTop.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DetailTop.vue"], "names": ["t"], "mappings": ";;;;EAqLM,KAAK,EAAC,QAAQ;;qBAER,KAAK,EAAC,OAAO;;;EAiBnB,KAAK,EAAC,aAAa;;qBAEb,KAAK,EAAC,OAAO;qBAGb,KAAK,EAAC,SAAS;;qBAcX,KAAK,EAAC,OAAO;;;;EAgBvB,KAAK,EAAC,QAAQ;;sBAET,KAAK,EAAC,MAAM;sBACT,KAAK,EAAC,OAAO;;;;;;EAkCrB,KAAK,EAAC,aAAa;;sBAEb,KAAK,EAAC,OAAO;;;;;;;;;;wBAnGvB,oBA6HM;IA5HJ,KAAK,mBAAC,YAAY,UACF,gBAAO;;KAGf,sBAAa;uBADrB,oBAiBM,OAjBN,UAiBM;UAbJ,oBAEO,QAFP,UAEO,mBADFA,MAAC,2CAA0C,WAChD;;UACA,oBASO;+BARL,oBAOc,6BANQ,mBAAU,GAAvB,SAAS;oCADlB,aAOc;gBALX,GAAG,EAAE,SAAS,CAAC,IAAI;gBACnB,EAAE,EAAE,SAAS,CAAC,cAAc;gBAC7B,KAAK,EAAC,mBAAmB;;kCAEzB,CAAoB;oDAAjB,SAAS,CAAC,IAAI;;;;;;;;;KAMf,oBAAW;uBADnB,oBAQM,OARN,UAQM;UAJJ,oBAEO,QAFP,UAEO,mBADFA,MAAC,4CAA2C,WACjD;;UACA,oBAA8C,QAA9C,UAA8C,mBAArB,oBAAW;;;;KAG3B,mBAAU;uBAArB,oBAuBM;6BAtBJ,oBAqBM,6BApBmB,gBAAO,GAAvB,KAAK,EAAE,KAAK;kCADrB,oBAqBM;cAnBH,GAAG,EAAE,KAAK;cACX,KAAK,EAAC,SAAS;;iCAEf,oBAeM,6BAdkB,KAAK,GAAnB,MAAM,EAAE,CAAC;sCADnB,oBAeM;kBAbH,GAAG,EAAE,CAAC;kBACP,KAAK,EAAC,QAAQ;;kBAEd,oBAEO,QAFP,UAEO,mBADF,MAAM,CAAC,KAAK,IAAG,eACpB;;mBAGQ,MAAM,CAAC,SAAS;qCAFxB,aAKE,yBAJK,MAAM,CAAC,SAAS,GADvB,YAKE;;wBAFC,KAAK,EAAE,MAAM,CAAC,OAAO;4CACd,MAAM,CAAC,aAAa;qCAE9B,oBAAwC,qCAAxB,MAAM,CAAC,OAAO;;;;;;;;KAM5B,kBAAS;uBADjB,oBAmCM,OAnCN,UAmCM;UA/BJ,oBA8BM,OA9BN,WA8BM;YA7BJ,oBAEO,QAFP,WAEO,mBADFA,MAAC,uCAAsC,aAC5C;;+BACA,oBAiBM,6BAhBkB,eAAM,GAApB,IAAI,EAAE,GAAG;oCADnB,aAiBM,kBAfH,GAAG,EAAE,GAAG;;mBAGD,sBAAa,CAAC,GAAG;qCADzB,oBAIE;;wBAFA,KAAK,mBAAC,MAAM,EACJ,sBAAa,CAAC,GAAG;;;;mBAGnB,yBAAgB,CAAC,GAAG;qDAD5B,oBAMO;wBAFL,oBAAsE,+BAA7D,yBAAgB,CAAC,GAAG,IAAI,yBAAgB,CAAC,GAAG,IAAI,GAAG;;yBAChD,mBAAa;2CAAzB,oBAA6C,qBAAlB,IAAE,oBAAG,GAAG;;;mDAHlB,IAAI,MAAM,GAAG,MAAM,IAAI,KAAK,GAAG;;qCAKlD,oBAAyD,sCAAzC,IAAI,MAAM,GAAG,MAAM,IAAI,KAAK,GAAG;;;;;;;aAGzC,iCAAwB;+BADhC,oBAOI;;kBALF,IAAI,EAAC,GAAG;kBACR,KAAK,EAAC,0BAA0B;kBAC/B,OAAK,yDAAU,uDAAY;oCAEzBA,MAAC,6BAA6B,mBAAa;;;;;;KAM5C,uBAAc;uBADtB,oBAsBM,OAtBN,WAsBM;UAlBJ,oBAEO,QAFP,WAEO,mBADFA,MAAC,4CAA2C,WACjD;;UACA,oBAKI;YAJF,IAAI,EAAC,GAAG;YACP,OAAK,yDAAU,iEAAiB;8BAE9BA,MAAC,6BAA6B,wBAAkB,yDAAyD,wBAAe;;WAElH,wBAAkB;6BAA7B,oBAQM;mCAPJ,oBAME,6BALqB,oBAAW,GAAxB,GAAG,EAAE,GAAG;wCADlB,aAME;oBAJC,GAAG,EAAE,GAAG;oBACT,KAAK,EAAC,YAAY;oBACjB,KAAK,EAAE,GAAG;oBACV,KAAK,EAAE,GAAG;;;;;;;;IAKjB,wCAAwB;;IACxB,aAIE;MAHC,QAAQ,EAAE,YAAK;MACf,IAAI,EAAE,mBAAa;MACnB,QAAQ,EAAE,uBAAiB", "sourcesContent": ["<script>\nimport Tag from '@shell/components/Tag';\nimport isEmpty from 'lodash/isEmpty';\nimport DetailText from '@shell/components/DetailText';\nimport { _VIEW } from '@shell/config/query-params';\nimport { ExtensionPoint, PanelLocation } from '@shell/core/types';\nimport ExtensionPanel from '@shell/components/ExtensionPanel';\n\nexport default {\n  components: {\n    DetailText, Tag, ExtensionPanel\n  },\n\n  props: {\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n\n    moreDetails: {\n      type:    Array,\n      default: () => {\n        return [];\n      }\n    },\n\n    /**\n     * Optionally replace key/value and display tooltips for the tab\n     * Dictionary key based\n     */\n    tooltips: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n\n    /**\n     * Optionally display icons next to the tab\n     * Dictionary key based\n     */\n    icons: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  data() {\n    return {\n      extensionType:      ExtensionPoint.PANEL,\n      extensionLocation:  PanelLocation.DETAIL_TOP,\n      annotationsVisible: false,\n      showAllLabels:      false,\n      view:               _VIEW\n    };\n  },\n\n  computed: {\n    namespaces() {\n      return (this.value?.namespaces || []).map((namespace) => {\n        return {\n          name:           namespace?.metadata?.name,\n          detailLocation: namespace.detailLocation\n        };\n      });\n    },\n    details() {\n      const items = [\n        ...(this.moreDetails || []),\n        ...(this.value?.details || []),\n      ].filter((x) => x.separator || (!!`${ x.content }` && x.content !== undefined && x.content !== null));\n\n      const groups = [];\n      let currentGroup = [];\n\n      items.forEach((i) => {\n        if (i.separator) {\n          groups.push(currentGroup);\n          currentGroup = [];\n        } else {\n          currentGroup.push(i);\n        }\n      });\n\n      if (currentGroup.length) {\n        groups.push(currentGroup);\n      }\n\n      return groups;\n    },\n\n    labels() {\n      if (this.showAllLabels || !this.showFilteredSystemLabels) {\n        return this.value?.labels || {};\n      }\n\n      return this.value?.filteredSystemLabels;\n    },\n\n    internalTooltips() {\n      return this.value?.detailTopTooltips || this.tooltips;\n    },\n\n    internalIcons() {\n      return this.value?.detailTopIcons || this.icons;\n    },\n\n    annotations() {\n      return this.value?.annotations || {};\n    },\n\n    description() {\n      return this.value?.description;\n    },\n\n    hasDetails() {\n      return !isEmpty(this.details);\n    },\n\n    hasLabels() {\n      return !isEmpty(this.labels);\n    },\n\n    hasAnnotations() {\n      return !isEmpty(this.annotations);\n    },\n\n    hasDescription() {\n      return !isEmpty(this.description);\n    },\n\n    hasNamespaces() {\n      return !isEmpty(this.namespaces);\n    },\n\n    annotationCount() {\n      return Object.keys(this.annotations || {}).length;\n    },\n\n    isEmpty() {\n      const hasAnything = this.hasDetails || this.hasLabels || this.hasAnnotations || this.hasDescription || this.hasNamespaces;\n\n      return !hasAnything;\n    },\n\n    showFilteredSystemLabels() {\n      // It would be nicer to use hasSystemLabels here, but not all places have implemented it\n      // Instead check that there's a discrepancy between all labels and all labels without system ones\n      if (this.value?.labels && this.value?.filteredSystemLabels) {\n        const labelCount = Object.keys(this.value.labels).length;\n        const filteredSystemLabelsCount = Object.keys(this.value.filteredSystemLabels).length;\n\n        return labelCount !== filteredSystemLabelsCount;\n      }\n\n      return false;\n    },\n  },\n  methods: {\n    toggleLabels() {\n      this.showAllLabels = !this.showAllLabels;\n    },\n\n    toggleAnnotations(ev) {\n      this.annotationsVisible = !this.annotationsVisible;\n    }\n  }\n};\n</script>\n\n<template>\n  <div\n    class=\"detail-top\"\n    :class=\"{empty: isEmpty}\"\n  >\n    <div\n      v-if=\"hasNamespaces\"\n      class=\"labels\"\n    >\n      <span class=\"label\">\n        {{ t('resourceDetail.detailTop.namespaces') }}:\n      </span>\n      <span>\n        <router-link\n          v-for=\"namespace in namespaces\"\n          :key=\"namespace.name\"\n          :to=\"namespace.detailLocation\"\n          class=\"namespaceLinkList\"\n        >\n          {{ namespace.name }}\n        </router-link>\n      </span>\n    </div>\n\n    <div\n      v-if=\"description\"\n      class=\"description\"\n    >\n      <span class=\"label\">\n        {{ t('resourceDetail.detailTop.description') }}:\n      </span>\n      <span class=\"content\">{{ description }}</span>\n    </div>\n\n    <div v-if=\"hasDetails\">\n      <div\n        v-for=\"group, index in details\"\n        :key=\"index\"\n        class=\"details\"\n      >\n        <div\n          v-for=\"(detail, i) in group\"\n          :key=\"i\"\n          class=\"detail\"\n        >\n          <span class=\"label\">\n            {{ detail.label }}:\n          </span>\n          <component\n            :is=\"detail.formatter\"\n            v-if=\"detail.formatter\"\n            :value=\"detail.content\"\n            v-bind=\"detail.formatterOpts\"\n          />\n          <span v-else>{{ detail.content }}</span>\n        </div>\n      </div>\n    </div>\n\n    <div\n      v-if=\"hasLabels\"\n      class=\"labels\"\n    >\n      <div class=\"tags\">\n        <span class=\"label\">\n          {{ t('resourceDetail.detailTop.labels') }}:\n        </span>\n        <Tag\n          v-for=\"(prop, key) in labels\"\n          :key=\"key\"\n        >\n          <i\n            v-if=\"internalIcons[key]\"\n            class=\"icon\"\n            :class=\"internalIcons[key]\"\n          />\n          <span\n            v-if=\"internalTooltips[key]\"\n            v-clean-tooltip=\"prop ? `${key} : ${prop}` : key\"\n          >\n            <span>{{ internalTooltips[key] ? internalTooltips[key] : key }}</span>\n            <span v-if=\"showAllLabels\">: {{ key }}</span>\n          </span>\n          <span v-else>{{ prop ? `${key} : ${prop}` : key }}</span>\n        </Tag>\n        <a\n          v-if=\"showFilteredSystemLabels\"\n          href=\"#\"\n          class=\"detail-top__label-button\"\n          @click.prevent=\"toggleLabels\"\n        >\n          {{ t(`resourceDetail.detailTop.${showAllLabels? 'hideLabels' : 'showLabels'}`) }}\n        </a>\n      </div>\n    </div>\n\n    <div\n      v-if=\"hasAnnotations\"\n      class=\"annotations\"\n    >\n      <span class=\"label\">\n        {{ t('resourceDetail.detailTop.annotations') }}:\n      </span>\n      <a\n        href=\"#\"\n        @click.prevent=\"toggleAnnotations\"\n      >\n        {{ t(`resourceDetail.detailTop.${annotationsVisible? 'hideAnnotations' : 'showAnnotations'}`, {annotations: annotationCount}) }}\n      </a>\n      <div v-if=\"annotationsVisible\">\n        <DetailText\n          v-for=\"(val, key) in annotations\"\n          :key=\"key\"\n          class=\"annotation\"\n          :value=\"val\"\n          :label=\"key\"\n        />\n      </div>\n    </div>\n\n    <!-- Extensions area -->\n    <ExtensionPanel\n      :resource=\"value\"\n      :type=\"extensionType\"\n      :location=\"extensionLocation\"\n    />\n  </div>\n</template>\n\n<style lang=\"scss\">\n  .detail-top {\n    $spacing: 4px;\n\n    &:not(.empty) {\n      // Flip of .masthead padding/margin\n      padding-top: 10px;\n      border-top: 1px solid var(--border);\n      margin-top: 10px;\n    }\n\n    .namespaceLinkList:not(:first-child):before {\n      content: \", \";\n    }\n\n    .tags {\n      display: inline-flex;\n      flex-direction: row;\n      flex-wrap: wrap;\n      position: relative;\n      top: $spacing * math.div(-1, 2);\n\n      .label {\n        position: relative;\n        top: $spacing;\n      }\n\n      .tag {\n        margin: math.div($spacing, 2) $spacing 0 math.div($spacing, 2);\n        font-size: 12px;\n      }\n    }\n\n    .annotation {\n      margin-top: 10px;\n    }\n\n    .label {\n      color: var(--input-label);\n      margin: 0 4px 0 0;\n    }\n\n    &__label-button {\n      padding: 4px;\n    }\n\n    .details {\n      display: flex;\n      flex-direction: row;\n      flex-wrap: wrap;\n\n      .detail {\n        margin-right: 20px;\n        margin-bottom: 3px;\n      }\n      &:not(:first-of-type) {\n        margin-top: 3px;\n      }\n    }\n\n    & > div {\n      &:not(:last-of-type) {\n        margin-bottom: $spacing;\n      }\n    }\n\n    .icon {\n      vertical-align: top;\n    }\n  }\n</style>\n"]}]}