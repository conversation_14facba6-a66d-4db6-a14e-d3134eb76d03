{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Dialog.vue?vue&type=template&id=530e148b&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Dialog.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Dialog.vue"], "names": ["$slots", "t"], "mappings": ";;qBAoFS,KAAK,EAAC,cAAc;qBAKlB,KAAK,EAAC,sBAAsB;;;;;;;WAf5B,YAAM;qBADf,aAyCY;;QAvCT,IAAI,EAAE,WAAI;QACX,MAAM,EAAC,MAAM;QACZ,UAAU,EAAE,IAAI;QAChB,oBAAkB,EAAE,IAAI;QACxB,uBAAqB,EAAE,0BAAmB;QAC1C,2CAAyC,EAAE,2CAAoC;QAC/E,OAAK,uCAAE,oBAAW;QAClB,YAAW,EAAE,mBAAU;;0BAExB,CA6BM;UA7BN,oBA6BM,OA7BN,UA6BM;YA5BJ,oBAEK,6BADA,YAAK;;YAEV,YAAQ;;YACR,oBAuBM,OAvBN,UAuBM;cAtBJ,YAAuB;;gBACXA,WAAM,CAAC,OAAO;iCAA1B,oBAoBM;oBAnBJ,oBAKS;sBAJP,KAAK,EAAC,oBAAoB;sBACzB,OAAK,uCAAE,oBAAW;wCAEhBC,MAAC;;sBAGG,WAAI;uCADb,oBAMS;;0BAJP,KAAK,EAAC,wBAAwB;0BAC7B,OAAK,uCAAE,oBAAW;4CAEhBA,MAAC;uCAEN,aAKE;;0BAHC,IAAI,EAAE,WAAI;0BACX,KAAK,EAAC,OAAO;0BACZ,OAAK,EAAE,WAAE", "sourcesContent": ["<script>\nimport AsyncButton from '@shell/components/AsyncButton';\nimport AppModal, { DEFAULT_ITERABLE_NODE_SELECTOR } from '@shell/components/AppModal.vue';\n\nexport default {\n  emits: ['okay', 'closed'],\n\n  components: { AsyncButton, AppModal },\n\n  props: {\n    name: {\n      type:     String,\n      required: true,\n    },\n\n    title: {\n      type:     String,\n      required: true,\n    },\n\n    mode: {\n      type:    String,\n      default: '',\n    },\n\n    /**\n     * forcefully set return focus element based on this selector\n     */\n    returnFocusSelector: {\n      type:    String,\n      default: '',\n    },\n\n    /**\n     * will return focus to the first iterable node of this container select\n     */\n    returnFocusFirstIterableNodeSelector: {\n      type:    String,\n      default: DEFAULT_ITERABLE_NODE_SELECTOR,\n    }\n  },\n\n  data() {\n    return { closed: false };\n  },\n\n  methods: {\n    beforeOpen() {\n      this.closed = false;\n    },\n\n    ok(btnCb) {\n      const callback = (ok) => {\n        btnCb(ok);\n        if (ok) {\n          this.closeDialog(true);\n        }\n      };\n\n      this.$emit('okay', callback);\n    },\n\n    closeDialog(result) {\n      if (!this.closed) {\n        this.$emit('closed', result);\n        this.closed = true;\n      }\n    },\n  }\n};\n</script>\n\n<template>\n  <app-modal\n    v-if=\"!closed\"\n    :name=\"name\"\n    height=\"auto\"\n    :scrollable=\"true\"\n    :trigger-focus-trap=\"true\"\n    :return-focus-selector=\"returnFocusSelector\"\n    :return-focus-first-iterable-node-selector=\"returnFocusFirstIterableNodeSelector\"\n    @close=\"closeDialog(false)\"\n    @before-open=\"beforeOpen\"\n  >\n    <div class=\"modal-dialog\">\n      <h4>\n        {{ title }}\n      </h4>\n      <slot />\n      <div class=\"dialog-buttons mt-20\">\n        <slot name=\"buttons\" />\n        <div v-if=\"!$slots.buttons\">\n          <button\n            class=\"btn role-secondary\"\n            @click=\"closeDialog(false)\"\n          >\n            {{ t('generic.cancel') }}\n          </button>\n          <button\n            v-if=\"!mode\"\n            class=\"btn role-primary ml-10\"\n            @click=\"closeDialog(true)\"\n          >\n            {{ t('generic.ok') }}\n          </button>\n          <AsyncButton\n            v-else\n            :mode=\"mode\"\n            class=\"ml-10\"\n            @click=\"ok\"\n          />\n        </div>\n      </div>\n    </div>\n  </app-modal>\n</template>\n\n<style lang=\"scss\" scoped>\n  .modal-dialog {\n    padding: 10px;\n\n    h4 {\n      font-weight: bold;\n    }\n\n    .dialog-buttons {\n      display: flex;\n      justify-content: flex-end;\n      margin-top: 10px;\n\n      > *:not(:last-child) {\n        margin-right: 10px;\n      }\n    }\n  }\n</style>\n"]}]}