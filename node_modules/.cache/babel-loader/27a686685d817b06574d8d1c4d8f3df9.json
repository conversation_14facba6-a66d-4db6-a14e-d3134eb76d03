{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ExtensionPanel.vue?vue&type=template&id=0f963a70", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ExtensionPanel.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVuZGVyTGlzdCBhcyBfcmVuZGVyTGlzdCwgRnJhZ21lbnQgYXMgX0ZyYWdtZW50LCBvcGVuQmxvY2sgYXMgX29wZW5CbG9jaywgY3JlYXRlRWxlbWVudEJsb2NrIGFzIF9jcmVhdGVFbGVtZW50QmxvY2ssIHJlc29sdmVEeW5hbWljQ29tcG9uZW50IGFzIF9yZXNvbHZlRHluYW1pY0NvbXBvbmVudCwgY3JlYXRlQmxvY2sgYXMgX2NyZWF0ZUJsb2NrLCBjcmVhdGVDb21tZW50Vk5vZGUgYXMgX2NyZWF0ZUNvbW1lbnRWTm9kZSB9IGZyb20gInZ1ZSIKCmNvbnN0IF9ob2lzdGVkXzEgPSB7IGtleTogMCB9CgpleHBvcnQgZnVuY3Rpb24gcmVuZGVyKF9jdHgsIF9jYWNoZSwgJHByb3BzLCAkc2V0dXAsICRkYXRhLCAkb3B0aW9ucykgewogIHJldHVybiAoJGRhdGEuZXh0ZW5zaW9uRGF0YS5sZW5ndGgpCiAgICA/IChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soImRpdiIsIF9ob2lzdGVkXzEsIFsKICAgICAgICAoX29wZW5CbG9jayh0cnVlKSwgX2NyZWF0ZUVsZW1lbnRCbG9jayhfRnJhZ21lbnQsIG51bGwsIF9yZW5kZXJMaXN0KCRkYXRhLmV4dGVuc2lvbkRhdGEsIChpdGVtLCBpKSA9PiB7CiAgICAgICAgICByZXR1cm4gKF9vcGVuQmxvY2soKSwgX2NyZWF0ZUVsZW1lbnRCbG9jaygiZGl2IiwgewogICAgICAgICAgICBrZXk6IGBleHRlbnNpb25EYXRhJHskcHJvcHMubG9jYXRpb259JHtpfWAKICAgICAgICAgIH0sIFsKICAgICAgICAgICAgKF9vcGVuQmxvY2soKSwgX2NyZWF0ZUJsb2NrKF9yZXNvbHZlRHluYW1pY0NvbXBvbmVudChpdGVtLmNvbXBvbmVudCksIHsgcmVzb3VyY2U6ICRwcm9wcy5yZXNvdXJjZSB9LCBudWxsLCA4IC8qIFBST1BTICovLCBbInJlc291cmNlIl0pKQogICAgICAgICAgXSkpCiAgICAgICAgfSksIDEyOCAvKiBLRVlFRF9GUkFHTUVOVCAqLykpCiAgICAgIF0pKQogICAgOiBfY3JlYXRlQ29tbWVudFZOb2RlKCJ2LWlmIiwgdHJ1ZSkKfQ=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ExtensionPanel.vue"], "names": [], "mappings": ";;;;;UA6BU,mBAAa,CAAC,MAAM;qBAD5B,oBAYM;2BATJ,oBAQM,6BAPc,mBAAa,GAAxB,IAAI,EAAE,CAAC;gCADhB,oBAQM;YANH,GAAG,kBAAkB,eAAQ,GAAG,CAAC;;2BAElC,aAGE,yBAFK,IAAI,CAAC,SAAS,KAClB,QAAQ,EAAE,eAAQ", "sourcesContent": ["<script>\nimport { getApplicableExtensionEnhancements } from '@shell/core/plugin-helpers';\n\nexport default {\n  name:  'ExtensionPanel',\n  props: {\n    resource: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    type: {\n      type:    String,\n      default: ''\n    },\n    location: {\n      type:    String,\n      default: ''\n    },\n  },\n  data() {\n    return { extensionData: getApplicableExtensionEnhancements(this, this.type, this.location, this.$route) };\n  },\n};\n</script>\n\n<template>\n  <div\n    v-if=\"extensionData.length\"\n  >\n    <div\n      v-for=\"item, i in extensionData\"\n      :key=\"`extensionData${location}${i}`\"\n    >\n      <component\n        :is=\"item.component\"\n        :resource=\"resource\"\n      />\n    </div>\n  </div>\n</template>\n"]}]}