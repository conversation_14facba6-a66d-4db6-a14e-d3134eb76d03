{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Circle.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Circle.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Circle.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;;AAEV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1B,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC5B,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;EACrB,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACZ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MACzC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;;MAE5C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC9B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5D,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACvE,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1F,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;IAC7C,CAAC;EACH;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Circle.vue", "sourceRoot": "", "sourcesContent": ["<script>\nlet id = 0;\n\nexport default {\n  props: {\n    percentage: {\n      type:    Number,\n      default: 0.75\n    },\n    strokeWidth: {\n      type:    Number,\n      default: 22\n    },\n    primaryStrokeColor: {\n      type:     String,\n      required: true\n    },\n    primaryStrokeGradientColor: {\n      type:    String,\n      default: null\n    },\n    secondaryStrokeColor: {\n      type:     String,\n      required: true\n    },\n    secondaryStrokeGradientColor: {\n      type:    String,\n      default: null\n    },\n    rotate: {\n      type:    Number,\n      default: 90\n    },\n    showText: {\n      type:    Boolean,\n      default: false\n    },\n  },\n  data() {\n    return { id: id++ };\n  },\n  computed: {\n    viewportSize() {\n      return 100;\n    },\n    radius() {\n      const outerRadius = this.viewportSize / 2;\n      const halfStrokeWidth = this.strokeWidth / 2;\n\n      return outerRadius - halfStrokeWidth;\n    },\n    center() {\n      return this.viewportSize / 2;\n    },\n    viewBox() {\n      return `0 0 ${ this.viewportSize } ${ this.viewportSize }`;\n    },\n    circumference() {\n      return 2 * Math.PI * this.radius;\n    },\n    transform() {\n      return `rotate(${ this.rotate }, ${ this.center }, ${ this.center })`;\n    },\n    strokeDasharray() {\n      // This needs to be the circumference of the circle in order to allow the path to be filled\n      return this.circumference;\n    },\n    strokeDashoffset() {\n      // This needs to be the percentage of the circumference that we won't show as it will hide that portion of the path\n      return this.circumference * (1 - this.percentage);\n    },\n    primaryStrokeColorId() {\n      return `primary-${ id }`;\n    },\n    secondaryStrokeColorId() {\n      return `secondary-${ id }`;\n    },\n    parsePercentage() {\n      return parseInt(this.percentage * 100) || 0;\n    },\n  }\n};\n\n</script>\n\n<template>\n  <svg\n    class=\"circle\"\n    width=\"100%\"\n    height=\"100%\"\n    :viewBox=\"viewBox\"\n  >\n    <g :transform=\"transform\">\n      <defs>\n        <linearGradient\n          :id=\"primaryStrokeColorId\"\n          x1=\"0%\"\n          y1=\"0%\"\n          x2=\"100%\"\n          y2=\"0%\"\n        >\n          <stop\n            offset=\"50%\"\n            :stop-color=\"primaryStrokeGradientColor || primaryStrokeColor\"\n          />\n          <stop\n            offset=\"100%\"\n            :stop-color=\"primaryStrokeColor\"\n          />\n        </linearGradient>\n        <linearGradient\n          :id=\"secondaryStrokeColorId\"\n          x1=\"0%\"\n          y1=\"0%\"\n          x2=\"100%\"\n          y2=\"0%\"\n        >\n          <stop\n            offset=\"50%\"\n            :stop-color=\"secondaryStrokeGradientColor || secondaryStrokeColor\"\n          />\n          <stop\n            offset=\"100%\"\n            :stop-color=\"secondaryStrokeColor\"\n          />\n        </linearGradient>\n      </defs>\n      <circle\n        :r=\"radius\"\n        :cy=\"center\"\n        :cx=\"center\"\n        :stroke-width=\"strokeWidth\"\n        :stroke=\"`url(#${secondaryStrokeColorId})`\"\n        fill=\"none\"\n      />\n      <circle\n        :r=\"radius\"\n        :cy=\"center\"\n        :cx=\"center\"\n        :stroke-width=\"strokeWidth\"\n        :stroke=\"`url(#${primaryStrokeColorId})`\"\n        :stroke-dasharray=\"circumference\"\n        :stroke-dashoffset=\"circumference * (1 - percentage)\"\n        fill=\"none\"\n      />\n    </g>\n\n    <text\n      v-if=\"showText\"\n      :x=\"center\"\n      :y=\"center\"\n      style=\"font-size: 25; dominant-baseline:  middle; text-anchor:middle;\"\n      :fill=\"`url(#${primaryStrokeColorId})`\"\n    >\n      {{ parsePercentage }}%\n    </text>\n  </svg>\n</template>\n\n<style lang=\"scss\" scoped>\nsvg.text {\n  fill: red\n}\n</style>\n"]}]}