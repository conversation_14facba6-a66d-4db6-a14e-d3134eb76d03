{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??clonedRuleSet-44.use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/composables/useLabeledFormElement.ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/composables/useLabeledFormElement.ts", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVmLCBjb21wdXRlZCwgZGVmaW5lRW1pdHMgfSBmcm9tICd2dWUnOwppbXBvcnQgeyBfVklFVywgX0VESVQgfSBmcm9tICdAc2hlbGwvY29uZmlnL3F1ZXJ5LXBhcmFtcyc7CmV4cG9ydCBjb25zdCBsYWJlbGVkRm9ybUVsZW1lbnRQcm9wcyA9IHsKICAgIHRvb2x0aXBLZXk6IHsKICAgICAgICB0eXBlOiBTdHJpbmcsCiAgICAgICAgZGVmYXVsdDogbnVsbAogICAgfSwKICAgIHBsYWNlaG9sZGVyOiB7CiAgICAgICAgdHlwZTogW1N0cmluZywgTnVtYmVyXSwKICAgICAgICBkZWZhdWx0OiAnJwogICAgfSwKICAgIHBsYWNlaG9sZGVyS2V5OiB7CiAgICAgICAgdHlwZTogU3RyaW5nLAogICAgICAgIGRlZmF1bHQ6IG51bGwKICAgIH0sCiAgICBsYWJlbDogewogICAgICAgIHR5cGU6IFN0cmluZywKICAgICAgICBkZWZhdWx0OiBudWxsCiAgICB9LAogICAgbGFiZWxLZXk6IHsKICAgICAgICB0eXBlOiBTdHJpbmcsCiAgICAgICAgZGVmYXVsdDogbnVsbAogICAgfSwKICAgIHZhbHVlOiB7CiAgICAgICAgdHlwZTogW1N0cmluZywgTnVtYmVyLCBPYmplY3RdLAogICAgICAgIGRlZmF1bHQ6ICcnCiAgICB9LAogICAgbW9kZTogewogICAgICAgIHR5cGU6IFN0cmluZywKICAgICAgICBkZWZhdWx0OiBfRURJVCwKICAgIH0sCiAgICBydWxlczogewogICAgICAgIGRlZmF1bHQ6ICgpID0+IFtdLAogICAgICAgIHR5cGU6IEFycmF5LAogICAgICAgIC8vIHdlIG9ubHkgd2FudCBmdW5jdGlvbnMgaW4gdGhlIHJ1bGVzIGFycmF5CiAgICAgICAgdmFsaWRhdG9yOiAocnVsZXMpID0+IHJ1bGVzLmV2ZXJ5KChydWxlKSA9PiBbJ2Z1bmN0aW9uJ10uaW5jbHVkZXModHlwZW9mIHJ1bGUpKQogICAgfSwKICAgIHJlcXVpcmVkOiB7CiAgICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgICBkZWZhdWx0OiBmYWxzZSwKICAgIH0sCiAgICBkaXNhYmxlZDogewogICAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgICAgZGVmYXVsdDogZmFsc2UsCiAgICB9LAogICAgcmVxdWlyZURpcnR5OiB7CiAgICAgICAgZGVmYXVsdDogdHJ1ZSwKICAgICAgICB0eXBlOiBCb29sZWFuCiAgICB9Cn07CmNvbnN0IGxhYmVsZWRGb3JtRWxlbWVudEVtaXRzID0gZGVmaW5lRW1pdHMoWyd1cGRhdGU6dmFsaWRhdGlvbiddKTsKZXhwb3J0IGNvbnN0IHVzZUxhYmVsZWRGb3JtRWxlbWVudCA9IChwcm9wcywgZW1pdCkgPT4gewogICAgY29uc3QgcmFpc2VkID0gcmVmKHByb3BzLm1vZGUgPT09IF9WSUVXIHx8ICEhYCR7cHJvcHMudmFsdWV9YCk7CiAgICBjb25zdCBmb2N1c2VkID0gcmVmKGZhbHNlKTsKICAgIGNvbnN0IGJsdXJyZWQgPSByZWYobnVsbCk7CiAgICBjb25zdCByZXF1aXJlZEZpZWxkID0gY29tcHV0ZWQoKCkgPT4gewogICAgICAgIHZhciBfYTsKICAgICAgICByZXR1cm4gcHJvcHMucmVxdWlyZWQgfHwgKChfYSA9IHByb3BzLnJ1bGVzKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2Euc29tZSgocnVsZSkgPT4gKHJ1bGUgPT09IG51bGwgfHwgcnVsZSA9PT0gdm9pZCAwID8gdm9pZCAwIDogcnVsZS5uYW1lKSA9PT0gJ3JlcXVpcmVkJykpOwogICAgfSk7CiAgICBjb25zdCBpc1ZpZXcgPSBjb21wdXRlZCgoKSA9PiB7CiAgICAgICAgcmV0dXJuIHByb3BzLm1vZGUgPT09IF9WSUVXOwogICAgfSk7CiAgICBjb25zdCBpc0Rpc2FibGVkID0gY29tcHV0ZWQoKCkgPT4gewogICAgICAgIHJldHVybiBwcm9wcy5kaXNhYmxlZCB8fCBpc1ZpZXcudmFsdWU7CiAgICB9KTsKICAgIGNvbnN0IHZhbGlkYXRpb25NZXNzYWdlID0gY29tcHV0ZWQoKCkgPT4gewogICAgICAgIGNvbnN0IHJlcXVpcmVkUnVsZSA9IHByb3BzLnJ1bGVzLmZpbmQoKHJ1bGUpID0+IChydWxlID09PSBudWxsIHx8IHJ1bGUgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHJ1bGUubmFtZSkgPT09ICdyZXF1aXJlZCcpOwogICAgICAgIGNvbnN0IHJ1bGVNZXNzYWdlcyA9IFtdOwogICAgICAgIGNvbnN0IHZhbHVlID0gcHJvcHMudmFsdWU7CiAgICAgICAgaWYgKHJlcXVpcmVkUnVsZSAmJiBibHVycmVkLnZhbHVlICYmICFmb2N1c2VkLnZhbHVlKSB7CiAgICAgICAgICAgIGNvbnN0IG1lc3NhZ2UgPSByZXF1aXJlZFJ1bGUodmFsdWUpOwogICAgICAgICAgICBpZiAoISFtZXNzYWdlKSB7CiAgICAgICAgICAgICAgICBlbWl0KCd1cGRhdGU6dmFsaWRhdGlvbicsIGZhbHNlKTsKICAgICAgICAgICAgICAgIHJldHVybiBtZXNzYWdlOwogICAgICAgICAgICB9CiAgICAgICAgfQogICAgICAgIGZvciAoY29uc3QgcnVsZSBvZiBwcm9wcy5ydWxlcykgewogICAgICAgICAgICBjb25zdCBtZXNzYWdlID0gcnVsZSh2YWx1ZSk7CiAgICAgICAgICAgIGlmICghIW1lc3NhZ2UgJiYgcnVsZS5uYW1lICE9PSAncmVxdWlyZWQnKSB7CiAgICAgICAgICAgICAgICBydWxlTWVzc2FnZXMucHVzaChtZXNzYWdlKTsKICAgICAgICAgICAgfQogICAgICAgIH0KICAgICAgICBpZiAocnVsZU1lc3NhZ2VzLmxlbmd0aCA+IDAgJiYgKGJsdXJyZWQudmFsdWUgfHwgZm9jdXNlZC52YWx1ZSB8fCAhcHJvcHMucmVxdWlyZURpcnR5KSkgewogICAgICAgICAgICBlbWl0KCd1cGRhdGU6dmFsaWRhdGlvbicsIGZhbHNlKTsKICAgICAgICAgICAgcmV0dXJuIHJ1bGVNZXNzYWdlcy5qb2luKCcsICcpOwogICAgICAgIH0KICAgICAgICBlbHNlIHsKICAgICAgICAgICAgZW1pdCgndXBkYXRlOnZhbGlkYXRpb24nLCB0cnVlKTsKICAgICAgICAgICAgcmV0dXJuIHVuZGVmaW5lZDsKICAgICAgICB9CiAgICB9KTsKICAgIGNvbnN0IG9uRm9jdXNMYWJlbGVkID0gKCkgPT4gewogICAgICAgIHJhaXNlZC52YWx1ZSA9IHRydWU7CiAgICAgICAgZm9jdXNlZC52YWx1ZSA9IHRydWU7CiAgICB9OwogICAgY29uc3Qgb25CbHVyTGFiZWxlZCA9ICgpID0+IHsKICAgICAgICBmb2N1c2VkLnZhbHVlID0gZmFsc2U7CiAgICAgICAgaWYgKCFwcm9wcy52YWx1ZSkgewogICAgICAgICAgICByYWlzZWQudmFsdWUgPSBmYWxzZTsKICAgICAgICB9CiAgICAgICAgYmx1cnJlZC52YWx1ZSA9IERhdGUubm93KCk7CiAgICB9OwogICAgcmV0dXJuIHsKICAgICAgICByYWlzZWQsCiAgICAgICAgZm9jdXNlZCwKICAgICAgICBibHVycmVkLAogICAgICAgIG9uRm9jdXNMYWJlbGVkLAogICAgICAgIG9uQmx1ckxhYmVsZWQsCiAgICAgICAgaXNEaXNhYmxlZCwKICAgICAgICB2YWxpZGF0aW9uTWVzc2FnZSwKICAgICAgICByZXF1aXJlZEZpZWxkCiAgICB9Owp9Owo="}, {"version": 3, "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/composables/useLabeledFormElement.ts", "sourceRoot": "", "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/composables/useLabeledFormElement.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,GAAG,EAAE,QAAQ,EAAoB,WAAW,EAC7C,MAAM,KAAK,CAAC;AACb,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,4BAA4B,CAAC;AAsB1D,MAAM,CAAC,MAAM,uBAAuB,GAAG;IACrC,UAAU,EAAE;QACV,IAAI,EAAK,MAAM;QACf,OAAO,EAAE,IAAI;KACd;IACD,WAAW,EAAE;QACX,IAAI,EAAK,CAAC,MAAM,EAAE,MAAM,CAAC;QACzB,OAAO,EAAE,EAAE;KACZ;IACD,cAAc,EAAE;QACd,IAAI,EAAK,MAAM;QACf,OAAO,EAAE,IAAI;KACd;IACD,KAAK,EAAE;QACL,IAAI,EAAK,MAAM;QACf,OAAO,EAAE,IAAI;KACd;IACD,QAAQ,EAAE;QACR,IAAI,EAAK,MAAM;QACf,OAAO,EAAE,IAAI;KACd;IACD,KAAK,EAAE;QACL,IAAI,EAAK,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;QACjC,OAAO,EAAE,EAAE;KACZ;IACD,IAAI,EAAE;QACJ,IAAI,EAAK,MAAM;QACf,OAAO,EAAE,KAAK;KACf;IACD,KAAK,EAAE;QACL,OAAO,EAAI,GAAmB,EAAE,CAAC,EAAE;QACnC,IAAI,EAAO,KAAK;QAChB,4CAA4C;QAC5C,SAAS,EAAE,CAAC,KAAqB,EAAW,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAa,EAAE,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,CAAC;KAClH;IACD,QAAQ,EAAE;QACR,IAAI,EAAK,OAAO;QAChB,OAAO,EAAE,KAAK;KACf;IACD,QAAQ,EAAE;QACR,IAAI,EAAK,OAAO;QAChB,OAAO,EAAE,KAAK;KACf;IACD,YAAY,EAAE;QACZ,OAAO,EAAE,IAAI;QACb,IAAI,EAAK,OAAO;KACjB;CACF,CAAC;AAEF,MAAM,uBAAuB,GAAG,WAAW,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC;AAEnE,MAAM,CAAC,MAAM,qBAAqB,GAAG,CAAC,KAA8B,EAAE,IAAoC,EAAyB,EAAE;IACnI,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,KAAK,KAAK,IAAI,CAAC,CAAC,GAAI,KAAK,CAAC,KAAM,EAAE,CAAC,CAAC;IACjE,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;IAC3B,MAAM,OAAO,GAAG,GAAG,CAAgB,IAAI,CAAC,CAAC;IAEzC,MAAM,aAAa,GAAG,QAAQ,CAAC,GAAG,EAAE;;QAClC,OAAO,KAAK,CAAC,QAAQ,KAAI,MAAA,KAAK,CAAC,KAAK,0CAAE,IAAI,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,MAAK,UAAU,CAAC,CAAA,CAAC;IACvF,CAAC,CAAC,CAAC;IAEH,MAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,EAAE;QAC3B,OAAO,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,MAAM,UAAU,GAAG,QAAQ,CAAC,GAAG,EAAE;QAC/B,OAAO,KAAK,CAAC,QAAQ,IAAI,MAAM,CAAC,KAAK,CAAC;IACxC,CAAC,CAAC,CAAC;IAEH,MAAM,iBAAiB,GAAG,QAAQ,CAAC,GAAG,EAAE;QACtC,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,MAAK,UAAU,CAAa,CAAC;QAC5F,MAAM,YAAY,GAAG,EAAE,CAAC;QACxB,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QAE1B,IAAI,YAAY,IAAI,OAAO,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACpD,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;YAEpC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;gBACd,IAAI,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;gBAEjC,OAAO,OAAO,CAAC;YACjB,CAAC;QACH,CAAC;QAED,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;YAC/B,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;YAE5B,IAAI,CAAC,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gBAC1C,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC;QAED,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC;YACvF,IAAI,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;YAEjC,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;YAEhC,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,MAAM,cAAc,GAAG,GAAG,EAAE;QAC1B,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;QACpB,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;IACvB,CAAC,CAAC;IAEF,MAAM,aAAa,GAAG,GAAG,EAAE;QACzB,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QAEtB,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;YACjB,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;QACvB,CAAC;QAED,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC7B,CAAC,CAAC;IAEF,OAAO;QACL,MAAM;QACN,OAAO;QACP,OAAO;QACP,cAAc;QACd,aAAa;QACb,UAAU;QACV,iBAAiB;QACjB,aAAa;KACd,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["import {\n  ref, computed, ComputedRef, Ref, defineEmits\n} from 'vue';\nimport { _VIEW, _EDIT } from '@shell/config/query-params';\n\ninterface LabeledFormElementProps {\n  mode: string;\n  value: string | number | Record<string, any>\n  required: boolean;\n  disabled: boolean;\n  rules: Array<any>;\n  requireDirty?: boolean;\n}\n\ninterface UseLabeledFormElement {\n  raised: Ref<boolean>;\n  focused: Ref<boolean>;\n  blurred: Ref<number | null>;\n  requiredField: ComputedRef<any>;\n  isDisabled: ComputedRef<any>;\n  validationMessage: ComputedRef<any>;\n  onFocusLabeled: () => void;\n  onBlurLabeled: () => void;\n}\n\nexport const labeledFormElementProps = {\n  tooltipKey: {\n    type:    String,\n    default: null\n  },\n  placeholder: {\n    type:    [String, Number],\n    default: ''\n  },\n  placeholderKey: {\n    type:    String,\n    default: null\n  },\n  label: {\n    type:    String,\n    default: null\n  },\n  labelKey: {\n    type:    String,\n    default: null\n  },\n  value: {\n    type:    [String, Number, Object],\n    default: ''\n  },\n  mode: {\n    type:    String,\n    default: _EDIT,\n  },\n  rules: {\n    default:   (): Array<unknown> => [],\n    type:      Array,\n    // we only want functions in the rules array\n    validator: (rules: Array<unknown>): boolean => rules.every((rule: unknown) => ['function'].includes(typeof rule))\n  },\n  required: {\n    type:    Boolean,\n    default: false,\n  },\n  disabled: {\n    type:    Boolean,\n    default: false,\n  },\n  requireDirty: {\n    default: true,\n    type:    Boolean\n  }\n};\n\nconst labeledFormElementEmits = defineEmits(['update:validation']);\n\nexport const useLabeledFormElement = (props: LabeledFormElementProps, emit: typeof labeledFormElementEmits): UseLabeledFormElement => {\n  const raised = ref(props.mode === _VIEW || !!`${ props.value }`);\n  const focused = ref(false);\n  const blurred = ref<number | null>(null);\n\n  const requiredField = computed(() => {\n    return props.required || props.rules?.some((rule: any) => rule?.name === 'required');\n  });\n\n  const isView = computed(() => {\n    return props.mode === _VIEW;\n  });\n\n  const isDisabled = computed(() => {\n    return props.disabled || isView.value;\n  });\n\n  const validationMessage = computed(() => {\n    const requiredRule = props.rules.find((rule: any) => rule?.name === 'required') as Function;\n    const ruleMessages = [];\n    const value = props.value;\n\n    if (requiredRule && blurred.value && !focused.value) {\n      const message = requiredRule(value);\n\n      if (!!message) {\n        emit('update:validation', false);\n\n        return message;\n      }\n    }\n\n    for (const rule of props.rules) {\n      const message = rule(value);\n\n      if (!!message && rule.name !== 'required') {\n        ruleMessages.push(message);\n      }\n    }\n\n    if (ruleMessages.length > 0 && (blurred.value || focused.value || !props.requireDirty)) {\n      emit('update:validation', false);\n\n      return ruleMessages.join(', ');\n    } else {\n      emit('update:validation', true);\n\n      return undefined;\n    }\n  });\n\n  const onFocusLabeled = () => {\n    raised.value = true;\n    focused.value = true;\n  };\n\n  const onBlurLabeled = () => {\n    focused.value = false;\n\n    if (!props.value) {\n      raised.value = false;\n    }\n\n    blurred.value = Date.now();\n  };\n\n  return {\n    raised,\n    focused,\n    blurred,\n    onFocusLabeled,\n    onBlurLabeled,\n    isDisabled,\n    validationMessage,\n    requiredField\n  };\n};\n"]}]}