{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??clonedRuleSet-44.use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/AccountAccess.vue?vue&type=template&id=19e09548&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/AccountAccess.vue", "mtime": *************}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": *************}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": *************}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": *************}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": *************}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": *************}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": *************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/AccountAccess.vue?vue&type=template&id=19e09548&ts=true", "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/AccountAccess.vue"], "names": ["credential", "mode", "region", "regionOptions", "$emit", "VIEW", "CREATE"], "mappings": "AAAA,OAAO,EAAE,gBAAgB,IAAI,iBAAiB,EAAE,WAAW,IAAI,YAAY,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,eAAe,IAAI,gBAAgB,EAAE,cAAc,IAAI,eAAe,EAAE,SAAS,IAAI,UAAU,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,MAAM,KAAK,CAAA;AAE/Q,MAAM,UAAU,GAAG,ECsIV,KAAK,EAAC,cAAc,EAAA,CAAA;ADrI7B,MAAM,UAAU,GAAG,ECgJb,KAAK,EAAC,6BAA6B,EAAA,CAAA;AD9IzC,MAAM,UAAU,MAAM,CAAC,IAAS,EAAC,MAAW,EAAC,MAAW,EAAC,MAAW,EAAC,KAAU,EAAC,QAAa;IAC3F,MAAM,wBAAwB,GAAG,iBAAiB,CAAC,eAAe,CAAE,CAAA;IACpE,MAAM,2BAA2B,GAAG,iBAAiB,CAAC,kBAAkB,CAAE,CAAA;IAE1E,OAAO,CAAC,UAAU,EAAE,EC2HpB,mBAAA,CA6BM,KAAA,EAAA;QA5BH,KAAK,EAAA,eAAA,CAAA,CAAA,EAAA,cAAA,EAAA,CAAoBA,IAAAA,CAAAA,UAAU,EAAA,EAC9B,mBAAmB,CAAA,CAAA;KD3H1B,EAAE;QC6HD,mBAAA,CASM,KAAA,EATN,UASM,EAAA;YARJ,YAAA,CAOE,wBAAA,EAAA;gBANC,QAAQ,EAAEC,IAAAA,CAAAA,IAAI,KAAA,QAAA;gBACd,KAAK,EAAEC,IAAAA,CAAAA,MAAM;gBACd,aAAW,EAAC,YAAY;gBACxB,WAAS,EAAC,kBAAkB;gBAC3B,OAAO,EAAEC,IAAAA,CAAAA,aAAa;gBACtB,gBAAY,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEC,IAAAA,CAAAA,KAAK,CAAA,eAAA,EAAkB,MAAM,CAAA,CAAA,CAAA;aD3H7C,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;SAC1D,CAAC;QACF,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;QC4H7C,mBAAA,CAcM,KAAA,EAdN,UAcM,EAAA;YAXJ,YAAA,CAUE,2BAAA,EAAA;gBATC,KAAK,EAAEJ,IAAAA,CAAAA,UAAU;gBAClB,aAAW,EAAC,0BAA0B;gBACrC,IAAI,EAAEC,IAAAA,CAAAA,IAAI,KAAKI,IAAAA,CAAAA,IAAI,CAAA,CAAA,CAAGA,IAAAA,CAAAA,IAAI,CAAA,CAAA,CAAGC,IAAAA,CAAAA,MAAM;gBACpC,QAAQ,EAAC,KAAK;gBACb,mBAAiB,EAAE,IAAI;gBACvB,cAAY,EAAA,CAAGN,IAAAA,CAAAA,UAAU;gBAC1B,KAAK,EAAC,mBAAmB;gBACxB,MAAM,EAAA,GAAA,EAAMI,CAAAA,IAAAA,CAAAA,KAAK,CAAA,mBAAA,CAAA;gBACjB,gBAAY,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAEA,IAAAA,CAAAA,KAAK,CAAA,mBAAA,EAAsB,MAAM,CAAA,CAAA,CAAA;aD5HjD,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,QAAQ,CAAC,CAAC;SACrE,CAAC;KACH,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAA;AACpB,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/AccountAccess.vue.tsx", "sourceRoot": "", "sourcesContent": ["import { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, normalizeClass as _normalizeClass, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"region mb-10\" }\nconst _hoisted_2 = { class: \"select-credential-container\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_LabeledSelect = _resolveComponent(\"LabeledSelect\")!\n  const _component_SelectCredential = _resolveComponent(\"SelectCredential\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", {\n    class: _normalizeClass([{'showing-form': !_ctx.credential}, \"credential-region\"])\n  }, [\n    _createElementVNode(\"div\", _hoisted_1, [\n      _createVNode(_component_LabeledSelect, {\n        disabled: _ctx.mode!=='create',\n        value: _ctx.region,\n        \"data-testid\": \"eks_region\",\n        \"label-key\": \"eks.region.label\",\n        options: _ctx.regionOptions,\n        \"onUpdate:value\": _cache[0] || (_cache[0] = ($event: any) => (_ctx.$emit('update-region', $event)))\n      }, null, 8 /* PROPS */, [\"disabled\", \"value\", \"options\"])\n    ]),\n    _cache[2] || (_cache[2] = _createTextVNode()),\n    _createElementVNode(\"div\", _hoisted_2, [\n      _createVNode(_component_SelectCredential, {\n        value: _ctx.credential,\n        \"data-testid\": \"crueks-select-credential\",\n        mode: _ctx.mode === _ctx.VIEW ? _ctx.VIEW : _ctx.CREATE,\n        provider: \"aws\",\n        \"default-on-cancel\": true,\n        \"showing-form\": !_ctx.credential,\n        class: \"select-credential\",\n        cancel: ()=>_ctx.$emit('cancel-credential'),\n        \"onUpdate:value\": _cache[1] || (_cache[1] = ($event: any) => (_ctx.$emit('update-credential', $event)))\n      }, null, 8 /* PROPS */, [\"value\", \"mode\", \"showing-form\", \"cancel\"])\n    ])\n  ], 2 /* CLASS */))\n}", "<script lang=\"ts\">\nimport { defineComponent } from 'vue';\nimport { _CREATE, _VIEW } from '@shell/config/query-params';\nimport LabeledSelect from '@shell/components/form/LabeledSelect.vue';\nimport SelectCredential from '@shell/edit/provisioning.cattle.io.cluster/SelectCredential.vue';\nimport { DEFAULT_REGION } from './CruEKS.vue';\nimport { mapGetters } from 'vuex';\nimport { AWS } from '../../types';\nimport { NORMAN } from '@shell/config/types';\n\n\nexport default defineComponent({\n  name: 'EKSAccountAccess',\n\n  emits: ['update-region', 'error', 'cancel-credential', 'update-credential'],\n\n  components: {\n    LabeledSelect,\n    SelectCredential\n  },\n\n  props: {\n    mode: {\n      type:    String,\n      default: _CREATE\n    },\n\n    credential: {\n      type:    String,\n      default: null\n    },\n\n    region: {\n      type:    String,\n      default: ''\n    }\n  },\n\n  async fetch() {\n    if (this.mode !== _VIEW) {\n      this.defaultRegions = await this.$store.dispatch('aws/defaultRegions');\n      if (this.defaultRegions.length && !this.region) {\n        this.$emit('update-region', DEFAULT_REGION);\n      }\n    }\n  },\n\n  data() {\n    return { regions: [] as string[], defaultRegions: [] as string[] };\n  },\n\n  watch: {\n    isAuthenticated: {\n      async handler(neu) {\n        if (neu && this.mode !== _VIEW) {\n          // Auto-default to credential's region only once on initialization if no region is set\n          const credentialDefaultRegion = await this.getCredentialDefaultRegion();\n          if (credentialDefaultRegion) {\n            this.$emit('update-region', credentialDefaultRegion);\n          }\n          await this.fetchRegions();\n        } else {\n          if (this.defaultRegions.length && !this.defaultRegions.includes(this.region)) {\n            if (this.defaultRegions.includes(DEFAULT_REGION)) {\n              this.$emit('update-region', DEFAULT_REGION);\n            } else {\n              this.$emit('update-region', this.defaultRegions[0]);\n            }\n          }\n        }\n      },\n      immediate: true\n    }\n  },\n\n  methods: {\n    async fetchRegions() {\n      const { region, credential }: { region: string, credential: string} = this;\n\n      if (!!region && !!credential) {\n        try {\n          const ec2Client = await this.$store.dispatch('aws/ec2', { region, cloudCredentialId: credential });\n\n          const res: {Regions: AWS.EC2Region[]} = await ec2Client.describeRegions({});\n\n          this.regions = (res?.Regions || []).map((r) => r.RegionName);\n        } catch (err) {\n          this.$emit('error', this.t('eks.errors.fetchingRegions', { err }));\n        }\n      }\n    },\n    \n    async getCredentialDefaultRegion(): Promise<string|null> {\n      if (!this.credential) return null;\n            // Use the correct store and schema type consistent with rest of codebase\n      return this.$store.dispatch('rancher/find', {\n        type: NORMAN.CLOUD_CREDENTIAL,\n        id: this.credential\n      }).then(credentialResource => {\n        return credentialResource?.decodedData?.defaultRegion || null;\n      }).catch(err => {\n        console.warn('Failed to fetch credential default region:', err);\n        return null;\n      });\n    },\n  },\n\n  computed: {\n    ...mapGetters({ t: 'i18n/t' }),\n\n    // once the credential is validated we can fetch a list of available regions\n    isAuthenticated(): boolean {\n      return !!this.credential;\n    },\n\n    regionOptions(): string[] {\n      return this.regions.length ? this.regions : this.defaultRegions;\n    },\n\n    CREATE(): string {\n      return _CREATE;\n    },\n\n    VIEW(): string {\n      return _VIEW;\n    },\n\n  },\n});\n</script>\n\n<template>\n  <div\n    :class=\"{'showing-form': !credential}\"\n    class=\"credential-region\"\n  >\n    <div class=\"region mb-10\">\n      <LabeledSelect\n        :disabled=\"mode!=='create'\"\n        :value=\"region\"\n        data-testid=\"eks_region\"\n        label-key=\"eks.region.label\"\n        :options=\"regionOptions\"\n        @update:value=\"$emit('update-region', $event)\"\n      />\n    </div>\n    <div\n      class=\"select-credential-container\"\n    >\n      <SelectCredential\n        :value=\"credential\"\n        data-testid=\"crueks-select-credential\"\n        :mode=\"mode === VIEW ? VIEW : CREATE\"\n        provider=\"aws\"\n        :default-on-cancel=\"true\"\n        :showing-form=\"!credential\"\n        class=\"select-credential\"\n        :cancel=\"()=>$emit('cancel-credential')\"\n        @update:value=\"$emit('update-credential', $event)\"\n      />\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\">\n  .credential-region {\n    display: flex;\n\n    .region {\n      flex-basis: 50%;\n      flex-grow: 0;\n      margin: 0 1.75% 0 0;\n    }\n\n    &.showing-form {\n      flex-direction: column;\n      flex-grow: 1;\n\n      &>.region {\n        margin: 0;\n      }\n\n      &>.select-credential-container{\n      display:flex;\n      flex-direction: column;\n      flex-grow: 1;\n      }\n    }\n\n    &>.select-credential-container{\n      flex-basis: 50%;\n\n      &>.select-credential{\n        flex-grow: 1;\n      }\n\n    }\n  }\n\n</style>\n"]}]}