{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Question.js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/Question.js", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}