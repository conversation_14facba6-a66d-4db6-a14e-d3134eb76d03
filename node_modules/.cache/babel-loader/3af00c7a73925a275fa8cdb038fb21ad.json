{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Conditions.vue?vue&type=template&id=4842edf0", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Conditions.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgdG9EaXNwbGF5U3RyaW5nIGFzIF90b0Rpc3BsYXlTdHJpbmcsIG5vcm1hbGl6ZUNsYXNzIGFzIF9ub3JtYWxpemVDbGFzcywgY3JlYXRlRWxlbWVudFZOb2RlIGFzIF9jcmVhdGVFbGVtZW50Vk5vZGUsIGNyZWF0ZVRleHRWTm9kZSBhcyBfY3JlYXRlVGV4dFZOb2RlLCByZXNvbHZlQ29tcG9uZW50IGFzIF9yZXNvbHZlQ29tcG9uZW50LCB3aXRoQ3R4IGFzIF93aXRoQ3R4LCBvcGVuQmxvY2sgYXMgX29wZW5CbG9jaywgY3JlYXRlQmxvY2sgYXMgX2NyZWF0ZUJsb2NrIH0gZnJvbSAidnVlIgoKZXhwb3J0IGZ1bmN0aW9uIHJlbmRlcihfY3R4LCBfY2FjaGUsICRwcm9wcywgJHNldHVwLCAkZGF0YSwgJG9wdGlvbnMpIHsKICBjb25zdCBfY29tcG9uZW50X1NvcnRhYmxlVGFibGUgPSBfcmVzb2x2ZUNvbXBvbmVudCgiU29ydGFibGVUYWJsZSIpCgogIHJldHVybiAoX29wZW5CbG9jaygpLCBfY3JlYXRlQmxvY2soX2NvbXBvbmVudF9Tb3J0YWJsZVRhYmxlLCB7CiAgICBoZWFkZXJzOiAkb3B0aW9ucy5oZWFkZXJzLAogICAgcm93czogJG9wdGlvbnMucm93cywKICAgICJrZXktZmllbGQiOiAiY29uZGl0aW9uIiwKICAgICJkZWZhdWx0LXNvcnQtYnkiOiAiY29uZGl0aW9uIiwKICAgICJ0YWJsZS1hY3Rpb25zIjogZmFsc2UsCiAgICAicm93LWFjdGlvbnMiOiBmYWxzZSwKICAgIHNlYXJjaDogZmFsc2UKICB9LCB7CiAgICAiY2VsbDpjb25kaXRpb24iOiBfd2l0aEN0eCgoe3Jvd30pID0+IFsKICAgICAgX2NyZWF0ZUVsZW1lbnRWTm9kZSgic3BhbiIsIHsKICAgICAgICBjbGFzczogX25vcm1hbGl6ZUNsYXNzKHsndGV4dC1lcnJvcic6IHJvdy5lcnJvcn0pCiAgICAgIH0sIF90b0Rpc3BsYXlTdHJpbmcocm93LmNvbmRpdGlvbiksIDMgLyogVEVYVCwgQ0xBU1MgKi8pCiAgICBdKSwKICAgICJjZWxsOnN0YXR1cyI6IF93aXRoQ3R4KCh7cm93fSkgPT4gWwogICAgICBfY3JlYXRlRWxlbWVudFZOb2RlKCJzcGFuIiwgewogICAgICAgIGNsYXNzOiBfbm9ybWFsaXplQ2xhc3Moeyd0ZXh0LWVycm9yJzogcm93LmVycm9yfSkKICAgICAgfSwgX3RvRGlzcGxheVN0cmluZyhyb3cuc3RhdHVzKSwgMyAvKiBURVhULCBDTEFTUyAqLykKICAgIF0pLAogICAgXzogMSAvKiBTVEFCTEUgKi8sCiAgICBfXzogWzBdCiAgfSwgOCAvKiBQUk9QUyAqLywgWyJoZWFkZXJzIiwgInJvd3MiXSkpCn0="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Conditions.vue"], "names": [], "mappings": ";;;;;wBA2EE,aAgBgB;IAfb,OAAO,EAAE,gBAAO;IAChB,IAAI,EAAE,aAAI;IACX,WAAS,EAAC,WAAW;IACrB,iBAAe,EAAC,WAAW;IAC1B,eAAa,EAAE,KAAK;IACpB,aAAW,EAAE,KAAK;IAClB,MAAM,EAAE,KAAK;;IAEH,gBAAc,WACvB,CAAmE,CADzC,GAAG;MAC7B,oBAAmE;QAA5D,KAAK,iCAAiB,GAAG,CAAC,KAAK;0BAAM,GAAG,CAAC,SAAS;;IAGhD,aAAW,WACpB,CAAgE,CADzC,GAAG;MAC1B,oBAAgE;QAAzD,KAAK,iCAAiB,GAAG,CAAC,KAAK;0BAAM,GAAG,CAAC,MAAM", "sourcesContent": ["<script>\nimport SortableTable from '@shell/components/SortableTable';\n\nexport default {\n  components: { SortableTable },\n  props:      {\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  computed: {\n    headers() {\n      return [\n        {\n          name:        'condition',\n          labelKey:    'tableHeaders.condition',\n          value:       'condition',\n          width:       150,\n          sort:        'condition',\n          dashIfEmpty: true,\n        },\n        {\n          name:        'status',\n          labelKey:    'tableHeaders.status',\n          value:       'status',\n          width:       75,\n          sort:        'status',\n          dashIfEmpty: true,\n        },\n        {\n          name:          'time',\n          labelKey:      'tableHeaders.updated',\n          value:         'time',\n          sort:          'time',\n          formatter:     'LiveDate',\n          formatterOpts: { addSuffix: true },\n          width:         125,\n          dashIfEmpty:   true,\n        },\n        {\n          name:        'message',\n          labelKey:    'tableHeaders.message',\n          value:       'message',\n          sort:        ['message'],\n          dashIfEmpty: true,\n        },\n      ];\n    },\n\n    rows() {\n      return (this.value.status?.conditions || []).map((cond) => {\n        let message = cond.message || '';\n\n        if ( cond.reason ) {\n          message = `[${ cond.reason }] ${ message }`.trim();\n        }\n\n        return {\n          condition: cond.type || 'Unknown',\n          status:    cond.status || 'Unknown',\n          error:     cond.error,\n          time:      cond.lastProbeTime || cond.lastUpdateTime || cond.lastTransitionTime,\n          message,\n        };\n      });\n    },\n  }\n};\n</script>\n\n<template>\n  <SortableTable\n    :headers=\"headers\"\n    :rows=\"rows\"\n    key-field=\"condition\"\n    default-sort-by=\"condition\"\n    :table-actions=\"false\"\n    :row-actions=\"false\"\n    :search=\"false\"\n  >\n    <template #cell:condition=\"{row}\">\n      <span :class=\"{'text-error': row.error}\">{{ row.condition }}</span>\n    </template>\n\n    <template #cell:status=\"{row}\">\n      <span :class=\"{'text-error': row.error}\">{{ row.status }}</span>\n    </template>\n  </SortableTable>\n</template>\n"]}]}