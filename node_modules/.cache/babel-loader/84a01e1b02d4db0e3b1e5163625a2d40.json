{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AuthBanner.vue?vue&type=template&id=0e8d2270&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AuthBanner.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AuthBanner.vue"], "names": ["t", "$slots"], "mappings": ";;qBAiDW,KAAK,EAAC,MAAM;;;EAsBjB,KAAK,EAAC,QAAQ;;;;;;;wBA3BlB,oBAyCM;IAxCJ,aAsBS;MArBP,KAAK,EAAC,kBAAkB;MACxB,KAAK,EAAC,QAAQ;;wBAEd,CAEM;QAFN,oBAEM,OAFN,UAEM,mBADDA,MAAC,mCAAmC,YAAK;;QAE9C,YAAuB;;QACvB,oBAMS;UALP,IAAI,EAAC,QAAQ;UACb,KAAK,EAAC,qBAAqB;UAC1B,OAAK,0CAAE,mCAAI;4BAETA,MAAC;;QAEN,oBAMS;UALP,IAAI,EAAC,QAAQ;UACb,KAAK,EAAC,oCAAoC;UACzC,OAAK,0CAAE,+DAAgB;4BAErBA,MAAC;;;;;;OAKEC,WAAM,CAAC,IAAI;uBADrB,oBAKQ,SALR,UAKQ;UADN,YAAoB;;;;KAIdA,WAAM,CAAC,MAAM;QADrB,YAGE;;;IAEF,aAGE;MAFA,GAAG,EAAC,0BAA0B;MAC7B,SAAO,EAAE,cAAO", "sourcesContent": ["\n<script>\nimport { Banner } from '@components/Banner';\nimport DisableAuthProviderModal from '@shell/components/DisableAuthProviderModal';\n\nexport default {\n  components: {\n    Banner,\n    DisableAuthProviderModal\n  },\n\n  props: {\n    tArgs: {\n      type:     Object,\n      required: true,\n      default:  () => { },\n    },\n    disable: {\n      type:     Function,\n      required: true,\n      default:  () => { },\n    },\n    edit: {\n      type:     Function,\n      required: true,\n      default:  () => { },\n    }\n  },\n\n  computed: {\n    values() {\n      return Object.entries(this.table);\n    }\n  },\n\n  methods: {\n    showDisableModal() {\n      this.$refs.disableAuthProviderModal.show();\n    }\n  },\n};\n</script>\n\n<template>\n  <div>\n    <Banner\n      color=\"success clearfix\"\n      class=\"banner\"\n    >\n      <div class=\"text\">\n        {{ t('authConfig.stateBanner.enabled', tArgs) }}\n      </div>\n      <slot name=\"actions\" />\n      <button\n        type=\"button\"\n        class=\"btn-sm role-primary\"\n        @click=\"edit\"\n      >\n        {{ t('action.edit') }}\n      </button>\n      <button\n        type=\"button\"\n        class=\"ml-10 btn-sm role-primary bg-error\"\n        @click=\"showDisableModal\"\n      >\n        {{ t('generic.disable') }}\n      </button>\n    </Banner>\n\n    <table\n      v-if=\"!!$slots.rows\"\n      class=\"values\"\n    >\n      <slot name=\"rows\" />\n    </table>\n\n    <slot\n      v-if=\"$slots.footer\"\n      name=\"footer\"\n    />\n\n    <DisableAuthProviderModal\n      ref=\"disableAuthProviderModal\"\n      @disable=\"disable\"\n    />\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.banner {\n  display: flex;\n    align-items: center;\n  .text {\n    flex: 1;\n  }\n}\n\n.values {\n  tr td:not(:first-of-type) {\n    padding-left: 10px;\n  }\n}\n\n</style>\n"]}]}