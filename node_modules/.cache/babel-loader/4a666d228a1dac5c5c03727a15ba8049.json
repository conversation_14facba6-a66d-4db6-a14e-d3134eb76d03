{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Import.vue?vue&type=template&id=e4791144", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Import.vue", "mtime": 1754992537319}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/pkg/rancher-saas/components/eks/Import.vue"], "names": ["$emit", "t"], "mappings": ";;qBAmGO,KAAK,EAAC,wBAAwB;qBAC5B,KAAK,EAAC,YAAY;qBAmBlB,KAAK,EAAC,YAAa;;;;;;;wBApB1B,oBA4BM,OA5BN,UA4BM;IA3BJ,oBAkBM,OAlBN,UAkBM;OAhBI,cAAQ,CAAC,MAAM;yBADvB,aASE;;YAPC,OAAO,EAAE,qBAAe;YACxB,IAAI,EAAE,WAAI;YACV,KAAK,EAAE,kBAAW;YAClB,OAAO,EAAE,cAAQ;YAClB,WAAS,EAAC,kBAAkB;YAC3B,KAAK,EAAE,YAAK,CAAC,WAAW;YACxB,WAAS,uCAAEA,UAAK,uBAAuB,MAAM;;yBAEhD,aAME;;YAJA,WAAS,EAAC,kBAAkB;YAC3B,KAAK,EAAE,kBAAW;YAClB,KAAK,EAAE,YAAK,CAAC,WAAW;YACxB,gBAAY,uCAAEA,UAAK,uBAAuB,MAAM;;;;IAGrD,oBAOM,OAPN,UAOM;MANJ,aAKE;QAJC,KAAK,EAAE,0BAAmB;QAC1B,IAAI,EAAE,WAAI;QACV,KAAK,EAAEC,MAAC;QACR,gBAAY,uCAAED,UAAK,+BAA+B,MAAM", "sourcesContent": ["<script>\nimport { _EDIT } from '@shell/config/query-params';\nimport debounce from 'lodash/debounce';\nimport LabeledSelect from '@shell/components/form/LabeledSelect.vue';\nimport LabeledInput from '@components/Form/LabeledInput/LabeledInput.vue';\nimport Checkbox from '@components/Form/Checkbox/Checkbox.vue';\n\nexport default {\n  name: 'ImportEKS',\n\n  emits: ['update:clusterName', 'error', 'update:enableNetworkPolicy'],\n\n  components: {\n    LabeledSelect, LabeledInput, Checkbox\n  },\n\n  props: {\n    mode: {\n      type:    String,\n      default: _EDIT\n    },\n\n    // name of cluster to be imported\n    // this wont necessarily align with normanCluster.name as it would w/ provisioning\n    clusterName: {\n      type:    String,\n      default: ''\n    },\n\n    credential: {\n      type:    String,\n      default: null\n    },\n\n    region: {\n      type:    String,\n      default: ''\n    },\n\n    enableNetworkPolicy: {\n      type:    Boolean,\n      default: false\n    },\n\n    rules: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  created() {\n    this.debouncedlistEKSClusters = debounce(this.listEKSClusters, 200);\n    this.debouncedlistEKSClusters();\n  },\n\n  data() {\n    return { loadingClusters: false, clusters: [] };\n  },\n\n  watch: {\n    region() {\n      this.debouncedlistEKSClusters();\n    },\n    cloudCredentialId() {\n      this.debouncedlistEKSClusters();\n    }\n  },\n\n  methods: {\n\n    async listEKSClusters() {\n      if (!this.region || !this.credential) {\n        return;\n      }\n      this.loadingClusters = true;\n      try {\n        const eksClient = await this.$store.dispatch('aws/eks', { region: this.region, cloudCredentialId: this.credential });\n\n        const res = await eksClient.listClusters({});\n        const clusters = res?.clusters;\n\n        if (!clusters) {\n          return;\n        }\n\n        this.clusters = res?.clusters;\n      } catch (err) {\n        this.$emit('error', err);\n      }\n\n      this.loadingClusters = false;\n    }\n  }\n};\n</script>\n\n<template>\n  <div class=\"row mb-10 align-center\">\n    <div class=\"col span-6\">\n      <LabeledSelect\n        v-if=\"clusters.length\"\n        :loading=\"loadingClusters\"\n        :mode=\"mode\"\n        :value=\"clusterName\"\n        :options=\"clusters\"\n        label-key=\"eks.import.label\"\n        :rules=\"rules.displayName\"\n        @selecting=\"$emit('update:clusterName', $event)\"\n      />\n      <LabeledInput\n        v-else\n        label-key=\"eks.import.label\"\n        :value=\"clusterName\"\n        :rules=\"rules.displayName\"\n        @update:value=\"$emit('update:clusterName', $event)\"\n      />\n    </div>\n    <div class=\"col span-6 \">\n      <Checkbox\n        :value=\"enableNetworkPolicy\"\n        :mode=\"mode\"\n        :label=\"t('eks.enableNetworkPolicy.label')\"\n        @update:value=\"$emit('update:enableNetworkPolicy', $event)\"\n      />\n    </div>\n  </div>\n</template>\n"]}]}