{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Import.vue?vue&type=template&id=24491468&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Import.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Import.vue"], "names": ["$fetchState", "t"], "mappings": ";;qBAkIW,KAAoC,EAApC,kCAAoC;;;EAEjC,aAAW,EAAC,qBAAqB;;qBAMhC,KAAK,EAAC,KAAK;qBACT,KAAK,EAAC,YAAY;qBASlB,KAAK,EAAC,YAAY;;;EAetB,KAAK,EAAC,SAAS;;;;EAgCpB,KAAK,EAAC,aAAa;EACnB,KAAmB,EAAnB,gBAAmB;;;;;EAenB,KAAK,EAAC,aAAa;EACnB,KAAmB,EAAnB,gBAAmB;;;;;;;;;;;;;;;UA1FVA,gBAAW,CAAC,OAAO;qBAAlC,aAAsC;qBACtC,aA+GO;;QA7GJ,uBAAqB,EAAE,KAAK;QAC7B,aAAW,EAAC,aAAa;QACxB,oBAAkB,EAAE,IAAI;;QAEd,KAAK,WACd,CA6BM;UA7BN,oBA6BM,OA7BN,UA6BM;aA5BY,UAAI;+BAClB,oBAEK,MAFL,UAEK,mBADAC,MAAC,2BAA2B,UAAI,CAAC,MAAM;+BAG9C,oBAsBW;kCArBT,oBAA2B;mCAAlB,cAAc;;;kBACvB,oBAmBM,OAnBN,UAmBM;oBAlBJ,oBAQM,OARN,UAQM;sBAPJ,aAME;wBALA,IAAI,EAAC,QAAQ;wBACZ,YAAU,EAAEA,MAAC,qCAAqCA,MAAC;wBACpD,KAAK,EAAC,8BAA8B;wBACnC,KAAK,EAAEA,MAAC;wBACR,UAAQ,EAAE,uBAAc;;;;oBAG7B,oBAQM,OARN,UAQM;sBAPJ,aAME;wBALC,KAAK,EAAE,uBAAgB;wBACvB,OAAO,EAAE,yBAAgB;wBAC1B,WAAS,EAAC,+BAA+B;wBACzC,IAAI,EAAC,MAAM;wBACV,gBAAY,4BAAE,QAAQ,IAAI,uBAAgB,GAAG,QAAQ;;;;;;;QAOvD,IAAI;WACG,UAAI;6BAClB,oBAaM,OAbN,UAaM;gBAZJ,aAWE;kBAVC,IAAI,EAAE,UAAI;kBACV,OAAO,EAAE,gBAAO;kBACjB,IAAI,EAAC,MAAM;kBACX,WAAS,EAAC,MAAM;kBACf,MAAM,EAAE,KAAK;kBACb,MAAM,EAAE,IAAI;kBACZ,aAAW,EAAE,KAAK;kBAClB,eAAa,EAAE,KAAK;kBACpB,sBAAoB,EAAE,KAAK;kBAC3B,UAAQ,EAAE,iBAAQ;;;6BAIzB,aAME;;gBAJA,GAAG,EAAC,YAAY;gBACR,KAAK,EAAE,iBAAW;wEAAX,iBAAW;gBAC1B,KAAK,EAAC,aAAa;gBAClB,SAAO,EAAE,0BAAiB;;;6BAE7B,oBAKE,6BAJmB,YAAM,GAAjB,GAAG,EAAE,CAAC;kCADhB,aAKE;cAHC,GAAG,EAAE,CAAC;cACP,KAAK,EAAC,OAAO;cACZ,KAAK,EAAE,GAAG;;;;QAGJ,OAAO;WAER,UAAI;6BADZ,oBAeM,OAfN,UAeM;gBAVJ,oBASS;kBARN,YAAU,EAAEA,MAAC;kBACd,IAAI,EAAC,QAAQ;kBACb,IAAI,EAAC,QAAQ;kBACb,KAAK,EAAC,kBAAkB;kBACxB,aAAW,EAAC,mBAAmB;kBAC9B,OAAK,0CAAE,yCAAK;oCAEVA,MAAC;;6BAGR,oBAuBM,OAvBN,UAuBM;gBAlBJ,oBASS;kBARN,YAAU,EAAEA,MAAC;kBACd,IAAI,EAAC,QAAQ;kBACb,IAAI,EAAC,QAAQ;kBACb,KAAK,EAAC,0BAA0B;kBAChC,aAAW,EAAC,oBAAoB;kBAC/B,OAAK,0CAAE,yCAAK;oCAEVA,MAAC;;kBAGG,UAAI;mCADb,aAOE;;sBALA,IAAI,EAAC,QAAQ;sBACZ,QAAQ,GAAG,iBAAW,CAAC,MAAM;sBAC9B,aAAW,EAAC,2BAA2B;sBACtC,YAAU,EAAEA,MAAC;sBACb,OAAK,EAAE,mBAAU", "sourcesContent": ["<script>\nimport { mapGetters } from 'vuex';\nimport { Card } from '@components/Card';\nimport { Banner } from '@components/Banner';\nimport Loading from '@shell/components/Loading';\nimport YamlEditor from '@shell/components/YamlEditor';\nimport FileSelector from '@shell/components/form/FileSelector';\nimport AsyncButton from '@shell/components/AsyncButton';\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\nimport SortableTable from '@shell/components/SortableTable';\nimport { sortBy } from '@shell/utils/sort';\nimport { exceptionToErrorsArray } from '@shell/utils/error';\nimport { NAMESPACE } from '@shell/config/types';\nimport { NAME as NAME_COL, TYPE, NAMESPACE as NAMESPACE_COL, AGE } from '@shell/config/table-headers';\n\nexport default {\n  emits: ['close', 'onReadyYamlEditor'],\n\n  components: {\n    AsyncButton,\n    Banner,\n    Card,\n    Loading,\n    YamlEditor,\n    FileSelector,\n    LabeledSelect,\n    SortableTable\n  },\n\n  props: {\n    defaultNamespace: {\n      type:    String,\n      default: 'default'\n    },\n  },\n\n  async fetch() {\n    this.allNamespaces = await this.$store.dispatch('cluster/findAll', { type: NAMESPACE, opt: { url: 'namespaces' } });\n  },\n\n  data() {\n    return {\n      currentYaml:   '',\n      allNamespaces: [],\n      errors:        null,\n      rows:          null,\n      done:          false,\n    };\n  },\n\n  computed: {\n    ...mapGetters(['currentCluster']),\n\n    namespaceOptions() {\n      const out = this.allNamespaces.map((obj) => {\n        return {\n          label: obj.name,\n          value: obj.name,\n        };\n      });\n\n      return sortBy(out, 'label');\n    },\n\n    headers() {\n      return [\n        TYPE,\n        NAME_COL,\n        NAMESPACE_COL,\n        AGE\n      ];\n    },\n  },\n\n  methods: {\n    close() {\n      this.$emit('close');\n    },\n\n    onFileSelected(value) {\n      const component = this.$refs.yamleditor;\n\n      if (component) {\n        this.errors = null;\n        component.updateValue(value);\n      }\n    },\n\n    async importYaml(btnCb) {\n      try {\n        this.errors = [];\n\n        const res = await this.currentCluster.doAction('apply', {\n          yaml:             this.currentYaml,\n          defaultNamespace: this.defaultNamespace,\n        });\n\n        btnCb(true);\n\n        this.rows = res;\n        this.done = true;\n      } catch (err) {\n        this.errors = exceptionToErrorsArray(err);\n        this.done = false;\n        btnCb(false);\n      }\n    },\n\n    rowClick(e) {\n      if ( e.target.tagName === 'A' ) {\n        this.close();\n      }\n    },\n\n    onReadyYamlEditor(arg) {\n      this.$emit('onReadyYamlEditor', arg);\n    }\n  },\n};\n</script>\n\n<template>\n  <Loading v-if=\"$fetchState.pending\" />\n  <Card\n    v-else\n    :show-highlight-border=\"false\"\n    data-testid=\"import-yaml\"\n    :trigger-focus-trap=\"true\"\n  >\n    <template #title>\n      <div style=\"display: block; width: 100%;\">\n        <template v-if=\"done\">\n          <h4 data-testid=\"import-yaml-success\">\n            {{ t('import.success', {count: rows.length}) }}\n          </h4>\n        </template>\n        <template v-else>\n          <h4 v-t=\"'import.title'\" />\n          <div class=\"row\">\n            <div class=\"col span-6\">\n              <FileSelector\n                role=\"button\"\n                :aria-label=\"t('generic.readFromFileArea', { area: t('import.title') })\"\n                class=\"btn role-secondary pull-left\"\n                :label=\"t('generic.readFromFile')\"\n                @selected=\"onFileSelected\"\n              />\n            </div>\n            <div class=\"col span-6\">\n              <LabeledSelect\n                :value=\"defaultNamespace\"\n                :options=\"namespaceOptions\"\n                label-key=\"import.defaultNamespace.label\"\n                mode=\"edit\"\n                @update:value=\"newValue => defaultNamespace = newValue\"\n              />\n            </div>\n          </div>\n        </template>\n      </div>\n    </template>\n    <template #body>\n      <template v-if=\"done\">\n        <div class=\"results\">\n          <SortableTable\n            :rows=\"rows\"\n            :headers=\"headers\"\n            mode=\"view\"\n            key-field=\"_key\"\n            :search=\"false\"\n            :paging=\"true\"\n            :row-actions=\"false\"\n            :table-actions=\"false\"\n            :sub-rows-description=\"false\"\n            @rowClick=\"rowClick\"\n          />\n        </div>\n      </template>\n      <YamlEditor\n        v-else\n        ref=\"yamleditor\"\n        v-model:value=\"currentYaml\"\n        class=\"yaml-editor\"\n        @onReady=\"onReadyYamlEditor\"\n      />\n      <Banner\n        v-for=\"(err, i) in errors\"\n        :key=\"i\"\n        color=\"error\"\n        :label=\"err\"\n      />\n    </template>\n    <template #actions>\n      <div\n        v-if=\"done\"\n        class=\"text-center\"\n        style=\"width: 100%\"\n      >\n        <button\n          :aria-label=\"t('generic.close')\"\n          role=\"button\"\n          type=\"button\"\n          class=\"btn role-primary\"\n          data-testid=\"import-yaml-close\"\n          @click=\"close\"\n        >\n          {{ t('generic.close') }}\n        </button>\n      </div>\n      <div\n        v-else\n        class=\"text-center\"\n        style=\"width: 100%\"\n      >\n        <button\n          :aria-label=\"t('generic.cancel')\"\n          role=\"button\"\n          type=\"button\"\n          class=\"btn role-secondary mr-10\"\n          data-testid=\"import-yaml-cancel\"\n          @click=\"close\"\n        >\n          {{ t('generic.cancel') }}\n        </button>\n        <AsyncButton\n          v-if=\"!done\"\n          mode=\"import\"\n          :disabled=\"!currentYaml.length\"\n          data-testid=\"import-yaml-import-action\"\n          :aria-label=\"t('import.title')\"\n          @click=\"importYaml\"\n        />\n      </div>\n    </template>\n  </Card>\n</template>\n\n<style lang='scss' scoped>\n  $min: 50vh;\n  $max: 50vh;\n\n  .yaml-editor {\n    flex: 1;\n    min-height: $min;\n    max-height: $max;\n\n    :deep() .code-mirror {\n      .CodeMirror {\n        position: initial;\n      }\n\n      .CodeMirror,\n      .CodeMirror-scroll,\n      .CodeMirror-gutters {\n        min-height: $min;\n        max-height: $max;\n      }\n    }\n  }\n</style>\n"]}]}