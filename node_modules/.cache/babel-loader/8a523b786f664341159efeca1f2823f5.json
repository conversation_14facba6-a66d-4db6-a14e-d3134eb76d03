{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GrafanaDashboard.vue?vue&type=template&id=0ee01af3&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GrafanaDashboard.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GrafanaDashboard.vue"], "names": ["t"], "mappings": ";;qBAgPO,KAAK,EAAC,eAAe;qBAMjB,KAAK,EAAC,aAAa;;;;;EAoBxB,KAAK,EAAC,eAAe;;;;;;;;wBA1BzB,oBAwCM,OAxCN,UAwCM;KAtCI,WAAK;uBADb,aAWS;;UATP,KAAK,EAAC,OAAO;UACb,KAAqB,EAArB,kBAAqB;;4BAErB,CAKM;YALN,oBAKM,OALN,UAKM;gDAJDA,MAAC,qCAAoC,GAAC;cAAA,oBAGF;gBAFrC,IAAI,EAAC,GAAG;gBACP,OAAK,0CAAE,2CAAM;kCACZA,MAAC;;;;;;;oBAGT,oBAOE;MALA,GAAG,EAAC,OAAO;MACV,KAAK,4BAAG,aAAO;MACf,GAAG,EAAE,mBAAU;MAChB,WAAW,EAAC,GAAG;MACf,SAAS,EAAC,IAAI;;gBALL,WAAK;;;KAOL,aAAO;uBAAlB,oBAEM;UADJ,aAAW;;;;MAGJ,aAAO,KAAK,WAAK;uBAD1B,oBAeM,OAfN,UAeM;UAXJ,wFAAwE;;UACxE,0GAA0F;;UAC1F,2HAA2G;;UAC3G,2DAA2C;;UAC3C,qDAAqC;;UACrC,8DAA8C;;UAC9C,oBAI8E;YAH3E,IAAI,EAAE,mBAAU;YACjB,MAAM,EAAC,QAAQ;YACf,GAAG,EAAC,mBAAmB;;8CACrBA,MAAC,gCAA+B,GAAC;sCAAA,oBAAqC,OAAlC,KAAK,EAAC,yBAAyB", "sourcesContent": ["<script>\r\nimport Loading from '@shell/components/Loading';\r\nimport { Banner } from '@components/Banner';\r\nimport { computeDashboardUrl } from '@shell/utils/grafana';\r\nimport { CATALOG } from '@shell/config/types';\r\n\r\nexport default {\r\n  components: { Banner, Loading },\r\n  props:      {\r\n    url: {\r\n      type:     String,\r\n      required: true,\r\n    },\r\n    vars: {\r\n      type:    Object,\r\n      default: () => ({})\r\n    },\r\n    range: {\r\n      type:    String,\r\n      default: null\r\n    },\r\n    refreshRate: {\r\n      type:    String,\r\n      default: null\r\n    },\r\n    // change the grafana url prefix for local clusters in certain monitoring versions\r\n    // project monitoring (projectHelmCharts) supply a grafana url that never needs to be modified in this way\r\n    modifyPrefix: {\r\n      type:    Boolean,\r\n      default: true\r\n    },\r\n    backgroundColor: {\r\n      type:    String,\r\n      default: '#1b1c21'\r\n    },\r\n    theme: {\r\n      type:    String,\r\n      default: 'dark'\r\n    }\r\n  },\r\n  async fetch() {\r\n    const inStore = this.$store.getters['currentProduct'].inStore;\r\n\r\n    if (this.$store.getters[`${ inStore }/canList`](CATALOG.APP)) {\r\n      try {\r\n        const res = await this.$store.dispatch(`${ inStore }/find`, { type: CATALOG.APP, id: 'cattle-monitoring-system/rancher-monitoring' });\r\n\r\n        this.monitoringVersion = res?.currentVersion;\r\n      } catch (err) {}\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false, error: false, interval: null, errorTimer: null, monitoringVersion: ''\r\n    };\r\n  },\r\n  computed: {\r\n    currentUrl() {\r\n      return this.computeUrl();\r\n    },\r\n    grafanaUrl() {\r\n      return this.currentUrl.replace('&kiosk', '');\r\n    },\r\n    graphWindow() {\r\n      return this.$refs.frame?.contentWindow;\r\n    },\r\n    graphHistory() {\r\n      return this.graphWindow?.history;\r\n    },\r\n    graphDocument() {\r\n      return this.graphWindow?.document;\r\n    }\r\n  },\r\n  watch: {\r\n    currentUrl(neu) {\r\n      // Should consider changing `this.graphWindow?.angular` to something like `!loaded && !error`\r\n      // https://github.com/rancher/dashboard/pull/5802\r\n      if (this.graphHistory && this.graphWindow?.angular) {\r\n        this.graphWindow.location.replace(neu);\r\n      }\r\n    },\r\n\r\n    error(neu) {\r\n      if (neu) {\r\n        this.errorTimer = setInterval(() => {\r\n          this.reload();\r\n        }, 45000);\r\n      } else {\r\n        clearInterval(this.errorTimer);\r\n        this.errorTimer = null;\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$refs.frame.onload = this.inject;\r\n    this.poll();\r\n  },\r\n  beforeUnmount() {\r\n    if (this.interval) {\r\n      clearInterval(this.interval);\r\n    }\r\n\r\n    if (this.errorTimer) {\r\n      clearInterval(this.errorTimer);\r\n    }\r\n  },\r\n  methods: {\r\n    poll() {\r\n      if (this.interval) {\r\n        clearInterval(this.interval);\r\n        this.interval = null;\r\n      }\r\n\r\n      this.interval = setInterval(() => {\r\n        try {\r\n          const graphWindow = this.$refs.frame?.contentWindow;\r\n\r\n          // Note. getElementsByClassName won't work, following a grafana bump class names are now unique - for example css-2qng6u-panel-container\r\n          const errorElements = graphWindow.document.querySelectorAll('[class$=\"alert-error');\r\n          const errorCornerElements = graphWindow.document.querySelectorAll('[class$=\"panel-info-corner--error');\r\n          const panelInFullScreenElements = graphWindow.document.querySelectorAll('[class$=\"panel-in-fullscreen');\r\n          const panelContainerElements = graphWindow.document.querySelectorAll('[class$=\"panel-container');\r\n          const error = errorElements.length > 0 || errorCornerElements.length > 0;\r\n          const loaded = panelInFullScreenElements.length > 0 || panelContainerElements.length > 0;\r\n          const errorMessageElms = graphWindow.document.getElementsByTagName('pre');\r\n          const errorMessage = errorMessageElms.length > 0 ? errorMessageElms[0].innerText : '';\r\n          const isFailure = errorMessage.includes('\"status\": \"Failure\"');\r\n\r\n          if (error) {\r\n            throw new Error('An error was detected in the iframe');\r\n          }\r\n\r\n          this['loading'] = !loaded;\r\n          this['error'] = isFailure;\r\n        } catch (ex) {\r\n          this['error'] = true;\r\n          this['loading'] = false;\r\n          clearInterval(this.interval);\r\n          this.interval = null;\r\n        }\r\n      }, 100);\r\n    },\r\n    computeFromTo() {\r\n      return {\r\n        from: `now-${ this.range }`,\r\n        to:   `now`\r\n      };\r\n    },\r\n    computeUrl() {\r\n      const embedUrl = this.url;\r\n      const clusterId = this.$store.getters['currentCluster'].id;\r\n      const params = this.computeParams();\r\n\r\n      return computeDashboardUrl(this.monitoringVersion, embedUrl, clusterId, params, this.modifyPrefix);\r\n    },\r\n    computeParams() {\r\n      const params = {};\r\n      const fromTo = this.computeFromTo();\r\n\r\n      if (fromTo.from) {\r\n        params.from = fromTo.from;\r\n      }\r\n\r\n      if (fromTo.to) {\r\n        params.to = fromTo.to;\r\n      }\r\n\r\n      if (this.refreshRate) {\r\n        params.refresh = this.refreshRate;\r\n      }\r\n\r\n      if (Object.keys(this.vars).length > 0) {\r\n        Object.entries(this.vars).forEach((entry) => {\r\n          const paramName = `var-${ entry[0] }`;\r\n\r\n          params[paramName] = entry[1];\r\n        });\r\n      }\r\n\r\n      params.theme = this.theme;\r\n\r\n      return params;\r\n    },\r\n    reload(ev) {\r\n      ev && ev.preventDefault();\r\n      this.$refs.frame.contentWindow.location.reload();\r\n      this.poll();\r\n    },\r\n    injectCss() {\r\n      const style = document.createElement('style');\r\n\r\n      style.innerHTML = `\r\n        body .grafana-app .dashboard-content {\r\n          background: ${ this.backgroundColor };\r\n          padding: 0;\r\n        }\r\n\r\n        body .grafana-app .layout {\r\n          background: ${ this.backgroundColor };\r\n        }\r\n\r\n\r\n        body .grafana-app .dashboard-content .panel-container {\r\n          background-color: initial;\r\n          border: none;\r\n        }\r\n\r\n        body .grafana-app .dashboard-content .panel-wrapper {\r\n          height: 100%;\r\n        }\r\n\r\n        body .grafana-app .panel-menu-container {\r\n          display: none;\r\n        }\r\n\r\n        body .grafana-app .panel-title {\r\n          cursor: default;\r\n        }\r\n\r\n        body .grafana-app .panel-title .panel-title-text div {\r\n          display: none;\r\n        }\r\n      `;\r\n\r\n      const graphWindow = this.$refs.frame?.contentWindow;\r\n      const graphDocument = graphWindow?.document;\r\n\r\n      if (graphDocument.head) {\r\n        graphDocument.head.appendChild(style);\r\n      }\r\n    },\r\n\r\n    inject() {\r\n      this.injectCss();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<template>\r\n  <div class=\"grafana-graph\">\r\n    <Banner\r\n      v-if=\"error\"\r\n      color=\"error\"\r\n      style=\"z-index: 1000\"\r\n    >\r\n      <div class=\"text-center\">\r\n        {{ t('grafanaDashboard.failedToLoad') }} <a\r\n          href=\"#\"\r\n          @click=\"reload\"\r\n        >{{ t('grafanaDashboard.reload') }}</a>\r\n      </div>\r\n    </Banner>\r\n    <iframe\r\n      v-show=\"!error\"\r\n      ref=\"frame\"\r\n      :class=\"{loading, frame: true}\"\r\n      :src=\"currentUrl\"\r\n      frameborder=\"0\"\r\n      scrolling=\"no\"\r\n    />\r\n    <div v-if=\"loading\">\r\n      <Loading />\r\n    </div>\r\n    <div\r\n      v-if=\"!loading && !error\"\r\n      class=\"external-link\"\r\n    >\r\n      <!-- https://github.com/harvester/harvester-installer/pull/512/files -->\r\n      <!-- It is necessary to include the parameter referer when accessing the Grafana page. -->\r\n      <!-- This parameter is required by the backend to identify the origin of the request from which cluster -->\r\n      <!-- The matching mechanism as follows: -->\r\n      <!-- ~.*/k8s/clusters/(c-m-.+)/.* -->\r\n      <!-- ~.*/dashboard/harvester/c/(c-m-.+)/.* -->\r\n      <a\r\n        :href=\"grafanaUrl\"\r\n        target=\"_blank\"\r\n        rel=\"noopener nofollow\"\r\n      >{{ t('grafanaDashboard.grafana') }} <i class=\"icon icon-external-link\" /></a>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<style lang='scss' scoped>\r\n.grafana-graph {\r\n  position: relative;\r\n  min-height: 100%;\r\n  min-width: 100%;\r\n\r\n  & :deep() .content {\r\n    position: relative;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-items: center;\r\n    width: 100%;\r\n    height: 100%;\r\n    padding: 0;\r\n  }\r\n\r\n  & :deep() .overlay {\r\n    position: static;\r\n    background-color: initial;\r\n  }\r\n\r\n  iframe {\r\n    position: absolute;\r\n    left: 0;\r\n    right: 0;\r\n    top: 20px;\r\n    bottom: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    overflow: hidden;\r\n\r\n    &.loading {\r\n      visibility: hidden;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}