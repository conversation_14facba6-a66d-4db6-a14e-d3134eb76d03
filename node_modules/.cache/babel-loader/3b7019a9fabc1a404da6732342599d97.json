{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??clonedRuleSet-44.use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ButtonMultiAction.vue?vue&type=template&id=439898ca&scoped=true&ts=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ButtonMultiAction.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgY3JlYXRlRWxlbWVudFZOb2RlIGFzIF9jcmVhdGVFbGVtZW50Vk5vZGUsIG5vcm1hbGl6ZUNsYXNzIGFzIF9ub3JtYWxpemVDbGFzcywgb3BlbkJsb2NrIGFzIF9vcGVuQmxvY2ssIGNyZWF0ZUVsZW1lbnRCbG9jayBhcyBfY3JlYXRlRWxlbWVudEJsb2NrIH0gZnJvbSAidnVlIjsKY29uc3QgX2hvaXN0ZWRfMSA9IFsiYWx0Il07CmV4cG9ydCBmdW5jdGlvbiByZW5kZXIoX2N0eCwgX2NhY2hlLCAkcHJvcHMsICRzZXR1cCwgJGRhdGEsICRvcHRpb25zKSB7CiAgICByZXR1cm4gKF9vcGVuQmxvY2soKSwgX2NyZWF0ZUVsZW1lbnRCbG9jaygiYnV0dG9uIiwgewogICAgICAgIHR5cGU6ICJidXR0b24iLAogICAgICAgIGNsYXNzOiBfbm9ybWFsaXplQ2xhc3MoWyJidG4gYnRuLXNtIHJvbGUtbXVsdGktYWN0aW9uIGFjdGlvbnMiLCAkc2V0dXAuYnV0dG9uQ2xhc3NdKSwKICAgICAgICByb2xlOiAiYnV0dG9uIiwKICAgICAgICBvbkNsaWNrOiBfY2FjaGVbMF0gfHwgKF9jYWNoZVswXSA9IChlKSA9PiBfY3R4LiRlbWl0KCdjbGljaycsIGUpKQogICAgfSwgWwogICAgICAgIF9jcmVhdGVFbGVtZW50Vk5vZGUoImkiLCB7CiAgICAgICAgICAgIGNsYXNzOiAiaWNvbiBpY29uLWFjdGlvbnMiLAogICAgICAgICAgICBhbHQ6IF9jdHgudCgnc29ydGFibGVUYWJsZS50YWJsZUFjdGlvbnNJbWdBbHQnKQogICAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIF9ob2lzdGVkXzEpCiAgICBdLCAyIC8qIENMQVNTICovKSk7Cn0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ButtonMultiAction.vue?vue&type=template&id=439898ca&scoped=true&ts=true", "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ButtonMultiAction.vue"], "names": ["$emit", "t"], "mappings": "AAAA,OAAO,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,cAAc,IAAI,eAAe,EAAE,SAAS,IAAI,UAAU,EAAE,kBAAkB,IAAI,mBAAmB,EAAE,MAAM,KAAK,CAAA;AAEtK,MAAM,UAAU,GAAG,CAAC,KAAK,CAAC,CAAA;AAE1B,MAAM,UAAU,MAAM,CAAC,IAAS,EAAC,MAAW,EAAC,MAAW,EAAC,MAAW,EAAC,KAAU,EAAC,QAAa;IAC3F,OAAO,CAAC,UAAU,EAAE,ECgBpB,mBAAA,CAWS,QAAA,EAAA;QAVP,IAAI,EAAC,QAAQ;QACb,KAAK,EAAA,eAAA,CAAA,CAAC,sCAAsC,EAEpC,MAAA,CAAA,WAAW,CAAA,CAAA;QADnB,IAAI,EAAC,QAAQ;QAEZ,OAAK,EAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAG,CAAQ,EAAA,EAAA,CAAKA,IAAAA,CAAAA,KAAK,CAAA,OAAA,EAAU,CAAC,CAAA,CAAA;KDhBvC,EAAE;QCkBD,mBAAA,CAGE,GAAA,EAAA;YAFA,KAAK,EAAC,mBAAmB;YACxB,GAAG,EAAEC,IAAAA,CAAAA,CAAC,CAAA,kCAAA,CAAA;SDhBR,EAAE,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC;KACpC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAA;AACpB,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ButtonMultiAction.vue.tsx", "sourceRoot": "", "sourcesContent": ["import { createElementVNode as _createElementVNode, normalizeClass as _normalizeClass, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = [\"alt\"]\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  return (_openBlock(), _createElementBlock(\"button\", {\n    type: \"button\",\n    class: _normalizeClass([\"btn btn-sm role-multi-action actions\", $setup.buttonClass]),\n    role: \"button\",\n    onClick: _cache[0] || (_cache[0] = (e) => _ctx.$emit('click', e))\n  }, [\n    _createElementVNode(\"i\", {\n      class: \"icon icon-actions\",\n      alt: _ctx.t('sortableTable.tableActionsImgAlt')\n    }, null, 8 /* PROPS */, _hoisted_1)\n  ], 2 /* CLASS */))\n}", "<script setup lang=\"ts\">\nimport { computed } from 'vue';\n\ndefineEmits(['click']);\n\ntype Props = {\n  borderless?: boolean;\n  invisible?: boolean;\n}\n\nconst props = defineProps<Props>();\n\nconst buttonClass = computed(() => {\n  return {\n    borderless: props?.borderless,\n    invisible:  props?.invisible,\n  };\n});\n</script>\n\n<template>\n  <button\n    type=\"button\"\n    class=\"btn btn-sm role-multi-action actions\"\n    role=\"button\"\n    :class=\"buttonClass\"\n    @click=\"(e: Event) => $emit('click', e)\"\n  >\n    <i\n      class=\"icon icon-actions\"\n      :alt=\"t('sortableTable.tableActionsImgAlt')\"\n    />\n  </button>\n</template>\n\n<style lang=\"scss\" scoped>\n.borderless {\n  background-color: transparent;\n  border: none;\n\n  &:focus-visible {\n    @include focus-outline;\n    outline-offset: -2px;\n  }\n\n  &:hover, &:focus {\n    background-color: var(--accent-btn);\n    box-shadow: none;\n  }\n}\n</style>\n"]}]}