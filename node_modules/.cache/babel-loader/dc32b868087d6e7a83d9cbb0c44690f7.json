{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??clonedRuleSet-44.use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownItem.vue?vue&type=script&setup=true&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownItem.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownItem.vue?vue&type=script&setup=true&lang=ts", "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownItem.vue"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,IAAI,gBAAgB,EAAE,MAAM,KAAK,CAAA;ACIzD,OAAO,EAAE,MAAM,EAAE,MAAM,KAAK,CAAA;AAC5B,OAAO,EAAmB,cAAc,EAAE,MAAM,SAAS,CAAA;ADAzD,eAAe,aAAa,CAAA,gBAAgB,CAAC;IAC3C,MAAM,EAAE,gBAAgB;IACxB,KAAK,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE;IAC5B,KAAK,EAAE,CAAC,OAAO,CAAC;IAChB,KAAK,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE;QACjD,QAAQ,EAAE,CAAC;QCTb;;WAEE;QAIF,MAAM,KAAK,GAAG,OAAkC,CAAA;QAChD,MAAM,KAAK,GAAG,MAAsB,CAAA;QAEpC,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,MAAM,CAAkB,iBAAiB,CAAC,IAAI,cAAc,CAAA;QAE7F;;;WAGE;QACF,MAAM,aAAa,GAAG,CAAC,CAAgB,EAAE,EAAE;YACzC,MAAM,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAA;YAEzC,MAAM,WAAW,GAAG,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,IAAI,IAAI,WAAW,EAAE,CAAC,CAAA;YAEhF,IAAI,WAAW,GAAG,CAAC,EAAE,CAAA;gBACnB,OAAM;YACR,CAAA;YAEA,MAAM,aAAa,GAAG,CAAC,CAAC,GAAG,KAAK,WAAW,CAAA;YAE3C,MAAM,QAAQ,GAAG,YAAY,CAAC,aAAa,EAAE,WAAW,EAAE,aAAa,CAAC,KAAK,CAAC,CAAA;YAE9E,IAAI,aAAa,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,WAAW,EAAE,CAAA;gBACxD,aAAa,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAA;YACvC,CAAA;QACF,CAAC,CAAA;QAED;;;;;;WAME;QACF,MAAM,YAAY,GAAG,CAAC,aAAsB,EAAE,WAAmB,EAAE,QAAmB,EAAE,EAAE;YACxF,MAAM,QAAQ,GAAG,aAAa,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAA;YAElE,IAAI,QAAQ,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAA;gBAClC,OAAO,CAAC,CAAA;YACV,CAAA;YAEA,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAA;gBAChB,OAAO,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAA;YAC5B,CAAA;YAEA,OAAO,QAAQ,CAAA;QACjB,CAAC,CAAA;QAED,MAAM,WAAW,GAAG,CAAC,CAAa,EAAE,EAAE;YACpC,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAA;gBAClB,OAAM;YACR,CAAA;YAEA,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAA;YACjB,KAAK,EAAE,CAAA;QACT,CAAC,CAAA;QAED;;;WAGE;QACF,MAAM,cAAc,GAAG,CAAC,CAAgB,EAAE,EAAE;;YAC1C,IAAI,CAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,MAAM,aAAY,WAAW,EAAE,CAAA;gBACpC,MAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,MAAM,0CAAE,KAAK,EAAE,CAAA;YACpB,CAAA;QACF,CAAC,CAAA;QDUD,MAAM,YAAY,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,EAAE,aAAa,EAAE,YAAY,EAAE,WAAW,EAAE,cAAc,EAAE,CAAA;QACrH,MAAM,CAAC,cAAc,CAAC,YAAY,EAAE,iBAAiB,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAA;QAC1F,OAAO,YAAY,CAAA;IACnB,CAAC;CAEA,CAAC,CAAA", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownItem.vue.tsx", "sourceRoot": "", "sourcesContent": ["import { defineComponent as _defineComponent } from 'vue'\nimport { inject } from 'vue';\nimport { DropdownContext, defaultContext } from './types';\n\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'RcDropdownItem',\n  props: { disabled: Boolean },\n  emits: ['click'],\n  setup(__props, { expose: __expose, emit: __emit }) {\n  __expose();\n\n/**\n * An item for a dropdown menu. Used in conjunction with RcDropdown.\n */\nconst props = __props;\nconst emits = __emit;\n\nconst { close, dropdownItems } = inject<DropdownContext>('dropdownContext') || defaultContext;\n\n/**\n * Handles keydown events to navigate between dropdown items.\n * @param {KeyboardEvent} e - The keydown event.\n */\nconst handleKeydown = (e: KeyboardEvent) => {\n  const activeItem = document.activeElement;\n\n  const activeIndex = dropdownItems.value.indexOf(activeItem || new HTMLElement());\n\n  if (activeIndex < 0) {\n    return;\n  }\n\n  const shouldAdvance = e.key === 'ArrowDown';\n\n  const newIndex = findNewIndex(shouldAdvance, activeIndex, dropdownItems.value);\n\n  if (dropdownItems.value[newIndex] instanceof HTMLElement) {\n    dropdownItems.value[newIndex].focus();\n  }\n};\n\n/**\n * Finds the new index for the dropdown item based on the key pressed.\n * @param shouldAdvance - Whether to advance to the next or previous item.\n * @param activeIndex - Current active index.\n * @param itemsArr - Array of dropdown items.\n * @returns The new index.\n */\nconst findNewIndex = (shouldAdvance: boolean, activeIndex: number, itemsArr: Element[]) => {\n  const newIndex = shouldAdvance ? activeIndex + 1 : activeIndex - 1;\n\n  if (newIndex > itemsArr.length - 1) {\n    return 0;\n  }\n\n  if (newIndex < 0) {\n    return itemsArr.length - 1;\n  }\n\n  return newIndex;\n};\n\nconst handleClick = (e: MouseEvent) => {\n  if (props.disabled) {\n    return;\n  }\n\n  emits('click', e);\n  close();\n};\n\n/**\n * Handles keydown events to activate the dropdown item.\n * @param e - The keydown event.\n */\nconst handleActivate = (e: KeyboardEvent) => {\n  if (e?.target instanceof HTMLElement) {\n    e?.target?.click();\n  }\n};\n\nconst __returned__ = { props, emits, close, dropdownItems, handleKeydown, findNewIndex, handleClick, handleActivate }\nObject.defineProperty(__returned__, '__isScriptSetup', { enumerable: false, value: true })\nreturn __returned__\n}\n\n})", "<script setup lang=\"ts\">\n/**\n * An item for a dropdown menu. Used in conjunction with RcDropdown.\n */\nimport { inject } from 'vue';\nimport { DropdownContext, defaultContext } from './types';\n\nconst props = defineProps({ disabled: Boolean });\nconst emits = defineEmits(['click']);\n\nconst { close, dropdownItems } = inject<DropdownContext>('dropdownContext') || defaultContext;\n\n/**\n * Handles keydown events to navigate between dropdown items.\n * @param {KeyboardEvent} e - The keydown event.\n */\nconst handleKeydown = (e: KeyboardEvent) => {\n  const activeItem = document.activeElement;\n\n  const activeIndex = dropdownItems.value.indexOf(activeItem || new HTMLElement());\n\n  if (activeIndex < 0) {\n    return;\n  }\n\n  const shouldAdvance = e.key === 'ArrowDown';\n\n  const newIndex = findNewIndex(shouldAdvance, activeIndex, dropdownItems.value);\n\n  if (dropdownItems.value[newIndex] instanceof HTMLElement) {\n    dropdownItems.value[newIndex].focus();\n  }\n};\n\n/**\n * Finds the new index for the dropdown item based on the key pressed.\n * @param shouldAdvance - Whether to advance to the next or previous item.\n * @param activeIndex - Current active index.\n * @param itemsArr - Array of dropdown items.\n * @returns The new index.\n */\nconst findNewIndex = (shouldAdvance: boolean, activeIndex: number, itemsArr: Element[]) => {\n  const newIndex = shouldAdvance ? activeIndex + 1 : activeIndex - 1;\n\n  if (newIndex > itemsArr.length - 1) {\n    return 0;\n  }\n\n  if (newIndex < 0) {\n    return itemsArr.length - 1;\n  }\n\n  return newIndex;\n};\n\nconst handleClick = (e: MouseEvent) => {\n  if (props.disabled) {\n    return;\n  }\n\n  emits('click', e);\n  close();\n};\n\n/**\n * Handles keydown events to activate the dropdown item.\n * @param e - The keydown event.\n */\nconst handleActivate = (e: KeyboardEvent) => {\n  if (e?.target instanceof HTMLElement) {\n    e?.target?.click();\n  }\n};\n</script>\n\n<template>\n  <div\n    ref=\"dropdownMenuItem\"\n    dropdown-menu-item\n    tabindex=\"-1\"\n    role=\"menuitem\"\n    :disabled=\"disabled || null\"\n    :aria-disabled=\"disabled || false\"\n    @click.stop=\"handleClick\"\n    @keydown.enter.space=\"handleActivate\"\n    @keydown.up.down.stop=\"handleKeydown\"\n  >\n    <slot name=\"before\">\n      <!--Empty slot content-->\n    </slot>\n    <slot name=\"default\">\n      <!--Empty slot content-->\n    </slot>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  [dropdown-menu-item] {\n    display: flex;\n    gap: 8px;\n    align-items: center;\n    padding: 9px 8px;\n    margin: 0 9px;\n    border-radius: 4px;\n\n    &:hover {\n      cursor: pointer;\n      background-color: var(--dropdown-hover-bg);\n    }\n    &:focus-visible, &:focus {\n      @include focus-outline;\n      outline-offset: 0;\n    }\n    &[disabled] {\n      color: var(--disabled-text);\n      &:hover {\n        cursor: not-allowed;\n      }\n    }\n  }\n</style>\n"]}]}