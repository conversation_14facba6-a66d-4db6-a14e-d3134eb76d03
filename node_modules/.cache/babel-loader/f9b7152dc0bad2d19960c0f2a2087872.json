{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Pinned.vue?vue&type=template&id=6f3b6bf2&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Pinned.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgd2l0aE1vZGlmaWVycyBhcyBfd2l0aE1vZGlmaWVycywgd2l0aEtleXMgYXMgX3dpdGhLZXlzLCBub3JtYWxpemVDbGFzcyBhcyBfbm9ybWFsaXplQ2xhc3MsIG9wZW5CbG9jayBhcyBfb3BlbkJsb2NrLCBjcmVhdGVFbGVtZW50QmxvY2sgYXMgX2NyZWF0ZUVsZW1lbnRCbG9jayB9IGZyb20gInZ1ZSIKCmNvbnN0IF9ob2lzdGVkXzEgPSBbInRhYmluZGV4IiwgImFyaWEtY2hlY2tlZCIsICJhcmlhLWxhYmVsIl0KCmV4cG9ydCBmdW5jdGlvbiByZW5kZXIoX2N0eCwgX2NhY2hlLCAkcHJvcHMsICRzZXR1cCwgJGRhdGEsICRvcHRpb25zKSB7CiAgcmV0dXJuIChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soImkiLCB7CiAgICB0YWJpbmRleDogJHByb3BzLnRhYk9yZGVyLAogICAgImFyaWEtY2hlY2tlZCI6ICEhJG9wdGlvbnMucGlubmVkLAogICAgY2xhc3M6IF9ub3JtYWxpemVDbGFzcyhbInBpbiBpY29uIiwgeydpY29uLXBpbi1vdXRsaW5lZCc6ICEkb3B0aW9ucy5waW5uZWQsICdpY29uLXBpbic6ICRvcHRpb25zLnBpbm5lZH1dKSwKICAgIHJvbGU6ICJidXR0b24iLAogICAgImFyaWEtbGFiZWwiOiBfY3R4LnQoJ25hdi5hcmlhTGFiZWwucGluQ2x1c3RlcicsIHsgY2x1c3RlcjogJHByb3BzLmNsdXN0ZXIubGFiZWwgfSksCiAgICBvbkNsaWNrOiBfY2FjaGVbMF0gfHwgKF9jYWNoZVswXSA9IF93aXRoTW9kaWZpZXJzKCguLi5hcmdzKSA9PiAoJG9wdGlvbnMudG9nZ2xlICYmICRvcHRpb25zLnRvZ2dsZSguLi5hcmdzKSksIFsic3RvcCIsInByZXZlbnQiXSkpLAogICAgb25LZXlkb3duOiBbCiAgICAgIF9jYWNoZVsxXSB8fCAoX2NhY2hlWzFdID0gX3dpdGhLZXlzKF93aXRoTW9kaWZpZXJzKCguLi5hcmdzKSA9PiAoJG9wdGlvbnMudG9nZ2xlICYmICRvcHRpb25zLnRvZ2dsZSguLi5hcmdzKSksIFsicHJldmVudCJdKSwgWyJlbnRlciJdKSksCiAgICAgIF9jYWNoZVsyXSB8fCAoX2NhY2hlWzJdID0gX3dpdGhLZXlzKF93aXRoTW9kaWZpZXJzKCguLi5hcmdzKSA9PiAoJG9wdGlvbnMudG9nZ2xlICYmICRvcHRpb25zLnRvZ2dsZSguLi5hcmdzKSksIFsicHJldmVudCJdKSwgWyJzcGFjZSJdKSkKICAgIF0KICB9LCBudWxsLCA0MiAvKiBDTEFTUywgUFJPUFMsIE5FRURfSFlEUkFUSU9OICovLCBfaG9pc3RlZF8xKSkKfQ=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Pinned.vue"], "names": ["t"], "mappings": ";;;;;wBAiCE,oBAUE;IATC,QAAQ,EAAE,eAAQ;IAClB,cAAY,IAAI,eAAM;IACvB,KAAK,mBAAC,UAAU,yBACe,eAAM,cAAc,eAAM;IACzD,IAAI,EAAC,QAAQ;IACZ,YAAU,EAAEA,MAAC,wCAAwC,cAAO,CAAC,KAAK;IAClE,OAAK,yDAAe,2CAAM;IAC1B,SAAO;uEAAgB,2CAAM;uEACN,2CAAM", "sourcesContent": ["<script>\n// Allow the user to pin a cluster by clicking it.\nexport default {\n  props: {\n    cluster: {\n      type:     Object,\n      required: true,\n    },\n    tabOrder: {\n      type:    Number,\n      default: null,\n    }\n  },\n\n  computed: {\n    pinned() {\n      return this.cluster.pinned;\n    }\n  },\n\n  methods: {\n    toggle() {\n      if ( this.pinned ) {\n        this.cluster.unpin();\n      } else {\n        this.cluster.pin();\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <i\n    :tabindex=\"tabOrder\"\n    :aria-checked=\"!!pinned\"\n    class=\"pin icon\"\n    :class=\"{'icon-pin-outlined': !pinned, 'icon-pin': pinned}\"\n    role=\"button\"\n    :aria-label=\"t('nav.ariaLabel.pinCluster', { cluster: cluster.label })\"\n    @click.stop.prevent=\"toggle\"\n    @keydown.enter.prevent=\"toggle\"\n    @keydown.space.prevent=\"toggle\"\n  />\n</template>\n\n<style lang=\"scss\" scoped>\n  .icon {\n    font-size: 14px;\n    transform: scaleX(-1);\n  }\n</style>\n"]}]}