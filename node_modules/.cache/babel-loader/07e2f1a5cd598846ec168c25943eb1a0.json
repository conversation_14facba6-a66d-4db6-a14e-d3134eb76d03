{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ShellInput.vue?vue&type=template&id=ffca249c", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ShellInput.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVzb2x2ZUNvbXBvbmVudCBhcyBfcmVzb2x2ZUNvbXBvbmVudCwgbWVyZ2VQcm9wcyBhcyBfbWVyZ2VQcm9wcywgb3BlbkJsb2NrIGFzIF9vcGVuQmxvY2ssIGNyZWF0ZUJsb2NrIGFzIF9jcmVhdGVCbG9jayB9IGZyb20gInZ1ZSIKCmV4cG9ydCBmdW5jdGlvbiByZW5kZXIoX2N0eCwgX2NhY2hlLCAkcHJvcHMsICRzZXR1cCwgJGRhdGEsICRvcHRpb25zKSB7CiAgY29uc3QgX2NvbXBvbmVudF9MYWJlbGVkSW5wdXQgPSBfcmVzb2x2ZUNvbXBvbmVudCgiTGFiZWxlZElucHV0IikKCiAgcmV0dXJuIChfb3BlbkJsb2NrKCksIF9jcmVhdGVCbG9jayhfY29tcG9uZW50X0xhYmVsZWRJbnB1dCwgX21lcmdlUHJvcHMoewogICAgdmFsdWU6ICRkYXRhLnVzZXJWYWx1ZSwKICAgICJvblVwZGF0ZTp2YWx1ZSI6IF9jYWNoZVswXSB8fCAoX2NhY2hlWzBdID0gJGV2ZW50ID0+ICgoJGRhdGEudXNlclZhbHVlKSA9ICRldmVudCkpCiAgfSwgX2N0eC4kYXR0cnMsIHsKICAgICJvblVwZGF0ZTp2YWx1ZSI6IF9jYWNoZVsxXSB8fCAoX2NhY2hlWzFdID0gJGV2ZW50ID0+ICgkb3B0aW9ucy51cGRhdGUoJGV2ZW50KSkpCiAgfSksIG51bGwsIDE2IC8qIEZVTExfUFJPUFMgKi8sIFsidmFsdWUiXSkpCn0="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ShellInput.vue"], "names": ["$attrs"], "mappings": ";;;;;wBAqFE,aAIE,yBAJF,YAIE;IAHQ,KAAK,EAAE,eAAS;4DAAT,eAAS;KAChBA,WAAM;IACb,gBAAY,uCAAE,eAAM,CAAC,MAAM", "sourcesContent": ["<script>\nimport { LabeledInput } from '@components/Form/LabeledInput';\n\nexport default {\n  emits: ['update:value'],\n\n  components: { LabeledInput },\n\n  props: {\n    value: {\n      type:    Array,\n      default: null,\n    }\n  },\n  /*\n      userValue is a string representation of args array, with spaces between each array item and single quotes around any items with whitespace\n      value input of [\"-c\", \"sleep 600\"]\n      is displayed as: \"-c 'sleep 600'\"\n\n      user input of \"-c \"sleep 600\"\" or \"-c 'sleep 600'\"\n      causes $emit 'input' of [\"-c\", \"sleep 600\"]\n  */\n  data() {\n    let userValue = '';\n\n    if ( this.value ) {\n      userValue = this.value.reduce((str, each) => {\n        if (each.includes(' ')) {\n          str += `'${ each }'`;\n        } else {\n          str += each;\n        }\n        str += ' ';\n\n        return str;\n      }, '').trim();\n    }\n\n    return { userValue };\n  },\n\n  methods: {\n    update(userValue) {\n      let out = null;\n\n      if ( userValue ) {\n        out = userValue.match(/('[^']+')|(\"[^\"]+\")|\\S+/g).map((string) => string.replace(/^'|'$|^\"|\"$/g, ''));\n      }\n      this.$emit('update:value', out);\n    },\n  }\n};\n\nexport const OPS = ['||', '&&', ';;', '|&', '&', ';', '(', ')', '|', '<', '>'];\nexport function reop(xs) {\n  return xs.map((s) => {\n    if ( OPS.includes(s) ) {\n      return { op: s };\n    } else {\n      return s;\n    }\n  });\n}\n\nexport function unparse(xs) {\n  return xs.map((s) => {\n    if ( s && typeof s === 'object' ) {\n      if ( Object.prototype.hasOwnProperty.call(s, 'pattern') ) {\n        return `\"${ s.pattern }\"`;\n      } else {\n        return s.op;\n      }\n    } else if ( /[\"\\s]/.test(s) && !/'/.test(s) ) {\n      return `'${ s.replace(/(['\\\\])/g, '\\\\$1') }'`;\n    } else if ( /[\"'\\s]/.test(s) ) {\n      return `\"${ s.replace(/([\"\\\\$`!])/g, '\\\\$1') }\"`;\n    } else {\n      return String(s).replace(/([\\\\$`()!#&*|])/g, '\\\\$1');\n    }\n  }).join(' ');\n}\n\n</script>\n\n<template>\n  <LabeledInput\n    v-model:value=\"userValue\"\n    v-bind=\"$attrs\"\n    @update:value=\"update($event)\"\n  />\n</template>\n"]}]}