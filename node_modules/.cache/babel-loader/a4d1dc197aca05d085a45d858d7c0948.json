{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Jump.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Jump.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Jump.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;EAErB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACd,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1B,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAErD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAClG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE3H,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;MACnI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAExF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACzB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;UAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC;MACJ,CAAC,CAAC;;MAEF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;MAEjB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACzB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEzG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC;IACJ;EACF,CAAC;AACH,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Jump.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport debounce from 'lodash/debounce';\nimport Group from '@shell/components/nav/Group';\nimport { isMac } from '@shell/utils/platform';\nimport { BOTH, TYPE_MODES } from '@shell/store/type-map';\nimport { COUNT } from '@shell/config/types';\n\nexport default {\n  emits: ['closeSearch'],\n\n  components: { Group },\n\n  data() {\n    return {\n      isMac,\n      value:  '',\n      groups: null,\n    };\n  },\n\n  watch: {\n    value() {\n      this.queueUpdate();\n    },\n  },\n\n  mounted() {\n    this.updateMatches();\n    this.queueUpdate = debounce(this.updateMatches, 250);\n\n    this.$refs.input.focus();\n  },\n\n  methods: {\n    updateMatches() {\n      const clusterId = this.$store.getters['clusterId'];\n      const productId = this.$store.getters['productId'];\n      const product = this.$store.getters['currentProduct'];\n\n      const allTypesByMode = this.$store.getters['type-map/allTypes'](productId, [TYPE_MODES.ALL]) || {};\n      const allTypes = allTypesByMode[TYPE_MODES.ALL];\n      const out = this.$store.getters['type-map/getTree'](productId, TYPE_MODES.ALL, allTypes, clusterId, BOTH, null, this.value);\n\n      // Suplement the output with count info. Usualy the `Type` component would handle this individualy... but scales real bad so give it\n      // some help\n      const counts = this.$store.getters[`${ product.inStore }/all`](COUNT)?.[0]?.counts || {};\n\n      out.forEach((o) => {\n        o.children?.forEach((t) => {\n          const count = counts[t.name];\n\n          t.count = count ? count.summary.count || 0 : null;\n          t.byNamespace = count ? count.namespaces : {};\n          t.revision = count ? count.revision : null;\n        });\n      });\n\n      this.groups = out;\n\n      // Hide top-level groups with no children (or one child that is an overview)\n      this.groups.forEach((g) => {\n        const isRoot = g.isRoot || g.name === 'Root';\n        const hidden = isRoot || g.children?.length === 0 || (g.children?.length === 1 && g.children[0].overview);\n\n        g.hidden = !!hidden;\n      });\n    }\n  },\n};\n</script>\n\n<template>\n  <div>\n    <p\n      id=\"describe-filter-resource-search\"\n      hidden\n    >\n      {{ t('nav.resourceSearch.filteringDescription') }}\n    </p>\n    <input\n      ref=\"input\"\n      v-model=\"value\"\n      :placeholder=\"t('nav.resourceSearch.placeholder')\"\n      class=\"search\"\n      role=\"textbox\"\n      :aria-label=\"t('nav.resourceSearch.label')\"\n      aria-describedby=\"describe-filter-resource-search\"\n      @keyup.esc=\"$emit('closeSearch')\"\n    >\n    <div class=\"results\">\n      <div\n        v-for=\"g in groups\"\n        :key=\"g.name\"\n        class=\"package\"\n      >\n        <Group\n          v-if=\"!g.hidden\"\n          :key=\"g.name\"\n          id-prefix=\"\"\n          :group=\"g\"\n          :can-collapse=\"false\"\n          :fixed-open=\"true\"\n          @close=\"$emit('closeSearch')\"\n        >\n          <template #accordion>\n            <h6>{{ g.label }}</h6>\n          </template>\n        </Group>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .search, .search:hover {\n    position: relative;\n    background-color: var(--dropdown-bg);\n    border-radius: 0;\n    box-shadow: none;\n  }\n\n  .search:focus-visible {\n    outline-offset: -2px;\n  }\n\n  .results {\n    margin-top: -1px;\n    overflow-y: auto;\n    padding: 10px;\n    color: var(--dropdown-text);\n    background-color: var(--dropdown-bg);\n    border: 1px solid var(--dropdown-border);\n    height: 75vh;\n  }\n</style>\n"]}]}