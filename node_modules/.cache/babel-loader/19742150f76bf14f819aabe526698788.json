{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/NamespaceFilter.vue?vue&type=template&id=4d1ee814&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/NamespaceFilter.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/NamespaceFilter.vue"], "names": ["$fetchState", "t"], "mappings": ";;;EA8tBQ,GAAG,EAAC,QAAQ;EAEZ,aAAW,EAAC,mBAAmB;EAC/B,KAAK,EAAC,WAAW;;;;;;EAgCjB,GAAG,EAAC,MAAM;EAEV,KAAK,EAAC,SAAS;;;;EAMf,KAAK,EAAC,wBAAwB;;;;EAI9B,KAAK,EAAC,sBAAsB;;;;EAY9B,KAAK,EAAC,kBAAkB;EACxB,aAAW,EAAC,iBAAiB;;qBAExB,KAAK,EAAC,aAAa;qBACjB,KAAK,EAAC,UAAU;;;EAiBnB,KAAK,EAAC,mBAAmB;;sBAIvB,KAAK,EAAC,gBAAgB;;;EAKxB,KAAK,EAAC,UAAU;;;EAUlB,GAAG,EAAC,SAAS;EACb,KAAK,EAAC,YAAY;EAClB,IAAI,EAAC,MAAM;;;;;EAoBP,KAAK,EAAC,YAAY;;;;EAIlB,KAAK,EAAC,SAAS;;;;EAIb,KAAK,EAAC,kBAAkB;;;;EAKxB,KAAK,EAAC,qBAAqB;;;;EAM/B,KAAK,EAAC,SAAS;EACf,aAAW,EAAC,wBAAwB;;;;;;;WAzLnCA,gBAAW,CAAC,OAAO;qBAD5B,oBAgMM;;QA9LJ,KAAK,EAAC,WAAW;QACjB,aAAW,EAAC,mBAAmB;QAC/B,QAAQ,EAAC,GAAG;QACX,WAAS,2CAAV,QAAkB;QACjB,OAAK,uCAAE,aAAI;;SAGJ,YAAM;2BADd,oBAIE;;cAFA,KAAK,EAAC,UAAU;cACf,OAAK,uCAAE,cAAK;;;;QAGf,gDAAgC;;QAChC,oBA+EM;UA9EJ,GAAG,EAAC,UAAU;UACd,KAAK,mBAAC,aAAa,eAEE,YAAM;UAD3B,aAAW,EAAC,qBAAqB;UAEhC,OAAK,uCAAE,eAAM;;UAEd,sDAAsC;;WAE9B,cAAK,CAAC,MAAM;6BADpB,oBAOM;;gBALJ,GAAG,EAAC,QAAQ;gBACZ,aAAW,EAAC,wBAAwB;gBACpC,KAAK,EAAC,WAAW;kCAEdC,MAAC;eAKO,wBAAe;+BAD5B,oBAOM;kBARN,kFAAgE;kBAChE,oBAOM;oBALJ,GAAG,EAAC,QAAQ;oBACZ,aAAW,EAAC,yBAAyB;oBACrC,KAAK,EAAC,WAAW;sCAEd,cAAK,IAAI,KAAK;;+BAInB,oBAgCM;kBAjCN,oDAAoC;iDACpC,oBAgCM,OAhCN,UAgCM;qBAxBI,WAAK;uCADb,oBAOM;;0BALJ,GAAG,EAAC,OAAO;0BACX,aAAW,EAAC,yBAAyB;0BACrC,KAAK,EAAC,iBAAiB;4CAEpBA,MAAC,4CAAqC,WAAK;;;uCAEhD,oBAgBM,6BAfc,cAAK,GAAf,EAAE,EAAE,CAAC;4CADf,oBAgBM;;wBAdJ,GAAG,EAAC,OAAO;wBACV,GAAG,EAAE,EAAE,CAAC,EAAE;wBACV,aAAW,sBAAsB,CAAC;wBACnC,KAAK,EAAC,UAAU;;wBAEhB,oBAAyB,8BAAjB,EAAE,CAAC,KAAK;;wBAChB,iGAAiF;;0BAExE,yBAAmB,IAAI,cAAK,CAAC,MAAM;2CAD5C,oBAME;;8BAJA,KAAK,EAAC,iBAAiB;8BACtB,aAAW,6BAA6B,CAAC;8BACzC,OAAK,aAAE,qBAAY,CAAC,EAAE,EAAE,MAAM;8BAC9B,WAAS,aAAE,6BAAoB,CAAC,EAAE,EAAE,MAAM;;;;;;+CA1B9B,gBAAO;;;;UA+B1B,oEAAoD;;WAE5C,YAAM;6CADd,oBAOM,OAPN,UAOM;kDADDA,MAAC,iCAAiC,YAAM;;2CAH1B,gBAAO;;;;YAMjB,YAAM;6BADf,oBAGE,KAHF,UAGE;6BACF,oBAGE,KAHF,UAGE;;;wBAEJ,oBAIE;UAFA,KAAK,EAAC,MAAM;UACX,UAAQ,uCAAE,aAAI;;;;YAFE,KAAK;;cAAX,IAAI,EAAf,IAAuB;;;;QAKzB,sCAAsB;;SAEd,YAAM;2BADd,oBAyFM,OAzFN,UAyFM;cApFJ,oBAkCM,OAlCN,UAkCM;gBAjCJ,oBAcM,OAdN,UAcM;kCAbJ,oBAOC;oBANC,GAAG,EAAC,QAAQ;iFACH,YAAM;oBACf,QAAQ,EAAC,GAAG;oBACZ,KAAK,EAAC,iBAAiB;oBACtB,OAAK,0CAAE,qDAAW;oBAClB,SAAO,uCAAE,wBAAe,CAAC,MAAM;;kCAJvB,YAAM;;;mBAOT,kBAAS;qCADjB,oBAIE;;wBAFA,KAAK,EAAC,iCAAiC;wBACtC,OAAK,uCAAE,YAAM;;;;;iBAIV,yBAAmB;mCAD3B,oBAQM,OARN,WAQM;sCAJJ,oBAGE,KAHF,WAGE;mDAFiBA,MAAC;;;mCAItB,oBAQM,OARN,WAQM;sBAJJ,oBAGE;wBAFA,KAAK,EAAC,iBAAiB;wBACtB,OAAK,uCAAE,cAAK;;;;;0CAInB,oBAA+B,SAA1B,KAAK,EAAC,iBAAiB;;cAC5B,oBA+CM,OA/CN,WA+CM;mCA1CJ,oBAkCM,6BAjCe,oBAAc,GAAzB,GAAG,EAAE,CAAC;wCADhB,oBAkCM;oBAhCH,EAAE,EAAE,GAAG,CAAC,SAAS;oBACjB,GAAG,EAAE,GAAG,CAAC,EAAE;oBACZ,QAAQ,EAAC,GAAG;oBACZ,KAAK,mBAAC,WAAW;2BAEoB,GAAG,CAAC,QAAQ;+BAAiC,oBAAc,CAAC,MAAM,WAAW,GAAG,CAAC,QAAQ;;oBAD7H,QAAQ,EAAE,GAAG,CAAC,OAAO;oBAKrB,aAAW,uBAAuB,CAAC;oBACnC,OAAK,aAAE,GAAG,CAAC,OAAO,IAAI,qBAAY,CAAC,GAAG;oBACtC,WAAS,aAAE,GAAG,CAAC,OAAO,IAAI,kBAAS,CAAC,MAAM;oBAC1C,SAAO,aAAE,uBAAc,CAAC,MAAM,EAAE,GAAG;;qBAG5B,GAAG,CAAC,IAAI,KAAK,4BAAsB,CAAC,OAAO;uCADnD,oBAGE,OAHF,WAGE;uCACF,oBAaM,OAbN,WAaM;2BARI,GAAG,CAAC,IAAI,KAAK,4BAAsB,CAAC,SAAS;6CADrD,oBAGE,KAHF,WAGE;;;0BACF,oBAA0B,8BAAlB,GAAG,CAAC,KAAK;;2BAET,GAAG,CAAC,QAAQ;6CADpB,oBAGE,KAHF,WAGE;;;;;;iBAIE,oBAAc,CAAC,MAAM;mCAD7B,oBAMM,OANN,WAMM,mBADDA,MAAC", "sourcesContent": ["<script>\nimport { mapGetters } from 'vuex';\nimport { NAMESPACE_FILTERS, ALL_NAMESPACES } from '@shell/store/prefs';\nimport { NAMESPACE, MANAGEMENT } from '@shell/config/types';\nimport { sortBy } from '@shell/utils/sort';\nimport { isArray, addObjects, findBy, filterBy } from '@shell/utils/array';\nimport {\n  NAMESPACE_FILTER_ALL_USER as ALL_USER,\n  NAMESPACE_FILTER_ALL as ALL,\n  NAMESPACE_FILTER_ALL_SYSTEM as ALL_SYSTEM,\n  NAMESPACE_FILTER_ALL_ORPHANS as ALL_ORPHANS,\n  NAMESPACE_FILTER_NAMESPACED_YES as NAMESPACED_YES,\n  NAMESPACE_FILTER_NAMESPACED_NO as NAMESPACED_NO,\n  createNamespaceFilterKey,\n  NAMESPACE_FILTER_KINDS,\n  NAMESPACE_FILTER_NS_FULL_PREFIX,\n  NAMESPACE_FILTER_P_FULL_PREFIX,\n} from '@shell/utils/namespace-filter';\nimport { KEY } from '@shell/utils/platform';\nimport pAndNFiltering from '@shell/plugins/steve/projectAndNamespaceFiltering.utils';\nimport { SETTING } from '@shell/config/settings';\nimport paginationUtils from '@shell/utils/pagination-utils';\n\nconst forcedNamespaceValidTypes = [NAMESPACE_FILTER_KINDS.DIVIDER, NAMESPACE_FILTER_KINDS.PROJECT, NAMESPACE_FILTER_KINDS.NAMESPACE];\n\nexport default {\n\n  data() {\n    return {\n      isOpen:              false,\n      filter:              '',\n      hidden:              0,\n      total:               0,\n      activeElement:       null,\n      cachedFiltered:      [],\n      NAMESPACE_FILTER_KINDS,\n      namespaceFilterMode: undefined,\n    };\n  },\n\n  async fetch() {\n    // Determine if filtering by specific namespaces/projects is required\n    // This is done once and up front\n    // - it doesn't need to be re-active\n    // - added it as a computed caused massive amounts of churn around the `filtered` watcher\n    await this.$store.dispatch('management/find', { type: MANAGEMENT.SETTING, id: SETTING.UI_PERFORMANCE });\n    this.namespaceFilterMode = this.calcNamespaceFilterMode();\n  },\n\n  computed: {\n    ...mapGetters(['currentProduct']),\n\n    hasFilter() {\n      return this.filter.length > 0;\n    },\n\n    paginatedListFilterMode() {\n      return this.$store.getters[`${ this.currentProduct.inStore }/paginationEnabled`](this.$route.params?.resource) ? paginationUtils.validNsProjectFilters : null;\n    },\n\n    filtered() {\n      let out = this.options;\n\n      out = out.filter((item) => {\n        // Filter out anything not applicable to singleton selection\n        if (this.namespaceFilterMode?.length) {\n          // We always show dividers, projects and namespaces\n          if (!forcedNamespaceValidTypes.includes(item.kind)) {\n            const validCustomType = this.namespaceFilterMode.find((prefix) => item.kind.startsWith(prefix));\n\n            if (!validCustomType) {\n              // Hide any invalid option that's not selected\n              return this.value.findIndex((v) => v.id === item.id) >= 0;\n            }\n          }\n        }\n\n        // Filter by the current filter\n        if (this.hasFilter) {\n          return item.kind !== NAMESPACE_FILTER_KINDS.SPECIAL && item.label.toLowerCase().includes(this.filter.toLowerCase());\n        }\n\n        return true;\n      });\n\n      if (out?.[0]?.kind === NAMESPACE_FILTER_KINDS.DIVIDER) {\n        out.splice(0, 1);\n      }\n\n      const mapped = this.value.reduce((m, v) => {\n        m[v.id] = v;\n\n        return m;\n      }, {});\n\n      // Mark all of the selected options\n      out.forEach((i) => {\n        i.selected = !!mapped[i.id] || (i.id === ALL && this.value && this.value.length === 0);\n        i.elementId = (i.id || '').replace('://', '_');\n        i.enabled = true;\n        // Are we in restricted resource type mode, if so is this an allowed type?\n        if (this.namespaceFilterMode?.length) {\n          const isLastSelected = i.selected && (i.id === ALL || this.value.length === 1);\n          const kindAllowed = this.namespaceFilterMode.find((f) => f === i.kind);\n          const isNotInProjectGroup = i.id === ALL_ORPHANS;\n\n          i.enabled = (!isLastSelected && kindAllowed) && !isNotInProjectGroup;\n        } else if (this.paginatedListFilterMode?.length) {\n          i.enabled = !!i.id && paginationUtils.validateNsProjectFilter(i.id);\n        }\n      });\n\n      return out;\n    },\n\n    tooltip() {\n      if (this.isOpen || (this.total + this.hidden) === 0) {\n        return null;\n      }\n\n      let tooltip = '<div class=\"ns-filter-tooltip\">';\n\n      (this.value || []).forEach((v) => {\n        tooltip += `<div class=\"ns-filter-tooltip-item\"><div>${ v.label }</div></div>`;\n      });\n\n      tooltip += '</div>';\n\n      return {\n        content:   tooltip,\n        placement: 'bottom',\n        delay:     { show: 500 }\n      };\n    },\n\n    key() {\n      return createNamespaceFilterKey(this.$store.getters['clusterId'], this.currentProduct);\n    },\n\n    options() {\n      const t = this.$store.getters['i18n/t'];\n      let out = [];\n      const inStore = this.$store.getters['currentStore'](NAMESPACE);\n\n      const params = { ...this.$route.params };\n      const resource = params.resource;\n\n      // Sometimes, different pages may have different namespaces to filter\n      const notFilterNamespaces = this.$store.getters[`type-map/optionsFor`](resource).notFilterNamespace || [];\n\n      // TODO: Add return info\n      if (this.currentProduct?.customNamespaceFilter && this.currentProduct?.inStore) {\n        // Sometimes the component can show before the 'currentProduct' has caught up, so access the product via the getter rather\n        // than caching it in the `fetch`\n\n        // The namespace display on the list and edit pages should be the same as in the namespaceFilter component\n        if (this.$store.getters[`${ this.currentProduct.inStore }/filterNamespace`]) {\n          const allNamespaces = this.$store.getters[`${ this.currentProduct.inStore }/filterNamespace`](notFilterNamespaces);\n\n          this.$store.commit('changeAllNamespaces', allNamespaces);\n        }\n\n        return this.$store.getters[`${ this.currentProduct.inStore }/namespaceFilterOptions`]({\n          addNamespace,\n          divider,\n          notFilterNamespaces\n        });\n      }\n\n      // TODO: Add return info\n      if (!this.currentProduct?.hideSystemResources) {\n        out = [\n          {\n            id:    ALL,\n            kind:  NAMESPACE_FILTER_KINDS.SPECIAL,\n            label: t('nav.ns.all'),\n          },\n          {\n            id:    ALL_USER,\n            kind:  NAMESPACE_FILTER_KINDS.SPECIAL,\n            label: t('nav.ns.user'),\n          },\n          {\n            id:    ALL_SYSTEM,\n            kind:  NAMESPACE_FILTER_KINDS.SPECIAL,\n            label: t('nav.ns.system'),\n          },\n          {\n            id:    NAMESPACED_YES,\n            kind:  NAMESPACE_FILTER_KINDS.SPECIAL,\n            label: t('nav.ns.namespaced'),\n          },\n          {\n            id:    NAMESPACED_NO,\n            kind:  NAMESPACE_FILTER_KINDS.SPECIAL,\n            label: t('nav.ns.clusterLevel'),\n          },\n        ];\n\n        divider(out);\n      }\n\n      if (!inStore) {\n        return out;\n      }\n\n      let namespaces = sortBy(\n        this.$store.getters[`${ inStore }/all`](NAMESPACE),\n        ['nameDisplay']\n      );\n\n      namespaces = this.filterNamespaces(namespaces);\n\n      // isRancher = mgmt schemas are loaded and there's a project schema\n      if (this.$store.getters['isRancher']) {\n        const cluster = this.$store.getters['currentCluster'];\n        let projects = this.$store.getters['management/all'](\n          MANAGEMENT.PROJECT\n        );\n\n        projects = projects.filter((p) => {\n          return this.currentProduct?.hideSystemResources ? !p.isSystem && p.spec.clusterName === cluster.id : p.spec.clusterName === cluster.id;\n        });\n        projects = sortBy(filterBy(projects, 'spec.clusterName', cluster.id), [\n          'nameDisplay',\n        ]);\n        const projectsById = {};\n        const namespacesByProject = {};\n        let firstProject = true;\n\n        namespacesByProject[null] = []; // For namespaces not in a project\n        for (const project of projects) {\n          projectsById[project.metadata.name] = project;\n        }\n\n        for (const namespace of namespaces) {\n          let projectId = namespace.projectId;\n\n          if (!projectId || !projectsById[projectId]) {\n            // If there's a projectId but that project doesn't exist, treat it like no project\n            projectId = null;\n          }\n\n          let entry = namespacesByProject[projectId];\n\n          if (!entry) {\n            entry = [];\n            namespacesByProject[namespace.projectId] = entry;\n          }\n          entry.push(namespace);\n        }\n\n        for (const project of projects) {\n          const id = project.metadata.name;\n\n          if (firstProject) {\n            firstProject = false;\n          } else {\n            divider(out);\n          }\n\n          out.push({\n            id:    `${ NAMESPACE_FILTER_P_FULL_PREFIX }${ id }`,\n            kind:  NAMESPACE_FILTER_KINDS.PROJECT,\n            label: t('nav.ns.project', { name: project.nameDisplay }),\n          });\n\n          const forThisProject = namespacesByProject[id] || [];\n\n          addNamespace(out, forThisProject);\n        }\n\n        const orphans = namespacesByProject[null];\n\n        if (orphans.length) {\n          if (!firstProject) {\n            divider(out);\n          }\n\n          out.push({\n            id:       ALL_ORPHANS,\n            kind:     NAMESPACE_FILTER_KINDS.PROJECT,\n            label:    t('nav.ns.orphan'),\n            disabled: true,\n          });\n\n          addNamespace(out, orphans);\n        }\n      } else {\n        addNamespace(out, namespaces);\n      }\n\n      return out;\n\n      function addNamespace(out, namespaces) {\n        if (!isArray(namespaces)) {\n          namespaces = [namespaces];\n        }\n\n        addObjects(\n          out,\n          namespaces.map((namespace) => {\n            return {\n              id:    `${ NAMESPACE_FILTER_NS_FULL_PREFIX }${ namespace.id }`,\n              kind:  NAMESPACE_FILTER_KINDS.NAMESPACE,\n              label: t('nav.ns.namespace', { name: namespace.nameDisplay }),\n            };\n          })\n        );\n      }\n\n      function divider(out) {\n        out.push({\n          kind:     NAMESPACE_FILTER_KINDS.DIVIDER,\n          label:    `Divider ${ out.length }`,\n          disabled: true,\n        });\n      }\n    },\n\n    isSingleSpecial() {\n      return this.value && this.value.length === 1 && this.value[0].kind === NAMESPACE_FILTER_KINDS.SPECIAL;\n    },\n\n    value: {\n      get() {\n        // Use last picked filter from user preferences\n        const prefs = this.$store.getters['prefs/get'](NAMESPACE_FILTERS);\n        const values = prefs && prefs[this.key] ? prefs[this.key] : this.defaultOption();\n        const options = this.options;\n\n        // Remove values that are not valid options\n        const filters = values\n          .map((value) => {\n            return findBy(options, 'id', value);\n          })\n          .filter((x) => !!x);\n\n        return filters;\n      },\n\n      set(neu) {\n        const old = (this.value || []).slice();\n\n        neu = neu.filter((x) => !!x.id);\n\n        const last = neu[neu.length - 1];\n        const lastIsSpecial = last?.kind === NAMESPACE_FILTER_KINDS.SPECIAL;\n        const hadUser = !!old.find((x) => x.id === ALL_USER);\n        const hadAll = !!old.find((x) => x.id === ALL);\n\n        if (lastIsSpecial) {\n          neu = [last];\n        }\n\n        if (neu.length > 1) {\n          neu = neu.filter((x) => x.kind !== NAMESPACE_FILTER_KINDS.SPECIAL);\n        }\n\n        if (neu.find((x) => x.id === 'all')) {\n          neu = [];\n        }\n\n        let ids;\n\n        // If there was something selected and you remove it, go back to user by default\n        // Unless it was user or all\n        if (neu.length === 0 && !hadUser && !hadAll) {\n          ids = this.defaultOption();\n        } else {\n          ids = neu.map((x) => x.id);\n        }\n\n        this.$nextTick(() => {\n          this.$store.dispatch('switchNamespaces', {\n            ids,\n            key: this.key\n          });\n        });\n      },\n    }\n  },\n\n  beforeUnmount() {\n    this.removeCloseKeyHandler();\n  },\n\n  mounted() {\n    this.layout();\n  },\n\n  watch: {\n    value(neu) {\n      this.layout();\n    },\n\n    /**\n     * When there are thousands of entries certain actions (drop down opened, selection changed, etc) take a long time to complete (upwards\n     * of 5 seconds)\n     *\n     * This is caused by churn of the filtered and options computed properties causing multiple renders for each action.\n     *\n     * To break this multiple-render per cycle behaviour detatch `filtered` from the value used in `v-for`.\n     *\n     */\n    filtered(neu) {\n      if (!!neu) {\n        this.cachedFiltered = neu;\n      }\n    }\n  },\n\n  methods: {\n    filterNamespaces(namespaces) {\n      if (this.$store.getters['prefs/get'](ALL_NAMESPACES)) {\n        // If all namespaces options are turned on in the user preferences,\n        // return all namespaces including system namespaces and RBAC\n        // management namespaces.\n        return namespaces;\n      }\n\n      return namespaces.filter((namespace) => {\n        // Otherwise only filter out obscure namespaces, such as namespaces\n        // that Rancher uses to manage RBAC for projects, which should not be\n        // edited or deleted by Rancher users.\n        return !namespace.isObscure;\n      });\n    },\n    // Layout the content in the dropdown box to best use the space to show the selection\n    layout() {\n      this.$nextTick(() => {\n        // One we have re-rendered, see what we can fit in the control to show the selected namespaces\n        if (this.$refs.values) {\n          const container = this.$refs.values;\n          const overflow = container.scrollWidth > container.offsetWidth;\n          let hidden = 0;\n\n          const dropdown = this.$refs.dropdown;\n          // Remove padding and dropdown arrow size\n          const maxWidth = dropdown.offsetWidth - 20 - 24;\n\n          // If we are overflowing, then allow some space for the +N indicator\n          const itemCount = this.$refs.value ? this.$refs.value.length : 0;\n          let currentWidth = 0;\n          let show = true;\n\n          this.total = 0;\n\n          for (let i = 0; i < itemCount; i++) {\n            const item = this.$refs.value[i];\n            let itemWidth = item.offsetWidth + 10;\n\n            // If this is the first item and we have overflow then add on some space for the +N\n            if (i === 0 && overflow) {\n              itemWidth += 40;\n            }\n\n            currentWidth += itemWidth;\n\n            if (show) {\n              if (i === 0) {\n                // Can't even fit the first item in\n                if (itemWidth > maxWidth) {\n                  show = false;\n                  this.total = this.value.length;\n                }\n              } else {\n                show = currentWidth < maxWidth;\n              }\n            }\n\n            hidden += show ? 0 : 1;\n            item.style.visibility = show ? 'visible' : 'hidden';\n          }\n\n          this.hidden = this.total > 0 ? 0 : hidden;\n        }\n      });\n    },\n    addCloseKeyHandler() {\n      document.addEventListener('keyup', this.closeKeyHandler);\n    },\n    removeCloseKeyHandler() {\n      document.removeEventListener('keyup', this.closeKeyHandler);\n    },\n    closeKeyHandler(e) {\n      if (e.keyCode === KEY.ESCAPE ) {\n        this.close();\n      }\n    },\n    // Keyboard support\n    itemKeyHandler(e, opt) {\n      if (e.keyCode === KEY.DOWN ) {\n        e.preventDefault();\n        e.stopPropagation();\n        this.down();\n      } else if (e.keyCode === KEY.UP ) {\n        e.preventDefault();\n        e.stopPropagation();\n        this.up();\n      } else if (e.keyCode === KEY.SPACE || e.keyCode === KEY.CR) {\n        if (this.namespaceFilterMode && !opt.enabled) {\n          return;\n        }\n        e.preventDefault();\n        e.stopPropagation();\n        this.selectOption(opt);\n        e.target.focus();\n      }\n    },\n    inputKeyHandler(e) {\n      switch (e.keyCode) {\n      case KEY.DOWN:\n        e.preventDefault();\n        e.stopPropagation();\n        this.down(true);\n        break;\n      case KEY.TAB:\n        // Tab out of the input box\n        this.close();\n        e.target.blur();\n        break;\n      case KEY.CR:\n        if (this.filtered.length === 1) {\n          this.selectOption(this.filtered[0]);\n          this.filter = '';\n        }\n        break;\n      }\n    },\n    mouseOver(event) {\n      const el = event?.path?.find((e) => e.classList.contains('ns-option'));\n\n      this.activeElement = el;\n    },\n    setActiveElement(el) {\n      if (!el?.focus) {\n        return;\n      }\n\n      el.focus();\n      this.activeElement = null;\n    },\n    down(input) {\n      const exising = this.activeElement || document.activeElement;\n\n      // Focus the first element in the list\n      if (input || !exising) {\n        if (this.$refs.options) {\n          const c = this.$refs.options.children;\n\n          if (c && c.length > 0) {\n            this.setActiveElement(c[0]);\n          }\n        }\n      } else {\n        let next = exising.nextSibling;\n\n        if (next?.children?.length) {\n          const item = next.children[0];\n\n          // Skip over dividers (assumes we don't have two dividers next to each other)\n          if (item.classList.contains('ns-divider')) {\n            next = next.nextSibling;\n          }\n        }\n\n        if (next?.focus) {\n          this.setActiveElement(next);\n        }\n      }\n    },\n    up() {\n      if (document.activeElement) {\n        let prev = document.activeElement.previousSibling;\n\n        if (prev?.children?.length) {\n          const item = prev.children[0];\n\n          if (item.classList.contains('ns-divider')) {\n            prev = prev.previousSibling;\n          }\n        }\n\n        if (prev?.focus) {\n          this.setActiveElement(prev);\n        }\n      }\n    },\n    toggle() {\n      if (this.isOpen) {\n        this.close();\n      } else {\n        this.open();\n      }\n    },\n    open() {\n      this.isOpen = true;\n      this.$nextTick(() => {\n        this.focusFilter();\n      });\n      this.addCloseKeyHandler();\n      this.layout();\n    },\n    focusFilter() {\n      this.$refs.filter.focus();\n    },\n    close() {\n      this.isOpen = false;\n      this.activeElement = null;\n      this.removeCloseKeyHandler();\n      this.layout();\n    },\n    clear() {\n      this.value = [];\n    },\n    selectOption(option) {\n      // Ignore click for a divider\n      if (option.kind === NAMESPACE_FILTER_KINDS.DIVIDER) {\n        return;\n      }\n\n      const current = this.value;\n\n      // Remove invalid\n      if (!!this.namespaceFilterMode?.length) {\n        this.value.forEach((v) => {\n          if (!this.namespaceFilterMode.find((f) => f === v.kind)) {\n            const index = current.findIndex((c) => c.id === v.id);\n\n            current.splice(index, 1);\n          }\n        });\n      }\n\n      const exists = current.findIndex((v) => v.id === option.id);\n\n      // Remove if it exists (or always add if in singleton mode - we've reset the list above)\n      if (exists !== -1) {\n        current.splice(exists, 1);\n      } else {\n        current.push(option);\n      }\n\n      this.value = current;\n\n      if (document.activeElement) {\n        document.activeElement.blur();\n      }\n    },\n    handleValueMouseDown(ns, event) {\n      this.removeOption(ns, event);\n\n      if (this.value.length === 0) {\n        this.open();\n      }\n    },\n\n    removeOption(ns, event) {\n      this.selectOption(ns);\n      event.preventDefault();\n      event.stopPropagation();\n    },\n\n    defaultOption() {\n      // Note - This is one place where a default ns/project filter value is provided (ALL_USER)\n      // There's also..\n      // - dashboard root store `loadCluster` --> when `updateNamespaces` is dispatched\n      // - harvester root store `loadCluster` --> when `updateNamespaces` is dispatched (can be discarded)\n      // Due to this, we can't really set a nicer default when forced ns/project filtering is on (ALL_USER is invalid)\n      if (this.currentProduct?.customNamespaceFilter) {\n        return [];\n      }\n\n      return [ALL_USER];\n    },\n\n    calcNamespaceFilterMode() {\n      if (pAndNFiltering.isEnabled(this.$store.getters)) {\n        return [NAMESPACE_FILTER_KINDS.NAMESPACE, NAMESPACE_FILTER_KINDS.PROJECT];\n      }\n\n      return null;\n    },\n  }\n};\n</script>\n\n<template>\n  <div\n    v-if=\"!$fetchState.pending\"\n    class=\"ns-filter\"\n    data-testid=\"namespaces-filter\"\n    tabindex=\"0\"\n    @mousedown.prevent\n    @focus=\"open()\"\n  >\n    <div\n      v-if=\"isOpen\"\n      class=\"ns-glass\"\n      @click=\"close()\"\n    />\n\n    <!-- Select Dropdown control -->\n    <div\n      ref=\"dropdown\"\n      class=\"ns-dropdown\"\n      data-testid=\"namespaces-dropdown\"\n      :class=\"{ 'ns-open': isOpen }\"\n      @click=\"toggle()\"\n    >\n      <!-- No filters found or available -->\n      <div\n        v-if=\"value.length === 0\"\n        ref=\"values\"\n        data-testid=\"namespaces-values-none\"\n        class=\"ns-values\"\n      >\n        {{ t('nav.ns.all') }}\n      </div>\n\n      <!-- Filtered by set with custom label E.g. \"All namespaces\" -->\n      <div\n        v-else-if=\"isSingleSpecial\"\n        ref=\"values\"\n        data-testid=\"namespaces-values-label\"\n        class=\"ns-values\"\n      >\n        {{ value[0].label }}\n      </div>\n\n      <!-- All the selected namespaces -->\n      <div\n        v-else\n        ref=\"values\"\n        v-clean-tooltip=\"tooltip\"\n        data-testid=\"namespaces-values\"\n        class=\"ns-values\"\n      >\n        <div\n          v-if=\"total\"\n          ref=\"total\"\n          data-testid=\"namespaces-values-total\"\n          class=\"ns-value ns-abs\"\n        >\n          {{ t('namespaceFilter.selected.label', { total }) }}\n        </div>\n        <div\n          v-for=\"(ns, j) in value\"\n          ref=\"value\"\n          :key=\"ns.id\"\n          :data-testid=\"`namespaces-value-${j}`\"\n          class=\"ns-value\"\n        >\n          <div>{{ ns.label }}</div>\n          <!-- block user from removing the last selection if ns forced filtering is on -->\n          <i\n            v-if=\"!namespaceFilterMode || value.length > 1\"\n            class=\"icon icon-close\"\n            :data-testid=\"`namespaces-values-close-${j}`\"\n            @click=\"removeOption(ns, $event)\"\n            @mousedown=\"handleValueMouseDown(ns, $event)\"\n          />\n        </div>\n      </div>\n\n      <!-- Inform user if more namespaces are selected -->\n      <div\n        v-if=\"hidden > 0\"\n        ref=\"more\"\n        v-clean-tooltip=\"tooltip\"\n        class=\"ns-more\"\n      >\n        {{ t('namespaceFilter.more', { more: hidden }) }}\n      </div>\n      <i\n        v-if=\"!isOpen\"\n        class=\"icon icon-chevron-down\"\n      />\n      <i\n        v-else\n        class=\"icon icon-chevron-up\"\n      />\n    </div>\n    <button\n      v-shortkey.once=\"['n']\"\n      class=\"hide\"\n      @shortkey=\"open()\"\n    />\n\n    <!-- Dropdown menu -->\n    <div\n      v-if=\"isOpen\"\n      class=\"ns-dropdown-menu\"\n      data-testid=\"namespaces-menu\"\n    >\n      <div class=\"ns-controls\">\n        <div class=\"ns-input\">\n          <input\n            ref=\"filter\"\n            v-model=\"filter\"\n            tabindex=\"0\"\n            class=\"ns-filter-input\"\n            @click=\"focusFilter\"\n            @keydown=\"inputKeyHandler($event)\"\n          >\n          <i\n            v-if=\"hasFilter\"\n            class=\"ns-filter-clear icon icon-close\"\n            @click=\"filter = ''\"\n          />\n        </div>\n        <div\n          v-if=\"namespaceFilterMode\"\n          class=\"ns-singleton-info\"\n        >\n          <i\n            v-clean-tooltip=\"t('resourceList.nsFilterToolTip')\"\n            class=\"icon icon-info\"\n          />\n        </div>\n        <div\n          v-else\n          class=\"ns-clear\"\n        >\n          <i\n            class=\"icon icon-close\"\n            @click=\"clear()\"\n          />\n        </div>\n      </div>\n      <div class=\"ns-divider mt-0\" />\n      <div\n        ref=\"options\"\n        class=\"ns-options\"\n        role=\"list\"\n      >\n        <div\n          v-for=\"(opt, i) in cachedFiltered\"\n          :id=\"opt.elementId\"\n          :key=\"opt.id\"\n          tabindex=\"0\"\n          class=\"ns-option\"\n          :disabled=\"opt.enabled ? null : true\"\n          :class=\"{\n            'ns-selected': opt.selected,\n            'ns-single-match': cachedFiltered.length === 1 && !opt.selected,\n          }\"\n          :data-testid=\"`namespaces-option-${i}`\"\n          @click=\"opt.enabled && selectOption(opt)\"\n          @mouseover=\"opt.enabled && mouseOver($event)\"\n          @keydown=\"itemKeyHandler($event, opt)\"\n        >\n          <div\n            v-if=\"opt.kind === NAMESPACE_FILTER_KINDS.DIVIDER\"\n            class=\"ns-divider\"\n          />\n          <div\n            v-else\n            class=\"ns-item\"\n          >\n            <i\n              v-if=\"opt.kind === NAMESPACE_FILTER_KINDS.NAMESPACE\"\n              class=\"icon icon-folder\"\n            />\n            <div>{{ opt.label }}</div>\n            <i\n              v-if=\"opt.selected\"\n              class=\"icon icon-checkmark\"\n            />\n          </div>\n        </div>\n        <div\n          v-if=\"cachedFiltered.length === 0\"\n          class=\"ns-none\"\n          data-testid=\"namespaces-option-none\"\n        >\n          {{ t('namespaceFilter.noMatchingOptions') }}\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  $ns_dropdown_size: 24px;\n\n  .ns-abs {\n    position: absolute;\n  }\n\n  .ns-filter {\n    width: 280px;\n    display: inline-block;\n\n    .ns-glass {\n      top: 0;\n      bottom: 0;\n      left: 0;\n      right: 0;\n      opacity: 0;\n      position: fixed;\n\n      z-index: z-index('overContent');\n    }\n\n    .ns-controls {\n      align-items: center;\n      display: flex;\n    }\n\n    .ns-clear {\n      &:hover {\n        color: var(--primary);\n        cursor: pointer;\n      }\n    }\n\n    .ns-singleton-info, .ns-clear {\n      align-items: center;\n      display: flex;\n      > i {\n        padding-right: 5px;\n      }\n    }\n\n    .ns-input {\n      flex: 1;\n      padding: 5px;\n      position: relative;\n    }\n\n    .ns-filter-input {\n      height: 24px;\n    }\n\n    .ns-filter-clear {\n      cursor: pointer;\n      position: absolute;\n      right: 10px;\n      top: 5px;\n      line-height: 24px;\n      text-align: center;\n      width: 24px;\n    }\n\n    .ns-dropdown-menu {\n      background-color: var(--header-bg);\n      border: 1px solid var(--primary-border);\n      border-bottom-left-radius: var(--border-radius);\n      border-bottom-right-radius: var(--border-radius);\n      color: var(--header-btn-text);\n      margin-top: -1px;\n      padding-bottom: 10px;\n      position: relative;\n      z-index: z-index('dropdownOverlay');\n\n      .ns-options {\n        max-height: 50vh;\n        overflow-y: auto;\n\n        .ns-none {\n          color: var(--muted);\n          padding: 0 10px;\n        }\n      }\n\n      .ns-divider {\n        border-top: 1px solid var(--border);\n        cursor: default;\n        margin-top: 10px;\n        padding-bottom: 10px;\n      }\n\n      .ns-option {\n\n        &[disabled] {\n          cursor: default;\n        }\n\n        &:not([disabled]) {\n          &:focus {\n            background-color: var(--dropdown-hover-bg);\n            color: var(--dropdown-hover-text);\n          }\n          .ns-item {\n             &:hover, &:focus {\n              background-color: var(--dropdown-hover-bg);\n              color: var(--dropdown-hover-text);\n              cursor: pointer;\n\n              > i {\n                color: var(--dropdown-hover-text);\n              }\n            }\n          }\n\n          &.ns-selected {\n            &:hover,&:focus {\n              .ns-item {\n                > * {\n                  background-color: var(--dropdown-hover-bg);\n                  color: var(--dropdown-hover-text);\n                }\n              }\n            }\n          }\n\n          &.ns-selected:not(:hover) {\n            .ns-item {\n              > * {\n                color: var(--primary);\n              }\n            }\n\n            &:focus {\n              .ns-item {\n                > * {\n                  color: var(--dropdown-hover-text);\n                }\n              }\n            }\n          }\n        }\n\n        .ns-item {\n          align-items: center;\n          display: flex;\n          height: 24px;\n          line-height: 24px;\n          padding: 0 10px;\n\n          > i {\n            color: var(--muted);\n            margin: 0 5px;\n          }\n\n          > div {\n            flex: 1;\n            overflow: hidden;\n            text-overflow: ellipsis;\n            white-space: nowrap;\n          }\n\n        }\n\n        &.ns-single-match {\n          .ns-item {\n            background-color: var(--dropdown-hover-bg);\n            > * {\n              color: var(--dropdown-hover-text);\n            }\n          }\n        }\n      }\n    }\n\n    .ns-dropdown {\n      align-items: center;\n      display: flex;\n      border: 1px solid var(--header-border);\n      border-radius: var(--border-radius);\n      color: var(--header-btn-text);\n      cursor: pointer;\n      height: 40px;\n      padding: 0 10px;\n      position: relative;\n      z-index: z-index('dropdownOverlay');\n\n      &.ns-open {\n        border-bottom-left-radius: 0;\n        border-bottom-right-radius: 0;\n        border-color: var(--primary-border);\n      }\n\n      > .ns-values {\n        flex: 1;\n      }\n\n      &:hover {\n        > i {\n          color: var(--primary);\n        }\n      }\n\n      > i {\n        height: $ns_dropdown_size;\n        width: $ns_dropdown_size;\n        cursor: pointer;\n        text-align: center;\n        line-height: $ns_dropdown_size;\n      }\n\n      .ns-more {\n        border: 1px solid var(--header-border);\n        border-radius: 5px;\n        padding: 2px 8px;\n        margin-left: 4px;\n      }\n\n      .ns-values {\n        display: flex;\n        overflow: hidden;\n\n        .ns-value {\n          align-items: center;\n          background-color: rgba(0,0,0,.05);\n          border: 1px solid var(--header-border);\n          border-radius: 5px;\n          color: var(--tag-text);\n          display: flex;\n          line-height: 20px;\n          padding: 2px 5px;\n          white-space: nowrap;\n\n          > i {\n            margin-left: 5px;\n\n            &:hover {\n              color: var(--primary);\n            };\n          }\n\n          // Spacing between tags\n          &:not(:last-child) {\n            margin-right: 5px;\n          }\n        }\n      }\n    }\n  }\n</style>\n<style lang=\"scss\">\n  .v-popper__popper {\n    .ns-filter-tooltip {\n      background-color: var(--body-bg);\n      margin: -6px;\n      padding: 6px;\n\n      .ns-filter-tooltip-item {\n        > div {\n          background-color: rgba(0,0,0,.05);\n          border: 1px solid var(--header-border);\n          border-radius: 5px;\n          color: var(--tag-text);\n          display: inline-block;\n          line-height: 20px;\n          padding: 2px 5px;\n          white-space: nowrap;\n          margin: 4px 0;\n        }\n      }\n    }\n  }\n</style>\n"]}]}