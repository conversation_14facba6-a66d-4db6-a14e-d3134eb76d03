{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceList/ResourceLoadingIndicator.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceList/ResourceLoadingIndicator.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceList/ResourceLoadingIndicator.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE3C,CAAC,CAAC;CACD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;CAC5F,CAAC;AACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;EAEb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEhC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAElE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACpB,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;QAC7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAE1F,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACV,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEzE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACvC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;;QAEhC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,EAAE,CAAC,CAAC;IACP,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7E,CAAC,EAAE,CAAC,CAAC;IACP,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;AACH,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceList/ResourceLoadingIndicator.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { COUNT } from '@shell/config/types';\n\n/**\n * Loading Indicator for resources - used when we are loading resources incrementally, by page\n */\nexport default {\n\n  name: 'ResourceLoadingIndicator',\n\n  props: {\n    resources: {\n      type:     Array,\n      required: true,\n    },\n    indeterminate: {\n      type:    Boolean,\n      default: false,\n    },\n  },\n\n  data() {\n    const inStore = this.$store.getters['currentStore'](this.resource);\n\n    return { inStore };\n  },\n\n  computed: {\n    // Count of rows - either from the data provided or from the rows for the first resource\n    rowsCount() {\n      if (this.resources.length > 0) {\n        const existingData = this.$store.getters[`${ this.inStore }/all`](this.resources[0]) || [];\n\n        return (existingData || []).length;\n      }\n\n      return 0;\n    },\n\n    // Have we loaded all resources for the types that are needed\n    haveAll() {\n      return this.resources.reduce((acc, r) => {\n        return acc && this.$store.getters[`${ this.inStore }/haveAll`](r);\n      }, true);\n    },\n\n    // Total of all counts of all resources for all of the resources being loaded\n    total() {\n      const clusterCounts = this.$store.getters[`${ this.inStore }/all`](COUNT);\n\n      return this.resources.reduce((acc, r) => {\n        const resourceCounts = clusterCounts?.[0]?.counts?.[r];\n        const resourceCount = resourceCounts?.summary?.count;\n        const count = resourceCount || 0;\n\n        return acc + count;\n      }, 0);\n    },\n\n    // Total count of all of the resources for all of the resources being loaded\n    count() {\n      return this.resources.reduce((acc, r) => {\n        return acc + (this.$store.getters[`${ this.inStore }/all`](r) || []).length;\n      }, 0);\n    },\n\n    // Width style to enable the progress bar style presentation\n    width() {\n      const progress = Math.ceil(100 * (this.count / this.total));\n\n      return `${ progress }%`;\n    }\n  },\n};\n</script>\n\n<template>\n  <div\n    v-if=\"count && !haveAll\"\n    class=\"ml-10 resource-loading-indicator\"\n  >\n    <div class=\"inner\">\n      <div class=\"resource-loader\">\n        <div class=\"rl-bg\">\n          <i class=\"icon icon-spinner icon-spin\" /><span>{{ t( 'resourceLoadingIndicator.loading' ) }} <span v-if=\"!indeterminate\">{{ count }} / {{ total }}</span></span>\n        </div>\n      </div>\n      <div\n        class=\"resource-loader\"\n        :style=\"{width}\"\n      >\n        <div class=\"rl-fg\">\n          <i class=\"icon icon-spinner icon-spin\" /><span>{{ t( 'resourceLoadingIndicator.loading' ) }} <span v-if=\"!indeterminate\">{{ count }} / {{ total }}</span></span>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .resource-loading-indicator {\n    border: 1px solid var(--link);\n    border-radius: 10px;\n    position: relative;\n    width: min-content;\n    overflow: hidden;\n\n    .resource-loader:last-child {\n      position: absolute;\n      top: 0;\n\n      background-color: var(--link);\n      color: var(--link-text);\n      overflow: hidden;\n      white-space: nowrap;\n    }\n\n    .resource-loader {\n      padding: 1px 10px;\n      width: max-content;\n\n      .rl-fg, .rl-bg {\n        align-content: center;\n        display: flex;\n\n        > i {\n          font-size: 18px;\n          line-height: 18px;\n        }\n\n        > span {\n          margin-left: 5px;\n        }\n      }\n    }\n  }\n</style>\n"]}]}