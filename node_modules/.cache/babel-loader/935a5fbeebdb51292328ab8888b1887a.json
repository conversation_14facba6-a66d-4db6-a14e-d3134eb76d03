{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/ForceDirectedTreeChart/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/ForceDirectedTreeChart/index.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/ForceDirectedTreeChart/index.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE9C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;IACV,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,2BAA2B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC,CAAC,CAAC,CAAC,iCAAiC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,6BAA6B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,6BAA6B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC,CAAC,CAAC,CAAC,iCAAiC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC,CAAC,CAAC,CAAC,iCAAiC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC,CAAC,CAAC,kCAAkC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC,CAAC,CAAC,CAAC,iCAAiC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,2BAA2B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,6BAA6B,CAAC;IACxC,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAErD,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;UAE7B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;UAEhC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxF,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YAC9B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAErG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAClC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;cAEtB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;cACnD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3D;YACF;UACF,CAAC,CAAC;;UAEF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC;QACF;MACF;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC;QAChG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;UACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACjD;QACF,CAAC,CAAC;IACN,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3C;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC;;MAEJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEzB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC;QACD,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE/B,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEhC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAClC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAClC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE5C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC;QACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAElC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnE,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7C;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACzD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEtF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACrC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEjD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAErD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC7B,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;UACrC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACzC,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1C;QACF,CAAC,CAAC;;QAEF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAExC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR;;MAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC3C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;;MAE5C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAElB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE/E,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEf,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC;;MAEJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC;IACN,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5C;MACA,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACZ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC;MACA,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MAChB,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC;;MAET,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACrB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC;QACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACf,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC;QACL;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACd;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACrF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEtC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC5E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC;EACJ,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC;AACH,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/ForceDirectedTreeChart/index.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport * as d3 from 'd3';\nimport { STATES } from '@shell/plugins/dashboard-store/resource-class';\nimport { BadgeState } from '@components/BadgeState';\nimport { getChartIcon } from './chartIcons.js';\n\nexport default {\n  name:       'ForceDirectedTreeChart',\n  components: { BadgeState },\n  props:      {\n    data: {\n      type:     [Array, Object],\n      required: true\n    },\n    fdcConfig: {\n      type:     Object,\n      required: true\n    }\n  },\n  data() {\n    return {\n      dataWatcher:                         undefined,\n      parsedInfo:                          undefined,\n      root:                                undefined,\n      allNodesData:                        undefined,\n      allLinks:                            undefined,\n      rootNode:                            undefined,\n      node:                                undefined,\n      link:                                undefined,\n      svg:                                 undefined,\n      zoom:                                undefined,\n      simulation:                          undefined,\n      isChartFirstRendered:                false,\n      isChartFirstRenderAnimationFinished: false,\n      moreInfo:                            {}\n    };\n  },\n  methods: {\n    watcherFunction(newValue) {\n      if (newValue.length) {\n        if (!this.isChartFirstRendered) {\n          this.parsedInfo = this.fdcConfig.parseData(this.data);\n\n          // set details info and set active state for node\n          this.setDetailsInfo(this.parsedInfo, false);\n          this.parsedInfo.active = true;\n\n          // render and update chart\n          this.renderChart();\n          this.updateChart(true, true);\n          this.isChartFirstRendered = true;\n\n          // here we just look for changes in the status of the nodes and update them accordingly\n        } else {\n          const parsedInfo = this.fdcConfig.parseData(this.data);\n          const flattenedData = this.flatten(parsedInfo);\n          let hasStatusChange = false;\n\n          flattenedData.forEach((item) => {\n            const index = this.allNodesData.findIndex((nodeData) => item.matchingId === nodeData.data.matchingId);\n\n            // apply status change to each node\n            if (index > -1 && this.allNodesData[index].data.state !== item.state) {\n              this.allNodesData[index].data.state = item.state;\n              this.allNodesData[index].data.stateLabel = item.stateLabel;\n              this.allNodesData[index].data.stateColor = item.stateColor;\n              hasStatusChange = true;\n\n              // if node is selected (active), update details info\n              if (this.allNodesData[index].data.active) {\n                this.setDetailsInfo(this.allNodesData[index].data, false);\n              }\n            }\n          });\n\n          if (hasStatusChange) {\n            this.updateChart(false, false);\n          }\n        }\n      }\n    },\n    renderChart() {\n      this.zoom = d3.zoom().scaleExtent([1 / 8, 16]).on('zoom', this.zoomed);\n      const transform = d3.zoomIdentity.scale(1).translate(0, 0);\n\n      this.rootNode = this.svg.append('g')\n        .attr('class', 'root-node');\n\n      this.svg.call(this.zoom);\n      this.svg.call(this.zoom.transform, transform);\n\n      this.simulation = d3.forceSimulation()\n        .force('charge', d3.forceManyBody().strength(this.fdcConfig.simulationParams.fdcStrength).distanceMax(this.fdcConfig.simulationParams.fdcDistanceMax))\n        .force('collision', d3.forceCollide(this.fdcConfig.simulationParams.fdcForceCollide))\n        .force('center', d3.forceCenter( this.fdcConfig.chartWidth / 2, this.fdcConfig.chartHeight / 2 ))\n        .alphaDecay(this.fdcConfig.simulationParams.fdcAlphaDecay)\n        .on('tick', this.ticked)\n        .on('end', () => {\n          if (!this.isChartFirstRenderAnimationFinished) {\n            this.zoomFit();\n            this.isChartFirstRenderAnimationFinished = true;\n          }\n        });\n    },\n    updateChart(isStartingData, isSettingNodesAndLinks) {\n      if (isStartingData) {\n        this.root = d3.hierarchy(this.parsedInfo);\n      }\n\n      if (isSettingNodesAndLinks) {\n        this.allNodesData = this.flatten(this.root);\n        this.allLinks = this.root.links();\n      }\n\n      this.link = this.rootNode\n        .selectAll('.link')\n        .data(this.allLinks, (d) => {\n          return d.target.id;\n        });\n\n      this.link.exit().remove();\n\n      const linkEnter = this.link\n        .enter()\n        .append('line')\n        .attr('class', 'link')\n        .style('opacity', '0.2')\n        .style('stroke-width', 4);\n\n      this.link = linkEnter.merge(this.link);\n\n      this.node = this.rootNode\n        .selectAll('.node')\n        .data(this.allNodesData, (d) => {\n          return d.id;\n        })\n        // this is where we define which prop changes with any data update (status color)\n        .attr('class', this.mainNodeClass);\n\n      this.node.exit().remove();\n\n      // define the node styling and function\n      const nodeEnter = this.node\n        .enter()\n        .append('g')\n        .attr('class', this.mainNodeClass)\n        .style('opacity', 1)\n        .on('click', (ev, d) => {\n          this.setDetailsInfo(d.data, true);\n        })\n        .call(d3.drag()\n          .on('start', this.dragStarted)\n          .on('drag', this.dragging)\n          .on('end', this.dragEnded));\n\n      // draw status circle (inherits color from main node)\n      nodeEnter.append('circle')\n        .attr('r', this.setNodeRadius);\n\n      nodeEnter.append('circle')\n        .attr('r', (d) => {\n          return this.setNodeRadius(d) - 5;\n        })\n        .attr('class', 'node-hover-layer');\n\n      nodeEnter.append('svg').html((d) => {\n        const icon = this.fdcConfig.fetchNodeIcon(d);\n\n        return getChartIcon(icon);\n      })\n        .attr('x', this.nodeImagePosition)\n        .attr('y', this.nodeImagePosition)\n        .attr('height', this.nodeImageSize)\n        .attr('width', this.nodeImageSize);\n\n      this.node = nodeEnter.merge(this.node);\n\n      this.simulation.nodes(this.allNodesData);\n      this.simulation.force('link', d3.forceLink()\n        .id((d) => {\n          return d.id;\n        })\n        .distance(100)\n        .links(this.allLinks)\n      );\n    },\n    mainNodeClass(d) {\n      const lowerCaseStatus = d.data?.state ? d.data.state.toLowerCase() : 'unkown_status';\n      const defaultClassArray = ['node'];\n\n      if (STATES[lowerCaseStatus] && STATES[lowerCaseStatus].color) {\n        defaultClassArray.push(`node-${ STATES[lowerCaseStatus].color }`);\n      } else {\n        defaultClassArray.push(`node-default-fill`);\n      }\n\n      // node active (clicked)\n      if (d.data?.active) {\n        defaultClassArray.push('active');\n      }\n\n      // here we extend the node classes (different chart types)\n      const extendedClassArray = this.fdcConfig.extendNodeClass(d).concat(defaultClassArray);\n\n      return extendedClassArray.join(' ');\n    },\n    setNodeRadius(d) {\n      const { radius } = this.fdcConfig.nodeDimensions(d);\n\n      return radius;\n    },\n    nodeImageSize(d) {\n      const { size } = this.fdcConfig.nodeDimensions(d);\n\n      return size;\n    },\n    nodeImagePosition(d) {\n      const { position } = this.fdcConfig.nodeDimensions(d);\n\n      return position;\n    },\n    setDetailsInfo(data, toUpdate) {\n      // get the data to be displayed on info box, per each different chart\n      this.moreInfo = Object.assign([], this.fdcConfig.infoDetails(data));\n\n      // update to the chart is needed when active state changes\n      if (toUpdate) {\n        this.allNodesData.forEach((item, i) => {\n          if (item.data.matchingId === data.matchingId) {\n            this.allNodesData[i].data.active = true;\n          } else {\n            this.allNodesData[i].data.active = false;\n          }\n        });\n\n        this.updateChart(false, false);\n      }\n    },\n    zoomFit() {\n      const rootNode = d3.select('.root-node');\n\n      if (!rootNode?.node()) {\n        return;\n      }\n\n      const paddingBuffer = 30;\n      const chartDimentions = rootNode.node().getBoundingClientRect();\n      const chartCoordinates = rootNode.node().getBBox();\n      const parent = rootNode.node().parentElement;\n      const fullWidth = parent.clientWidth;\n      const fullHeight = parent.clientHeight;\n      const width = chartDimentions.width;\n      const height = chartDimentions.height;\n      const midX = chartCoordinates.x + width / 2;\n      const midY = chartCoordinates.y + height / 2;\n\n      if (width === 0 || height === 0) {\n        return;\n      } // nothing to fit\n\n      const scale = 1 / Math.max(width / (fullWidth - paddingBuffer), height / (fullHeight - paddingBuffer));\n      const translate = [fullWidth / 2 - scale * midX, fullHeight / 2 - scale * midY];\n\n      const transform = d3.zoomIdentity\n        .translate(translate[0], translate[1])\n        .scale(scale);\n\n      // this update the cached zoom state!!!!! very important so that any transforms from user interaction keep this base!\n      this.svg.call(this.zoom.transform, transform);\n    },\n    ticked() {\n      this.link\n        .attr('x1', (d) => {\n          return d.source.x;\n        })\n        .attr('y1', (d) => {\n          return d.source.y;\n        })\n        .attr('x2', (d) => {\n          return d.target.x;\n        })\n        .attr('y2', (d) => {\n          return d.target.y;\n        });\n\n      this.node\n        .attr('transform', (d) => {\n          return `translate(${ d.x }, ${ d.y })`;\n        });\n    },\n    dragStarted(ev, d) {\n      if (!ev.active) {\n        this.simulation.alphaTarget(0.3).restart();\n      }\n      d.fx = d.x;\n      d.fy = d.y;\n    },\n    dragging(ev, d) {\n      d.fx = ev.x;\n      d.fy = ev.y;\n    },\n    dragEnded(ev, d) {\n      if (!ev.active) {\n        this.simulation.alphaTarget(0);\n      }\n      d.fx = undefined;\n      d.fy = undefined;\n    },\n    zoomed(ev) {\n      this.rootNode.attr('transform', ev.transform);\n    },\n    flatten(root) {\n      const nodes = [];\n      let i = 0;\n\n      function recurse(node) {\n        if (node.children) {\n          node.children.forEach(recurse);\n        }\n        if (!node.id) {\n          node.id = ++i;\n        } else {\n          ++i;\n        }\n        nodes.push(node);\n      }\n      recurse(root);\n\n      return nodes;\n    }\n  },\n  mounted() {\n    // start by appending SVG to define height of chart area\n    this.svg = d3.select('#tree').append('svg')\n      .attr('viewBox', `0 0 ${ this.fdcConfig.chartWidth } ${ this.fdcConfig.chartHeight }`)\n      .attr('preserveAspectRatio', 'none');\n\n    // set watcher for the chart data\n    this.dataWatcher = this.$watch(this.fdcConfig.watcherProp, function(newValue) {\n      this.watcherFunction(newValue);\n    }, {\n      deep:      true,\n      immediate: true\n    });\n  },\n  unmounted() {\n    this.dataWatcher();\n  },\n};\n</script>\n\n<template>\n  <div>\n    <div\n      class=\"chart-container\"\n      data-testid=\"gitrepo_graph\"\n    >\n      <!-- loading status container -->\n      <div\n        v-if=\"!isChartFirstRenderAnimationFinished\"\n        class=\"loading-container\"\n      >\n        <p v-show=\"!isChartFirstRendered\">\n          {{ t('fleet.fdc.loadingChart') }}\n        </p>\n        <p v-show=\"isChartFirstRendered && !isChartFirstRenderAnimationFinished\">\n          {{ t('fleet.fdc.renderingChart') }}\n        </p>\n        <i class=\"mt-10 icon-spinner icon-spin\" />\n      </div>\n      <!-- main div for svg container -->\n      <div id=\"tree\" />\n      <!-- info box -->\n      <div class=\"more-info-container\">\n        <div class=\"more-info\">\n          <table>\n            <tr\n              v-for=\"(item, i) in moreInfo\"\n              :key=\"i\"\n            >\n              <td\n                v-if=\"item.type !== 'single-error'\"\n                :class=\"{'align-middle': item.type === 'state-badge'}\"\n              >\n                <span class=\"more-info-item-label\">{{ t(item.labelKey) }}:</span>\n              </td>\n              <!-- title template -->\n              <td v-if=\"item.type === 'title-link'\">\n                <span v-if=\"item.valueObj.detailLocation\">\n                  <router-link\n                    :to=\"item.valueObj.detailLocation\"\n                  >\n                    {{ item.valueObj.label }}\n                  </router-link>\n                </span>\n                <span v-else>{{ item.valueObj.label }}</span>\n              </td>\n              <!-- state-badge template -->\n              <td\n                v-else-if=\"item.type === 'state-badge'\"\n                class=\"align-middle\"\n              >\n                <span>\n                  <BadgeState\n                    :color=\"`bg-${item.valueObj.stateColor}`\"\n                    :label=\"item.valueObj.stateLabel\"\n                    class=\"state-bagde\"\n                  />\n                </span>\n              </td>\n              <!-- single-error template -->\n              <td\n                v-if=\"item.type === 'single-error'\"\n                class=\"single-error\"\n                colspan=\"2\"\n              >\n                <p>{{ item.value }}</p>\n              </td>\n              <!-- default template -->\n              <td v-else>\n                {{ item.value }}\n              </td>\n            </tr>\n          </table>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\">\n.chart-container {\n  display: flex;\n  background-color: var(--body-bg);\n  position: relative;\n  border: 1px solid var(--border);\n  border-radius: var(--border-radius);\n  min-height: 100px;\n\n  .loading-container {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    border-radius: var(--border-radius);\n    background-color: var(--body-bg);\n    z-index: 2;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    flex-direction: column;\n\n    i {\n      font-size: 24px;\n    }\n  }\n\n  #tree {\n    width: 70%;\n    height: fit-content;\n\n    svg {\n      margin-top: 3px;\n    }\n\n    .link {\n      stroke: var(--darker);\n    }\n\n    .node {\n      cursor: pointer;\n\n      &.active {\n        .node-hover-layer {\n          display: block;\n        }\n      }\n\n      &.repo.active > circle {\n        transform: scale(1.2);\n      }\n\n      &.bundle.active > circle {\n        transform: scale(1.35);\n      }\n\n      &.bundle-deployment.active > circle {\n        transform: scale(1.6);\n      }\n\n      &.node-default-fill > circle,\n      &.repo > circle {\n        fill: var(--muted);\n      }\n      &:not(.repo).node-success > circle {\n        fill: var(--success);\n      }\n      &:not(.repo).node-info > circle {\n        fill: var(--info);\n      }\n      &:not(.repo).node-warning > circle {\n        fill: var(--warning);\n      }\n      &:not(.repo).node-error > circle {\n        fill: var(--error);\n      }\n\n      .node-hover-layer {\n        stroke: var(--body-bg);\n        stroke-width: 2;\n        display: none;\n      }\n    }\n  }\n\n  .more-info-container {\n    width: 30%;\n    position: relative;\n    border-left: 1px solid var(--border);\n    background-color: var(--body-bg);\n    border-top-right-radius: var(--border-radius);\n    border-bottom-right-radius: var(--border-radius);\n    overflow: hidden;\n\n    .more-info {\n      position: absolute;\n      top: 0;\n      left: 0;\n      right:0;\n      bottom:0;\n      width: 100%;\n      padding: 20px;\n      border-top-right-radius: var(--border-radius);\n      border-bottom-right-radius: var(--border-radius);\n      overflow-y: auto;\n\n      table {\n        td {\n          vertical-align: top;\n          padding-bottom: 10px;\n\n          &.align-middle {\n            vertical-align: middle;\n          }\n        }\n\n        .more-info-item-label {\n          color: var(--darker);\n          margin-right: 8px;\n        }\n\n        .single-error {\n          color: var(--error);\n        }\n\n        p {\n          line-height: 1.5em;\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}