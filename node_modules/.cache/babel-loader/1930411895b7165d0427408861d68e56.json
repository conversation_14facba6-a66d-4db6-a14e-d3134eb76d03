{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/EnvVars.vue?vue&type=template&id=74c7a39e&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/EnvVars.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/EnvVars.vue"], "names": [], "mappings": ";;qBA0GQ,KAAK,EAAE,gBAAgB;;;;;;wBAA7B,oBAuBM,OAvBN,UAuBM;uBAtBJ,oBAaM,6BAZe,YAAM,GAAjB,GAAG,EAAE,CAAC;4BADhB,oBAaM,SAXH,GAAG,EAAE,CAAC;QAEP,aAQE;UAPQ,KAAK,EAAE,GAAG,CAAC,KAAK;yCAAT,GAAG,CAAC,KAAK,aAMT,kBAAS;UALvB,aAAW,EAAE,cAAO;UACpB,iBAAe,EAAE,iBAAU;UAC3B,IAAI,EAAE,WAAI;UACV,OAAO,EAAE,cAAO;UAChB,QAAM,aAAE,kBAAS,CAAC,CAAC;;;;;MAKf,eAAM;uCADf,oBAOE;;UAJA,IAAI,EAAC,QAAQ;UACb,KAAK,EAAC,uBAAuB;UAC7B,aAAW,EAAC,aAAa;UACxB,OAAK,0CAAE,+DAAgB;;yBAJnB,sCAAsC", "sourcesContent": ["<script>\nimport ValueFromResource from '@shell/components/form/ValueFromResource';\nimport debounce from 'lodash/debounce';\nimport { randomStr } from '@shell/utils/string';\nimport { _VIEW } from '@shell/config/query-params';\n\nexport default {\n  components: { ValueFromResource },\n\n  props: {\n    /**\n     * Form mode for the component\n     */\n    mode: {\n      type:     String,\n      required: true,\n    },\n    configMaps: {\n      type:     Array,\n      required: true\n    },\n    secrets: {\n      type:     Array,\n      required: true\n    },\n    loading: {\n      default: false,\n      type:    Boolean\n    },\n    /**\n     * Container spec\n     */\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  data() {\n    const { env = [], envFrom = [] } = this.value;\n\n    const allEnv = [...env, ...envFrom].map((row) => {\n      return { value: row, id: randomStr(4) };\n    });\n\n    return {\n      env, envFrom, allEnv\n    };\n  },\n\n  computed: {\n    isView() {\n      return this.mode === _VIEW;\n    }\n  },\n\n  watch: {\n    'value.tty'(neu) {\n      if (neu) {\n        this.value['stdin'] = true;\n      }\n    }\n  },\n  created() {\n    this.queueUpdate = debounce(this.update, 500);\n  },\n\n  methods: {\n    update() {\n      delete this.value.env;\n      delete this.value.envFrom;\n      const envVarSource = [];\n      const envVar = [];\n\n      this.allEnv.forEach((row) => {\n        if (!row.value) {\n          return;\n        }\n        if (!!row.value.configMapRef || !!row.value.secretRef) {\n          envVarSource.push(row.value);\n        } else {\n          envVar.push(row.value);\n        }\n      });\n      this.value['env'] = envVar;\n      this.value['envFrom'] = envVarSource;\n    },\n\n    updateRow() {\n      this.queueUpdate();\n    },\n\n    removeRow(idx) {\n      this.allEnv.splice(idx, 1);\n      this.queueUpdate();\n    },\n\n    addFromReference() {\n      this.allEnv.push({ value: { name: '', valueFrom: {} }, id: randomStr(4) });\n    },\n  },\n};\n</script>\n<template>\n  <div :style=\"{'width':'100%'}\">\n    <div\n      v-for=\"(row, i) in allEnv\"\n      :key=\"i\"\n    >\n      <ValueFromResource\n        v-model:value=\"row.value\"\n        :all-secrets=\"secrets\"\n        :all-config-maps=\"configMaps\"\n        :mode=\"mode\"\n        :loading=\"loading\"\n        @remove=\"removeRow(i)\"\n        @update:value=\"updateRow\"\n      />\n    </div>\n    <button\n      v-if=\"!isView\"\n      v-t=\"'workload.container.command.addEnvVar'\"\n      type=\"button\"\n      class=\"btn role-tertiary add\"\n      data-testid=\"add-env-var\"\n      @click=\"addFromReference\"\n    />\n  </div>\n</template>\n\n<style lang='scss' scoped>\n.value-from :deep() {\n  .v-select {\n    height: 50px;\n  }\n\n  INPUT:not(.vs__search) {\n    height: 50px;\n  }\n}\n.value-from, .value-from-headers {\n  display: grid;\n  grid-template-columns: 20% 20% 20% 5% 20% auto;\n  grid-gap: $column-gutter;\n  align-items: center;\n  margin-bottom: 10px;\n}\n  .value-from-headers {\n    margin: 10px 0px 10px 0px;\n    color: var(--input-label);\n    }\n</style>\n"]}]}