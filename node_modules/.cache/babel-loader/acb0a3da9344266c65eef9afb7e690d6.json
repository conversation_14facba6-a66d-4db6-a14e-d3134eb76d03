{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/FileDiff.vue?vue&type=template&id=c3e1c8b8&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/FileDiff.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVzb2x2ZUNvbXBvbmVudCBhcyBfcmVzb2x2ZUNvbXBvbmVudCwgY3JlYXRlVk5vZGUgYXMgX2NyZWF0ZVZOb2RlLCBjcmVhdGVFbGVtZW50Vk5vZGUgYXMgX2NyZWF0ZUVsZW1lbnRWTm9kZSwgY3JlYXRlVGV4dFZOb2RlIGFzIF9jcmVhdGVUZXh0Vk5vZGUsIG9wZW5CbG9jayBhcyBfb3BlbkJsb2NrLCBjcmVhdGVFbGVtZW50QmxvY2sgYXMgX2NyZWF0ZUVsZW1lbnRCbG9jayB9IGZyb20gInZ1ZSIKCmNvbnN0IF9ob2lzdGVkXzEgPSB7CiAgaWQ6ICJkaWZmRWxlbWVudCIsCiAgcmVmOiAicm9vdCIsCiAgY2xhc3M6ICJyb290Igp9CgpleHBvcnQgZnVuY3Rpb24gcmVuZGVyKF9jdHgsIF9jYWNoZSwgJHByb3BzLCAkc2V0dXAsICRkYXRhLCAkb3B0aW9ucykgewogIGNvbnN0IF9jb21wb25lbnRfcmVzaXplX29ic2VydmVyID0gX3Jlc29sdmVDb21wb25lbnQoInJlc2l6ZS1vYnNlcnZlciIpCgogIHJldHVybiAoX29wZW5CbG9jaygpLCBfY3JlYXRlRWxlbWVudEJsb2NrKCJkaXYiLCBudWxsLCBbCiAgICBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9yZXNpemVfb2JzZXJ2ZXIsIHsgb25Ob3RpZnk6ICRvcHRpb25zLmZpdCB9LCBudWxsLCA4IC8qIFBST1BTICovLCBbIm9uTm90aWZ5Il0pLAogICAgX2NhY2hlWzBdIHx8IChfY2FjaGVbMF0gPSBfY3JlYXRlVGV4dFZOb2RlKCkpLAogICAgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfMSwgbnVsbCwgNTEyIC8qIE5FRURfUEFUQ0ggKi8pCiAgXSkpCn0="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/FileDiff.vue"], "names": [], "mappings": ";;;EAwGM,EAAE,EAAC,aAAa;EAChB,GAAG,EAAC,MAAM;EACV,KAAK,EAAC,MAAM;;;;;;wBALhB,oBAOM;IANJ,aAAiC,8BAAf,QAAM,EAAE,YAAG;;IAC7B,oBAIE,OAJF,UAIE", "sourcesContent": ["<script>\nimport { Diff2HtmlUI } from 'diff2html/lib/ui/js/diff2html-ui-slim.js';\n\nimport { createPatch } from 'diff';\n\nexport default {\n  props: {\n    filename: {\n      type:    String,\n      default: 'file.txt',\n    },\n\n    sideBySide: {\n      type:    Boolean,\n      default: false,\n    },\n\n    orig: {\n      type:     String,\n      required: true,\n    },\n\n    neu: {\n      type:     String,\n      required: true,\n    },\n\n    autoResize: {\n      type:    Boolean,\n      default: true,\n    },\n    footerSpace: {\n      type:    Number,\n      default: 0,\n    },\n    minHeight: {\n      type:    Number,\n      default: 200,\n    }\n  },\n\n  mounted() {\n    this.draw();\n  },\n\n  watch: {\n    sideBySide() {\n      this.draw();\n    }\n  },\n\n  methods: {\n    draw() {\n      const targetElement = document.getElementById('diffElement');\n      const patch = createPatch(\n        this.filename,\n        this.orig,\n        this.neu\n      );\n      const configuration = {\n        // UI\n        synchronisedScroll: true,\n\n        // Base\n        outputFormat: this.sideBySide ? 'side-by-side' : 'line-by-line',\n        drawFileList: false,\n        matching:     'words',\n      };\n\n      const diff2htmlUi = new Diff2HtmlUI(targetElement, patch, configuration);\n\n      diff2htmlUi.draw();\n      this.fit();\n    },\n\n    fit() {\n      if ( !this.autoResize ) {\n        return;\n      }\n\n      const container = this.$refs.root;\n\n      if ( !container ) {\n        return;\n      }\n\n      const offset = container.getBoundingClientRect();\n\n      if ( !offset ) {\n        return;\n      }\n\n      const desired = window.innerHeight - offset.top - this.footerSpace;\n\n      container.style.height = `${ Math.max(0, desired) }px`;\n    },\n  },\n};\n</script>\n\n<template>\n  <div>\n    <resize-observer @notify=\"fit\" />\n    <div\n      id=\"diffElement\"\n      ref=\"root\"\n      class=\"root\"\n    />\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n.root {\n  max-width: 100%;\n  position: relative;\n  overflow: auto;\n}\n</style>\n\n<style scoped lang=\"scss\">\n@import 'node_modules/diff2html/bundles/css/diff2html.min.css';\n\n:deep() .d2h-wrapper {\n  .d2h-file-header {\n    display: none;\n  }\n\n  .d2h-file-wrapper {\n    border-color: var(--diff-border);\n  }\n\n  .d2h-diff-table {\n    font-family: Menlo,Consolas,monospace;\n    font-size: 13px;\n  }\n\n  .d2h-emptyplaceholder, .d2h-code-side-emptyplaceholder {\n    border-color: var(--diff-linenum-border);\n    background-color: var(--diff-empty-placeholder);\n  }\n\n  .d2h-code-linenumber,\n  .d2h-code-side-linenumber {\n    background-color: var(--diff-linenum-bg);\n    color: var(--diff-linenum);\n    border-color: var(--diff-linenum-border);\n    border-left: 0;\n  }\n\n  .d2h-code-line del,.d2h-code-side-line del {\n    background-color: var(--diff-line-del-bg);\n  }\n\n  .d2h-code-line ins,.d2h-code-side-line ins {\n    background-color: var(--diff-line-ins-bg);\n  }\n\n  .d2h-del {\n    background-color: var(--diff-del-bg);\n    border-color: var(--diff-del-border);\n    color: var(--body-text);\n  }\n\n  .d2h-ins {\n    background-color: var(--diff-ins-bg);\n    border-color: var(--diff-ins-border);\n    color: var(--body-text);\n  }\n\n  .d2h-info {\n    background-color: var(--diff-header-bg);\n    color: var(--diff-header);\n    border-color: var(--diff-header-border);\n  }\n\n  .d2h-file-diff .d2h-del.d2h-change {\n    background-color: var(--diff-chg-del);\n  }\n\n  .d2h-file-diff .d2h-ins.d2h-change {\n    background-color: var(--diff-chg-ins);\n  }\n}\n</style>\n"]}]}