{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Members/MembershipEditor.vue?vue&type=template&id=192d741e&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Members/MembershipEditor.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/Members/MembershipEditor.vue"], "names": ["$fetchState", "t"], "mappings": ";;qBA2KW,KAAK,EAAC,UAAU;qBACd,KAAK,EAAC,oBAAoB;qBACxB,KAAK,EAAC,YAAY;qBACd,KAAK,EAAC,YAAY;qBAEtB,KAAK,EAAC,YAAY;qBACd,KAAK,EAAC,YAAY;qBAM1B,KAAK,EAAC,aAAa;qBACjB,KAAK,EAAC,YAAY;;;;;;;;;;UArBdA,gBAAW,CAAC,OAAO;qBAAlC,aAAsC;qBACtC,aAwDY;;QAtDF,KAAK,EAAE,cAAQ;gEAAR,cAAQ;QACtB,IAAI,EAAE,WAAI;QACV,aAAW,EAAE,IAAI;;QAEP,gBAAc,WACvB,CASM;UATN,oBASM,OATN,UASM;YARJ,oBAOM,OAPN,UAOM;cANJ,oBAEM,OAFN,UAEM;gBADJ,oBAAkE,SAAlE,UAAkE,mBAArCC,MAAC;;;cAEhC,oBAEM,OAFN,UAEM;gBADJ,oBAAkE,SAAlE,UAAkE,mBAArCA,MAAC;;;;;QAK3B,OAAO,WAChB,CAYM,CAba,GAAG,EAAE,CAAC;UACzB,oBAYM,OAZN,UAYM;YAXJ,oBAIM,OAJN,UAIM;cAHJ,aAEE;gBADC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,WAAW;;;;YAGjC,oBAKM;cAJH,aAAW,eAAe,CAAC;cAC5B,KAAK,EAAC,iBAAiB;gCAEpB,GAAG,CAAC,KAAK,CAAC,WAAW;;;QAInB,GAAG,WACZ,CAOS;UAPT,oBAOS;YANP,IAAI,EAAC,QAAQ;YACb,KAAK,EAAC,wBAAwB;YAC9B,aAAW,EAAC,UAAU;YACrB,OAAK,0CAAE,iDAAS;8BAEdA,MAAC;;QAGG,eAAa,WAL5B,CAGE,CAE6B,MAAM,EAAE,CAAC;YACrB,iBAAQ,IAAI,CAAC,WAAW,eAAM;6BAA3C,oBAA+C;6BAC/C,oBASS;;gBAPP,IAAI,EAAC,QAAQ;gBACZ,QAAQ,EAAE,eAAM;gBACjB,KAAK,EAAC,eAAe;gBACpB,aAAW,iBAAiB,CAAC;gBAC7B,OAAK,EAAE,MAAM;kCAEXA,MAAC", "sourcesContent": ["<script>\nimport { MANAGEMENT, NORMAN } from '@shell/config/types';\nimport ArrayList from '@shell/components/form/ArrayList';\nimport Principal from '@shell/components/auth/Principal';\nimport Loading from '@shell/components/Loading';\nimport { _CREATE, _VIEW } from '@shell/config/query-params';\nimport { get, set } from '@shell/utils/object';\n\nfunction normalizeId(id) {\n  return id?.replace(':', '/') || id;\n}\n\nexport function canViewMembershipEditor(store, needsProject = false) {\n  return (!!store.getters['management/schemaFor'](MANAGEMENT.PROJECT_ROLE_TEMPLATE_BINDING) || !needsProject) &&\n    !!store.getters['management/schemaFor'](MANAGEMENT.ROLE_TEMPLATE) &&\n    !!store.getters['rancher/schemaFor'](NORMAN.PRINCIPAL);\n}\n\nexport default {\n  emits: ['membership-update'],\n\n  components: {\n    ArrayList, Loading, Principal\n  },\n\n  props: {\n    addMemberDialogName: {\n      type:     String,\n      required: true\n    },\n\n    parentKey: {\n      type:     String,\n      required: true\n    },\n\n    parentId: {\n      type:    String,\n      default: null\n    },\n\n    mode: {\n      type:     String,\n      required: true\n    },\n\n    type: {\n      type:     String,\n      required: true\n    },\n\n    defaultBindingHandler: {\n      type:    Function,\n      default: null,\n    },\n\n    modalSticky: {\n      type:    Boolean,\n      default: false,\n    }\n  },\n\n  async fetch() {\n    const roleBindingRequestParams = { type: this.type, opt: { force: true } };\n\n    if (this.type === NORMAN.PROJECT_ROLE_TEMPLATE_BINDING && this.parentId) {\n      Object.assign(roleBindingRequestParams, { opt: { filter: { projectId: this.parentId.split('/').join(':') }, force: true } });\n    }\n    const userHydration = [\n      this.schema ? this.$store.dispatch(`rancher/findAll`, roleBindingRequestParams) : [],\n      this.$store.dispatch('rancher/findAll', { type: NORMAN.PRINCIPAL }),\n      this.$store.dispatch(`management/findAll`, { type: MANAGEMENT.ROLE_TEMPLATE }),\n      this.$store.dispatch(`management/findAll`, { type: MANAGEMENT.USER })\n    ];\n    const [allBindings] = await Promise.all(userHydration);\n\n    const bindings = allBindings\n      .filter((b) => normalizeId(get(b, this.parentKey)) === normalizeId(this.parentId));\n\n    this['lastSavedBindings'] = [...bindings];\n\n    // Add the current user as the project owner. This will get created by default\n    if (this.mode === _CREATE && bindings.length === 0 && this.defaultBindingHandler) {\n      const defaultBinding = await this.defaultBindingHandler();\n\n      defaultBinding.isDefaultBinding = true;\n      bindings.push(defaultBinding);\n    }\n\n    this['bindings'] = bindings;\n  },\n\n  data() {\n    return {\n      schema:            this.$store.getters[`rancher/schemaFor`](this.type),\n      bindings:          [],\n      lastSavedBindings: [],\n    };\n  },\n\n  computed: {\n    newBindings() {\n      return this.bindings\n        .filter((binding) => !binding.id && !this.lastSavedBindings.includes(binding) && !binding.isDefaultBinding);\n    },\n    removedBindings() {\n      return this.lastSavedBindings\n        .filter((binding) => !this.bindings.includes(binding));\n    },\n    membershipUpdate() {\n      const newBindings = this.newBindings;\n      const removedBindings = this.removedBindings;\n\n      return {\n        newBindings:     this.newBindings,\n        removedBindings: this.removedBindings,\n        save:            (parentId) => {\n          const savedPromises = newBindings.map((binding) => {\n            set(binding, this.parentKey, parentId);\n\n            return binding.save();\n          });\n\n          const removedPromises = removedBindings.map((binding) => binding.remove());\n\n          return Promise.all([...savedPromises, ...removedPromises]);\n        }\n      };\n    },\n\n    isCreate() {\n      return this.mode === _CREATE;\n    },\n\n    isView() {\n      return this.mode === _VIEW;\n    },\n  },\n  watch: {\n    membershipUpdate: {\n      deep: true,\n      handler() {\n        this.$emit('membership-update', this.membershipUpdate);\n      }\n    }\n  },\n\n  methods: {\n    addMember() {\n      this.$store.dispatch('cluster/promptModal', {\n        component:      this.addMemberDialogName,\n        componentProps: { onAdd: this.onAddMember },\n        modalSticky:    this.modalSticky\n      });\n    },\n\n    onAddMember(bindings) {\n      this['bindings'] = [...this.bindings, ...bindings];\n    },\n  }\n};\n</script>\n<template>\n  <Loading v-if=\"$fetchState.pending\" />\n  <ArrayList\n    v-else\n    v-model:value=\"bindings\"\n    :mode=\"mode\"\n    :show-header=\"true\"\n  >\n    <template #column-headers>\n      <div class=\"box mb-0\">\n        <div class=\"column-headers row\">\n          <div class=\"col span-6\">\n            <label class=\"text-label\">{{ t('membershipEditor.user') }}</label>\n          </div>\n          <div class=\"col span-6\">\n            <label class=\"text-label\">{{ t('membershipEditor.role') }}</label>\n          </div>\n        </div>\n      </div>\n    </template>\n    <template #columns=\"{row, i}\">\n      <div class=\"columns row\">\n        <div class=\"col span-6\">\n          <Principal\n            :value=\"row.value.principalId\"\n          />\n        </div>\n        <div\n          :data-testid=\"`role-item-${i}`\"\n          class=\"col span-6 role\"\n        >\n          {{ row.value.roleDisplay }}\n        </div>\n      </div>\n    </template>\n    <template #add>\n      <button\n        type=\"button\"\n        class=\"btn role-primary mt-10\"\n        data-testid=\"add-item\"\n        @click=\"addMember\"\n      >\n        {{ t('generic.add') }}\n      </button>\n    </template>\n    <template #remove-button=\"{remove, i}\">\n      <span v-if=\"(isCreate && i === 0) || isView\" />\n      <button\n        v-else\n        type=\"button\"\n        :disabled=\"isView\"\n        class=\"btn role-link\"\n        :data-testid=\"`remove-item-${i}`\"\n        @click=\"remove\"\n      >\n        {{ t('generic.remove') }}\n      </button>\n    </template>\n  </ArrayList>\n</template>\n\n<style lang=\"scss\" scoped>\n.role {\n  display: flex;\n  align-items: center;\n  flex-direction: row;\n}\n</style>\n"]}]}