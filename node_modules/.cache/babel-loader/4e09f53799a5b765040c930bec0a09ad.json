{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/NamespaceRow.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/NamespaceRow.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/NamespaceRow.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjC,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1B,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC5B,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1B,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX;IACF;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3F,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5F,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACxF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C;;IAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzD;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;MAC3D,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9E,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACpE,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjG,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACxE,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvD,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC3G,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;MAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACzC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;QACzD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD,CAAC,CAAC;;MAEF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACZ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;QACV;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC3F,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD;MACF,CAAC;;MAED,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE/D,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;;EAEH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MAC7D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACvC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC;;MAEF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9C;EACF;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/NamespaceRow.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport Select from '@shell/components/form/Select';\nimport UnitInput from '@shell/components/form/UnitInput';\nimport PercentageBar from '@shell/components/PercentageBar';\nimport { formatSi, parseSi } from '@shell/utils/units';\nimport { ROW_COMPUTED } from './shared';\n\nexport default {\n  emits: ['update:value'],\n\n  components: {\n    Select, PercentageBar, UnitInput\n  },\n\n  props: {\n    mode: {\n      type:     String,\n      required: true,\n    },\n    types: {\n      type:    Array,\n      default: () => []\n    },\n    type: {\n      type:     String,\n      required: true\n    },\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    namespace: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    projectResourceQuotaLimits: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n    namespaceResourceQuotaLimits: {\n      type:    Array,\n      default: () => {\n        return [];\n      }\n    },\n    defaultResourceQuotaLimits: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  mounted() {\n    // We want to update the value first so that the value will be rounded to the project limit.\n    // This is relevant when switching projects. If the value is 1200 and the project that it was\n    // switched to only has capacity for 800 more this will force the value to be set to 800.\n    if (this.value?.limit?.[this.type]) {\n      this.update(this.value.limit[this.type]);\n    }\n\n    if (!this.value?.limit?.[this.type]) {\n      this.update(this.defaultResourceQuotaLimits[this.type]);\n    }\n  },\n\n  computed: {\n    ...ROW_COMPUTED,\n    limitValue() {\n      return parseSi(this.projectResourceQuotaLimits[this.type]);\n    },\n    siOptions() {\n      return {\n        maxExponent: this.typeOption.inputExponent,\n        minExponent: this.typeOption.inputExponent,\n        increment:   this.typeOption.increment,\n        suffix:      this.typeOption.increment === 1024 ? 'i' : ''\n      };\n    },\n    namespaceLimits() {\n      return this.namespaceResourceQuotaLimits\n        .filter((resourceQuota) => resourceQuota[this.type] && resourceQuota.id !== this.namespace.id)\n        .map((resourceQuota) => parseSi(resourceQuota[this.type], this.siOptions));\n    },\n    namespaceContribution() {\n      return this.namespaceLimits.reduce((sum, limit) => sum + limit, 0);\n    },\n    totalContribution() {\n      return this.namespaceContribution + parseSi(this.value.limit[this.type] || '0', this.siOptions);\n    },\n    percentageUsed() {\n      return Math.min(this.totalContribution * 100 / this.projectLimit, 100);\n    },\n    projectLimit() {\n      return parseSi(this.projectResourceQuotaLimits[this.type] || 0, this.siOptions);\n    },\n    max() {\n      return this.projectLimit - this.namespaceContribution;\n    },\n    availableResourceQuotas() {\n      return formatSi(this.projectLimit - this.totalContribution, { ...this.siOptions, addSuffixSpace: false });\n    },\n    slices() {\n      const out = [];\n\n      this.namespaceLimits.forEach((limit, i) => {\n        const lastValue = i > 0 ? this.namespaceLimits[i - 1] : 0;\n        const sliceTotal = lastValue + limit;\n\n        out.push(sliceTotal * 100 / this.projectLimit);\n      });\n\n      return out;\n    },\n    tooltip() {\n      const t = this.$store.getters['i18n/t'];\n      const out = [\n        {\n          label: t('resourceQuota.tooltip.reserved'),\n          value: formatSi(this.namespaceContribution, { ...this.siOptions, addSuffixSpace: false }),\n        },\n        {\n          label: t('resourceQuota.tooltip.namespace'),\n          value: this.value.limit[this.type]\n        },\n        {\n          label: t('resourceQuota.tooltip.available'),\n          value: this.availableResourceQuotas\n        },\n        {\n          label: t('resourceQuota.tooltip.max'),\n          value: this.projectResourceQuotaLimits[this.type]\n        }\n      ];\n\n      let formattedTooltip = '<div class=\"quota-percentage-tooltip\">';\n\n      (out || []).forEach((v) => {\n        formattedTooltip += `\n        <div style='margin-top: 5px; display: flex; justify-content: space-between;'>\n          ${ v.label }\n          <span style='margin-left: 20px;'>${ v.value }</span>\n        </div>`;\n      });\n      formattedTooltip += '</div>';\n\n      return formattedTooltip;\n    },\n\n  },\n\n  methods: {\n    update(newValue) {\n      const parsedNewValue = parseSi(newValue, this.siOptions) || 0;\n      const min = Math.max(parsedNewValue, 0);\n      const max = Math.min(min, this.max);\n      const value = formatSi(max, {\n        ...this.siOptions,\n        addSuffixSpace: false\n      });\n\n      this.$emit('update:value', this.type, value);\n    }\n  }\n};\n</script>\n<template>\n  <div\n    v-if=\"typeOption\"\n    class=\"row\"\n  >\n    <Select\n      class=\"mr-10\"\n      :mode=\"mode\"\n      :value=\"type\"\n      :disabled=\"true\"\n      :options=\"types\"\n    />\n    <div class=\"resource-availability mr-10\">\n      <PercentageBar\n        v-clean-tooltip=\"tooltip\"\n        class=\"percentage-bar\"\n        :modelValue=\"percentageUsed\"\n        :slices=\"slices\"\n        :color-stops=\"{'100': '--primary'}\"\n      />\n    </div>\n    <UnitInput\n      :value=\"value.limit[type]\"\n      :mode=\"mode\"\n      :placeholder=\"typeOption.placeholder\"\n      :increment=\"typeOption.increment\"\n      :input-exponent=\"typeOption.inputExponent\"\n      :base-unit=\"typeOption.baseUnit\"\n      :output-modifier=\"true\"\n      @update:value=\"update\"\n    />\n  </div>\n</template>\n\n<style lang='scss' scoped>\n  .resource-availability {\n    align-self: center;\n  }\n  .row {\n    display: flex;\n    flex-direction: row;\n    justify-content: space-evenly;\n\n    & > * {\n      width: 100%;\n    }\n  }\n</style>\n"]}]}