{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/QuestionMap.vue?vue&type=template&id=0bee037e", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/QuestionMap.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Questions/QuestionMap.vue"], "names": ["showDescription", "question", "value", "mode", "disabled", "displayTooltip"], "mappings": ";;;;EAwBM,KAAK,EAAC,WAAW;;qBAEZ,KAAK,EAAC,aAAa;qBAIrB,KAAK,EAAC,KAAK;qBACT,KAAK,EAAC,mBAAmB;;;;;wBAVlC,oBAsBM;KApBIA,oBAAe;uBADvB,oBAOM,OAPN,UAOM;UAHJ,oBAEM,OAFN,UAEM,mBADDC,aAAQ,CAAC,WAAW;;;;IAG3B,oBAYM,OAZN,UAYM;MAXJ,oBAUM,OAVN,UAUM;QATJ,aAQE;UAPC,KAAK,EAAEC,UAAK;UACZ,KAAK,EAAED,aAAQ,CAAC,KAAK;UACrB,IAAI,EAAEE,SAAI;UACV,MAAM,EAAE,KAAK;UACb,QAAQ,EAAEC,aAAQ;UAClB,cAAY,EAAEC,mBAAc;UAC5B,gBAAY,EAAE,eAAM", "sourcesContent": ["<script>\nimport KeyValue from '@shell/components/form/KeyValue';\nimport Question from './Question';\n\nexport default {\n  name: 'QuestionMap',\n\n  emits: ['update:value'],\n\n  components: { KeyValue },\n  mixins:     [Question],\n\n  methods: {\n    update(val) {\n      this.$emit('update:value', val);\n    }\n  }\n};\n</script>\n\n<template>\n  <div>\n    <div\n      v-if=\"showDescription\"\n      class=\"row mt-10\"\n    >\n      <div class=\"col span-12\">\n        {{ question.description }}\n      </div>\n    </div>\n    <div class=\"row\">\n      <div class=\"col span-12 mt-10\">\n        <KeyValue\n          :value=\"value\"\n          :title=\"question.label\"\n          :mode=\"mode\"\n          :protip=\"false\"\n          :disabled=\"disabled\"\n          :title-protip=\"displayTooltip\"\n          @update:value=\"update\"\n        />\n      </div>\n    </div>\n  </div>\n</template>\n"]}]}