{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ActionDropdown.vue?vue&type=template&id=da1fb5ac", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ActionDropdown.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ActionDropdown.vue"], "names": [], "mappings": ";;qBA2DO,KAAK,EAAC,uBAAuB;;;;;;wBAAlC,oBA8BM,OA9BN,UA8BM;IA7BJ,oBA4BM;MA3BJ,KAAK,mBAAC,4BAA4B,iBACX,iBAAU,GAAG,mBAAU,oBAAoB,oBAAa;;MAE/E,aAuBa;QAtBX,SAAS,EAAC,QAAQ;QACjB,SAAS,EAAE,KAAK;QAChB,QAAQ,EAAE,oBAAa;QACvB,IAAI,EAAE,KAAK;;QAgBD,MAAM,WACf,CAA+B;UAA/B,YAA+B;;0BAfjC,CAaO;UAbP,YAaO,iCAXJ,UAAU,EAAE,mBAAU,IAFzB,CAaO;YATL,oBAQS;cAPP,GAAG,EAAC,eAAe;cACnB,KAAK,mBAAC,iDAAiD,EAC/C,mBAAU;cACjB,QAAQ,EAAE,oBAAa;cACxB,IAAI,EAAC,QAAQ;;+BACd,uBACQ;cAAA,oBAAoC,OAAjC,KAAK,EAAC,wBAAwB", "sourcesContent": ["<script>\nexport default {\n  name: 'ActionDropdown',\n\n  props: {\n    size: {\n      type:    String,\n      default: '' // possible values are xs, sm, lg. empty is default .btn\n    },\n    // whether this is a button and dropdown (default) or dropdown that looks like a button/dropdown\n    dualAction: {\n      type:    Boolean,\n      default: true\n    },\n\n    disableButton: {\n      type:    Boolean,\n      default: false\n    }\n  },\n\n  computed: {\n    buttonSize() {\n      const { size } = this;\n      let out;\n\n      switch (size) {\n      case '':\n        out = 'btn';\n        break;\n      case 'xs':\n        out = 'btn btn-xs';\n        break;\n      case 'sm':\n        out = 'btn btn-sm';\n        break;\n      case 'lg':\n        out = 'btn btn-lg';\n        break;\n      default:\n      }\n\n      return out;\n    }\n  },\n\n  methods: {\n    hasSlot(name = 'default') {\n      return !!this.$slots[name] || !!this.$slots.name();\n    },\n\n    // allows parent components to programmatically open the dropdown\n    togglePopover() {\n      // this.$refs.popoverButton.click();\n    },\n  }\n};\n</script>\n<template>\n  <div class=\"dropdown-button-group\">\n    <div\n      class=\"dropdown-button bg-primary\"\n      :class=\"{'one-action':!dualAction, [buttonSize]:true, 'disabled': disableButton}\"\n    >\n      <v-dropdown\n        placement=\"bottom\"\n        :container=\"false\"\n        :disabled=\"disableButton\"\n        :flip=\"false\"\n      >\n        <slot\n          name=\"button-content\"\n          :buttonSize=\"buttonSize\"\n        >\n          <button\n            ref=\"popoverButton\"\n            class=\"icon-container bg-primary no-left-border-radius\"\n            :class=\"buttonSize\"\n            :disabled=\"disableButton\"\n            type=\"button\"\n          >\n            Button <i class=\"icon icon-chevron-down\" />\n          </button>\n        </slot>\n        <template #popper>\n          <slot name=\"popover-content\" />\n        </template>\n      </v-dropdown>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\">\n// load here instead of component so SSR render isn't all wonky\n.dropdown-button-group {\n  $xs-padding: 2px 3px;\n\n  .no-left-border-radius {\n    border-top-left-radius: 0px;\n    border-bottom-left-radius: 0px;\n  }\n\n  .no-right-border-radius {\n    border-top-right-radius: 0px;\n    border-bottom-right-radius: 0px;\n  }\n\n  .btn {\n    line-height: normal;\n    border: 0px;\n  }\n\n  .btn-xs,\n  .btn-group-xs > .btn,\n  .btn-xs .btn-label {\n      padding: $xs-padding;\n      font-size: 13px;\n  }\n\n  // this matches the top/bottom padding of the default button\n  $trigger-padding: 15px 10px 15px 10px;\n  $xs-trigger-padding: 2px 4px 4px 4px;\n  $sm-trigger-padding: 10px 10px 10px 10px;\n  $lg-trigger-padding: 18px 10px 10px 10px;\n\n  .v-popper {\n    .text-right {\n      margin-top: 5px;\n    }\n    .trigger {\n      height: 100%;\n      .icon-container {\n        height: 100%;\n        padding: 10px 10px 10px 10px;\n        i {\n          transform: scale(1);\n        }\n        &.btn-xs {\n          padding: $xs-trigger-padding;\n        }\n        &.btn-sm {\n          padding: $sm-trigger-padding;\n        }\n        &.btn-lg {\n          padding: $lg-trigger-padding;\n        }\n        &:focus {\n          outline-style: none;\n          box-shadow: none;\n          border-color: transparent;\n        }\n      }\n    }\n  }\n\n  .dropdown-button {\n    background: var(--tooltip-bg);\n    color: var(--link-text);\n    padding: 0;\n    display: inline-flex;\n\n    .wrapper-content {\n      button {\n        border-right: 0px;\n      }\n    }\n\n    &>*, .icon-chevron-down {\n      color: var(--primary);\n      background-color: rgba(0,0,0,0);\n    }\n\n    &.bg-primary:hover {\n      background: var(--accent-btn-hover);\n    }\n\n    &.one-action {\n      position: relative;\n      &>.btn {\n        padding: 15px 35px 15px 15px;\n      }\n      .v-popper{\n        .trigger{\n          position: absolute;\n          top: 0px;\n          right: 0px;\n          left: 0px;\n          bottom: 0px;\n          BUTTON {\n            position: absolute;\n            right: 0px;\n          }\n        }\n      }\n    }\n  }\n  .v-popper__popper {\n    border: none;\n  }\n  .v-popper__popper {\n    margin-top: 0px;\n\n    &[data-popper-placement^=\"bottom\"] {\n      .v-popper__arrow-container {\n        display: none;\n      }\n    }\n\n    .v-popper__inner {\n      color: var(--dropdown-text);\n      background-color: var(--dropdown-bg);\n      border: 1px solid var(--dropdown-border);\n      padding: 0px;\n      text-align: left;\n\n      LI {\n        padding: 10px;\n\n        &.divider {\n          padding-top: 0px;\n          padding-bottom: 0px;\n\n          > .divider-inner {\n            padding: 0;\n            border-bottom: 1px solid var(--dropdown-divider);\n            width: 125%;\n            margin: 0 auto;\n          }\n        }\n\n        &:not(.divider):hover {\n          background-color: var(--dropdown-hover-bg);\n          color: var(--dropdown-hover-text);\n          cursor: pointer;\n        }\n      }\n\n    }\n  }\n\n  //header\n  .user-info {\n    border-bottom: 1px solid var(--border);\n    display: block;\n  }\n}\n\n</style>\n"]}]}