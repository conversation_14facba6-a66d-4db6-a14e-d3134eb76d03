{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceDetail/Masthead.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceDetail/Masthead.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceDetail/Masthead.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAA<PERSON>,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEjD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEnC,CAAC,CAAC;CACD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACpC;CACA,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC5K,CAAC;AACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;EAEb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1D,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;;IAED,CAAC,CAAC,EAAE;MACF,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAExF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACvE,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1H,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACrC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC;QACF,CAAC;MACH;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7F,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC;MACF,CAAC;IACH,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAErD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAExD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9F;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEzG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC;MACH;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC;MACH;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACvC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEzH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpE,CAAC;MACH;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3G,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE1D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACtB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT;MACF,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEtE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C;;MAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC/E,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACZ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzD,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpD,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpD,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAClB;MACF,CAAC;IACH,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;MAEd,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC;MACJ;;MAEA,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC;MACJ;;MAEA,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC;MACJ;;MAEA,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ;;MAEA,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACZ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE;QACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC;UACF,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC;UACF,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC;UACF,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC;UACF,CAAC,CAAC,CAAC,CAAC,CAAC;QACP;MACF,CAAC;IACH,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAClF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAE5C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACrD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEpF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC;IACH,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAExC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7C;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClE,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7G,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC;;IAEH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC;IACJ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACxE,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEjC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEpC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1B;MACF;IACF;EACF;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceDetail/Masthead.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { KUBERNETES, PROJECT } from '@shell/config/labels-annotations';\nimport { FLEET, NAMESPACE, MANAGEMENT, HELM } from '@shell/config/types';\nimport ButtonGroup from '@shell/components/ButtonGroup';\nimport { BadgeState } from '@components/BadgeState';\nimport { Banner } from '@components/Banner';\nimport { get } from '@shell/utils/object';\nimport { NAME as FLEET_NAME } from '@shell/config/product/fleet';\nimport { HIDE_SENSITIVE } from '@shell/store/prefs';\nimport {\n  AS, _DETAIL, _CONFIG, _YAML, MODE, _CREATE, _EDIT, _VIEW, _UNFLAG, _GRAPH\n} from '@shell/config/query-params';\nimport { ExtensionPoint, PanelLocation } from '@shell/core/types';\nimport ExtensionPanel from '@shell/components/ExtensionPanel';\nimport TabTitle from '@shell/components/TabTitle';\n\n// i18n-uses resourceDetail.header.*\n\n/**\n * Resource Detail Masthead component.\n *\n * ToDo: this component seem to be picking up a lot of logic from special cases, could be simplified down to parameters and then customized per use-case via wrapper component\n */\nexport default {\n\n  name: 'MastheadResourceDetail',\n\n  components: {\n    BadgeState, Banner, ButtonGroup, ExtensionPanel, TabTitle\n  },\n  props: {\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n\n    mode: {\n      type:    String,\n      default: 'create'\n    },\n\n    realMode: {\n      type:    String,\n      default: 'create'\n    },\n\n    as: {\n      type:    String,\n      default: _YAML,\n    },\n\n    hasGraph: {\n      type:    Boolean,\n      default: false\n    },\n\n    hasDetail: {\n      type:    Boolean,\n      default: false\n    },\n\n    hasEdit: {\n      type:    Boolean,\n      default: false\n    },\n\n    storeOverride: {\n      type:    String,\n      default: null,\n    },\n\n    resource: {\n      type:    String,\n      default: null,\n    },\n\n    resourceSubtype: {\n      type:    String,\n      default: null,\n    },\n\n    parentRouteOverride: {\n      type:    String,\n      default: null,\n    },\n\n    canViewYaml: {\n      type:    Boolean,\n      default: false,\n    }\n  },\n\n  data() {\n    return {\n      DETAIL_VIEW:       _DETAIL,\n      extensionType:     ExtensionPoint.PANEL,\n      extensionLocation: PanelLocation.DETAILS_MASTHEAD,\n    };\n  },\n\n  computed: {\n    dev() {\n      return this.$store.getters['prefs/dev'];\n    },\n\n    schema() {\n      const inStore = this.storeOverride || this.$store.getters['currentStore'](this.resource);\n\n      return this.$store.getters[`${ inStore }/schemaFor`]( this.resource );\n    },\n\n    isView() {\n      return this.mode === _VIEW;\n    },\n\n    isEdit() {\n      return this.mode === _EDIT;\n    },\n\n    isCreate() {\n      return this.mode === _CREATE;\n    },\n\n    isNamespace() {\n      return this.schema?.id === NAMESPACE;\n    },\n\n    isProject() {\n      return this.schema?.id === MANAGEMENT.PROJECT;\n    },\n\n    isProjectHelmChart() {\n      return this.schema?.id === HELM.PROJECTHELMCHART;\n    },\n\n    hasMultipleNamespaces() {\n      return !!this.value.namespaces;\n    },\n\n    namespace() {\n      if (this.value?.metadata?.namespace) {\n        return this.value?.metadata?.namespace;\n      }\n\n      return null;\n    },\n\n    detailsAction() {\n      return this.value?.detailsAction;\n    },\n\n    shouldHifenize() {\n      return (this.mode === 'view' || this.mode === 'edit') && this.resourceSubtype?.length && this.value?.nameDisplay?.length;\n    },\n\n    namespaceLocation() {\n      if (!this.isNamespace) {\n        return this.value.namespaceLocation || {\n          name:   'c-cluster-product-resource-id',\n          params: {\n            cluster:  this.$route.params.cluster,\n            product:  this.$store.getters['productId'],\n            resource: NAMESPACE,\n            id:       this.$route.params.namespace\n          }\n        };\n      }\n\n      return null;\n    },\n\n    isWorkspace() {\n      return this.$store.getters['productId'] === FLEET_NAME && !!this.value?.metadata?.namespace;\n    },\n\n    workspaceLocation() {\n      return {\n        name:   'c-cluster-product-resource-id',\n        params: {\n          cluster:  this.$route.params.cluster,\n          product:  this.$store.getters['productId'],\n          resource: FLEET.WORKSPACE,\n          id:       this.$route.params.namespace\n        }\n      };\n    },\n\n    project() {\n      if (this.isNamespace) {\n        const cluster = this.$store.getters['currentCluster'];\n\n        if (cluster) {\n          const id = (this.value?.metadata?.labels || {})[PROJECT];\n\n          return this.$store.getters['management/byId'](MANAGEMENT.PROJECT, `${ cluster.id }/${ id }`);\n        }\n      }\n\n      return null;\n    },\n\n    banner() {\n      if (this.value?.stateObj?.error) {\n        const defaultErrorMessage = this.t('resourceDetail.masthead.defaultBannerMessage.error', undefined, true);\n\n        return {\n          color:   'error',\n          message: this.value.stateObj.message || defaultErrorMessage\n        };\n      }\n\n      if (this.value?.spec?.paused) {\n        return {\n          color:   'info',\n          message: this.t('asyncButton.pause.description')\n        };\n      }\n\n      if (this.value?.stateObj?.transitioning) {\n        const defaultTransitioningMessage = this.t('resourceDetail.masthead.defaultBannerMessage.transitioning', undefined, true);\n\n        return {\n          color:   'info',\n          message: this.value.stateObj.message || defaultTransitioningMessage\n        };\n      }\n\n      return null;\n    },\n\n    parent() {\n      const displayName = this.value?.parentNameOverride || this.$store.getters['type-map/labelFor'](this.schema);\n      const product = this.$store.getters['currentProduct'].name;\n\n      const defaultLocation = {\n        name:   'c-cluster-product-resource',\n        params: {\n          resource: this.resource,\n          product,\n        }\n      };\n\n      const location = this.value?.parentLocationOverride || defaultLocation;\n\n      if (this.parentRouteOverride) {\n        location.name = this.parentRouteOverride;\n      }\n\n      const typeOptions = this.$store.getters[`type-map/optionsFor`]( this.resource );\n      const out = {\n        displayName, location, ...typeOptions\n      };\n\n      return out;\n    },\n\n    hideSensitiveData() {\n      return this.$store.getters['prefs/get'](HIDE_SENSITIVE);\n    },\n\n    sensitiveOptions() {\n      return [\n        {\n          tooltipKey: 'resourceDetail.masthead.sensitive.hide',\n          icon:       'icon-hide',\n          value:      true,\n        },\n        {\n          tooltipKey: 'resourceDetail.masthead.sensitive.show',\n          icon:       'icon-show',\n          value:      false\n        }\n      ];\n    },\n\n    viewOptions() {\n      const out = [];\n\n      if ( this.hasDetail ) {\n        out.push({\n          labelKey: 'resourceDetail.masthead.detail',\n          value:    _DETAIL,\n        });\n      }\n\n      if ( this.hasEdit && this.parent?.showConfigView !== false) {\n        out.push({\n          labelKey: 'resourceDetail.masthead.config',\n          value:    _CONFIG,\n        });\n      }\n\n      if ( this.hasGraph ) {\n        out.push({\n          labelKey: 'resourceDetail.masthead.graph',\n          value:    _GRAPH,\n        });\n      }\n\n      if ( this.canViewYaml ) {\n        out.push({\n          labelKey: 'resourceDetail.masthead.yaml',\n          value:    _YAML,\n        });\n      }\n\n      if ( out.length < 2 ) {\n        return null;\n      }\n\n      return out;\n    },\n\n    currentView: {\n      get() {\n        return this.as;\n      },\n\n      set(val) {\n        switch ( val ) {\n        case _DETAIL:\n          this.$router.applyQuery({\n            [MODE]: _UNFLAG,\n            [AS]:   _UNFLAG,\n          });\n          break;\n        case _CONFIG:\n          this.$router.applyQuery({\n            [MODE]: _UNFLAG,\n            [AS]:   _CONFIG,\n          });\n          break;\n        case _GRAPH:\n          this.$router.applyQuery({\n            [MODE]: _UNFLAG,\n            [AS]:   _GRAPH,\n          });\n          break;\n        case _YAML:\n          this.$router.applyQuery({\n            [MODE]: _UNFLAG,\n            [AS]:   _YAML,\n          });\n          break;\n        }\n      },\n    },\n\n    showSensitiveToggle() {\n      return !!this.value.hasSensitiveData && this.mode === _VIEW && this.as !== _YAML;\n    },\n\n    managedWarning() {\n      const { value } = this;\n      const labels = value?.metadata?.labels || {};\n\n      const managedBy = labels[KUBERNETES.MANAGED_BY] || '';\n      const appName = labels[KUBERNETES.MANAGED_NAME] || labels[KUBERNETES.INSTANCE] || '';\n\n      return {\n        show:    this.mode === _EDIT && !!managedBy,\n        type:    value?.kind || '',\n        hasName: appName ? 'yes' : 'no',\n        appName,\n        managedBy,\n      };\n    },\n\n    displayName() {\n      let displayName = this.value.nameDisplay;\n\n      if (this.isProjectHelmChart) {\n        displayName = this.value.projectDisplayName;\n      }\n\n      return this.shouldHifenize ? ` - ${ displayName }` : displayName;\n    },\n\n    location() {\n      const { parent } = this;\n\n      return parent?.location;\n    },\n\n    hideNamespaceLocation() {\n      return this.$store.getters['currentProduct'].hideNamespaceLocation || this.value.namespaceLocation === null;\n    },\n\n    resourceExternalLink() {\n      return this.value.resourceExternalLink;\n    },\n  },\n\n  methods: {\n    get,\n\n    showActions() {\n      this.$store.commit('action-menu/show', {\n        resources: this.value,\n        elem:      this.$refs.actions,\n      });\n    },\n\n    toggleSensitiveData(e) {\n      this.$store.dispatch('prefs/set', { key: HIDE_SENSITIVE, value: !!e });\n    },\n\n    invokeDetailsAction() {\n      const action = this.detailsAction;\n\n      if (action) {\n        const fn = this.value[action.action];\n\n        if (fn) {\n          fn.apply(this.value, []);\n        }\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <div class=\"masthead\">\n    <header>\n      <div class=\"title\">\n        <div class=\"primaryheader\">\n          <h1>\n            <TabTitle\n              v-if=\"isCreate\"\n              :showChild=\"false\"\n            >\n              {{ parent.displayName }}\n            </TabTitle>\n            <TabTitle\n              v-else\n              :showChild=\"false\"\n            >\n              {{ displayName }}\n            </TabTitle>\n            <router-link\n              v-if=\"location\"\n              :to=\"location\"\n              role=\"link\"\n              class=\"masthead-resource-list-link\"\n              :aria-label=\"parent.displayName\"\n            >\n              {{ parent.displayName }}:\n            </router-link>\n            <span v-else>{{ parent.displayName }}:</span>\n            <span v-if=\"value?.detailPageHeaderActionOverride && value?.detailPageHeaderActionOverride(realMode)\">{{ value?.detailPageHeaderActionOverride(realMode) }}</span>\n            <t\n              v-else\n              class=\"masthead-resource-title\"\n              :k=\"'resourceDetail.header.' + realMode\"\n              :subtype=\"resourceSubtype\"\n              :name=\"displayName\"\n              :escapehtml=\"false\"\n            />\n            <BadgeState\n              v-if=\"!isCreate && parent.showState\"\n              class=\"masthead-state\"\n              :value=\"value\"\n            />\n            <span\n              v-if=\"!isCreate && value.injectionEnabled\"\n              class=\"masthead-istio\"\n            >\n              <i\n                v-clean-tooltip=\"t('projectNamespaces.isIstioInjectionEnabled')\"\n                class=\"icon icon-sm icon-istio\"\n              />\n            </span>\n            <a\n              v-if=\"dev && !!resourceExternalLink\"\n              v-clean-tooltip=\"t(resourceExternalLink.tipsKey || 'generic.resourceExternalLinkTips')\"\n              class=\"resource-external\"\n              rel=\"nofollow noopener noreferrer\"\n              target=\"_blank\"\n              :href=\"resourceExternalLink.url\"\n            >\n              <i class=\"icon icon-external-link\" />\n            </a>\n          </h1>\n        </div>\n        <div\n          v-if=\"!isCreate\"\n          class=\"subheader\"\n        >\n          <span v-if=\"isNamespace && project\">{{ t(\"resourceDetail.masthead.project\") }}: <router-link :to=\"project.detailLocation\">{{ project.nameDisplay }}</router-link></span>\n          <span v-else-if=\"isWorkspace\">{{ t(\"resourceDetail.masthead.workspace\") }}: <router-link :to=\"workspaceLocation\">{{ namespace }}</router-link></span>\n          <span v-else-if=\"namespace && !hasMultipleNamespaces\">\n            {{ t(\"resourceDetail.masthead.namespace\") }}:\n            <router-link\n              v-if=\"!hideNamespaceLocation\"\n              :to=\"namespaceLocation\"\n              data-testid=\"masthead-subheader-namespace\"\n            >\n              {{ namespace }}\n            </router-link>\n            <span v-else>\n              {{ namespace }}\n            </span>\n          </span>\n          <span v-if=\"parent.showAge\">\n            {{ t(\"resourceDetail.masthead.age\") }}:\n            <LiveDate\n              class=\"live-date\"\n              :value=\"value.creationTimestamp\"\n            />\n          </span>\n          <span\n            v-if=\"value.showCreatedBy\"\n            data-testid=\"masthead-subheader-createdBy\"\n          >\n            {{ t(\"resourceDetail.masthead.createdBy\") }}:\n            <router-link\n              v-if=\"value.createdBy.location\"\n              :to=\"value.createdBy.location\"\n              data-testid=\"masthead-subheader-createdBy-link\"\n            >\n              {{ value.createdBy.displayName }}\n            </router-link>\n            <span\n              v-else\n              data-testid=\"masthead-subheader-createdBy_plain-text\"\n            >\n              {{ value.createdBy.displayName }}\n            </span>\n          </span>\n          <span v-if=\"value.showPodRestarts\">{{ t(\"resourceDetail.masthead.restartCount\") }}:<span class=\"live-data\"> {{ value.restartCount }}</span></span>\n        </div>\n      </div>\n      <slot name=\"right\">\n        <div class=\"actions-container align-start\">\n          <div class=\"actions\">\n            <button\n              v-if=\"detailsAction && currentView === DETAIL_VIEW && isView\"\n              type=\"button\"\n              class=\"btn role-primary actions mr-10\"\n              :disabled=\"!detailsAction.enabled\"\n              @click=\"invokeDetailsAction\"\n            >\n              {{ detailsAction.label }}\n            </button>\n            <ButtonGroup\n              v-if=\"showSensitiveToggle\"\n              :value=\"!!hideSensitiveData\"\n              icon-size=\"lg\"\n              :options=\"sensitiveOptions\"\n              class=\"mr-10\"\n              @update:value=\"toggleSensitiveData\"\n            />\n\n            <ButtonGroup\n              v-if=\"viewOptions && isView\"\n              v-model:value=\"currentView\"\n              :options=\"viewOptions\"\n              class=\"mr-10\"\n            />\n\n            <button\n              v-if=\"isView\"\n              ref=\"actions\"\n              data-testid=\"masthead-action-menu\"\n              aria-haspopup=\"true\"\n              type=\"button\"\n              class=\"btn role-multi-action actions\"\n              @click=\"showActions\"\n            >\n              <i class=\"icon icon-actions\" />\n            </button>\n          </div>\n        </div>\n      </slot>\n    </header>\n\n    <!-- Extension area -->\n    <ExtensionPanel\n      :resource=\"value\"\n      :type=\"extensionType\"\n      :location=\"extensionLocation\"\n    />\n\n    <Banner\n      v-if=\"banner && isView && !parent.hideBanner\"\n      class=\"state-banner mb-10\"\n      :color=\"banner.color\"\n      :label=\"banner.message\"\n    />\n    <Banner\n      v-if=\"managedWarning.show\"\n      color=\"warning\"\n      class=\"mb-20\"\n      :label=\"t('resourceDetail.masthead.managedWarning', managedWarning)\"\n    />\n\n    <slot />\n  </div>\n</template>\n\n<style lang='scss' scoped>\n  .masthead {\n    padding-bottom: 10px;\n    border-bottom: 1px solid var(--border);\n    margin-bottom: 10px;\n  }\n\n  HEADER {\n    margin: 0;\n    grid-template-columns: minmax(0, 1fr) auto;\n  }\n\n  .primaryheader {\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n\n    h1 {\n      margin: 0 0 0 -5px;\n      overflow-x: hidden;\n      display: flex;\n      flex-direction: row;\n      align-items: center;\n\n      .masthead-resource-title {\n        text-overflow: ellipsis;\n        overflow: hidden;\n        white-space: nowrap;\n      }\n\n      .masthead-resource-list-link {\n        margin: 5px;\n      }\n    }\n  }\n\n  .subheader{\n    display: flex;\n    flex-direction: row;\n    color: var(--input-label);\n    & > * {\n      margin: 5px 20px 5px 0px;\n    }\n\n    .live-data {\n      color: var(--body-text);\n      margin-left: 3px;\n    }\n  }\n\n  .state-banner {\n    margin: 3px 0 0 0;\n  }\n\n  .masthead-state {\n    margin-left: 8px;\n    font-size: initial;\n  }\n\n  .masthead-istio {\n    .icon {\n      vertical-align: middle;\n      color: var(--primary);\n    }\n  }\n\n  .left-right-split {\n    display: grid;\n    align-items: center;\n\n    .left-half {\n      grid-column: 1;\n    }\n\n    .right-half {\n      grid-column: 2;\n    }\n  }\n\n  div.actions-container > div.actions {\n    display: flex;\n    flex-direction: row;\n    justify-content: flex-end;\n  }\n\n  .resource-external {\n    font-size: 18px;\n  }\n</style>\n"]}]}