{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceDetail/Masthead.vue?vue&type=template&id=c0c5e95c&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceDetail/Masthead.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceDetail/Masthead.vue"], "names": ["t"], "mappings": ";;qBAyaO,KAAK,EAAC,UAAU;qBAEZ,KAAK,EAAC,OAAO;qBACX,KAAK,EAAC,eAAe;;;;;EAwCpB,KAAK,EAAC,gBAAgB;;qBAIpB,KAAK,EAAC,yBAAyB;;;;EAiBrC,KAAK,EAAC,WAAW;;;;;;;;;EA0Bf,aAAW,EAAC,8BAA8B;;;;EAYxC,aAAW,EAAC,yCAAyC;;;sBAKgC,KAAK,EAAC,WAAW;sBAIvG,KAAK,EAAC,+BAA+B;sBACnC,KAAK,EAAC,SAAS;;;;;;;;;;;;;;wBAhH5B,oBA+KM,OA/KN,UA+KM;IA9KJ,oBAuJS;MAtJP,oBA2GM,OA3GN,UA2GM;QA1GJ,oBA0DM,OA1DN,UA0DM;UAzDJ,oBAwDK;aAtDK,iBAAQ;+BADhB,aAKW;;kBAHR,SAAS,EAAE,KAAK;;oCAEjB,CAAwB;sDAArB,eAAM,CAAC,WAAW;;;;+BAEvB,aAKW;;kBAHR,SAAS,EAAE,KAAK;;oCAEjB,CAAiB;sDAAd,oBAAW;;;;;aAGR,iBAAQ;+BADhB,aAQc;;kBANX,EAAE,EAAE,iBAAQ;kBACb,IAAI,EAAC,MAAM;kBACX,KAAK,EAAC,6BAA6B;kBAClC,YAAU,EAAE,eAAM,CAAC,WAAW;;oCAE/B,CAAwB;sDAArB,eAAM,CAAC,WAAW,IAAG,iBAC1B;;;;+BACA,oBAA6C,qCAA7B,eAAM,CAAC,WAAW,IAAG,GAAC;;aAC1B,YAAK,EAAE,8BAA8B,IAAI,YAAK,EAAE,8BAA8B,CAAC,eAAQ;+BAAnG,oBAAkK,qCAAzD,YAAK,EAAE,8BAA8B,CAAC,eAAQ;+BACvJ,aAOE;;kBALA,KAAK,EAAC,yBAAyB;kBAC9B,CAAC,6BAA6B,eAAQ;kBACtC,OAAO,EAAE,sBAAe;kBACxB,IAAI,EAAE,oBAAW;kBACjB,UAAU,EAAE,KAAK;;;cAGX,iBAAQ,IAAI,eAAM,CAAC,SAAS;+BADrC,aAIE;;kBAFA,KAAK,EAAC,gBAAgB;kBACrB,KAAK,EAAE,YAAK;;;;cAGN,iBAAQ,IAAI,YAAK,CAAC,gBAAgB;+BAD3C,oBAQO,QARP,UAQO;kCAJL,oBAGE,KAHF,UAGE;+CAFiBA,MAAC;;;;;aAKd,YAAG,MAAM,6BAAoB;+CADrC,oBASI;;kBANF,KAAK,EAAC,mBAAmB;kBACzB,GAAG,EAAC,8BAA8B;kBAClC,MAAM,EAAC,QAAQ;kBACd,IAAI,EAAE,6BAAoB,CAAC,GAAG;;kBAE/B,oBAAqC,OAAlC,KAAK,EAAC,yBAAyB;;6CANjBA,MAAC,CAAC,6BAAoB,CAAC,OAAO;;;;;;UAW5C,iBAAQ;2BADjB,oBA8CM,OA9CN,UA8CM;eA1CQ,oBAAW,IAAI,gBAAO;iCAAlC,oBAAwK;sDAAjIA,MAAC,uCAAsC,IAAE;oBAAA,aAAiF;sBAAnE,EAAE,EAAE,gBAAO,CAAC,cAAc;;wCAAE,CAAyB;0DAAtB,gBAAO,CAAC,WAAW;;;;;mBAC/H,oBAAW;mCAA5B,oBAAqJ;wDAApHA,MAAC,yCAAwC,IAAE;sBAAA,aAAkE,0BAApD,EAAE,EAAE,0BAAiB;0CAAE,CAAe;4DAAZ,kBAAS;;;;;qBAC5G,kBAAS,KAAK,8BAAqB;qCAApD,oBAYO;0DAXFA,MAAC,yCAAwC,iBAC5C;0BACS,8BAAqB;2CAD9B,aAMc;;8BAJX,EAAE,EAAE,0BAAiB;8BACtB,aAAW,EAAC,8BAA8B;;gDAE1C,CAAe;kEAAZ,kBAAS;;;;2CAEd,oBAEO,sCADF,kBAAS;;;;eAGJ,eAAM,CAAC,OAAO;iCAA1B,oBAMO;sDALFA,MAAC,mCAAkC,iBACtC;oBAAA,aAGE;sBAFA,KAAK,EAAC,WAAW;sBAChB,KAAK,EAAE,YAAK,CAAC,iBAAiB;;;;;eAI3B,YAAK,CAAC,aAAa;iCAD3B,oBAkBO,QAlBP,WAkBO;sDAdFA,MAAC,yCAAwC,iBAC5C;qBACQ,YAAK,CAAC,SAAS,CAAC,QAAQ;uCADhC,aAMc;;0BAJX,EAAE,EAAE,YAAK,CAAC,SAAS,CAAC,QAAQ;0BAC7B,aAAW,EAAC,mCAAmC;;4CAE/C,CAAiC;8DAA9B,YAAK,CAAC,SAAS,CAAC,WAAW;;;;uCAEhC,oBAKO,QALP,WAKO,mBADF,YAAK,CAAC,SAAS,CAAC,WAAW;;;;eAGtB,YAAK,CAAC,eAAe;iCAAjC,oBAAkJ;sDAA5GA,MAAC,4CAA2C,GAAC;oBAAA,oBAAwD,QAAxD,WAAwD,mBAA5B,YAAK,CAAC,YAAY;;;;;;;MAGrI,YAyCO,0BAzCP,CAyCO;QAxCL,oBAuCM,OAvCN,WAuCM;UAtCJ,oBAqCM,OArCN,WAqCM;aAnCI,sBAAa,IAAI,oBAAW,KAAK,iBAAW,IAAI,eAAM;+BAD9D,oBAQS;;kBANP,IAAI,EAAC,QAAQ;kBACb,KAAK,EAAC,gCAAgC;kBACrC,QAAQ,GAAG,sBAAa,CAAC,OAAO;kBAChC,OAAK,0CAAE,qEAAmB;oCAExB,sBAAa,CAAC,KAAK;;;aAGhB,4BAAmB;+BAD3B,aAOE;;kBALC,KAAK,IAAI,0BAAiB;kBAC3B,WAAS,EAAC,IAAI;kBACb,OAAO,EAAE,yBAAgB;kBAC1B,KAAK,EAAC,OAAO;kBACZ,gBAAY,EAAE,4BAAmB;;;;aAI5B,oBAAW,IAAI,eAAM;+BAD7B,aAKE;;kBAHQ,KAAK,EAAE,oBAAW;0EAAX,oBAAW;kBACzB,OAAO,EAAE,oBAAW;kBACrB,KAAK,EAAC,OAAO;;;;aAIP,eAAM;+BADd,oBAUS;;kBARP,GAAG,EAAC,SAAS;kBACb,aAAW,EAAC,sBAAsB;kBAClC,eAAa,EAAC,MAAM;kBACpB,IAAI,EAAC,QAAQ;kBACb,KAAK,EAAC,+BAA+B;kBACpC,OAAK,0CAAE,qDAAW;;kBAEnB,oBAA+B,OAA5B,KAAK,EAAC,mBAAmB;;;;;;;;IAOtC,uCAAuB;;IACvB,aAIE;MAHC,QAAQ,EAAE,YAAK;MACf,IAAI,EAAE,mBAAa;MACnB,QAAQ,EAAE,uBAAiB;;;KAItB,eAAM,IAAI,eAAM,KAAK,eAAM,CAAC,UAAU;uBAD9C,aAKE;;UAHA,KAAK,EAAC,oBAAoB;UACzB,KAAK,EAAE,eAAM,CAAC,KAAK;UACnB,KAAK,EAAE,eAAM,CAAC,OAAO;;;;KAGhB,uBAAc,CAAC,IAAI;uBAD3B,aAKE;;UAHA,KAAK,EAAC,SAAS;UACf,KAAK,EAAC,OAAO;UACZ,KAAK,EAAEA,MAAC,2CAA2C,uBAAc;;;;IAGpE,YAAQ", "sourcesContent": ["<script>\nimport { KUBERNETES, PROJECT } from '@shell/config/labels-annotations';\nimport { FLEET, NAMESPACE, MANAGEMENT, HELM } from '@shell/config/types';\nimport ButtonGroup from '@shell/components/ButtonGroup';\nimport { BadgeState } from '@components/BadgeState';\nimport { Banner } from '@components/Banner';\nimport { get } from '@shell/utils/object';\nimport { NAME as FLEET_NAME } from '@shell/config/product/fleet';\nimport { HIDE_SENSITIVE } from '@shell/store/prefs';\nimport {\n  AS, _DETAIL, _CONFIG, _YAML, MODE, _CREATE, _EDIT, _VIEW, _UNFLAG, _GRAPH\n} from '@shell/config/query-params';\nimport { ExtensionPoint, PanelLocation } from '@shell/core/types';\nimport ExtensionPanel from '@shell/components/ExtensionPanel';\nimport TabTitle from '@shell/components/TabTitle';\n\n// i18n-uses resourceDetail.header.*\n\n/**\n * Resource Detail Masthead component.\n *\n * ToDo: this component seem to be picking up a lot of logic from special cases, could be simplified down to parameters and then customized per use-case via wrapper component\n */\nexport default {\n\n  name: 'MastheadResourceDetail',\n\n  components: {\n    BadgeState, Banner, ButtonGroup, ExtensionPanel, TabTitle\n  },\n  props: {\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    },\n\n    mode: {\n      type:    String,\n      default: 'create'\n    },\n\n    realMode: {\n      type:    String,\n      default: 'create'\n    },\n\n    as: {\n      type:    String,\n      default: _YAML,\n    },\n\n    hasGraph: {\n      type:    Boolean,\n      default: false\n    },\n\n    hasDetail: {\n      type:    Boolean,\n      default: false\n    },\n\n    hasEdit: {\n      type:    Boolean,\n      default: false\n    },\n\n    storeOverride: {\n      type:    String,\n      default: null,\n    },\n\n    resource: {\n      type:    String,\n      default: null,\n    },\n\n    resourceSubtype: {\n      type:    String,\n      default: null,\n    },\n\n    parentRouteOverride: {\n      type:    String,\n      default: null,\n    },\n\n    canViewYaml: {\n      type:    Boolean,\n      default: false,\n    }\n  },\n\n  data() {\n    return {\n      DETAIL_VIEW:       _DETAIL,\n      extensionType:     ExtensionPoint.PANEL,\n      extensionLocation: PanelLocation.DETAILS_MASTHEAD,\n    };\n  },\n\n  computed: {\n    dev() {\n      return this.$store.getters['prefs/dev'];\n    },\n\n    schema() {\n      const inStore = this.storeOverride || this.$store.getters['currentStore'](this.resource);\n\n      return this.$store.getters[`${ inStore }/schemaFor`]( this.resource );\n    },\n\n    isView() {\n      return this.mode === _VIEW;\n    },\n\n    isEdit() {\n      return this.mode === _EDIT;\n    },\n\n    isCreate() {\n      return this.mode === _CREATE;\n    },\n\n    isNamespace() {\n      return this.schema?.id === NAMESPACE;\n    },\n\n    isProject() {\n      return this.schema?.id === MANAGEMENT.PROJECT;\n    },\n\n    isProjectHelmChart() {\n      return this.schema?.id === HELM.PROJECTHELMCHART;\n    },\n\n    hasMultipleNamespaces() {\n      return !!this.value.namespaces;\n    },\n\n    namespace() {\n      if (this.value?.metadata?.namespace) {\n        return this.value?.metadata?.namespace;\n      }\n\n      return null;\n    },\n\n    detailsAction() {\n      return this.value?.detailsAction;\n    },\n\n    shouldHifenize() {\n      return (this.mode === 'view' || this.mode === 'edit') && this.resourceSubtype?.length && this.value?.nameDisplay?.length;\n    },\n\n    namespaceLocation() {\n      if (!this.isNamespace) {\n        return this.value.namespaceLocation || {\n          name:   'c-cluster-product-resource-id',\n          params: {\n            cluster:  this.$route.params.cluster,\n            product:  this.$store.getters['productId'],\n            resource: NAMESPACE,\n            id:       this.$route.params.namespace\n          }\n        };\n      }\n\n      return null;\n    },\n\n    isWorkspace() {\n      return this.$store.getters['productId'] === FLEET_NAME && !!this.value?.metadata?.namespace;\n    },\n\n    workspaceLocation() {\n      return {\n        name:   'c-cluster-product-resource-id',\n        params: {\n          cluster:  this.$route.params.cluster,\n          product:  this.$store.getters['productId'],\n          resource: FLEET.WORKSPACE,\n          id:       this.$route.params.namespace\n        }\n      };\n    },\n\n    project() {\n      if (this.isNamespace) {\n        const cluster = this.$store.getters['currentCluster'];\n\n        if (cluster) {\n          const id = (this.value?.metadata?.labels || {})[PROJECT];\n\n          return this.$store.getters['management/byId'](MANAGEMENT.PROJECT, `${ cluster.id }/${ id }`);\n        }\n      }\n\n      return null;\n    },\n\n    banner() {\n      if (this.value?.stateObj?.error) {\n        const defaultErrorMessage = this.t('resourceDetail.masthead.defaultBannerMessage.error', undefined, true);\n\n        return {\n          color:   'error',\n          message: this.value.stateObj.message || defaultErrorMessage\n        };\n      }\n\n      if (this.value?.spec?.paused) {\n        return {\n          color:   'info',\n          message: this.t('asyncButton.pause.description')\n        };\n      }\n\n      if (this.value?.stateObj?.transitioning) {\n        const defaultTransitioningMessage = this.t('resourceDetail.masthead.defaultBannerMessage.transitioning', undefined, true);\n\n        return {\n          color:   'info',\n          message: this.value.stateObj.message || defaultTransitioningMessage\n        };\n      }\n\n      return null;\n    },\n\n    parent() {\n      const displayName = this.value?.parentNameOverride || this.$store.getters['type-map/labelFor'](this.schema);\n      const product = this.$store.getters['currentProduct'].name;\n\n      const defaultLocation = {\n        name:   'c-cluster-product-resource',\n        params: {\n          resource: this.resource,\n          product,\n        }\n      };\n\n      const location = this.value?.parentLocationOverride || defaultLocation;\n\n      if (this.parentRouteOverride) {\n        location.name = this.parentRouteOverride;\n      }\n\n      const typeOptions = this.$store.getters[`type-map/optionsFor`]( this.resource );\n      const out = {\n        displayName, location, ...typeOptions\n      };\n\n      return out;\n    },\n\n    hideSensitiveData() {\n      return this.$store.getters['prefs/get'](HIDE_SENSITIVE);\n    },\n\n    sensitiveOptions() {\n      return [\n        {\n          tooltipKey: 'resourceDetail.masthead.sensitive.hide',\n          icon:       'icon-hide',\n          value:      true,\n        },\n        {\n          tooltipKey: 'resourceDetail.masthead.sensitive.show',\n          icon:       'icon-show',\n          value:      false\n        }\n      ];\n    },\n\n    viewOptions() {\n      const out = [];\n\n      if ( this.hasDetail ) {\n        out.push({\n          labelKey: 'resourceDetail.masthead.detail',\n          value:    _DETAIL,\n        });\n      }\n\n      if ( this.hasEdit && this.parent?.showConfigView !== false) {\n        out.push({\n          labelKey: 'resourceDetail.masthead.config',\n          value:    _CONFIG,\n        });\n      }\n\n      if ( this.hasGraph ) {\n        out.push({\n          labelKey: 'resourceDetail.masthead.graph',\n          value:    _GRAPH,\n        });\n      }\n\n      if ( this.canViewYaml ) {\n        out.push({\n          labelKey: 'resourceDetail.masthead.yaml',\n          value:    _YAML,\n        });\n      }\n\n      if ( out.length < 2 ) {\n        return null;\n      }\n\n      return out;\n    },\n\n    currentView: {\n      get() {\n        return this.as;\n      },\n\n      set(val) {\n        switch ( val ) {\n        case _DETAIL:\n          this.$router.applyQuery({\n            [MODE]: _UNFLAG,\n            [AS]:   _UNFLAG,\n          });\n          break;\n        case _CONFIG:\n          this.$router.applyQuery({\n            [MODE]: _UNFLAG,\n            [AS]:   _CONFIG,\n          });\n          break;\n        case _GRAPH:\n          this.$router.applyQuery({\n            [MODE]: _UNFLAG,\n            [AS]:   _GRAPH,\n          });\n          break;\n        case _YAML:\n          this.$router.applyQuery({\n            [MODE]: _UNFLAG,\n            [AS]:   _YAML,\n          });\n          break;\n        }\n      },\n    },\n\n    showSensitiveToggle() {\n      return !!this.value.hasSensitiveData && this.mode === _VIEW && this.as !== _YAML;\n    },\n\n    managedWarning() {\n      const { value } = this;\n      const labels = value?.metadata?.labels || {};\n\n      const managedBy = labels[KUBERNETES.MANAGED_BY] || '';\n      const appName = labels[KUBERNETES.MANAGED_NAME] || labels[KUBERNETES.INSTANCE] || '';\n\n      return {\n        show:    this.mode === _EDIT && !!managedBy,\n        type:    value?.kind || '',\n        hasName: appName ? 'yes' : 'no',\n        appName,\n        managedBy,\n      };\n    },\n\n    displayName() {\n      let displayName = this.value.nameDisplay;\n\n      if (this.isProjectHelmChart) {\n        displayName = this.value.projectDisplayName;\n      }\n\n      return this.shouldHifenize ? ` - ${ displayName }` : displayName;\n    },\n\n    location() {\n      const { parent } = this;\n\n      return parent?.location;\n    },\n\n    hideNamespaceLocation() {\n      return this.$store.getters['currentProduct'].hideNamespaceLocation || this.value.namespaceLocation === null;\n    },\n\n    resourceExternalLink() {\n      return this.value.resourceExternalLink;\n    },\n  },\n\n  methods: {\n    get,\n\n    showActions() {\n      this.$store.commit('action-menu/show', {\n        resources: this.value,\n        elem:      this.$refs.actions,\n      });\n    },\n\n    toggleSensitiveData(e) {\n      this.$store.dispatch('prefs/set', { key: HIDE_SENSITIVE, value: !!e });\n    },\n\n    invokeDetailsAction() {\n      const action = this.detailsAction;\n\n      if (action) {\n        const fn = this.value[action.action];\n\n        if (fn) {\n          fn.apply(this.value, []);\n        }\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <div class=\"masthead\">\n    <header>\n      <div class=\"title\">\n        <div class=\"primaryheader\">\n          <h1>\n            <TabTitle\n              v-if=\"isCreate\"\n              :showChild=\"false\"\n            >\n              {{ parent.displayName }}\n            </TabTitle>\n            <TabTitle\n              v-else\n              :showChild=\"false\"\n            >\n              {{ displayName }}\n            </TabTitle>\n            <router-link\n              v-if=\"location\"\n              :to=\"location\"\n              role=\"link\"\n              class=\"masthead-resource-list-link\"\n              :aria-label=\"parent.displayName\"\n            >\n              {{ parent.displayName }}:\n            </router-link>\n            <span v-else>{{ parent.displayName }}:</span>\n            <span v-if=\"value?.detailPageHeaderActionOverride && value?.detailPageHeaderActionOverride(realMode)\">{{ value?.detailPageHeaderActionOverride(realMode) }}</span>\n            <t\n              v-else\n              class=\"masthead-resource-title\"\n              :k=\"'resourceDetail.header.' + realMode\"\n              :subtype=\"resourceSubtype\"\n              :name=\"displayName\"\n              :escapehtml=\"false\"\n            />\n            <BadgeState\n              v-if=\"!isCreate && parent.showState\"\n              class=\"masthead-state\"\n              :value=\"value\"\n            />\n            <span\n              v-if=\"!isCreate && value.injectionEnabled\"\n              class=\"masthead-istio\"\n            >\n              <i\n                v-clean-tooltip=\"t('projectNamespaces.isIstioInjectionEnabled')\"\n                class=\"icon icon-sm icon-istio\"\n              />\n            </span>\n            <a\n              v-if=\"dev && !!resourceExternalLink\"\n              v-clean-tooltip=\"t(resourceExternalLink.tipsKey || 'generic.resourceExternalLinkTips')\"\n              class=\"resource-external\"\n              rel=\"nofollow noopener noreferrer\"\n              target=\"_blank\"\n              :href=\"resourceExternalLink.url\"\n            >\n              <i class=\"icon icon-external-link\" />\n            </a>\n          </h1>\n        </div>\n        <div\n          v-if=\"!isCreate\"\n          class=\"subheader\"\n        >\n          <span v-if=\"isNamespace && project\">{{ t(\"resourceDetail.masthead.project\") }}: <router-link :to=\"project.detailLocation\">{{ project.nameDisplay }}</router-link></span>\n          <span v-else-if=\"isWorkspace\">{{ t(\"resourceDetail.masthead.workspace\") }}: <router-link :to=\"workspaceLocation\">{{ namespace }}</router-link></span>\n          <span v-else-if=\"namespace && !hasMultipleNamespaces\">\n            {{ t(\"resourceDetail.masthead.namespace\") }}:\n            <router-link\n              v-if=\"!hideNamespaceLocation\"\n              :to=\"namespaceLocation\"\n              data-testid=\"masthead-subheader-namespace\"\n            >\n              {{ namespace }}\n            </router-link>\n            <span v-else>\n              {{ namespace }}\n            </span>\n          </span>\n          <span v-if=\"parent.showAge\">\n            {{ t(\"resourceDetail.masthead.age\") }}:\n            <LiveDate\n              class=\"live-date\"\n              :value=\"value.creationTimestamp\"\n            />\n          </span>\n          <span\n            v-if=\"value.showCreatedBy\"\n            data-testid=\"masthead-subheader-createdBy\"\n          >\n            {{ t(\"resourceDetail.masthead.createdBy\") }}:\n            <router-link\n              v-if=\"value.createdBy.location\"\n              :to=\"value.createdBy.location\"\n              data-testid=\"masthead-subheader-createdBy-link\"\n            >\n              {{ value.createdBy.displayName }}\n            </router-link>\n            <span\n              v-else\n              data-testid=\"masthead-subheader-createdBy_plain-text\"\n            >\n              {{ value.createdBy.displayName }}\n            </span>\n          </span>\n          <span v-if=\"value.showPodRestarts\">{{ t(\"resourceDetail.masthead.restartCount\") }}:<span class=\"live-data\"> {{ value.restartCount }}</span></span>\n        </div>\n      </div>\n      <slot name=\"right\">\n        <div class=\"actions-container align-start\">\n          <div class=\"actions\">\n            <button\n              v-if=\"detailsAction && currentView === DETAIL_VIEW && isView\"\n              type=\"button\"\n              class=\"btn role-primary actions mr-10\"\n              :disabled=\"!detailsAction.enabled\"\n              @click=\"invokeDetailsAction\"\n            >\n              {{ detailsAction.label }}\n            </button>\n            <ButtonGroup\n              v-if=\"showSensitiveToggle\"\n              :value=\"!!hideSensitiveData\"\n              icon-size=\"lg\"\n              :options=\"sensitiveOptions\"\n              class=\"mr-10\"\n              @update:value=\"toggleSensitiveData\"\n            />\n\n            <ButtonGroup\n              v-if=\"viewOptions && isView\"\n              v-model:value=\"currentView\"\n              :options=\"viewOptions\"\n              class=\"mr-10\"\n            />\n\n            <button\n              v-if=\"isView\"\n              ref=\"actions\"\n              data-testid=\"masthead-action-menu\"\n              aria-haspopup=\"true\"\n              type=\"button\"\n              class=\"btn role-multi-action actions\"\n              @click=\"showActions\"\n            >\n              <i class=\"icon icon-actions\" />\n            </button>\n          </div>\n        </div>\n      </slot>\n    </header>\n\n    <!-- Extension area -->\n    <ExtensionPanel\n      :resource=\"value\"\n      :type=\"extensionType\"\n      :location=\"extensionLocation\"\n    />\n\n    <Banner\n      v-if=\"banner && isView && !parent.hideBanner\"\n      class=\"state-banner mb-10\"\n      :color=\"banner.color\"\n      :label=\"banner.message\"\n    />\n    <Banner\n      v-if=\"managedWarning.show\"\n      color=\"warning\"\n      class=\"mb-20\"\n      :label=\"t('resourceDetail.masthead.managedWarning', managedWarning)\"\n    />\n\n    <slot />\n  </div>\n</template>\n\n<style lang='scss' scoped>\n  .masthead {\n    padding-bottom: 10px;\n    border-bottom: 1px solid var(--border);\n    margin-bottom: 10px;\n  }\n\n  HEADER {\n    margin: 0;\n    grid-template-columns: minmax(0, 1fr) auto;\n  }\n\n  .primaryheader {\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n\n    h1 {\n      margin: 0 0 0 -5px;\n      overflow-x: hidden;\n      display: flex;\n      flex-direction: row;\n      align-items: center;\n\n      .masthead-resource-title {\n        text-overflow: ellipsis;\n        overflow: hidden;\n        white-space: nowrap;\n      }\n\n      .masthead-resource-list-link {\n        margin: 5px;\n      }\n    }\n  }\n\n  .subheader{\n    display: flex;\n    flex-direction: row;\n    color: var(--input-label);\n    & > * {\n      margin: 5px 20px 5px 0px;\n    }\n\n    .live-data {\n      color: var(--body-text);\n      margin-left: 3px;\n    }\n  }\n\n  .state-banner {\n    margin: 3px 0 0 0;\n  }\n\n  .masthead-state {\n    margin-left: 8px;\n    font-size: initial;\n  }\n\n  .masthead-istio {\n    .icon {\n      vertical-align: middle;\n      color: var(--primary);\n    }\n  }\n\n  .left-right-split {\n    display: grid;\n    align-items: center;\n\n    .left-half {\n      grid-column: 1;\n    }\n\n    .right-half {\n      grid-column: 2;\n    }\n  }\n\n  div.actions-container > div.actions {\n    display: flex;\n    flex-direction: row;\n    justify-content: flex-end;\n  }\n\n  .resource-external {\n    font-size: 18px;\n  }\n</style>\n"]}]}