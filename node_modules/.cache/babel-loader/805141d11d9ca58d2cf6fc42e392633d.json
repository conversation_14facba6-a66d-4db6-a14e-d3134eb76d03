{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/EnvVars.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/EnvVars.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/EnvVars.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAElD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;EAEjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC5B,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;IACD,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KACf,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX;IACF;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE7C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACzC,CAAC,CAAC;;IAEF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAC5B;IACF;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC/C,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;MAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC3B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR;QACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB;MACF,CAAC,CAAC;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5E,CAAC;EACH,CAAC;AACH,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/EnvVars.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport ValueFromResource from '@shell/components/form/ValueFromResource';\nimport debounce from 'lodash/debounce';\nimport { randomStr } from '@shell/utils/string';\nimport { _VIEW } from '@shell/config/query-params';\n\nexport default {\n  components: { ValueFromResource },\n\n  props: {\n    /**\n     * Form mode for the component\n     */\n    mode: {\n      type:     String,\n      required: true,\n    },\n    configMaps: {\n      type:     Array,\n      required: true\n    },\n    secrets: {\n      type:     Array,\n      required: true\n    },\n    loading: {\n      default: false,\n      type:    Boolean\n    },\n    /**\n     * Container spec\n     */\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  data() {\n    const { env = [], envFrom = [] } = this.value;\n\n    const allEnv = [...env, ...envFrom].map((row) => {\n      return { value: row, id: randomStr(4) };\n    });\n\n    return {\n      env, envFrom, allEnv\n    };\n  },\n\n  computed: {\n    isView() {\n      return this.mode === _VIEW;\n    }\n  },\n\n  watch: {\n    'value.tty'(neu) {\n      if (neu) {\n        this.value['stdin'] = true;\n      }\n    }\n  },\n  created() {\n    this.queueUpdate = debounce(this.update, 500);\n  },\n\n  methods: {\n    update() {\n      delete this.value.env;\n      delete this.value.envFrom;\n      const envVarSource = [];\n      const envVar = [];\n\n      this.allEnv.forEach((row) => {\n        if (!row.value) {\n          return;\n        }\n        if (!!row.value.configMapRef || !!row.value.secretRef) {\n          envVarSource.push(row.value);\n        } else {\n          envVar.push(row.value);\n        }\n      });\n      this.value['env'] = envVar;\n      this.value['envFrom'] = envVarSource;\n    },\n\n    updateRow() {\n      this.queueUpdate();\n    },\n\n    removeRow(idx) {\n      this.allEnv.splice(idx, 1);\n      this.queueUpdate();\n    },\n\n    addFromReference() {\n      this.allEnv.push({ value: { name: '', valueFrom: {} }, id: randomStr(4) });\n    },\n  },\n};\n</script>\n<template>\n  <div :style=\"{'width':'100%'}\">\n    <div\n      v-for=\"(row, i) in allEnv\"\n      :key=\"i\"\n    >\n      <ValueFromResource\n        v-model:value=\"row.value\"\n        :all-secrets=\"secrets\"\n        :all-config-maps=\"configMaps\"\n        :mode=\"mode\"\n        :loading=\"loading\"\n        @remove=\"removeRow(i)\"\n        @update:value=\"updateRow\"\n      />\n    </div>\n    <button\n      v-if=\"!isView\"\n      v-t=\"'workload.container.command.addEnvVar'\"\n      type=\"button\"\n      class=\"btn role-tertiary add\"\n      data-testid=\"add-env-var\"\n      @click=\"addFromReference\"\n    />\n  </div>\n</template>\n\n<style lang='scss' scoped>\n.value-from :deep() {\n  .v-select {\n    height: 50px;\n  }\n\n  INPUT:not(.vs__search) {\n    height: 50px;\n  }\n}\n.value-from, .value-from-headers {\n  display: grid;\n  grid-template-columns: 20% 20% 20% 5% 20% auto;\n  grid-gap: $column-gutter;\n  align-items: center;\n  margin-bottom: 10px;\n}\n  .value-from-headers {\n    margin: 10px 0px 10px 0px;\n    color: var(--input-label);\n    }\n</style>\n"]}]}