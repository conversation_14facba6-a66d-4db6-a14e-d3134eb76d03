{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Group.vue?vue&type=template&id=5dc959c0&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Group.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Group.vue"], "names": ["t", "$attrs"], "mappings": ";;;;;;;;;;;;wBA6ME,oBA0FM;IAzFJ,KAAK,mBAAC,WAAW,aACE,YAAK,uBAAuB,mBAAU,kBAAkB,oBAAW,qBAAqB,sBAAa;;KAGhH,iBAAU;uBADlB,oBAuCM;;UArCJ,KAAK,mBAAC,QAAQ,aACK,mBAAU,cAAc,kBAAW;UACtD,IAAI,EAAC,QAAQ;UACb,QAAQ,EAAC,GAAG;UACX,YAAU,EAAE,YAAK,CAAC,YAAY,IAAI,YAAK,CAAC,KAAK;UAC7C,OAAK,uCAAE,sBAAa;UACpB,OAAK;2DAAQ,sBAAa;2DACb,sBAAa;;;UAE3B,YAgBO,2BAhBP,CAgBO;aAdG,oBAAW;+BADnB,aASc;;kBAPX,EAAE,EAAE,YAAK,CAAC,QAAQ,IAAI,KAAK;kBAC3B,KAAK,EAAE,YAAK,CAAC,QAAQ,IAAI,KAAK;kBAC9B,QAAQ,EAAE,EAAE;;oCAEb,CAEK;oBAFL,oBAEK;sCADH,oBAAyD;gDAArC,YAAK,CAAC,YAAY,IAAI,YAAK,CAAC,KAAK;;;;;;+BAGzD,oBAIK;kCADH,oBAAyD;4CAArC,YAAK,CAAC,YAAY,IAAI,YAAK,CAAC,KAAK;;;;;YAIhD,wBAAe,IAAI,kBAAW;6BADvC,oBAUE;;gBARA,KAAK,mBAAC,8BAA8B,0BACJ,mBAAU,uBAAuB,mBAAU;gBAC3E,IAAI,EAAC,QAAQ;gBACb,QAAQ,EAAC,GAAG;gBACX,YAAU,EAAEA,MAAC;gBACb,OAAK,uCAAE,aAAI,CAAC,MAAM;gBAClB,OAAK;iEAAQ,aAAI,CAAC,MAAM;iEACX,aAAI,CAAC,MAAM;;;;;;;KAIrB,mBAAU;uBADlB,oBA6CK,MA7CL,YA6CK;;UA3CH,KAAK,EAAC,oBAAoB;WAClBC,WAAM;6BAEd,oBAuCW,6BAtCc,YAAK,CAAC,kBAAW,IAAhC,KAAK,EAAE,GAAG;wEACZ,GAAG;eAGD,KAAK,CAAC,OAAO;iCADrB,oBAKK,QAHF,GAAG,EAAE,GAAG;oBAET,oBAAI;;mBAMO,KAAK,CAAC,kBAAW;mCAD9B,oBAiBK;sBApBL,sJAEU;qCACV,oBAiBK;wBAfF,GAAG,EAAE,KAAK,CAAC,IAAI;;uCAEhB,aAYE;;0BAXA,GAAG,EAAC,QAAQ;0BACX,GAAG,EAAE,QAAE,KAAK,KAAK,CAAC,IAAI;0BACtB,WAAS,EAAE,QAAE;0BACb,KAAK,EAAE,YAAK;0BACZ,cAAY,EAAE,kBAAW;0BACzB,cAAY,EAAE,kBAAW;0BACzB,KAAK,EAAE,KAAK;0BACZ,YAAU,EAAE,gBAAS;0BACrB,UAAQ,uCAAE,sBAAa,CAAC,MAAM;0BAC9B,QAAM,uCAAE,oBAAW,CAAC,MAAM;0BAC1B,OAAK,uCAAE,cAAK,CAAC,MAAM;;;;sBAIV,KAAK,CAAC,QAAQ,IAAI,YAAK,CAAC,IAAI;qCAD1C,aAOE;wBALC,GAAG,EAAE,QAAE,OAAO,KAAK,CAAC,IAAI;wBACxB,SAAO,EAAE,YAAK,UAAU,iBAAU;wBAClC,IAAI,EAAE,KAAK;wBACX,KAAK,EAAE,YAAK;wBACZ,UAAQ,uCAAE,mBAAU,CAAC,MAAM", "sourcesContent": ["<script>\nimport Type from '@shell/components/nav/Type';\nexport default {\n  name: 'Group',\n\n  components: { Type },\n\n  emits: ['expand', 'close'],\n\n  props: {\n    depth: {\n      type:    Number,\n      default: 0,\n    },\n\n    idPrefix: {\n      type:     String,\n      required: true,\n    },\n\n    group: {\n      type:     Object,\n      required: true,\n    },\n\n    childrenKey: {\n      type:    String,\n      default: 'children',\n    },\n\n    canCollapse: {\n      type:    Boolean,\n      default: true,\n    },\n\n    showHeader: {\n      type:    Boolean,\n      default: true,\n    },\n\n    fixedOpen: {\n      type:    Boolean,\n      default: false,\n    }\n  },\n\n  data() {\n    const id = (this.idPrefix || '') + this.group.name;\n\n    return { id, expanded: false };\n  },\n\n  computed: {\n    isGroupActive() {\n      return this.isOverview || (this.hasActiveRoute() && this.isExpanded && this.showHeader);\n    },\n\n    hasChildren() {\n      return this.group.children?.length > 0;\n    },\n\n    hasOverview() {\n      return this.group.children?.[0]?.overview;\n    },\n\n    onlyHasOverview() {\n      return this.group.children && this.group.children.length === 1 && this.hasOverview;\n    },\n\n    isOverview() {\n      if (this.group.children && this.group.children.length > 0) {\n        const grp = this.group.children[0];\n        const overviewRoute = grp?.route;\n\n        if (overviewRoute && grp.overview) {\n          const route = this.$router.resolve(overviewRoute || {});\n\n          return this.$route.fullPath.split('#')[0] === route?.fullPath;\n        }\n      }\n\n      return false;\n    },\n\n    isExpanded: {\n      get() {\n        return this.fixedOpen || this.group.isRoot || !!this.expanded;\n      },\n      set(v) {\n        this.expanded = v;\n      }\n    }\n  },\n\n  methods: {\n    expandGroup() {\n      this.isExpanded = true;\n      this.$emit('expand', this.group);\n    },\n\n    groupSelected() {\n      // Don't auto-select first group entry if we're already expanded and contain the currently-selected nav item\n      if (this.hasActiveRoute() && this.isExpanded) {\n        return;\n      } else {\n        // Remove all active class if click on group header and not active route\n        const headerEl = document.querySelectorAll('.header');\n\n        headerEl.forEach((el) => {\n          el.classList.remove('active');\n        });\n      }\n      this.expandGroup();\n\n      const items = this.group[this.childrenKey];\n\n      // Navigate to one of the child items (by default the first child)\n      if (items && items.length > 0) {\n        let index = 0;\n\n        // If there is a default type, use it\n        if (this.group.defaultType) {\n          const found = items.findIndex((i) => i.name === this.group.defaultType);\n\n          index = (found === -1) ? 0 : found;\n        }\n\n        const route = items[index].route;\n\n        if (route) {\n          this.$router.replace(route);\n        }\n      }\n    },\n\n    selectType() {\n      this.groupSelected();\n      this.close();\n    },\n\n    close() {\n      this.$emit('close');\n    },\n\n    // User clicked on the expander icon, so toggle the expansion so the user can see inside the group\n    peek($event) {\n      // Add active class to the current header if click on chevron icon\n      $event.target.parentElement.classList.remove('active');\n      if (this.hasActiveRoute() && this.isExpanded) {\n        $event.target.parentElement.classList.add('active');\n      }\n      this.isExpanded = !this.isExpanded;\n      $event.stopPropagation();\n    },\n\n    hasActiveRoute(items) {\n      if (!items) {\n        items = this.group;\n      }\n\n      for (const item of items.children) {\n        if (item.children && this.hasActiveRoute(item)) {\n          return true;\n        } else if (item.route) {\n          const navLevels = ['cluster', 'product', 'resource'];\n          const matchesNavLevel = navLevels.filter((param) => !this.$route.params[param] || this.$route.params[param] !== item.route.params[param]).length === 0;\n          const withoutHash = this.$route.hash ? this.$route.fullPath.slice(0, this.$route.fullPath.indexOf(this.$route.hash)) : this.$route.fullPath;\n          const withoutQuery = withoutHash.split('?')[0];\n\n          if (matchesNavLevel || this.$router.resolve(item.route).fullPath === withoutQuery) {\n            return true;\n          }\n        }\n      }\n\n      return false;\n    },\n\n    syncNav() {\n      const refs = this.$refs.groups;\n\n      if (refs) {\n        // Only expand one group - so after the first has been expanded, no more will\n        let canExpand = true;\n\n        refs.forEach((grp) => {\n          if (!grp.group.isRoot) {\n            if (canExpand) {\n              const isActive = this.hasActiveRoute(grp.group);\n\n              if (isActive) {\n                grp.isExpanded = true;\n                canExpand = false;\n                this.$nextTick(() => grp.syncNav());\n              }\n            }\n          }\n        });\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <div\n    class=\"accordion\"\n    :class=\"{[`depth-${depth}`]: true, 'expanded': isExpanded, 'has-children': hasChildren, 'group-highlight': isGroupActive}\"\n  >\n    <div\n      v-if=\"showHeader\"\n      class=\"header\"\n      :class=\"{'active': isOverview, 'noHover': !canCollapse}\"\n      role=\"button\"\n      tabindex=\"0\"\n      :aria-label=\"group.labelDisplay || group.label || ''\"\n      @click=\"groupSelected()\"\n      @keyup.enter=\"groupSelected()\"\n      @keyup.space=\"groupSelected()\"\n    >\n      <slot name=\"header\">\n        <router-link\n          v-if=\"hasOverview\"\n          :to=\"group.children[0].route\"\n          :exact=\"group.children[0].exact\"\n          :tabindex=\"-1\"\n        >\n          <h6>\n            <span v-clean-html=\"group.labelDisplay || group.label\" />\n          </h6>\n        </router-link>\n        <h6\n          v-else\n        >\n          <span v-clean-html=\"group.labelDisplay || group.label\" />\n        </h6>\n      </slot>\n      <i\n        v-if=\"!onlyHasOverview && canCollapse\"\n        class=\"icon toggle toggle-accordion\"\n        :class=\"{'icon-chevron-right': !isExpanded, 'icon-chevron-down': isExpanded}\"\n        role=\"button\"\n        tabindex=\"0\"\n        :aria-label=\"t('nav.ariaLabel.collapseExpand')\"\n        @click=\"peek($event, true)\"\n        @keyup.enter=\"peek($event, true)\"\n        @keyup.space=\"peek($event, true)\"\n      />\n    </div>\n    <ul\n      v-if=\"isExpanded\"\n      class=\"list-unstyled body\"\n      v-bind=\"$attrs\"\n    >\n      <template\n        v-for=\"(child, idx) in group[childrenKey]\"\n        :key=\"idx\"\n      >\n        <li\n          v-if=\"child.divider\"\n          :key=\"idx\"\n        >\n          <hr>\n        </li>\n        <!-- <div v-else-if=\"child[childrenKey] && hideGroup(child[childrenKey])\" :key=\"child.name\">\n          HIDDEN\n        </div> -->\n        <li\n          v-else-if=\"child[childrenKey]\"\n          :key=\"child.name\"\n        >\n          <Group\n            ref=\"groups\"\n            :key=\"id+'_'+child.name+'_children'\"\n            :id-prefix=\"id+'_'\"\n            :depth=\"depth + 1\"\n            :children-key=\"childrenKey\"\n            :can-collapse=\"canCollapse\"\n            :group=\"child\"\n            :fixed-open=\"fixedOpen\"\n            @selected=\"groupSelected($event)\"\n            @expand=\"expandGroup($event)\"\n            @close=\"close($event)\"\n          />\n        </li>\n        <Type\n          v-else-if=\"!child.overview || group.name === 'starred'\"\n          :key=\"id+'_' + child.name + '_type'\"\n          :is-root=\"depth == 0 && !showHeader\"\n          :type=\"child\"\n          :depth=\"depth\"\n          @selected=\"selectType($event)\"\n        />\n      </template>\n    </ul>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .header {\n    position: relative;\n    cursor: pointer;\n    color: var(--body-text);\n    height: 33px;\n    outline: none;\n\n    H6 {\n      color: var(--body-text);\n      user-select: none;\n      text-transform: none;\n      font-size: 14px;\n    }\n\n    > A {\n      display: block;\n      box-sizing:border-box;\n      height: 100%;\n      &:hover{\n        text-decoration: none;\n      }\n      &:focus{\n        outline:none;\n      }\n      > H6 {\n        text-transform: none;\n        padding: 8px 0 8px 16px;\n      }\n    }\n  }\n\n  .accordion {\n    .header {\n      &:focus-visible {\n        h6 span {\n          @include focus-outline;\n          outline-offset: 2px;\n        }\n      }\n      .toggle-accordion:focus-visible {\n        @include focus-outline;\n        outline-offset: -6px;\n      }\n\n      &.active {\n        color: var(--primary-hover-text);\n        background-color: var(--primary-hover-bg);\n\n        h6 {\n          padding: 8px 0 8px 16px;\n          font-weight: bold;\n          color: var(--primary-hover-text);\n        }\n\n        &:hover {\n          background-color: var(--primary-hover-bg);\n        }\n      }\n      &:hover:not(.active) {\n        background-color: var(--nav-hover);\n      }\n    }\n  }\n\n  .accordion {\n    &.depth-0 {\n      > .header {\n\n        &.noHover {\n          cursor: default;\n        }\n\n        > H6 {\n          text-transform: none;\n          padding: 8px 0 8px 16px;\n        }\n\n        > I {\n          position: absolute;\n          right: 0;\n          top: 0;\n          padding: 10px 10px 9px 7px;\n          user-select: none;\n        }\n      }\n\n      > .body {\n        margin-left: 0;\n      }\n\n      &.group-highlight {\n        background: var(--nav-active);\n      }\n    }\n\n    &.depth-1 {\n      > .header {\n        padding-left: 20px;\n        > H6 {\n          line-height: 18px;\n          padding: 8px 0 7px 5px !important;\n        }\n        > I {\n          padding: 10px 7px 9px 7px !important;\n        }\n      }\n\n      &:deep() .type-link > .label {\n        padding-left: 10px;\n      }\n    }\n\n    &:not(.depth-0) {\n      > .header {\n        > H6 {\n          // Child groups that aren't linked themselves\n          display: inline-block;\n          padding: 5px 0 5px 5px;\n        }\n\n        > I {\n          position: absolute;\n          right: 0;\n          top: 0;\n          padding: 6px 8px 6px 8px;\n        }\n      }\n    }\n  }\n\n  .body :deep() > .child.router-link-active,\n  .header :deep() > .child.router-link-exact-active {\n    padding: 0;\n\n    A, A I {\n      color: var(--primary-hover-text);\n    }\n\n    A {\n      color: var(--primary-hover-text);\n      background-color: var(--primary-hover-bg);\n      font-weight: bold;\n    }\n  }\n\n  .body :deep() > .child {\n    A {\n      border-left: solid 5px transparent;\n      line-height: 16px;\n      font-size: 14px;\n      padding-left: 24px;\n      display: flex;\n      justify-content: space-between;\n    }\n\n    A:focus {\n      outline: none;\n    }\n\n    &.root {\n      background: transparent;\n      A {\n        padding-left: 14px;\n      }\n    }\n  }\n</style>\n"]}]}