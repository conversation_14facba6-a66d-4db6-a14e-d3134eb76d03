{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/TopLevelMenu.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/TopLevelMenu.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/TopLevelMenu.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEjD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAExF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxD,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAChJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACxH,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;IAEpG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/E,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;;MAExB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACrF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;IAE3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1D,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvD,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC9E,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAE9C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjD,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;KAC9B;KACA,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC5D,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAC9D,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;KAC1D;KACA,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC5D,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAC9D,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;MAElD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE3I,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB;MACF,CAAC,CAAC;IACJ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClE,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEzE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACzE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACjB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACpB,CAAC;;QAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAElF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3G,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAC/C,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,CAAC;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC;MACH,CAAC,CAAC;;MAEF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjH,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7H,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACrD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACvC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MAClI;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAClC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC7C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC3C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE1F,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UAC1B;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;;MAEF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf;EACF,CAAC;;EAED,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACtE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/F,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEhG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;QAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR;;QAEA,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACrG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9E,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;QAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACtJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrF,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;QAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACtJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrF,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;;EAEH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClD,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrD,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3I;;MAEA,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACrF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC5B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC1F,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YACnB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACjC,CAAC;;UAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAExC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChD,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACzB;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC3B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE1H,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;MACR;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC7C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR;;MAEA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACpB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;MAEpB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACxE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE3C,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MAC7D,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE3C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5C,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAC3C;;MAEF,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpG,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAExC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAChD;;QAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5C,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;UAEzC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC;QAC/F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC;IACH,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC7D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC;MACP;IACF;EACF;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/TopLevelMenu.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport BrandImage from '@shell/components/BrandImage';\nimport ClusterIconMenu from '@shell/components/ClusterIconMenu';\nimport IconOrSvg from '../IconOrSvg';\nimport { BLANK_CLUSTER } from '@shell/store/store-types.js';\nimport { mapGetters } from 'vuex';\nimport { CAPI, COUNT, MANAGEMENT } from '@shell/config/types';\nimport { MENU_MAX_CLUSTERS, PINNED_CLUSTERS } from '@shell/store/prefs';\nimport { sortBy } from '@shell/utils/sort';\nimport { ucFirst } from '@shell/utils/string';\nimport { KEY } from '@shell/utils/platform';\nimport { getVersionInfo } from '@shell/utils/version';\nimport { SETTING } from '@shell/config/settings';\nimport { getProductFromRoute } from '@shell/utils/router';\nimport { isRancherPrime } from '@shell/config/version';\nimport Pinned from '@shell/components/nav/Pinned';\nimport { TopLevelMenuHelperPagination, TopLevelMenuHelperLegacy } from '@shell/components/nav/TopLevelMenu.helper';\nimport { debounce } from 'lodash';\nimport { sameContents } from '@shell/utils/array';\n\nexport default {\n  components: {\n    BrandImage,\n    ClusterIconMenu,\n    IconOrSvg,\n    Pinned\n  },\n\n  data() {\n    const { displayVersion, fullVersion } = getVersionInfo(this.$store);\n    const hasProvCluster = this.$store.getters[`management/schemaFor`](CAPI.RANCHER_CLUSTER);\n\n    const canPagination = this.$store.getters[`management/paginationEnabled`]({\n      id:      MANAGEMENT.CLUSTER,\n      context: 'side-bar',\n    }) && this.$store.getters[`management/paginationEnabled`]({\n      id:      CAPI.RANCHER_CLUSTER,\n      context: 'side-bar',\n    });\n    const helper = canPagination ? new TopLevelMenuHelperPagination({ $store: this.$store }) : new TopLevelMenuHelperLegacy({ $store: this.$store });\n    const provClusters = !canPagination && hasProvCluster ? this.$store.getters[`management/all`](CAPI.RANCHER_CLUSTER) : [];\n    const mgmtClusters = !canPagination ? this.$store.getters[`management/all`](MANAGEMENT.CLUSTER) : [];\n\n    if (!canPagination) {\n      // Reduce the impact of the initial load, but only if we're not making a request\n      const args = {\n        pinnedIds:   this.pinnedIds,\n        searchTerm:  this.search,\n        unPinnedMax: this.maxClustersToShow\n      };\n\n      helper.update(args);\n    }\n\n    return {\n      shown:             false,\n      displayVersion,\n      fullVersion,\n      clusterFilter:     '',\n      hasProvCluster,\n      maxClustersToShow: MENU_MAX_CLUSTERS,\n      emptyCluster:      BLANK_CLUSTER,\n      routeCombo:        false,\n\n      canPagination,\n      helper,\n      debouncedHelperUpdateSlow:   debounce((...args) => this.helper.update(...args), 1000),\n      debouncedHelperUpdateMedium: debounce((...args) => this.helper.update(...args), 750),\n      debouncedHelperUpdateQuick:  debounce((...args) => this.helper.update(...args), 200),\n      provClusters,\n      mgmtClusters,\n    };\n  },\n\n  computed: {\n    ...mapGetters(['clusterId']),\n    ...mapGetters(['clusterReady', 'isRancher', 'currentCluster', 'currentProduct', 'isRancherInHarvester']),\n    ...mapGetters({ features: 'features/get' }),\n\n    pinnedIds() {\n      return this.$store.getters['prefs/get'](PINNED_CLUSTERS);\n    },\n\n    showClusterSearch() {\n      return this.allClustersCount > this.maxClustersToShow;\n    },\n\n    allClustersCount() {\n      const counts = this.$store.getters[`management/all`](COUNT)?.[0]?.counts || {};\n      const count = counts[MANAGEMENT.CLUSTER] || {};\n\n      return count?.summary.count;\n    },\n\n    // New\n    search() {\n      return (this.clusterFilter || '').toLowerCase();\n    },\n\n    // New\n    showPinClusters() {\n      return !this.clusterFilter;\n    },\n\n    // New\n    searchActive() {\n      return !!this.search;\n    },\n\n    /**\n     * Only Clusters that are pinned\n     *\n     * (see description of helper.clustersPinned for more details)\n     */\n    pinFiltered() {\n      return this.hasProvCluster ? this.helper.clustersPinned : [];\n    },\n\n    /**\n     * Used to shown unpinned clusters OR results of text search\n     *\n     * (see description of helper.clustersOthers for more details)\n     */\n    clustersFiltered() {\n      return this.hasProvCluster ? this.helper.clustersOthers : [];\n    },\n\n    pinnedClustersHeight() {\n      const pinCount = this.pinFiltered.length;\n      const height = pinCount > 2 ? (pinCount * 43) : 90;\n\n      return `min-height: ${ height }px`;\n    },\n    clusterFilterCount() {\n      return this.clusterFilter ? this.clustersFiltered.length : this.allClustersCount;\n    },\n\n    multiClusterApps() {\n      const options = this.options;\n\n      return options.filter((opt) => {\n        const filterApps = (opt.inStore === 'management' || opt.isMultiClusterApp) && opt.category !== 'configuration' && opt.category !== 'legacy';\n\n        if (this.isRancherInHarvester) {\n          return filterApps && opt.category !== 'hci';\n        } else {\n          // We expect the location of Virtualization Management to remain the same when rancher-manage-support is not enabled\n          return filterApps;\n        }\n      });\n    },\n\n    configurationApps() {\n      const options = this.options;\n\n      return options.filter((opt) => opt.category === 'configuration');\n    },\n\n    hciApps() {\n      const options = this.options;\n\n      return options.filter((opt) => this.isRancherInHarvester && opt.category === 'hci');\n    },\n\n    options() {\n      const cluster = this.clusterId || this.$store.getters['defaultClusterId'];\n\n      // TODO plugin routes\n      const entries = this.$store.getters['type-map/activeProducts']?.map((p) => {\n        // Try product-specific index first\n        const to = p.to || {\n          name:   `c-cluster-${ p.name }`,\n          params: { cluster }\n        };\n\n        const matched = this.$router.getRoutes().filter((route) => route.name === to.name);\n\n        if ( !matched.length ) {\n          to.name = 'c-cluster-product';\n          to.params.product = p.name;\n        }\n\n        return {\n          label:             this.$store.getters['i18n/withFallback'](`product.\"${ p.name }\"`, null, ucFirst(p.name)),\n          icon:              `icon-${ p.icon || 'copy' }`,\n          svg:               p.svg,\n          value:             p.name,\n          removable:         p.removable !== false,\n          inStore:           p.inStore || 'cluster',\n          weight:            p.weight || 1,\n          category:          p.category || 'none',\n          to,\n          isMultiClusterApp: p.isMultiClusterApp,\n        };\n      });\n\n      return sortBy(entries, ['weight']);\n    },\n\n    canEditSettings() {\n      return (this.$store.getters['management/schemaFor'](MANAGEMENT.SETTING)?.resourceMethods || []).includes('PUT');\n    },\n\n    hasSupport() {\n      return isRancherPrime() || this.$store.getters['management/byId'](MANAGEMENT.SETTING, SETTING.SUPPORTED )?.value === 'true';\n    },\n\n    isCurrRouteClusterExplorer() {\n      return this.$route?.name?.startsWith('c-cluster');\n    },\n\n    productFromRoute() {\n      return getProductFromRoute(this.$route);\n    },\n\n    aboutText() {\n      // If a version number (starts with 'v') then use that\n      if (this.displayVersion.startsWith('v')) {\n        // Don't show the '.0' for a minor release (e.g. 2.8.0, 2.9.0 etc)\n        return !this.displayVersion.endsWith('.0') ? this.displayVersion : this.displayVersion.substr(0, this.displayVersion.length - 2);\n      }\n\n      // Default fallback to 'About'\n      return this.t('about.title');\n    },\n\n    largeAboutText() {\n      return this.aboutText.length > 6;\n    },\n\n    appBar() {\n      let activeFound = false;\n\n      // order is important for the object keys here\n      // since we want to check last pinFiltered and clustersFiltered\n      const appBar = {\n        hciApps:           this.hciApps,\n        multiClusterApps:  this.multiClusterApps,\n        configurationApps: this.configurationApps,\n        pinFiltered:       this.pinFiltered,\n        clustersFiltered:  this.clustersFiltered,\n      };\n\n      Object.keys(appBar).forEach((menuSection) => {\n        const menuSectionItems = appBar[menuSection];\n        const isClusterCheck = menuSection === 'pinFiltered' || menuSection === 'clustersFiltered';\n\n        // need to reset active state on other menu items\n        menuSectionItems.forEach((item) => {\n          item.isMenuActive = false;\n\n          if (!activeFound && this.checkActiveRoute(item, isClusterCheck)) {\n            activeFound = true;\n            item.isMenuActive = true;\n          }\n        });\n      });\n\n      return appBar;\n    }\n  },\n\n  // See https://github.com/rancher/dashboard/issues/12831 for outstanding performance related work\n  watch: {\n    $route() {\n      this.shown = false;\n    },\n\n    // Before SSP world all of these changes were kicked off given Vue change detection to properties in a computed method.\n    // Changes could come from two scenarios\n    // 1. Changes made by the user (pin / search). Could be tens per second\n    // 2. Changes made by rancher to clusters (state, label, etc change). Could be hundreds a second\n    // They can be restricted to help the churn caused from above\n    // 1. When SSP enabled reduce http spam\n    // 2. When SSP is disabled (legacy) reduce fn churn (this was a known performance customer issue)\n\n    pinnedIds: {\n      immediate: true,\n      handler(neu, old) {\n        if (sameContents(neu, old)) {\n          return;\n        }\n\n        // Low throughput (user click). Changes should be shown quickly\n        this.updateClusters(neu, 'quick');\n      }\n    },\n\n    search() {\n      // Medium throughput. Changes should be shown quickly, unless we want to reduce http spam in SSP world\n      this.updateClusters(this.pinnedIds, this.canPagination ? 'medium' : 'quick');\n    },\n\n    provClusters: {\n      handler(neu, old) {\n        // Potentially incredibly high throughput. Changes should be at least limited (slow if state change, quick if added/removed). Shouldn't get here if SSP\n        this.updateClusters(this.pinnedIds, neu?.length === old?.length ? 'slow' : 'quick');\n      },\n      deep:      true,\n      immediate: true,\n    },\n\n    mgmtClusters: {\n      handler(neu, old) {\n        // Potentially incredibly high throughput. Changes should be at least limited (slow if state change, quick if added/removed). Shouldn't get here if SSP\n        this.updateClusters(this.pinnedIds, neu?.length === old?.length ? 'slow' : 'quick');\n      },\n      deep:      true,\n      immediate: true,\n    },\n\n  },\n\n  mounted() {\n    document.addEventListener('keyup', this.handler);\n  },\n\n  beforeUnmount() {\n    document.removeEventListener('keyup', this.handler);\n  },\n\n  methods: {\n    checkActiveRoute(obj, isClusterRoute) {\n      // for Cluster links in main nav: check if route is a cluster explorer one + check if route cluster matches cluster obj id + check if curr product matches route product\n      if (isClusterRoute) {\n        return this.isCurrRouteClusterExplorer && this.$route?.params?.cluster === obj?.id && this.productFromRoute === this.currentProduct?.name;\n      }\n\n      // for remaining main nav items, check if curr product matches route product is enough\n      return this.productFromRoute === obj?.value;\n    },\n\n    handleKeyComboClick() {\n      this.routeCombo = !this.routeCombo;\n    },\n\n    clusterMenuClick(ev, cluster) {\n      if (this.routeCombo) {\n        ev.preventDefault();\n\n        if (this.isCurrRouteClusterExplorer && this.productFromRoute === this.currentProduct?.name) {\n          const clusterRoute = {\n            name:   this.$route.name,\n            params: { ...this.$route.params },\n            query:  { ...this.$route.query }\n          };\n\n          clusterRoute.params.cluster = cluster.id;\n\n          return this.$router.push(clusterRoute);\n        }\n      }\n\n      return this.$router.push(cluster.clusterRoute);\n    },\n\n    handler(e) {\n      if (e.keyCode === KEY.ESCAPE ) {\n        this.hide();\n      }\n    },\n\n    hide() {\n      this.shown = false;\n      if (this.clustersFiltered === 0) {\n        this.clusterFilter = '';\n      }\n    },\n\n    toggle() {\n      this.shown = !this.shown;\n    },\n\n    async goToHarvesterCluster() {\n      const localCluster = this.$store.getters['management/all'](CAPI.RANCHER_CLUSTER).find((C) => C.id === 'fleet-local/local');\n\n      try {\n        await localCluster.goToHarvesterCluster();\n      } catch {\n      }\n    },\n\n    getTooltipConfig(item, showWhenClosed = false) {\n      if (!item) {\n        return;\n      }\n\n      let contentText = '';\n      let content;\n      let popperClass = '';\n\n      // this is the normal tooltip scenario where we are just passing a string\n      if (typeof item === 'string') {\n        contentText = item;\n        content = this.shown ? null : contentText;\n\n      // if key combo is pressed, then we update the tooltip as well\n      } else if (this.routeCombo &&\n        typeof item === 'object' &&\n        !Array.isArray(item) &&\n        item !== null &&\n        item.ready) {\n        contentText = this.t('nav.keyComboTooltip');\n\n        if (showWhenClosed) {\n          content = !this.shown ? contentText : null;\n        } else {\n          content = this.shown ? contentText : null;\n        }\n\n      // this is scenario where we show a tooltip when we are on the expanded menu to show full description\n      } else {\n        contentText = item.label;\n        // this adds a class to the tooltip container so that we can control the max width\n        popperClass = 'menu-description-tooltip';\n\n        if (item.description) {\n          contentText += `<br><br>${ item.description }`;\n        }\n\n        if (showWhenClosed) {\n          content = !this.shown ? contentText : null;\n        } else {\n          content = this.shown ? contentText : null;\n\n          // this adds a class to adjust tooltip position so it doesn't overlap the cluster pinning action\n          popperClass += ' description-tooltip-pos-adjustment';\n        }\n      }\n\n      return {\n        content,\n        placement:     'right',\n        popperOptions: { modifiers: { preventOverflow: { enabled: false }, hide: { enabled: false } } },\n        popperClass\n      };\n    },\n\n    updateClusters(pinnedIds, speed = 'slow' | 'medium' | 'quick') {\n      const args = {\n        pinnedIds,\n        searchTerm:  this.search,\n        unPinnedMax: this.maxClustersToShow\n      };\n\n      switch (speed) {\n      case 'slow':\n        this.debouncedHelperUpdateSlow(args);\n        break;\n      case 'medium':\n        this.debouncedHelperUpdateMedium(args);\n        break;\n      case 'quick':\n        this.debouncedHelperUpdateQuick(args);\n        break;\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <div>\n    <!-- Overlay -->\n    <div\n      v-if=\"shown\"\n      class=\"side-menu-glass\"\n      @click=\"hide()\"\n    />\n    <transition name=\"fade\">\n      <!-- Side menu -->\n      <div\n        data-testid=\"side-menu\"\n        class=\"side-menu\"\n        :class=\"{'menu-open': shown, 'menu-close':!shown}\"\n        tabindex=\"-1\"\n        role=\"navigation\"\n        :aria-label=\"t('nav.ariaLabel.topLevelMenu')\"\n      >\n        <!-- Logo and name -->\n        <div class=\"title\">\n          <div\n            data-testid=\"top-level-menu\"\n            :aria-label=\"t('nav.expandCollapseAppBar')\"\n            role=\"button\"\n            tabindex=\"0\"\n            class=\"menu\"\n            @keyup.enter=\"toggle()\"\n            @keyup.space=\"toggle()\"\n            @click=\"toggle()\"\n          >\n            <svg\n              class=\"menu-icon\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n              height=\"24\"\n              viewBox=\"0 0 24 24\"\n              width=\"24\"\n              :alt=\"t('nav.alt.mainMenuIcon')\"\n            ><path\n              d=\"M0 0h24v24H0z\"\n              fill=\"none\"\n            /><path d=\"M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z\" /></svg>\n          </div>\n          <div class=\"side-menu-logo\">\n            <BrandImage\n              data-testid=\"side-menu__brand-img\"\n              :alt=\"t('nav.alt.mainMenuRancherLogo')\"\n              file-name=\"rancher-logo.svg\"\n            />\n          </div>\n        </div>\n\n        <!-- Menu body -->\n        <div class=\"body\">\n          <div>\n            <!-- Home button -->\n            <div @click=\"hide()\">\n              <router-link\n                class=\"option cluster selector home\"\n                :to=\"{ name: 'home' }\"\n                role=\"link\"\n                :aria-label=\"t('nav.ariaLabel.homePage')\"\n              >\n                <svg\n                  v-clean-tooltip=\"getTooltipConfig(t('nav.home'))\"\n                  class=\"top-menu-icon\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  width=\"24\"\n                ><path\n                  d=\"M0 0h24v24H0z\"\n                  fill=\"none\"\n                /><path d=\"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z\" /></svg>\n                <div class=\"home-text\">\n                  {{ t('nav.home') }}\n                </div>\n              </router-link>\n            </div>\n            <!-- Search bar -->\n            <div\n              v-if=\"showClusterSearch\"\n              class=\"clusters-search\"\n            >\n              <div class=\"clusters-search-count\">\n                <span>{{ clusterFilterCount }}</span>\n                {{ t('nav.search.clusters') }}\n                <i\n                  v-if=\"clusterFilter\"\n                  class=\"icon icon-filter_alt\"\n                />\n              </div>\n\n              <div\n                class=\"search\"\n              >\n                <input\n                  ref=\"clusterFilter\"\n                  v-model=\"clusterFilter\"\n                  :placeholder=\"t('nav.search.placeholder')\"\n                  :tabindex=\"!shown ? -1 : 0\"\n                  :aria-label=\"t('nav.search.ariaLabel')\"\n                >\n                <i\n                  class=\"magnifier icon icon-search\"\n                  :class=\"{ active: clusterFilter }\"\n                />\n                <i\n                  v-if=\"clusterFilter\"\n                  class=\"icon icon-close\"\n                  @click=\"clusterFilter=''\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <!-- Harvester extras -->\n          <template v-if=\"hciApps.length\">\n            <div class=\"category\" />\n            <div>\n              <a\n                v-if=\"isRancherInHarvester\"\n                class=\"option\"\n                tabindex=\"0\"\n                @click=\"goToHarvesterCluster()\"\n              >\n                <i\n                  class=\"icon icon-dashboard app-icon\"\n                />\n                <div>\n                  {{ t('nav.harvesterDashboard') }}\n                </div>\n              </a>\n            </div>\n            <div\n              v-for=\"(a, i) in appBar.hciApps\"\n              :key=\"i\"\n              @click=\"hide()\"\n            >\n              <router-link\n                class=\"option\"\n                :to=\"a.to\"\n                :class=\"{'active-menu-link': a.isMenuActive }\"\n                role=\"link\"\n                :aria-label=\"`${t('nav.ariaLabel.harvesterCluster')} ${ a.label }`\"\n              >\n                <IconOrSvg\n                  class=\"app-icon\"\n                  :icon=\"a.icon\"\n                  :src=\"a.svg\"\n                />\n                <div>{{ a.label }}</div>\n              </router-link>\n            </div>\n          </template>\n\n          <!-- Cluster menu -->\n          <template v-if=\"!!allClustersCount\">\n            <div\n              ref=\"clusterList\"\n              class=\"clusters\"\n              :style=\"pinnedClustersHeight\"\n            >\n              <!-- Pinned Clusters -->\n              <div\n                v-if=\"showPinClusters && pinFiltered.length\"\n                class=\"clustersPinned\"\n              >\n                <div\n                  v-for=\"(c, index) in appBar.pinFiltered\"\n                  :key=\"index\"\n                  :data-testid=\"`pinned-ready-cluster-${index}`\"\n                  @click=\"hide()\"\n                >\n                  <button\n                    v-if=\"c.ready\"\n                    v-shortkey.push=\"{windows: ['alt'], mac: ['option']}\"\n                    :data-testid=\"`pinned-menu-cluster-${ c.id }`\"\n                    class=\"cluster selector option\"\n                    :class=\"{'active-menu-link': c.isMenuActive }\"\n                    :to=\"c.clusterRoute\"\n                    role=\"button\"\n                    :aria-label=\"`${t('nav.ariaLabel.cluster')} ${ c.label }`\"\n                    @click.prevent=\"clusterMenuClick($event, c)\"\n                    @shortkey=\"handleKeyComboClick\"\n                  >\n                    <ClusterIconMenu\n                      v-clean-tooltip=\"getTooltipConfig(c, true)\"\n                      :cluster=\"c\"\n                      :route-combo=\"routeCombo\"\n                      class=\"rancher-provider-icon\"\n                    />\n                    <div\n                      v-clean-tooltip=\"getTooltipConfig(c)\"\n                      class=\"cluster-name\"\n                    >\n                      <p>{{ c.label }}</p>\n                      <p\n                        v-if=\"c.description\"\n                        class=\"description\"\n                      >\n                        {{ c.description }}\n                      </p>\n                    </div>\n                    <Pinned\n                      :cluster=\"c\"\n                      :tab-order=\"shown ? 0 : -1\"\n                    />\n                  </button>\n                  <span\n                    v-else\n                    class=\"option cluster selector disabled\"\n                    :data-testid=\"`pinned-menu-cluster-disabled-${ c.id }`\"\n                  >\n                    <ClusterIconMenu\n                      v-clean-tooltip=\"getTooltipConfig(c, true)\"\n                      :cluster=\"c\"\n                      class=\"rancher-provider-icon\"\n                    />\n                    <div\n                      v-clean-tooltip=\"getTooltipConfig(c)\"\n                      class=\"cluster-name\"\n                    >\n                      <p>{{ c.label }}</p>\n                      <p\n                        v-if=\"c.description\"\n                        class=\"description\"\n                      >\n                        {{ c.description }}\n                      </p>\n                    </div>\n                    <Pinned\n                      :cluster=\"c\"\n                      :tab-order=\"shown ? 0 : -1\"\n                    />\n                  </span>\n                </div>\n                <div\n                  v-if=\"clustersFiltered.length > 0\"\n                  class=\"category-title\"\n                >\n                  <hr>\n                </div>\n              </div>\n\n              <!-- Clusters Search result -->\n              <div class=\"clustersList\">\n                <div\n                  v-for=\"(c, index) in appBar.clustersFiltered\"\n                  :key=\"index\"\n                  :data-testid=\"`top-level-menu-cluster-${index}`\"\n                  @click=\"hide()\"\n                >\n                  <button\n                    v-if=\"c.ready\"\n                    v-shortkey.push=\"{windows: ['alt'], mac: ['option']}\"\n                    :data-testid=\"`menu-cluster-${ c.id }`\"\n                    class=\"cluster selector option\"\n                    :class=\"{'active-menu-link': c.isMenuActive }\"\n                    :to=\"c.clusterRoute\"\n                    role=\"button\"\n                    :aria-label=\"`${t('nav.ariaLabel.cluster')} ${ c.label }`\"\n                    @click=\"clusterMenuClick($event, c)\"\n                    @shortkey=\"handleKeyComboClick\"\n                  >\n                    <ClusterIconMenu\n                      v-clean-tooltip=\"getTooltipConfig(c, true)\"\n                      :cluster=\"c\"\n                      :route-combo=\"routeCombo\"\n                      class=\"rancher-provider-icon\"\n                    />\n                    <div\n                      v-clean-tooltip=\"getTooltipConfig(c)\"\n                      class=\"cluster-name\"\n                    >\n                      <p>{{ c.label }}</p>\n                      <p\n                        v-if=\"c.description\"\n                        class=\"description\"\n                      >\n                        {{ c.description }}\n                      </p>\n                    </div>\n                    <Pinned\n                      :class=\"{'showPin': c.pinned}\"\n                      :tab-order=\"shown ? 0 : -1\"\n                      :cluster=\"c\"\n                    />\n                  </button>\n                  <span\n                    v-else\n                    class=\"option cluster selector disabled\"\n                    :data-testid=\"`menu-cluster-disabled-${ c.id }`\"\n                  >\n                    <ClusterIconMenu\n                      v-clean-tooltip=\"getTooltipConfig(c, true)\"\n                      :cluster=\"c\"\n                      class=\"rancher-provider-icon\"\n                    />\n                    <div\n                      v-clean-tooltip=\"getTooltipConfig(c)\"\n                      class=\"cluster-name\"\n                    >\n                      <p>{{ c.label }}</p>\n                      <p\n                        v-if=\"c.description\"\n                        class=\"description\"\n                      >\n                        {{ c.description }}\n                      </p>\n                    </div>\n                    <Pinned\n                      :class=\"{'showPin': c.pinned}\"\n                      :tab-order=\"shown ? 0 : -1\"\n                      :cluster=\"c\"\n                    />\n                  </span>\n                </div>\n              </div>\n\n              <!-- No clusters message -->\n              <div\n                v-if=\"clustersFiltered.length === 0 && searchActive\"\n                data-testid=\"top-level-menu-no-results\"\n                class=\"none-matching\"\n              >\n                {{ t('nav.search.noResults') }}\n              </div>\n            </div>\n\n            <!-- See all clusters -->\n            <router-link\n              v-if=\"allClustersCount > maxClustersToShow\"\n              class=\"clusters-all\"\n              :to=\"{name: 'c-cluster-product-resource', params: {\n                cluster: emptyCluster,\n                product: 'manager',\n                resource: 'provisioning.cattle.io.cluster'\n              } }\"\n              role=\"link\"\n              :aria-label=\"t('nav.ariaLabel.seeAll')\"\n            >\n              <span>\n                {{ shown ? t('nav.seeAllClusters') : t('nav.seeAllClustersCollapsed') }}\n                <i class=\"icon icon-chevron-right\" />\n              </span>\n            </router-link>\n          </template>\n\n          <!-- MULTI CLUSTER APPS -->\n          <div class=\"category\">\n            <template v-if=\"multiClusterApps.length\">\n              <div\n                class=\"category-title\"\n              >\n                <hr>\n                <span>\n                  {{ t('nav.categories.multiCluster') }}\n                </span>\n              </div>\n              <div\n                v-for=\"(a, i) in appBar.multiClusterApps\"\n                :key=\"i\"\n                @click=\"hide()\"\n              >\n                <router-link\n                  class=\"option\"\n                  :class=\"{'active-menu-link': a.isMenuActive }\"\n                  :to=\"a.to\"\n                  role=\"link\"\n                  :aria-label=\"`${t('nav.ariaLabel.multiClusterApps')} ${ a.label }`\"\n                >\n                  <IconOrSvg\n                    v-clean-tooltip=\"getTooltipConfig(a.label)\"\n                    class=\"app-icon\"\n                    :icon=\"a.icon\"\n                    :src=\"a.svg\"\n                  />\n                  <span class=\"option-link\">{{ a.label }}</span>\n                </router-link>\n              </div>\n            </template>\n\n            <!-- Configuration apps menu -->\n            <template v-if=\"configurationApps.length\">\n              <div\n                class=\"category-title\"\n              >\n                <hr>\n                <span>\n                  {{ t('nav.categories.configuration') }}\n                </span>\n              </div>\n              <div\n                v-for=\"(a, i) in appBar.configurationApps\"\n                :key=\"i\"\n                @click=\"hide()\"\n              >\n                <router-link\n                  class=\"option\"\n                  :class=\"{'active-menu-link': a.isMenuActive }\"\n                  :to=\"a.to\"\n                  role=\"link\"\n                  :aria-label=\"`${t('nav.ariaLabel.configurationApps')} ${ a.label }`\"\n                >\n                  <IconOrSvg\n                    v-clean-tooltip=\"getTooltipConfig(a.label)\"\n                    class=\"app-icon\"\n                    :icon=\"a.icon\"\n                    :src=\"a.svg\"\n                  />\n                  <div>{{ a.label }}</div>\n                </router-link>\n              </div>\n            </template>\n          </div>\n        </div>\n\n        <!-- Footer -->\n        <div\n          class=\"footer\"\n        >\n          <div\n            v-if=\"canEditSettings\"\n            class=\"support\"\n            @click=\"hide()\"\n          >\n            <router-link\n              :to=\"{name: 'support'}\"\n              role=\"link\"\n              :aria-label=\"t('nav.ariaLabel.support')\"\n            >\n              {{ t('nav.support', {hasSupport}) }}\n            </router-link>\n          </div>\n          <div\n            class=\"version\"\n            :class=\"{'version-small': largeAboutText}\"\n            @click=\"hide()\"\n          >\n            <router-link\n              :to=\"{ name: 'about' }\"\n              role=\"link\"\n              :aria-label=\"t('nav.ariaLabel.about')\"\n            >\n              {{ aboutText }}\n            </router-link>\n          </div>\n        </div>\n      </div>\n    </transition>\n  </div>\n</template>\n\n<style lang=\"scss\">\n  .menu-description-tooltip {\n    max-width: 200px;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n  }\n\n  .description-tooltip-pos-adjustment {\n    // needs !important so that we can\n    // offset the tooltip a bit so it doesn't\n    // overlap the pin icon and cause bad UX\n    left: 48px !important;\n  }\n\n  .localeSelector, .footer-tooltip {\n    z-index: 1000;\n  }\n\n  .localeSelector {\n    .v-popper__inner {\n      padding: 10px 0;\n    }\n\n    .v-popper__arrow-container {\n      display: none;\n    }\n\n    .v-popper:focus {\n      outline: 0;\n    }\n  }\n\n  .theme-dark .cluster-name .description {\n    color: var(--input-label) !important;\n  }\n  .theme-dark .body .option  {\n    &:hover .cluster-name .description,\n    &.router-link-active .cluster-name .description,\n    &.active-menu-link .cluster-name .description {\n      color: var(--side-menu-desc) !important;\n  }\n  }\n</style>\n\n<style lang=\"scss\" scoped>\n  $clear-search-size: 20px;\n  $icon-size: 25px;\n  $option-padding: 9px;\n  $option-padding-left: 14px;\n  $option-height: $icon-size + $option-padding + $option-padding;\n\n  .side-menu {\n    .menu {\n      position: absolute;\n      width: $app-bar-collapsed-width;\n      height: 54px;\n      top: 0;\n      grid-area: menu;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n\n      &:focus-visible {\n        outline: none;\n\n        .menu-icon {\n          @include focus-outline;\n          outline-offset: 4px;  // Ensure there is space around the menu icon for the focus indication\n        }\n      }\n\n      .menu-icon {\n        width: 25px;\n        height: 25px;\n        fill: var(--header-btn-text);\n      }\n    }\n\n    position: absolute;\n    top: 0;\n    left: 0px;\n    bottom: 0;\n    width: $app-bar-collapsed-width;\n    background-color: var(--topmenu-bg);\n    z-index: 100;\n    border-right: 1px solid var(--topmost-border);\n    display: flex;\n    flex-direction: column;\n    padding: 0;\n    overflow: hidden;\n    transition: width 250ms;\n\n    &:focus, &:focus-visible {\n      outline: 0;\n    }\n\n    .option:focus-visible {\n      outline: 0;\n    }\n\n    &.menu-open {\n      width: 300px;\n      box-shadow: 3px 1px 3px var(--shadow);\n\n      // because of accessibility, we force pin action to be visible on menu open\n      .pin {\n        display: block !important;\n\n        &:focus-visible {\n          @include focus-outline;\n          outline-offset: 4px;\n        }\n      }\n    }\n\n    .title {\n      display: flex;\n      height: 55px;\n      flex: 0 0 55px;\n      width: 100%;\n      justify-content: flex-start;\n      align-items: center;\n\n      .menu {\n        display: flex;\n        justify-content: center;\n      }\n      .menu-icon {\n        width: 25px;\n        height: 25px;\n      }\n    }\n    .home {\n      svg {\n        width: 25px;\n        height: 25px;\n        margin-left: 9px;\n      }\n    }\n    .home-text {\n      margin-left: $option-padding-left - 7;\n    }\n    .body {\n      flex: 1;\n      display: flex;\n      flex-direction: column;\n      margin: 10px 0;\n      width: 300px;\n      overflow: auto;\n\n      .option {\n        align-items: center;\n        cursor: pointer;\n        display: flex;\n        color: var(--link);\n        font-size: 14px;\n        height: $option-height;\n        white-space: nowrap;\n        background-color: transparent;\n        width: 100%;\n        border-radius: 0;\n        border: none;\n\n        .cluster-badge-logo-text {\n          color: var(--default-active-text);\n          font-weight: 500;\n        }\n\n        .pin {\n          font-size: 16px;\n          margin-left: auto;\n          display: none;\n          color: var(--body-text);\n          &.showPin {\n            display: block;\n          }\n        }\n\n        .cluster-name {\n          line-height: normal;\n\n          & > p {\n            width: 182px;\n            white-space: nowrap;\n            overflow: hidden;\n            text-overflow: ellipsis;\n            text-align: left;\n\n            &.description {\n              font-size: 12px;\n              padding-right: 8px;\n              color: var(--darker);\n            }\n          }\n        }\n\n        &:hover {\n          text-decoration: none;\n\n          .pin {\n            display: block;\n            color: var(--darker-text);\n          }\n        }\n        &.disabled {\n          background: transparent;\n          cursor: not-allowed;\n\n          .rancher-provider-icon,\n          .cluster-name p {\n            filter: grayscale(1);\n            color: var(--muted) !important;\n          }\n\n          .pin {\n            cursor: pointer;\n          }\n        }\n\n        &:focus {\n          outline: 0;\n          box-shadow: none;\n        }\n\n        > i, > img {\n          display: block;\n          font-size: $icon-size;\n          margin-right: 14px;\n          &:not(.pin){\n            width: 42px;\n          }\n        }\n\n        .rancher-provider-icon,\n        svg {\n          margin-right: 16px;\n          fill: var(--link);\n        }\n\n        .top-menu-icon {\n          outline-offset: 4px;\n        }\n\n        &.router-link-active, &.active-menu-link {\n          &:focus-visible {\n            .top-menu-icon, .app-icon {\n              @include focus-outline;\n            }\n          }\n\n          &:focus-visible .rancher-provider-icon {\n            @include focus-outline;\n            outline-offset: -4px;\n          }\n\n          background: var(--primary-hover-bg);\n          color: var(--primary-hover-text);\n\n          svg {\n            fill: var(--primary-hover-text);\n          }\n\n          i {\n            color: var(--primary-hover-text);\n          }\n\n          div .description {\n            color: var(--default);\n          }\n        }\n\n        &:focus-visible {\n          .top-menu-icon, .rancher-provider-icon, .app-icon {\n            @include focus-outline;\n          }\n        }\n\n        &:hover {\n          color: var(--primary-hover-text);\n          background: var(--primary-hover-bg);\n          > div {\n            color: var(--primary-hover-text);\n\n            .description {\n              color: var(--default);\n            }\n          }\n          svg {\n            fill: var(--primary-hover-text);\n          }\n          div {\n            color: var(--primary-hover-text);\n          }\n          &.disabled {\n            background: transparent;\n            color: var(--muted);\n\n            > .pin {\n              color:var(--default-text);\n              display: block;\n            }\n          }\n        }\n      }\n\n      .option, .option-disabled {\n        padding: $option-padding 0 $option-padding $option-padding-left;\n      }\n\n      .search {\n        position: relative;\n        > input {\n          background-color: transparent;\n          padding-right: 35px;\n          padding-left: 25px;\n          height: 32px;\n        }\n        > .magnifier {\n          position: absolute;\n          top: 12px;\n          left: 8px;\n          width: 12px;\n          height: 12px;\n          font-size: 12px;\n          opacity: 0.4;\n\n          &.active {\n            opacity: 1;\n\n            &:hover {\n              color: var(--body-text);\n            }\n          }\n        }\n        > i {\n          position: absolute;\n          font-size: 12px;\n          top: 12px;\n          right: 8px;\n          opacity: 0.7;\n          cursor: pointer;\n          &:hover {\n            color: var(--disabled-bg);\n          }\n        }\n      }\n\n      .clusters-all {\n        display: flex;\n        flex-direction: row-reverse;\n        margin-right: 16px;\n        margin-top: 10px;\n\n        &:focus-visible {\n          outline: none;\n        }\n\n        span {\n          display: flex;\n          align-items: center;\n        }\n\n        &:focus-visible span {\n          @include focus-outline;\n          outline-offset: 4px;\n        }\n      }\n\n      .clusters {\n        overflow-y: auto;\n\n         a, span {\n          margin: 0;\n         }\n\n        &-search {\n          display: flex;\n          align-items: center;\n          gap: 14px;\n          margin: 16px 0;\n          height: 42px;\n\n          .search {\n            transition: all 0.25s ease-in-out;\n            transition-delay: 2s;\n            width: 72%;\n            height: 36px;\n\n            input {\n              height: 100%;\n            }\n          }\n\n          &-count {\n            position: relative;\n            display: flex;\n            flex-direction: column;\n            width: 42px;\n            height: 42px;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            color: var(--default-active-text);\n            margin-left: $option-padding-left;\n            border-radius: 5px;\n            font-size: 10px;\n            font-weight: bold;\n\n            span {\n              font-size: 14px;\n            }\n\n            .router-link-active {\n              &:hover {\n                text-decoration: none;\n              }\n            }\n\n            i {\n              font-size: 12px;\n              position: absolute;\n              right: -3px;\n              top: 2px;\n            }\n          }\n        }\n      }\n\n      .none-matching {\n        width: 100%;\n        text-align: center;\n        padding: 8px\n      }\n\n      .clustersPinned {\n        .category {\n          &-title {\n            margin: 8px 0;\n            margin-left: 16px;\n            hr {\n              margin: 0;\n              width: 94%;\n              transition: all 0.25s ease-in-out;\n              max-width: 100%;\n            }\n          }\n        }\n        .pin {\n          display: block;\n        }\n      }\n\n      .category {\n        display: flex;\n        flex-direction: column;\n        place-content: flex-end;\n        flex: 1;\n\n        &-title {\n          display: flex;\n          flex-direction: row;\n          align-items: flex-start;\n          align-items: center;\n          margin: 15px 0;\n          margin-left: 16px;\n          font-size: 14px;\n          text-transform: uppercase;\n\n          span {\n            transition: all 0.25s ease-in-out;\n            display: flex;\n            max-height: 16px;\n          }\n\n          hr {\n            margin: 0;\n            max-width: 50px;\n            width: 0;\n            transition: all 0.25s ease-in-out;\n          }\n        }\n\n         i {\n            padding-left: $option-padding-left - 5;\n          }\n      }\n    }\n\n    &.menu-open {\n      .option {\n        &.router-link-active, &.active-menu-link {\n          &:focus-visible {\n            @include focus-outline;\n            border-radius: 0;\n            outline-offset: -4px;\n\n            .top-menu-icon, .app-icon, .rancher-provider-icon {\n              outline: none;\n              border-radius: 0;\n            }\n          }\n        }\n\n        &:focus-visible {\n          @include focus-outline;\n          outline-offset: -4px;\n\n          .top-menu-icon, .app-icon, .rancher-provider-icon {\n            outline: none;\n            border-radius: 0;\n          }\n        }\n      }\n    }\n\n    &.menu-close {\n      .side-menu-logo  {\n        opacity: 0;\n      }\n      .category {\n        &-title {\n          span {\n            opacity: 0;\n          }\n\n          hr {\n            width: 40px;\n          }\n        }\n      }\n      .clusters-all {\n        flex-direction: row;\n        margin-left: $option-padding-left + 2;\n\n        span {\n          i {\n            display: none;\n          }\n        }\n      }\n\n      .clustersPinned {\n        .category {\n          &-title {\n            hr {\n              width: 40px;\n            }\n          }\n        }\n      }\n\n      .footer {\n        margin: 20px 10px;\n        width: 50px;\n\n        .support {\n          display: none;\n        }\n\n        .version{\n          text-align: center;\n\n          &.version-small {\n            font-size: 12px;\n          }\n        }\n      }\n    }\n\n    .footer {\n      margin: 20px;\n      width: 240px;\n      display: flex;\n      flex: 0;\n      flex-direction: row;\n      > * {\n        flex: 1;\n        color: var(--link);\n\n        &:first-child {\n          text-align: left;\n        }\n        &:last-child {\n          text-align: right;\n        }\n        text-align: center;\n      }\n\n      .support a:focus-visible {\n        @include focus-outline;\n        outline-offset: 4px;\n      }\n\n      .version {\n        cursor: pointer;\n\n        a:focus-visible {\n          @include focus-outline;\n          outline-offset: 4px;\n        }\n      }\n    }\n  }\n\n  .side-menu-glass {\n    position: absolute;\n    top: 0;\n    left: 0px;\n    bottom: 0;\n    width: 100vw;\n    z-index: 99;\n    opacity: 1;\n  }\n\n  .side-menu-logo {\n    align-items: center;\n    display: flex;\n    transform: translateX($app-bar-collapsed-width);\n    opacity: 1;\n    max-width: 200px;\n    width: 100%;\n    justify-content: center;\n    transition: all 0.5s;\n    overflow: hidden;\n    & IMG {\n      object-fit: contain;\n      height: 21px;\n      max-width: 200px;\n    }\n  }\n\n  .fade-enter-active, .fade-leave-active {\n    transition: all 0.25s;\n    transition-timing-function: ease;\n  }\n\n  .fade-leave-active {\n    transition: all 0.25s;\n  }\n\n  .fade-leave-to {\n    left: -300px;\n  }\n\n  .fade-enter {\n    left: -300px;\n  }\n\n  .locale-chooser {\n    cursor: pointer;\n  }\n\n  .localeSelector {\n    :deep() .v-popper__inner {\n      padding: 50px 0;\n    }\n\n    :deep() .v-popper__arrow-container {\n      display: none;\n    }\n\n    :deep() .v-popper:focus {\n      outline: 0;\n    }\n\n    li {\n      padding: 8px 20px;\n\n      &:hover {\n        background-color: var(--primary-hover-bg);\n        color: var(--primary-hover-text);\n        text-decoration: none;\n      }\n    }\n  }\n</style>\n"]}]}