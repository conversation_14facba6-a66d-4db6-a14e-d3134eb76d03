{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DashboardOptions.vue?vue&type=template&id=40e0fc26&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DashboardOptions.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DashboardOptions.vue"], "names": ["t"], "mappings": ";;qBAuGO,KAAK,EAAC,eAAe;;;qBAUnB,KAAK,EAAC,eAAe;;;;;;wBAV5B,oBAsBM,OAtBN,UAsBM;KArBO,0BAAmB;uBAA9B,oBAKM;UAJJ,aAGE;YAFQ,KAAK,EAAE,YAAK,CAAC,IAAI;oEAAV,YAAK,CAAC,IAAI;YACxB,OAAO,EAAE,0BAAoB;;;uBAGlC,oBAEM;UADJ,oBAAO;;;IAET,oBAWM,OAXN,UAWM;MAVJ,aAIE;QAHQ,KAAK,EAAE,YAAK,CAAC,KAAK;gEAAX,YAAK,CAAC,KAAK;QACzB,OAAO,EAAE,kBAAY;QACrB,KAAK,EAAEA,MAAC;;;MAEX,aAIE;QAHQ,KAAK,EAAE,YAAK,CAAC,WAAW;gEAAjB,YAAK,CAAC,WAAW;QAC/B,OAAO,EAAE,oBAAc;QACvB,KAAK,EAAEA,MAAC", "sourcesContent": ["<script>\r\nimport ButtonGroup from '@shell/components/ButtonGroup';\r\nimport LabeledSelect from '@shell/components/form/LabeledSelect';\r\n\r\nexport default {\r\n  components: { ButtonGroup, LabeledSelect },\r\n  props:      {\r\n    value: {\r\n      type:     Object,\r\n      required: true,\r\n    },\r\n    hasSummaryAndDetail: {\r\n      type:    Boolean,\r\n      default: true,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      range:        null,\r\n      rangeOptions: [\r\n        {\r\n          label: this.t('generic.units.time.5m'),\r\n          value: '5m',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.1h'),\r\n          value: '1h',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.6h'),\r\n          value: '6h',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.1d'),\r\n          value: '1d',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.7d'),\r\n          value: '7d',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.30d'),\r\n          value: '30d',\r\n        },\r\n      ],\r\n      refreshOptions: [\r\n        {\r\n          label: this.t('generic.units.time.5s'),\r\n          value: '5s',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.10s'),\r\n          value: '10s',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.30s'),\r\n          value: '30s',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.1m'),\r\n          value: '1m',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.5m'),\r\n          value: '5m',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.15m'),\r\n          value: '15m',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.30m'),\r\n          value: '30m',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.1h'),\r\n          value: '1h',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.2h'),\r\n          value: '2h',\r\n        },\r\n        {\r\n          label: this.t('generic.units.time.1d'),\r\n          value: '1d',\r\n        }\r\n      ],\r\n      detailSummaryOptions: [\r\n        {\r\n          label: this.t('graphOptions.detail'),\r\n          value: 'detail'\r\n        },\r\n        {\r\n          label: this.t('graphOptions.summary'),\r\n          value: 'summary'\r\n        }\r\n      ]\r\n    };\r\n  }\r\n};\r\n</script>\r\n\r\n<template>\r\n  <div class=\"graph-options\">\r\n    <div v-if=\"hasSummaryAndDetail\">\r\n      <ButtonGroup\n        v-model:value=\"value.type\"\n        :options=\"detailSummaryOptions\"\n      />\r\n    </div>\r\n    <div v-else>\r\n      <div />\r\n    </div>\r\n    <div class=\"range-refresh\">\r\n      <LabeledSelect\n        v-model:value=\"value.range\"\n        :options=\"rangeOptions\"\n        :label=\"t('graphOptions.range')\"\n      />\r\n      <LabeledSelect\n        v-model:value=\"value.refreshRate\"\n        :options=\"refreshOptions\"\n        :label=\"t('graphOptions.refresh')\"\n      />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<style lang='scss' scoped>\r\n.graph-options {\r\n    &, .range-refresh {\r\n      display: flex;\r\n      flex-direction: row;\r\n      justify-content: flex-end;\r\n    }\r\n\r\n    & {\r\n      justify-content: space-between;\r\n      align-items: center;\r\n    }\r\n\r\n    .labeled-select {\r\n        width: 100px;\r\n        margin-left: 10px;\r\n    }\r\n}\r\n</style>\r\n"]}]}