{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GrafanaDashboard.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GrafanaDashboard.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GrafanaDashboard.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;IACV,CAAC,CAAC,CAAC,EAAE;MACH,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACpB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACzG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE7D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC5D,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;QAErI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACjB;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACtF,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9C,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5F,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACxB;IACF;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B;;IAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACtB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAChC,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEnD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;UACxE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;UACxF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;UACrF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE9D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxD;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACtB;MACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACT,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC3B,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpG,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC3C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;UAErC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC;MACJ;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACZ;;QAEA,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACvC;;;QAGA,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACd;;QAEA,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACd;;QAEA,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf;;QAEA,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB;;QAEA,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf;MACF,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE3C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB;EACF;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GrafanaDashboard.vue", "sourceRoot": "", "sourcesContent": ["<script>\r\nimport Loading from '@shell/components/Loading';\r\nimport { Banner } from '@components/Banner';\r\nimport { computeDashboardUrl } from '@shell/utils/grafana';\r\nimport { CATALOG } from '@shell/config/types';\r\n\r\nexport default {\r\n  components: { Banner, Loading },\r\n  props:      {\r\n    url: {\r\n      type:     String,\r\n      required: true,\r\n    },\r\n    vars: {\r\n      type:    Object,\r\n      default: () => ({})\r\n    },\r\n    range: {\r\n      type:    String,\r\n      default: null\r\n    },\r\n    refreshRate: {\r\n      type:    String,\r\n      default: null\r\n    },\r\n    // change the grafana url prefix for local clusters in certain monitoring versions\r\n    // project monitoring (projectHelmCharts) supply a grafana url that never needs to be modified in this way\r\n    modifyPrefix: {\r\n      type:    Boolean,\r\n      default: true\r\n    },\r\n    backgroundColor: {\r\n      type:    String,\r\n      default: '#1b1c21'\r\n    },\r\n    theme: {\r\n      type:    String,\r\n      default: 'dark'\r\n    }\r\n  },\r\n  async fetch() {\r\n    const inStore = this.$store.getters['currentProduct'].inStore;\r\n\r\n    if (this.$store.getters[`${ inStore }/canList`](CATALOG.APP)) {\r\n      try {\r\n        const res = await this.$store.dispatch(`${ inStore }/find`, { type: CATALOG.APP, id: 'cattle-monitoring-system/rancher-monitoring' });\r\n\r\n        this.monitoringVersion = res?.currentVersion;\r\n      } catch (err) {}\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false, error: false, interval: null, errorTimer: null, monitoringVersion: ''\r\n    };\r\n  },\r\n  computed: {\r\n    currentUrl() {\r\n      return this.computeUrl();\r\n    },\r\n    grafanaUrl() {\r\n      return this.currentUrl.replace('&kiosk', '');\r\n    },\r\n    graphWindow() {\r\n      return this.$refs.frame?.contentWindow;\r\n    },\r\n    graphHistory() {\r\n      return this.graphWindow?.history;\r\n    },\r\n    graphDocument() {\r\n      return this.graphWindow?.document;\r\n    }\r\n  },\r\n  watch: {\r\n    currentUrl(neu) {\r\n      // Should consider changing `this.graphWindow?.angular` to something like `!loaded && !error`\r\n      // https://github.com/rancher/dashboard/pull/5802\r\n      if (this.graphHistory && this.graphWindow?.angular) {\r\n        this.graphWindow.location.replace(neu);\r\n      }\r\n    },\r\n\r\n    error(neu) {\r\n      if (neu) {\r\n        this.errorTimer = setInterval(() => {\r\n          this.reload();\r\n        }, 45000);\r\n      } else {\r\n        clearInterval(this.errorTimer);\r\n        this.errorTimer = null;\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$refs.frame.onload = this.inject;\r\n    this.poll();\r\n  },\r\n  beforeUnmount() {\r\n    if (this.interval) {\r\n      clearInterval(this.interval);\r\n    }\r\n\r\n    if (this.errorTimer) {\r\n      clearInterval(this.errorTimer);\r\n    }\r\n  },\r\n  methods: {\r\n    poll() {\r\n      if (this.interval) {\r\n        clearInterval(this.interval);\r\n        this.interval = null;\r\n      }\r\n\r\n      this.interval = setInterval(() => {\r\n        try {\r\n          const graphWindow = this.$refs.frame?.contentWindow;\r\n\r\n          // Note. getElementsByClassName won't work, following a grafana bump class names are now unique - for example css-2qng6u-panel-container\r\n          const errorElements = graphWindow.document.querySelectorAll('[class$=\"alert-error');\r\n          const errorCornerElements = graphWindow.document.querySelectorAll('[class$=\"panel-info-corner--error');\r\n          const panelInFullScreenElements = graphWindow.document.querySelectorAll('[class$=\"panel-in-fullscreen');\r\n          const panelContainerElements = graphWindow.document.querySelectorAll('[class$=\"panel-container');\r\n          const error = errorElements.length > 0 || errorCornerElements.length > 0;\r\n          const loaded = panelInFullScreenElements.length > 0 || panelContainerElements.length > 0;\r\n          const errorMessageElms = graphWindow.document.getElementsByTagName('pre');\r\n          const errorMessage = errorMessageElms.length > 0 ? errorMessageElms[0].innerText : '';\r\n          const isFailure = errorMessage.includes('\"status\": \"Failure\"');\r\n\r\n          if (error) {\r\n            throw new Error('An error was detected in the iframe');\r\n          }\r\n\r\n          this['loading'] = !loaded;\r\n          this['error'] = isFailure;\r\n        } catch (ex) {\r\n          this['error'] = true;\r\n          this['loading'] = false;\r\n          clearInterval(this.interval);\r\n          this.interval = null;\r\n        }\r\n      }, 100);\r\n    },\r\n    computeFromTo() {\r\n      return {\r\n        from: `now-${ this.range }`,\r\n        to:   `now`\r\n      };\r\n    },\r\n    computeUrl() {\r\n      const embedUrl = this.url;\r\n      const clusterId = this.$store.getters['currentCluster'].id;\r\n      const params = this.computeParams();\r\n\r\n      return computeDashboardUrl(this.monitoringVersion, embedUrl, clusterId, params, this.modifyPrefix);\r\n    },\r\n    computeParams() {\r\n      const params = {};\r\n      const fromTo = this.computeFromTo();\r\n\r\n      if (fromTo.from) {\r\n        params.from = fromTo.from;\r\n      }\r\n\r\n      if (fromTo.to) {\r\n        params.to = fromTo.to;\r\n      }\r\n\r\n      if (this.refreshRate) {\r\n        params.refresh = this.refreshRate;\r\n      }\r\n\r\n      if (Object.keys(this.vars).length > 0) {\r\n        Object.entries(this.vars).forEach((entry) => {\r\n          const paramName = `var-${ entry[0] }`;\r\n\r\n          params[paramName] = entry[1];\r\n        });\r\n      }\r\n\r\n      params.theme = this.theme;\r\n\r\n      return params;\r\n    },\r\n    reload(ev) {\r\n      ev && ev.preventDefault();\r\n      this.$refs.frame.contentWindow.location.reload();\r\n      this.poll();\r\n    },\r\n    injectCss() {\r\n      const style = document.createElement('style');\r\n\r\n      style.innerHTML = `\r\n        body .grafana-app .dashboard-content {\r\n          background: ${ this.backgroundColor };\r\n          padding: 0;\r\n        }\r\n\r\n        body .grafana-app .layout {\r\n          background: ${ this.backgroundColor };\r\n        }\r\n\r\n\r\n        body .grafana-app .dashboard-content .panel-container {\r\n          background-color: initial;\r\n          border: none;\r\n        }\r\n\r\n        body .grafana-app .dashboard-content .panel-wrapper {\r\n          height: 100%;\r\n        }\r\n\r\n        body .grafana-app .panel-menu-container {\r\n          display: none;\r\n        }\r\n\r\n        body .grafana-app .panel-title {\r\n          cursor: default;\r\n        }\r\n\r\n        body .grafana-app .panel-title .panel-title-text div {\r\n          display: none;\r\n        }\r\n      `;\r\n\r\n      const graphWindow = this.$refs.frame?.contentWindow;\r\n      const graphDocument = graphWindow?.document;\r\n\r\n      if (graphDocument.head) {\r\n        graphDocument.head.appendChild(style);\r\n      }\r\n    },\r\n\r\n    inject() {\r\n      this.injectCss();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<template>\r\n  <div class=\"grafana-graph\">\r\n    <Banner\r\n      v-if=\"error\"\r\n      color=\"error\"\r\n      style=\"z-index: 1000\"\r\n    >\r\n      <div class=\"text-center\">\r\n        {{ t('grafanaDashboard.failedToLoad') }} <a\r\n          href=\"#\"\r\n          @click=\"reload\"\r\n        >{{ t('grafanaDashboard.reload') }}</a>\r\n      </div>\r\n    </Banner>\r\n    <iframe\r\n      v-show=\"!error\"\r\n      ref=\"frame\"\r\n      :class=\"{loading, frame: true}\"\r\n      :src=\"currentUrl\"\r\n      frameborder=\"0\"\r\n      scrolling=\"no\"\r\n    />\r\n    <div v-if=\"loading\">\r\n      <Loading />\r\n    </div>\r\n    <div\r\n      v-if=\"!loading && !error\"\r\n      class=\"external-link\"\r\n    >\r\n      <!-- https://github.com/harvester/harvester-installer/pull/512/files -->\r\n      <!-- It is necessary to include the parameter referer when accessing the Grafana page. -->\r\n      <!-- This parameter is required by the backend to identify the origin of the request from which cluster -->\r\n      <!-- The matching mechanism as follows: -->\r\n      <!-- ~.*/k8s/clusters/(c-m-.+)/.* -->\r\n      <!-- ~.*/dashboard/harvester/c/(c-m-.+)/.* -->\r\n      <a\r\n        :href=\"grafanaUrl\"\r\n        target=\"_blank\"\r\n        rel=\"noopener nofollow\"\r\n      >{{ t('grafanaDashboard.grafana') }} <i class=\"icon icon-external-link\" /></a>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<style lang='scss' scoped>\r\n.grafana-graph {\r\n  position: relative;\r\n  min-height: 100%;\r\n  min-width: 100%;\r\n\r\n  & :deep() .content {\r\n    position: relative;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-items: center;\r\n    width: 100%;\r\n    height: 100%;\r\n    padding: 0;\r\n  }\r\n\r\n  & :deep() .overlay {\r\n    position: static;\r\n    background-color: initial;\r\n  }\r\n\r\n  iframe {\r\n    position: absolute;\r\n    left: 0;\r\n    right: 0;\r\n    top: 20px;\r\n    bottom: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    overflow: hidden;\r\n\r\n    &.loading {\r\n      visibility: hidden;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}