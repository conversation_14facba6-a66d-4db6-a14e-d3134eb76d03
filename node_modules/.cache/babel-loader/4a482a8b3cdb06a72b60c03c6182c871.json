{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/ProjectRow.vue?vue&type=template&id=db070704&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/ProjectRow.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/form/ResourceQuota/ProjectRow.vue"], "names": ["typeOption"], "mappings": ";;;;EAwEI,KAAK,EAAC,KAAK;;;;;;;UADLA,eAAU;qBADlB,oBAmCM,OAnCN,UAmCM;QA/BJ,aAOE;UANC,KAAK,EAAE,WAAI;UACZ,KAAK,EAAC,OAAO;UACZ,IAAI,EAAE,WAAI;UACV,OAAO,EAAE,YAAK;UACf,aAAW,EAAC,uBAAuB;UAClC,gBAAY,uCAAE,mBAAU,CAAC,MAAM;;;QAElC,aAWE;UAVC,KAAK,EAAE,2BAAkB,CAAC,WAAI;UAC/B,KAAK,EAAC,OAAO;UACZ,IAAI,EAAE,WAAI;UACV,WAAW,EAAEA,eAAU,CAAC,WAAW;UACnC,SAAS,EAAEA,eAAU,CAAC,SAAS;UAC/B,gBAAc,EAAEA,eAAU,CAAC,aAAa;UACxC,WAAS,EAAEA,eAAU,CAAC,QAAQ;UAC9B,iBAAe,EAAE,IAAI;UACtB,aAAW,EAAC,gCAAgC;UAC3C,gBAAY,uCAAE,yBAAgB,kBAAkB,WAAI,EAAE,MAAM;;;QAE/D,aAUE;UATC,KAAK,EAAE,2CAAkC,CAAC,WAAI;UAC9C,IAAI,EAAE,WAAI;UACV,WAAW,EAAEA,eAAU,CAAC,WAAW;UACnC,SAAS,EAAEA,eAAU,CAAC,SAAS;UAC/B,gBAAc,EAAEA,eAAU,CAAC,aAAa;UACxC,WAAS,EAAEA,eAAU,CAAC,QAAQ;UAC9B,iBAAe,EAAE,IAAI;UACtB,aAAW,EAAC,kCAAkC;UAC7C,gBAAY,uCAAE,yBAAgB,kCAAkC,WAAI,EAAE,MAAM", "sourcesContent": ["<script>\nimport Select from '@shell/components/form/Select';\nimport UnitInput from '@shell/components/form/UnitInput';\nimport { ROW_COMPUTED } from './shared';\n\nexport default {\n  emits: ['type-change'],\n\n  components: { Select, UnitInput },\n\n  props: {\n    mode: {\n      type:     String,\n      required: true,\n    },\n    types: {\n      type:    Array,\n      default: () => []\n    },\n    type: {\n      type:    String,\n      default: ''\n    },\n    value: {\n      type:    Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n\n  computed: {\n    ...ROW_COMPUTED,\n\n    resourceQuotaLimit: {\n      get() {\n        return this.value.spec.resourceQuota?.limit || {};\n      },\n    },\n\n    namespaceDefaultResourceQuotaLimit: {\n      get() {\n        return this.value.spec.namespaceDefaultResourceQuota?.limit || {};\n      },\n    }\n  },\n\n  methods: {\n    updateType(type) {\n      if (typeof this.value.spec.resourceQuota?.limit[this.type] !== 'undefined') {\n        delete this.value.spec.resourceQuota.limit[this.type];\n      }\n      if (typeof this.value.spec.namespaceDefaultResourceQuota?.limit[this.type] !== 'undefined') {\n        delete this.value.spec.namespaceDefaultResourceQuota.limit[this.type];\n      }\n\n      this.$emit('type-change', type);\n    },\n\n    updateQuotaLimit(prop, type, val) {\n      if (!this.value.spec[prop]) {\n        this.value.spec[prop] = { limit: { } };\n      }\n\n      this.value.spec[prop].limit[type] = val;\n    }\n  },\n};\n</script>\n<template>\n  <div\n    v-if=\"typeOption\"\n    class=\"row\"\n  >\n    <Select\n      :value=\"type\"\n      class=\"mr-10\"\n      :mode=\"mode\"\n      :options=\"types\"\n      data-testid=\"projectrow-type-input\"\n      @update:value=\"updateType($event)\"\n    />\n    <UnitInput\n      :value=\"resourceQuotaLimit[type]\"\n      class=\"mr-10\"\n      :mode=\"mode\"\n      :placeholder=\"typeOption.placeholder\"\n      :increment=\"typeOption.increment\"\n      :input-exponent=\"typeOption.inputExponent\"\n      :base-unit=\"typeOption.baseUnit\"\n      :output-modifier=\"true\"\n      data-testid=\"projectrow-project-quota-input\"\n      @update:value=\"updateQuotaLimit('resourceQuota', type, $event)\"\n    />\n    <UnitInput\n      :value=\"namespaceDefaultResourceQuotaLimit[type]\"\n      :mode=\"mode\"\n      :placeholder=\"typeOption.placeholder\"\n      :increment=\"typeOption.increment\"\n      :input-exponent=\"typeOption.inputExponent\"\n      :base-unit=\"typeOption.baseUnit\"\n      :output-modifier=\"true\"\n      data-testid=\"projectrow-namespace-quota-input\"\n      @update:value=\"updateQuotaLimit('namespaceDefaultResourceQuota', type, $event)\"\n    />\n  </div>\n</template>\n\n<style lang='scss' scoped>\n  .row {\n    display: flex;\n    flex-direction: row;\n    justify-content: space-evenly;\n  }\n</style>\n"]}]}