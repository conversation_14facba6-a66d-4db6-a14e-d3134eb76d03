{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??clonedRuleSet-44.use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownMenu.vue?vue&type=script&setup=true&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownMenu.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgZGVmaW5lQ29tcG9uZW50IGFzIF9kZWZpbmVDb21wb25lbnQgfSBmcm9tICd2dWUnOwppbXBvcnQgeyBSY0Ryb3Bkb3duLCBSY0Ryb3Bkb3duSXRlbSwgUmNEcm9wZG93blNlcGFyYXRvciwgUmNEcm9wZG93blRyaWdnZXIgfSBmcm9tICdAY29tcG9uZW50cy9SY0Ryb3Bkb3duJzsKaW1wb3J0IEljb25PclN2ZyBmcm9tICdAc2hlbGwvY29tcG9uZW50cy9JY29uT3JTdmcnOwpleHBvcnQgZGVmYXVsdCAvKkBfX1BVUkVfXyovIF9kZWZpbmVDb21wb25lbnQoewogICAgX19uYW1lOiAnUmNEcm9wZG93bk1lbnUnLAogICAgcHJvcHM6IHsKICAgICAgICBvcHRpb25zOiB7IHR5cGU6IEFycmF5LCByZXF1aXJlZDogdHJ1ZSB9LAogICAgICAgIGJ1dHRvblJvbGU6IHsgdHlwZTogbnVsbCwgcmVxdWlyZWQ6IGZhbHNlLCBkZWZhdWx0OiAncHJpbWFyeScgfSwKICAgICAgICBidXR0b25TaXplOiB7IHR5cGU6IG51bGwsIHJlcXVpcmVkOiBmYWxzZSwgZGVmYXVsdDogdW5kZWZpbmVkIH0sCiAgICAgICAgYnV0dG9uQXJpYUxhYmVsOiB7IHR5cGU6IFN0cmluZywgcmVxdWlyZWQ6IGZhbHNlIH0sCiAgICAgICAgZHJvcGRvd25BcmlhTGFiZWw6IHsgdHlwZTogU3RyaW5nLCByZXF1aXJlZDogZmFsc2UgfSwKICAgICAgICBkYXRhVGVzdGlkOiB7IHR5cGU6IFN0cmluZywgcmVxdWlyZWQ6IGZhbHNlIH0KICAgIH0sCiAgICBlbWl0czogWyd1cGRhdGU6b3BlbicsICdzZWxlY3QnXSwKICAgIHNldHVwKF9fcHJvcHMsIHsgZXhwb3NlOiBfX2V4cG9zZSwgZW1pdDogX19lbWl0IH0pIHsKICAgICAgICBfX2V4cG9zZSgpOwogICAgICAgIGNvbnN0IGVtaXQgPSBfX2VtaXQ7CiAgICAgICAgY29uc3QgaGFzT3B0aW9ucyA9IChvcHRpb25zKSA9PiB7CiAgICAgICAgICAgIHJldHVybiBvcHRpb25zLmxlbmd0aCAhPT0gdW5kZWZpbmVkID8gb3B0aW9ucy5sZW5ndGggOiBPYmplY3Qua2V5cyhvcHRpb25zKS5sZW5ndGggPiAwOwogICAgICAgIH07CiAgICAgICAgY29uc3QgX19yZXR1cm5lZF9fID0geyBlbWl0LCBoYXNPcHRpb25zLCBnZXQgUmNEcm9wZG93bigpIHsgcmV0dXJuIFJjRHJvcGRvd247IH0sIGdldCBSY0Ryb3Bkb3duSXRlbSgpIHsgcmV0dXJuIFJjRHJvcGRvd25JdGVtOyB9LCBnZXQgUmNEcm9wZG93blNlcGFyYXRvcigpIHsgcmV0dXJuIFJjRHJvcGRvd25TZXBhcmF0b3I7IH0sIGdldCBSY0Ryb3Bkb3duVHJpZ2dlcigpIHsgcmV0dXJuIFJjRHJvcGRvd25UcmlnZ2VyOyB9LCBnZXQgSWNvbk9yU3ZnKCkgeyByZXR1cm4gSWNvbk9yU3ZnOyB9IH07CiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KF9fcmV0dXJuZWRfXywgJ19faXNTY3JpcHRTZXR1cCcsIHsgZW51bWVyYWJsZTogZmFsc2UsIHZhbHVlOiB0cnVlIH0pOwogICAgICAgIHJldHVybiBfX3JldHVybmVkX187CiAgICB9Cn0pOwo="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownMenu.vue?vue&type=script&setup=true&lang=ts", "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownMenu.vue"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,IAAI,gBAAgB,EAAE,MAAM,KAAK,CAAA;ACCzD,OAAO,EACL,UAAU,EACV,cAAc,EACd,mBAAmB,EACnB,iBAAgB,EACjB,MAAM,wBAAwB,CAAA;AAE/B,OAAO,SAAS,MAAM,6BAA6B,CAAA;ADGnD,eAAe,aAAa,CAAA,gBAAgB,CAAC;IAC3C,MAAM,EAAE,gBAAgB;IACxB,KAAK,EAAE;QACL,OAAO,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE;QACxC,UAAU,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE;QAC/D,UAAU,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE;QAC/D,eAAe,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE;QAClD,iBAAiB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE;QACpD,UAAU,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE;KAC9C;IACD,KAAK,EAAE,CAAC,aAAa,EAAE,QAAQ,CAAC;IAChC,KAAK,CAAC,OAAY,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE;QACtD,QAAQ,EAAE,CAAC;QCRb,MAAM,IAAI,GAAG,MAAsC,CAAA;QAEnD,MAAM,UAAU,GAAG,CAAC,OAAyB,EAAE,EAAE;YAC/C,OAAO,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAA;QACxF,CAAC,CAAA;QDcD,MAAM,YAAY,GAAG,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,UAAU,KAAK,OAAO,UAAU,CAAA,CAAC,CAAC,EAAE,IAAI,cAAc,KAAK,OAAO,cAAc,CAAA,CAAC,CAAC,EAAE,IAAI,mBAAmB,KAAK,OAAO,mBAAmB,CAAA,CAAC,CAAC,EAAE,IAAI,iBAAiB,KAAK,OAAO,iBAAiB,CAAA,CAAC,CAAC,EAAE,IAAI,SAAS,KAAK,OAAO,SAAS,CAAA,CAAC,CAAC,EAAE,CAAA;QACvR,MAAM,CAAC,cAAc,CAAC,YAAY,EAAE,iBAAiB,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAA;QAC1F,OAAO,YAAY,CAAA;IACnB,CAAC;CAEA,CAAC,CAAA", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdownMenu.vue.tsx", "sourceRoot": "", "sourcesContent": ["import { defineComponent as _defineComponent } from 'vue'\nimport {\n  Rc<PERSON>ropdown,\n  Rc<PERSON>ropdownItem,\n  RcDropdownSeparator,\n  RcDropdownTrigger\n} from '@components/RcDropdown';\nimport { RcDropdownMenuComponentProps, DropdownOption } from './types';\nimport IconOrSvg from '@shell/components/IconOrSvg';\n\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'RcDropdownMenu',\n  props: {\n    options: { type: Array, required: true },\n    buttonRole: { type: null, required: false, default: 'primary' },\n    buttonSize: { type: null, required: false, default: undefined },\n    buttonAriaLabel: { type: String, required: false },\n    dropdownAriaLabel: { type: String, required: false },\n    dataTestid: { type: String, required: false }\n  },\n  emits: ['update:open', 'select'],\n  setup(__props: any, { expose: __expose, emit: __emit }) {\n  __expose();\n\n\n\nconst emit = __emit;\n\nconst hasOptions = (options: DropdownOption[]) => {\n  return options.length !== undefined ? options.length : Object.keys(options).length > 0;\n};\n\nconst __returned__ = { emit, hasOptions, get RcDropdown() { return RcDropdown }, get RcDropdownItem() { return RcDropdownItem }, get RcDropdownSeparator() { return RcDropdownSeparator }, get RcDropdownTrigger() { return RcDropdownTrigger }, get IconOrSvg() { return IconOrSvg } }\nObject.defineProperty(__returned__, '__isScriptSetup', { enumerable: false, value: true })\nreturn __returned__\n}\n\n})", "<script setup lang=\"ts\">\nimport {\n  Rc<PERSON><PERSON>down,\n  Rc<PERSON>ropdownItem,\n  RcDropdownSeparator,\n  RcDropdownTrigger\n} from '@components/RcDropdown';\nimport { RcDropdownMenuComponentProps, DropdownOption } from './types';\nimport IconOrSvg from '@shell/components/IconOrSvg';\n\nwithDefaults(defineProps<RcDropdownMenuComponentProps>(), {\n  buttonRole: 'primary',\n  buttonSize: undefined,\n});\n\nconst emit = defineEmits(['update:open', 'select']);\n\nconst hasOptions = (options: DropdownOption[]) => {\n  return options.length !== undefined ? options.length : Object.keys(options).length > 0;\n};\n</script>\n\n<template>\n  <rc-dropdown\n    :aria-label=\"dropdownAriaLabel\"\n    @update:open=\"(e: boolean) => emit('update:open', e)\"\n  >\n    <rc-dropdown-trigger\n      :[buttonRole]=\"true\"\n      :[buttonSize]=\"true\"\n      :data-testid=\"dataTestid\"\n      :aria-label=\"buttonAriaLabel\"\n    >\n      <i class=\"icon icon-actions\" />\n    </rc-dropdown-trigger>\n    <template #dropdownCollection>\n      <template\n        v-for=\"(a) in options\"\n        :key=\"a.label\"\n      >\n        <rc-dropdown-item\n          v-if=\"!a.divider\"\n          @click=\"(e: MouseEvent) => emit('select', e, a)\"\n        >\n          <template #before>\n            <IconOrSvg\n              v-if=\"a.icon || a.svg\"\n              :icon=\"a.icon\"\n              :src=\"a.svg\"\n              class=\"icon\"\n              color=\"header\"\n            />\n          </template>\n          {{ a.label }}\n        </rc-dropdown-item>\n        <rc-dropdown-separator\n          v-else\n        />\n      </template>\n      <rc-dropdown-item\n        v-if=\"!hasOptions(options)\"\n        disabled\n      >\n        No actions available\n      </rc-dropdown-item>\n    </template>\n  </rc-dropdown>\n</template>\n"]}]}