{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SortableTable/filtering.js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/SortableTable/filtering.js", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}