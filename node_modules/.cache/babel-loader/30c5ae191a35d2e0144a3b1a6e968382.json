{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GradientBox.vue?vue&type=template&id=c4203070&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GradientBox.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVuZGVyU2xvdCBhcyBfcmVuZGVyU2xvdCwgbm9ybWFsaXplQ2xhc3MgYXMgX25vcm1hbGl6ZUNsYXNzLCBub3JtYWxpemVTdHlsZSBhcyBfbm9ybWFsaXplU3R5bGUsIG9wZW5CbG9jayBhcyBfb3BlbkJsb2NrLCBjcmVhdGVFbGVtZW50QmxvY2sgYXMgX2NyZWF0ZUVsZW1lbnRCbG9jayB9IGZyb20gInZ1ZSIKCmV4cG9ydCBmdW5jdGlvbiByZW5kZXIoX2N0eCwgX2NhY2hlLCAkcHJvcHMsICRzZXR1cCwgJGRhdGEsICRvcHRpb25zKSB7CiAgcmV0dXJuIChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soImRpdiIsIHsKICAgIGNsYXNzOiBfbm9ybWFsaXplQ2xhc3MoWyJncmFkaWVudC1ib3giLCB7J3Nob3ctdGFiJzogJHByb3BzLnNob3dUYWIsICdwbGFpbic6ICRwcm9wcy5wbGFpbn1dKSwKICAgIHN0eWxlOiBfbm9ybWFsaXplU3R5bGUoJG9wdGlvbnMuc3R5bGUpCiAgfSwgWwogICAgX3JlbmRlclNsb3QoX2N0eC4kc2xvdHMsICJkZWZhdWx0Iiwge30sIHVuZGVmaW5lZCwgdHJ1ZSkKICBdLCA2IC8qIENMQVNTLCBTVFlMRSAqLykpCn0="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/GradientBox.vue"], "names": [], "mappings": ";;;wBA8<PERSON>,oBAMM;IALJ,KAAK,mBAAC,cAAc,eACC,cAAO,WAAW,YAAK;IAC3C,KAAK,kBAAE,cAAK;;IAEb,YAAQ", "sourcesContent": ["<script>\nexport default {\n  props: {\n    // a \"r, g, b\" tuple\n    primaryColorVar: {\n      type:    String,\n      default: null,\n    },\n\n    // Show the left side\n    showTab: {\n      type:    Boolean,\n      default: true,\n    },\n    showSolid: {\n      type:    Boolean,\n      default: false,\n    },\n    backgroundOpacityAdjustment: {\n      type:    Number,\n      default: 1\n    },\n    plain: {\n      type:    Boolean,\n      default: false,\n    }\n  },\n\n  computed: {\n    leftColor() {\n      return this.showSolid ? this.primaryColor : this.customizePrimaryColorOpacity(0.25 * this.backgroundOpacityAdjustment);\n    },\n\n    rightColor() {\n      return this.showSolid ? this.primaryColor : this.customizePrimaryColorOpacity(0.125 * this.backgroundOpacityAdjustment);\n    },\n\n    primaryColor() {\n      return this.customizePrimaryColorOpacity(1);\n    },\n\n    style() {\n      if (!this.plain) {\n        const background = `background: transparent linear-gradient(94deg, ${ this.leftColor } 0%, ${ this.rightColor } 100%) 0% 0% no-repeat padding-box;`;\n        const borderLeft = this.showTab ? `border-left: 9px solid ${ this.primaryColor };` : '';\n\n        return `${ background }${ borderLeft }`;\n      }\n\n      return '';\n    },\n  },\n\n  methods: {\n    customizePrimaryColorOpacity(opacity) {\n      return `rgba(var(${ this.primaryColorVar }), ${ opacity })`;\n    }\n  }\n};\n</script>\n\n<template>\n  <div\n    class=\"gradient-box\"\n    :class=\"{'show-tab': showTab, 'plain': plain}\"\n    :style=\"style\"\n  >\n    <slot />\n  </div>\n</template>\n\n<style lang=\"scss\">\n  .gradient-box {\n      border-radius: 5px;\n  }\n </style>\n\n<style lang=\"scss\" scoped>\n  .gradient-box.plain {\n      border: 1px solid var(--border);\n  }\n</style>\n"]}]}