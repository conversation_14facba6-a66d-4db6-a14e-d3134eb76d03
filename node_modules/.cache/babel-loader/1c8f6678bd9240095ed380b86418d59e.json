{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??clonedRuleSet-44.use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/config/roles.ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/config/roles.ts", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/config/roles.ts", "sourceRoot": "", "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/config/roles.ts"], "names": [], "mappings": "AAAA,MAAM,CAAN,IAAkB,sBAIjB;AAJD,WAAkB,sBAAsB;IACtC,0DAAgC,CAAA;IAChC,4DAAkC,CAAA;IAClC,sEAA4C,CAAA;AAC9C,CAAC,EAJiB,sBAAsB,KAAtB,sBAAsB,QAIvC;AAED;;;;;;;GAOG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG;IAC9B,sEAAsE;IACtE,4DAA4D;IAC5D,sDAAsD;IACtD,2CAA2C;IAE3C,gDAAgD;IAChD,uDAAuD;IACvD,iEAAiE;IACjE,QAAQ;IAER,sDAAsD;IACtD,wEAAwE;IACxE,qCAAqC;IAErC,CAAC,sBAAsB,CAAC,MAAM,CAAC,EAAE;QAC/B,4CAA4C;QAC5C,wCAAwC;QACxC,sCAAsC;QACtC,qDAAqD;QACrD,2DAA2D;QAC3D,mBAAmB,EAAE;YACnB,SAAS,EAAE;gBACT,MAAM;gBACN,cAAc;gBACd,YAAY;aACb;SACF;QACD,kBAAkB,EAAE;YAClB,SAAS,EAAE;gBACT,UAAU;aACX;SACF;QACD,iBAAiB,EAAE;YACjB,SAAS,EAAE;gBACT,SAAS;gBACT,mBAAmB;gBACnB,yBAAyB;gBACzB,UAAU;gBACV,eAAe;gBACf,sBAAsB;gBACtB,2BAA2B;gBAC3B,UAAU;gBACV,UAAU;gBACV,qBAAqB;aACtB;SACF;QACD,kBAAkB,EAAE;YAClB,SAAS,EAAE;gBACT,SAAS;aACV;SACF;QACD,4BAA4B,EAAE;YAC5B,SAAS,EAAE;gBACT,UAAU;aACX;SACF;QACD,sBAAsB,EAAE;YACtB,wCAAwC;YACxC,SAAS,EAAE;gBACT,aAAa;gBACb,aAAa;gBACb,UAAU;gBACV,kBAAkB;gBAClB,yBAAyB;gBACzB,sBAAsB;gBACtB,YAAY;gBACZ,iBAAiB;gBACjB,2BAA2B;gBAC3B,6BAA6B;gBAC7B,UAAU;gBACV,cAAc;gBACd,kBAAkB;gBAClB,0BAA0B;gBAC1B,gBAAgB;gBAChB,gBAAgB;gBAChB,aAAa;gBACb,UAAU;gBACV,iBAAiB;gBACjB,aAAa;gBACb,oBAAoB;gBACpB,QAAQ;gBACR,cAAc;gBACd,kBAAkB;gBAClB,oBAAoB;gBACpB,gBAAgB;gBAChB,OAAO;gBACP,aAAa;gBACb,WAAW;gBACX,eAAe;gBACf,4BAA4B;gBAC5B,0CAA0C;gBAC1C,aAAa;gBACb,UAAU;gBACV,iBAAiB;gBACjB,iBAAiB;gBACjB,sBAAsB;gBACtB,wBAAwB;gBACxB,6BAA6B;gBAC7B,WAAW;gBACX,sBAAsB;gBACtB,eAAe;gBACf,sBAAsB;gBACtB,YAAY;gBACZ,UAAU;gBACV,WAAW;gBACX,kBAAkB;gBAClB,kBAAkB;gBAClB,QAAQ;gBACR,OAAO;gBACP,gBAAgB;aACjB;YACD,mBAAmB,EAAE;gBACnB,eAAe,EAAE,4BAA4B;gBAC7C,oBAAoB,EAAE,4BAA4B;gBAClD,mBAAmB,EAAE,4BAA4B;gBACjD,iBAAiB,EAAE,yBAAyB;gBAC5C,sBAAsB,EAAE,4BAA4B;gBACpD,aAAa,EAAE,wCAAwC;gBACvD,oBAAoB,EAAE,wCAAwC;gBAC9D,kBAAkB,EAAE,oBAAoB;gBACxC,0BAA0B,EAAE,oBAAoB;gBAChD,WAAW,EAAE,4BAA4B;gBACzC,eAAe,EAAE,4BAA4B;gBAC7C,oBAAoB,EAAE,4BAA4B;gBAClD,mBAAmB,EAAE,4BAA4B;aAClD;SACF;QACD,wBAAwB,EAAE;YACxB,SAAS,EAAE;gBACT,UAAU;aACX;SACF;KACF;IACD,CAAC,sBAAsB,CAAC,OAAO,CAAC,EAAE;QAChC,kDAAkD;QAClD,+CAA+C;QAC/C,qBAAqB;QACrB,uDAAuD;QACvD,+CAA+C;QAC/C,iBAAiB,EAAE;YACjB,SAAS,EAAE;gBACT,2CAA2C;gBAC3C,4CAA4C;gBAC5C,WAAW;gBACX,MAAM;gBACN,mBAAmB;gBACnB,gBAAgB;aACjB;YACD,mBAAmB,EAAE;gBACnB,mBAAmB,EAAE,wGAAwG;aAC9H;SACF;QACD,8BAA8B,EAAE;YAC9B,SAAS,EAAE;gBACT,+BAA+B;gBAC/B,iCAAiC;aAClC;SACF;QACD,sBAAsB,EAAE;YACtB,SAAS,EAAE;gBACT,2BAA2B;aAC5B;SACF;QACD,wBAAwB,EAAE;YACxB,SAAS,EAAE;gBACT,aAAa;aACd;SACF;QACD,qBAAqB,EAAE;YACrB,SAAS,EAAE;gBACT,4BAA4B;aAC7B;SACF;QACD,2BAA2B,EAAE;YAC3B,SAAS,EAAE;gBACT,cAAc;gBACd,qBAAqB;gBACrB,OAAO;gBACP,cAAc;aACf;SACF;QACD,sBAAsB,EAAE;YACtB,SAAS,EAAE;gBACT,SAAS;aACV;SACF;QACD,mBAAmB,EAAE;YACnB,SAAS,EAAE;gBACT,iBAAiB;aAClB;SACF;QACD,gBAAgB,EAAE;YAChB,SAAS,EAAE;gBACT,YAAY;gBACZ,UAAU;gBACV,qBAAqB;gBACrB,gBAAgB;gBAChB,mBAAmB;aACpB;SACF;QACD,YAAY,EAAE;YACZ,SAAS,EAAE;gBACT,kBAAkB;gBAClB,gBAAgB;gBAChB,QAAQ;gBACR,SAAS;gBACT,YAAY;gBACZ,eAAe;aAChB;SACF;KACF;IACD,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,EAAE;QAC1C,wDAAwD;QACxD,aAAa;QACb,iBAAiB,EAAE;YACjB,SAAS,EAAE;gBACT,sCAAsC;gBACtC,gCAAgC;gBAChC,YAAY;gBACZ,aAAa,EAAE,qIAAqI;gBACpJ,YAAY;gBACZ,wBAAwB;gBACxB,MAAM;gBACN,cAAc;gBACd,wBAAwB;gBACxB,SAAS;gBACT,UAAU;gBACV,iBAAiB;aAClB;SACF;QACD,IAAI,EAAE;YACJ,SAAS,EAAE;gBACT,qBAAqB;gBACrB,YAAY;gBACZ,aAAa;gBACb,aAAa;gBACb,cAAc;aACf;SACF;QACD,WAAW,EAAE;YACX,SAAS,EAAE;gBACT,0BAA0B;aAC3B;SACF;QACD,KAAK,EAAE;YACL,SAAS,EAAE;gBACT,UAAU;gBACV,MAAM;aACP;SACF;QACD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc;gBACd,oBAAoB;aACrB;SACF;QACD,2BAA2B,EAAE;YAC3B,SAAS,EAAE;gBACT,iBAAiB;gBACjB,mBAAmB;gBACnB,aAAa;aACd;SACF;QACD,yBAAyB,EAAE;YACzB,SAAS,EAAE;gBACT,qBAAqB;aACtB;SACF;QACD,qBAAqB,EAAE;YACrB,SAAS,EAAE;gBACT,QAAQ;aACT;SACF;QACD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,QAAQ;aACT;SACF;QACD,gBAAgB,EAAE;YAChB,SAAS,EAAE;gBACT,mBAAmB;aACpB;SACF;QACD,uBAAuB,EAAE;YACvB,SAAS,EAAE;gBACT,eAAe;gBACf,qBAAqB;gBACrB,aAAa;gBACb,QAAQ;gBACR,cAAc;gBACd,iBAAiB;gBACjB,QAAQ;gBACR,WAAW;gBACX,iBAAiB;gBACjB,cAAc;aACf;SACF;QACD,mBAAmB,EAAE;YACnB,SAAS,EAAE;gBACT,WAAW;gBACX,gBAAgB;gBAChB,iBAAiB;aAClB;SACF;QACD,sBAAsB,EAAE;YACtB,SAAS,EAAE;gBACT,WAAW;aACZ;SACF;QACD,kBAAkB,EAAE;YAClB,SAAS,EAAE;gBACT,gBAAgB;aACjB;SACF;QACD,aAAa,EAAE;YACb,SAAS,EAAE;gBACT,gBAAgB;aACjB;SACF;QACD,MAAM,EAAE;YACN,SAAS,EAAE;gBACT,sBAAsB;gBACtB,qBAAqB;aACtB;SACF;QACD,mBAAmB,EAAE;YACnB,SAAS,EAAE;gBACT,MAAM;gBACN,cAAc;aACf;YACD,mBAAmB,EAAE;gBACnB,WAAW,EAAE,oBAAoB;gBACjC,oBAAoB,EAAE,oBAAoB;gBAC1C,kBAAkB,EAAE,oBAAoB;gBACxC,uBAAuB,EAAE,oBAAoB;gBAC7C,2BAA2B,EAAE,oBAAoB;gBACjD,wBAAwB,EAAE,oBAAoB;aAC/C;SACF;QACD,wBAAwB,EAAE;YACxB,SAAS,EAAE;gBACT,cAAc;gBACd,gBAAgB;gBAChB,OAAO;gBACP,UAAU;gBACV,SAAS;aACV;SACF;QACD,kBAAkB,EAAE;YAClB,SAAS,EAAE;gBACT,gBAAgB;aACjB;SACF;QACD,mBAAmB,EAAE;YACnB,SAAS,EAAE;gBACT,uBAAuB;gBACvB,wBAAwB;gBACxB,qBAAqB;aACtB;SACF;QACD,sBAAsB,EAAE;YACtB,SAAS,EAAE;gBACT,uBAAuB;gBACvB,+BAA+B;aAChC;SACF;QACD,qBAAqB,EAAE;YACrB,SAAS,EAAE;gBACT,kBAAkB;gBAClB,cAAc;gBACd,UAAU;gBACV,gBAAgB;gBAChB,UAAU;gBACV,iBAAiB;gBACjB,iBAAiB;gBACjB,gBAAgB;aACjB;SACF;QACD,YAAY,EAAE;YACZ,SAAS,EAAE;gBACT,aAAa;gBACb,eAAe;gBACf,YAAY;gBACZ,QAAQ;gBACR,WAAW;gBACX,cAAc;gBACd,eAAe;gBACf,aAAa;gBACb,gBAAgB;gBAChB,cAAc;aACf;SACF;KACF;CACF,CAAC", "sourcesContent": ["export const enum SCOPED_RESOURCE_GROUPS {\n  GLOBAL = 'globalScopedApiGroups', // eslint-disable-line no-unused-vars\n  CLUSTER = 'clusterScopedApiGroups', // eslint-disable-line no-unused-vars\n  PROJECT_NAMESPACE = 'projectScopedApiGroups', // eslint-disable-line no-unused-vars\n}\n\n/**\n * Resources users can select when creating grants when managing global, cluster and project/namespace roles\n *\n * **************NOTE*****************\n * Global roles will show ALL entries\n * Cluster roles will show cluster AND project/namespace entries\n * Project/Namespace roles will show ONLY project/namespace entries\n */\nexport const SCOPED_RESOURCES = {\n  // With this hardcoded list, it will be easier to curate a more useful\n  // and human-understandable list of resources to choose from\n  // when creating a role. The list is not meant to be a\n  // comprehensive list, but a helpful guide.\n\n  // Cluster scoped roles and project scoped roles\n  // are intended to restrict users, so the resource list\n  // in the global role creation form includes the largest resource\n  // list.\n\n  // The cluster role creation form includes a subset of\n  // the global scoped list, and the project role creation form includes a\n  // subset of the cluster scoped list.\n\n  [SCOPED_RESOURCE_GROUPS.GLOBAL]: {\n    // Global scoped resources are resources for\n    // Rancher's global apps, mainly Cluster\n    // Management and Continuous Delivery.\n    // A global role can include everything at the global\n    // scope, plus everything in the cluster and project scope.\n    'catalog.cattle.io': {\n      resources: [\n        'Apps',\n        'ClusterRepos',\n        'Operations',\n      ]\n    },\n    'cluster.x-k8s.io': {\n      resources: [\n        'Clusters'\n      ]\n    },\n    'fleet.cattle.io': {\n      resources: [\n        'Bundles',\n        'BundleDeployments',\n        'BundleNamespaceMappings',\n        'Clusters',\n        'ClusterGroups',\n        'ClusterRegistrations',\n        'ClusterRegistrationTokens',\n        'Contents',\n        'GitRepos',\n        'GitRepoRestrictions',\n      ],\n    },\n    'gitjob.cattle.io': {\n      resources: [\n        'GitJobs',\n      ]\n    },\n    'harvesterhci.io.management': {\n      resources: [\n        'Clusters'\n      ]\n    },\n    'management.cattle.io': {\n      // Resources provided by the Norman API.\n      resources: [\n        'APIServices',\n        'AuthConfigs',\n        'Catalogs',\n        'CatalogTemplates',\n        'CatalogTemplateVersions',\n        'CisBenchmarkVersions',\n        'CisConfigs',\n        'ClusterCatalogs',\n        'ClusterRegistrationTokens',\n        'ClusterRoleTemplateBindings',\n        'Clusters',\n        'ClusterScans',\n        'ClusterTemplates',\n        'ClusterTemplateRevisions',\n        'ComposeConfigs',\n        'DynamicSchemas',\n        'EtcdBackups',\n        'Features',\n        'FleetWorkspaces',\n        'GlobalRoles',\n        'GlobalRoleBindings',\n        'Groups',\n        'GroupMembers',\n        'KontainerDrivers',\n        'RkeK8sSystemImages',\n        'MonitorMetrics',\n        'Nodes',\n        'NodeDrivers',\n        'NodePools',\n        'NodeTemplates',\n        'PodSecurityPolicyTemplates',\n        'PodSecurityPolicyTemplateProjectBindings',\n        'Preferences',\n        'Projects',\n        'ProjectCatalogs',\n        'ProjectLoggings',\n        'ProjectMonitorGraphs',\n        'ProjectNetworkPolicies',\n        'ProjectRoleTemplateBindings',\n        'RkeAddons',\n        'RkeK8sServiceOptions',\n        'RoleTemplates',\n        'RoleTemplateBindings',\n        'SamlTokens',\n        'Settings',\n        'Templates',\n        'TemplateContents',\n        'TemplateVersions',\n        'Tokens',\n        'Users',\n        'UserAttributes',\n      ],\n      deprecatedResources: [\n        'ClusterAlerts', // Replaced by monitoring V2\n        'ClusterAlertGroups', // Replaced by monitoring V2\n        'ClusterAlertRules', // Replaced by monitoring V2\n        'ClusterLoggings', // Replaced by logging V2\n        'ClusterMonitorGraphs', // Replaced by monitoring V2\n        'GlobalDnses', // Deprecated along with legacy catalogs\n        'GlobalDnsProviders', // Deprecated along with legacy catalogs\n        'MultiClusterApps', // Replaced by Fleet\n        'MultiClusterAppRevisions', // Replaced by Fleet\n        'Notifiers', // Replaced by monitoring V2\n        'ProjectAlerts', // Replaced by monitoring V2\n        'ProjectAlertGroups', // Replaced by monitoring V2\n        'ProjectAlertRules', // Replaced by monitoring V2\n      ]\n    },\n    'provisioning.cattle.io': {\n      resources: [\n        'Clusters'\n      ]\n    },\n  },\n  [SCOPED_RESOURCE_GROUPS.CLUSTER]: {\n    // Cluster scoped resources are for non-namespaced\n    // resources at the cluster level, for example,\n    // storage resources.\n    // A cluster role can include everything at the cluster\n    // scope, plus everything in the project scope.\n    coreKubernetesApi: {\n      resources: [\n        // Core K8s API - Non-namespaced resources.\n        // These resources do not have an API group.\n        'APIGroups',\n        'Node',\n        'PersistentVolumes',\n        'ResourceQuotas',\n      ],\n      deprecatedResources: [\n        'ComponentStatuses', // A deprecated API that provided status of etcd, kube-scheduler, and kube-controller-manager components\n      ]\n    },\n    'admissionregistration.k8s.io': {\n      resources: [\n        'MutatingWebhookConfigurations',\n        'ValidatingWebhookConfigurations',\n      ],\n    },\n    'apiextensions.k8s.io': {\n      resources: [\n        'CustomResourceDefinitions',\n      ]\n    },\n    'apiregistration.k8s.io': {\n      resources: [\n        'APIServices'\n      ]\n    },\n    'certificates.k8s.io': {\n      resources: [\n        'CertificateSigningRequests'\n      ]\n    },\n    'rbac.authorization.k8s.io': {\n      resources: [\n        'ClusterRoles',\n        'ClusterRoleBindings',\n        'Roles',\n        'RoleBindings',\n      ]\n    },\n    'config.gatekeeper.sh': {\n      resources: [\n        'Configs'\n      ]\n    },\n    'scheduling.k8s.io': {\n      resources: [\n        'PriorityClasses',\n      ],\n    },\n    'storage.k8s.io': {\n      resources: [\n        'CSIDrivers',\n        'CSINodes',\n        'CSIStorageCapacitys',\n        'StorageClasses',\n        'VolumeAttachments',\n      ]\n    },\n    neuvectorApi: {\n      resources: [\n        'AdmissionControl',\n        'Authentication',\n        'CIScan',\n        'Cluster',\n        'Federation',\n        'Vulnerability',\n      ]\n    }\n  },\n  [SCOPED_RESOURCE_GROUPS.PROJECT_NAMESPACE]: {\n    // Project scoped resources include all other namespaced\n    // resources.\n    coreKubernetesApi: {\n      resources: [\n        // Core K8s API - Namespaced resources\n        // that are not in an API group.\n        'ConfigMaps',\n        'LimitRanges', // enumerates compute resource constraints in a project at the pod, container, image, image stream, and persistent volume claim level\n        'Namespaces',\n        'PersistentVolumeClaims',\n        'Pods',\n        'PodTemplates',\n        'ReplicationControllers',\n        'Secrets',\n        'Services',\n        'ServiceAccounts',\n      ],\n    },\n    apps: {\n      resources: [\n        'ControllerRevisions',\n        'DaemonSets',\n        'Deployments',\n        'ReplicaSets',\n        'StatefulSets',\n      ]\n    },\n    autoscaling: {\n      resources: [\n        'HorizontalPodAutoscalers',\n      ]\n    },\n    batch: {\n      resources: [\n        'CronJobs',\n        'Jobs',\n      ]\n    },\n    'cis.cattle.io': {\n      resources: [\n        'ClusterScans',\n        'ClusterScanReports'\n      ]\n    },\n    'constraints.gatekeeper.sh': {\n      resources: [\n        'K8sAllowedRepos',\n        'K8sRequiredLabels',\n        'Constraints',\n      ]\n    },\n    'templates.gatekeeper.sh': {\n      resources: [\n        'ConstraintTemplates'\n      ]\n    },\n    'coordination.k8s.io': {\n      resources: [\n        'Leases',\n      ]\n    },\n    'events.k8s.io': {\n      resources: [\n        'Events',\n      ]\n    },\n    'helm.cattle.io': {\n      resources: [\n        'ProjectHelmCharts',\n      ]\n    },\n    'monitoring.coreos.com': {\n      resources: [\n        'Alertmanagers',\n        'AlertmanagerConfigs',\n        'PodMonitors',\n        'Probes',\n        'Prometheuses',\n        'PrometheusRules',\n        'Routes',\n        'Receivers',\n        'ServiceMonitors',\n        'ThanosRulers'\n      ]\n    },\n    'networking.k8s.io': {\n      resources: [\n        'Ingresses',\n        'IngressClasses',\n        'NetworkPolicies',\n      ]\n    },\n    'io.k8s.api.discovery': {\n      resources: [\n        'Endpoints'\n      ]\n    },\n    'discovery.k8s.io': {\n      resources: [\n        'EndpointSlices',\n      ]\n    },\n    'node.k8s.io': {\n      resources: [\n        'RuntimeClasses',\n      ]\n    },\n    policy: {\n      resources: [\n        'PodDisruptionBudgets',\n        'PodSecurityPolicies',\n      ]\n    },\n    'project.cattle.io': {\n      resources: [\n        'Apps',\n        'AppRevisions'\n      ],\n      deprecatedResources: [\n        'Pipelines', // Replaced by Fleet\n        'PipelineExecutions', // Replaced by Fleet\n        'PipelineSettings', // Replaced by Fleet\n        'SourceCodeCredentials', // Replaced by Fleet\n        'SourceCodeProviderConfigs', // Replaced by Fleet\n        'SourceCodeRepositories', // Replaced by Fleet\n      ]\n    },\n    'logging.banzaicloud.io': {\n      resources: [\n        'ClusterFlows',\n        'ClusterOutputs',\n        'Flows',\n        'Loggings',\n        'Outputs',\n      ]\n    },\n    'install.istio.io': {\n      resources: [\n        'IstioOperators',\n      ]\n    },\n    'security.istio.io': {\n      resources: [\n        'AuthorizationPolicies',\n        'RequestAuthentications',\n        'PeerAuthentications',\n      ]\n    },\n    'status.gatekeeper.sh': {\n      resources: [\n        'ConstraintPodStatuses',\n        'ConstraintTemplatePodStatuses',\n      ]\n    },\n    'networking.istio.io': {\n      resources: [\n        'DestinationRules',\n        'EnvoyFilters',\n        'Gateways',\n        'ServiceEntries',\n        'Sidecars',\n        'VirtualServices',\n        'WorkloadEntries',\n        'WorkloadGroups'\n      ]\n    },\n    neuvectorApi: {\n      resources: [\n        'AuditEvents',\n        'Authorization',\n        'Compliance',\n        'Events',\n        'Namespace',\n        'RegistryScan',\n        'RuntimePolicy',\n        'RuntimeScan',\n        'SecurityEvents',\n        'SystemConfig',\n      ]\n    }\n  }\n};\n"]}]}