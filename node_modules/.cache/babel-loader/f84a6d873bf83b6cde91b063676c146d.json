{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AllowedPrincipals.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AllowedPrincipals.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCB7IFJhZGlvR3JvdXAgfSBmcm9tICdAY29tcG9uZW50cy9Gb3JtL1JhZGlvJzsKaW1wb3J0IEFycmF5TGlzdCBmcm9tICdAc2hlbGwvY29tcG9uZW50cy9mb3JtL0FycmF5TGlzdCc7CmltcG9ydCBQcmluY2lwYWwgZnJvbSAnQHNoZWxsL2NvbXBvbmVudHMvYXV0aC9QcmluY2lwYWwnOwppbXBvcnQgU2VsZWN0UHJpbmNpcGFsIGZyb20gJ0BzaGVsbC9jb21wb25lbnRzL2F1dGgvU2VsZWN0UHJpbmNpcGFsJzsKaW1wb3J0IHsgX0VESVQgfSBmcm9tICdAc2hlbGwvY29uZmlnL3F1ZXJ5LXBhcmFtcyc7CmltcG9ydCB1bmlxIGZyb20gJ2xvZGFzaC91bmlxJzsKCmV4cG9ydCBkZWZhdWx0IHsKICBjb21wb25lbnRzOiB7CiAgICBTZWxlY3RQcmluY2lwYWwsCiAgICBBcnJheUxpc3QsCiAgICBSYWRpb0dyb3VwLAogICAgUHJpbmNpcGFsLAogIH0sCgogIHByb3BzOiB7CiAgICBwcm92aWRlcjogewogICAgICB0eXBlOiAgICAgU3RyaW5nLAogICAgICByZXF1aXJlZDogdHJ1ZSwKICAgIH0sCgogICAgYXV0aENvbmZpZzogewogICAgICB0eXBlOiAgICAgT2JqZWN0LAogICAgICByZXF1aXJlZDogdHJ1ZSwKICAgIH0sCgogICAgbW9kZTogewogICAgICB0eXBlOiAgICBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6IF9FRElULAogICAgfSwKICB9LAoKICBjb21wdXRlZDogewogICAgYWNjZXNzTW9kZU9wdGlvbnMoKSB7CiAgICAgIHJldHVybiBbJ3VucmVzdHJpY3RlZCcsICdyZXN0cmljdGVkJywgJ3JlcXVpcmVkJ10ubWFwKChrKSA9PiB7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIGxhYmVsOiB0aGlzLnQoYGF1dGhDb25maWcuYWNjZXNzTW9kZS4keyBrIH1gLCB7IHByb3ZpZGVyOiB0aGlzLmF1dGhDb25maWcubmFtZURpc3BsYXkgfSksCiAgICAgICAgICB2YWx1ZTogaywKICAgICAgICB9OwogICAgICB9KTsKICAgIH0sCgogICAgYWNjZXNzTW9kZSgpIHsKICAgICAgcmV0dXJuIHRoaXMuYXV0aENvbmZpZz8uYWNjZXNzTW9kZTsKICAgIH0KICB9LAoKICBjcmVhdGVkKCkgewogICAgaWYgKCAhdGhpcy5hdXRoQ29uZmlnLmFjY2Vzc01vZGUgKSB7CiAgICAgIHRoaXMuYXV0aENvbmZpZ1snYWNjZXNzTW9kZSddID0gJ3Jlc3RyaWN0ZWQnOwogICAgfSBpZiAoIXRoaXMuYXV0aENvbmZpZy5hbGxvd2VkUHJpbmNpcGFsSWRzKSB7CiAgICAgIHRoaXMuYXV0aENvbmZpZ1snYWxsb3dlZFByaW5jaXBhbElkcyddID0gW107CiAgICB9CiAgfSwKCiAgbWV0aG9kczogewogICAgYWRkUHJpbmNpcGFsKGlkKSB7CiAgICAgIHRoaXMuYXV0aENvbmZpZy5hbGxvd2VkUHJpbmNpcGFsSWRzID0gdW5pcShbLi4udGhpcy5hdXRoQ29uZmlnLmFsbG93ZWRQcmluY2lwYWxJZHMsIGlkXSk7CiAgICB9LAogIH0KfTsK"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AllowedPrincipals.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE9B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACX,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACxF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACV,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9C,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAC7C;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1F,CAAC;EACH;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/auth/AllowedPrincipals.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { RadioGroup } from '@components/Form/Radio';\nimport ArrayList from '@shell/components/form/ArrayList';\nimport Principal from '@shell/components/auth/Principal';\nimport SelectPrincipal from '@shell/components/auth/SelectPrincipal';\nimport { _EDIT } from '@shell/config/query-params';\nimport uniq from 'lodash/uniq';\n\nexport default {\n  components: {\n    SelectPrincipal,\n    ArrayList,\n    RadioGroup,\n    Principal,\n  },\n\n  props: {\n    provider: {\n      type:     String,\n      required: true,\n    },\n\n    authConfig: {\n      type:     Object,\n      required: true,\n    },\n\n    mode: {\n      type:    String,\n      default: _EDIT,\n    },\n  },\n\n  computed: {\n    accessModeOptions() {\n      return ['unrestricted', 'restricted', 'required'].map((k) => {\n        return {\n          label: this.t(`authConfig.accessMode.${ k }`, { provider: this.authConfig.nameDisplay }),\n          value: k,\n        };\n      });\n    },\n\n    accessMode() {\n      return this.authConfig?.accessMode;\n    }\n  },\n\n  created() {\n    if ( !this.authConfig.accessMode ) {\n      this.authConfig['accessMode'] = 'restricted';\n    } if (!this.authConfig.allowedPrincipalIds) {\n      this.authConfig['allowedPrincipalIds'] = [];\n    }\n  },\n\n  methods: {\n    addPrincipal(id) {\n      this.authConfig.allowedPrincipalIds = uniq([...this.authConfig.allowedPrincipalIds, id]);\n    },\n  }\n};\n</script>\n\n<template>\n  <div>\n    <h3>{{ t('authConfig.accessMode.label', {provider: authConfig.nameDisplay}) }}</h3>\n\n    <div class=\"row\">\n      <div class=\"col span-6\">\n        <RadioGroup\n          v-model:value=\"authConfig.accessMode\"\n          name=\"accessMode\"\n          :mode=\"mode\"\n          :options=\"accessModeOptions\"\n        />\n      </div>\n      <div class=\"col span-6\">\n        <h4 v-if=\"accessMode!=='unrestricted'\">\n          <t\n            k=\"authConfig.allowedPrincipalIds.title\"\n            :raw=\"true\"\n          />\n        </h4>\n        <ArrayList\n          v-if=\"accessMode!=='unrestricted'\"\n          key=\"allowedPrincipalIds\"\n          v-model:value=\"authConfig.allowedPrincipalIds\"\n          title-key=\"authConfig.allowedPrincipalIds.label\"\n          :mode=\"mode\"\n          :protip=\"false\"\n        >\n          <template #value=\"{row}\">\n            <Principal\n              :value=\"row.value\"\n            />\n          </template>\n\n          <template\n            v-if=\"authConfig.allowedPrincipalIds.length <= 1\"\n            #remove-button\n          >\n            <button\n              type=\"button\"\n              disabled\n              class=\"btn role-link bg-transparent\"\n            >\n              {{ t('generic.remove') }}\n            </button>\n          </template>\n\n          <template #add>\n            <SelectPrincipal\n              :mode=\"mode\"\n              @add=\"addPrincipal\"\n            />\n          </template>\n        </ArrayList>\n      </div>\n    </div>\n  </div>\n</template>\n"]}]}