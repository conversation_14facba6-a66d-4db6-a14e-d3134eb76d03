{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetIntro.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetIntro.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCB7IEZMRUVUIH0gZnJvbSAnQHNoZWxsL2NvbmZpZy90eXBlcyc7CmltcG9ydCB7IE5BTUUgfSBmcm9tICdAc2hlbGwvY29uZmlnL3Byb2R1Y3QvZmxlZXQnOwoKZXhwb3J0IGRlZmF1bHQgewoKICBuYW1lOiAnRmxlZXRJbnRybycsCgogIGRhdGEoKSB7CiAgICBjb25zdCBnaXRSZXBvUm91dGUgPSB7CiAgICAgIG5hbWU6ICAgJ2MtY2x1c3Rlci1wcm9kdWN0LXJlc291cmNlLWNyZWF0ZScsCiAgICAgIHBhcmFtczogewogICAgICAgIHByb2R1Y3Q6ICBOQU1FLAogICAgICAgIHJlc291cmNlOiBGTEVFVC5HSVRfUkVQTwogICAgICB9LAogICAgfTsKCiAgICBjb25zdCBnaXRSZXBvU2NoZW1hID0gdGhpcy4kc3RvcmUuZ2V0dGVyc1snbWFuYWdlbWVudC9zY2hlbWFGb3InXShGTEVFVC5HSVRfUkVQTyk7CiAgICBjb25zdCBjYW5DcmVhdGVSZXBvcyA9IGdpdFJlcG9TY2hlbWEgJiYgZ2l0UmVwb1NjaGVtYS5yZXNvdXJjZU1ldGhvZHMuaW5jbHVkZXMoJ1BVVCcpOwoKICAgIHJldHVybiB7CiAgICAgIGdpdFJlcG9Sb3V0ZSwKICAgICAgY2FuQ3JlYXRlUmVwb3MKICAgIH07CiAgfSwKfTsK"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetIntro.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAElD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;EAEb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAElB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC;IACH,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAErF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;EACH,CAAC;AACH,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/fleet/FleetIntro.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport { FLEET } from '@shell/config/types';\nimport { NAME } from '@shell/config/product/fleet';\n\nexport default {\n\n  name: 'FleetIntro',\n\n  data() {\n    const gitRepoRoute = {\n      name:   'c-cluster-product-resource-create',\n      params: {\n        product:  NAME,\n        resource: FLEET.GIT_REPO\n      },\n    };\n\n    const gitRepoSchema = this.$store.getters['management/schemaFor'](FLEET.GIT_REPO);\n    const canCreateRepos = gitRepoSchema && gitRepoSchema.resourceMethods.includes('PUT');\n\n    return {\n      gitRepoRoute,\n      canCreateRepos\n    };\n  },\n};\n</script>\n<template>\n  <div class=\"intro-box\">\n    <i class=\"icon icon-repository\" />\n    <div class=\"title\">\n      {{ t('fleet.gitRepo.repo.noRepos') }}\n    </div>\n    <div\n      v-if=\"canCreateRepos\"\n      class=\"actions\"\n    >\n      <router-link\n        :to=\"gitRepoRoute\"\n        class=\"btn role-secondary\"\n      >\n        {{ t('fleet.gitRepo.repo.addRepo') }}\n      </router-link>\n    </div>\n  </div>\n</template>\n<style lang=\"scss\" scoped>\n.intro-box {\n  flex: 0 0 100%;\n  height: calc(100vh - 246px); // 2(48 content header + 20 padding + 55 pageheader)\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-direction: column;\n}\n\n.title {\n  margin-bottom: 15px;\n  font-size: $font-size-h2;\n}\n.icon-repository {\n  font-size: 96px;\n  margin-bottom: 32px;\n}\n</style>\n"]}]}