{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??clonedRuleSet-44.use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ButtonMultiAction.vue?vue&type=script&setup=true&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ButtonMultiAction.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgZGVmaW5lQ29tcG9uZW50IGFzIF9kZWZpbmVDb21wb25lbnQgfSBmcm9tICd2dWUnOwppbXBvcnQgeyBjb21wdXRlZCB9IGZyb20gJ3Z1ZSc7CmV4cG9ydCBkZWZhdWx0IC8qQF9fUFVSRV9fKi8gX2RlZmluZUNvbXBvbmVudCh7CiAgICBfX25hbWU6ICdCdXR0b25NdWx0aUFjdGlvbicsCiAgICBwcm9wczogewogICAgICAgIGJvcmRlcmxlc3M6IHsgdHlwZTogQm9vbGVhbiwgcmVxdWlyZWQ6IGZhbHNlIH0sCiAgICAgICAgaW52aXNpYmxlOiB7IHR5cGU6IEJvb2xlYW4sIHJlcXVpcmVkOiBmYWxzZSB9CiAgICB9LAogICAgZW1pdHM6IFsnY2xpY2snXSwKICAgIHNldHVwKF9fcHJvcHMsIHsgZXhwb3NlOiBfX2V4cG9zZSB9KSB7CiAgICAgICAgX19leHBvc2UoKTsKICAgICAgICBjb25zdCBwcm9wcyA9IF9fcHJvcHM7CiAgICAgICAgY29uc3QgYnV0dG9uQ2xhc3MgPSBjb21wdXRlZCgoKSA9PiB7CiAgICAgICAgICAgIHJldHVybiB7CiAgICAgICAgICAgICAgICBib3JkZXJsZXNzOiBwcm9wcyA9PT0gbnVsbCB8fCBwcm9wcyA9PT0gdm9pZCAwID8gdm9pZCAwIDogcHJvcHMuYm9yZGVybGVzcywKICAgICAgICAgICAgICAgIGludmlzaWJsZTogcHJvcHMgPT09IG51bGwgfHwgcHJvcHMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHByb3BzLmludmlzaWJsZSwKICAgICAgICAgICAgfTsKICAgICAgICB9KTsKICAgICAgICBjb25zdCBfX3JldHVybmVkX18gPSB7IHByb3BzLCBidXR0b25DbGFzcyB9OwogICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShfX3JldHVybmVkX18sICdfX2lzU2NyaXB0U2V0dXAnLCB7IGVudW1lcmFibGU6IGZhbHNlLCB2YWx1ZTogdHJ1ZSB9KTsKICAgICAgICByZXR1cm4gX19yZXR1cm5lZF9fOwogICAgfQp9KTsK"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ButtonMultiAction.vue?vue&type=script&setup=true&lang=ts", "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ButtonMultiAction.vue"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,IAAI,gBAAgB,EAAE,MAAM,KAAK,CAAA;ACCzD,OAAO,EAAE,QAAQ,EAAE,MAAM,KAAK,CAAA;ADQ9B,eAAe,aAAa,CAAA,gBAAgB,CAAC;IAC3C,MAAM,EAAE,mBAAmB;IAC3B,KAAK,EAAE;QACL,UAAU,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;QAC9C,SAAS,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;KAC9C;IACD,KAAK,EAAE,CAAC,OAAO,CAAC;IAChB,KAAK,CAAC,OAAY,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE;QACxC,QAAQ,EAAE,CAAC;QCPb,MAAM,KAAK,GAAG,OAAoB,CAAA;QAElC,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,EAAE;YAChC,OAAO;gBACL,UAAU,EAAE,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,UAAU;gBAC7B,SAAS,EAAG,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,SAAS;aAC7B,CAAA;QACH,CAAC,CAAC,CAAA;QDaF,MAAM,YAAY,GAAG,EAAE,KAAK,EAAE,WAAW,EAAE,CAAA;QAC3C,MAAM,CAAC,cAAc,CAAC,YAAY,EAAE,iBAAiB,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAA;QAC1F,OAAO,YAAY,CAAA;IACnB,CAAC;CAEA,CAAC,CAAA", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ButtonMultiAction.vue.tsx", "sourceRoot": "", "sourcesContent": ["import { defineComponent as _defineComponent } from 'vue'\nimport { computed } from 'vue';\n\ntype Props = {\n  borderless?: boolean;\n  invisible?: boolean;\n}\n\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'ButtonMultiAction',\n  props: {\n    borderless: { type: Boolean, required: false },\n    invisible: { type: Boolean, required: false }\n  },\n  emits: ['click'],\n  setup(__props: any, { expose: __expose }) {\n  __expose();\n\n\n\nconst props = __props;\n\nconst buttonClass = computed(() => {\n  return {\n    borderless: props?.borderless,\n    invisible:  props?.invisible,\n  };\n});\n\nconst __returned__ = { props, buttonClass }\nObject.defineProperty(__returned__, '__isScriptSetup', { enumerable: false, value: true })\nreturn __returned__\n}\n\n})", "<script setup lang=\"ts\">\nimport { computed } from 'vue';\n\ndefineEmits(['click']);\n\ntype Props = {\n  borderless?: boolean;\n  invisible?: boolean;\n}\n\nconst props = defineProps<Props>();\n\nconst buttonClass = computed(() => {\n  return {\n    borderless: props?.borderless,\n    invisible:  props?.invisible,\n  };\n});\n</script>\n\n<template>\n  <button\n    type=\"button\"\n    class=\"btn btn-sm role-multi-action actions\"\n    role=\"button\"\n    :class=\"buttonClass\"\n    @click=\"(e: Event) => $emit('click', e)\"\n  >\n    <i\n      class=\"icon icon-actions\"\n      :alt=\"t('sortableTable.tableActionsImgAlt')\"\n    />\n  </button>\n</template>\n\n<style lang=\"scss\" scoped>\n.borderless {\n  background-color: transparent;\n  border: none;\n\n  &:focus-visible {\n    @include focus-outline;\n    outline-offset: -2px;\n  }\n\n  &:hover, &:focus {\n    background-color: var(--accent-btn);\n    box-shadow: none;\n  }\n}\n</style>\n"]}]}