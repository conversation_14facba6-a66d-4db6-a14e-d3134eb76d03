{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceList/ResourceLoadingIndicator.vue?vue&type=template&id=351c01f1&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceList/ResourceLoadingIndicator.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgY3JlYXRlRWxlbWVudFZOb2RlIGFzIF9jcmVhdGVFbGVtZW50Vk5vZGUsIHRvRGlzcGxheVN0cmluZyBhcyBfdG9EaXNwbGF5U3RyaW5nLCBvcGVuQmxvY2sgYXMgX29wZW5CbG9jaywgY3JlYXRlRWxlbWVudEJsb2NrIGFzIF9jcmVhdGVFbGVtZW50QmxvY2ssIGNyZWF0ZUNvbW1lbnRWTm9kZSBhcyBfY3JlYXRlQ29tbWVudFZOb2RlLCBjcmVhdGVUZXh0Vk5vZGUgYXMgX2NyZWF0ZVRleHRWTm9kZSwgbm9ybWFsaXplU3R5bGUgYXMgX25vcm1hbGl6ZVN0eWxlIH0gZnJvbSAidnVlIgoKY29uc3QgX2hvaXN0ZWRfMSA9IHsKICBrZXk6IDAsCiAgY2xhc3M6ICJtbC0xMCByZXNvdXJjZS1sb2FkaW5nLWluZGljYXRvciIKfQpjb25zdCBfaG9pc3RlZF8yID0geyBjbGFzczogImlubmVyIiB9CmNvbnN0IF9ob2lzdGVkXzMgPSB7IGNsYXNzOiAicmVzb3VyY2UtbG9hZGVyIiB9CmNvbnN0IF9ob2lzdGVkXzQgPSB7IGNsYXNzOiAicmwtYmciIH0KY29uc3QgX2hvaXN0ZWRfNSA9IHsga2V5OiAwIH0KY29uc3QgX2hvaXN0ZWRfNiA9IHsgY2xhc3M6ICJybC1mZyIgfQpjb25zdCBfaG9pc3RlZF83ID0geyBrZXk6IDAgfQoKZXhwb3J0IGZ1bmN0aW9uIHJlbmRlcihfY3R4LCBfY2FjaGUsICRwcm9wcywgJHNldHVwLCAkZGF0YSwgJG9wdGlvbnMpIHsKICByZXR1cm4gKCRvcHRpb25zLmNvdW50ICYmICEkb3B0aW9ucy5oYXZlQWxsKQogICAgPyAoX29wZW5CbG9jaygpLCBfY3JlYXRlRWxlbWVudEJsb2NrKCJkaXYiLCBfaG9pc3RlZF8xLCBbCiAgICAgICAgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfMiwgWwogICAgICAgICAgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfMywgWwogICAgICAgICAgICBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCBfaG9pc3RlZF80LCBbCiAgICAgICAgICAgICAgX2NhY2hlWzBdIHx8IChfY2FjaGVbMF0gPSBfY3JlYXRlRWxlbWVudFZOb2RlKCJpIiwgeyBjbGFzczogImljb24gaWNvbi1zcGlubmVyIGljb24tc3BpbiIgfSwgbnVsbCwgLTEgLyogQ0FDSEVEICovKSksCiAgICAgICAgICAgICAgX2NyZWF0ZUVsZW1lbnRWTm9kZSgic3BhbiIsIG51bGwsIFsKICAgICAgICAgICAgICAgIF9jcmVhdGVUZXh0Vk5vZGUoX3RvRGlzcGxheVN0cmluZyhfY3R4LnQoICdyZXNvdXJjZUxvYWRpbmdJbmRpY2F0b3IubG9hZGluZycgKSkgKyAiICIsIDEgLyogVEVYVCAqLyksCiAgICAgICAgICAgICAgICAoISRwcm9wcy5pbmRldGVybWluYXRlKQogICAgICAgICAgICAgICAgICA/IChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soInNwYW4iLCBfaG9pc3RlZF81LCBfdG9EaXNwbGF5U3RyaW5nKCRvcHRpb25zLmNvdW50KSArICIgLyAiICsgX3RvRGlzcGxheVN0cmluZygkb3B0aW9ucy50b3RhbCksIDEgLyogVEVYVCAqLykpCiAgICAgICAgICAgICAgICAgIDogX2NyZWF0ZUNvbW1lbnRWTm9kZSgidi1pZiIsIHRydWUpCiAgICAgICAgICAgICAgXSkKICAgICAgICAgICAgXSkKICAgICAgICAgIF0pLAogICAgICAgICAgX2NhY2hlWzJdIHx8IChfY2FjaGVbMl0gPSBfY3JlYXRlVGV4dFZOb2RlKCkpLAogICAgICAgICAgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgewogICAgICAgICAgICBjbGFzczogInJlc291cmNlLWxvYWRlciIsCiAgICAgICAgICAgIHN0eWxlOiBfbm9ybWFsaXplU3R5bGUoe3dpZHRoOiAkb3B0aW9ucy53aWR0aH0pCiAgICAgICAgICB9LCBbCiAgICAgICAgICAgIF9jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIF9ob2lzdGVkXzYsIFsKICAgICAgICAgICAgICBfY2FjaGVbMV0gfHwgKF9jYWNoZVsxXSA9IF9jcmVhdGVFbGVtZW50Vk5vZGUoImkiLCB7IGNsYXNzOiAiaWNvbiBpY29uLXNwaW5uZXIgaWNvbi1zcGluIiB9LCBudWxsLCAtMSAvKiBDQUNIRUQgKi8pKSwKICAgICAgICAgICAgICBfY3JlYXRlRWxlbWVudFZOb2RlKCJzcGFuIiwgbnVsbCwgWwogICAgICAgICAgICAgICAgX2NyZWF0ZVRleHRWTm9kZShfdG9EaXNwbGF5U3RyaW5nKF9jdHgudCggJ3Jlc291cmNlTG9hZGluZ0luZGljYXRvci5sb2FkaW5nJyApKSArICIgIiwgMSAvKiBURVhUICovKSwKICAgICAgICAgICAgICAgICghJHByb3BzLmluZGV0ZXJtaW5hdGUpCiAgICAgICAgICAgICAgICAgID8gKF9vcGVuQmxvY2soKSwgX2NyZWF0ZUVsZW1lbnRCbG9jaygic3BhbiIsIF9ob2lzdGVkXzcsIF90b0Rpc3BsYXlTdHJpbmcoJG9wdGlvbnMuY291bnQpICsgIiAvICIgKyBfdG9EaXNwbGF5U3RyaW5nKCRvcHRpb25zLnRvdGFsKSwgMSAvKiBURVhUICovKSkKICAgICAgICAgICAgICAgICAgOiBfY3JlYXRlQ29tbWVudFZOb2RlKCJ2LWlmIiwgdHJ1ZSkKICAgICAgICAgICAgICBdKQogICAgICAgICAgICBdKQogICAgICAgICAgXSwgNCAvKiBTVFlMRSAqLykKICAgICAgICBdKQogICAgICBdKSkKICAgIDogX2NyZWF0ZUNvbW1lbnRWTm9kZSgidi1pZiIsIHRydWUpCn0="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/ResourceList/ResourceLoadingIndicator.vue"], "names": ["t"], "mappings": ";;;;EA+EI,KAAK,EAAC,kCAAkC;;qBAEnC,KAAK,EAAC,OAAO;qBACX,KAAK,EAAC,iBAAiB;qBACrB,KAAK,EAAC,OAAO;;qBAQb,KAAK,EAAC,OAAO;;;;UAbhB,cAAK,KAAK,gBAAO;qBADzB,oBAmBM,OAnBN,UAmBM;QAfJ,oBAcM,OAdN,UAcM;UAbJ,oBAIM,OAJN,UAIM;YAHJ,oBAEM,OAFN,UAEM;wCADJ,oBAAyC,OAAtC,KAAK,EAAC,6BAA6B;cAAG,oBAAuH;kDAA9GA,MAAC,0CAAyC,GAAC;kBAAa,oBAAa;mCAA1B,oBAA4D,qCAA7B,cAAK,IAAG,KAAG,oBAAG,cAAK;;;;;;UAGnJ,oBAOM;YANJ,KAAK,EAAC,iBAAiB;YACtB,KAAK,0BAAG,cAAK;;YAEd,oBAEM,OAFN,UAEM;wCADJ,oBAAyC,OAAtC,KAAK,EAAC,6BAA6B;cAAG,oBAAuH;kDAA9GA,MAAC,0CAAyC,GAAC;kBAAa,oBAAa;mCAA1B,oBAA4D,qCAA7B,cAAK,IAAG,KAAG,oBAAG,cAAK", "sourcesContent": ["<script>\nimport { COUNT } from '@shell/config/types';\n\n/**\n * Loading Indicator for resources - used when we are loading resources incrementally, by page\n */\nexport default {\n\n  name: 'ResourceLoadingIndicator',\n\n  props: {\n    resources: {\n      type:     Array,\n      required: true,\n    },\n    indeterminate: {\n      type:    Boolean,\n      default: false,\n    },\n  },\n\n  data() {\n    const inStore = this.$store.getters['currentStore'](this.resource);\n\n    return { inStore };\n  },\n\n  computed: {\n    // Count of rows - either from the data provided or from the rows for the first resource\n    rowsCount() {\n      if (this.resources.length > 0) {\n        const existingData = this.$store.getters[`${ this.inStore }/all`](this.resources[0]) || [];\n\n        return (existingData || []).length;\n      }\n\n      return 0;\n    },\n\n    // Have we loaded all resources for the types that are needed\n    haveAll() {\n      return this.resources.reduce((acc, r) => {\n        return acc && this.$store.getters[`${ this.inStore }/haveAll`](r);\n      }, true);\n    },\n\n    // Total of all counts of all resources for all of the resources being loaded\n    total() {\n      const clusterCounts = this.$store.getters[`${ this.inStore }/all`](COUNT);\n\n      return this.resources.reduce((acc, r) => {\n        const resourceCounts = clusterCounts?.[0]?.counts?.[r];\n        const resourceCount = resourceCounts?.summary?.count;\n        const count = resourceCount || 0;\n\n        return acc + count;\n      }, 0);\n    },\n\n    // Total count of all of the resources for all of the resources being loaded\n    count() {\n      return this.resources.reduce((acc, r) => {\n        return acc + (this.$store.getters[`${ this.inStore }/all`](r) || []).length;\n      }, 0);\n    },\n\n    // Width style to enable the progress bar style presentation\n    width() {\n      const progress = Math.ceil(100 * (this.count / this.total));\n\n      return `${ progress }%`;\n    }\n  },\n};\n</script>\n\n<template>\n  <div\n    v-if=\"count && !haveAll\"\n    class=\"ml-10 resource-loading-indicator\"\n  >\n    <div class=\"inner\">\n      <div class=\"resource-loader\">\n        <div class=\"rl-bg\">\n          <i class=\"icon icon-spinner icon-spin\" /><span>{{ t( 'resourceLoadingIndicator.loading' ) }} <span v-if=\"!indeterminate\">{{ count }} / {{ total }}</span></span>\n        </div>\n      </div>\n      <div\n        class=\"resource-loader\"\n        :style=\"{width}\"\n      >\n        <div class=\"rl-fg\">\n          <i class=\"icon icon-spinner icon-spin\" /><span>{{ t( 'resourceLoadingIndicator.loading' ) }} <span v-if=\"!indeterminate\">{{ count }} / {{ total }}</span></span>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .resource-loading-indicator {\n    border: 1px solid var(--link);\n    border-radius: 10px;\n    position: relative;\n    width: min-content;\n    overflow: hidden;\n\n    .resource-loader:last-child {\n      position: absolute;\n      top: 0;\n\n      background-color: var(--link);\n      color: var(--link-text);\n      overflow: hidden;\n      white-space: nowrap;\n    }\n\n    .resource-loader {\n      padding: 1px 10px;\n      width: max-content;\n\n      .rl-fg, .rl-bg {\n        align-content: center;\n        display: flex;\n\n        > i {\n          font-size: 18px;\n          line-height: 18px;\n        }\n\n        > span {\n          margin-left: 5px;\n        }\n      }\n    }\n  }\n</style>\n"]}]}