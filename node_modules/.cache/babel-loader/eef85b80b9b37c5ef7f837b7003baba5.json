{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Dialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Dialog.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCBBc3luY0J1dHRvbiBmcm9tICdAc2hlbGwvY29tcG9uZW50cy9Bc3luY0J1dHRvbic7CmltcG9ydCBBcHBNb2RhbCwgeyBERUZBVUxUX0lURVJBQkxFX05PREVfU0VMRUNUT1IgfSBmcm9tICdAc2hlbGwvY29tcG9uZW50cy9BcHBNb2RhbC52dWUnOwoKZXhwb3J0IGRlZmF1bHQgewogIGVtaXRzOiBbJ29rYXknLCAnY2xvc2VkJ10sCgogIGNvbXBvbmVudHM6IHsgQXN5bmNCdXR0b24sIEFwcE1vZGFsIH0sCgogIHByb3BzOiB7CiAgICBuYW1lOiB7CiAgICAgIHR5cGU6ICAgICBTdHJpbmcsCiAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgfSwKCiAgICB0aXRsZTogewogICAgICB0eXBlOiAgICAgU3RyaW5nLAogICAgICByZXF1aXJlZDogdHJ1ZSwKICAgIH0sCgogICAgbW9kZTogewogICAgICB0eXBlOiAgICBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICcnLAogICAgfSwKCiAgICAvKioKICAgICAqIGZvcmNlZnVsbHkgc2V0IHJldHVybiBmb2N1cyBlbGVtZW50IGJhc2VkIG9uIHRoaXMgc2VsZWN0b3IKICAgICAqLwogICAgcmV0dXJuRm9jdXNTZWxlY3RvcjogewogICAgICB0eXBlOiAgICBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICcnLAogICAgfSwKCiAgICAvKioKICAgICAqIHdpbGwgcmV0dXJuIGZvY3VzIHRvIHRoZSBmaXJzdCBpdGVyYWJsZSBub2RlIG9mIHRoaXMgY29udGFpbmVyIHNlbGVjdAogICAgICovCiAgICByZXR1cm5Gb2N1c0ZpcnN0SXRlcmFibGVOb2RlU2VsZWN0b3I6IHsKICAgICAgdHlwZTogICAgU3RyaW5nLAogICAgICBkZWZhdWx0OiBERUZBVUxUX0lURVJBQkxFX05PREVfU0VMRUNUT1IsCiAgICB9CiAgfSwKCiAgZGF0YSgpIHsKICAgIHJldHVybiB7IGNsb3NlZDogZmFsc2UgfTsKICB9LAoKICBtZXRob2RzOiB7CiAgICBiZWZvcmVPcGVuKCkgewogICAgICB0aGlzLmNsb3NlZCA9IGZhbHNlOwogICAgfSwKCiAgICBvayhidG5DYikgewogICAgICBjb25zdCBjYWxsYmFjayA9IChvaykgPT4gewogICAgICAgIGJ0bkNiKG9rKTsKICAgICAgICBpZiAob2spIHsKICAgICAgICAgIHRoaXMuY2xvc2VEaWFsb2codHJ1ZSk7CiAgICAgICAgfQogICAgICB9OwoKICAgICAgdGhpcy4kZW1pdCgnb2theScsIGNhbGxiYWNrKTsKICAgIH0sCgogICAgY2xvc2VEaWFsb2cocmVzdWx0KSB7CiAgICAgIGlmICghdGhpcy5jbG9zZWQpIHsKICAgICAgICB0aGlzLiRlbWl0KCdjbG9zZWQnLCByZXN1bHQpOwogICAgICAgIHRoaXMuY2xvc2VkID0gdHJ1ZTsKICAgICAgfQogICAgfSwKICB9Cn07Cg=="}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Dialog.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,<PERSON>AC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEzF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;EAErC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACb,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC3D,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACb,CAAC;;IAED,CAAC,CAAC;KACD,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;KACtE,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAC1B,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB;MACF,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACpB;IACF,CAAC;EACH;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/Dialog.vue", "sourceRoot": "", "sourcesContent": ["<script>\nimport AsyncButton from '@shell/components/AsyncButton';\nimport AppModal, { DEFAULT_ITERABLE_NODE_SELECTOR } from '@shell/components/AppModal.vue';\n\nexport default {\n  emits: ['okay', 'closed'],\n\n  components: { AsyncButton, AppModal },\n\n  props: {\n    name: {\n      type:     String,\n      required: true,\n    },\n\n    title: {\n      type:     String,\n      required: true,\n    },\n\n    mode: {\n      type:    String,\n      default: '',\n    },\n\n    /**\n     * forcefully set return focus element based on this selector\n     */\n    returnFocusSelector: {\n      type:    String,\n      default: '',\n    },\n\n    /**\n     * will return focus to the first iterable node of this container select\n     */\n    returnFocusFirstIterableNodeSelector: {\n      type:    String,\n      default: DEFAULT_ITERABLE_NODE_SELECTOR,\n    }\n  },\n\n  data() {\n    return { closed: false };\n  },\n\n  methods: {\n    beforeOpen() {\n      this.closed = false;\n    },\n\n    ok(btnCb) {\n      const callback = (ok) => {\n        btnCb(ok);\n        if (ok) {\n          this.closeDialog(true);\n        }\n      };\n\n      this.$emit('okay', callback);\n    },\n\n    closeDialog(result) {\n      if (!this.closed) {\n        this.$emit('closed', result);\n        this.closed = true;\n      }\n    },\n  }\n};\n</script>\n\n<template>\n  <app-modal\n    v-if=\"!closed\"\n    :name=\"name\"\n    height=\"auto\"\n    :scrollable=\"true\"\n    :trigger-focus-trap=\"true\"\n    :return-focus-selector=\"returnFocusSelector\"\n    :return-focus-first-iterable-node-selector=\"returnFocusFirstIterableNodeSelector\"\n    @close=\"closeDialog(false)\"\n    @before-open=\"beforeOpen\"\n  >\n    <div class=\"modal-dialog\">\n      <h4>\n        {{ title }}\n      </h4>\n      <slot />\n      <div class=\"dialog-buttons mt-20\">\n        <slot name=\"buttons\" />\n        <div v-if=\"!$slots.buttons\">\n          <button\n            class=\"btn role-secondary\"\n            @click=\"closeDialog(false)\"\n          >\n            {{ t('generic.cancel') }}\n          </button>\n          <button\n            v-if=\"!mode\"\n            class=\"btn role-primary ml-10\"\n            @click=\"closeDialog(true)\"\n          >\n            {{ t('generic.ok') }}\n          </button>\n          <AsyncButton\n            v-else\n            :mode=\"mode\"\n            class=\"ml-10\"\n            @click=\"ok\"\n          />\n        </div>\n      </div>\n    </div>\n  </app-modal>\n</template>\n\n<style lang=\"scss\" scoped>\n  .modal-dialog {\n    padding: 10px;\n\n    h4 {\n      font-weight: bold;\n    }\n\n    .dialog-buttons {\n      display: flex;\n      justify-content: flex-end;\n      margin-top: 10px;\n\n      > *:not(:last-child) {\n        margin-right: 10px;\n      }\n    }\n  }\n</style>\n"]}]}