{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Type.vue?vue&type=template&id=4c933e29&scoped=true", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Type.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/nav/Type.vue"], "names": ["t"], "mappings": ";;;;;;EAmIU,KAAK,EAAC,OAAO;;;;EAUb,KAAK,EAAC,OAAO;;;;EAcX,aAAW,EAAC,YAAY;;;;EAQhC,KAAK,EAAC,yBAAyB;EAC/B,aAAW,EAAC,WAAW;;;qBASf,KAAK,EAAC,OAAO;;;;;;;;;;UAxEf,WAAI,CAAC,KAAK;qBADlB,aA4Dc;QA1DX,GAAG,EAAE,WAAI,CAAC,IAAI;QAEf,MAAM,EAAN,EAAM;QACL,EAAE,EAAE,WAAI,CAAC,KAAK;;0BAEf,CAoDK,EAxDK,IAAI,EAAE,QAAQ,CAAC,aAAa;UAItC,oBAoDK;YAnDH,KAAK,mBAAC,gBAAgB,WACL,aAAM,YAAY,YAAK,iCAAiC,iBAAQ,8BAA8B,aAAa;YAC3H,OAAK,EAAE,QAAQ;YACf,UAAQ,YAAQ,QAAQ;;YAGjB,aAAa;+BADrB,aAKW;;kBAHR,YAAU,EAAE,KAAK;;oCAElB,CAA0E;sDAAvE,WAAI,CAAC,QAAQ,GAAGA,MAAC,CAAC,WAAI,CAAC,QAAQ,KAAK,WAAI,CAAC,YAAY,IAAI,WAAI,CAAC,KAAK;;;;;;YAExE,oBAuCI;cAtCF,IAAI,EAAC,MAAM;cACV,YAAU,EAAE,WAAI,CAAC,QAAQ,GAAGA,MAAC,CAAC,WAAI,CAAC,QAAQ,KAAK,WAAI,CAAC,YAAY,IAAI,WAAI,CAAC,KAAK;cAC/E,IAAI,EAAE,IAAI;cACX,KAAK,EAAC,WAAW;cAChB,cAAY,EAAE,iBAAQ,YAAY,SAAS;cAC3C,OAAK,aAAE,mBAAU,IAAI,QAAQ,CAAC,MAAM;cACpC,YAAU,uCAAE,gBAAO;cACnB,YAAU,uCAAE,gBAAO;;eAGZ,WAAI,CAAC,QAAQ;iCADrB,oBAGgC,QAHhC,UAGgC;oBAA/B,aAAwB;sBAApB,CAAC,EAAE,WAAI,CAAC,QAAQ;;;iDACrB,oBAKE;;oBAFA,KAAK,mBAAC,OAAO,eACQ,WAAI,CAAC,IAAI;;4CAFhB,WAAI,CAAC,YAAY,IAAI,WAAI,CAAC,KAAK;;;eAKvC,qBAAY,IAAI,sBAAa,IAAI,kBAAS;iCADlD,oBAkBO,QAlBP,UAkBO;qBAbG,qBAAY;uCADpB,aAGE;;0BADC,QAAQ,EAAE,WAAI,CAAC,IAAI;;;;qBAGd,sBAAa;uCADrB,oBAKE;;0BAHA,KAAK,mBAAC,qBAAqB,kBACH,kBAAS;0BACjC,aAAW,EAAC,iBAAiB;;;;qBAGvB,kBAAS;uCADjB,oBAGmB,QAHnB,UAGmB,mBAAf,cAAK;;;;;;;;;OAMJ,WAAI,CAAC,IAAI;uBADtB,oBAcK,MAdL,UAcK;UATH,oBAQI;YAPF,IAAI,EAAC,MAAM;YACV,IAAI,EAAE,WAAI,CAAC,IAAI;YACf,MAAM,EAAE,WAAI,CAAC,MAAM;YACpB,GAAG,EAAC,8BAA8B;YACjC,YAAU,EAAE,WAAI,CAAC,KAAK;;YAEvB,oBAAsF,QAAtF,UAAsF;gDAA/D,WAAI,CAAC,KAAK,IAAG,GAAM;wCAAA,oBAAqC,OAAlC,KAAK,EAAC,yBAAyB;;;;uBAGhF,oBAEK,mCADA,WAAI,IAAG,OACZ", "sourcesContent": ["<script>\nimport Favorite from '@shell/components/nav/Favorite';\nimport { TYPE_MODES } from '@shell/store/type-map';\n\nimport TabTitle from '@shell/components/TabTitle';\n\nconst showFavoritesFor = [TYPE_MODES.FAVORITE, TYPE_MODES.USED];\n\nexport default {\n\n  components: { Favorite, TabTitle },\n\n  emits: ['selected'],\n\n  props: {\n    type: {\n      type:     Object,\n      required: true\n    },\n\n    isRoot: {\n      type:    Boolean,\n      default: false,\n    },\n\n    depth: {\n      type:    Number,\n      default: 0,\n    },\n  },\n\n  data() {\n    return { near: false };\n  },\n\n  computed: {\n    showFavorite() {\n      return ( this.type.mode && this.near && showFavoritesFor.includes(this.type.mode) );\n    },\n\n    showCount() {\n      return this.count !== undefined && this.count !== null;\n    },\n\n    namespaceIcon() {\n      return this.type.namespaced;\n    },\n\n    count() {\n      if (this.type.count !== undefined) {\n        return this.type.count;\n      }\n\n      const inStore = this.$store.getters['currentStore'](this.type.name);\n\n      return this.$store.getters[`${ inStore }/count`]({ name: this.type.name });\n    },\n\n    isActive() {\n      const typeFullPath = this.$router.resolve(this.type.route)?.fullPath.toLowerCase();\n      const pageFullPath = this.$route.fullPath?.toLowerCase();\n\n      if ( !this.type.exact) {\n        const typeSplit = typeFullPath.split('/');\n        const pageSplit = pageFullPath.split('/');\n\n        for (let index = 0; index < typeSplit.length; ++index) {\n          if ( index >= pageSplit.length || typeSplit[index] !== pageSplit[index] ) {\n            return false;\n          }\n        }\n\n        return true;\n      }\n\n      return typeFullPath === pageFullPath;\n    }\n\n  },\n\n  methods: {\n    setNear(val) {\n      this.near = val;\n    },\n\n    selectType() {\n      // Prevent issues if custom NavLink is used #5047\n      if (this.type?.route) {\n        const typePath = this.$router.resolve(this.type.route)?.fullPath;\n\n        if (typePath !== this.$route.fullPath) {\n          this.$emit('selected');\n        }\n      }\n    }\n  }\n};\n</script>\n\n<template>\n  <router-link\n    v-if=\"type.route\"\n    :key=\"type.name\"\n    v-slot=\"{ href, navigate,isExactActive }\"\n    custom\n    :to=\"type.route\"\n  >\n    <li\n      class=\"child nav-type\"\n      :class=\"{'root': isRoot, [`depth-${depth}`]: true, 'router-link-active': isActive, 'router-link-exact-active': isExactActive}\"\n      @click=\"navigate\"\n      @keypress.enter=\"navigate\"\n    >\n      <TabTitle\n        v-if=\"isExactActive\"\n        :show-child=\"false\"\n      >\n        {{ type.labelKey ? t(type.labelKey) : (type.labelDisplay || type.label) }}\n      </TabTitle>\n      <a\n        role=\"link\"\n        :aria-label=\"type.labelKey ? t(type.labelKey) : (type.labelDisplay || type.label)\"\n        :href=\"href\"\n        class=\"type-link\"\n        :aria-current=\"isActive ? 'page' : undefined\"\n        @click=\"selectType(); navigate($event);\"\n        @mouseenter=\"setNear(true)\"\n        @mouseleave=\"setNear(false)\"\n      >\n        <span\n          v-if=\"type.labelKey\"\n          class=\"label\"\n        ><t :k=\"type.labelKey\" /></span>\n        <span\n          v-else\n          v-clean-html=\"type.labelDisplay || type.label\"\n          class=\"label\"\n          :class=\"{'no-icon': !type.icon}\"\n        />\n        <span\n          v-if=\"showFavorite || namespaceIcon || showCount\"\n          class=\"count\"\n        >\n          <Favorite\n            v-if=\"showFavorite\"\n            :resource=\"type.name\"\n          />\n          <i\n            v-if=\"namespaceIcon\"\n            class=\"icon icon-namespace\"\n            :class=\"{'ns-and-icon': showCount}\"\n            data-testid=\"type-namespaced\"\n          />\n          <span\n            v-if=\"showCount\"\n            data-testid=\"type-count\"\n          >{{ count }}</span>\n        </span>\n      </a>\n    </li>\n  </router-link>\n  <li\n    v-else-if=\"type.link\"\n    class=\"child nav-type nav-link\"\n    data-testid=\"link-type\"\n  >\n    <a\n      role=\"link\"\n      :href=\"type.link\"\n      :target=\"type.target\"\n      rel=\"noopener noreferrer nofollow\"\n      :aria-label=\"type.label\"\n    >\n      <span class=\"label\">{{ type.label }}&nbsp;<i class=\"icon icon-external-link\" /></span>\n    </a>\n  </li>\n  <li v-else>\n    {{ type }}?\n  </li>\n</template>\n\n<style lang=\"scss\" scoped>\n  .ns-and-icon {\n    margin-right: 4px;\n  }\n\n  .type-link:focus-visible span.label {\n    @include focus-outline;\n    outline-offset: 2px;\n  }\n\n  .nav-link a:focus-visible .label {\n    @include focus-outline;\n    outline-offset: 2px;\n  }\n\n  .child {\n    margin: 0 var(--outline) 0 0;\n\n    .label {\n      align-items: center;\n      grid-area: label;\n      overflow: hidden;\n      text-overflow: ellipsis;\n\n      &:not(.nav-type) &.no-icon {\n        padding-left: 3px;\n      }\n\n      :deep() .highlight {\n        background: var(--diff-ins-bg);\n        color: var(--body-text);\n        padding: 2px;\n      }\n\n      :deep() .icon {\n        position: relative;\n        color: var(--muted);\n      }\n    }\n\n    A {\n      display: grid;\n      grid-template-areas: \"label count\";\n      grid-template-columns: auto auto;\n      grid-column-gap: 5px;\n      font-size: 14px;\n      line-height: 24px;\n      padding: 7.5px 7px 7.5px 10px;\n      margin: 0 0 0 -3px;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n      color: var(--body-text);\n      height: 33px;\n\n      &:hover {\n        background: var(--nav-hover);\n        text-decoration: none;\n\n        :deep() .icon {\n          color: var(--body-text);\n        }\n      }\n    }\n\n    .favorite {\n      grid-area: favorite;\n      font-size: 12px;\n      position: relative;\n      vertical-align: middle;\n      margin-right: 4px;\n    }\n\n    .count {\n      font-size: 12px;\n      justify-items: center;\n      padding-right: 4px;\n      display: flex;\n      align-items: center;\n    }\n\n    &.nav-type.nav-link {\n      a .label {\n        display: flex;\n      }\n    }\n\n    &.nav-type:not(.depth-0) {\n      A {\n        padding-left: 16px;\n      }\n\n      :deep() .label I {\n        padding-right: 2px;\n      }\n    }\n\n    &.nav-type:is(.depth-1) {\n      A {\n        font-size: 13px;\n        padding-left: 23px;\n      }\n    }\n\n    &.nav-type:not(.depth-0):not(.depth-1) {\n      A {\n        padding-left: 14px;\n      }\n    }\n  }\n\n</style>\n"]}]}