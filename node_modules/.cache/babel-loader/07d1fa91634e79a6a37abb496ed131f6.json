{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Bar.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Bar.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIHByb3BzOiB7DQogICAgcGVyY2VudGFnZTogew0KICAgICAgdHlwZTogICAgIE51bWJlciwNCiAgICAgIHJlcXVpcmVkOiB0cnVlDQogICAgfSwNCiAgICBwcmltYXJ5Q29sb3I6IHsNCiAgICAgIHR5cGU6ICAgIFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICctLXByaW1hcnknDQogICAgfSwNCiAgICBzZWNvbmRhcnlDb2xvcjogew0KICAgICAgdHlwZTogICAgU3RyaW5nLA0KICAgICAgZGVmYXVsdDogJy0tYm9yZGVyJw0KICAgIH0sDQogICAgc2xpY2VzOiB7DQogICAgICB0eXBlOiAgICBBcnJheSwNCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdDQogICAgfQ0KICB9LA0KICBjb21wdXRlZDogew0KICAgIGluZGljYXRvclN0eWxlKCkgew0KICAgICAgcmV0dXJuIHsNCiAgICAgICAgd2lkdGg6ICAgICAgICAgICBgJHsgdGhpcy5wZXJjZW50YWdlIH0lYCwNCiAgICAgICAgYmFja2dyb3VuZENvbG9yOiBgdmFyKCR7IHRoaXMucHJpbWFyeUNvbG9yIH0pYA0KICAgICAgfTsNCiAgICB9LA0KICAgIGJhclN0eWxlKCkgew0KICAgICAgcmV0dXJuIHsgYmFja2dyb3VuZENvbG9yOiBgdmFyKCR7IHRoaXMuc2Vjb25kYXJ5Q29sb3IgfSlgIH07DQogICAgfSwNCiAgICBzbGljZVN0eWxlcygpIHsNCiAgICAgIHJldHVybiB0aGlzLnNsaWNlcy5tYXAoKHNsaWNlKSA9PiAoew0KICAgICAgICBsZWZ0OiAgICAgICBgJHsgc2xpY2UgfSVgLA0KICAgICAgICB2aXNpYmlsaXR5OiBzbGljZSA8IHRoaXMucGVyY2VudGFnZSA/ICd2aXNpYmxlJyA6ICdoaWRkZW4nDQogICAgICB9KSk7DQogICAgfQ0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Bar.vue"], "names": [], "mappings": ";AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAC/C,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAC7D,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3D,CAAC,CAAC,CAAC;IACL;EACF;AACF,CAAC", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/graph/Bar.vue", "sourceRoot": "", "sourcesContent": ["<script>\r\nexport default {\r\n  props: {\r\n    percentage: {\r\n      type:     Number,\r\n      required: true\r\n    },\r\n    primaryColor: {\r\n      type:    String,\r\n      default: '--primary'\r\n    },\r\n    secondaryColor: {\r\n      type:    String,\r\n      default: '--border'\r\n    },\r\n    slices: {\r\n      type:    Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  computed: {\r\n    indicatorStyle() {\r\n      return {\r\n        width:           `${ this.percentage }%`,\r\n        backgroundColor: `var(${ this.primaryColor })`\r\n      };\r\n    },\r\n    barStyle() {\r\n      return { backgroundColor: `var(${ this.secondaryColor })` };\r\n    },\r\n    sliceStyles() {\r\n      return this.slices.map((slice) => ({\r\n        left:       `${ slice }%`,\r\n        visibility: slice < this.percentage ? 'visible' : 'hidden'\r\n      }));\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<template>\r\n  <div\n    class=\"bar\"\n    :style=\"barStyle\"\n  >\r\n    <div\n      class=\"indicator\"\n      :style=\"indicatorStyle\"\n    />\r\n    <div\n      v-for=\"(sliceStyle, i) in sliceStyles\"\n      :key=\"i\"\n      class=\"slice\"\n      :style=\"sliceStyle\"\n    />\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.bar {\r\n    $height: 15px;\r\n\r\n    width: 100%;\r\n    height: $height;\r\n    border-radius: math.div($height, 2);\r\n    overflow: hidden;\r\n    position: relative;\r\n\r\n    .indicator {\r\n        height: 100%;\r\n    }\r\n\r\n    .slice {\r\n      position: absolute;\r\n      top: 0;\r\n      bottom: 0;\r\n      width: 1px;\r\n      background-color: var(--body-bg);\r\n    }\r\n}\r\n</style>\r\n"]}]}