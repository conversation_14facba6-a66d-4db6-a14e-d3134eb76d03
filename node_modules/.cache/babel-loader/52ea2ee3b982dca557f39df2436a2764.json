{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??clonedRuleSet-44.use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js??clonedRuleSet-44.use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdown.vue?vue&type=script&setup=true&lang=ts", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdown.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/ts-loader/index.js", "mtime": 1753522229047}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdown.vue?vue&type=script&setup=true&lang=ts", "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdown.vue"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,IAAI,gBAAgB,EAAE,MAAM,KAAK,CAAA;ACsBzD,OAAO,EAAE,GAAG,EAAE,MAAM,KAAK,CAAA;AACzB,OAAO,EAAE,eAAe,EAAE,MAAM,oCAAoC,CAAA;AACpE,OAAO,EAAE,kBAAkB,EAAE,MAAM,2CAA2C,CAAA;ADlB9E,eAAe,aAAa,CAAA,gBAAgB,CAAC;IAC3C,MAAM,EAAE,YAAY;IACpB,KAAK,EAAE;QACL,SAAS,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE;KAC7C;IACD,KAAK,EAAE,CAAC,aAAa,CAAC;IACtB,KAAK,CAAC,OAAY,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE;QACtD,QAAQ,EAAE,CAAC;QCZb;;;;;;;;;;;;;;;;;;;;WAoBE;QASF,MAAM,IAAI,GAAG,MAA4B,CAAA;QAEzC,MAAM,EACJ,UAAU,EACV,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,sBAAsB,EACtB,0BAA0B,EAC1B,aAAa,GACd,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAA;QAE5B,sBAAsB,EAAE,CAAA;QAExB,MAAM,eAAe,GAAG,GAAG,CAAC,IAAI,CAAC,CAAA;QACjC,MAAM,cAAc,GAAG,GAAG,CAAC,IAAI,CAAC,CAAA;QAEhC,eAAe,CAAC,cAAc,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAA;QAEtD,MAAM,SAAS,GAAG,GAAG,EAAE;YACrB,0BAA0B,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;YAChD,QAAQ,EAAE,CAAA;QACZ,CAAC,CAAA;QDWD,MAAM,YAAY,GAAG,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,sBAAsB,EAAE,0BAA0B,EAAE,aAAa,EAAE,eAAe,EAAE,cAAc,EAAE,SAAS,EAAE,CAAA;QACzL,MAAM,CAAC,cAAc,CAAC,YAAY,EAAE,iBAAiB,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAA;QAC1F,OAAO,YAAY,CAAA;IACnB,CAAC;CAEA,CAAC,CAAA", "file": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/rancher-components/RcDropdown/RcDropdown.vue.tsx", "sourceRoot": "", "sourcesContent": ["import { defineComponent as _defineComponent } from 'vue'\nimport { ref } from 'vue';\nimport { useClickOutside } from '@shell/composables/useClickOutside';\nimport { useDropdownContext } from '@components/RcDropdown/useDropdownContext';\n\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'RcDropdown',\n  props: {\n    ariaLabel: { type: String, required: false }\n  },\n  emits: ['update:open'],\n  setup(__props: any, { expose: __expose, emit: __emit }) {\n  __expose();\n\n/**\n * Offers a list of choices to the user, such as a set of actions or functions.\n * Opened by activating RcDropdownTrigger.\n *\n * Example:\n *\n *  <rc-dropdown :aria-label=\"t('nav.actionMenu.label')\">\n *    <rc-dropdown-trigger tertiary>\n *      <i class=\"icon icon-actions\" />\n *    </rc-dropdown-trigger>\n *    <template #dropdownCollection>\n *      <rc-dropdown-item @click=\"performAction()\">\n *        Action 1\n *      </rc-dropdown-item>\n *      <rc-dropdown-separator />\n *      <rc-dropdown-item @click=\"performAction()\">\n *        Action 2\n *      </rc-dropdown-item>\n *    </template>\n *  </rc-dropdown>\n */\n\n\nconst emit = __emit;\n\nconst {\n  isMenuOpen,\n  showMenu,\n  returnFocus,\n  setFocus,\n  provideDropdownContext,\n  registerDropdownCollection,\n  handleKeydown,\n} = useDropdownContext(emit);\n\nprovideDropdownContext();\n\nconst popperContainer = ref(null);\nconst dropdownTarget = ref(null);\n\nuseClickOutside(dropdownTarget, () => showMenu(false));\n\nconst applyShow = () => {\n  registerDropdownCollection(dropdownTarget.value);\n  setFocus();\n};\n\n\nconst __returned__ = { emit, isMenuOpen, showMenu, returnFocus, setFocus, provideDropdownContext, registerDropdownCollection, handleKeydown, popperContainer, dropdownTarget, applyShow }\nObject.defineProperty(__returned__, '__isScriptSetup', { enumerable: false, value: true })\nreturn __returned__\n}\n\n})", "<script setup lang=\"ts\">\n/**\n * Offers a list of choices to the user, such as a set of actions or functions.\n * Opened by activating RcDropdownTrigger.\n *\n * Example:\n *\n *  <rc-dropdown :aria-label=\"t('nav.actionMenu.label')\">\n *    <rc-dropdown-trigger tertiary>\n *      <i class=\"icon icon-actions\" />\n *    </rc-dropdown-trigger>\n *    <template #dropdownCollection>\n *      <rc-dropdown-item @click=\"performAction()\">\n *        Action 1\n *      </rc-dropdown-item>\n *      <rc-dropdown-separator />\n *      <rc-dropdown-item @click=\"performAction()\">\n *        Action 2\n *      </rc-dropdown-item>\n *    </template>\n *  </rc-dropdown>\n */\nimport { ref } from 'vue';\nimport { useClickOutside } from '@shell/composables/useClickOutside';\nimport { useDropdownContext } from '@components/RcDropdown/useDropdownContext';\n\ndefineProps<{\n  ariaLabel?: string\n}>();\n\nconst emit = defineEmits(['update:open']);\n\nconst {\n  isMenuOpen,\n  showMenu,\n  returnFocus,\n  setFocus,\n  provideDropdownContext,\n  registerDropdownCollection,\n  handleKeydown,\n} = useDropdownContext(emit);\n\nprovideDropdownContext();\n\nconst popperContainer = ref(null);\nconst dropdownTarget = ref(null);\n\nuseClickOutside(dropdownTarget, () => showMenu(false));\n\nconst applyShow = () => {\n  registerDropdownCollection(dropdownTarget.value);\n  setFocus();\n};\n\n</script>\n\n<template>\n  <v-dropdown\n    no-auto-focus\n    :triggers=\"[]\"\n    :shown=\"isMenuOpen\"\n    :auto-hide=\"false\"\n    :container=\"popperContainer\"\n    :placement=\"'bottom-end'\"\n    @apply-show=\"applyShow\"\n  >\n    <slot name=\"default\">\n      <!--Empty slot content Trigger-->\n    </slot>\n\n    <template #popper>\n      <div\n        ref=\"dropdownTarget\"\n        class=\"dropdownTarget\"\n        tabindex=\"-1\"\n        role=\"menu\"\n        aria-orientation=\"vertical\"\n        dropdown-menu-collection\n        :aria-label=\"ariaLabel || 'Dropdown Menu'\"\n        @keydown=\"handleKeydown\"\n        @keydown.down=\"setFocus()\"\n      >\n        <slot name=\"dropdownCollection\">\n          <!--Empty slot content-->\n        </slot>\n      </div>\n    </template>\n  </v-dropdown>\n  <div\n    ref=\"popperContainer\"\n    class=\"popperContainer\"\n    @keydown.tab=\"showMenu(false)\"\n    @keydown.escape=\"returnFocus\"\n  >\n    <!--Empty container for mounting popper content-->\n  </div>\n</template>\n\n<style lang=\"scss\" scoped>\n  .popperContainer {\n    display: contents;\n    &:deep(.v-popper__popper) {\n\n      .v-popper__wrapper {\n        box-shadow: 0px 6px 18px 0px rgba(0, 0, 0, 0.25), 0px 4px 10px 0px rgba(0, 0, 0, 0.15);\n        border-radius: var(--border-radius-lg);\n\n        .v-popper__arrow-container {\n          display: none;\n        }\n\n        .v-popper__inner {\n          padding: 10px 0 10px 0;\n        }\n      }\n    }\n  }\n\n  .dropdownTarget {\n    &:focus-visible, &:focus {\n      outline: none;\n    }\n  }\n</style>\n"]}]}