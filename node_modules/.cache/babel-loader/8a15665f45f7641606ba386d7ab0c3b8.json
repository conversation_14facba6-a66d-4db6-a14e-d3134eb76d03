{"remainingRequest": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[4]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js??ruleSet[0].use[0]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js??ruleSet[0].use[1]!/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DisableAuthProviderModal.vue?vue&type=template&id=434b50ef", "dependencies": [{"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DisableAuthProviderModal.vue", "mtime": 1753522221313}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/templateLoader.js", "mtime": 1753522226950}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/cache-loader/dist/cjs.js", "mtime": 1753522223085}, {"path": "/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/vue-loader/dist/index.js", "mtime": 1753522226950}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["/Users/<USER>/suse/repo/github/rancher/saas-cloud/wt/MCM-14/ui-extension/node_modules/@rancher/shell/components/DisableAuthProviderModal.vue"], "names": ["t"], "mappings": ";;qBAqDY,KAAK,EAAC,mBAAmB;qBAKxB,KAAK,EAAC,OAAO;;;;;;;;UAlBhB,eAAS;qBADjB,aAwCY;;QAtCV,cAAY,EAAC,cAAc;QAC3B,IAAI,EAAC,0BAA0B;QAC9B,KAAK,EAAE,GAAG;QACX,MAAM,EAAC,MAAM;QACb,MAAM,EAAC,oBAAoB;QAC1B,OAAK,EAAE,cAAK;;0BAEb,CA8BO;UA9BP,aA8BO;YA7BL,KAAK,EAAC,uBAAuB;YAC5B,uBAAqB,EAAE,KAAK;;YAElB,KAAK,WACd,CAEK;cAFL,oBAEK,MAFL,UAEK,mBADAA,MAAC;;YAGG,IAAI,WACb,CAEM;cAFN,oBAEM,OAFN,UAEM;gCADJ,oBAA+E;0CAA9DA,MAAC;;;;YAGX,OAAO,WAChB,CAKS;cALT,oBAKS;gBAJP,KAAK,EAAC,oBAAoB;gBACzB,OAAK,0CAAE,yCAAK;kCAEVA,MAAC;;wCAEN,oBAAsB,SAAjB,KAAK,EAAC,QAAQ;;cACnB,oBAMS;gBALP,KAAK,EAAC,iCAAiC;gBACtC,aAAW,EAAE,sBAAe;gBAC5B,OAAK,0CAAE,6CAAO;kCAEZA,MAAC", "sourcesContent": ["<script>\nimport { Card } from '@components/Card';\nimport AppModal from '@shell/components/AppModal.vue';\n\nexport default {\n  name: 'PromptRemove',\n\n  emits: ['disable'],\n\n  components: { Card, AppModal },\n  props:      {\n    /**\n     * Inherited global identifier prefix for tests\n     * Define a term based on the parent component to avoid conflicts on multiple components\n     */\n    componentTestid: {\n      type:    String,\n      default: 'disable-auth-provider'\n    }\n  },\n  data() {\n    return { showModal: false };\n  },\n  methods: {\n    show() {\n      this.showModal = true;\n    },\n    close() {\n      this.showModal = false;\n    },\n    disable() {\n      this.showModal = false;\n      this.$emit('disable');\n    },\n  }\n};\n</script>\n\n<template>\n  <app-modal\n    v-if=\"showModal\"\n    custom-class=\"remove-modal\"\n    name=\"disableAuthProviderModal\"\n    :width=\"400\"\n    height=\"auto\"\n    styles=\"max-height: 100vh;\"\n    @close=\"close\"\n  >\n    <Card\n      class=\"disable-auth-provider\"\n      :show-highlight-border=\"false\"\n    >\n      <template #title>\n        <h4 class=\"text-default-text\">\n          {{ t('promptRemove.title') }}\n        </h4>\n      </template>\n      <template #body>\n        <div class=\"mb-10\">\n          <p v-clean-html=\"t('promptRemove.attemptingToRemoveAuthConfig', null, true)\" />\n        </div>\n      </template>\n      <template #actions>\n        <button\n          class=\"btn role-secondary\"\n          @click=\"close\"\n        >\n          {{ t('generic.cancel') }}\n        </button>\n        <div class=\"spacer\" />\n        <button\n          class=\"btn role-primary bg-error ml-10\"\n          :data-testid=\"componentTestid + '-confirm-button'\"\n          @click=\"disable\"\n        >\n          {{ t('generic.disable') }}\n        </button>\n      </template>\n    </Card>\n  </app-modal>\n</template>\n\n<style lang='scss'>\n  .disable-auth-provider {\n    &.card-container {\n      box-shadow: none;\n    }\n    #confirm {\n      width: 90%;\n      margin-left: 3px;\n    }\n\n    .remove-modal {\n        border-radius: var(--border-radius);\n        overflow: scroll;\n        max-height: 100vh;\n        & ::-webkit-scrollbar-corner {\n          background: rgba(0,0,0,0);\n        }\n    }\n\n    .actions {\n      text-align: right;\n    }\n\n    .card-actions {\n      display: flex;\n\n      .spacer {\n        flex: 1;\n      }\n    }\n  }\n</style>\n"]}]}