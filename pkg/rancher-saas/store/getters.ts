export default {
  messages(state: any) {
    return state.chat.messages;
  },
  backendUrl(state: any) {
    return state.config.backendUrl;
  },
  roleArn(state: any) {
    return state.config.roleArn;
  },
  callbackUrl(state: any) {
    return state.config.callbackUrl;
  },
  appRegion(state: any) {
    return state.config.appRegion;
  },
  appId(state: any) {
    return state.config.appId;
  },
  accesorId(state: any) {
    return state.config.accesorId;
  },
  ssoId(state: any) {
    return state.config.ssoId;
  },
  ssoRegion(state: any) {
    return state.config.ssoRegion;
  },
  qindexConfig(state: any) {
    return state;
  },
  qindexExpiration(state: any) {
    return state.expiration < Date.now();
  },
  isPLGMode(state: any) {
    return state.config.isPLGMode ?? true;
  },
  lastWizardStep(state: any) {
    return state.config.lastWizardStep;
  }
};
